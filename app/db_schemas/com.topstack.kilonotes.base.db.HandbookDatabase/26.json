{"formatVersion": 1, "database": {"version": 26, "identityHash": "e034d506c75e76a9cea0249c153fede5", "entities": [{"tableName": "handbook_covers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`thumbnail_url` TEXT NOT NULL, `tag_list` TEXT NOT NULL, `page_num` INTEGER NOT NULL, `sort` INTEGER NOT NULL, `modified_time` INTEGER NOT NULL, `note_id` INTEGER NOT NULL, `notebook_id` TEXT NOT NULL, `product_id` TEXT NOT NULL, `google_product_id` TEXT NOT NULL, `pdf_url` TEXT NOT NULL, `price` REAL NOT NULL, `title` TEXT NOT NULL, `file` TEXT, `is_free` INTEGER NOT NULL, `is_vip` INTEGER NOT NULL, `audio_url` TEXT NOT NULL DEFAULT '', `audio_file` TEXT DEFAULT NULL, PRIMARY KEY(`note_id`))", "fields": [{"fieldPath": "thumbnailUrl", "columnName": "thumbnail_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tags", "columnName": "tag_list", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pageNum", "columnName": "page_num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifiedTime", "columnName": "modified_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "noteId", "columnName": "note_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notebookId", "columnName": "notebook_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productId", "columnName": "product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "googleProductId", "columnName": "google_product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pdfUrl", "columnName": "pdf_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "price", "columnName": "price", "affinity": "REAL", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isFree", "columnName": "is_free", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_vip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "audioUrl", "columnName": "audio_url", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "audioFile", "columnName": "audio_file", "affinity": "TEXT", "notNull": false, "defaultValue": "NULL"}], "primaryKey": {"autoGenerate": false, "columnNames": ["note_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "handbook_details", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`top_banner_url` TEXT NOT NULL, `description` TEXT NOT NULL, `banner_list` TEXT NOT NULL, `tag_detail_list` TEXT NOT NULL, `note_id` INTEGER NOT NULL, `notebook_id` TEXT NOT NULL, `product_id` TEXT NOT NULL, `google_product_id` TEXT NOT NULL, `pdf_url` TEXT NOT NULL, `price` REAL NOT NULL, `title` TEXT NOT NULL, `file` TEXT, `is_free` INTEGER NOT NULL, `is_vip` INTEGER NOT NULL, `audio_url` TEXT NOT NULL DEFAULT '', `audio_file` TEXT DEFAULT NULL, PRIMARY KEY(`note_id`))", "fields": [{"fieldPath": "topBannerUrl", "columnName": "top_banner_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "desc", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "banners", "columnName": "banner_list", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tagDetails", "columnName": "tag_detail_list", "affinity": "TEXT", "notNull": true}, {"fieldPath": "noteId", "columnName": "note_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notebookId", "columnName": "notebook_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productId", "columnName": "product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "googleProductId", "columnName": "google_product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pdfUrl", "columnName": "pdf_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "price", "columnName": "price", "affinity": "REAL", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isFree", "columnName": "is_free", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_vip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "audioUrl", "columnName": "audio_url", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "audioFile", "columnName": "audio_file", "affinity": "TEXT", "notNull": false, "defaultValue": "NULL"}], "primaryKey": {"autoGenerate": false, "columnNames": ["note_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "template_categories", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`category_id` INTEGER NOT NULL, `category_name` TEXT NOT NULL, `note_id` INTEGER NOT NULL, `pre_url` TEXT NOT NULL, `product_id` TEXT NOT NULL, `notebook_id` TEXT NOT NULL, `format` INTEGER NOT NULL, `google_product_id` TEXT NOT NULL, `device` TEXT NOT NULL, `sort` INTEGER NOT NULL, PRIMARY KEY(`category_id`))", "fields": [{"fieldPath": "categoryId", "columnName": "category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryName", "columnName": "category_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "noteId", "columnName": "note_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "preUrl", "columnName": "pre_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productId", "columnName": "product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notebookId", "columnName": "notebook_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "format", "columnName": "format", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "googleProductId", "columnName": "google_product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "device", "columnName": "device", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["category_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "templates", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `category_id` INTEGER NOT NULL, `name` TEXT NOT NULL, `template_url` TEXT NOT NULL, `thumbnail_url` TEXT NOT NULL, `sort` INTEGER NOT NULL, `is_vip` INTEGER NOT NULL, `is_open_ad` INTEGER NOT NULL, `device` TEXT NOT NULL, `file` TEXT, `version_code` INTEGER NOT NULL, `last_use_time` INTEGER NOT NULL, `modified_time` INTEGER NOT NULL, `thumbnail_width` INTEGER NOT NULL, `thumbnail_height` INTEGER NOT NULL, `template_type` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "templateUrl", "columnName": "template_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnail_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_vip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isOpenAd", "columnName": "is_open_ad", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "device", "columnName": "device", "affinity": "TEXT", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "versionCode", "columnName": "version_code", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUseTime", "columnName": "last_use_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifiedTime", "columnName": "modified_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "thumbnailWidth", "columnName": "thumbnail_width", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "thumbnailHeight", "columnName": "thumbnail_height", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "templateType", "columnName": "template_type", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "material_categories", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`category_id` INTEGER NOT NULL, `notebook_id` TEXT NOT NULL, `google_product_id` TEXT NOT NULL, `name` TEXT NOT NULL, `code` TEXT NOT NULL, `is_vip` INTEGER NOT NULL, `is_open_ad` INTEGER NOT NULL, `sort` INTEGER NOT NULL, `type` TEXT NOT NULL, `format` INTEGER NOT NULL, PRIMARY KEY(`category_id`))", "fields": [{"fieldPath": "id", "columnName": "category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notebookId", "columnName": "notebook_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "googleProductId", "columnName": "google_product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "code", "columnName": "code", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_vip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isOpenAd", "columnName": "is_open_ad", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "format", "columnName": "format", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["category_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "material_stickers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `category_id` INTEGER NOT NULL, `is_vip` INTEGER NOT NULL, `url` TEXT NOT NULL, `pre_url` TEXT NOT NULL, `sort` INTEGER NOT NULL, `file` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_vip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "preUrl", "columnName": "pre_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "material_fonts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`url` TEXT NOT NULL, `preview_url` TEXT NOT NULL, `sort` INTEGER NOT NULL, `is_vip` INTEGER NOT NULL, `last_use_time` INTEGER NOT NULL, `md5` TEXT NOT NULL DEFAULT '', `id` INTEGER NOT NULL, `font_type` INTEGER NOT NULL, `predefined_font_family` INTEGER NOT NULL, `name` TEXT NOT NULL, `sub_path` TEXT NOT NULL, `font_file_md5` TEXT NOT NULL, `font_actual_name` TEXT NOT NULL, PRIMARY KEY(`sub_path`))", "fields": [{"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "previewUrl", "columnName": "preview_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_vip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUseTime", "columnName": "last_use_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "md5", "columnName": "md5", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fontType", "columnName": "font_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "predefinedFontFamily", "columnName": "predefined_font_family", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subPath", "columnName": "sub_path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fontFileMd5", "columnName": "font_file_md5", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fontActualName", "columnName": "font_actual_name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["sub_path"]}, "indices": [], "foreignKeys": []}, {"tableName": "custom_material", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `sort` INTEGER NOT NULL, `file` TEXT NOT NULL, `category_id` TEXT NOT NULL, `file_md5` TEXT NOT NULL, `compressed_file_md5` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fileMd5", "columnName": "file_md5", "affinity": "TEXT", "notNull": true}, {"fieldPath": "compressedFileMd5", "columnName": "compressed_file_md5", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "template_page_usage", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`page_uuid` TEXT NOT NULL, `template_id` INTEGER NOT NULL, `used_tools_flag` INTEGER NOT NULL, PRIMARY KEY(`page_uuid`))", "fields": [{"fieldPath": "pageUuid", "columnName": "page_uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "templateId", "columnName": "template_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "usedToolsFlag", "columnName": "used_tools_flag", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["page_uuid"]}, "indices": [], "foreignKeys": []}, {"tableName": "download_type", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `entity` TEXT NOT NULL, `download_tag` TEXT NOT NULL, PRIMARY KEY(`id`, `entity`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "entity", "columnName": "entity", "affinity": "TEXT", "notNull": true}, {"fieldPath": "downloadTag", "columnName": "download_tag", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id", "entity"]}, "indices": [], "foreignKeys": []}, {"tableName": "custom_material_category", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `category_id` TEXT NOT NULL, `category_name_res_name` TEXT NOT NULL, `category_name` TEXT NOT NULL, `create_time` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameResName", "columnName": "category_name_res_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nameString", "columnName": "category_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createTime", "columnName": "create_time", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "note_snippet_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`tag_id` TEXT NOT NULL, `name` TEXT NOT NULL, `sort` INTEGER NOT NULL, `pseudo_tag_flag` TEXT, PRIMARY KEY(`tag_id`))", "fields": [{"fieldPath": "tagId", "columnName": "tag_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pseudoTagFlag", "columnName": "pseudo_tag_flag", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["tag_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "note_relation_of_snippet_and_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`snippet_id` TEXT NOT NULL, `tag_id` TEXT NOT NULL, PRIMARY KEY(`snippet_id`, `tag_id`))", "fields": [{"fieldPath": "snippetId", "columnName": "snippet_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tagId", "columnName": "tag_id", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["snippet_id", "tag_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "note_snippet", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`snippet_id` TEXT NOT NULL, `color` INTEGER NOT NULL, `snippet_type` TEXT NOT NULL DEFAULT 'IMAGE', `thumbnail_path` TEXT NOT NULL, `document_id` TEXT, `page_id` TEXT, `title` TEXT NOT NULL, `text` TEXT NOT NULL DEFAULT '', `doodle_id` TEXT, `create_time` INTEGER NOT NULL, `doodle_initial_scale` REAL NOT NULL DEFAULT -1.0, `symbols` TEXT, `symbols_rect_list` TEXT, PRIMARY KEY(`snippet_id`))", "fields": [{"fieldPath": "snippetId", "columnName": "snippet_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "color", "columnName": "color", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "snippetType", "columnName": "snippet_type", "affinity": "TEXT", "notNull": true, "defaultValue": "'IMAGE'"}, {"fieldPath": "thumbnail<PERSON>ath", "columnName": "thumbnail_path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "documentId", "columnName": "document_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pageId", "columnName": "page_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "text", "columnName": "text", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "doodleId", "columnName": "doodle_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createTime", "columnName": "create_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "doodleInitialScale", "columnName": "doodle_initial_scale", "affinity": "REAL", "notNull": true, "defaultValue": "-1.0"}, {"fieldPath": "textRecognitionResult.symbols", "columnName": "symbols", "affinity": "TEXT", "notNull": false}, {"fieldPath": "textRecognitionResult.symbolsRectList", "columnName": "symbols_rect_list", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["snippet_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "file_info_v2", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`path` TEXT NOT NULL, `last_modified_time` INTEGER, `current_modified_time` INTEGER, `upload_Time` INTEGER NOT NULL, `delete_time` INTEGER, `is_hidden_space` INTEGER NOT NULL, PRIMARY KEY(`path`, `is_hidden_space`))", "fields": [{"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastModifiedTime", "columnName": "last_modified_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "currentModifiedTime", "columnName": "current_modified_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "uploadTime", "columnName": "upload_Time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleteTime", "columnName": "delete_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isHiddenSpace", "columnName": "is_hidden_space", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["path", "is_hidden_space"]}, "indices": [], "foreignKeys": []}, {"tableName": "document_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uuid` TEXT NOT NULL, `current_document_name` TEXT NOT NULL, PRIMARY KEY(`uuid`))", "fields": [{"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currentDocumentName", "columnName": "current_document_name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uuid"]}, "indices": [], "foreignKeys": []}, {"tableName": "completion", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uuid` BLOB NOT NULL, `role` TEXT NOT NULL, `content` TEXT NOT NULL, `create_time` INTEGER NOT NULL, PRIMARY KEY(`uuid`))", "fields": [{"fieldPath": "uuid", "columnName": "uuid", "affinity": "BLOB", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createTime", "columnName": "create_time", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uuid"]}, "indices": [], "foreignKeys": []}, {"tableName": "purchased_note_config", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`product_id` TEXT NOT NULL, `purchased_note_uuid` BLOB NOT NULL, `extension` TEXT NOT NULL, PRIMARY KEY(`purchased_note_uuid`))", "fields": [{"fieldPath": "productId", "columnName": "product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "purchasedNoteUUID", "columnName": "purchased_note_uuid", "affinity": "BLOB", "notNull": true}, {"fieldPath": "extension", "columnName": "extension", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["purchased_note_uuid"]}, "indices": [], "foreignKeys": []}, {"tableName": "google_purchased_note_config", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`google_product_id` TEXT NOT NULL, `purchased_note_uuid` BLOB NOT NULL, `extension` TEXT NOT NULL, PRIMARY KEY(`purchased_note_uuid`))", "fields": [{"fieldPath": "googleProductId", "columnName": "google_product_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "purchasedNoteUUID", "columnName": "purchased_note_uuid", "affinity": "BLOB", "notNull": true}, {"fieldPath": "extension", "columnName": "extension", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["purchased_note_uuid"]}, "indices": [], "foreignKeys": []}, {"tableName": "promotion", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`promotionId` BLOB NOT NULL, `name` TEXT NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER NOT NULL, `overtimeHour` INTEGER NOT NULL, `markFinished` INTEGER NOT NULL, PRIMARY KEY(`promotionId`))", "fields": [{"fieldPath": "promotionId", "columnName": "promotionId", "affinity": "BLOB", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "overtimeHour", "columnName": "overtimeHour", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "markFinished", "columnName": "markFinished", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["promotionId"]}, "indices": [], "foreignKeys": []}, {"tableName": "check_in_record", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`recordId` BLOB NOT NULL, `roleId` TEXT NOT NULL, `checkInTime` INTEGER NOT NULL, `promotionName` TEXT NOT NULL, PRIMARY KEY(`recordId`))", "fields": [{"fieldPath": "recordId", "columnName": "recordId", "affinity": "BLOB", "notNull": true}, {"fieldPath": "roleId", "columnName": "roleId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "checkInTime", "columnName": "checkInTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "promotionName", "columnName": "promotionName", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["recordId"]}, "indices": [], "foreignKeys": []}, {"tableName": "note_limit_bonus", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`bonusId` BLOB NOT NULL, `roleId` TEXT NOT NULL, `gainedTime` INTEGER NOT NULL, `expiredTime` INTEGER NOT NULL, `used` INTEGER NOT NULL, `isPermanent` INTEGER NOT NULL, `extraInfo` TEXT NOT NULL, PRIMARY KEY(`bonusId`))", "fields": [{"fieldPath": "bonusId", "columnName": "bonusId", "affinity": "BLOB", "notNull": true}, {"fieldPath": "roleId", "columnName": "roleId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gainedTime", "columnName": "gainedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "expiredTime", "columnName": "expiredTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "used", "columnName": "used", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPermanent", "columnName": "isPermanent", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "extraInfo", "columnName": "extraInfo", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["bonusId"]}, "indices": [], "foreignKeys": []}, {"tableName": "folder", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`children` TEXT NOT NULL, `uuid` TEXT NOT NULL, `title` TEXT NOT NULL, `parent_folder` TEXT, `level` INTEGER NOT NULL, `createTime` INTEGER NOT NULL, `modifiedTime` INTEGER NOT NULL, `openedTime` INTEGER NOT NULL, `type` TEXT NOT NULL, `isHid` INTEGER NOT NULL, `colorTags` TEXT NOT NULL, PRIMARY KEY(`uuid`))", "fields": [{"fieldPath": "childrenIds", "columnName": "children", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "folder", "columnName": "parent_folder", "affinity": "TEXT", "notNull": false}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifiedTime", "columnName": "modifiedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "openedTime", "columnName": "openedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isHid", "columnName": "isHid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "colorTags", "columnName": "colorTags", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uuid"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e034d506c75e76a9cea0249c153fede5')"]}}
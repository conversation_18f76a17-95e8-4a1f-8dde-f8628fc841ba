<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/to_replace">
        <Constraint android:id="@+id/add_page_selected_flag">
            <Layout
                app:layout_constraintBottom_toBottomOf="@id/replace"
                app:layout_constraintEnd_toEndOf="@id/replace"
                app:layout_constraintStart_toStartOf="@id/replace"
                app:layout_constraintTop_toTopOf="@id/replace" />
        </Constraint>
        <Constraint android:id="@+id/replace">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/white" />
        </Constraint>
        <Constraint android:id="@+id/pre">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/next">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/last">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/to_pre">
        <Constraint android:id="@id/add_page_selected_flag">
            <Layout
                app:layout_constraintBottom_toBottomOf="@id/pre"
                app:layout_constraintEnd_toEndOf="@id/pre"
                app:layout_constraintStart_toStartOf="@id/pre"
                app:layout_constraintTop_toTopOf="@id/pre" />
        </Constraint>
        <Constraint android:id="@+id/replace">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/pre">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/white" />
        </Constraint>
        <Constraint android:id="@+id/next">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/last">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/to_next">
        <Constraint android:id="@id/add_page_selected_flag">
            <Layout
                app:layout_constraintBottom_toBottomOf="@id/next"
                app:layout_constraintEnd_toEndOf="@id/next"
                app:layout_constraintStart_toStartOf="@id/next"
                app:layout_constraintTop_toTopOf="@id/next" />
        </Constraint>
        <Constraint android:id="@+id/replace">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/pre">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/next">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/white" />
        </Constraint>
        <Constraint android:id="@+id/last">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/to_last">
        <Constraint android:id="@id/add_page_selected_flag">
            <Layout
                app:layout_constraintBottom_toBottomOf="@id/last"
                app:layout_constraintEnd_toEndOf="@id/last"
                app:layout_constraintStart_toStartOf="@id/last"
                app:layout_constraintTop_toTopOf="@id/last" />
        </Constraint>
        <Constraint android:id="@+id/replace">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/pre">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/next">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/bottom_sheet_black" />
        </Constraint>
        <Constraint android:id="@+id/last">
            <CustomAttribute
                app:attributeName="textColor"
                app:customColorValue="@color/white" />
        </Constraint>
    </ConstraintSet>

    <Transition
        app:constraintSetEnd="@id/to_replace"
        app:constraintSetStart="@id/to_next" />

</MotionScene>

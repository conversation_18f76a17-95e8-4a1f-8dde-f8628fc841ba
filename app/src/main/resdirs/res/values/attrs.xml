<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="DotIndicator">
        <attr name="selectedColor" format="color" />
        <attr name="unselectedColor" format="color" />
        <attr name="radius" format="dimension" />
        <attr name="dotInterval" format="dimension" />
    </declare-styleable>
    <declare-styleable name="CommonInputLayout">
        <attr name="defaultText" format="string" />
        <attr name="textColor" format="color" />
        <attr name="textSize" format="dimension" />
        <attr name="textAlignment" format="integer">
            <enum name="inherit" value="0" />
            <enum name="gravity" value="1" />
            <enum name="textStart" value="2" />
            <enum name="textEnd" value="3" />
            <enum name="center" value="4" />
            <enum name="viewStart" value="5" />
            <enum name="viewEnd" value="6" />
        </attr>
        <attr name="editTextPaddingTop" format="dimension" />
        <attr name="editTextPaddingBottom" format="dimension" />
        <attr name="editTextPaddingStart" format="dimension" />
        <attr name="editTextPaddingEnd" format="dimension" />
        <attr name="text_hint" format="string" />
    </declare-styleable>
    <declare-styleable name="SidebarPenStyle">
        <attr name="brushColor" format="color" />
        <attr name="brushHeight" format="dimension" />
    </declare-styleable>
    <declare-styleable name="CircleSizeSelectorItemView">
        <attr name="selectorRadius" format="dimension" />
    </declare-styleable>
    <declare-styleable name="NoteInfoLabelBg">
        <attr name="circle_radius" format="dimension" />
        <attr name="circle_center_distance" format="dimension" />
        <attr name="mask_paint_stroke_width" format="dimension" />
    </declare-styleable>


    <!--  RCRelativeLayout相关属性  https://github.com/GcsSloop/rclayout  -->
    <!-- *公共属性* -->
    <!--圆形-->
    <attr name="round_as_circle" format="boolean" />
    <!--全部圆角半径-->
    <attr name="round_corner" format="integer|dimension" />
    <!--针对各个角的半径-->
    <attr name="round_corner_top_left" format="integer|dimension" />
    <attr name="round_corner_top_right" format="integer|dimension" />
    <attr name="round_corner_bottom_left" format="integer|dimension" />
    <attr name="round_corner_bottom_right" format="integer|dimension" />

    <!--描边颜色／半径-->
    <attr name="stroke_color" format="color|reference" />
    <attr name="stroke_width" format="integer|dimension" />

    <!-- 是否剪裁 RCLayout 的背景 -->
    <attr name="clip_background" format="boolean" />

    <!--真正用于解析的属性-->
    <declare-styleable name="RCAttrs">
        <attr name="round_as_circle" />
        <attr name="round_corner" />
        <attr name="round_corner_top_left" />
        <attr name="round_corner_top_right" />
        <attr name="round_corner_bottom_left" />
        <attr name="round_corner_bottom_right" />
        <attr name="stroke_color" />
        <attr name="stroke_width" />
        <attr name="clip_background" />
    </declare-styleable>

    <!--假体：用于提示-->
    <declare-styleable name="RCRelativeLayout">
        <attr name="round_as_circle" />
        <attr name="round_corner" />
        <attr name="round_corner_top_left" />
        <attr name="round_corner_top_right" />
        <attr name="round_corner_bottom_left" />
        <attr name="round_corner_bottom_right" />
        <attr name="stroke_color" />
        <attr name="stroke_width" />
        <attr name="clip_background" />
    </declare-styleable>

    <declare-styleable name="ColorSelectView">
        <attr name="item_size" format="dimension" />
        <attr name="item_interval" format="dimension" />
    </declare-styleable>


    <!--  tablet flag -->
    <bool name="isPhone">true</bool>
    <!--  tablet flag -->

    <!--  ColorPickView  -->
    <declare-styleable name="ColorPickView">
        <attr name="default_color" format="color" />
        <attr name="hue_panel_height" format="dimension" />
        <attr name="hue_tracker_width" format="dimension" />
        <attr name="hue_tracker_height" format="dimension" />
        <attr name="hue_tracker_color" format="color" />
        <attr name="hue_tracker_stroke_width" format="dimension" />
        <attr name="hue_tracker_stroke_color" format="color" />
        <attr name="saturation_value_panel_height" format="dimension" />
        <attr name="saturation_value_tracker_radius" format="dimension" />
        <attr name="saturation_value_tracker_stroke_width" format="dimension" />
        <attr name="saturation_value_tracker_stroke_color" format="color" />
        <attr name="panel_interval" format="dimension" />
    </declare-styleable>

    <attr name="progress" format="float" />
    <attr name="max_progress" format="float" />
    <attr name="min_progress" format="float" />

    <declare-styleable name="PenSizeSeekBar">
        <attr name="progress_background_radio" format="dimension" />
        <attr name="progress_background_color" format="color" />
        <attr name="progress_tracker_radius" format="dimension" />
        <attr name="process_tracker_background" format="reference" />
        <attr name="progress" />
        <attr name="max_progress" />
        <attr name="min_progress" />
    </declare-styleable>

    <declare-styleable name="CircleProgressView">
        <attr name="progress_color" format="color" />
        <attr name="circle_stroke" format="dimension" />
    </declare-styleable>

    <declare-styleable name="ProgressButton">
        <attr name="progressColor" format="color" />
        <attr name="progressBackColor" format="color" />
        <attr name="buttonColor" format="color" />
        <attr name="cornerRadius" format="dimension" />
        <attr name="progress" />
        <attr name="max_progress" />
        <attr name="min_progress" />
        <attr name="progressMargin" format="dimension" />
    </declare-styleable>

    <declare-styleable name="LoadingView">
        <attr name="dotRadius" format="dimension" />
        <attr name="bounceHeight" format="dimension" />
        <attr name="dotSpace" format="dimension" />
    </declare-styleable>

    <declare-styleable name="SideShadowLayout">
        <attr name="shadow_direction" format="string" />
        <attr name="shadow_viewRadius" format="dimension" />
        <attr name="shadow_scope" format="dimension" />
        <attr name="shadow_offsetX" format="dimension" />
        <attr name="shadow_offsetY" format="dimension" />
        <attr name="shadow_startColor" format="color" />
        <attr name="shadow_endColor" format="color" />
    </declare-styleable>

    <declare-styleable name="AdsorptionEdgeLayout">
        <attr name="customIsAttach" format="boolean" />
        <attr name="customIsDrag" format="boolean" />
        <attr name="dragPoint" format="reference" />
        <attr name="leftView" format="reference" />
        <attr name="topView" format="reference" />
        <attr name="rightView" format="reference" />
        <attr name="bottomView" format="reference" />
    </declare-styleable>

    <declare-styleable name="CustomRecycleView">
        <attr name="maxHeight" format="dimension" />
    </declare-styleable>

    <declare-styleable name="RealTimeBlurView">
        <attr name="blurRadius" format="integer" />
        <attr name="downSampleFactor" format="float" />
        <attr name="overlayColor" format="color" />
    </declare-styleable>

    <declare-styleable name="BottomDraggableLayout">
        <attr name="dragViewId" format="reference" />
        <attr name="contentViewId" format="reference" />
        <attr name="variableViewId" format="reference" />
        <attr name="invariableTopViewId" format="reference" />
        <attr name="invariableBottomViewId" format="reference" />
        <attr name="variableListViewId" format="reference" />
    </declare-styleable>

    <declare-styleable name="OverScrollCoordinatorRecyclerView">
        <attr name="scrollOrientation">
            <flag name="horizontal" value="0" />
            <flag name="vertical" value="1" />
        </attr>
        <attr name="recyclerViewPaddingTop" format="dimension" />
        <attr name="recyclerViewClipToPadding" format="boolean" />
    </declare-styleable>

    <declare-styleable name="FormatVerificationInputLayout">
        <attr name="hint_text_color" format="color" />
        <attr name="text_background" format="reference|color" />
        <attr name="text_size" format="dimension" />
        <attr name="text_padding_start" format="dimension" />
        <attr name="text_padding_end" format="dimension" />
        <attr name="default_text" format="string" />
        <attr name="hint_text" format="string" />
        <attr name="text_color" format="color" />
        <attr name="verify_length" format="integer" />
        <attr name="verifyType" format="flags">
            <flag name="not_verify" value="0x10000" />
            <flag name="small_letter" value="0x00001" />
            <flag name="capital_letter" value="0x00010" />
            <flag name="number" value="0x00100" />
        </attr>
    </declare-styleable>
    <declare-styleable name="EllipsizedTextView">
        <attr name="ellipsis" format="string" />
        <attr name="ellipsisColor" format="color" />
    </declare-styleable>

    <declare-styleable name="WaveView">
        <!--波浪的长度、高度、颜色和每次重绘的偏移量-->
        <attr name="waveViewLength" format="integer" />
        <attr name="waveViewHeight" format="integer" />
        <attr name="waveViewColor" format="color" />
        <attr name="waveViewOffset" format="integer" />

        <!--水位高度的百分比-->
        <attr name="percent" format="float" />
        <!--两次重绘的间隔时间-->
        <attr name="intervalTime" format="integer" />
        <!--控件的显示形状，rect矩形、circle圆形-->
        <attr name="showShape" format="enum">
            <enum name="rect" value="0" />
            <enum name="circle" value="1" />
        </attr>
    </declare-styleable>
    <declare-styleable name="BubbleLayout">
        <attr name="bubble_leg_offset" format="float" />
        <attr name="bubble_leg_width" format="dimension" />
        <attr name="bubble_leg_height" format="dimension" />
        <attr name="bubble_orientation" format="enum">
            <enum name="left" value="0" />
            <enum name="top" value="1" />
            <enum name="right" value="2" />
            <enum name="bottom" value="3" />
        </attr>
        <attr name="bubble_corner_radius" format="dimension" />
        <attr name="bubble_background_color" format="color" />
        <attr name="bubble_shadow_radius" format="dimension" />
        <attr name="bubble_type" format="enum">
            <enum name="rect" value="0" />
            <enum name="triangle" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="DraggableLayout">
        <attr name="change_position_view" format="reference" />
        <attr name="change_size_view" format="reference" />
        <attr name="minWidth" />
        <attr name="minHeight" />
    </declare-styleable>

    <!--  ConsoleBoarderShadowView  -->
    <declare-styleable name="ConsoleBorderShadowView">
        <attr name="border_stroke_width" format="dimension" />
        <attr name="border_corner_radius" format="dimension" />
        <attr name="outer_border_color" format="color" />
        <attr name="inner_border_color" format="color" />
        <attr name="inside_background_color" format="color" />
        <attr name="border_type" format="enum">
            <enum name="outer_border_overlap" value="0" />
            <enum name="inner_border_overlap" value="1" />
            <enum name="inner_border_overlap_completely_background" value="2" />
        </attr>
        <attr name="border_shadow_color" format="color" />
        <attr name="border_shadow_inner_color" format="color" />
        <attr name="border_shadow_size" format="dimension" />
        <attr name="border_shadow_inner_size" format="dimension" />
        <attr name="border_shadow_x_offset" format="dimension" />
        <attr name="border_shadow_y_offset" format="dimension" />
    </declare-styleable>

    <declare-styleable name="DoubleImageLayerToolView">
        <attr name="tintLayerSrc" format="reference" />
        <attr name="borderLayerSrc" format="reference" />
    </declare-styleable>
</resources>
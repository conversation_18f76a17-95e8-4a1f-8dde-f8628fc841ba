<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="CircleImageStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="SmallCoverImageStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">@dimen/dp_4</item>
        <item name="cornerSizeTopRight">@dimen/dp_12</item>
        <item name="cornerSizeBottomLeft">@dimen/dp_4</item>
        <item name="cornerSizeBottomRight">@dimen/dp_12</item>
    </style>

    <style name="LargeCoverImageStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">@dimen/dp_8</item>
        <item name="cornerSizeTopRight">@dimen/dp_20</item>
        <item name="cornerSizeBottomLeft">@dimen/dp_8</item>
        <item name="cornerSizeBottomRight">@dimen/dp_20</item>
    </style>
    <style name="BottomSheetEdit" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>


    <style name="SwitchStyle">
        <item name="android:layout_width">@dimen/dp_60</item>
        <item name="android:layout_height">@dimen/dp_30</item>
        <item name="android:minWidth">@dimen/dp_60</item>
        <item name="splitTrack">false</item>
        <item name="android:thumb">@drawable/setting_image_optimization_switch_thumb_selector</item>
        <item name="android:track">@drawable/setting_image_optimization_switch_track_selector</item>
    </style>

    <style name="PhoneSwitchStyle">
        <item name="android:layout_width">@dimen/dp_130</item>
        <item name="android:layout_height">@dimen/dp_64</item>
        <item name="android:minWidth">@dimen/dp_64</item>
        <item name="splitTrack">false</item>
        <item name="android:thumb">
            @drawable/phone_setting_image_optimization_switch_thumb_selector
        </item>
        <item name="android:track">
            @drawable/phone_setting_image_optimization_switch_track_selector
        </item>
    </style>

    <style name="Dialog.FullScreen" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="DialogFullScreenFloatTheme" parent="Dialog.FullScreen">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>

    <style name="DialogImmersionFullScreenTheme" parent="Theme.MaterialComponents.Dialog">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="CustomBottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
    </style>

    <style name="CustomBottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

</resources>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- base application theme -->
    <style name="Theme.Kilonotes" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:textDirection">locale</item>
    </style>
    <!-- base application theme -->

    <!--   no title bar  -->
    <style name="Theme.Kilonotes.NoTitleBar" parent ="Theme.Kilonotes">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
    </style>
    <!--   no title bar -->

    <!--  full screen  -->
    <style name="Theme.Kilonotes.NoTitleBar.FullScreen" parent ="Theme.Kilonotes.NoTitleBar">
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
    <!--  full screen  -->

    <style name="Theme.Kilonotes.Dialog" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.18</item>
    </style>
    
    <style name="Theme.Kilonotes.SwitchCompat" parent="Theme.AppCompat.Light">
        <item name="colorControlActivated">@color/switch_thumb_activated</item>
    </style>

</resources>
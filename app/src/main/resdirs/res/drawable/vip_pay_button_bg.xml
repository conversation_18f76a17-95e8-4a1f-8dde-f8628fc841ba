<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/text_disable" />
            <corners android:radius="@dimen/dp_60" />
        </shape>
    </item>
    <item android:state_enabled="true">
        <shape android:shape="rectangle">
            <gradient android:endColor="@color/button_gradient_end" android:startColor="@color/button_gradient_start" />
            <corners android:radius="@dimen/dp_60" />
        </shape>
    </item>
</selector>
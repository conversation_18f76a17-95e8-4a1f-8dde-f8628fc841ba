<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M52.087,8C55.353,8 58,10.647 58,13.913L58,52.087C58,55.353 55.353,58 52.087,58L13.913,58C10.647,58 8,55.353 8,52.087L8,13.913C8,10.647 10.647,8 13.913,8L52.087,8ZM15,46C13.343,46 12,47.343 12,49C12,50.657 13.343,52 15,52C16.657,52 18,50.657 18,49C18,47.343 16.657,46 15,46ZM15,30C13.343,30 12,31.343 12,33C12,34.657 13.343,36 15,36C16.657,36 18,34.657 18,33C18,31.343 16.657,30 15,30ZM15,14C13.343,14 12,15.343 12,17C12,18.657 13.343,20 15,20C16.657,20 18,18.657 18,17C18,15.343 16.657,14 15,14Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18.065"
          android:startY="11.991"
          android:endX="46.589"
          android:endY="58"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M51.636,52C52.389,52 53,52.448 53,53C53,53.552 52.389,54 51.636,54L51.636,54L24.364,54C23.611,54 23,53.552 23,53C23,52.448 23.611,52 24.364,52L24.364,52ZM24.364,46L51.636,46C52.389,46 53,46.448 53,47C53,47.518 52.463,47.944 51.776,47.995L51.636,48L24.364,48C23.611,48 23,47.552 23,47C23,46.482 23.537,46.056 24.224,46.005L24.364,46L51.636,46ZM51.636,39C52.389,39 53,39.448 53,40C53,40.518 52.463,40.944 51.776,40.995L51.636,41L24.364,41C23.611,41 23,40.552 23,40C23,39.482 23.537,39.056 24.224,39.005L24.364,39L51.636,39ZM51.636,33C52.389,33 53,33.448 53,34C53,34.518 52.463,34.944 51.776,34.995L51.636,35L24.364,35C23.611,35 23,34.552 23,34C23,33.482 23.537,33.056 24.224,33.005L24.364,33L51.636,33ZM51.636,27C52.389,27 53,27.448 53,28C53,28.518 52.463,28.944 51.776,28.995L51.636,29L24.364,29C23.611,29 23,28.552 23,28C23,27.482 23.537,27.056 24.224,27.005L24.364,27L51.636,27ZM51.636,20C52.389,20 53,20.448 53,21C53,21.518 52.463,21.944 51.776,21.995L51.636,22L24.364,22C23.611,22 23,21.552 23,21C23,20.482 23.537,20.056 24.224,20.005L24.364,20L51.636,20ZM51.636,14C52.389,14 53,14.448 53,15C53,15.518 52.463,15.944 51.776,15.995L51.636,16L24.364,16C23.611,16 23,15.552 23,15C23,14.482 23.537,14.056 24.224,14.005L24.364,14L51.636,14Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="38"
          android:startY="14"
          android:endX="38"
          android:endY="54"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

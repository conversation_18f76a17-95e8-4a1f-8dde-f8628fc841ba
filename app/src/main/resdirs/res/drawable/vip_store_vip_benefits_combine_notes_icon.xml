<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M24.791,7C28.22,7 31,9.78 31,13.209L31,50.791C31,54.22 28.22,57 24.791,57L12.209,57C8.78,57 6,54.22 6,50.791L6,43.999L8.051,44C9.621,44 10.905,42.772 10.995,41.224L11,41.051L11,40.949C11,39.32 9.68,38 8.051,38L8.051,38L6,37.999L6,27.999L8.051,28C9.621,28 10.905,26.772 10.995,25.224L11,25.051L11,24.949C11,23.32 9.68,22 8.051,22L8.051,22L6,21.999L6,13.209C6,9.78 8.78,7 12.209,7L24.791,7Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.633"
          android:startY="10.991"
          android:endX="20.199"
          android:endY="57"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M4.5,23L7.5,23A1.5,1.5 0,0 1,9 24.5L9,24.5A1.5,1.5 0,0 1,7.5 26L4.5,26A1.5,1.5 0,0 1,3 24.5L3,24.5A1.5,1.5 0,0 1,4.5 23z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M4.5,39L7.5,39A1.5,1.5 0,0 1,9 40.5L9,40.5A1.5,1.5 0,0 1,7.5 42L4.5,42A1.5,1.5 0,0 1,3 40.5L3,40.5A1.5,1.5 0,0 1,4.5 39z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M18.381,23.435C18.904,22.87 19.767,22.853 20.31,23.397L20.31,23.397L27.582,30.696C28.125,31.24 28.141,32.14 27.618,32.706L27.618,32.706L20.345,40.565C19.822,41.13 18.959,41.147 18.417,40.602C17.875,40.057 17.859,39.157 18.382,38.593L18.382,38.593L23.557,32.999L10.8,33C9.858,33 9.085,32.327 9.007,31.618L9,31.5C9,30.75 9.806,30 10.8,30L10.8,30L22.956,29.999L18.418,25.445C17.911,24.937 17.864,24.119 18.284,23.553Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18.5"
          android:startY="23"
          android:endX="18.5"
          android:endY="41"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M40.209,7C36.78,7 34,9.78 34,13.209L34,50.791C34,54.22 36.78,57 40.209,57L52.791,57C56.22,57 59,54.22 59,50.791L59.001,43.999L57.949,44C56.379,44 55.095,42.772 55.005,41.224L55,41.051L55,40.949C55,39.32 56.32,38 57.949,38L57.949,38L59.001,37.999L59.001,27.999L57.949,28C56.379,28 55.095,26.772 55.005,25.224L55,25.051L55,24.949C55,23.32 56.32,22 57.949,22L57.949,22L59.001,21.999L59,13.209C59,9.78 56.22,7 52.791,7L40.209,7Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="44.634"
          android:startY="10.991"
          android:endX="48.199"
          android:endY="57"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M60.398,23L57.398,23A1.5,1.5 0,0 0,55.898 24.5L55.898,24.5A1.5,1.5 0,0 0,57.398 26L60.398,26A1.5,1.5 0,0 0,61.898 24.5L61.898,24.5A1.5,1.5 0,0 0,60.398 23z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M60.398,39L57.398,39A1.5,1.5 0,0 0,55.898 40.5L55.898,40.5A1.5,1.5 0,0 0,57.398 42L60.398,42A1.5,1.5 0,0 0,61.898 40.5L61.898,40.5A1.5,1.5 0,0 0,60.398 39z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M46.619,23.435C46.096,22.87 45.233,22.853 44.69,23.397L44.69,23.397L37.418,30.696C36.875,31.24 36.859,32.14 37.382,32.706L37.382,32.706L44.655,40.565C45.178,41.13 46.041,41.147 46.583,40.602C47.125,40.057 47.141,39.157 46.618,38.593L46.618,38.593L41.443,32.999L54.2,33C55.142,33 55.915,32.327 55.993,31.618L56,31.5C56,30.75 55.194,30 54.2,30L54.2,30L42.044,29.999L46.582,25.445C47.089,24.937 47.136,24.119 46.716,23.553Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="46.5"
          android:startY="23"
          android:endX="46.5"
          android:endY="41"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

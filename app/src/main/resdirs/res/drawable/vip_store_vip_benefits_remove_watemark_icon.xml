<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.5,32.5m-26.5,0a26.5,26.5 0,1 1,53 0a26.5,26.5 0,1 1,-53 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.669"
          android:startY="10.231"
          android:endX="46.905"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M31.992,46C29.341,46.007 26.798,44.947 24.929,43.056C23.048,41.176 21.993,38.616 22,35.948C22,34.205 22.978,31.725 24.906,28.577L23.161,26.821C22.853,26.511 22.68,26.091 22.68,25.653C22.68,25.214 22.853,24.794 23.161,24.484C23.469,24.173 23.887,23.999 24.323,24C24.758,23.999 25.177,24.173 25.484,24.484L42.519,41.622C42.828,41.932 43.001,42.352 43,42.791C43.001,43.229 42.828,43.65 42.519,43.96C42.211,44.27 41.793,44.444 41.357,44.443C40.922,44.444 40.504,44.27 40.196,43.96L39.177,42.933C38.567,43.569 37.876,44.121 37.123,44.575C35.572,45.509 33.799,46.001 31.992,46L31.992,46ZM25.025,37.057C25.628,39.783 27.474,41.999 29.934,42.95C30.019,42.982 30.109,42.999 30.199,43L30.205,43C30.541,42.998 30.839,42.771 30.952,42.433L30.952,42.429C30.983,42.338 30.999,42.241 31,42.143L31,42.131C30.997,41.77 30.785,41.45 30.469,41.326C28.519,40.571 27.055,38.814 26.576,36.653C26.489,36.269 26.169,35.999 25.8,36C25.736,36 25.673,36.008 25.611,36.024C25.405,36.077 25.227,36.217 25.117,36.411C25.006,36.605 24.972,36.838 25.023,37.059L25.025,37.057Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.5"
          android:startY="24"
          android:endX="32.5"
          android:endY="46"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M42.077,37L28,23.358C29.368,21.47 30.76,19.773 31.686,18.681C32.05,18.249 32.597,17.999 33.173,18C33.749,17.999 34.295,18.249 34.66,18.681C36.533,20.869 38.27,23.164 39.86,25.553C41.944,28.748 43,31.244 43,32.972C43.004,34.364 42.689,35.739 42.079,36.999L42.077,37Z"
      android:strokeAlpha="0.45584542"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:fillAlpha="0.45584542">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="35.5"
          android:startY="18"
          android:endX="35.5"
          android:endY="37"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

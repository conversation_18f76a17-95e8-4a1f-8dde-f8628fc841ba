<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M28.959,42.872C26.97,42.253 25.262,41.145 24.014,39.657C22.694,38.086 22,36.223 22,34.272L22,19.131C22,16.639 23.129,14.318 25.174,12.586C27.141,10.921 29.746,10 32.5,10C35.254,10 37.859,10.916 39.826,12.586C41.871,14.318 43,16.644 43,19.131L43,34.272C43,36.233 42.301,38.101 40.976,39.673C39.723,41.161 38.004,42.269 36.005,42.883C35.845,42.93 29.777,43.127 28.959,42.872Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.96"
          android:startY="12.634"
          android:endX="34.811"
          android:endY="43"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M32.5,50C28.189,50 24.112,48.608 21.03,46.082C19.477,44.81 18.254,43.319 17.391,41.644C16.468,39.857 16,37.951 16,35.979C16,34.884 16.803,34 17.799,34C18.794,34 19.597,34.884 19.597,35.979C19.597,38.557 20.868,41.017 23.177,42.91C25.647,44.935 28.957,46.043 32.5,46.043C36.043,46.043 39.353,44.928 41.823,42.91C44.132,41.017 45.403,38.557 45.403,35.979C45.403,34.884 46.206,34 47.201,34C48.197,34 49,34.884 49,35.979C49,37.951 48.532,39.857 47.609,41.644C46.746,43.319 45.523,44.81 43.97,46.082C40.888,48.608 36.811,50 32.5,50Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="26.65"
          android:startY="40.119"
          android:endX="31.377"
          android:endY="45.077"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M27.955,29L24.045,29C23.467,29 23,28.33 23,27.5C23,26.67 23.467,26 24.045,26L27.955,26C28.533,26 29,26.67 29,27.5C29,28.33 28.533,29 27.955,29ZM27.955,23L24.045,23C23.467,23 23,22.33 23,21.5C23,20.67 23.467,20 24.045,20L27.955,20C28.533,20 29,20.67 29,21.5C29,22.33 28.533,23 27.955,23ZM27.955,35L24.045,35C23.467,35 23,34.33 23,33.5C23,32.67 23.467,32 24.045,32L27.955,32C28.533,32 29,32.67 29,33.5C29,34.33 28.533,35 27.955,35ZM40.955,29L37.045,29C36.467,29 36,28.33 36,27.5C36,26.67 36.467,26 37.045,26L40.955,26C41.533,26 42,26.67 42,27.5C42,28.33 41.53,29 40.955,29ZM40.955,23L37.045,23C36.467,23 36,22.33 36,21.5C36,20.67 36.467,20 37.045,20L40.955,20C41.533,20 42,20.67 42,21.5C42,22.33 41.53,23 40.955,23ZM40.955,35L37.045,35C36.467,35 36,34.33 36,33.5C36,32.67 36.467,32 37.045,32L40.955,32C41.533,32 42,32.67 42,33.5C42,34.33 41.53,35 40.955,35Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.5"
          android:startY="20"
          android:endX="32.5"
          android:endY="35"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M43.855,59C42.669,59 41.711,58.283 41.711,57.396C41.711,56.668 41.439,56.578 41.203,56.492C40.488,56.246 39.123,56.209 37.879,56.209L27.121,56.209C25.877,56.209 24.512,56.241 23.797,56.492C23.561,56.578 23.289,56.668 23.289,57.396C23.289,58.283 22.331,59 21.145,59C19.958,59 19,58.283 19,57.396C19,55.107 20.623,54.059 21.988,53.583C23.532,53.048 25.441,53 27.121,53L37.879,53C39.552,53 41.468,53.043 43.012,53.583C44.377,54.059 46,55.102 46,57.396C46,58.283 45.035,59 43.855,59L43.855,59Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.5"
          android:startY="55.747"
          android:endX="35.538"
          android:endY="56.208"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M32.5,55C31.67,55 31,54.202 31,53.213L31,49.787C31,48.798 31.67,48 32.5,48C33.33,48 34,48.798 34,49.787L34,53.213C34,54.202 33.33,55 32.5,55Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.5"
          android:startY="45.525"
          android:endX="32.562"
          android:endY="56.414"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

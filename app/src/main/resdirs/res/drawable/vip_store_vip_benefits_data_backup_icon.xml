<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.5,32.5m-26.5,0a26.5,26.5 0,1 1,53 0a26.5,26.5 0,1 1,-53 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.669"
          android:startY="10.231"
          android:endX="46.905"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M34.405,20.737L45.149,26.498C45.778,26.835 46.015,27.619 45.677,28.249C45.558,28.472 45.375,28.656 45.152,28.776L34.414,34.568C33.259,35.191 31.868,35.188 30.715,34.56L20.092,28.773C19.464,28.432 19.233,27.646 19.574,27.019C19.694,26.799 19.875,26.619 20.094,26.5L30.724,20.744C31.872,20.123 33.255,20.12 34.405,20.737Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.624"
          android:startY="20.276"
          android:endX="32.624"
          android:endY="35.033"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M45.42,32.274L34.496,38.166C33.615,38.642 32.554,38.64 31.675,38.161L20.868,32.275C20.39,32.015 19.79,32.191 19.529,32.67C19.269,33.149 19.445,33.748 19.924,34.009L30.73,39.894C32.195,40.692 33.965,40.696 35.433,39.903L46.357,34.011C46.836,33.752 47.016,33.153 46.757,32.674C46.498,32.194 45.9,32.015 45.42,32.274Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33.142"
          android:startY="32.155"
          android:endX="33.142"
          android:endY="40.495"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M45.42,37.274L34.496,43.166C33.615,43.642 32.554,43.64 31.675,43.161L20.868,37.275C20.39,37.015 19.79,37.191 19.529,37.67C19.269,38.149 19.445,38.748 19.924,39.009L30.73,44.894C32.195,45.692 33.965,45.696 35.433,44.903L46.357,39.011C46.836,38.752 47.016,38.153 46.757,37.674C46.498,37.194 45.9,37.015 45.42,37.274Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33.142"
          android:startY="37.155"
          android:endX="33.142"
          android:endY="45.495"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

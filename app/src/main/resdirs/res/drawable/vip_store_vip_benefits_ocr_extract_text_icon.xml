<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M17.826,18L47.174,18A11.826,11.826 0,0 1,59 29.826L59,36.174A11.826,11.826 0,0 1,47.174 48L17.826,48A11.826,11.826 0,0 1,6 36.174L6,29.826A11.826,11.826 0,0 1,17.826 18z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.669"
          android:startY="28.961"
          android:endX="46.905"
          android:endY="37.806"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M19,40C17.654,40 16.546,39.794 15.676,39.382C14.806,38.971 14.143,38.255 13.686,37.235C13.229,36.216 13,34.804 13,33C13,31.196 13.229,29.784 13.686,28.765C14.143,27.745 14.806,27.029 15.676,26.618C16.546,26.206 17.654,26 19,26C20.371,26 21.489,26.206 22.352,26.618C23.216,27.029 23.873,27.745 24.324,28.765C24.775,29.784 25,31.196 25,33C25,34.804 24.775,36.216 24.324,37.235C23.873,38.255 23.216,38.971 22.352,39.382C21.489,39.794 20.371,40 19,40ZM16,32.529C16,33.695 16.08,34.593 16.241,35.221C16.402,35.85 16.695,36.303 17.119,36.582C17.543,36.861 18.167,37 18.99,37C19.814,37 20.441,36.861 20.871,36.582C21.302,36.303 21.598,35.85 21.759,35.221C21.92,34.593 22,33.695 22,32.529C22,31.376 21.92,30.482 21.759,29.847C21.598,29.212 21.302,28.745 20.871,28.447C20.441,28.149 19.814,28 18.99,28C18.18,28 17.559,28.149 17.129,28.447C16.698,28.745 16.402,29.212 16.241,29.847C16.08,30.482 16,31.376 16,32.529ZM33.653,40C32.201,40 30.997,39.804 30.04,39.412C29.083,39.02 28.337,38.31 27.802,37.284C27.267,36.258 27,34.83 27,33C27,30.49 27.568,28.696 28.703,27.618C29.838,26.539 31.482,26 33.634,26C34.901,26 35.97,26.15 36.842,26.451L36.842,29.02C35.931,28.706 34.927,28.549 33.832,28.549C32.987,28.549 32.307,28.69 31.792,28.971C31.277,29.252 30.894,29.712 30.644,30.353C30.393,30.993 30.267,31.869 30.267,32.98C30.267,34.144 30.389,35.046 30.634,35.686C30.878,36.327 31.254,36.775 31.762,37.029C32.271,37.284 32.947,37.412 33.792,37.412C34.941,37.412 36.01,37.235 37,36.882L37,39.431C36.578,39.601 36.076,39.739 35.495,39.843C34.914,39.948 34.3,40 33.653,40ZM43.388,34.896L41.938,34.896L41.938,40L39,40L39,26L43.562,26C45.393,26 46.714,26.34 47.525,27.019C48.337,27.698 48.743,28.838 48.743,30.438C48.743,31.595 48.54,32.506 48.134,33.171C47.728,33.837 47.094,34.305 46.23,34.573L50,40L46.54,40L43.388,34.896ZM43.214,33C43.917,33 44.468,32.942 44.866,32.827C45.264,32.712 45.552,32.506 45.731,32.212C45.91,31.917 46,31.487 46,30.923C46,30.397 45.91,29.997 45.731,29.721C45.552,29.446 45.267,29.256 44.876,29.154C44.484,29.051 43.93,29 43.214,29L42,29L42,33L43.214,33Z"
      android:strokeWidth="1"
      android:fillColor="#040404"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M19,40C17.654,40 16.546,39.794 15.676,39.382C14.806,38.971 14.143,38.255 13.686,37.235C13.229,36.216 13,34.804 13,33C13,31.196 13.229,29.784 13.686,28.765C14.143,27.745 14.806,27.029 15.676,26.618C16.546,26.206 17.654,26 19,26C20.371,26 21.489,26.206 22.352,26.618C23.216,27.029 23.873,27.745 24.324,28.765C24.775,29.784 25,31.196 25,33C25,34.804 24.775,36.216 24.324,37.235C23.873,38.255 23.216,38.971 22.352,39.382C21.489,39.794 20.371,40 19,40ZM16,32.529C16,33.695 16.08,34.593 16.241,35.221C16.402,35.85 16.695,36.303 17.119,36.582C17.543,36.861 18.167,37 18.99,37C19.814,37 20.441,36.861 20.871,36.582C21.302,36.303 21.598,35.85 21.759,35.221C21.92,34.593 22,33.695 22,32.529C22,31.376 21.92,30.482 21.759,29.847C21.598,29.212 21.302,28.745 20.871,28.447C20.441,28.149 19.814,28 18.99,28C18.18,28 17.559,28.149 17.129,28.447C16.698,28.745 16.402,29.212 16.241,29.847C16.08,30.482 16,31.376 16,32.529ZM33.653,40C32.201,40 30.997,39.804 30.04,39.412C29.083,39.02 28.337,38.31 27.802,37.284C27.267,36.258 27,34.83 27,33C27,30.49 27.568,28.696 28.703,27.618C29.838,26.539 31.482,26 33.634,26C34.901,26 35.97,26.15 36.842,26.451L36.842,29.02C35.931,28.706 34.927,28.549 33.832,28.549C32.987,28.549 32.307,28.69 31.792,28.971C31.277,29.252 30.894,29.712 30.644,30.353C30.393,30.993 30.267,31.869 30.267,32.98C30.267,34.144 30.389,35.046 30.634,35.686C30.878,36.327 31.254,36.775 31.762,37.029C32.271,37.284 32.947,37.412 33.792,37.412C34.941,37.412 36.01,37.235 37,36.882L37,39.431C36.578,39.601 36.076,39.739 35.495,39.843C34.914,39.948 34.3,40 33.653,40ZM43.388,34.896L41.938,34.896L41.938,40L39,40L39,26L43.562,26C45.393,26 46.714,26.34 47.525,27.019C48.337,27.698 48.743,28.838 48.743,30.438C48.743,31.595 48.54,32.506 48.134,33.171C47.728,33.837 47.094,34.305 46.23,34.573L50,40L46.54,40L43.388,34.896ZM43.214,33C43.917,33 44.468,32.942 44.866,32.827C45.264,32.712 45.552,32.506 45.731,32.212C45.91,31.917 46,31.487 46,30.923C46,30.397 45.91,29.997 45.731,29.721C45.552,29.446 45.267,29.256 44.876,29.154C44.484,29.051 43.93,29 43.214,29L42,29L42,33L43.214,33Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="31.5"
          android:startY="26"
          android:endX="31.5"
          android:endY="40"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M21.046,12L42,41.465L35.383,49L18.67,20.297C18.004,18.25 17.835,16.655 18.161,15.514C18.488,14.372 19.449,13.201 21.046,12ZM33.5,35C32.119,35 31,36.119 31,37.5C31,38.881 32.119,40 33.5,40C34.881,40 36,38.881 36,37.5C36,36.119 34.881,35 33.5,35Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="26.984"
          android:startY="14.953"
          android:endX="32.745"
          android:endY="49"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M44,41C48.971,41 53,45.029 53,50C53,54.971 48.971,59 44,59C39.029,59 35,54.971 35,50C35,45.029 39.029,41 44,41ZM43.5,46C41.567,46 40,47.567 40,49.5C40,51.433 41.567,53 43.5,53C45.433,53 47,51.433 47,49.5C47,47.567 45.433,46 43.5,46Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="38.623"
          android:startY="42.437"
          android:endX="48.892"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M22,41C26.971,41 31,45.029 31,50C31,54.971 26.971,59 22,59C17.029,59 13,54.971 13,50C13,45.029 17.029,41 22,41ZM21.5,46C19.567,46 18,47.567 18,49.5C18,51.433 19.567,53 21.5,53C23.433,53 25,51.433 25,49.5C25,47.567 23.433,46 21.5,46Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.623"
          android:startY="42.437"
          android:endX="26.892"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M43.086,26.711L39.192,34L35.119,28.289L36.215,28.045C38.575,27.529 40.865,27.084 43.086,26.711ZM44.343,12C45.527,12.925 46.343,14.164 46.749,15.52C44.923,15.767 43.084,16.071 41.23,16.431Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="39.9"
          android:startY="13.756"
          android:endX="41.874"
          android:endY="34"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M27.783,36l-3.783,5.363l6.826,7.637l2.174,-4.033z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="27.211"
          android:startY="37.038"
          android:endX="29.672"
          android:endY="49"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M4,17.261C8.389,18.541 12.646,19.389 16.772,19.807L22.045,28C17.834,28.006 13.551,27.558 9.194,26.658L9.194,26.658ZM35.164,18.603C44.687,16.217 53.632,15.471 62,16.366L62,16.366L57.239,23.973C50.025,23.973 42.378,24.868 34.299,26.658L34.335,26.649L29.406,19.688C31.14,19.465 32.847,19.156 34.528,18.758Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33"
          android:startY="16"
          android:endX="33"
          android:endY="28"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

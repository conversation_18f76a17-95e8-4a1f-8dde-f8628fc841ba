<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.5,32.5m-26.5,0a26.5,26.5 0,1 1,53 0a26.5,26.5 0,1 1,-53 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.669"
          android:startY="10.231"
          android:endX="46.905"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M28.125,47C27.606,47 27.12,46.796 26.755,46.431L16.569,36.245C16.204,35.88 16,35.394 16,34.875C16,34.356 16.204,33.87 16.569,33.505L21.074,29L34,41.926L29.495,46.431C29.13,46.801 28.644,47 28.125,47L28.125,47Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="25"
          android:startY="29"
          android:endX="25"
          android:endY="47"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M35.036,13.562C35.786,12.813 37.01,12.813 37.759,13.562L49.434,25.237C49.798,25.6 50,26.083 50,26.598C50,27.113 49.798,27.596 49.434,27.96L36.398,41L36.205,40.807L22.193,26.796L22,26.603L35.036,13.562Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="36"
          android:startY="13"
          android:endX="36"
          android:endY="41"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M18.086,50L36.914,50C37.514,50 38,49.552 38,49C38,48.448 37.514,48 36.914,48L18.086,48C17.486,48 17,48.448 17,49C17,49.552 17.486,50 18.086,50Z"
      android:strokeAlpha="0.55"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:fillAlpha="0.55">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="26.384"
          android:startY="48.958"
          android:endX="27.5"
          android:endY="49.009"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

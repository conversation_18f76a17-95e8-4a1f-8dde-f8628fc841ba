<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M12,13.913L12,52.087C12,55.353 14.647,58 17.913,58L47.087,58C50.353,58 53,55.353 53,52.087L53,23.441L53,23.441L39.821,8L17.913,8C14.647,8 12,10.647 12,13.913Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="24.265"
          android:startY="11.991"
          android:endX="39.993"
          android:endY="58"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M40.601,21.273C40.185,20.774 39.968,20.086 40.004,19.381L40.004,10L50,21.997L42.177,21.997C41.59,22.033 41.016,21.772 40.601,21.273Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="45"
          android:startY="10"
          android:endX="45"
          android:endY="22"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M46.844,38.396C47.337,37.868 48.137,37.868 48.63,38.396C49.123,38.924 49.123,39.78 48.63,40.308L48.63,40.308L37.36,51.428L45.8,51.429C46.421,51.429 46.932,51.934 46.994,52.583L47,52.714C47,53.424 46.463,54 45.8,54L45.8,54L34.2,54C33.537,54 33,53.424 33,52.714L33,52.714L33,40.286C33,39.576 33.537,39 34.2,39C34.863,39 35.4,39.576 35.4,40.286L35.4,40.286L35.4,49.687Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="41"
          android:startY="38"
          android:endX="41"
          android:endY="54"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M44.865,28C45.492,28 46,28.448 46,29C46,29.552 45.492,30 44.865,30L44.865,30L19.135,30C18.508,30 18,29.552 18,29C18,28.448 18.508,28 19.135,28L19.135,28ZM19.159,21L33.841,21C34.481,21 35,21.448 35,22C35,22.515 34.548,22.94 33.967,22.994L33.841,23L19.159,23C18.519,23 18,22.552 18,22C18,21.485 18.452,21.06 19.033,21.006L19.159,21L33.841,21ZM19.159,15L33.841,15C34.481,15 35,15.448 35,16C35,16.515 34.548,16.94 33.967,16.994L33.841,17L19.159,17C18.519,17 18,16.552 18,16C18,15.485 18.452,15.06 19.033,15.006L19.159,15L33.841,15Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32"
          android:startY="15"
          android:endX="32"
          android:endY="30"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

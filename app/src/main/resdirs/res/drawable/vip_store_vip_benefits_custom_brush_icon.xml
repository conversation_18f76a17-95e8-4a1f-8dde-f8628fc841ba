<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M30.5,32.5m-26.5,0a26.5,26.5 0,1 1,53 0a26.5,26.5 0,1 1,-53 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="14.669"
          android:startY="10.231"
          android:endX="44.905"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M18.834,15.858C26.313,19.042 29.195,22.204 28.205,25.24C27.605,27.078 25.329,29.012 20.793,31.831C20.297,32.14 20.173,32.216 18.928,32.977C12.875,36.679 11.055,38.144 11.016,40.636C10.957,44.412 14.777,46.821 21.974,48.247C22.776,48.406 23.566,47.889 23.738,47.093C23.91,46.297 23.4,45.523 22.598,45.364C16.66,44.188 13.957,42.484 13.986,40.639C13.999,39.769 15.781,38.334 20.463,35.471C21.716,34.705 21.842,34.628 22.347,34.314C27.528,31.093 30.122,28.889 31.03,26.107C32.649,21.144 28.745,16.861 20.041,13.155C19.292,12.836 18.414,13.183 18.081,13.929C17.747,14.675 18.085,15.539 18.834,15.858Z"
      android:strokeAlpha="0.55"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:fillAlpha="0.55">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="21.202"
          android:startY="13.036"
          android:endX="21.202"
          android:endY="48.276"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M50.5,11.5l-2,2l9,8l2,-2z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="50.714"
          android:startY="12.545"
          android:endX="56.99"
          android:endY="21.206"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M60.268,9.904C61.495,11.013 62.221,12.529 62.308,14.183C62.394,15.837 61.831,17.42 60.726,18.652L59.374,20.153L50.17,11.864L51.522,10.362C53.808,7.824 57.733,7.618 60.268,9.904L60.268,9.904Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="56.243"
          android:startY="8.315"
          android:endX="56.243"
          android:endY="20.153"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M48.489,13.444l8.924,8.036l-13.734,15.248l-8.92,-8.036z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="46.086"
          android:startY="13.444"
          android:endX="46.086"
          android:endY="36.728"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M42.434,41.981C42.286,42.148 42.032,42.161 41.867,42.011L38.707,39.135L38.702,39.13L33.09,34.019L30.27,31.597C30.193,31.526 30.141,31.426 30.135,31.314C30.13,31.207 30.166,31.102 30.24,31.019L32.275,28.735C32.344,28.657 32.443,28.605 32.554,28.599C32.66,28.593 32.764,28.63 32.846,28.705L44.443,39.119C44.521,39.19 44.573,39.29 44.578,39.402C44.584,39.509 44.548,39.614 44.474,39.697L42.434,41.981Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="37.357"
          android:startY="28.598"
          android:endX="37.357"
          android:endY="42.115"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M31.606,34.282L37.26,39.388L34.321,48.89C34.265,49.118 34.075,49.285 33.842,49.318L22.904,50.76L27.067,46.122C27.348,45.809 27.323,45.329 27.011,45.052C26.699,44.771 26.221,44.796 25.945,45.108L21.781,49.746L22.067,38.68C22.076,38.444 22.221,38.237 22.441,38.158L31.606,34.282ZM31.61,39.315L30.326,40.74C30.02,41.073 30.048,41.593 30.381,41.898C30.547,42.048 30.76,42.121 30.975,42.11C31.184,42.099 31.389,42.009 31.544,41.837L32.828,40.411C33.133,40.072 33.106,39.553 32.767,39.254C32.428,38.949 31.909,38.976 31.61,39.315Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.52"
          android:startY="34.282"
          android:endX="29.52"
          android:endY="50.76"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M51.087,4C54.353,4 57,6.647 57,9.913L57,54.087C57,57.353 54.353,60 51.087,60L15.913,60C12.647,60 10,57.353 10,54.087L10,46L13.043,46C14.618,46 15.905,44.769 15.995,43.217L16,43.043L16,42.957C16,41.324 14.676,40 13.043,40L13.043,40L10,40L10,35L13.043,35C14.618,35 15.905,33.769 15.995,32.217L16,32.043L16,31.957C16,30.324 14.676,29 13.043,29L13.043,29L10,29L10,23L13.043,23C14.618,23 15.905,21.769 15.995,20.217L16,20.043L16,19.957C16,18.324 14.676,17 13.043,17L13.043,17L10,17L10,9.913C10,6.647 12.647,4 15.913,4L51.087,4Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="23.611"
          android:startY="8.47"
          android:endX="42.498"
          android:endY="60"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M8.5,19L12.5,19A1.5,1.5 0,0 1,14 20.5L14,20.5A1.5,1.5 0,0 1,12.5 22L8.5,22A1.5,1.5 0,0 1,7 20.5L7,20.5A1.5,1.5 0,0 1,8.5 19z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M8.5,31L12.5,31A1.5,1.5 0,0 1,14 32.5L14,32.5A1.5,1.5 0,0 1,12.5 34L8.5,34A1.5,1.5 0,0 1,7 32.5L7,32.5A1.5,1.5 0,0 1,8.5 31z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M8.5,42L12.5,42A1.5,1.5 0,0 1,14 43.5L14,43.5A1.5,1.5 0,0 1,12.5 45L8.5,45A1.5,1.5 0,0 1,7 43.5L7,43.5A1.5,1.5 0,0 1,8.5 42z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M34.5,19C36.569,19 38.442,20.564 39.799,23.093C42.956,22.739 45.543,23.477 46.72,25.36C48.021,27.442 47.308,30.458 45.147,33.421C47.073,36.513 47.56,39.55 46.125,41.526C44.885,43.232 42.45,43.819 39.532,43.383C38.201,45.631 36.436,47 34.5,47C32.332,47 30.379,45.283 29.009,42.537C26.258,42.668 24.039,41.902 22.974,40.198C21.754,38.245 22.305,35.472 24.162,32.689C22.479,29.767 22.111,26.941 23.472,25.068C24.586,23.535 26.665,22.905 29.192,23.109C30.55,20.571 32.427,19 34.5,19ZM33.916,41.519L33.649,41.609C32.534,41.984 31.446,42.248 30.412,42.399C31.442,44.035 32.706,45 34,45C35.165,45 36.305,44.218 37.272,42.869C36.181,42.54 35.052,42.089 33.916,41.519ZM44.261,34.54L44.201,34.608C43.47,35.453 42.624,36.283 41.677,37.074C41.382,38.893 40.892,40.556 40.255,41.979C42.483,42.259 44.259,41.826 45.102,40.666C46.095,39.299 45.727,37.008 44.261,34.54ZM39.098,38.965L38.821,39.14C37.85,39.747 36.867,40.274 35.889,40.721C36.639,41.047 37.376,41.313 38.087,41.52C38.472,40.765 38.814,39.907 39.098,38.965ZM25.082,34.117L25.018,34.226C23.865,36.235 23.574,38.095 24.349,39.336C25.022,40.412 26.464,40.972 28.343,41C27.86,39.704 27.491,38.251 27.263,36.688C26.434,35.85 25.703,34.985 25.082,34.117ZM28.631,37.968L28.66,38.084C28.921,39.131 29.252,40.091 29.636,40.942C30.401,40.865 31.219,40.714 32.069,40.488C31.506,40.142 30.945,39.767 30.39,39.364C29.772,38.915 29.185,38.448 28.631,37.968ZM34.552,26.124L34.274,26.254C33.443,26.646 32.604,27.103 31.77,27.624C30.378,28.494 29.141,29.455 28.084,30.451C28.029,31.117 28,31.801 28,32.5C28,33.459 28.054,34.389 28.156,35.281C29.103,36.284 30.211,37.264 31.462,38.173C32.329,38.803 33.206,39.356 34.078,39.834C35.404,39.325 36.774,38.648 38.129,37.801C38.647,37.477 39.144,37.14 39.618,36.794C39.864,35.461 40,34.016 40,32.5C40,31.454 39.935,30.441 39.814,29.475C39.401,29.131 38.968,28.793 38.516,28.465C37.201,27.509 35.859,26.729 34.552,26.124ZM41.958,31.502L41.961,31.569C41.987,32.039 42,32.517 42,33C42,33.631 41.978,34.253 41.934,34.862C42.48,34.338 42.977,33.806 43.422,33.273C42.992,32.682 42.502,32.089 41.958,31.502ZM27.042,31.505L26.842,31.728C26.566,32.039 26.308,32.351 26.069,32.663C26.353,33.097 26.67,33.534 27.018,33.972C27.006,33.651 27,33.327 27,33C27,32.495 27.014,31.996 27.042,31.505ZM45.55,26.088C44.751,24.809 42.861,24.26 40.446,24.466C41.035,25.893 41.479,27.538 41.739,29.324C42.708,30.238 43.559,31.191 44.277,32.151C45.917,29.787 46.447,27.523 45.55,26.088ZM28.49,24.624L28.508,24.577C26.855,24.57 25.559,25.032 24.876,25.972C23.999,27.179 24.184,29.105 25.247,31.239C25.848,30.517 26.533,29.804 27.293,29.115C27.546,27.474 27.956,25.957 28.49,24.624L28.508,24.577ZM29.397,24.623L29.36,24.712C28.953,25.744 28.62,26.912 28.386,28.182C29.155,27.564 29.986,26.973 30.873,26.419C31.479,26.04 32.089,25.693 32.7,25.376C31.528,24.971 30.411,24.72 29.397,24.623ZM38.651,24.739L38.488,24.775C37.788,24.932 37.062,25.143 36.319,25.407C37.287,25.93 38.256,26.538 39.207,27.229C39.291,27.29 39.375,27.352 39.458,27.414C39.243,26.45 38.97,25.553 38.651,24.739ZM34,20C32.553,20 31.145,21.206 30.057,23.208C31.456,23.416 32.967,23.86 34.51,24.528C35.714,24.025 36.901,23.644 38.04,23.39C36.939,21.279 35.49,20 34,20Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="34.847"
          android:startY="19"
          android:endX="34.847"
          android:endY="47"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

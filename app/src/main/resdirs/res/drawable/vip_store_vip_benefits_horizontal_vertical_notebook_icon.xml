<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M62.348,32.913L62.348,53.609A5.913,5.913 0,0 1,56.435 59.522L23.913,59.522A5.913,5.913 135,0 1,18 53.609L18,32.913A5.913,5.913 135,0 1,23.913 27L56.435,27A5.913,5.913 0,0 1,62.348 32.913z"
      android:strokeAlpha="0.6"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.6">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.675"
          android:startY="45.408"
          android:endX="54.071"
          android:endY="33.806"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M48,45.114C48,45.603 47.552,46 47,46C46.448,46 46,45.603 46,45.114L46,45.114L46,33.886C46,33.397 46.448,33 47,33C47.552,33 48,33.397 48,33.886L48,33.886ZM56,45.114C56,45.603 55.552,46 55,46C54.448,46 54,45.603 54,45.114L54,45.114L54,33.886C54,33.397 54.448,33 55,33C55.552,33 56,33.397 56,33.886L56,33.886Z"
      android:strokeAlpha="0.5822405"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:fillAlpha="0.5822405">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="51"
          android:startY="33"
          android:endX="51"
          android:endY="46"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M9.913,9L35.087,9A5.913,5.913 0,0 1,41 14.913L41,53.087A5.913,5.913 0,0 1,35.087 59L9.913,59A5.913,5.913 0,0 1,4 53.087L4,14.913A5.913,5.913 0,0 1,9.913 9z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.448"
          android:startY="12.991"
          android:endX="28.007"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M31.108,31C31.601,31 32,31.448 32,32C32,32.552 31.601,33 31.108,33L31.108,33L10.892,33C10.399,33 10,32.552 10,32C10,31.448 10.399,31 10.892,31L10.892,31ZM10.886,24L22.114,24C22.603,24 23,24.448 23,25C23,25.513 22.658,25.936 22.217,25.993L22.114,26L10.886,26C10.397,26 10,25.552 10,25C10,24.487 10.342,24.064 10.783,24.007L10.886,24L22.114,24ZM10.886,18L22.114,18C22.603,18 23,18.448 23,19C23,19.513 22.658,19.936 22.217,19.993L22.114,20L10.886,20C10.397,20 10,19.552 10,19C10,18.487 10.342,18.064 10.783,18.007L10.886,18L22.114,18Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="21"
          android:startY="18"
          android:endX="21"
          android:endY="33"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M51.851,7L51.852,10.041C58.068,10.472 61,14.285 61,20.364C61,21.267 60.267,22 59.364,22C58.46,22 57.727,21.267 57.727,20.364C57.727,15.999 56.066,13.679 51.852,13.321L51.851,18.703L46,12.851L51.851,7Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="49.019"
          android:startY="8.197"
          android:endX="57.577"
          android:endY="22"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

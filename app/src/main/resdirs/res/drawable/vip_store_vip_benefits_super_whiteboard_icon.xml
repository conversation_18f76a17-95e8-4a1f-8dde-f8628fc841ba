<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M18.714,10L45.286,10A6.714,6.714 0,0 1,52 16.714L52,35.286A6.714,6.714 0,0 1,45.286 42L18.714,42A6.714,6.714 0,0 1,12 35.286L12,16.714A6.714,6.714 0,0 1,18.714 10z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20.052"
          android:startY="17.395"
          android:endX="42.872"
          android:endY="36.24"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17,55L27,43"
      android:strokeWidth="2.95656"
      android:fillColor="#00000000"
      android:strokeColor="#313243"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M47,55L37,43"
      android:strokeWidth="2.95656"
      android:fillColor="#00000000"
      android:strokeColor="#313243"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M32,43L32,53"
      android:strokeWidth="2.95656"
      android:fillColor="#00000000"
      android:strokeColor="#313243"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M10,41L54,41"
      android:strokeWidth="2.95656"
      android:fillColor="#00000000"
      android:strokeColor="#313243"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M23.535,25.931C28.115,21.576 31.075,20.312 32.359,21.377C32.913,21.837 32.781,23.221 31.769,26.56L31.724,26.71C31.004,29.082 30.755,30.057 30.666,31.239C30.524,33.135 31.09,34.62 32.626,35.417C36.046,37.191 40.021,34.871 44.67,29.084C45.189,28.439 45.084,27.498 44.438,26.981C43.791,26.464 42.847,26.568 42.329,27.213C38.494,31.988 35.681,33.629 34.01,32.762C33.401,32.446 33.495,31.2 34.595,27.577L34.641,27.426C36.073,22.7 36.261,20.723 34.277,19.077C31.319,16.622 27.067,18.438 21.465,23.765C20.865,24.335 20.843,25.282 21.414,25.88C21.986,26.479 22.935,26.501 23.535,25.931Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33"
          android:startY="18"
          android:endX="33"
          android:endY="36"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M49.455,12L39.545,12C37.045,12 35,14.045 35,16.545L35,26.455C35,28.955 37.045,31 39.545,31L49.455,31C51.955,31 54,28.955 54,26.455L54,16.545C54,14.045 51.955,12 49.455,12Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="44.5"
          android:startY="12"
          android:endX="44.5"
          android:endY="31"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M25.455,12L15.545,12C13.045,12 11,14.045 11,16.545L11,26.455C11,28.955 13.045,31 15.545,31L25.455,31C27.955,31 30,28.955 30,26.455L30,16.545C30,14.045 27.955,12 25.455,12Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="14.825"
          android:startY="13.517"
          android:endX="25.664"
          android:endY="31"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M25.455,35L15.545,35C13.045,35 11,37.045 11,39.545L11,49.455C11,51.955 13.045,54 15.545,54L25.455,54C27.955,54 30,51.955 30,49.455L30,39.545C30,37.045 27.955,35 25.455,35L25.455,35Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20.5"
          android:startY="35"
          android:endX="20.5"
          android:endY="54"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M39,35C37.9,35 37,35.818 37,36.818L37,52.182C37,53.182 37.9,54 39,54C40.1,54 41,53.182 41,52.182L41,36.818C41,35.818 40.1,35 39,35L39,35Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="38.947"
          android:startY="36.517"
          android:endX="39.048"
          android:endY="54"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M50,35C48.9,35 48,35.818 48,36.818L48,52.182C48,53.182 48.9,54 50,54C51.1,54 52,53.182 52,52.182L52,36.818C52,35.818 51.1,35 50,35L50,35Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="49.947"
          android:startY="36.517"
          android:endX="50.048"
          android:endY="54"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M50.087,4C53.353,4 56,6.647 56,9.913L56,54.087C56,57.353 53.353,60 50.087,60L14.913,60C11.647,60 9,57.353 9,54.087L9,46L12.043,46C13.618,46 14.905,44.769 14.995,43.217L15,43.043L15,42.957C15,41.324 13.676,40 12.043,40L12.043,40L9,40L9,35L12.043,35C13.618,35 14.905,33.769 14.995,32.217L15,32.043L15,31.957C15,30.324 13.676,29 12.043,29L12.043,29L9,29L9,23L12.043,23C13.618,23 14.905,21.769 14.995,20.217L15,20.043L15,19.957C15,18.324 13.676,17 12.043,17L12.043,17L9,17L9,9.913C9,6.647 11.647,4 14.913,4L50.087,4Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.611"
          android:startY="8.47"
          android:endX="41.498"
          android:endY="60"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.5,19L11.5,19A1.5,1.5 0,0 1,13 20.5L13,20.5A1.5,1.5 0,0 1,11.5 22L7.5,22A1.5,1.5 0,0 1,6 20.5L6,20.5A1.5,1.5 0,0 1,7.5 19z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M7.5,30L11.5,30A1.5,1.5 0,0 1,13 31.5L13,31.5A1.5,1.5 0,0 1,11.5 33L7.5,33A1.5,1.5 0,0 1,6 31.5L6,31.5A1.5,1.5 0,0 1,7.5 30z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M7.5,42L11.5,42A1.5,1.5 0,0 1,13 43.5L13,43.5A1.5,1.5 0,0 1,11.5 45L7.5,45A1.5,1.5 0,0 1,6 43.5L6,43.5A1.5,1.5 0,0 1,7.5 42z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M31.39,29.288C27.181,26.075 23.225,26.225 20.248,29.871C17.965,32.666 18.97,36.1 21.996,38.105C24.925,40.046 28.798,40.061 31.477,37.675C36.335,33.349 38.951,31.201 40.489,30.297C42.086,29.358 45.498,30.358 45.945,32.747C46.578,36.128 41.619,38.861 38.496,35.616C37.931,35.03 36.983,34.998 36.377,35.544C35.772,36.091 35.739,37.01 36.303,37.596C41.546,43.043 50.042,38.359 48.895,32.229C48.033,27.623 42.177,25.909 38.934,27.814C37.157,28.859 34.471,31.064 29.448,35.538C27.872,36.941 25.535,36.932 23.689,35.708C21.975,34.573 21.515,33 22.599,31.672C24.48,29.369 26.55,29.29 29.535,31.569C30.185,32.065 31.127,31.957 31.64,31.327C32.152,30.697 32.04,29.784 31.39,29.288Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="34"
          android:startY="27"
          android:endX="34"
          android:endY="40"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

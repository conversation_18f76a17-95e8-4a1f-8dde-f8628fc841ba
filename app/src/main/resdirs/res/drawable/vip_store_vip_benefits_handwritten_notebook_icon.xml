<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M20.87,7L47.13,7A8.87,8.87 0,0 1,56 15.87L56,48.13A8.87,8.87 0,0 1,47.13 57L20.87,57A8.87,8.87 0,0 1,12 48.13L12,15.87A8.87,8.87 0,0 1,20.87 7z"
      android:strokeAlpha="0.60628253"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.60628253">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="23.822"
          android:startY="10.991"
          android:endX="43.261"
          android:endY="57"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.913,7L44.087,7A5.913,5.913 0,0 1,50 12.913L50,51.087A5.913,5.913 0,0 1,44.087 57L17.913,57A5.913,5.913 0,0 1,12 51.087L12,12.913A5.913,5.913 0,0 1,17.913 7z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="24.444"
          android:startY="10.991"
          android:endX="36.965"
          android:endY="57"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M39.489,34.57C38.228,36.16 35.96,38.082 32.133,40.8C31.758,41.067 31.242,41.067 30.867,40.8C27.04,38.081 24.772,36.16 23.511,34.57C22.25,32.979 22,31.763 22,30.308C22,27.381 24.513,25 27.603,25C29.058,24.999 30.456,25.535 31.5,26.495C32.544,25.535 33.942,24.999 35.397,25C38.487,25 41,27.381 41,30.308C41,31.763 40.747,32.983 39.489,34.57L39.489,34.57Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="31.5"
          android:startY="25"
          android:endX="31.5"
          android:endY="41"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.702,19.632C24.558,20.142 24.219,20.812 23.603,21.805C23.542,21.902 23.428,21.939 23.322,21.896C22.239,21.455 21.571,21.112 21.155,20.784C20.739,20.456 20.579,20.153 20.455,19.77C20.204,18.998 20.558,18.189 21.244,17.966C21.567,17.861 21.923,17.902 22.237,18.079C22.386,17.751 22.651,17.509 22.974,17.404C23.66,17.181 24.422,17.628 24.672,18.399C24.797,18.783 24.845,19.123 24.702,19.632L24.702,19.632Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.582"
          android:startY="17.347"
          android:endX="22.582"
          android:endY="21.914"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M46.937,47.318C46.353,47.716 45.432,48.11 43.952,48.595C43.806,48.643 43.658,48.583 43.586,48.448C42.858,47.07 42.47,46.147 42.326,45.455C42.182,44.763 42.279,44.317 42.48,43.819C42.885,42.816 43.94,42.293 44.832,42.653C45.252,42.822 45.581,43.169 45.749,43.62C46.184,43.413 46.661,43.392 47.081,43.562C47.972,43.922 48.368,45.031 47.963,46.034C47.761,46.532 47.519,46.921 46.937,47.318L46.937,47.318Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="45.19"
          android:startY="42.539"
          android:endX="45.19"
          android:endY="48.611"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

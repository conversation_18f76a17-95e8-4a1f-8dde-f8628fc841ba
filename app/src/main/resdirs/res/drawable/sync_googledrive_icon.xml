<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="130dp"
    android:height="130dp"
    android:viewportWidth="130"
    android:viewportHeight="130">
  <path
      android:pathData="M30,0L100,0A30,30 0,0 1,130 30L130,100A30,30 0,0 1,100 130L30,130A30,30 0,0 1,0 100L0,30A30,30 0,0 1,30 0z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M49,24l33.26,57l29.74,0l-32.76,-57z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="65.57"
          android:startY="29.17"
          android:endX="98.41"
          android:endY="75.83"
          android:type="linear">
        <item android:offset="0" android:color="#FFEFCD62"/>
        <item android:offset="0.45" android:color="#FFFCD04B"/>
        <item android:offset="1" android:color="#FFDBA92C"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M32,105.69l32.25,-56.15l-14.75,-25.55l-32.25,56.15z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.61"
          android:startY="31.51"
          android:endX="54.11"
          android:endY="98.17"
          android:type="linear">
        <item android:offset="0" android:color="#FF2CB671"/>
        <item android:offset="0.45" android:color="#FF2DB573"/>
        <item android:offset="1" android:color="#FF188D40"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M111.75,80.95l-66,0.19l-13.75,24.55l66,-0.19z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="52.97"
          android:startY="82.47"
          android:endX="94.54"
          android:endY="104.17"
          android:type="linear">
        <item android:offset="0" android:color="#FF527BBB"/>
        <item android:offset="0.45" android:color="#FF527ABF"/>
        <item android:offset="1" android:color="#FF3755A9"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

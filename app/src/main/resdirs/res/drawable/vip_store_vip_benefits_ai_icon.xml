<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.5,32.5m-26.5,0a26.5,26.5 0,1 1,53 0a26.5,26.5 0,1 1,-53 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.669"
          android:startY="10.231"
          android:endX="46.905"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M18.434,46.566C13.673,41.805 16.111,31.647 23.879,23.879C31.647,16.111 41.805,13.673 46.566,18.434C51.327,23.195 48.889,33.353 41.121,41.121C33.353,48.889 23.195,51.327 18.434,46.566Z"
      android:strokeWidth="1.47828"
      android:fillColor="#00000000"
      android:fillType="evenOdd">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="32.5"
          android:startY="16"
          android:endX="32.5"
          android:endY="49"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M46.566,46.566C51.327,41.805 48.889,31.647 41.121,23.879C33.353,16.111 23.195,13.673 18.434,18.434C13.673,23.195 16.111,33.353 23.879,41.121C31.647,48.889 41.805,51.327 46.566,46.566Z"
      android:strokeWidth="1.47828"
      android:fillColor="#00000000"
      android:fillType="evenOdd">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="32.5"
          android:startY="16"
          android:endX="32.5"
          android:endY="49"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M29.286,26L30.714,26L35,38L33.571,38L32.143,35L27.857,35L26.429,38L25,38L29.286,26ZM28,34L32,34L30.667,28L29.333,28L28,34ZM37,26L38,26L38,38L37,38L37,26Z"
      android:strokeWidth="1.330452"
      android:fillType="nonZero">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="31.5"
          android:startY="26"
          android:endX="31.5"
          android:endY="38"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="31.5"
          android:startY="26"
          android:endX="31.5"
          android:endY="38"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

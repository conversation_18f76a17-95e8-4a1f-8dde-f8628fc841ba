<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M50.087,4C53.353,4 56,6.647 56,9.913L56,54.087C56,57.353 53.353,60 50.087,60L14.913,60C11.647,60 9,57.353 9,54.087L9,45.999L12.043,46C13.618,46 14.905,44.769 14.995,43.217L15,43.043L15,42.957C15,41.324 13.676,40 12.043,40L12.043,40L9,39.999L9,34.999L12.043,35C13.618,35 14.905,33.769 14.995,32.217L15,32.043L15,31.957C15,30.324 13.676,29 12.043,29L12.043,29L9,28.999L9,23L12.043,23C13.618,23 14.905,21.769 14.995,20.217L15,20.043L15,19.957C15,18.324 13.676,17 12.043,17L12.043,17L9,17L9,9.913C9,6.647 11.647,4 14.913,4L50.087,4Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.611"
          android:startY="8.47"
          android:endX="41.498"
          android:endY="60"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.5,19L11.5,19A1.5,1.5 0,0 1,13 20.5L13,20.5A1.5,1.5 0,0 1,11.5 22L7.5,22A1.5,1.5 0,0 1,6 20.5L6,20.5A1.5,1.5 0,0 1,7.5 19z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M7.5,31L11.5,31A1.5,1.5 0,0 1,13 32.5L13,32.5A1.5,1.5 0,0 1,11.5 34L7.5,34A1.5,1.5 0,0 1,6 32.5L6,32.5A1.5,1.5 0,0 1,7.5 31z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M7.5,42L11.5,42A1.5,1.5 0,0 1,13 43.5L13,43.5A1.5,1.5 0,0 1,11.5 45L7.5,45A1.5,1.5 0,0 1,6 43.5L6,43.5A1.5,1.5 0,0 1,7.5 42z"
      android:strokeWidth="1"
      android:fillColor="#292B3B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M38.894,21C40.187,21 41.234,21.21 42.082,21.549L38.689,24.604C37.756,25.444 37.681,26.881 38.521,27.813C39.361,28.746 40.798,28.822 41.73,27.982L41.73,27.982L45.289,24.778C45.792,26.014 45.788,27.022 45.889,26.797C46.318,29.307 45.491,31.867 43.675,33.652C42.211,35.136 40.217,35.975 38.133,35.986C37.564,35.986 36.997,35.921 36.444,35.791L35.893,35.638L26.44,45.094C25.327,46.204 23.559,46.307 22.326,45.333L22.056,45.094L21.906,44.944C20.797,43.831 20.694,42.065 21.664,40.83L21.906,40.561L31.36,31.105C30.526,28.508 31.199,25.472 33.343,23.328C34.811,21.846 36.808,21.008 38.894,21Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33.5"
          android:startY="21"
          android:endX="33.5"
          android:endY="46"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

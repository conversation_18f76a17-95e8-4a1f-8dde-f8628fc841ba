<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M17.913,7L47.087,7A5.913,5.913 0,0 1,53 12.913L53,51.087A5.913,5.913 0,0 1,47.087 57L17.913,57A5.913,5.913 0,0 1,12 51.087L12,12.913A5.913,5.913 0,0 1,17.913 7z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="24.265"
          android:startY="10.991"
          android:endX="39.993"
          android:endY="57"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M48.045,45C48.573,45 49,45.448 49,46C49,46.552 48.573,47 48.045,47L48.045,47L28.955,47C28.427,47 28,46.552 28,46C28,45.448 28.427,45 28.955,45L28.955,45ZM28.955,33L48.045,33C48.573,33 49,33.448 49,34C49,34.513 48.632,34.936 48.157,34.993L48.045,35L28.955,35C28.427,35 28,34.552 28,34C28,33.487 28.368,33.064 28.843,33.007L28.955,33L48.045,33ZM28.955,19L48.045,19C48.573,19 49,19.448 49,20C49,20.513 48.632,20.936 48.157,20.993L48.045,21L28.955,21C28.427,21 28,20.552 28,20C28,19.487 28.368,19.064 28.843,19.007L28.955,19L48.045,19Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="38.5"
          android:startY="19"
          android:endX="38.5"
          android:endY="47"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M18.561,18.51C17.975,17.963 17.025,17.963 16.439,18.51C15.854,19.057 15.854,19.943 16.439,20.49L18.689,22.59C19.313,23.172 20.338,23.129 20.902,22.496L24.652,18.296C25.183,17.702 25.097,16.82 24.46,16.325C23.824,15.83 22.878,15.91 22.348,16.504L19.649,19.526L18.561,18.51Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20.5"
          android:startY="16"
          android:endX="20.5"
          android:endY="23"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M18.561,32.51C17.975,31.963 17.025,31.963 16.439,32.51C15.854,33.057 15.854,33.943 16.439,34.49L18.689,36.59C19.313,37.172 20.338,37.129 20.902,36.496L24.652,32.296C25.183,31.702 25.097,30.82 24.46,30.325C23.824,29.83 22.878,29.91 22.348,30.504L19.649,33.526L18.561,32.51Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20.5"
          android:startY="30"
          android:endX="20.5"
          android:endY="37"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

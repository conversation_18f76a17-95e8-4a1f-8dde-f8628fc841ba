<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.5,32.5m-26.5,0a26.5,26.5 0,1 1,53 0a26.5,26.5 0,1 1,-53 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.669"
          android:startY="10.231"
          android:endX="46.905"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M33.074,47C34.105,47 35.123,46.923 36.118,46.773C36.702,46.684 37.089,46.219 36.982,45.734C36.876,45.248 36.317,44.926 35.734,45.015C34.865,45.146 33.976,45.213 33.074,45.213C32.481,45.213 32,45.613 32,46.106C32,46.6 32.481,47 33.074,47ZM40.703,44.875C41.717,44.369 42.671,43.77 43.549,43.087C44.06,42.69 44.15,41.956 43.75,41.448C43.35,40.94 42.611,40.851 42.1,41.248C41.345,41.835 40.523,42.352 39.648,42.789C39.068,43.079 38.834,43.78 39.125,44.357C39.417,44.933 40.123,45.165 40.703,44.875ZM47.292,40.487C47.965,39.556 48.513,38.566 48.924,37.532C49.16,36.939 48.826,36.285 48.177,36.069C47.529,35.854 46.812,36.159 46.576,36.751C46.232,37.616 45.772,38.447 45.207,39.23C44.827,39.756 44.986,40.464 45.561,40.811C46.137,41.158 46.912,41.013 47.292,40.487ZM49,31.902C48.976,30.846 48.8,29.806 48.48,28.796C48.297,28.221 47.604,27.882 46.932,28.038C46.259,28.194 45.862,28.785 46.044,29.36C46.311,30.2 46.457,31.065 46.477,31.944C46.491,32.539 47.066,33.011 47.763,33C48.46,32.988 49.013,32.496 49,31.902ZM45.803,24.416C45.217,23.637 44.535,22.914 43.771,22.26C43.338,21.888 42.668,21.919 42.274,22.329C41.881,22.739 41.914,23.373 42.348,23.745C43,24.304 43.58,24.918 44.077,25.579C44.416,26.03 45.078,26.135 45.555,25.814C46.032,25.493 46.143,24.867 45.803,24.416ZM40.36,19.023C39.41,18.608 38.412,18.276 37.38,18.032C36.786,17.892 36.184,18.228 36.034,18.783C35.884,19.338 36.243,19.902 36.837,20.042C37.734,20.255 38.6,20.543 39.424,20.903C39.978,21.145 40.637,20.92 40.896,20.401C41.155,19.882 40.915,19.265 40.36,19.023ZM31.894,18.001C30.862,18.036 29.844,18.183 28.852,18.437C28.271,18.585 27.9,19.274 28.024,19.973C28.147,20.673 28.719,21.12 29.3,20.971C30.165,20.75 31.053,20.622 31.955,20.591C32.549,20.571 33.016,19.974 33,19.259C32.983,18.544 32.488,17.98 31.894,18.001ZM25.25,20.137C24.24,20.662 23.294,21.277 22.428,21.973C21.924,22.378 21.855,23.1 22.274,23.587C22.694,24.073 23.442,24.14 23.946,23.735C24.69,23.138 25.504,22.608 26.375,22.155C26.953,21.855 27.169,21.16 26.858,20.603C26.547,20.046 25.827,19.837 25.25,20.137ZM19.221,25.539C18.729,26.481 18.335,27.479 18.048,28.518C17.883,29.112 18.159,29.75 18.663,29.944C19.168,30.138 19.71,29.813 19.874,29.219C20.115,28.351 20.444,27.515 20.857,26.724C21.135,26.192 20.994,25.495 20.542,25.168C20.091,24.84 19.499,25.007 19.221,25.539ZM17.002,33.145C17.072,34.201 17.285,35.238 17.637,36.242C17.837,36.812 18.515,37.129 19.153,36.95C19.79,36.771 20.144,36.164 19.944,35.594C19.651,34.759 19.474,33.897 19.416,33.018C19.377,32.422 18.805,31.967 18.138,32.002C17.471,32.037 16.963,32.549 17.002,33.145ZM20.215,40.639C20.82,41.409 21.514,42.119 22.286,42.759C22.725,43.122 23.382,43.069 23.754,42.64C24.125,42.21 24.07,41.568 23.632,41.204C22.972,40.658 22.38,40.052 21.867,39.398C21.516,38.952 20.863,38.868 20.407,39.211C19.951,39.553 19.865,40.193 20.215,40.639ZM25.668,45.045C26.625,45.442 27.627,45.754 28.661,45.975C29.255,46.102 29.843,45.739 29.974,45.164C30.105,44.59 29.73,44.021 29.136,43.894C28.237,43.702 27.367,43.431 26.536,43.087C25.977,42.855 25.329,43.105 25.089,43.646C24.85,44.186 25.109,44.813 25.668,45.045Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33"
          android:startY="18"
          android:endX="33"
          android:endY="47"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M41,46C42.657,46 44,44.433 44,42.5C44,40.567 42.657,39 41,39C39.343,39 38,40.567 38,42.5C38,44.433 39.343,46 41,46Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="41"
          android:startY="39"
          android:endX="41"
          android:endY="46"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M38.524,45.24C40.739,42.344 41.973,41.69 42.46,42.267C42.87,42.753 43.227,43.423 43.756,44.632C43.814,44.764 44.028,45.254 44.01,45.213C45.297,48.17 46.131,49.272 48.013,49.041C48.629,48.966 49.067,48.394 48.989,47.765C48.912,47.135 48.349,46.686 47.733,46.762C47.35,46.809 46.909,46.227 46.066,44.29C46.085,44.334 45.87,43.839 45.81,43.703C45.194,42.296 44.761,41.483 44.162,40.774C42.291,38.555 39.741,39.908 36.747,43.822C36.366,44.321 36.454,45.042 36.945,45.434C37.436,45.825 38.143,45.739 38.524,45.24Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="42.754"
          android:startY="39.708"
          android:endX="42.754"
          android:endY="49.071"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M33,33m-27,0a27,27 0,1 1,54 0a27,27 0,1 1,-54 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.87"
          android:startY="10.31"
          android:endX="47.677"
          android:endY="60"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M33,22C26.934,22 22,26.934 22,33C22,39.066 26.934,44 33,44C39.066,44 44,39.066 44,33C44,26.934 39.066,22 33,22ZM33,24C37.962,24 42,28.038 42,33C42,37.962 37.962,42 33,42C28.038,42 24,37.962 24,33C24,28.038 28.038,24 33,24Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33"
          android:startY="22"
          android:endX="33"
          android:endY="44"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M36.062,39L36.053,39C35.77,39 35.592,38.823 35.51,38.737L32.979,37.32L30.393,38.677C30.253,38.751 30.094,38.79 29.934,38.79C29.713,38.79 29.491,38.716 29.308,38.58L29.299,38.574C28.984,38.332 28.83,37.935 28.896,37.538L29.398,34.591L27.31,32.498C27.112,32.298 27.002,32.033 27,31.75C26.998,31.467 27.104,31.2 27.298,30.999C27.455,30.836 27.664,30.727 27.886,30.694L30.781,30.272L32.081,27.592C32.257,27.232 32.621,27 33.008,27C33.399,27 33.764,27.234 33.937,27.597L35.223,30.282L38.115,30.719C38.679,30.804 39.072,31.343 38.989,31.92C38.956,32.15 38.849,32.362 38.687,32.525L36.587,34.607L37.055,37.437C37.095,37.6 37.097,37.793 37.097,37.836C37.097,38.386 36.672,39 36.062,39Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33"
          android:startY="27"
          android:endX="33"
          android:endY="39"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M45,47C43.564,46.313 42.348,46.128 41,46C45.369,43.211 48,38.427 48,33C48,24.823 41.271,18 33,18C24.729,18 18,24.823 18,33C18,38.42 20.666,43.254 25,46C22.619,46.279 21.198,46.541 21,47C20.766,46.622 20.507,47.004 21,47C20.653,47.799 21.029,48.061 21,48C21.519,47.969 29.567,46.489 39,47C38.697,47.21 38.707,47.215 39,47L39,47C40.628,47.366 42.573,47.61 45,48C44.895,48.053 45.267,47.784 45,47C45.396,47 45.151,46.632 45,47ZM28,45C27.624,44.957 27.57,44.92 28,45C22.949,42.755 20,38.107 20,33C20,25.856 25.832,20 33,20C40.168,20 46,25.856 46,33C45.998,38.182 43.008,42.834 38,45C34.373,44.65 30.614,44.772 28,45L28,45Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33"
          android:startY="18"
          android:endX="33"
          android:endY="48.008"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M12.913,7L51.087,7A5.913,5.913 0,0 1,57 12.913L57,51.087A5.913,5.913 0,0 1,51.087 57L12.913,57A5.913,5.913 0,0 1,7 51.087L7,12.913A5.913,5.913 0,0 1,12.913 7z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="17.065"
          android:startY="10.991"
          android:endX="45.589"
          android:endY="57"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M38,47L38,50L33,50L33,47L38,47ZM29,47L29,50L24,50L24,47L29,47ZM18,47L17,47L18,48L18,47ZM19,39L19,44L16,44L16,39L19,39ZM51,31L51,36L48,36L48,31L51,31ZM19,30L19,35L16,35L16,30L19,30ZM51,22L51,27L48,27L48,22L51,22ZM51,16L51,19L48.273,19L48.272,18.571L46,18.571L46,16L51,16ZM48.001,17L48,18L49,18L48.001,17ZM34,16L34,19L29,19L29,16L34,16ZM43,16L43,19L38,19L38,16L43,16ZM18.727,47.428L21,47.429L21,50L16,50L16,47L18.727,47L18.727,47.428Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33.5"
          android:startY="16"
          android:endX="33.5"
          android:endY="50"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.689,15L23.333,15C24.254,15 25,15.746 25,16.667C25,17.587 24.254,18.333 23.333,18.333L18.333,18.333L18.333,18.333L18.333,23.333C18.333,24.254 17.587,25 16.667,25C15.746,25 15,24.254 15,23.333L15,16.689C15,15.756 15.756,15 16.689,15Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20"
          android:startY="15"
          android:endX="20"
          android:endY="25"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M48.311,50L41.667,50C40.746,50 40,49.254 40,48.333C40,47.413 40.746,46.667 41.667,46.667L46.667,46.667L46.667,46.667L46.667,41.667C46.667,40.746 47.413,40 48.333,40C49.254,40 50,40.746 50,41.667L50,48.311C50,49.244 49.244,50 48.311,50Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="45"
          android:startY="40"
          android:endX="45"
          android:endY="50"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M22.278,29L42.722,29C43.428,29 44,28.328 44,27.5C44,26.672 43.428,26 42.722,26L22.278,26C21.572,26 21,26.672 21,27.5C21,28.328 21.572,29 22.278,29Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.5"
          android:startY="26"
          android:endX="32.5"
          android:endY="29"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M22.263,38L35.737,38C36.434,38 37,37.328 37,36.5C37,35.672 36.434,35 35.737,35L22.263,35C21.566,35 21,35.672 21,36.5C21,37.328 21.566,38 22.263,38Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29"
          android:startY="35"
          android:endX="29"
          android:endY="38"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

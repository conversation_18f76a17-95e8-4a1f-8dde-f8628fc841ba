<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.5,32.5m-26.5,0a26.5,26.5 0,1 1,53 0a26.5,26.5 0,1 1,-53 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.669"
          android:startY="10.231"
          android:endX="46.905"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.4,23.8l4.2,0l0,9.8l-1.4,0l0,1.4l5.6,0l0,-1.4l-1.4,0l0,-9.8l4.2,0l0,1.4l1.4,0l0,-4.2l-14,0l0,4.2l1.4,0z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="23"
          android:startY="21"
          android:endX="23"
          android:endY="35"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M46.329,44C46.7,44 47,44.448 47,45C47,45.552 46.7,46 46.329,46L46.329,46L16.671,46C16.3,46 16,45.552 16,45C16,44.448 16.3,44 16.671,44L16.671,44ZM16.671,37L46.329,37C46.7,37 47,37.448 47,38C47,38.51 46.744,38.931 46.413,38.992L46.329,39L16.671,39C16.3,39 16,38.552 16,38C16,37.49 16.256,37.069 16.587,37.008L16.671,37L46.329,37ZM46.318,30C46.695,30 47,30.448 47,31C47,31.51 46.74,31.931 46.404,31.992L46.318,32L32.682,32C32.305,32 32,31.552 32,31C32,30.49 32.26,30.069 32.596,30.008L32.682,30L46.318,30ZM46.318,21C46.695,21 47,21.448 47,22C47,22.51 46.74,22.931 46.404,22.992L46.318,23L32.682,23C32.305,23 32,22.552 32,22C32,21.49 32.26,21.069 32.596,21.008L32.682,21L46.318,21Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="31.5"
          android:startY="21"
          android:endX="31.5"
          android:endY="46"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

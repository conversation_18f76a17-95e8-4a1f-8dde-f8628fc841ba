<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M15.913,10L48.087,10A5.913,5.913 0,0 1,54 15.913L54,48.087A5.913,5.913 0,0 1,48.087 54L15.913,54A5.913,5.913 0,0 1,10 48.087L10,15.913A5.913,5.913 0,0 1,15.913 10z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18.857"
          android:startY="13.512"
          android:endX="43.959"
          android:endY="54"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M49.994,42C50.513,42 50.94,42.408 50.994,42.932L51,43.046L51,44.248C51,47.895 48.225,50.868 44.755,50.996L44.522,51L42.006,51C41.45,51 41,50.532 41,49.954C41,49.414 41.392,48.97 41.896,48.913L42.006,48.907L44.522,48.907C46.918,48.907 48.874,46.939 48.984,44.467L48.989,44.248L48.989,43.046C48.989,42.468 49.439,42 49.994,42ZM14.006,42C14.524,42 14.951,42.408 15.005,42.932L15.011,43.046L15.011,44.248C15.011,46.748 16.899,48.788 19.267,48.902L19.478,48.907L21.994,48.907C22.55,48.907 23,49.376 23,49.954C23,50.493 22.608,50.937 22.104,50.994L21.994,51L19.478,51C15.977,51 13.126,48.106 13.004,44.49L13,44.248L13,43.046C13,42.468 13.45,42 14.006,42ZM44.522,13C48.022,13 50.874,15.775 50.996,19.245L51,19.478L51,21.994C51,22.55 50.55,23 49.994,23C49.476,23 49.049,22.608 48.995,22.104L48.989,21.994L48.989,19.478C48.989,17.081 47.102,15.126 44.733,15.016L44.522,15.011L42.006,15.011C41.45,15.011 41,14.561 41,14.006C41,13.487 41.392,13.06 41.896,13.006L42.006,13L44.522,13ZM21.994,13C22.55,13 23,13.45 23,14.006C23,14.561 22.55,15.011 21.994,15.011L19.478,15.011C17.011,15.011 15.011,17.011 15.011,19.478L15.011,21.994C15.011,22.55 14.561,23 14.006,23C13.45,23 13,22.55 13,21.994L13,19.478C13,15.9 15.9,13 19.478,13L21.994,13Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32"
          android:startY="13"
          android:endX="32"
          android:endY="51"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M32.533,24C36.691,23.996 40.846,27.023 45,33.078C40.795,39.023 36.615,41.996 32.457,42C28.299,42.002 24.15,39.035 20,33.098C24.197,27.035 28.373,24.002 32.533,24ZM33.034,26C30.059,26 26.833,28.109 23.431,32.519L23,33.09C26.382,37.597 29.599,39.827 32.597,39.99L32.956,40C35.937,39.996 39.161,37.937 42.563,33.627L43,33.06L42.984,33.036C39.604,28.451 36.383,26.175 33.393,26.008L33.036,26L33.034,26Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.5"
          android:startY="24"
          android:endX="32.5"
          android:endY="42"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M32.5,29C33.426,29 34.286,29.279 35.001,29.759C34.297,30.438 34.132,31.503 34.598,32.363C35.064,33.223 36.047,33.666 37,33.445L37,33.502C36.999,35.987 34.984,38.001 32.499,38C30.014,37.999 28,35.984 28,33.499C28,31.014 30.015,29 32.5,29L32.5,29ZM32,20.002C32.483,20.002 32.897,20.367 32.984,20.869L33,21.06L33,22.94C33.001,23.488 32.606,23.946 32.09,23.996C31.575,24.045 31.108,23.67 31.016,23.131L31,22.94L31,21.058C31,20.474 31.448,20 32,20L32,20.002ZM44,25C44.483,25 44.897,25.365 44.984,25.868L45,26.058L45,27.94C45.001,28.488 44.606,28.946 44.09,28.996C43.575,29.045 43.108,28.67 43.016,28.13L43,27.94L43,26.058C43,25.474 43.448,25 44,25L44,25ZM21,25C21.483,25 21.897,25.365 21.984,25.868L22,26.058L22,27.94C22.001,28.488 21.606,28.946 21.09,28.996C20.575,29.045 20.108,28.67 20.016,28.13L20,27.94L20,26.058C20,25.474 20.448,25 21,25L21,25Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.5"
          android:startY="20"
          android:endX="32.5"
          android:endY="38"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

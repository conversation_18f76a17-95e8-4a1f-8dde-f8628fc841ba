<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M0,0h24v24h-24z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M1.308,8.667L6.508,3.328C6.712,3.118 6.993,3 7.286,3L17.503,3C17.796,3 18.076,3.118 18.28,3.327L23.491,8.667C23.689,8.87 23.8,9.142 23.8,9.426L23.8,11.798C23.8,12.095 23.679,12.379 23.464,12.583L14.404,21.242C13.894,21.728 13.217,22 12.513,22C11.808,22 11.129,21.732 10.614,21.251L1.344,12.585C1.125,12.379 1,12.092 1,11.791L1,9.425C1,9.142 1.11,8.87 1.308,8.667Z"
      android:strokeWidth="0.542857143"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="3.781"
          android:startY="7.053"
          android:endX="28.009"
          android:endY="16.901"
          android:type="linear">
        <item android:offset="0" android:color="#FFEAC96F"/>
        <item android:offset="0.276" android:color="#FFF9E39E"/>
        <item android:offset="0.467" android:color="#FFE3BF6C"/>
        <item android:offset="0.73" android:color="#FFC98E37"/>
        <item android:offset="1" android:color="#FFAD7028"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="9.23"
          android:startY="6.771"
          android:endX="17.856"
          android:endY="16.482"
          android:type="linear">
        <item android:offset="0" android:color="#FFD39D4C"/>
        <item android:offset="1" android:color="#FFE7BD5C"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.843,4.482C7.097,4.228 7.442,4.086 7.801,4.086L16.908,4.086C17.267,4.086 17.612,4.228 17.866,4.482L22.3,9.166C22.556,9.42 22.7,9.767 22.7,10.128L22.7,11.022C22.7,11.391 22.55,11.744 22.284,12C18.556,15.48 15.759,18.09 13.895,19.831C12.703,20.941 11.273,20.07 11.15,19.957C9.228,18.189 6.345,15.536 2.501,12C2.236,11.744 2.086,11.391 2.086,11.022L2.086,9.79C2.086,9.43 2.229,9.084 2.484,8.83L6.843,4.482ZM3.008,9.79L3.008,11.022C3.008,11.244 3.098,11.455 3.257,11.609C6.992,15.029 9.793,17.595 11.66,19.305C12.043,19.552 12.757,19.734 13.359,19.186C15.189,17.502 17.934,14.976 21.593,11.609C21.752,11.455 21.842,11.244 21.842,11.022L21.833,10.182C21.833,9.965 21.747,9.758 21.593,9.605L17.273,5.106C17.121,4.955 16.914,4.869 16.699,4.869L8.027,4.814C7.811,4.814 7.604,4.9 7.451,5.052L3.247,9.214C3.094,9.367 3.008,9.574 3.008,9.79Z"
      android:strokeWidth="1"
      android:fillColor="#7F3D12"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M12.423,12.869L16.086,8.157L18.643,8.157L18.643,10.957L18.371,10.957C17.897,10.957 17.449,11.173 17.154,11.545L12.393,17.553L7.713,11.582C7.404,11.187 6.93,10.957 6.429,10.957L6.157,10.957L6.157,8.157L8.806,8.157L12.423,12.869Z"
      android:strokeWidth="0.542857143"
      android:strokeColor="#FFE7B2"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="6.932"
          android:startY="9.667"
          android:endX="16.458"
          android:endY="13.747"
          android:type="linear">
        <item android:offset="0" android:color="#FFB46938"/>
        <item android:offset="0.549" android:color="#FFC88D2E"/>
        <item android:offset="1" android:color="#FF976121"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

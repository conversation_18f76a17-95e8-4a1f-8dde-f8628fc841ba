<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M54.043,28C55.676,28 57,29.324 57,30.957L57,55.043C57,56.676 55.676,58 54.043,58L29.957,58C28.324,58 27,56.676 27,55.043L27,39L36.043,39C37.618,39 38.905,37.769 38.995,36.217L39,36.043L39,28L54.043,28Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33.039"
          android:startY="30.395"
          android:endX="50.154"
          android:endY="58"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M54.043,28C55.676,28 57,29.324 57,30.957L57,55.043C57,56.676 55.676,58 54.043,58L29.957,58C28.324,58 27,56.676 27,55.043L27,39L36.043,39C37.618,39 38.905,37.769 38.995,36.217L39,36.043L39,28L54.043,28Z"/>
    <path
        android:pathData="M50.922,46.424L44.529,46.424C46.869,47.569 49.026,48.714 51,49.859L49.804,51.939C47.294,50.404 44.895,49.051 42.608,47.879C41.275,49.697 38.758,51.071 35.059,52L34,49.616C35.909,49.253 37.405,48.811 38.49,48.293C39.575,47.774 40.34,47.152 40.784,46.424L34.039,46.424L34.039,44.202L36,44.202L36,39.495L41.333,39.495L41.333,38L43.588,38.081L43.588,39.495L49.02,39.495L49.02,44.202L50.922,44.202L50.922,46.424ZM34,35.545L37.969,35.545L37.969,34L40.266,34.081L40.266,35.545L42,35.545L42,37.76L40.266,37.76L40.266,39L37.969,39L37.969,37.76L34,37.76L34,35.545ZM41,44L41,42L38,42L38,44L41,44ZM43,35.545L44.845,35.545L44.845,34L47,34.081L47,35.545L51,35.545L51,37.76L47,37.76L47,39L44.845,39L44.845,37.76L43,37.76L43,35.545ZM47,44L47,42L44,42L44,44L47,44Z"
        android:strokeWidth="1"
        android:fillColor="#040404"
        android:fillType="nonZero"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M50.922,46.424L44.529,46.424C46.869,47.569 49.026,48.714 51,49.859L49.804,51.939C47.294,50.404 44.895,49.051 42.608,47.879C41.275,49.697 38.758,51.071 35.059,52L34,49.616C35.909,49.253 37.405,48.811 38.49,48.293C39.575,47.774 40.34,47.152 40.784,46.424L34.039,46.424L34.039,44.202L36,44.202L36,39.495L41.333,39.495L41.333,38L43.588,38.081L43.588,39.495L49.02,39.495L49.02,44.202L50.922,44.202L50.922,46.424ZM34,35.545L37.969,35.545L37.969,34L40.266,34.081L40.266,35.545L42,35.545L42,37.76L40.266,37.76L40.266,39L37.969,39L37.969,37.76L34,37.76L34,35.545ZM41,44L41,42L38,42L38,44L41,44ZM43,35.545L44.845,35.545L44.845,34L47,34.081L47,35.545L51,35.545L51,37.76L47,37.76L47,39L44.845,39L44.845,37.76L43,37.76L43,35.545ZM47,44L47,42L44,42L44,44L47,44Z"
        android:strokeWidth="1"
        android:fillType="nonZero"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="42.5"
            android:startY="34"
            android:endX="42.5"
            android:endY="52"
            android:type="linear">
          <item android:offset="0" android:color="#FFFEDCC7"/>
          <item android:offset="1" android:color="#FFF7C09F"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M9.957,7L34.043,7A2.957,2.957 0,0 1,37 9.957L37,34.043A2.957,2.957 0,0 1,34.043 37L9.957,37A2.957,2.957 0,0 1,7 34.043L7,9.957A2.957,2.957 0,0 1,9.957 7z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="13.039"
          android:startY="9.395"
          android:endX="30.154"
          android:endY="37"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M23.698,17.023L30,17.023L30,25.79L23.698,25.79L23.698,31L21.263,31L21.263,25.79L15,25.79L15,17.023L21.263,17.023L21.263,14L23.698,14.076L23.698,17.023ZM21,23L21,19L17,19L17,23L21,23ZM28,23L28,19L24,19L24,23L28,23Z"
      android:strokeWidth="1"
      android:fillColor="#040404"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M23.698,17.023L30,17.023L30,25.79L23.698,25.79L23.698,31L21.263,31L21.263,25.79L15,25.79L15,17.023L21.263,17.023L21.263,14L23.698,14.076L23.698,17.023ZM21,23L21,19L17,19L17,23L21,23ZM28,23L28,19L24,19L24,23L28,23Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.5"
          android:startY="14"
          android:endX="22.5"
          android:endY="31"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

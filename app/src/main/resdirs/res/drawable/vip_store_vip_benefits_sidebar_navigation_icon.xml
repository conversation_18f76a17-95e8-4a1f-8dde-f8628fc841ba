<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M12.913,7L51.087,7A5.913,5.913 0,0 1,57 12.913L57,51.087A5.913,5.913 0,0 1,51.087 57L12.913,57A5.913,5.913 0,0 1,7 51.087L7,12.913A5.913,5.913 0,0 1,12.913 7z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="17.065"
          android:startY="10.991"
          android:endX="45.589"
          android:endY="57"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M26.935,19.879L26.935,57.019L29.935,57.019L29.935,19.879C29.935,18.853 29.263,18.021 28.435,18.021C27.607,18.021 26.935,18.853 26.935,19.879Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="28.435"
          android:startY="18.021"
          android:endX="28.435"
          android:endY="57.019"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13.478,12L51.522,12A1.478,1.478 0,0 1,53 13.478L53,17.522A1.478,1.478 0,0 1,51.522 19L13.478,19A1.478,1.478 0,0 1,12 17.522L12,13.478A1.478,1.478 0,0 1,13.478 12z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32.5"
          android:startY="12"
          android:endX="32.5"
          android:endY="19"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M22.545,51C22.796,51 23,51.448 23,52C23,52.552 22.796,53 22.545,53L22.545,53L13.455,53C13.204,53 13,52.552 13,52C13,51.448 13.204,51 13.455,51L13.455,51ZM13.455,44L22.545,44C22.796,44 23,44.448 23,45C23,45.506 22.829,45.925 22.607,45.991L22.545,46L13.455,46C13.204,46 13,45.552 13,45C13,44.494 13.171,44.075 13.393,44.009L13.455,44L22.545,44ZM22.545,37C22.796,37 23,37.448 23,38C23,38.506 22.829,38.925 22.607,38.991L22.545,39L13.455,39C13.204,39 13,38.552 13,38C13,37.494 13.171,37.075 13.393,37.009L13.455,37L22.545,37ZM22.545,30C22.796,30 23,30.448 23,31C23,31.506 22.829,31.925 22.607,31.991L22.545,32L13.455,32C13.204,32 13,31.552 13,31C13,30.494 13.171,30.075 13.393,30.009L13.455,30L22.545,30ZM22.545,23C22.796,23 23,23.448 23,24C23,24.506 22.829,24.925 22.607,24.991L22.545,25L13.455,25C13.204,25 13,24.552 13,24C13,23.494 13.171,23.075 13.393,23.009L13.455,23L22.545,23Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18"
          android:startY="23"
          android:endX="18"
          android:endY="53"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

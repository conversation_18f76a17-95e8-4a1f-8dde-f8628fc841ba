<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="67dp"
    android:height="67dp"
    android:viewportWidth="67"
    android:viewportHeight="67">
  <path
      android:pathData="M0.178,0.963h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.7,33.485m-26.609,0a26.609,26.609 0,1 1,53.218 0a26.609,26.609 0,1 1,-53.218 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.803"
          android:startY="11.118"
          android:endX="47.164"
          android:endY="60.094"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.752,25.745L22.719,51.228C22.935,52.015 23.748,52.479 24.535,52.264C25.323,52.049 25.786,51.236 25.571,50.448L18.604,24.965C18.389,24.177 17.576,23.713 16.788,23.929C16.001,24.144 15.537,24.957 15.752,25.745Z"
      android:strokeAlpha="0.55"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:fillAlpha="0.55">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20.662"
          android:startY="23.876"
          android:endX="20.662"
          android:endY="52.317"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M30.991,50.116L27.295,47.233L24.264,51.151C24.117,51.336 24.264,51.557 24.486,51.557L29.734,51.557C29.808,51.557 29.882,51.52 29.956,51.446L30.991,50.116L30.991,50.116Z"
      android:strokeAlpha="0.3738374"
      android:strokeWidth="1"
      android:fillColor="#FACEB3"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:fillAlpha="0.3738374"/>
  <path
      android:pathData="M24.708,50.559L24.228,51.151C24.08,51.336 24.228,51.557 24.449,51.557L29.697,51.557C29.771,51.557 29.845,51.52 29.919,51.446L30.621,50.522L24.708,50.522L24.708,50.559Z"
      android:strokeWidth="1"
      android:fillColor="#0E1115"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M29.697,51.964L24.449,51.964C24.191,51.964 24.006,51.816 23.895,51.594C23.784,51.372 23.821,51.114 23.969,50.929L26.999,47.012C27.073,46.938 27.147,46.901 27.258,46.864C27.369,46.864 27.443,46.864 27.517,46.938L31.212,49.82C31.36,49.931 31.397,50.19 31.286,50.338L30.215,51.705C30.067,51.89 29.919,51.964 29.697,51.964L29.697,51.964ZM24.671,51.225L29.66,51.225L30.473,50.19L27.369,47.788L24.671,51.225L24.671,51.225Z"
      android:strokeWidth="1"
      android:fillColor="#F9C9AC"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M28.662,35.888L28.33,36.294L28.219,36.516C29.956,39.99 29.512,44.129 27.147,47.159L31.138,50.264C33.541,47.196 37.495,45.755 41.302,46.642L41.413,46.494L41.745,46.088L35.462,41.209L28.662,35.888L28.662,35.888Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="34.446"
          android:startY="35.888"
          android:endX="34.446"
          android:endY="50.264"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M43.787,45.312L29.226,33.855L52.093,8.133C52.98,7.098 54.569,6.95 55.641,7.8L62.293,12.974C63.365,13.824 63.624,15.376 62.811,16.485L43.787,45.312Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="46.257"
          android:startY="7.248"
          android:endX="46.257"
          android:endY="45.312"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

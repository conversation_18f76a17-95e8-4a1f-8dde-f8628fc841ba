<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M46.977,49C47.542,49 48,49.448 48,50C48,50.552 47.542,51 46.977,51L46.977,51L34.023,51C33.458,51 33,50.552 33,50C33,49.448 33.458,49 34.023,49L34.023,49ZM34.023,42L46.977,42C47.542,42 48,42.448 48,43C48,43.515 47.601,43.94 47.089,43.994L46.977,44L34.023,44C33.458,44 33,43.552 33,43C33,42.485 33.399,42.06 33.911,42.006L34.023,42L46.977,42ZM46.977,36C47.542,36 48,36.448 48,37C48,37.515 47.601,37.94 47.089,37.994L46.977,38L34.023,38C33.458,38 33,37.552 33,37C33,36.485 33.399,36.06 33.911,36.006L34.023,36L46.977,36ZM46.977,30C47.542,30 48,30.448 48,31C48,31.515 47.601,31.94 47.089,31.994L46.977,32L34.023,32C33.458,32 33,31.552 33,31C33,30.485 33.399,30.06 33.911,30.006L34.023,30L46.977,30Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="40.5"
          android:startY="30"
          android:endX="40.5"
          android:endY="51"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.913,7L47.087,7A5.913,5.913 0,0 1,53 12.913L53,51.087A5.913,5.913 0,0 1,47.087 57L17.913,57A5.913,5.913 0,0 1,12 51.087L12,12.913A5.913,5.913 0,0 1,17.913 7z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="24.265"
          android:startY="10.991"
          android:endX="39.993"
          android:endY="57"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M19.478,13L46.522,13A1.478,1.478 0,0 1,48 14.478L48,23.522A1.478,1.478 0,0 1,46.522 25L19.478,25A1.478,1.478 0,0 1,18 23.522L18,14.478A1.478,1.478 0,0 1,19.478 13z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33"
          android:startY="13"
          android:endX="33"
          android:endY="25"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M19.478,28L26.522,28A1.478,1.478 0,0 1,28 29.478L28,50.522A1.478,1.478 0,0 1,26.522 52L19.478,52A1.478,1.478 0,0 1,18 50.522L18,29.478A1.478,1.478 0,0 1,19.478 28z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="23"
          android:startY="28"
          android:endX="23"
          android:endY="52"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M47.318,50C47.695,50 48,50.448 48,51C48,51.552 47.695,52 47.318,52L47.318,52L33.682,52C33.305,52 33,51.552 33,51C33,50.448 33.305,50 33.682,50L33.682,50ZM33.682,42L47.318,42C47.695,42 48,42.448 48,43C48,43.51 47.74,43.931 47.404,43.992L47.318,44L33.682,44C33.305,44 33,43.552 33,43C33,42.49 33.26,42.069 33.596,42.008L33.682,42L47.318,42ZM47.318,35C47.695,35 48,35.448 48,36C48,36.51 47.74,36.931 47.404,36.992L47.318,37L33.682,37C33.305,37 33,36.552 33,36C33,35.49 33.26,35.069 33.596,35.008L33.682,35L47.318,35ZM47.318,28C47.695,28 48,28.448 48,29C48,29.51 47.74,29.931 47.404,29.992L47.318,30L33.682,30C33.305,30 33,29.552 33,29C33,28.49 33.26,28.069 33.596,28.008L33.682,28L47.318,28Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="40.5"
          android:startY="28"
          android:endX="40.5"
          android:endY="52"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.5,32.5m-26.5,0a26.5,26.5 0,1 1,53 0a26.5,26.5 0,1 1,-53 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.669"
          android:startY="10.231"
          android:endX="46.905"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M28.129,45L31,47.496L28.234,49.901C28.015,50.092 27.647,49.989 27.569,49.74L27.555,49.668L27.44,48.166C27.428,48.017 27.307,47.891 27.143,47.851L27.057,47.838L25,47.721L28.129,45ZM27.579,31L31,34.421L27.999,37.42L31,40.421L27.421,44L24,40.579L27,37.579L24,34.579L27.579,31ZM20.871,38L24,41.058L20.993,44C20.932,43.94 20.893,43.859 20.889,43.769L20.889,43.769L20.753,41.755C20.744,41.575 20.591,41.43 20.408,41.417L20.408,41.417L18.354,41.284C18.17,41.276 18.022,41.126 18.009,40.947L18.009,40.947L18,40.81L20.871,38ZM34.579,31L38,34.421L34.876,37.544L37,39.668L33.668,43L31,40.332L33.876,37.456L31,34.579L34.579,31ZM41.332,31L44,33.668L40.668,37L38,34.332L41.332,31ZM21.34,31L24,33.66L20.66,37L18,34.34L21.34,31ZM47.714,25L47.83,27.037C47.838,27.214 47.95,27.361 48.096,27.405L48.16,27.417L49.667,27.533C49.943,27.555 50.085,27.909 49.946,28.153L49.901,28.217L47.488,31L45,28.13L47.714,25ZM40.579,24L44,27.421L40.421,31L37.42,28L34.421,31L31,27.579L34.579,24L37.579,26.999L40.579,24ZM28.34,25L31,27.66L27.66,31L25,28.34L28.34,25ZM34.34,18L37,20.66L33.66,24L31,21.34L34.34,18ZM40.825,18L40.94,18.009C41.094,18.016 41.223,18.129 41.264,18.277L41.278,18.353L41.41,20.407C41.418,20.564 41.529,20.695 41.673,20.738L41.748,20.751L43.761,20.887C43.831,20.89 43.897,20.915 43.951,20.955L44,21L41.06,24L38,20.882L40.825,18Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="34"
          android:startY="18"
          android:endX="34"
          android:endY="50"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M48.475,24.01C48.417,23.203 47.773,22.552 46.959,22.513L45.572,22.422L45.479,21.006C45.421,20.199 44.777,19.549 43.964,19.51L42.572,19.418L42.479,18.002C42.421,17.196 41.777,16.545 40.964,16.506L39.571,16.414L39.51,15.503C39.416,14.142 37.765,13.492 36.791,14.468L14.467,36.79C13.491,37.768 14.144,39.438 15.514,39.514L16.411,39.574L16.504,40.986C16.561,41.792 17.206,42.443 18.019,42.482L19.407,42.574L19.5,43.989C19.557,44.796 20.201,45.447 21.015,45.486L22.407,45.578L22.5,46.993C22.557,47.8 23.202,48.451 24.015,48.489L25.407,48.581L25.468,49.497C25.563,50.858 27.214,51.508 28.188,50.532L50.503,28.191C51.526,27.307 50.864,25.578 49.486,25.482L48.568,25.421L48.475,24.01ZM27.284,47C27.163,46.295 26.554,45.743 25.817,45.691L24.396,45.597L24.303,44.183C24.266,43.391 23.616,42.746 22.817,42.689L21.396,42.596L21.303,41.181C21.265,40.389 20.615,39.744 19.816,39.687L18.399,39.594L18.306,38.183C18.272,37.452 17.714,36.847 17,36.713L36.731,17C36.852,17.704 37.462,18.254 38.197,18.306L39.618,18.4L39.711,19.815C39.749,20.606 40.399,21.251 41.198,21.308L42.619,21.402L42.712,22.816C42.749,23.608 43.399,24.253 44.198,24.31L45.615,24.403L45.708,25.814C45.742,26.54 46.292,27.143 47,27.281L27.284,47Z"
      android:strokeWidth="1"
      android:fillColor="#CF9D7F"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
</vector>

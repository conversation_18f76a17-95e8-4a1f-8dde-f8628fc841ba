<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M15.913,11L48.087,11A5.913,5.913 0,0 1,54 16.913L54,49.087A5.913,5.913 0,0 1,48.087 55L15.913,55A5.913,5.913 0,0 1,10 49.087L10,16.913A5.913,5.913 0,0 1,15.913 11z"
      android:strokeWidth="4.43484"
      android:fillColor="#00000000"
      android:strokeColor="#4A4E65"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M15.913,20L37.087,20A5.913,5.913 0,0 1,43 25.913L43,49.087A5.913,5.913 0,0 1,37.087 55L15.913,55A5.913,5.913 0,0 1,10 49.087L10,25.913A5.913,5.913 0,0 1,15.913 20z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="17.737"
          android:startY="22.794"
          android:endX="34.473"
          android:endY="55"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M36.045,48C36.573,48 37,48.448 37,49C37,49.552 36.573,50 36.045,50L36.045,50L16.955,50C16.427,50 16,49.552 16,49C16,48.448 16.427,48 16.955,48L16.955,48ZM16.955,40L36.045,40C36.573,40 37,40.448 37,41C37,41.513 36.632,41.936 36.157,41.993L36.045,42L16.955,42C16.427,42 16,41.552 16,41C16,40.487 16.368,40.064 16.843,40.007L16.955,40L36.045,40ZM36.045,33C36.573,33 37,33.448 37,34C37,34.513 36.632,34.936 36.157,34.993L36.045,35L16.955,35C16.427,35 16,34.552 16,34C16,33.487 16.368,33.064 16.843,33.007L16.955,33L36.045,33ZM36.045,26C36.573,26 37,26.448 37,27C37,27.513 36.632,27.936 36.157,27.993L36.045,28L16.955,28C16.427,28 16,27.552 16,27C16,26.487 16.368,26.064 16.843,26.007L16.955,26L36.045,26Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="26.5"
          android:startY="26"
          android:endX="26.5"
          android:endY="50"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35.752,7.878L52.197,17.372C54.21,18.534 55.45,20.682 55.45,23.005L55.45,41.995C55.45,44.318 54.21,46.466 52.197,47.628L35.752,57.122C33.74,58.284 31.26,58.284 29.248,57.122L12.803,47.628C10.79,46.466 9.55,44.318 9.55,41.995L9.55,23.005C9.55,20.682 10.79,18.534 12.803,17.372L29.248,7.878C31.26,6.716 33.74,6.716 35.752,7.878Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18.789"
          android:startY="11.076"
          android:endX="44.975"
          android:endY="57.994"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M33,28L37,32L37,31.797C37,29.7 35.305,28 33.215,28L33,28L33,28ZM28.493,29L30.225,30.732C30.166,30.969 30.136,31.213 30.135,31.458C30.135,33.309 31.636,34.809 33.487,34.809C33.731,34.807 33.975,34.777 34.213,34.72L36,36.451C33.863,37.476 31.312,37.04 29.636,35.364C27.96,33.688 27.524,31.137 28.549,29L28.493,29ZM22.681,23.455L24.703,25.518L25.211,26.044C23.327,27.566 21.873,29.582 21,31.883C22.298,35.302 24.859,38.045 28.118,39.505C31.377,40.965 35.063,41.021 38.362,39.66L38.847,40.151L41.566,43L43,41.517L24.09,22L22.681,23.455ZM33.533,25.86C35.036,25.86 36.478,26.462 37.54,27.533C38.603,28.604 39.2,30.057 39.2,31.573C39.199,32.286 39.061,32.992 38.792,33.652L42.113,37C43.839,35.548 45.176,33.682 46,31.573C43.355,24.77 35.818,21.325 29,23.803L31.459,26.26C32.127,25.994 32.838,25.858 33.556,25.86L33.533,25.86Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="33.5"
          android:startY="22"
          android:endX="33.5"
          android:endY="43"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M56,32.5C56,19.521 45.479,9 32.5,9C19.521,9 9,19.521 9,32.5C9,45.479 19.521,56 32.5,56L32.5,56"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18.461"
          android:startY="12.752"
          android:endX="45.274"
          android:endY="56"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M23,37.863C24.5,41.43 28.728,44 33.708,44C39.945,44 45,39.971 45,35"
      android:strokeWidth="2.57092174"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="34"
          android:startY="35"
          android:endX="34"
          android:endY="44"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M32.09,56L56,32.105C50.68,30.998 46.791,30.723 44.332,31.279C40.643,32.114 38.567,32.98 36.301,34.895C34.034,36.811 32.065,39.661 31.264,43.5C30.73,46.059 31.006,50.226 32.09,56Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="43.5"
          android:startY="31"
          android:endX="43.5"
          android:endY="56"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M20,25a3,5 0,1 0,6 0a3,5 0,1 0,-6 0z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="23"
          android:startY="20"
          android:endX="23"
          android:endY="30"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M39,25a3,5 0,1 0,6 0a3,5 0,1 0,-6 0z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="42"
          android:startY="20"
          android:endX="42"
          android:endY="30"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="53dp"
    android:height="44dp"
    android:viewportWidth="53"
    android:viewportHeight="44">
  <path
      android:pathData="M1.69,13.379L13.176,1.716C13.628,1.258 14.244,1 14.886,1L37.489,1C38.131,1 38.746,1.257 39.197,1.714L50.708,13.379C51.151,13.828 51.4,14.434 51.4,15.065L51.4,20.213C51.4,20.873 51.128,21.504 50.648,21.958L30.639,40.866C29.506,41.936 28.007,42.532 26.449,42.532C24.89,42.532 23.387,41.944 22.242,40.886L1.771,21.96C1.279,21.506 1,20.867 1,20.198L1,15.063C1,14.433 1.248,13.828 1.69,13.379Z"
      android:strokeWidth="1.4"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="7.146"
          android:startY="10.122"
          android:endX="62.307"
          android:endY="31.126"
          android:type="linear">
        <item android:offset="0" android:color="#FFEAC96F"/>
        <item android:offset="0.276" android:color="#FFF9E39E"/>
        <item android:offset="0.555" android:color="#FFE3BF6C"/>
        <item android:offset="1" android:color="#FFAD7028"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="19.193"
          android:startY="9.52"
          android:endX="38.262"
          android:endY="30.277"
          android:type="linear">
        <item android:offset="0" android:color="#FFD39D4C"/>
        <item android:offset="1" android:color="#FFE7BD5C"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13.915,4.276C14.477,3.715 15.239,3.4 16.034,3.4L36.166,3.401C36.959,3.401 37.72,3.716 38.283,4.275L48.085,14.63C48.65,15.193 48.968,15.958 48.968,16.756L48.968,18.734C48.968,19.548 48.636,20.328 48.05,20.894C39.807,28.587 33.625,34.358 29.504,38.204C26.87,40.659 23.709,38.734 23.438,38.485C19.189,34.576 12.816,28.712 4.318,20.894C3.732,20.328 3.4,19.548 3.4,18.734L3.4,16.01C3.4,15.213 3.717,14.449 4.281,13.886L13.915,4.276ZM5.439,16.01L5.439,18.734C5.439,19.223 5.638,19.69 5.99,20.03C14.245,27.591 20.437,33.263 24.565,37.043C25.411,37.589 26.988,37.991 28.32,36.779C32.365,33.057 38.432,27.474 46.522,20.03C46.874,19.69 47.073,19.223 47.073,18.734L47.052,16.876C47.052,16.397 46.861,15.938 46.522,15.6L36.972,5.656C36.635,5.321 36.178,5.132 35.702,5.132L16.532,5.011C16.056,5.011 15.599,5.2 15.261,5.536L5.968,14.736C5.629,15.074 5.439,15.532 5.439,16.01Z"
      android:strokeWidth="1"
      android:fillColor="#7F3D12"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M26.251,22.653L34.299,12.3L40.1,12.3L40.1,18.688L39.4,18.688C38.382,18.688 37.42,19.153 36.788,19.951L26.183,33.331L15.761,20.032C15.096,19.184 14.078,18.688 13,18.688L12.3,18.688L12.3,12.3L18.306,12.3L26.251,22.653Z"
      android:strokeWidth="1.4"
      android:strokeColor="#FFE7B2"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="14.026"
          android:startY="15.68"
          android:endX="35.236"
          android:endY="24.813"
          android:type="linear">
        <item android:offset="0" android:color="#FFB46938"/>
        <item android:offset="0.549" android:color="#FFC88D2E"/>
        <item android:offset="1" android:color="#FF976121"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

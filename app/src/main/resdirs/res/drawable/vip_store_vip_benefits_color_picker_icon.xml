<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="66dp"
    android:height="66dp"
    android:viewportWidth="66"
    android:viewportHeight="66">
  <path
      android:pathData="M0,0h66v66h-66z"
      android:strokeWidth="1"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M13.097,49C10.516,52.333 9.548,55 10.194,57C10.646,58.402 11.843,59.01 13.097,59C14.526,58.988 16,58.131 16,56C16,54 15.032,51.667 13.097,49Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="12.355"
          android:startY="49.798"
          android:endX="13.587"
          android:endY="59"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M23.05,32l5.872,6.856l-3.935,3.935l-9.824,3.949l-0.977,-1.961l4.928,-8.845z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="21.554"
          android:startY="32"
          android:endX="21.554"
          android:endY="46.74"
          android:type="linear">
        <item android:offset="0" android:color="#FFFEDCC7"/>
        <item android:offset="1" android:color="#FFF7C09F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M30.437,22.256l-13.174,12.662l-3.646,8.702l2.952,2.852l8.588,-2.987l13.174,-12.662"
      android:strokeWidth="2.95656"
      android:fillColor="#00000000"
      android:fillType="evenOdd">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="23.16"
          android:startY="31.74"
          android:endX="29.313"
          android:endY="42.655"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M54.046,8.303C56.975,11.232 56.975,15.981 54.046,18.91L37.429,35.527L37.429,35.527L26.822,24.92L43.439,8.303C46.368,5.374 51.117,5.374 54.046,8.303Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="38.088"
          android:startY="16.415"
          android:endX="46.382"
          android:endY="30.401"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M27.077,22.359L39.208,34.49A1.922,1.922 90,0 1,39.208 37.208L39.097,37.319A1.922,1.922 0,0 1,36.38 37.319L24.248,25.187A1.922,1.922 90,0 1,24.248 22.47L24.359,22.359A1.922,1.922 90,0 1,27.077 22.359z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.198"
          android:startY="29.547"
          android:endX="37.347"
          android:endY="30.359"
          android:type="linear">
        <item android:offset="0" android:color="#FF565B77"/>
        <item android:offset="1" android:color="#FF22222E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

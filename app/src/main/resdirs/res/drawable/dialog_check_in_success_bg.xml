<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">

    <item>
        <shape>
            <gradient
                android:angle="90"
                android:endColor="#80FFFFFF"
                android:startColor="#99A79CFF" />
            <corners android:radius="@dimen/dp_45" />

        </shape>
    </item>

    <item
        android:bottom="@dimen/dp_10"
        android:left="@dimen/dp_10"
        android:right="@dimen/dp_10"
        android:top="@dimen/dp_10">
        <shape>
            <gradient
                android:angle="90"
                android:endColor="#FFDFF7FF"
                android:startColor="#FFFDDEFF" />
            <corners android:radius="@dimen/dp_45" />
        </shape>
    </item>

</layer-list>
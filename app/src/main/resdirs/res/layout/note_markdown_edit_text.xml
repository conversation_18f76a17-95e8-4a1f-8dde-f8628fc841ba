<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android">

    <com.topstack.kilonotes.base.doodle.views.textborderview.DoodleEditTextView
        android:id="@+id/note_text_editor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:includeFontPadding="true"
        android:paddingBottom="@dimen/dp_1"
        android:textCursorDrawable="@drawable/note_edit_text_cursor"
        android:textSelectHandle="@drawable/shape_cursor_handler"
        android:textSelectHandleLeft="@drawable/shape_cursor_handler"
        android:visibility="gone"
        android:textSelectHandleRight="@drawable/shape_cursor_handler" />

    <com.topstack.kilonotes.base.doodle.views.markdownview.MarkdownPreviewTextView
        android:id="@+id/note_text_preview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:includeFontPadding="true"
        android:paddingBottom="@dimen/dp_1" />
</merge>
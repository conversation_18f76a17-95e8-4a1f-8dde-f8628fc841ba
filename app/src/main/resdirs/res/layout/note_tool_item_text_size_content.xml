<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_height="@dimen/dp_60"
    tools:layout_width="@dimen/dp_60">

    <TextView
        android:id="@+id/text_size_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_20" />
</FrameLayout>
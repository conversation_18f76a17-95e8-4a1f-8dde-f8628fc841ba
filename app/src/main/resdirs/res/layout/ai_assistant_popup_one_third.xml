<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_575"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/white">
        <TextView
            android:id="@+id/ai_assistant_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:gravity="center"
            android:text="@string/ai_assistant_title"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:layout_marginHorizontal="@dimen/dp_278"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/ai_assistant_guide_close"
            app:layout_constraintBottom_toBottomOf="@id/ai_assistant_guide_close"
            />

        <ImageView
            android:id="@+id/ai_assistant_guide_close"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:src="@drawable/dialog_setting_general_icon_close"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_18"
            android:padding="@dimen/dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <ImageView
            android:id="@+id/ai_assistant_chinese_banner"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_211"
            android:scaleType="centerCrop"
            android:layout_marginTop="@dimen/dp_20"
            app:layout_constraintTop_toBottomOf="@id/ai_assistant_title"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <ScrollView
            android:id="@+id/guiding_copy"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_100"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_20"
            android:scrollbars="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ai_assistant_chinese_banner">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ai_laboratory_introduction"
                android:textSize="@dimen/sp_20"
                />
        </ScrollView>

        <View
            android:id="@+id/frame"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_70"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_30"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:background="@drawable/ai_assistant_guide_frame"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/guiding_copy"
            app:layout_constraintBottom_toBottomOf="parent"
            />
        <TextView
            android:id="@+id/ai_assistant_word"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ai_assistant_title"
            android:textSize="@dimen/sp_24"
            android:textColor="@color/text_secondary"
            android:layout_marginStart="@dimen/dp_30"
            app:layout_constraintTop_toTopOf="@id/frame"
            app:layout_constraintBottom_toBottomOf="@id/frame"
            app:layout_constraintStart_toStartOf="@id/frame"
            />

        <Switch
            android:id="@+id/ai_assistant_button_switch"
            style="@style/SwitchStyle"
            android:layout_marginEnd="@dimen/dp_30"
            app:layout_constraintEnd_toEndOf="@id/frame"
            app:layout_constraintBottom_toBottomOf="@+id/ai_assistant_word"
            app:layout_constraintTop_toTopOf="@id/ai_assistant_word"
            tools:ignore="UseSwitchCompatOrMaterialXml"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

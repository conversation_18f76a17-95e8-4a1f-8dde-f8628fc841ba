<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_424"
    android:layout_height="@dimen/dp_88">

    <com.topstack.kilonotes.base.shadow.FixShadowLayout
        android:id="@+id/undo_and_redo_container"
        android:layout_width="@dimen/dp_416"
        android:layout_height="@dimen/dp_80"
        android:layout_marginTop="@dimen/dp_8"
        app:hl_cornerRadius="@dimen/dp_10"
        app:hl_layoutBackground="@color/transparent"
        app:hl_shadowColor="@color/black_20"
        app:hl_shadowLimit="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/dp_392"
            android:layout_height="@dimen/dp_56">

            <com.lihang.ShadowLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:hl_cornerRadius="@dimen/dp_10"
                app:hl_layoutBackground="@color/green">

                <eightbitlab.com.blurview.BlurView
                    android:id="@+id/tool_bar_blur_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:blurOverlayColor="@color/white_97">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/content_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/content"
                            android:layout_width="@dimen/dp_392"
                            android:layout_height="@dimen/dp_56"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </eightbitlab.com.blurview.BlurView>
            </com.lihang.ShadowLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.topstack.kilonotes.base.shadow.FixShadowLayout>

    <ImageView
        android:id="@+id/forbid"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:src="@drawable/file_manager_drag_icon_forbid"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
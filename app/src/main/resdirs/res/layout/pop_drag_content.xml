<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/move_tip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingBottom="@dimen/dp_10"
        android:text="@string/folder_move_to_home_page"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_24"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/move_tip" />

    <ImageView
        android:id="@+id/forbid"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:src="@drawable/file_manager_drag_icon_forbid"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@+id/content"
        app:layout_constraintTop_toTopOf="@+id/content" />
</androidx.constraintlayout.widget.ConstraintLayout>
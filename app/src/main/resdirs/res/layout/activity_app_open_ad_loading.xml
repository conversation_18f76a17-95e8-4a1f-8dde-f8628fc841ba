<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/splash_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <!-- logo  -->
    <ImageView
        android:id="@+id/logo"
        android:layout_width="@dimen/dp_160"
        android:layout_height="@dimen/dp_160"
        android:scaleType="fitXY"
        android:src="@drawable/splash_icon_logo"
        app:layout_constraintBottom_toTopOf="@id/app_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="ContentDescription" />
    <!-- logo  -->

    <!-- app name -->
    <TextView
        android:id="@+id/app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_32"
        android:text="@string/app_name"
        android:textColor="#FF131415"
        android:textSize="@dimen/sp_60"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/logo" />
    <!-- app name -->

</androidx.constraintlayout.widget.ConstraintLayout>
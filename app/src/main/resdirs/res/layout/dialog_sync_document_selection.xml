<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_32dp_white"
    android:clipToPadding="false">

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_80"
        android:gravity="center"
        android:text="@string/sync_selection_dialog_title"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_30"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/back" />

    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_27"
        android:layout_marginEnd="@dimen/dp_24"
        android:padding="@dimen/dp_12"
        android:src="@drawable/dialog_setting_general_icon_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/empty_image"
        android:layout_width="@dimen/dp_270"
        android:layout_height="@dimen/dp_270"
        android:scaleType="centerInside"
        android:src="@drawable/note_backup_empty_background"
        app:layout_constraintBottom_toTopOf="@id/footer_area"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/empty_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="empty_image, empty_tips" />

    <TextView
        android:id="@+id/empty_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:text="@string/sync_no_available_documents_tips"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/empty_image" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/list"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_marginHorizontal="@dimen/dp_36"
        android:layout_marginTop="@dimen/dp_15"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:paddingTop="@dimen/dp_15"
        app:layout_constraintBottom_toTopOf="@id/footer_area"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:listitem="@layout/dialog_backup_item" />

    <View
        android:id="@+id/footer_area"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80"
        android:background="@color/white"
        android:elevation="@dimen/dp_48"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/select_all_group"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="@id/select_all_checkbox"
        app:layout_constraintEnd_toEndOf="@id/select_all_text"
        app:layout_constraintStart_toStartOf="@+id/select_all_checkbox"
        app:layout_constraintTop_toTopOf="@id/select_all_checkbox" />

    <ImageView
        android:id="@+id/select_all_checkbox"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:elevation="@dimen/dp_48"
        android:src="@drawable/dialog_backup_checkbox"
        app:layout_constraintBottom_toBottomOf="@id/footer_area"
        app:layout_constraintStart_toStartOf="@id/list"
        app:layout_constraintTop_toTopOf="@id/footer_area"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/select_all_text"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:elevation="@dimen/dp_48"
        android:text="@string/select_all"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_20"
        app:layout_constraintBottom_toBottomOf="@id/select_all_checkbox"
        app:layout_constraintEnd_toStartOf="@id/confirm"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/select_all_checkbox"
        app:layout_constraintTop_toTopOf="@id/select_all_checkbox" />

    <TextView
        android:id="@+id/confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/note_backup_confirm_button_background_selector"
        android:elevation="@dimen/dp_48"
        android:gravity="center"
        android:maxLines="2"
        android:paddingHorizontal="@dimen/dp_24"
        android:paddingVertical="@dimen/dp_8"
        android:text="@string/sync_auto_backup"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_24"
        app:layout_constraintBottom_toBottomOf="@id/footer_area"
        app:layout_constraintEnd_toEndOf="@id/footer_area"
        app:layout_constraintHeight_min="@dimen/dp_48"
        app:layout_constraintStart_toStartOf="@id/footer_area"
        app:layout_constraintTop_toTopOf="@id/footer_area"
        app:layout_constraintWidth_max="@dimen/dp_300"
        app:layout_constraintWidth_min="@dimen/dp_252" />

</androidx.constraintlayout.widget.ConstraintLayout>
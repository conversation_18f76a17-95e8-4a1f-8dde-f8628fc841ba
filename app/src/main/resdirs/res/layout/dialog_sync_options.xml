<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_32dp_white">

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_68"
        android:gravity="center"
        android:text="@string/sync_login_title"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_28"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/back"
        app:layout_constraintWidth_max="wrap" />

    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_20"
        android:padding="@dimen/dp_12"
        android:src="@drawable/dialog_setting_general_icon_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginVertical="@dimen/dp_20"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/back">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/option_only_wifi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_30"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_30"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@string/sync_options_dialog_option_upload_through_wifi"
                    android:textColor="@color/text_secondary"
                    android:textSize="@dimen/sp_24" />

                <Switch
                    android:id="@+id/only_wifi_switch"
                    style="@style/SwitchStyle"
                    android:layout_gravity="end"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/option_sync_all"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_30"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_30"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@string/sync_options_dialog_option_sync_all"
                    android:textColor="@color/text_secondary"
                    android:textSize="@dimen/sp_24" />

                <Switch
                    android:id="@+id/sync_all_switch"
                    style="@style/SwitchStyle"
                    android:layout_gravity="end"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/option_select_doc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_30"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_30"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@string/sync_options_dialog_option_select_sync_doc"
                    android:textColor="@color/text_secondary"
                    android:textSize="@dimen/sp_24" />

                <ImageView
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_24"
                    android:layout_gravity="center_vertical"
                    android:scaleType="fitEnd"
                    android:src="@drawable/note_icon_jump_arrow" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/option_last_sync"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_30"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_30"
                    android:gravity="center_vertical"
                    android:text="@string/sync_options_dialog_last_sync_time_title"
                    android:textColor="@color/text_secondary"
                    android:textSize="@dimen/sp_24" />

                <TextView
                    android:id="@+id/last_sync_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:layout_marginEnd="@dimen/dp_90"
                    android:gravity="center_vertical"
                    android:textColor="@color/backup_subtitle_color"
                    android:textSize="@dimen/sp_18"
                    tools:text="2023-05-25 17:23" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/option_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_30"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center_vertical"
                    android:text="@string/hidden_space_notice_tips"
                    android:textColor="@color/backup_subtitle_color"
                    android:textSize="@dimen/sp_18" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center_vertical"
                    android:text="@string/sync_options_dialog_important_tips"
                    android:textColor="@color/backup_subtitle_color"
                    android:textSize="@dimen/sp_18" />

                <TextView
                    android:id="@+id/sync_options_dialog_tips_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center_vertical"
                    android:text="@string/sync_options_dialog_tips_1"
                    android:textColor="@color/backup_subtitle_color"
                    android:textSize="@dimen/sp_18" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center_vertical"
                    android:text="@string/sync_options_dialog_tips_2"
                    android:textColor="@color/backup_subtitle_color"
                    android:textSize="@dimen/sp_18" />

                <TextView
                    android:id="@+id/sync_options_dialog_tips_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:gravity="center_vertical"
                    android:text="@string/sync_options_dialog_tips_3"
                    android:textColor="@color/backup_subtitle_color"
                    android:textSize="@dimen/sp_18" />

            </androidx.appcompat.widget.LinearLayoutCompat>

        </androidx.appcompat.widget.LinearLayoutCompat>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
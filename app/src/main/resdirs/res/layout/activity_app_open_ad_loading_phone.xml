<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/splash_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/phone_guide_page_bg"
    android:orientation="vertical">

    <!-- logo  -->
    <ImageView
        android:layout_width="@dimen/dp_240"
        android:layout_height="@dimen/dp_240"
        android:scaleType="fitXY"
        android:src="@drawable/splash_icon_logo"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.35" />
    <!-- logo  -->

    <FrameLayout
        android:id="@+id/ad_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="invisible" />

</androidx.constraintlayout.widget.ConstraintLayout>
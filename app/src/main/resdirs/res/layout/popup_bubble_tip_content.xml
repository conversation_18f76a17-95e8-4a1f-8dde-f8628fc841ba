<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <TextView
        android:id="@+id/tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/dp_36"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_48"
        android:maxWidth="@dimen/dp_704"
        android:text="@string/create_folder_guide"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"/>

    <TextView
        android:id="@+id/know"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/guide_terms_text_link"
        android:textSize="@dimen/sp_42"
        android:text="@string/know"
        app:layout_constraintEnd_toEndOf="@id/tips"
        app:layout_constraintTop_toBottomOf="@id/tips"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/dp_36"
        android:layout_marginTop="@dimen/dp_48" />


</androidx.constraintlayout.widget.ConstraintLayout>
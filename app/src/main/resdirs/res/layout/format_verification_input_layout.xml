<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_height="@dimen/dp_64"
    tools:layout_width="@dimen/dp_840">

    <EditText
        android:id="@+id/common_input_layout_edit"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/format_verification_edit_text_background_selector"
        android:ellipsize="end"
        android:imeOptions="actionDone"
        android:importantForAutofill="no"
        android:inputType="text"
        android:textColor="@color/book_name_edit_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0"
        tools:ignore="LabelFor"
        tools:text="无标题的笔记本" />

    <ImageView
        android:id="@+id/common_input_layout_clear"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginEnd="@dimen/dp_16"
        android:scaleType="fitXY"
        android:src="@drawable/doodle_text_element_icon_clear"
        app:layout_constraintBottom_toBottomOf="@id/common_input_layout_edit"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toStartOf="@id/common_input_layout_edit"
        app:layout_constraintEnd_toEndOf="@id/common_input_layout_edit"
        app:layout_constraintTop_toTopOf="@id/common_input_layout_edit"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
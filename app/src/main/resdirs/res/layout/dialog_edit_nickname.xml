<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp_24"
    android:background="@drawable/dialog_outline_create_bg">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/edit_nickname_title"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/edit_nickname"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:background="@drawable/rounded_corner_edit_text_bg"
        android:hint="@string/edit_nickname_hint"
        android:inputType="textPersonName"
        android:maxLength="20"
        android:padding="@dimen/dp_12"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_title" />

    <TextView
        android:id="@+id/random_nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:text="@string/generate_random_nickname"
        android:textColor="@color/black"
        android:textSize="14sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/edit_nickname" />

    <TextView
        android:id="@+id/cancel_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_24"
        android:padding="@dimen/dp_8"
        android:text="@string/cancel"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintRight_toLeftOf="@id/confirm_button"
        app:layout_constraintTop_toBottomOf="@id/random_nickname" />

    <TextView
        android:id="@+id/confirm_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_24"
        android:padding="@dimen/dp_8"
        android:text="@string/confirm"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/random_nickname" />

</androidx.constraintlayout.widget.ConstraintLayout> 
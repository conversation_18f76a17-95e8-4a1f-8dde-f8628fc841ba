<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_132"
        android:layout_marginTop="@dimen/dp_36"
        android:text="@string/custom_tool_hide_title"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_24"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tools_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/dp_96"
        app:layout_constraintHeight_max="@dimen/dp_790" />

</androidx.constraintlayout.widget.ConstraintLayout>
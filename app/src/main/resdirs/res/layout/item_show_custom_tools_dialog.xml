<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/custom_tool_icon"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_42"
        android:layout_marginStart="@dimen/dp_32"
        android:src="@drawable/more_custom_tool_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/custom_tool_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:text="@string/custom_tools_title"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_22"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/custom_tool_icon"
        app:layout_constraintStart_toEndOf="@+id/custom_tool_icon"
        app:layout_constraintTop_toTopOf="@+id/custom_tool_icon" />

    <ImageView
        android:id="@+id/next_icon"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_35"
        android:paddingVertical="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_32"
        android:src="@drawable/custom_tool_icon_enter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
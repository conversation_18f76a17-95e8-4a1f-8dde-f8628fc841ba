<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:motion="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/start">
        <Constraint android:id="@+id/template_daily_plan">
            <Layout
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_270"
                android:layout_marginBottom="@dimen/dp_370"
                motion:layout_constraintBottom_toBottomOf="@id/template_yellow"
                motion:layout_constraintStart_toEndOf="parent" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_purple">
            <Layout
                android:layout_width="@dimen/dp_180"
                android:layout_height="@dimen/dp_220"
                android:layout_marginBottom="@dimen/dp_120"
                motion:layout_constraintBottom_toTopOf="@id/template_yellow"
                motion:layout_constraintStart_toEndOf="parent" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_green">
            <Layout
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_250"
                android:layout_marginTop="@dimen/dp_120"
                motion:layout_constraintStart_toEndOf="parent"
                motion:layout_constraintTop_toTopOf="@id/template_yellow" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_sport_plan">
            <Layout
                android:layout_width="@dimen/dp_230"
                android:layout_height="@dimen/dp_280"
                android:layout_marginTop="@dimen/dp_30"
                motion:layout_constraintStart_toEndOf="parent"
                motion:layout_constraintTop_toBottomOf="@id/template_yellow" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_blue">
            <Layout
                android:layout_width="@dimen/dp_250"
                android:layout_height="@dimen/dp_320"
                android:layout_marginBottom="@dimen/dp_100"
                motion:layout_constraintBottom_toTopOf="@id/template_yellow"
                motion:layout_constraintStart_toEndOf="parent" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_plan_day">
            <Layout
                android:layout_width="@dimen/dp_270"
                android:layout_height="@dimen/dp_320"
                android:layout_marginBottom="@dimen/dp_40"
                motion:layout_constraintBottom_toBottomOf="@id/template_yellow"
                motion:layout_constraintStart_toEndOf="parent" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_star">
            <Layout
                android:layout_width="@dimen/dp_200"
                android:layout_height="@dimen/dp_260"
                android:layout_marginTop="@dimen/dp_180"
                motion:layout_constraintStart_toEndOf="parent"
                motion:layout_constraintTop_toTopOf="@id/template_yellow" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_date_to_do">
            <Layout
                android:layout_width="@dimen/dp_240"
                android:layout_height="@dimen/dp_300"
                motion:layout_constraintStart_toEndOf="parent"
                motion:layout_constraintTop_toBottomOf="@id/template_yellow" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_clock_in_days">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_template_december">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_recite_words">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/guide_start_use">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/guide_terms_and_policy">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/middle">
        <Constraint android:id="@+id/template_clock_in_days">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_template_december">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_recite_words">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/guide_start_use">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

        <Constraint android:id="@+id/guide_terms_and_policy">
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
            <PropertySet android:alpha="0.0" />
        </Constraint>

    </ConstraintSet>

    <ConstraintSet android:id="@+id/end" />

    <Transition
        android:id="@+id/sliceAnimation"
        motion:constraintSetEnd="@+id/middle"
        motion:constraintSetStart="@id/start"
        motion:duration="600" />

    <Transition
        android:id="@+id/scaleAnimation"
        motion:constraintSetEnd="@+id/end"
        motion:constraintSetStart="@id/middle"
        motion:duration="1000">
        <KeyFrameSet>
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.6"
                android:scaleY="0.6"
                motion:framePosition="0"
                motion:motionTarget="@id/template_clock_in_days" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="40"
                motion:motionTarget="@id/template_clock_in_days" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.6"
                android:scaleY="0.6"
                motion:framePosition="20"
                motion:motionTarget="@id/template_template_december" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="60"
                motion:motionTarget="@id/template_template_december" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.6"
                android:scaleY="0.6"
                motion:framePosition="40"
                motion:motionTarget="@id/template_recite_words" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="80"
                motion:motionTarget="@id/template_recite_words" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.6"
                android:scaleY="0.6"
                motion:framePosition="80"
                motion:motionTarget="@id/guide_start_use" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.6"
                android:scaleY="0.6"
                motion:framePosition="80"
                motion:motionTarget="@id/guide_terms_and_policy" />
        </KeyFrameSet>
    </Transition>
</MotionScene>
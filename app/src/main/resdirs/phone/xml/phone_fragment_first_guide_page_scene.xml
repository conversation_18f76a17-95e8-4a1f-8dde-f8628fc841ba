<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:motion="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/start">
        <Constraint android:id="@+id/slice_text">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.3"
                android:scaleY="0.3" />
        </Constraint>

        <Constraint android:id="@+id/handbook">
            <PropertySet android:alpha="0.4" />
            <Transform
                android:scaleX="0.4"
                android:scaleY="0.4" />
        </Constraint>

        <Constraint android:id="@+id/first_handbook">
            <PropertySet android:alpha="0.2" />
            <Transform
                android:scaleX="0.4"
                android:scaleY="0.4" />
        </Constraint>

        <Constraint android:id="@+id/template_green">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_red">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_travel_summary">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/palette">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/tools">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

    </ConstraintSet>

    <ConstraintSet android:id="@+id/end" />

    <Transition
        motion:constraintSetEnd="@+id/end"
        motion:constraintSetStart="@id/start"
        motion:duration="1000">
        <KeyFrameSet>
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="40"
                motion:motionTarget="@id/slice_text" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="40"
                motion:motionTarget="@id/handbook" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.6"
                android:scaleY="0.6"
                motion:framePosition="30"
                motion:motionTarget="@+id/template_green" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="50"
                motion:motionTarget="@+id/template_green" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.4"
                android:scaleY="0.4"
                motion:framePosition="50"
                motion:motionTarget="@id/template_red" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="70"
                motion:motionTarget="@id/template_red" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.5"
                android:scaleY="0.5"
                motion:framePosition="75"
                motion:motionTarget="@id/template_travel_summary" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.3"
                android:scaleY="0.3"
                motion:framePosition="50"
                motion:motionTarget="@id/palette" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="70"
                motion:motionTarget="@id/palette" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.7"
                android:scaleY="0.7"
                motion:framePosition="80"
                motion:motionTarget="@id/tools" />
        </KeyFrameSet>
    </Transition>
</MotionScene>
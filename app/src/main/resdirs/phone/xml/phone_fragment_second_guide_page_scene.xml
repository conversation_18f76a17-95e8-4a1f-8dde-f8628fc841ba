<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:motion="http://schemas.android.com/apk/res-auto">


    <ConstraintSet android:id="@+id/start">

        <Constraint android:id="@+id/template_white">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_blue">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/template_purple">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/draw_tools">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/font_tools">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>

        <Constraint android:id="@+id/text_tools">
            <PropertySet android:alpha="0.0" />
            <Transform
                android:scaleX="0.0"
                android:scaleY="0.0" />
        </Constraint>
        <Constraint
            motion:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            motion:layout_constraintBottom_toTopOf="@id/handbook"
            motion:layout_constraintTop_toTopOf="parent"
            motion:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="0dp"
            android:id="@+id/slice_text" />

    </ConstraintSet>

    <ConstraintSet android:id="@+id/end" />

    <Transition
        motion:constraintSetEnd="@+id/end"
        motion:constraintSetStart="@id/start"
        motion:duration="1000">
        <KeyFrameSet>
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.4"
                android:scaleY="0.4"
                motion:framePosition="0"
                motion:motionTarget="@id/template_white" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="30"
                motion:motionTarget="@id/template_white" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.4"
                android:scaleY="0.4"
                motion:framePosition="30"
                motion:motionTarget="@id/template_blue" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="60"
                motion:motionTarget="@id/template_blue" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.4"
                android:scaleY="0.4"
                motion:framePosition="60"
                motion:motionTarget="@id/template_purple" />
            <KeyAttribute
                android:alpha="1.0"
                android:scaleX="1.0"
                android:scaleY="1.0"
                motion:framePosition="90"
                motion:motionTarget="@id/template_purple" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.4"
                android:scaleY="0.4"
                motion:framePosition="85"
                motion:motionTarget="@id/draw_tools" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.4"
                android:scaleY="0.4"
                motion:framePosition="80"
                motion:motionTarget="@id/font_tools" />
            <KeyAttribute
                android:alpha="0.0"
                android:scaleX="0.4"
                android:scaleY="0.4"
                motion:framePosition="80"
                motion:motionTarget="@id/text_tools" />
        </KeyFrameSet>
    </Transition>

</MotionScene>
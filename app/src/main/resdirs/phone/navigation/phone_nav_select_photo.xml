<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_select_photo"
    app:startDestination="@id/photoListFragment">

    <fragment
        android:id="@+id/photoListFragment"
        android:name="com.topstack.kilonotes.phone.select.PhonePhotoListFragment"
        android:label="PhotoListFragment"
        tools:layout="@layout/phone_fragment_photo_list">
        <action
            android:id="@+id/pick"
            app:destination="@id/pickPhotoFragment" />
        <action
            android:id="@+id/showAlbum"
            app:destination="@+id/albumPhotoListFragment"/>
        <action
            android:id="@+id/action_photoListFragment_to_pick_and_crop_photo_fragment"
            app:destination="@id/pick_and_crop_photo_fragment" />
    </fragment>
    <fragment
        android:id="@+id/pickPhotoFragment"
        android:name="com.topstack.kilonotes.phone.select.PhonePickPhotoFragment"
        android:label="PickPhotoFragment"
        tools:layout="@layout/phone_fragment_pick_photo" />
    <fragment
        android:id="@+id/albumPhotoListFragment"
        android:name="com.topstack.kilonotes.phone.select.PhoneAlbumPhotoListFragment"
        android:label="AlbumPhotoListFragment"
        tools:layout="@layout/phone_fragment_album_photo_list">
        <action
            android:id="@+id/pick"
            app:destination="@id/pickPhotoFragment" />
    </fragment>

    <fragment
        android:id="@+id/pick_and_crop_photo_fragment"
        android:name="com.topstack.kilonotes.phone.select.PhonePickAndCropPhotoFragment"
        android:label="PickAndCropPhotoFragment"
        tools:layout="@layout/phone_fragment_pick_and_crop_photo" />
</navigation>
<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/phone_nav_graph"
    app:startDestination="@id/note_list">

    <fragment
        android:id="@+id/note_list"
        android:name="com.topstack.kilonotes.phone.note.PhoneNoteListFragment"
        android:label="NoteList"
        tools:layout="@layout/note_list_fragment">

        <action
            android:id="@+id/catalog"
            app:destination="@+id/note_catalog" />

        <action
            android:id="@+id/edit"
            app:destination="@id/note_editor" />

        <action
            android:id="@+id/create"
            app:destination="@+id/note_create">
            <argument
                android:name="editMode"
                android:defaultValue="false"
                app:argType="boolean" />
        </action>

        <action
            android:id="@+id/folder"
            app:destination="@id/note_folder">
            <argument
                android:name="isHiddenSpaceModeAddNote"
                android:defaultValue="false"
                app:argType="boolean" />
        </action>

        <action
            android:id="@+id/edit_cover"
            app:destination="@+id/note_create">
            <argument
                android:name="editMode"
                android:defaultValue="true"
                app:argType="boolean" />
        </action>
        <action
            android:id="@+id/phone_action_note_list_to_vip_store"
            app:destination="@id/vip_store">
            <argument
                android:name="source"
                android:defaultValue="H_ICON"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/folder_inside"
            app:destination="@id/folder_note_show" />

        <action
            android:id="@+id/action_buy_membership_store"
            app:destination="@id/vip_store_new_edition">
            <argument
                android:name="source"
                android:defaultValue="H_WINDOW"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

        <action
            android:id="@+id/hidden_space"
            app:destination="@id/hidden_space_notice_fragment" />

        <action
            android:id="@+id/list_backup"
            app:destination="@id/backup" />

    </fragment>

    <fragment
        android:id="@+id/note_create"
        android:name="com.topstack.kilonotes.phone.note.PhoneCreateNoteFragment"
        android:label="NoteCreate"
        tools:layout="@layout/phone_fragment_create_notebook">

        <argument
            android:name="editMode"
            android:defaultValue="false"
            app:argType="boolean" />

        <action
            android:id="@+id/create_page"
            app:destination="@+id/note_create_page" />
    </fragment>

    <fragment
        android:id="@+id/note_create_page"
        android:name="com.topstack.kilonotes.phone.note.PhoneCreateNotePageFragment"
        android:label="NoteCreatePage"
        tools:layout="@layout/phone_create_note_paper_or_template">
        <action
            android:id="@+id/edit"
            app:destination="@+id/note_editor" />
        <action
            android:id="@+id/action_phone_note_create_page_to_vip_store"
            app:destination="@id/vip_store">
            <argument
                android:name="source"
                android:defaultValue="EDIT_TEMPLATE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/buy_template"
            app:destination="@id/handbook_detail">
            <argument
                android:name="noteId"
                app:argType="long" />
        </action>

    </fragment>

    <fragment
        android:id="@+id/note_editor"
        android:name="com.topstack.kilonotes.phone.note.NoteEditorFragment"
        android:label="NoteEditor"
        tools:layout="@layout/fragment_note_editor">

        <action
            android:id="@+id/list_notes"
            app:destination="@+id/note_list" />

        <action
            android:id="@+id/show_thumbnail"
            app:destination="@id/page_thumbnail" />
        <action
            android:id="@+id/action_phone_note_editor_to_vip_store"
            app:destination="@id/vip_store_new_edition">
            <argument
                android:name="source"
                android:defaultValue="EDIT_TEMPLATE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/buy_template"
            app:destination="@id/handbook_detail">
            <argument
                android:name="noteId"
                app:argType="long" />
            <argument
                android:name="source"
                android:defaultValue="EDIT_TEMPLATE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/action_note_editor_to_decoupage_fragment3"
            app:destination="@id/decoupage_fragment" />

        <action
            android:id="@+id/action_note_editor_to_instant_alpha_fragment"
            app:destination="@id/instant_alpha_fragment" />

    </fragment>

    <fragment
        android:id="@+id/note_catalog"
        android:name="com.topstack.kilonotes.phone.note.PhoneNoteCatalogFragment"
        android:label="NoteCatalog"
        tools:layout="@layout/phone_fragment_catalog_page">

        <action
            android:id="@+id/edit"
            app:destination="@id/note_editor"
            app:popUpTo="@id/note_catalog"
            app:popUpToInclusive="true" />

        <action
            android:id="@+id/action_phone_note_catalog_to_vip_store"
            app:destination="@id/vip_store_new_edition">
            <argument
                android:name="source"
                android:defaultValue="EDIT_TEMPLATE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/buy_template"
            app:destination="@id/handbook_detail">
            <argument
                android:name="noteId"
                app:argType="long" />
            <argument
                android:name="source"
                android:defaultValue="EDIT_TEMPLATE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

    </fragment>

    <fragment
        android:id="@+id/note_folder"
        android:name="com.topstack.kilonotes.phone.note.PhoneCreateFolderFragment"
        android:label="FolderFragment"
        tools:layout="@layout/phone_create_folder">

        <argument
            android:name="source"
            android:defaultValue="HOME"
            app:argType="string" />

        <argument
            android:name="isHiddenSpaceModeAddNote"
            android:defaultValue="false"
            app:argType="boolean" />

        <action
            android:id="@+id/action_dialog_buy_membership_to_vip_store"
            app:destination="@id/vip_store">
            <argument
                android:name="source"
                android:defaultValue="H_WINDOW"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

    </fragment>

    <fragment
        android:id="@+id/folder_note_show"
        android:name="com.topstack.kilonotes.phone.note.PhoneFolderInsideFragment"
        android:label="FolderInsideFragment"
        tools:layout="@layout/phone_folder_inside">

        <action
            android:id="@+id/catalog"
            app:destination="@id/note_catalog" />

        <action
            android:id="@+id/edit_cover"
            app:destination="@+id/note_create">
            <argument
                android:name="editMode"
                android:defaultValue="true"
                app:argType="boolean" />
        </action>

        <action
            android:id="@+id/folder"
            app:destination="@id/note_folder">

            <argument
                android:name="source"
                android:defaultValue="HOME"
                app:argType="string" />

        </action>


    </fragment>

    <fragment
        android:id="@+id/page_thumbnail"
        android:name="com.topstack.kilonotes.phone.note.PageThumbnailFragment"
        android:label="PageThumbnail"
        tools:layout="@layout/fragment_page_thumbnail" />

    <dialog
        android:id="@+id/setting"
        android:name="com.topstack.kilonotes.phone.setting.PhoneSettingBottomSheet"
        android:label="SettingDialog"
        tools:layout="@layout/phone_setting_bottom_sheet_fragment">

        <action
            android:id="@+id/setting_backup"
            app:destination="@id/backup" />

    </dialog>

    <fragment
        android:id="@+id/backup"
        android:name="com.topstack.kilonotes.phone.note.PhoneBackupFragment"
        tools:layout="@layout/phone_fragment_backup" />

    <fragment
        android:id="@+id/vip_store"
        android:name="com.topstack.kilonotes.phone.vip.PhoneVipStoreFragment"
        android:label="VipStore"
        tools:layout="@layout/phone_fragment_vip_store">
        <argument
            android:name="source"
            android:defaultValue="H_ICON"
            app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        <action
            android:id="@+id/action_vip_store_to_vip_store_new"
            app:destination="@+id/vip_store_new_edition" />
        <action
            android:id="@+id/show_detail"
            app:destination="@id/handbook_detail">
            <argument
                android:name="noteId"
                app:argType="long" />
            <argument
                android:name="source"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/action_vip_store_to_note_list"
            app:destination="@id/note_list" />
    </fragment>

    <fragment
        android:id="@+id/handbook_detail"
        android:name="com.topstack.kilonotes.phone.vip.PhoneHandbookDetailFragment"
        android:label="PhoneHandbookDetail"
        tools:layout="@layout/fragment_handbook_detail">
        <argument
            android:name="noteId"
            app:argType="long" />
        <argument
            android:name="source"
            android:defaultValue="STORE"
            app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        <action
            android:id="@+id/action_handbook_detail_to_note_list"
            app:destination="@id/note_list" />
        <action
            android:id="@+id/action_handbook_detail_to_vip_store_new"
            app:destination="@+id/vip_store_new_edition" />
        <action
            android:id="@+id/action_handbook_detail_to_vip_store"
            app:destination="@id/vip_store">
            <argument
                android:name="source"
                android:defaultValue="STORE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
    </fragment>

    <fragment
        android:id="@+id/decoupage_fragment"
        android:name="com.topstack.kilonotes.phone.note.PhoneDecoupageFragment"
        android:label="PhoneDecoupageFragment" />

    <fragment
        android:id="@+id/instant_alpha_fragment"
        android:name="com.topstack.kilonotes.phone.note.PhoneInstantAlphaFragment"
        android:label="InstantAlphaFragment"
        tools:layout="@layout/phone_fragment_instant_alpha">

        <argument
            android:name="source"
            android:defaultValue="Keying icon"
            app:argType="string" />

        <argument
            android:name="image_uri"
            android:defaultValue="@null"
            app:argType="android.net.Uri"
            app:nullable="true" />

        <argument
            android:name="alpha"
            android:defaultValue="255"
            app:argType="integer" />


        <action
            android:id="@+id/action_instant_alpha_to_vip_store"
            app:destination="@id/vip_store">
            <argument
                android:name="source"
                android:defaultValue="INSTANT_ALPHA"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

    </fragment>

    <fragment
        android:id="@+id/hidden_space_notice_fragment"
        android:name="com.topstack.kilonotes.phone.note.PhoneHiddenSpaceNoticeFragment"
        android:label="HiddenSpace"
        tools:layout="@layout/phone_hidden_space_notice_fragment" />

    <action
        android:id="@+id/action_vip_exclusive_dialog_to_vip_store"
        app:destination="@id/vip_store_new_edition">
        <argument
            android:name="source"
            android:defaultValue="INSTANT_ALPHA"
            app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
    </action>

    <fragment
        android:id="@+id/backup_space_fragment"
        android:name="com.topstack.kilonotes.phone.backup.fragment.PhoneBackupSpaceFragment"
        android:label="BackupSpace"
        tools:layout="@layout/fragment_backup_space" />

    <fragment
        android:id="@+id/vip_store_new_edition"
        android:name="com.topstack.kilonotes.phone.vip.PhoneUserVipStoreFragment"
        android:label="VipStore"
        tools:layout="@layout/phone_vip_store_new_edition">
        <argument
            android:name="source"
            android:defaultValue="STORE"
            app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
    </fragment>

    <fragment
        android:id="@+id/unified_integral_dialog"
        android:name="com.topstack.kilonotes.base.ai.dialog.UnifiedIntegralDialog"
        android:label="UnifiedIntegralDialog"
        tools:layout="@layout/phone_dialog_ai_integral_unified">

        <action
            android:id="@+id/action_unified_integral_dialog_to_vip_store_new"
            app:destination="@+id/vip_store_new_edition" />

    </fragment>

</navigation>
<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:centerColor="@color/button_stroke_gradient_center"
                android:endColor="@color/button_stroke_gradient_end"
                android:startColor="@color/button_stroke_gradient_start" />
            <corners android:radius="@dimen/dp_52" />
        </shape>
    </item>

    <item
        android:bottom="@dimen/dp_3"
        android:left="@dimen/dp_3"
        android:right="@dimen/dp_3"
        android:top="@dimen/dp_3">
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="@dimen/dp_52" />
        </shape>
    </item>

</layer-list>
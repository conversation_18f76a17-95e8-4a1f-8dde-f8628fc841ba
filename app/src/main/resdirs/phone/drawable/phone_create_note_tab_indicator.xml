<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item
        android:bottom="@dimen/dp_2"
        android:end="@dimen/dp_2"
        android:start="@dimen/dp_2"
        android:gravity="center">
        <shape>
            <size android:width="@dimen/dp_80"
                android:height="@dimen/dp_8"/>
            <corners android:radius="@dimen/dp_5"/>
            <gradient
                android:centerColor="@color/cover_type_indicator_top_start_color"
                android:startColor="@color/cover_type_indicator_top_center_color"
                android:endColor="@color/cover_type_indicator_top_end_color" />
        </shape>
    </item>

</layer-list>
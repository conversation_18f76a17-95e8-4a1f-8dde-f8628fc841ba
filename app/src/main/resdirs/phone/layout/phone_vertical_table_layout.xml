<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/split_line"
        android:layout_width="@dimen/dp_2"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp_2"
        android:background="@color/note_tool_text_operation_background"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/custom_table_indicator"
        android:layout_width="@dimen/dp_260"
        android:layout_height="@dimen/dp_162"
        android:visibility="gone"
        android:src="@drawable/phone_material_custom_table_item_bg"
        android:paddingHorizontal="@dimen/dp_20"
        android:paddingVertical="@dimen/dp_15"
        app:layout_constraintStart_toStartOf="@id/custom_material_table"
        app:layout_constraintTop_toTopOf="@id/custom_material_table" />

    <View
        android:id="@+id/custom_table_indicator_end"
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_0"
        android:background="@drawable/phone_material_custom_table_indicator_bg"
        app:layout_constraintBottom_toBottomOf="@id/custom_table_indicator"
        app:layout_constraintEnd_toEndOf="@id/split_line"
        app:layout_constraintStart_toStartOf="@id/split_line"
        app:layout_constraintTop_toTopOf="@id/custom_table_indicator" />

    <ListView
        android:id="@+id/custom_material_table"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/dp_5"
        android:fadeScrollbars="false"
        android:scrollbars="none"
        android:divider="@color/transparent"
        android:listSelector="@color/transparent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
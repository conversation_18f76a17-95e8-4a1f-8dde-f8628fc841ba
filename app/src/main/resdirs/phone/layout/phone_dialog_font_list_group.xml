<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/font_group_name"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_120"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_48"
        android:text="No data"
        android:maxLines="2"
        android:ellipsize="end"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toStartOf="@id/font_group_state"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/font_group_state"
        android:layout_width="@dimen/dp_145"
        android:layout_height="@dimen/dp_120"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_77"
        android:paddingHorizontal="@dimen/dp_13"
        android:src="@drawable/font_down"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="@id/font_group_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/font_group_name" />

    <ImageView
        android:id="@+id/font_group_preView"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:scaleType="fitStart"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/font_group_name"
        app:layout_constraintEnd_toEndOf="@id/font_group_name"
        app:layout_constraintTop_toTopOf="@id/font_group_name"
        app:layout_constraintBottom_toBottomOf="@id/font_group_name" />

    <com.topstack.kilonotes.base.fonts.CirclePgBar
        android:id="@+id/font_group_progress"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp_69"
        android:visibility="gone"
        android:padding="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@id/font_group_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/font_group_name"/>
</androidx.constraintlayout.widget.ConstraintLayout>
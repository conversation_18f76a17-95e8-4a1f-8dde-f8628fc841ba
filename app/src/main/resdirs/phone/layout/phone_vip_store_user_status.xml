<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.lihang.ShadowLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            app:hl_cornerRadius="@dimen/dp_24"
            app:hl_shadowLimit="@dimen/dp_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/vip_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/jump_to_user_login_background"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/phone_vip_membership_entrance_background"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/purchase_slogan"
                    android:layout_width="@dimen/dp_570"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_38"
                    android:layout_marginTop="@dimen/dp_35"
                    android:maxLines="3"
                    android:shadowColor="@color/white"
                    android:shadowDx="0"
                    android:shadowDy="2"
                    android:shadowRadius="3"
                    android:text="@string/purchase_slogan_for_nonmember"
                    android:textColor="#FF260762"
                    android:textSize="@dimen/sp_54"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="@id/jump_to_user_login_background"
                    app:layout_constraintTop_toTopOf="@id/jump_to_user_login_background"
                    app:layout_constraintWidth_max="wrap" />

                <TextView
                    android:id="@+id/jump_to_user_login_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_70"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginBottom="@dimen/dp_29"
                    android:background="@drawable/button_confirm_background"
                    android:gravity="center"
                    android:minWidth="@dimen/dp_240"
                    android:paddingHorizontal="@dimen/dp_30"
                    android:text="@string/go_to_vip"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_36"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/purchase_slogan"
                    app:layout_constraintTop_toBottomOf="@+id/purchase_slogan" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.lihang.ShadowLayout>

        <ImageView
            android:id="@+id/vip_icon"
            android:layout_width="@dimen/dp_374"
            android:layout_height="@dimen/dp_286"
            android:layout_marginEnd="@dimen/dp_54"
            android:paddingBottom="@dimen/dp_40"
            android:scaleType="centerCrop"
            android:src="@drawable/phone_vip_gold_membership_emblem"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <com.topstack.kilonotes.base.component.view.CommonInputLayout
        android:id="@+id/modifyName"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_124"
        android:layout_margin="@dimen/dp_58"
        android:layout_marginBottom="@dimen/dp_58"
        app:layout_constraintBottom_toTopOf="@id/edit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginBottom="@dimen/dp_58"
        app:textColor="@color/text_primary"
        app:textSize="@dimen/sp_42" />

    <TextView
        android:id="@+id/edit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/dp_36"
        android:paddingBottom="@dimen/dp_47"
        android:text="@string/popup_window_edit"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_42"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/move" />

    <TextView
        android:id="@+id/move"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/dp_36"
        android:paddingBottom="@dimen/dp_47"
        android:text="@string/folder_remove_note"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_42"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/delete" />

    <TextView
        android:id="@+id/delete"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/dp_47"
        android:paddingBottom="@dimen/dp_94"
        android:text="@string/popup_window_delete"
        android:textColor="@color/page_overview_red"
        android:textSize="@dimen/sp_42"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/split_line" />

    <View
        android:id="@+id/split_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/bottom_sheet_split_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/cancel" />

    <TextView
        android:id="@+id/cancel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_140"
        android:gravity="center"
        android:paddingBottom="@dimen/dp_20"
        android:text="@string/cancel"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
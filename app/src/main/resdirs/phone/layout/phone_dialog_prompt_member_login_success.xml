<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_32dp_white">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_56"
        android:layout_marginHorizontal="@dimen/dp_36"
        android:text="@string/toast_login_success"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_35"
        android:text="@string/tip_member_login_success"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.lihang.ShadowLayout
        android:id="@+id/know_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:hl_cornerRadius="@dimen/dp_41"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/content"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/know"
            android:layout_width="@dimen/dp_320"
            android:layout_height="@dimen/dp_60"
            android:layout_gravity="center"
            android:background="@color/redeem_code_convert_success_duration_color"
            android:gravity="center"
            android:text="@string/storage_not_enough_confirm"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48" />
    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_58"
        android:text="@string/text_size"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tag_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_116"
        android:layout_marginHorizontal="@dimen/dp_80"
        android:layout_marginTop="@dimen/dp_78"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/vertical_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="@dimen/dp_152" />

    <TextView
        android:id="@+id/text_size_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_94"
        android:layout_marginBottom="@dimen/dp_90"
        android:textColor="@color/note_tool_text_size_text_color"
        android:textSize="@dimen/sp_40"
        app:layout_constraintEnd_toEndOf="@id/vertical_line"
        app:layout_constraintTop_toBottomOf="@id/tag_list" />

    <SeekBar
        android:id="@+id/text_size_seek_bar"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp_48"
        android:duplicateParentState="true"
        android:max="200"
        android:maxHeight="@dimen/dp_12"
        android:minHeight="@dimen/dp_12"
        android:progressDrawable="@drawable/phone_text_size_seek_bar_progress"
        android:splitTrack="false"
        android:thumb="@drawable/phone_text_size_seek_bar_thumb"
        app:layout_constraintBottom_toBottomOf="@id/text_size_text"
        app:layout_constraintEnd_toStartOf="@id/text_size_reduce"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/vertical_line"
        app:layout_constraintTop_toTopOf="@+id/text_size_text" />

    <ImageView
        android:id="@+id/text_size_reduce"
        android:layout_width="@dimen/dp_95"
        android:layout_height="0dp"
        android:scaleType="centerInside"
        android:src="@drawable/phone_note_tool_text_size_reduce_selector"
        app:layout_constraintBottom_toBottomOf="@id/text_size_text"
        app:layout_constraintDimensionRatio="348:330"
        app:layout_constraintEnd_toStartOf="@id/text_size_add"
        app:layout_constraintStart_toEndOf="@id/text_size_seek_bar"
        app:layout_constraintTop_toTopOf="@+id/text_size_text"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/text_size_add"
        android:layout_width="@dimen/dp_95"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_76"
        android:scaleType="centerInside"
        android:src="@drawable/phone_note_tool_text_size_add_selector"
        app:layout_constraintBottom_toBottomOf="@id/text_size_text"
        app:layout_constraintDimensionRatio="348:330"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/text_size_reduce"
        app:layout_constraintTop_toTopOf="@+id/text_size_text"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.lihang.ShadowLayout
        android:id="@+id/container_shadow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_25"
        app:hl_cornerRadius="@dimen/dp_24"
        app:hl_shadowColor="#19000000"
        app:hl_shadowLimit="@dimen/dp_20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/hidden_space_notice_tips_background"
            android:minHeight="@dimen/dp_200">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_60"
                android:layout_marginTop="@dimen/dp_30"
                android:ellipsize="end"
                android:maxWidth="@dimen/dp_513"
                android:maxLines="1"
                android:text="@string/hidden_space_notice_title"
                android:textColor="#FF2E82FF"
                android:textSize="@dimen/sp_48"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_60"
                android:layout_marginTop="@dimen/dp_15"
                android:layout_marginBottom="@dimen/dp_30"
                android:ellipsize="end"
                android:maxWidth="@dimen/dp_513"
                android:maxLines="2"
                android:text="@string/hidden_space_notice_tips_message"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title" />

            <ImageView
                android:id="@+id/close"
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_36"
                android:focusable="true"
                android:src="@drawable/phone_redeem_code_dialog_close"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.lihang.ShadowLayout>

    <ImageView
        android:id="@+id/figure"
        android:layout_width="@dimen/dp_183"
        android:layout_height="@dimen/dp_221"
        android:layout_marginEnd="@dimen/dp_165"
        android:layout_marginBottom="@dimen/dp_41"
        android:src="@drawable/phone_hidden_space_bottom_tips_figure"
        app:layout_constraintBottom_toBottomOf="@+id/container_shadow"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
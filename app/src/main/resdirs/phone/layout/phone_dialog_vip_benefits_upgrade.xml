<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/dp_15">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_container"
        android:layout_width="@dimen/dp_892"
        android:layout_height="@dimen/dp_1156"
        android:background="@drawable/phone_vip_benefits_upgrade_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_308"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@string/title_vip_benefits_upgrade"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_44"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_60"
            android:paddingHorizontal="@dimen/dp_94"
            android:text="@string/tips_vip_benefits_upgrade"
            android:gravity="center"
            android:textSize="@dimen/sp_36"
            android:lineSpacingExtra="@dimen/dp_5"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title" />

        <ImageView
            android:layout_width="@dimen/dp_682"
            android:layout_height="@dimen/dp_486"
            android:layout_marginVertical="@dimen/dp_61"
            android:src="@drawable/phone_vip_benefits_upgrade_server_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/description" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_105"
        android:layout_height="@dimen/dp_105"
        android:layout_marginTop="@dimen/dp_20"
        android:scaleType="centerInside"
        android:src="@drawable/phone_vip_benefits_upgrade_close_icon"
        app:layout_constraintTop_toBottomOf="@+id/dialog_container"
        app:layout_constraintStart_toStartOf="@+id/dialog_container"
        app:layout_constraintEnd_toEndOf="@+id/dialog_container" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <TextView
        android:id="@+id/tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/hidden_space_guide_tips"
        android:textSize="@dimen/sp_48"
        android:textColor="@color/white"
        android:gravity="center"
        android:maxLines="2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <ImageView
        android:id="@+id/arrow"
        android:layout_width="@dimen/dp_554"
        android:layout_height="@dimen/dp_344"
        android:src="@drawable/phone_hidden_space_guide_arrow"
        app:layout_constraintEnd_toEndOf="@id/tips"
        app:layout_constraintTop_toBottomOf="@id/tips"
        android:layout_marginTop="@dimen/dp_44"
        android:layout_marginEnd="@dimen/dp_100"/>


    <TextView
        android:id="@+id/know"
        android:layout_width="@dimen/dp_335"
        android:layout_height="@dimen/dp_134"
        android:background="@drawable/phone_hidden_space_guide_confirm_bg"
        android:layout_marginTop="@dimen/dp_138"
        android:layout_marginStart="@dimen/dp_222"
        android:text="@string/know"
        android:textSize="@dimen/sp_54"
        android:textColor="@color/bottom_sheet_black"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="@id/arrow"
        app:layout_constraintTop_toBottomOf="@id/arrow"/>
    
</androidx.constraintlayout.widget.ConstraintLayout>
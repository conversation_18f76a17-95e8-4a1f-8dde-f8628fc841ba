<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <com.topstack.kilonotes.base.component.view.CommonInputLayout
        android:id="@+id/modifyName"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_120"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginVertical="@dimen/dp_60"
        app:layout_constraintBottom_toTopOf="@id/delete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:textColor="@color/text_primary"
        app:textSize="@dimen/sp_42" />

    <TextView
        android:id="@+id/delete"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/dp_20"
        android:paddingBottom="@dimen/dp_80"
        android:text="@string/popup_window_delete"
        android:textColor="@color/page_overview_red"
        android:textSize="@dimen/sp_42"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/split_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/split_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/bottom_sheet_split_line"
        app:layout_constraintBottom_toTopOf="@id/cancel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/cancel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_140"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
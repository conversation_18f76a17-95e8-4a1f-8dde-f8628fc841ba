<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <com.topstack.kilonotes.pad.select.ScaleImageView
        android:id="@+id/big_pic"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        tools:ignore="ContentDescription" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_144"
        android:alpha="0.9"
        android:background="@color/phone_pick_photo_head_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_54"
            android:layout_height="@dimen/dp_54"
            android:layout_marginStart="@dimen/dp_48"
            android:src="@drawable/phone_select_photo_icon_back"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_24"
            android:gravity="center"
            android:text="@string/pick_photo"
            android:textColor="@color/note_tool_image_pick_tips_color"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/use_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_24"
            android:gravity="center"
            android:padding="@dimen/dp_14"
            android:text="@string/use"
            android:textColor="@color/hint_text"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
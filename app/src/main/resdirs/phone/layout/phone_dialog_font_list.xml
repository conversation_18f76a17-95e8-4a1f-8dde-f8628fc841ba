<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/phone_font_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto">

        <ImageView
            android:id="@+id/font_add"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginStart="@dimen/dp_48"
            android:background="@drawable/phone_note_font_add"
            android:scaleType="fitXY"
            app:layout_constraintTop_toTopOf="@id/font_list_title"
            app:layout_constraintBottom_toBottomOf="@id/font_list_title"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/font_list_title"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_178"
            android:gravity="center"
            android:textSize="@dimen/sp_48"
            android:text="@string/fonts_title"
            android:textColor="@color/text_primary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/font_add"
            app:layout_constraintEnd_toStartOf="@id/back" />
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/font_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/font_list_title"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginEnd="@dimen/dp_45"
            android:background="@drawable/phone_note_tool_icon_font_back"
            android:scaleType="fitXY"
            app:layout_constraintTop_toTopOf="@id/font_list_title"
            app:layout_constraintBottom_toBottomOf="@id/font_list_title"
            app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
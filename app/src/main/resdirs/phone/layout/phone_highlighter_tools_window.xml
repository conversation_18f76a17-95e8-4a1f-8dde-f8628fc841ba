<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">


    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:text="@string/highlighter_setting"
        android:textColor="@color/bottom_sheet_black"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_80"
        android:text="@string/straight_line_draw"
        android:textColor="@color/bottom_sheet_black"
        android:textSize="@dimen/sp_48"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <Switch
        android:id="@+id/phone_straight_line_switch"
        style="@style/PhoneSwitchStyle"
        android:layout_marginTop="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_48"
        android:checked="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />


</androidx.constraintlayout.widget.ConstraintLayout>
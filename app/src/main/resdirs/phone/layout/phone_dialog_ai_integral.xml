<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:padding="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_108"
        android:layout_marginEnd="@dimen/dp_54"
        android:src="@drawable/dialog_setting_general_icon_close"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/integral_background_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintHeight_percent="0.25"
        app:layout_constraintTop_toBottomOf="@id/close">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/integral_header_image"
            android:alpha="0.86" />

        <TextView
            android:id="@+id/my_ai_integral"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_42"
            android:layout_marginStart="@dimen/dp_61"
            android:text="@string/my_ai_integral"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_44"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/my_ai_integral_values"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0"
            android:textColor="@color/ai_tip_to_upgrade_background_color"
            android:textSize="@dimen/sp_79"
            android:includeFontPadding="false"
            tools:text="108000"
            app:layout_constraintStart_toStartOf="@id/my_ai_integral"
            app:layout_constraintTop_toBottomOf="@id/my_ai_integral"
            app:layout_constraintBottom_toTopOf="@id/recharge_container" />

        <com.lihang.ShadowLayout
            android:id="@+id/recharge_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_31"
            app:hl_cornerRadius="@dimen/dp_48"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/recharge"
                android:layout_width="@dimen/dp_540"
                android:layout_height="@dimen/dp_96"
                android:layout_gravity="center"
                android:background="@drawable/recharge_button_background"
                android:gravity="center"
                android:text="@string/go_recharge"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_44" />
        </com.lihang.ShadowLayout>

        <LinearLayout
            android:id="@+id/integral_details_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/my_ai_integral"
            app:layout_constraintBottom_toBottomOf="@id/my_ai_integral">

            <ImageView
                android:id="@+id/integral_details_image"
                android:layout_width="@dimen/dp_50"
                android:layout_height="@dimen/dp_50"
                android:layout_marginEnd="@dimen/dp_8"
                android:src="@drawable/integral_details_image"
                android:scaleType="fitXY"
                android:layout_gravity="center"
                app:layout_constraintEnd_toStartOf="@+id/integral_details"/>

            <TextView
                android:id="@+id/integral_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_61"
                android:text="@string/integral_details"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/sp_42"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="积分明细"/>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/integral_illustrate_list_container"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:paddingHorizontal="@dimen/dp_36"
        android:layout_marginTop="@dimen/dp_31"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/integral_background_container"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/integral_illustrate_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/integral_illustrate_title"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_44"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="积分说明" />

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_40"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/integral_illustrate_title">

            <TextView
                android:id="@+id/integral_illustrate"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_646"
                android:layout_marginTop="@dimen/dp_10"
                android:lineSpacingMultiplier="1.2"
                android:paddingVertical="@dimen/dp_10"
                android:scrollbars="vertical"
                android:text="@string/integral_illustrate"
                android:textColor="#FF909090"
                android:textSize="@dimen/sp_42" />

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
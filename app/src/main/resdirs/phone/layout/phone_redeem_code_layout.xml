<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_144"
        android:layout_marginTop="@dimen/dp_60"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/redeem_code"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_54"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_63"
        android:layout_marginEnd="@dimen/dp_43"
        android:padding="@dimen/dp_5"
        android:src="@drawable/phone_redeem_code_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/login_tips"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_90"
        android:ellipsize="end"
        android:maxLines="2"
        android:gravity="center"
        android:text="@string/redeem_code_please_login_to_convert"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <TextView
        android:id="@+id/login_btn"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_90"
        android:layout_marginTop="@dimen/dp_30"
        android:background="@drawable/button_confirm_background"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_36"
        android:paddingTop="@dimen/dp_27"
        android:paddingBottom="@dimen/dp_26"
        android:text="@string/login"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login_tips" />

    <com.topstack.kilonotes.base.component.view.FormatVerificationInputLayout
        android:id="@+id/input"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_120"
        android:layout_marginHorizontal="@dimen/dp_90"
        android:layout_marginTop="@dimen/dp_90"
        app:hint_text="@string/redeem_code_please_input_redeem_code_hint"
        app:layout_constraintTop_toBottomOf="@id/login_btn"
        app:text_color="@color/redeem_code_text_color"
        app:text_padding_end="@dimen/dp_110"
        app:text_padding_start="@dimen/dp_36"
        app:text_size="@dimen/sp_42"
        app:verifyType="number|capital_letter|small_letter"
        app:verify_length="12" />

    <TextView
        android:id="@id/confirm"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_90"
        android:layout_marginTop="@dimen/dp_48"
        android:layout_marginBottom="@dimen/dp_91"
        android:background="@drawable/redeem_code_confirm_background_selector"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_36"
        android:paddingTop="@dimen/dp_27"
        android:paddingBottom="@dimen/dp_26"
        android:text="@string/confirm"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/input" />


</androidx.constraintlayout.widget.ConstraintLayout>
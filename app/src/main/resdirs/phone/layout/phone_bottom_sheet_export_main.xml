<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_84"
        android:text="@string/export"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_58"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/pdf_type"
        android:layout_width="@dimen/dp_242"
        android:layout_height="@dimen/dp_292"
        android:layout_marginTop="@dimen/dp_84"
        android:scaleType="centerInside"
        android:src="@drawable/phone_note_export_icon_pdf"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/file_name_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_220"
        android:text="@string/export_file_name"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toStartOf="@id/file_name_content"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pdf_type" />

    <EditText
        android:id="@+id/file_name_content"
        android:layout_width="@dimen/dp_572"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_58"
        android:background="@null"
        android:ellipsize="end"
        android:importantForAutofill="no"
        android:inputType="textMultiLine"
        android:maxLines="2"
        android:minWidth="@dimen/dp_10"
        android:textAlignment="viewEnd"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/file_name_title"
        app:layout_constraintTop_toTopOf="@id/file_name_title"
        tools:ignore="LabelFor"
        tools:text="笔记名称" />

    <TextView
        android:id="@+id/page_range_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_126"

        android:text="@string/export_page_range"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/file_name_title" />

    <TextView
        android:id="@+id/page_range_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_28"
        android:textAlignment="viewEnd"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toStartOf="@id/page_range_forward"
        app:layout_constraintStart_toEndOf="@id/page_range_title"
        app:layout_constraintTop_toTopOf="@id/page_range_title"
        tools:text="4/5" />

    <ImageView
        android:id="@+id/page_range_forward"
        android:layout_width="@dimen/dp_22"
        android:layout_height="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_58"
        android:scaleType="centerInside"
        android:src="@drawable/phone_note_export_icon_forward"
        app:layout_constraintBottom_toBottomOf="@id/page_range_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/page_range_title"
        tools:ignore="ContentDescription" />

    <View
        android:id="@+id/page_range_touch_delegate"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/page_range_content"
        app:layout_constraintBottom_toBottomOf="@id/page_range_content"/>

    <TextView
        android:id="@+id/confirm_export"
        android:layout_width="@dimen/dp_706"
        android:layout_height="@dimen/dp_100"
        android:layout_marginTop="@dimen/dp_170"
        android:layout_marginBottom="@dimen/dp_68"
        android:background="@drawable/phone_export_confirm_background_selector"
        android:gravity="center"
        android:text="@string/export_confirm"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_46"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/page_range_title" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/base_bg_color"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/add_page_select"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/add_page"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_130"
            android:layout_marginBottom="@dimen/dp_88"
            android:breakStrategy="high_quality"
            android:gravity="center_horizontal"
            android:maxLines="2"
            android:text="@string/add_page_title"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@id/add_template"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/add_template"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_130"
            android:layout_marginTop="@dimen/dp_60"
            android:layout_marginBottom="@dimen/dp_88"
            android:breakStrategy="high_quality"
            android:gravity="center_horizontal"
            android:maxLines="2"
            android:text="@string/note_add_page_template_title"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/add_page"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/selected_flag"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_60"
            android:background="@drawable/phone_add_page_tab_layout_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/add_template"
            app:layout_constraintStart_toStartOf="@id/add_template" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.motion.widget.MotionLayout
        android:id="@+id/navigation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:background="@drawable/phone_add_page_button_group_shape"
        android:minHeight="@dimen/dp_110"
        app:layoutDescription="@xml/phone_bottom_sheet_add_page_scene"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/add_page_select">

        <View
            android:id="@+id/add_page_selected_flag"
            android:layout_width="@dimen/dp_230"
            android:layout_height="wrap_content"
            android:background="@drawable/add_page_button_background_shape"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/next"
            app:layout_constraintStart_toStartOf="@id/next"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/replace"
            android:layout_width="@dimen/dp_230"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:layout_weight="1"
            android:gravity="center"
            android:lineSpacingExtra="-5dp"
            android:minHeight="@dimen/dp_90"
            android:text="@string/add_page_replace"
            android:textColor="@color/bottom_sheet_black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/pre"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/pre"
            android:layout_width="@dimen/dp_230"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:layout_weight="1"
            android:gravity="center"
            android:minHeight="@dimen/dp_90"
            android:text="@string/add_page_previous"
            android:textColor="@color/bottom_sheet_black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/next"
            app:layout_constraintStart_toEndOf="@id/replace"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/next"
            android:layout_width="@dimen/dp_230"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:layout_weight="1"
            android:gravity="center"
            android:minHeight="@dimen/dp_90"
            android:text="@string/add_page_next"
            android:textColor="@color/bottom_sheet_black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/last"
            app:layout_constraintStart_toEndOf="@id/pre"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/last"
            android:layout_width="@dimen/dp_230"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_5"
            android:layout_weight="1"
            android:gravity="center"
            android:minHeight="@dimen/dp_90"
            android:text="@string/add_page_last"
            android:textColor="@color/bottom_sheet_black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/next"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.motion.widget.MotionLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/template_list_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/cancel"
        app:layout_constraintTop_toBottomOf="@id/navigation">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/template_classification_select_list"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_200"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent" />

        <com.liaoinstan.springview.widget.SpringView
            android:id="@+id/spring_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/template_classification_select_list">

            <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
                android:id="@+id/template_list_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingHorizontal="@dimen/dp_24"
                android:visibility="visible"
                app:scrollOrientation="vertical" />
        </com.liaoinstan.springview.widget.SpringView>

        <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
            android:id="@+id/template_other_list_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/template_classification_select_list"
            app:scrollOrientation="vertical" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/mine_empty_data_bg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/template_classification_select_list">

            <ImageView
                android:id="@+id/empty_data_view"
                android:layout_width="@dimen/dp_450"
                android:layout_height="@dimen/dp_450"
                android:layout_marginTop="@dimen/dp_298"
                android:scaleType="centerInside"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/empty_data_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_48"
                android:text="@string/template_mine_empty_tips"
                android:textColor="@color/phone_template_mine_empty_tips"
                android:textSize="@dimen/sp_36"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/empty_data_view" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/paper_list_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/cancel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/navigation">
        <!--  使用了链 spread_inside为固定前后，中间等分  -->
        <ImageView
            android:id="@+id/color_white"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginStart="@dimen/dp_148"
            android:layout_marginTop="@dimen/dp_60"
            android:background="@drawable/phone_add_page_white_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@id/color_yellow"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_yellow"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:background="@drawable/phone_add_page_yellow_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@id/color_black"
            app:layout_constraintStart_toEndOf="@id/color_white"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_black"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:background="@drawable/phone_add_page_black_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@id/color_green"
            app:layout_constraintStart_toEndOf="@id/color_yellow"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_green"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:background="@drawable/phone_add_page_green_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@+id/color_purple"
            app:layout_constraintStart_toEndOf="@id/color_black"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_purple"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:background="@drawable/phone_add_page_purple_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@id/color_blue"
            app:layout_constraintStart_toEndOf="@id/color_green"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_blue"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginEnd="@dimen/dp_148"
            android:background="@drawable/phone_add_page_blue_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/color_purple"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
            android:id="@+id/paper_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/color_white"
            app:scrollOrientation="vertical" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/split_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/phone_select_color_window_top_shadow"
        app:layout_constraintBottom_toTopOf="@id/cancel" />

    <TextView
        android:id="@+id/cancel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_141"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/bottom_sheet_black"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent" />

    <include
        android:id="@+id/template_download_dialog"
        layout="@layout/phone_include_template_download"
        android:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>
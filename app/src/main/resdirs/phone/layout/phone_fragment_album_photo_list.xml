<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_188"
        android:background="@color/phone_pick_photo_head_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/back_iv"
            android:layout_width="@dimen/dp_96"
            android:layout_height="@dimen/dp_96"
            android:layout_marginStart="@dimen/dp_26"
            android:padding="@dimen/dp_25"
            android:scaleType="fitXY"
            android:src="@drawable/phone_select_photo_icon_back"
            app:layout_constraintBottom_toBottomOf="@id/title_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title_tv"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_80"
            android:textColor="@color/note_tool_text_size_tag_pressed_background"
            android:textSize="@dimen/sp_42"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/photo_rv"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_50"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header" />
</androidx.constraintlayout.widget.ConstraintLayout>
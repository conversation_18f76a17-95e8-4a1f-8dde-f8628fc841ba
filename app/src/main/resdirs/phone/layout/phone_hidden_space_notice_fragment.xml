<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/phone_hidden_space_notice_fragment_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_140"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_36"
            android:ellipsize="end"
            android:maxWidth="@dimen/dp_800"
            android:maxLines="1"
            android:text="@string/hidden_space_notice_title"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginStart="@dimen/dp_48"
            android:padding="@dimen/dp_5"
            android:src="@drawable/note_tool_icon_export_back"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/scroller_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header">

        <com.topstack.kilonotes.base.component.view.overScrollRecyclerView.view.OverScrollNestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_54">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/message_container_usage"
                    style="@style/PhoneHiddenSpaceNoticeMessageContainerStyle"
                    android:layout_marginTop="@dimen/dp_30"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/message_title_usage"
                        style="@style/PhoneHiddenSpaceNoticeMessageTitleStyle"
                        android:text="@string/hidden_space_notice_usage_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/usage_indicator"
                        android:layout_width="@dimen/dp_60"
                        android:layout_height="@dimen/dp_9"
                        android:layout_marginTop="@dimen/dp_10"
                        android:background="@drawable/hidden_space_notice_title_indicator"
                        app:layout_constraintEnd_toEndOf="@id/message_title_usage"
                        app:layout_constraintStart_toStartOf="@id/message_title_usage"
                        app:layout_constraintTop_toBottomOf="@+id/message_title_usage" />

                    <View
                        android:id="@+id/tip_point_covert_content"
                        android:layout_width="@dimen/dp_12"
                        android:layout_height="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_60"
                        android:layout_marginTop="@dimen/dp_26"
                        android:background="@drawable/hidden_space_notice_tip_point"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/message_covert_content" />

                    <TextView
                        android:id="@+id/message_covert_content"
                        style="@style/PhoneHiddenSpaceNoticeMessageSignificantContentStyle"
                        android:layout_marginTop="@dimen/dp_48"
                        android:text="@string/hidden_space_notice_usage_covert_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tip_point_covert_content"
                        app:layout_constraintTop_toBottomOf="@id/usage_indicator" />

                    <View
                        android:id="@+id/tip_point_piece_of_life"
                        android:layout_width="@dimen/dp_12"
                        android:layout_height="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_60"
                        android:layout_marginTop="@dimen/dp_26"
                        android:background="@drawable/hidden_space_notice_tip_point"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/message_piece_of_life" />

                    <TextView
                        android:id="@+id/message_piece_of_life"
                        style="@style/PhoneHiddenSpaceNoticeMessageSignificantContentStyle"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/hidden_space_notice_usage_piece_of_life"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tip_point_piece_of_life"
                        app:layout_constraintTop_toBottomOf="@id/message_covert_content" />

                    <View
                        android:id="@+id/tip_point_emotion"
                        android:layout_width="@dimen/dp_12"
                        android:layout_height="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_60"
                        android:layout_marginTop="@dimen/dp_26"
                        android:background="@drawable/hidden_space_notice_tip_point"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/message_emotion" />

                    <TextView
                        android:id="@+id/message_emotion"
                        style="@style/PhoneHiddenSpaceNoticeMessageSignificantContentStyle"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/hidden_space_notice_usage_emotion"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tip_point_emotion"
                        app:layout_constraintTop_toBottomOf="@id/message_piece_of_life" />

                    <TextView
                        style="@style/PhoneHiddenSpaceNoticeMessageContentStyle"
                        android:layout_marginTop="@dimen/dp_12"
                        android:text="@string/hidden_space_notice_usage_overview"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/message_emotion" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/message_container_security"
                    style="@style/PhoneHiddenSpaceNoticeMessageContainerStyle"
                    android:layout_marginTop="@dimen/dp_30"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/message_container_usage">

                    <TextView
                        android:id="@+id/hidden_space_notice_security"
                        style="@style/PhoneHiddenSpaceNoticeMessageTitleStyle"
                        android:text="@string/hidden_space_notice_security_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/security_indicator"
                        android:layout_width="@dimen/dp_60"
                        android:layout_height="@dimen/dp_9"
                        android:layout_marginTop="@dimen/dp_10"
                        android:background="@drawable/hidden_space_notice_title_indicator"
                        app:layout_constraintEnd_toEndOf="@id/hidden_space_notice_security"
                        app:layout_constraintStart_toStartOf="@id/hidden_space_notice_security"
                        app:layout_constraintTop_toBottomOf="@+id/hidden_space_notice_security" />

                    <View
                        android:id="@+id/tip_show_in_hidden"
                        android:layout_width="@dimen/dp_12"
                        android:layout_height="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_60"
                        android:layout_marginTop="@dimen/dp_26"
                        android:background="@drawable/hidden_space_notice_tip_point"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/message_show_in_hidden" />

                    <TextView
                        android:id="@+id/message_show_in_hidden"
                        style="@style/PhoneHiddenSpaceNoticeMessageSignificantContentStyle"
                        android:layout_marginTop="@dimen/dp_48"
                        android:text="@string/hidden_space_notice_security_hidden"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tip_show_in_hidden"
                        app:layout_constraintTop_toBottomOf="@id/security_indicator" />

                    <View
                        android:id="@+id/tip_enough_safe"
                        android:layout_width="@dimen/dp_12"
                        android:layout_height="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_60"
                        android:layout_marginTop="@dimen/dp_26"
                        android:background="@drawable/hidden_space_notice_tip_point"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/message_enough_safe" />

                    <TextView
                        android:id="@+id/message_enough_safe"
                        style="@style/PhoneHiddenSpaceNoticeMessageSignificantContentStyle"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/hidden_space_notice_security_safety"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tip_enough_safe"
                        app:layout_constraintTop_toBottomOf="@id/message_show_in_hidden" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/message_container_data_backup"
                    style="@style/PhoneHiddenSpaceNoticeMessageContainerStyle"
                    android:layout_marginTop="@dimen/dp_30"
                    android:layout_marginBottom="@dimen/dp_50"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/message_container_security">

                    <TextView
                        android:id="@+id/hidden_space_notice_data"
                        style="@style/PhoneHiddenSpaceNoticeMessageTitleStyle"
                        android:text="@string/hidden_space_notice_data_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/data_indicator"
                        android:layout_width="@dimen/dp_60"
                        android:layout_height="@dimen/dp_9"
                        android:layout_marginTop="@dimen/dp_10"
                        android:background="@drawable/hidden_space_notice_title_indicator"
                        app:layout_constraintEnd_toEndOf="@id/hidden_space_notice_data"
                        app:layout_constraintStart_toStartOf="@id/hidden_space_notice_data"
                        app:layout_constraintTop_toBottomOf="@+id/hidden_space_notice_data" />

                    <TextView
                        android:id="@+id/message_kind_tips"
                        style="@style/PhoneHiddenSpaceNoticeMessageContentStyle"
                        android:layout_marginTop="@dimen/dp_16"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/data_indicator" />

                    <ImageView
                        android:id="@+id/guide_image"
                        android:layout_width="@dimen/dp_0"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_48"
                        android:layout_marginTop="@dimen/dp_12"
                        android:layout_marginBottom="@dimen/dp_60"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/message_kind_tips" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.topstack.kilonotes.base.component.view.overScrollRecyclerView.view.OverScrollNestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
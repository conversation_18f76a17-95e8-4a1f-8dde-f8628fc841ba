<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/dp_160"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:background="@drawable/note_cover_bg"
        android:elevation="@dimen/dp_5"
        android:scaleType="fitEnd"
        app:layout_constraintBottom_toTopOf="@id/type"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_max="@dimen/dp_223"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/cover_foreground"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:background="@drawable/note_cover_highlight_foreground"
        android:elevation="@dimen/dp_6"
        app:layout_constraintBottom_toBottomOf="@id/image"
        app:layout_constraintEnd_toEndOf="@id/image"
        app:layout_constraintHorizontal_bias="0.0739"
        app:layout_constraintStart_toStartOf="@id/image"
        app:layout_constraintTop_toTopOf="@id/image"
        app:layout_constraintWidth_percent="0.3184"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_11"
        android:textSize="@dimen/sp_24"
        android:textColor="@color/select_cover_paper_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image" />

</androidx.constraintlayout.widget.ConstraintLayout>
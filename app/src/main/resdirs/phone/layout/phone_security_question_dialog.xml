<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/security_question_dialog_background_color">

    <LinearLayout
        android:id="@+id/trumpet_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_120"
        android:background="@color/security_question_trumpet_tips_bg_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/security_title_bar">

        <ImageView
            android:id="@+id/trumpet_icon"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dp_48"
            android:src="@drawable/phone_security_question_trumpet" />

        <TextView
            android:id="@+id/trumpet_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_24"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:text="@string/security_question_trumpet_tips"
            android:textColor="@color/security_question_trumpet_tips_text_color"
            android:textSize="@dimen/sp_42" />

    </LinearLayout>

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/question_and_answer_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/confirm_container"
        app:layout_constraintTop_toBottomOf="@+id/trumpet_layout"
        app:scrollOrientation="vertical" />

    <com.lihang.ShadowLayout
        android:id="@+id/confirm_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/dp_39"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_shadowColor="@color/floating_jump_to_pay_shadow_color"
        app:hl_shadowLimit="@dimen/dp_11"
        app:hl_shadowOffsetY="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/vip_pay_button_bg"
            android:duplicateParentState="true"
            android:gravity="center"
            android:minWidth="@dimen/dp_580"
            android:paddingHorizontal="@dimen/dp_36"
            android:paddingVertical="@dimen/dp_27"
            android:text="@string/confirm"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold" />
    </com.lihang.ShadowLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/security_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_170"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_54"
            android:layout_height="@dimen/dp_54"
            android:layout_marginStart="@dimen/dp_48"
            android:src="@drawable/phone_backup_back"
            app:layout_constraintBottom_toBottomOf="@+id/security_question_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/security_question_title" />

        <TextView
            android:id="@+id/security_question_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_112"
            android:layout_marginBottom="@dimen/dp_66"
            android:gravity="center"
            android:maxLines="2"
            android:text="@string/security_set_question_title"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
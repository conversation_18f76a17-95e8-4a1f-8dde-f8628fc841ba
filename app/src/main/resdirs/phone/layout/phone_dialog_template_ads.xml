<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_48">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_36"
        android:layout_marginBottom="@dimen/dp_36"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:text="@string/buy_vip_unlock_template"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_36"
        android:minWidth="@dimen/dp_706"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ad_background_view"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_100"
            android:background="@drawable/phone_note_template_ad_button_background"
            android:minWidth="@dimen/dp_706"
            android:paddingHorizontal="@dimen/dp_36"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_36"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/ad_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_24"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="2"
                    android:minHeight="@dimen/dp_100"
                    android:text="@string/template_ads_btn"
                    android:textColor="@color/text_primary"
                    android:textSize="@dimen/sp_42"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ad_icon"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/ad_icon"
                    android:layout_width="@dimen/dp_48"
                    android:layout_height="@dimen/dp_48"
                    android:layout_marginEnd="@dimen/dp_24"
                    android:src="@drawable/sticker_or_template_icon_ads"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/vip"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_100"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_25"
            android:background="@drawable/phone_note_template_confirm_background"
            android:gravity="center"
            android:minWidth="@dimen/dp_706"
            android:text="@string/buy_vip"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_46"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/ad_background_view"
            app:layout_constraintStart_toStartOf="@id/ad_background_view"
            app:layout_constraintTop_toBottomOf="@id/ad_background_view" />

        <TextView
            android:id="@+id/vip2"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_100"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_25"
            android:background="@drawable/phone_note_template_confirm_background"
            android:gravity="center"
            android:minWidth="@dimen/dp_706"
            android:text="@string/buy_vip"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_46"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_36"
        android:minWidth="@dimen/dp_706"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/only_ad_background_view"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_100"
            android:background="@drawable/phone_note_template_confirm_background"
            android:minWidth="@dimen/dp_706"
            android:paddingHorizontal="@dimen/dp_36"
            android:layout_marginBottom="@dimen/dp_25"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_36"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/only_ad_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_24"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="2"
                    android:minHeight="@dimen/dp_100"
                    android:text="@string/template_ads_btn"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_42"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/only_ad_icon"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/only_ad_icon"
                    android:layout_width="@dimen/dp_48"
                    android:layout_height="@dimen/dp_48"
                    android:layout_marginEnd="@dimen/dp_24"
                    android:src="@drawable/sticker_or_template_ad_tag"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
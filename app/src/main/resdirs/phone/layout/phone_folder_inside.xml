<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/phone_folder_inside_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_170"
        android:background="@drawable/phone_folder_inside_top_bar_background"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_66"
            android:ellipsize="middle"
            android:maxWidth="@dimen/dp_780"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="@string/folder_create" />

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_59"
            android:layout_height="@dimen/dp_59"
            android:layout_marginStart="@dimen/dp_45"
            android:padding="@dimen/dp_5"
            android:src="@drawable/phone_backup_back"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/add_note"
            android:layout_width="@dimen/dp_59"
            android:layout_height="@dimen/dp_59"
            android:layout_marginEnd="@dimen/dp_48"
            android:padding="@dimen/dp_5"
            android:src="@drawable/phone_add_note_to_folder_icon"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/noteRv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_33"
        android:layout_marginEnd="@dimen/dp_33"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header"
        app:scrollOrientation="vertical" />

    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/loading"
        style="?android:attr/progressBarStyle"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:layout_gravity="center"
        android:progressDrawable="@color/black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/noteRv"
        app:layout_constraintEnd_toEndOf="@id/noteRv"
        app:layout_constraintStart_toStartOf="@id/noteRv"
        app:layout_constraintTop_toTopOf="@id/noteRv" />

    <ImageView
        android:id="@+id/empty_img"
        android:layout_width="@dimen/dp_480"
        android:layout_height="@dimen/dp_480"
        android:scaleType="centerInside"
        android:src="@drawable/note_backup_empty_background"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/noteRv"
        app:layout_constraintEnd_toEndOf="@id/noteRv"
        app:layout_constraintStart_toStartOf="@id/noteRv"
        app:layout_constraintTop_toTopOf="@id/noteRv"
        app:layout_constraintVertical_bias="0.4"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/empty_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_36"
        android:text="@string/folder_empty_tips"
        android:textColor="@color/backup_subtitle_color"
        android:textSize="@dimen/sp_36"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@id/empty_img"
        app:layout_constraintStart_toStartOf="@id/empty_img"
        app:layout_constraintTop_toBottomOf="@id/empty_img" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="empty_img,empty_tips" />


</androidx.constraintlayout.widget.ConstraintLayout>
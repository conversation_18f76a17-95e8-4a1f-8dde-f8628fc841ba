<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_204">

    <ImageView
        android:id="@+id/checkbox"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:src="@drawable/phone_backup_checkbox"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <com.lihang.ShadowLayout
        android:id="@+id/cover_corner_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        app:hl_cornerRadius="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/checkbox"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/cover"
            android:layout_width="@dimen/dp_150"
            android:layout_height="@dimen/dp_204"
            android:background="@color/base_bg_color"
            android:scaleType="centerInside"
            tools:ignore="ContentDescription"
            tools:src="@drawable/cover1" />
    </com.lihang.ShadowLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_43"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/cover_corner_layout"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="title" />

    <TextView
        android:id="@+id/time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/backup_item_time"
        android:textSize="@dimen/sp_36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/title"
        tools:text="time" />

</androidx.constraintlayout.widget.ConstraintLayout>
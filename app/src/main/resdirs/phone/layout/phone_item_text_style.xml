<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_166">

    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/dp_10"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toTopOf="@id/name"
        app:layout_constraintEnd_toEndOf="@id/name"
        app:layout_constraintStart_toStartOf="@id/name"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/sign_for_first_time"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:background="@drawable/sign_red_bg"
        android:visibility="invisible"
        app:layout_constraintStart_toEndOf="@id/image"
        app:layout_constraintTop_toTopOf="@id/image" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="top|center_horizontal"
        android:maxLines="2"
        android:minWidth="@dimen/dp_120"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/image" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/material_image"
        android:layout_width="@dimen/dp_228"
        android:layout_height="@dimen/dp_228"
        android:layout_gravity="center"
        tools:ignore="ContentDescription" />

    <com.topstack.kilonotes.base.component.view.CircleProgressView
        android:id="@+id/download_progress"
        android:layout_width="@dimen/dp_96"
        android:layout_height="@dimen/dp_96"
        android:layout_gravity="center"
        app:circle_stroke="@dimen/dp_3"
        app:progress_color="@color/white" />

</FrameLayout>
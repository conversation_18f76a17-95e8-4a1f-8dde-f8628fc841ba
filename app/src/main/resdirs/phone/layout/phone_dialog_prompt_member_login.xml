<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_32dp_white">

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/dp_453"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_36"
        android:layout_marginHorizontal="@dimen/dp_36"
        android:text="@string/title_member_login"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_15"
        android:layout_marginHorizontal="@dimen/dp_36"
        android:text="@string/tip_member_login"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 添加分隔线 -->
    <View
        android:id="@+id/divider_horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/thumbnail_list_right_stroke"
        app:layout_constraintTop_toBottomOf="@+id/content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dp_35" />

    <!-- 竖线居中 -->
    <View
        android:id="@+id/divider_vertical"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:background="@color/thumbnail_list_right_stroke"
        app:layout_constraintTop_toBottomOf="@+id/divider_horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <!-- 左侧按钮 -->
    <TextView
        android:id="@+id/cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/cancel"
        android:textColor="@color/no_feedback_color"
        android:textSize="@dimen/sp_46"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/divider_vertical"
        app:layout_constraintTop_toBottomOf="@+id/divider_horizontal"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- 右侧按钮 -->
    <TextView
        android:id="@+id/go_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/go_login"
        android:textColor="@color/crop_view_line"
        android:textSize="@dimen/sp_46"
        app:layout_constraintStart_toEndOf="@+id/divider_vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider_horizontal"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

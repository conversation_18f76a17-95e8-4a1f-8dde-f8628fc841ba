<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/dp_200"
    tools:background="@color/black_70">

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginEnd="@dimen/dp_60"
        android:layout_marginBottom="@dimen/dp_20"
        android:padding="@dimen/dp_18"
        android:src="@drawable/phone_dialog_buy_membership_icon_close"
        app:layout_constraintBottom_toTopOf="@id/image"
        app:layout_constraintEnd_toEndOf="@id/image"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_80"
        app:layout_constraintBottom_toTopOf="@id/confirm"
        app:layout_constraintDimensionRatio="h,1604:1028"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="ContentDescription"
        tools:src="@drawable/dialog_buy_membership_image_zh" />

    <TextView
        android:id="@+id/confirm"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:background="@drawable/phone_dialog_buy_membership_confirm_button_background"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:minWidth="@dimen/dp_423"
        android:minHeight="@dimen/dp_90"
        android:paddingHorizontal="@dimen/dp_36"
        android:text="@string/go_to_store_to_buy"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_42"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/image"
        app:layout_constraintStart_toStartOf="@id/image"
        app:layout_constraintTop_toBottomOf="@id/image"
        app:layout_constraintWidth_max="wrap" />

</androidx.constraintlayout.widget.ConstraintLayout>
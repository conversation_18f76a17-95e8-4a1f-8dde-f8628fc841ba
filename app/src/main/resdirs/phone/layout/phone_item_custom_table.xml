<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_162">


    <ImageView
        android:id="@+id/custom_table_indicator"
        android:layout_width="@dimen/dp_260"
        android:layout_height="@dimen/dp_162"
        android:src="@drawable/phone_material_custom_table_item_bg"
        android:paddingHorizontal="@dimen/dp_20"
        android:paddingVertical="@dimen/dp_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/category_delete"
        android:layout_width="@dimen/dp_76"
        android:layout_height="@dimen/dp_76"
        android:paddingHorizontal="@dimen/dp_20"
        android:paddingBottom="@dimen/dp_40"
        android:src="@drawable/phone_custom_material_category_delete_icon"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <com.topstack.kilonotes.base.component.view.EllipsizedTextView
        android:id="@+id/tab_name"
        android:layout_width="@dimen/dp_180"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_40"
        android:textSize="@dimen/sp_45"
        android:gravity="center"
        android:ellipsize="middle"
        android:maxLines="2"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_0"
        android:background="@drawable/phone_material_custom_table_indicator_bg"
        app:layout_constraintBottom_toBottomOf="@id/custom_table_indicator"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/custom_table_indicator" />

</androidx.constraintlayout.widget.ConstraintLayout>
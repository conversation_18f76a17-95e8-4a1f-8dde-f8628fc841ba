<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/pay_type_icon"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginStart="@dimen/dp_36"
        android:src="@drawable/cover4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/pay_type_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_42"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintBottom_toBottomOf="@id/pay_type_icon"
        app:layout_constraintStart_toEndOf="@id/pay_type_icon"
        app:layout_constraintTop_toTopOf="@id/pay_type_icon"
        tools:text="微信支付" />

    <ImageView
        android:id="@+id/selector"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginEnd="@dimen/dp_36"
        android:background="@drawable/vip_store_choose_type_type_checkbox_bg"
        android:duplicateParentState="true"
        android:padding="@dimen/dp_14"
        android:src="@drawable/pad_login_dialog_checkbox_image_src"
        app:layout_constraintBottom_toBottomOf="@id/pay_type_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/pay_type_icon" />

</androidx.constraintlayout.widget.ConstraintLayout>
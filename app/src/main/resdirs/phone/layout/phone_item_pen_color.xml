<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_gravity="center"
        android:scaleType="centerInside"
        android:layout_marginBottom="@dimen/dp_10"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/name"
        app:layout_constraintStart_toStartOf="@id/name"
        app:layout_constraintEnd_toEndOf="@id/name"/>
    <TextView
        android:id="@+id/name"
        android:layout_width="@dimen/dp_120"
        android:layout_height="wrap_content"
        android:gravity="top|center_horizontal"
        android:textSize="@dimen/sp_30"
        android:textColor="@color/text_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>

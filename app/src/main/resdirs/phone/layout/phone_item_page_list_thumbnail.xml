<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_288"
    android:layout_height="@dimen/dp_470">

    <ImageView
        android:id="@+id/thumbnail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_41"
        android:background="@drawable/page_list_thumbnail"
        android:padding="@dimen/dp_2"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toTopOf="@id/number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/more"
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_54"
        android:layout_marginEnd="@dimen/dp_4"
        android:padding="@dimen/dp_9"
        android:scaleType="fitCenter"
        android:src="@drawable/phone_page_list_thumbnail_more"
        app:layout_constraintBottom_toBottomOf="@id/thumbnail"
        app:layout_constraintEnd_toEndOf="@id/thumbnail"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="@dimen/dp_7"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="12" />

</androidx.constraintlayout.widget.ConstraintLayout>
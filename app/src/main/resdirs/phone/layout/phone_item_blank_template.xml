<?xml version="1.0" encoding="utf-8"?>
<com.lihang.ShadowLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/dp_24"
    android:layout_marginBottom="@dimen/dp_60"
    app:hl_cornerRadius="@dimen/dp_24">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/dp_468"
        android:layout_height="@dimen/dp_627">

        <ImageView
            android:id="@+id/template_icon"
            android:layout_width="@dimen/dp_146"
            android:layout_height="@dimen/dp_146"
            android:src="@drawable/phone_icon_blank_template"
            app:layout_constraintVertical_bias="0.42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/add_page_template_create_blank_template"
            android:textColor="@color/add_page_template_create_blank_template_text_color"
            android:textSize="@dimen/sp_42"
            android:layout_marginTop="@dimen/dp_89"
            android:layout_marginHorizontal="@dimen/dp_30"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/template_icon"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.lihang.ShadowLayout>


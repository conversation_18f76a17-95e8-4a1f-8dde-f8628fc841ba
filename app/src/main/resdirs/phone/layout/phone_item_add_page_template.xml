<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_60">

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_35"
        android:layout_marginEnd="@dimen/dp_35"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toStartOf="@id/buy_template"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="小粉日记2022年手账本" />

    <TextView
        android:id="@+id/buy_template"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_70"
        android:layout_marginEnd="@dimen/dp_36"
        android:background="@drawable/phone_add_page_template_buy_bg"
        android:gravity="center"
        android:minWidth="@dimen/dp_232"
        android:paddingHorizontal="@dimen/dp_24"
        android:text="@string/note_add_page_buy_template"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_36"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/title" />

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/template_list_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_41"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:scrollOrientation="horizontal" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/dp_30">

    <com.topstack.kilonotes.phone.component.view.VerticalTableLayout
        android:id="@+id/custom_table"
        android:layout_width="@dimen/dp_263"
        android:layout_height="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_120"
        app:layout_constraintBottom_toTopOf="@id/paper_cut_tool"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/paper_cut_empty_img"
        android:layout_width="@dimen/dp_405"
        android:layout_height="@dimen/dp_294"
        android:layout_marginBottom="@dimen/dp_130"
        android:scaleType="centerInside"
        android:src="@drawable/phone_paper_cut_empty_tips"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/custom_table"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/paper_cut_empty_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_29"
        android:text="@string/paper_cut_tool_tips"
        android:textColor="@color/backup_subtitle_color"
        android:textSize="@dimen/sp_30"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/custom_table"
        app:layout_constraintTop_toBottomOf="@id/paper_cut_empty_img" />

    <ImageView
        android:id="@+id/paper_cut_edit_btn"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_36"
        android:src="@drawable/phone_paper_cut_edit_button"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/material_list"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:scrollOrientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/custom_table"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/paper_cut_tool" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/paper_cut_tool"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_154"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/bottom_sheet_split_line"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/move"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/phone_paper_cut_move_button"
            android:layout_marginStart="@dimen/dp_48"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/tips"
            android:layout_width="@dimen/dp_660"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="center"
            android:textSize="@dimen/sp_48"
            android:textColor="@color/text_primary"
            android:text="@string/paper_cut_unselected_tips"/>

        <ImageView
            android:id="@+id/delete"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/phone_paper_cut_del_button"
            android:layout_marginEnd="@dimen/dp_48"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/guide_page_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_guide_page_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/handbook_reference_position"
        android:layout_width="@dimen/dp_680"
        android:layout_height="@dimen/dp_1100"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/guide_skip"
        android:layout_width="@dimen/dp_152"
        android:layout_height="@dimen/dp_72"
        android:layout_marginEnd="@dimen/dp_60"
        android:layout_marginBottom="@dimen/dp_200"
        android:background="@drawable/guide_skip_bg"
        android:gravity="center"
        android:padding="@dimen/dp_10"
        android:text="@string/guide_skip"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_36"
        app:layout_constraintBottom_toTopOf="@+id/handbook_reference_position"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.topstack.kilonotes.base.guide.DotIndicator
        android:id="@+id/guide_dot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_70"
        android:fitsSystemWindows="true"
        app:dotInterval="@dimen/dp_22"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:radius="@dimen/dp_10"
        app:selectedColor="@color/dot_indicator_selected"
        app:unselectedColor="@color/dot_indicator_unselected" />

</androidx.constraintlayout.widget.ConstraintLayout>

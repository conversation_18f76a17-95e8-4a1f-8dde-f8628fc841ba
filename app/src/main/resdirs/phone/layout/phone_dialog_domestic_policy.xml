<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_round_corner_bg">

    <TextView
        android:id="@+id/info_collect_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_96"
        android:text="@string/info_collect_title"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_54"
        android:layout_height="@dimen/dp_54"
        android:layout_marginStart="@dimen/dp_48"
        android:src="@drawable/decoupage_preview_back"
        app:layout_constraintBottom_toBottomOf="@+id/info_collect_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/info_collect_title" />

    <LinearLayout
        android:id="@+id/select_period"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_86"
        android:divider="@drawable/phone_period_divider_spacing"
        android:orientation="horizontal"
        android:showDividers="middle"
        app:layout_constraintTop_toBottomOf="@+id/info_collect_title">

        <TextView
            android:id="@+id/seven_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/phone_period_selector_bg"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_8"
            android:text="@string/recently_seven_day"
            android:textColor="@color/domestic_period_color_selector"
            android:textSize="@dimen/sp_32" />

        <TextView
            android:id="@+id/thirty_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@drawable/phone_period_selector_bg"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_8"
            android:text="@string/recently_thirty_day"
            android:textColor="@color/domestic_period_color_selector"
            android:textSize="@dimen/sp_32" />

        <TextView
            android:id="@+id/one_year"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/phone_period_selector_bg"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_8"
            android:text="@string/recently_one_year"
            android:textColor="@color/domestic_period_color_selector"
            android:textSize="@dimen/sp_32" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/detailed_info_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_48"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/select_period" />

    <LinearLayout
        android:id="@+id/info_collect_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_80"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/info_collect_title">

        <TextView
            android:id="@+id/info_collect_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@string/info_collect_tips"
            android:textColor="@color/graph_type_text_color"
            android:textSize="@dimen/sp_32" />

        <TextView
            android:id="@+id/info_collect_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_60"
            android:text="@string/info_collect_subtitle"
            android:textColor="@color/graph_type_text_color"
            android:textSize="@dimen/sp_32" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/user_personal_info_constrain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_53">

            <TextView
                android:id="@+id/user_personal_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/user_personal_info_collect"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/user_personal_info_enter"
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_48"
                android:src="@drawable/phone_domestic_policy_next_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/use_procedure_info_constrain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_53">

            <TextView
                android:id="@+id/use_procedure_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/use_procedure_info_collect"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/use_procedure_info_enter"
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_48"
                android:src="@drawable/phone_domestic_policy_next_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/device_info_constrain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_53">

            <TextView
                android:id="@+id/device_info_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/device_info_collect"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/device_info_enter"
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_48"
                android:src="@drawable/phone_domestic_policy_next_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/app_info_constrain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_53">

            <TextView
                android:id="@+id/app_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/app_info_collect"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/app_info_enter"
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_48"
                android:src="@drawable/phone_domestic_policy_next_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </LinearLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/collect_info_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="info_collect_layout" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/detailed_info_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="detailed_info_list,select_period" />

</androidx.constraintlayout.widget.ConstraintLayout>
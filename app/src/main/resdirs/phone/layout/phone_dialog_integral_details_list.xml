<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 返回按钮 -->
    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:padding="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_108"
        android:layout_marginStart="@dimen/dp_31"
        android:src="@drawable/dialog_login_icon_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:padding="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_54"
        android:src="@drawable/dialog_login_icon_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/back"
        app:layout_constraintBottom_toBottomOf="@+id/back"
        tools:ignore="ContentDescription" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/integral_details_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/integral_details"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_44"
        android:visibility="visible"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/back"
        app:layout_constraintBottom_toBottomOf="@+id/back" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/integral_details_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_42"
        app:layout_constraintTop_toBottomOf="@+id/integral_details_title"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/empty_icon"
        android:layout_width="@dimen/dp_379"
        android:layout_height="@dimen/dp_379"
        android:src="@drawable/note_icon_backup_space_empty_list"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="@+id/integral_details_list"
        app:layout_constraintEnd_toEndOf="@+id/integral_details_list"
        app:layout_constraintBottom_toBottomOf="@+id/integral_details_list"
        app:layout_constraintTop_toTopOf="@+id/integral_details_list"/>

</androidx.constraintlayout.widget.ConstraintLayout>
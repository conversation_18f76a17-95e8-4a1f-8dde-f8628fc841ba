<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.lihang.ShadowLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_24"
        app:hl_shadowLimit="@dimen/dp_24">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/cover"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="h,1024:576"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/cover4" />

            <View
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_100"
                android:background="@drawable/vip_store_handbook_cover_gradient"
                app:layout_constraintBottom_toBottomOf="@id/cover"
                app:layout_constraintEnd_toEndOf="@id/cover"
                app:layout_constraintStart_toStartOf="@id/cover" />

            <HorizontalScrollView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/dp_15"
                app:layout_constraintBottom_toBottomOf="@id/cover"
                app:layout_constraintEnd_toEndOf="@id/cover"
                app:layout_constraintStart_toStartOf="@id/cover">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/handbook_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:background="@drawable/phone_vip_store_handbook_cover_tag1_bg"
                        android:paddingHorizontal="@dimen/dp_18"
                        android:paddingVertical="@dimen/dp_6"
                        android:text="@string/handbook"
                        android:textColor="@color/vip_store_handbook_cover_tag1_text_color"
                        android:textSize="@dimen/sp_30" />

                    <TextView
                        android:id="@+id/sticker_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:background="@drawable/phone_vip_store_handbook_cover_tag2_bg"
                        android:paddingHorizontal="@dimen/dp_18"
                        android:paddingVertical="@dimen/dp_6"
                        android:text="@string/sticker"
                        android:textColor="@color/vip_store_handbook_cover_tag2_text_color"
                        android:textSize="@dimen/sp_30"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/template_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:background="@drawable/phone_vip_store_handbook_cover_tag3_bg"
                        android:paddingHorizontal="@dimen/dp_18"
                        android:paddingVertical="@dimen/dp_6"
                        android:text="@string/template_name"
                        android:textColor="@color/vip_store_handbook_cover_tag3_text_color"
                        android:textSize="@dimen/sp_30"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

            </HorizontalScrollView>


            <ImageView
                android:id="@+id/tag_new"
                android:layout_width="@dimen/dp_84"
                android:layout_height="@dimen/dp_84"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/cover"
                app:layout_constraintTop_toTopOf="@id/cover" />

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_48"
                android:layout_marginEnd="@dimen/dp_48"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/vip_store_handbook_cover_title_text_color"
                android:textSize="@dimen/sp_48"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@id/confirm"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cover"
                tools:text="下午时光手账本下午时光手账本" />

            <TextView
                android:id="@+id/page_num"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_18"
                android:layout_marginBottom="@dimen/dp_48"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/phone_vip_store_handbook_cover_title_text_color"
                android:textSize="@dimen/sp_33"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/title"
                app:layout_constraintStart_toStartOf="@id/title"
                app:layout_constraintTop_toBottomOf="@id/title"
                tools:text="共包含10个页面模版" />

            <com.topstack.kilonotes.base.component.view.ProgressButton
                android:id="@+id/confirm"
                android:layout_width="@dimen/dp_210"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_63"
                android:layout_marginEnd="@dimen/dp_30"
                android:gravity="center"
                android:padding="@dimen/dp_12"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_42"
                app:cornerRadius="@dimen/dp_41"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cover"
                app:max_progress="100"
                app:min_progress="0"
                app:progressBackColor="@color/progress_button_second_background"
                app:progressColor="@color/progress_bar"
                tools:text="下载中..." />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.lihang.ShadowLayout>

</FrameLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipToPadding="false"
    android:paddingHorizontal="@dimen/dp_38"
    android:paddingVertical="@dimen/dp_44">

    <ImageView
        android:id="@+id/note_name_icon"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:src="@drawable/phone_note_tool_icon_note_name"
        app:layout_constraintBottom_toTopOf="@id/note_export_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/note_name_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_28"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintBottom_toBottomOf="@id/note_name_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/note_name_icon"
        app:layout_constraintTop_toTopOf="@id/note_name_icon"
        tools:text="笔记" />

    <com.topstack.kilonotes.base.component.view.CommonInputLayout
        android:id="@+id/input"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_66"
        android:layout_marginStart="@dimen/dp_28"
        app:textColor="@color/text_primary"
        app:textSize="@dimen/sp_40"
        app:textAlignment="textStart"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/note_name_icon"
        app:layout_constraintBottom_toBottomOf="@id/note_name_icon"
        app:layout_constraintTop_toTopOf="@id/note_name_icon"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/note_export_icon"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:layout_marginTop="@dimen/dp_64"
        android:src="@drawable/phone_note_tool_icon_export"
        app:layout_constraintEnd_toEndOf="@id/note_name_icon"
        app:layout_constraintStart_toStartOf="@id/note_name_icon"
        app:layout_constraintTop_toBottomOf="@id/note_name_icon"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/note_export"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_28"
        android:text="@string/export"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintBottom_toBottomOf="@id/note_export_icon"
        app:layout_constraintEnd_toStartOf="@id/note_export_forward"
        app:layout_constraintStart_toEndOf="@id/note_export_icon"
        app:layout_constraintTop_toTopOf="@id/note_export_icon" />

    <ImageView
        android:id="@+id/note_export_forward"
        android:layout_width="@dimen/dp_22"
        android:layout_height="@dimen/dp_36"
        android:src="@drawable/phone_note_tool_icon_next_menu"
        app:layout_constraintBottom_toBottomOf="@id/note_export_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/note_export_icon"
        tools:ignore="ContentDescription" />

    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/note_share_delegate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="note_share_img_icon,note_share_img_line"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/note_share_img_icon"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:src="@drawable/phone_note_editor_share_img"
        app:layout_constraintBottom_toBottomOf="@id/note_share_img_line"
        app:layout_constraintEnd_toEndOf="@id/note_read_mode_icon"
        app:layout_constraintStart_toStartOf="@id/note_read_mode_icon"
        app:layout_constraintTop_toTopOf="@id/note_share_img_line"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/note_share_img_line"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_78"
        android:layout_marginEnd="@dimen/dp_20"
        android:text="@string/share"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintEnd_toStartOf="@id/note_guide_line_switch"
        app:layout_constraintStart_toStartOf="@id/note_read_mode"
        app:layout_constraintTop_toBottomOf="@id/note_export" />

    <View
        android:id="@+id/note_export_touch_delegate"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="@id/note_export_forward"
        app:layout_constraintEnd_toEndOf="@id/note_export_forward"
        app:layout_constraintStart_toStartOf="@id/note_export_icon"
        app:layout_constraintTop_toTopOf="@id/note_export_icon" />

    <ImageView
        android:id="@+id/note_read_mode_icon"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:src="@drawable/phone_note_tool_icon_read_mode"
        app:layout_constraintBottom_toBottomOf="@id/note_read_mode"
        app:layout_constraintEnd_toEndOf="@id/note_export_icon"
        app:layout_constraintStart_toStartOf="@id/note_export_icon"
        app:layout_constraintTop_toTopOf="@id/note_read_mode"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/note_read_mode"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_78"
        android:layout_marginEnd="@dimen/dp_20"
        android:text="@string/read_mode"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintEnd_toStartOf="@id/note_read_mode_switch"
        app:layout_constraintStart_toStartOf="@id/note_export"
        app:layout_constraintTop_toBottomOf="@id/note_share_img_line" />


    <Switch
        android:id="@+id/note_read_mode_switch"
        style="@style/PhoneSwitchStyle"
        app:layout_constraintBottom_toBottomOf="@+id/note_read_mode"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/note_read_mode"
        tools:ignore="UseSwitchCompatOrMaterialXml" />


    <ImageView
        android:id="@+id/note_guide_line_icon"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:src="@drawable/phone_note_tool_guide_line"
        app:layout_constraintBottom_toBottomOf="@id/note_guide_line"
        app:layout_constraintEnd_toEndOf="@id/note_read_mode_icon"
        app:layout_constraintStart_toStartOf="@id/note_read_mode_icon"
        app:layout_constraintTop_toTopOf="@id/note_guide_line"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/note_guide_line"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_78"
        android:layout_marginEnd="@dimen/dp_20"
        android:text="@string/guide_line"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintEnd_toStartOf="@id/note_guide_line_switch"
        app:layout_constraintStart_toStartOf="@id/note_read_mode"
        app:layout_constraintTop_toBottomOf="@id/note_read_mode" />


    <Switch
        android:id="@+id/note_guide_line_switch"
        style="@style/PhoneSwitchStyle"
        app:layout_constraintBottom_toBottomOf="@+id/note_guide_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/note_guide_line"
        tools:ignore="UseSwitchCompatOrMaterialXml" />

    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/note_document_move_delegate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="note_document_move_icon,note_document_move_line"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/note_document_move_icon"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:src="@drawable/phone_note_editor_document_move_icon"
        app:layout_constraintBottom_toBottomOf="@id/note_document_move_line"
        app:layout_constraintEnd_toEndOf="@id/note_read_mode_icon"
        app:layout_constraintStart_toStartOf="@id/note_read_mode_icon"
        app:layout_constraintTop_toTopOf="@id/note_document_move_line"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/note_document_move_line"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_78"
        android:layout_marginEnd="@dimen/dp_20"
        android:text="@string/folder_remove"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintEnd_toStartOf="@id/note_guide_line_switch"
        app:layout_constraintStart_toStartOf="@id/note_read_mode"
        app:layout_constraintTop_toBottomOf="@id/note_guide_line" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/detailed_info_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/info_collect_subtitle"
        android:textColor="@color/graph_type_text_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/detailed_info_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_30"
        android:text="@string/detailed_info_content"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintTop_toBottomOf="@+id/detailed_info_title" />

    <TextView
        android:id="@+id/content_describe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/graph_type_text_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintBottom_toBottomOf="@+id/detailed_info_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/detailed_info_content"
        tools:text="场景" />

    <TextView
        android:id="@+id/detailed_info_goal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_53"
        android:text="@string/detailed_info_goal"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintTop_toBottomOf="@+id/detailed_info_content" />

    <TextView
        android:id="@+id/goal_describe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/graph_type_text_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintBottom_toBottomOf="@+id/detailed_info_goal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/detailed_info_goal"
        tools:text="目的" />

    <TextView
        android:id="@+id/detailed_info_scene"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_53"
        android:text="@string/detailed_info_scene"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintTop_toBottomOf="@+id/detailed_info_goal" />

    <TextView
        android:id="@+id/scene_describe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/graph_type_text_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintBottom_toBottomOf="@+id/detailed_info_scene"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/detailed_info_scene"
        tools:text="场景" />

    <TextView
        android:id="@+id/collect_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_53"
        android:text="@string/collect_status"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintTop_toBottomOf="@+id/detailed_info_scene" />

    <TextView
        android:id="@+id/collect_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/not_collect"
        android:textColor="@color/graph_type_text_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintBottom_toBottomOf="@+id/collect_status"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/collect_status" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.lihang.ShadowLayout
        android:id="@+id/container_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_38"
        app:hl_layoutBackground="@color/transparent"
        app:hl_shadowColor="@color/black_10"
        app:hl_shadowLimit="@dimen/dp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container"
            android:layout_width="@dimen/dp_417"
            android:layout_height="@dimen/dp_558"
            android:background="@color/white">

            <ImageView
                android:id="@+id/button"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/note_icon_create_note"
                app:layout_constraintBottom_toTopOf="@id/text"
                app:layout_constraintDimensionRatio="h,1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintWidth_percent="0.25"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_26"
                android:text="@string/note_create_note"
                android:textColor="#FF2E82FF"
                android:textSize="@dimen/sp_32"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/button" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

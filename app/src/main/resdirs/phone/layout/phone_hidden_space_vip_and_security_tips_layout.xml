<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.lihang.ShadowLayout
        android:id="@+id/container_shadow"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:background="@color/transparent"
        app:hl_cornerRadius="@dimen/dp_24"
        app:hl_shadowLimit="@dimen/dp_20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/dp_30">

            <TextView
                android:id="@+id/hidden_space_vip_tips_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_36"
                android:layout_marginTop="@dimen/dp_30"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/hidden_space_vip_tips_title"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_42"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/ask_later"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_60"
                android:background="@drawable/phone_hidden_space_top_tips_ask_later_btn_background"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp_52"
                android:paddingVertical="@dimen/dp_20"
                android:text="@string/ask_later"
                android:textColor="@color/skip_text"
                android:textSize="@dimen/sp_42"
                app:layout_constraintEnd_toStartOf="@id/buy_vip"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/hidden_space_vip_tips_title"
                tools:layout_width="@dimen/dp_272" />

            <TextView
                android:id="@+id/buy_vip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_60"
                android:background="@drawable/phone_hidden_space_top_tips_buy_vip_btn_background"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp_52"
                android:paddingVertical="@dimen/dp_20"
                android:text="@string/buy_vip"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_42"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ask_later"
                app:layout_constraintTop_toBottomOf="@id/hidden_space_vip_tips_title"
                tools:layout_width="@dimen/dp_272" />

            <TextView
                android:id="@+id/go_to_set_security_question_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_36"
                android:layout_marginTop="@dimen/dp_30"
                android:layout_marginEnd="@dimen/dp_94"
                android:ellipsize="end"
                android:gravity="center"
                android:maxWidth="@dimen/dp_840"
                android:maxLines="2"
                android:text="@string/hidden_space_set_security_question_tips"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/go_to_set_security_question_icon"
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_48"
                android:layout_marginStart="@dimen/dp_10"
                android:src="@drawable/phone_hidden_space_security_question_icon"
                app:layout_constraintBottom_toBottomOf="@id/go_to_set_security_question_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/go_to_set_security_question_text"
                app:layout_constraintTop_toTopOf="@id/go_to_set_security_question_text" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
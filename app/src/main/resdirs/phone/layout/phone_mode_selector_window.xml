<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">
    <View
        android:id="@+id/type_switch"
        android:layout_width="@dimen/dp_120"
        android:layout_height="@dimen/dp_120"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <com.lihang.ShadowLayout
        android:id="@+id/image_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_shadowLimit="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/type_image"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_120"
            android:padding="@dimen/dp_24"
            android:background="@drawable/phone_select_mode_selector_background"
            android:src="@drawable/phone_select_icon_mode_image" />
    </com.lihang.ShadowLayout>

    <com.lihang.ShadowLayout
        android:id="@+id/lasso_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_shadowLimit="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/type_lasso"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_120"
            android:padding="@dimen/dp_24"
            android:background="@drawable/phone_select_mode_selector_background"
            android:src="@drawable/phone_select_icon_mode_lasso" />
    </com.lihang.ShadowLayout>

    <com.lihang.ShadowLayout
        android:id="@+id/eraser_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_shadowLimit="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/type_eraser"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_120"
            android:padding="@dimen/dp_24"
            android:background="@drawable/phone_select_mode_selector_background"
            android:src="@drawable/phone_select_icon_mode_eraser" />
    </com.lihang.ShadowLayout>

    <com.lihang.ShadowLayout
        android:id="@+id/highlighter_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_shadowLimit="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/type_highlighter"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_120"
            android:padding="@dimen/dp_24"
            android:background="@drawable/phone_select_mode_selector_background"
            android:src="@drawable/phone_select_icon_mode_hightlighter" />
    </com.lihang.ShadowLayout>

    <com.lihang.ShadowLayout
        android:id="@+id/draw_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_shadowLimit="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/type_draw"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_120"
            android:padding="@dimen/dp_24"
            android:background="@drawable/phone_select_mode_selector_background"
            android:src="@drawable/phone_select_icon_mode_draw" />
    </com.lihang.ShadowLayout>

    <com.lihang.ShadowLayout
        android:id="@+id/text_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_shadowLimit="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/type_text"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_120"
            android:padding="@dimen/dp_24"
            android:background="@drawable/phone_select_mode_selector_background"
            android:src="@drawable/phone_select_icon_mode_text" />
    </com.lihang.ShadowLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
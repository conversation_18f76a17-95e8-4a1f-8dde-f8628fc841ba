<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/checkbox"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:src="@drawable/phone_folder_move_checkbox"
        android:layout_marginStart="@dimen/dp_48"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/note_folder_layout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_240"
        android:layout_marginStart="@dimen/dp_46"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/checkbox"
        tools:ignore="MissingConstraints">


        <com.topstack.kilonotes.base.component.view.InterceptClickRecyclerView
            android:id="@+id/note_folder"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_240"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:background="@drawable/phone_folder_cover"
            android:overScrollMode="never"
            android:paddingHorizontal="@dimen/dp_20"
            android:paddingTop="@dimen/dp_47"
            android:paddingBottom="@dimen/dp_20" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/dp_648"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:ellipsize="middle"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_42"
        android:layout_marginStart="@dimen/dp_50"
        app:layout_constraintTop_toTopOf="@id/note_folder_layout"
        app:layout_constraintBottom_toBottomOf="@id/note_folder_layout"
        app:layout_constraintStart_toEndOf="@id/note_folder_layout"
        tools:text="文件夹1"/>



</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:layout_height="@dimen/dp_500"
    tools:layout_weight="@dimen/dp_1080">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_58"
        android:text="@string/pen_size"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/size_selector"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/select_pen_size"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintVertical_bias="0.456" />

    <TextView
        android:id="@+id/current_text_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_92"
        android:layout_marginBottom="@dimen/dp_86"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="0.5mm" />

    <com.topstack.kilonotes.base.component.view.PenSizeSeekBar
        android:id="@+id/select_pen_size"
        android:layout_width="@dimen/dp_706"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_92"
        app:layout_constraintBottom_toBottomOf="@id/current_text_size"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/current_text_size"
        app:max_progress="4"
        app:min_progress="0.1"
        app:progress="0.1"
        app:progress_background_radio="@dimen/dp_16"
        app:progress_tracker_radius="@dimen/dp_29" />


</androidx.constraintlayout.widget.ConstraintLayout>
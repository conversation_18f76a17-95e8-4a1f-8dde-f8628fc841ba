<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/item_photo"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_180"
        android:scaleType="centerCrop"
        tools:ignore="ContentDescription"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <TextView
        android:id="@+id/photo_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/item_photo"
        app:layout_constraintBottom_toBottomOf="@id/item_photo"
        app:layout_constraintStart_toEndOf="@id/item_photo"
        android:layout_marginStart="@dimen/dp_48"
        android:maxLines="1"
        android:textColor="@color/album_name_text"
        android:textSize="@dimen/sp_48"
        tools:text="相册"/>

    <TextView
        android:id="@+id/photo_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/photo_name"
        app:layout_constraintStart_toEndOf="@id/photo_name"
        android:textColor="@color/album_name_text"
        android:textSize="@dimen/sp_48"
        tools:text="(80)"/>

    <ImageView
        android:id="@+id/album_selected_icon"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:src="@drawable/phone_album_selected"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="@dimen/dp_48"/>


</androidx.constraintlayout.widget.ConstraintLayout>
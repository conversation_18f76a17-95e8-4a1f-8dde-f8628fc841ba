<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_250"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/dp_111"
        android:layout_height="@dimen/dp_111"
        android:src="@drawable/vip_store_vip_benefits_ai_integral_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textSize="@dimen/sp_35"
        android:textColor="#FF723D20"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/image"
        tools:text="AI积分"/>

    <ImageView
        android:id="@+id/gold_icon"
        android:layout_width="@dimen/dp_41"
        android:layout_height="@dimen/dp_41"
        android:layout_marginEnd="@dimen/dp_14"
        app:layout_constraintEnd_toStartOf="@+id/description"
        app:layout_constraintTop_toBottomOf="@+id/title"
        tools:src="@drawable/vip_store_gold_icon"/>

    <TextView
        android:id="@+id/description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_30"
        android:textColor="#FFFD6C00"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/gold_icon"
        app:layout_constraintBottom_toBottomOf="@+id/gold_icon"
        tools:text="10000/月"/>

</androidx.constraintlayout.widget.ConstraintLayout>
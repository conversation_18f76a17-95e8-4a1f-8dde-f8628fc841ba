<?xml version="1.0" encoding="utf-8"?>
<com.topstack.kilonotes.base.component.view.BottomDraggableLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:contentViewId="@id/content"
    app:dragViewId="@id/drag_view"
    app:invariableTopViewId="@id/title_content"
    app:variableViewId="@id/material_list">

    <View
        android:id="@+id/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/drag_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/shadow"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_20"
            android:background="@drawable/phone_select_color_window_top_shadow"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:background="@drawable/phone_note_material_drag_view_shape"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/shadow" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:background="@color/white"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/drag">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/title_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/dp_20"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/cancel"
                android:layout_width="@dimen/dp_72"
                android:layout_height="@dimen/dp_72"
                android:layout_marginStart="@dimen/dp_54"
                android:layout_marginTop="@dimen/dp_8"
                android:src="@drawable/phone_note_material_tool_dialog_close"
                app:layout_constraintEnd_toStartOf="@id/title"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="@dimen/dp_450"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_horizontal"
                android:maxLines="2"
                android:text="@string/paper_cut_material_tool"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/sp_51"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/cancel" />


        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/material_list"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.topstack.kilonotes.base.component.view.BottomDraggableLayout>


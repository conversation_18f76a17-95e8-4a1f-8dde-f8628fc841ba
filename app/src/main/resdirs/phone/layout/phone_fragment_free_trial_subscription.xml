<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:scrollbars="none">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/vip_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_204"
            android:text="@string/free_trial_subscription_vip_title"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_100"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/vip_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/vip_close"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_169"
            android:layout_marginEnd="@dimen/dp_30"
            android:src="@drawable/dialog_setting_general_icon_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/vip_title"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/user_benefit_list"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1010"
            android:layout_marginStart="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_90"
            android:layout_marginEnd="@dimen/dp_60"
            app:layout_constraintTop_toBottomOf="@id/vip_title" />

        <TextView
            android:id="@+id/free_trial_subscription_explain"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_36"
            android:gravity="center"
            android:text="@string/vip_duration_loading"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@+id/floating_jump_to_pay_container"
            app:layout_constraintStart_toStartOf="@+id/floating_jump_to_pay_container"
            app:layout_constraintTop_toBottomOf="@+id/user_benefit_list"
            tools:text="7天后免费试用\n之后￥35.00每年" />

        <com.lihang.ShadowLayout
            android:id="@+id/floating_jump_to_pay_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp_48"
            app:hl_cornerRadius="@dimen/dp_100"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/free_trial_subscription_explain">

            <TextView
                android:id="@+id/floating_jump_to_pay"
                android:layout_width="@dimen/dp_870"
                android:layout_height="@dimen/dp_180"
                android:layout_gravity="center"
                android:background="@drawable/vip_pay_button_bg"
                android:duplicateParentState="true"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_60"
                android:text="@string/vip_store_pay_button_default_text"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_54"
                android:textStyle="bold" />
        </com.lihang.ShadowLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/user_choose_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dp_60"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/floating_jump_to_pay_container">

            <TextView
                android:id="@+id/keep_using"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_90"
                android:gravity="center"
                android:maxWidth="@dimen/dp_380"
                android:paddingVertical="@dimen/dp_48"
                android:text="@string/keep_using_limited_edition"
                android:textColor="@color/vip_store_vip_sync_vip_tip_text"
                android:textSize="@dimen/sp_36" />

            <TextView
                android:id="@+id/restore_subscription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:maxWidth="@dimen/dp_380"
                android:paddingVertical="@dimen/dp_48"
                android:text="@string/vip_store_restore_purchase_need_underline"
                android:textColor="@color/vip_store_vip_sync_vip_tip_text"
                android:textSize="@dimen/sp_36" />

        </androidx.appcompat.widget.LinearLayoutCompat>

        <TextView
            android:id="@+id/policy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="4"
            android:paddingHorizontal="@dimen/dp_100"
            android:paddingBottom="@dimen/dp_30"
            android:scrollbars="vertical"
            android:text="@string/vip_store_google_subs_read_and_agree_text"
            android:textColor="@color/backup_subtitle_color"
            android:textSize="@dimen/sp_30"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/user_choose_container" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>

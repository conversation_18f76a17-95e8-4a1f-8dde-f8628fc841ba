<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_92"
        android:padding="@dimen/dp_10"
        android:text="@string/page_overview_add"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/copy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_10"
        android:text="@string/page_overview_copy"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_42"
        android:layout_marginTop="@dimen/dp_92"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/add" />

    <TextView
        android:id="@+id/paste"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_10"
        android:text="@string/page_overview_paste"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_42"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dp_92"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/copy"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/clear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_10"
        android:text="@string/page_overview_clear"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_42"
        android:layout_marginTop="@dimen/dp_92"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/paste" />

    <TextView
        android:id="@+id/delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_92"
        android:padding="@dimen/dp_10"
        android:text="@string/page_overview_delete"
        android:textColor="@color/page_overview_red"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clear" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginTop="@dimen/dp_92"
        android:background="@color/page_border"
        app:layout_constraintTop_toBottomOf="@id/delete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_43"
        android:layout_marginBottom="@dimen/dp_43"
        android:padding="@dimen/dp_10"
        android:text="@string/page_overview_cancel"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_42"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider" />
</androidx.constraintlayout.widget.ConstraintLayout>

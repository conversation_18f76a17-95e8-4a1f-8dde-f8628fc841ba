<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_296"
    android:layout_height="@dimen/dp_464">

    <ImageView
        android:id="@+id/thumbnail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/page_list_thumbnail"
        android:padding="@dimen/dp_2"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toTopOf="@id/number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/selected"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_20"
        android:visibility="gone"
        android:scaleType="fitCenter"
        android:src="@drawable/phone_icon_note_editor_img_selected"
        app:layout_constraintBottom_toBottomOf="@id/thumbnail"
        app:layout_constraintEnd_toEndOf="@id/thumbnail"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="@dimen/dp_7"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="12" />

</androidx.constraintlayout.widget.ConstraintLayout>
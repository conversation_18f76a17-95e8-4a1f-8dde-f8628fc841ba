<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_1350">

    <ImageView
        android:id="@+id/cancel"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_68"
        android:layout_marginEnd="@dimen/dp_48"
        android:src="@drawable/phone_redeem_code_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/logo"
        android:layout_width="@dimen/dp_246"
        android:layout_height="@dimen/dp_246"
        android:layout_marginTop="@dimen/dp_171"
        android:scaleType="fitXY"
        android:src="@drawable/splash_icon_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_66"
        android:text="@string/app_name"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_64"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/logo" />

    <TextView
        android:id="@+id/version_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_36"
        android:gravity="center"
        android:textColor="@color/text_disable"
        android:textSize="@dimen/sp_34"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_name"
        tools:text="1.9.0" />

    <TextView
        android:id="@+id/user_agreement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_66"
        android:gravity="center"
        android:text="@string/about_user_agreement"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/version_name" />

    <TextView
        android:id="@+id/privacy_policy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_90"
        android:gravity="center"
        android:text="@string/about_privacy_policy"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/user_agreement" />

    <TextView
        android:id="@+id/userExperience"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_90"
        android:gravity="center"
        android:text="@string/about_user_experience"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/privacy_policy" />

    <TextView
        android:id="@+id/hidden_space"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_90"
        android:gravity="center"
        android:text="@string/hidden_space_title"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/userExperience" />

</androidx.constraintlayout.widget.ConstraintLayout>
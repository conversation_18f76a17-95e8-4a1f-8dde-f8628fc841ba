<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_height="@dimen/dp_1005">

    <!--  使用了链 spread_inside为固定前后，中间等分  -->

    <ImageView
        android:id="@+id/color_white"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginStart="@dimen/dp_148"
        android:layout_marginTop="@dimen/dp_60"
        android:background="@drawable/phone_add_page_white_background_selector"
        android:padding="@dimen/dp_18"
        android:src="@drawable/phone_add_page_color_selector"
        app:layout_constraintEnd_toStartOf="@id/color_yellow"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/color_yellow"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:background="@drawable/phone_add_page_yellow_background_selector"
        android:padding="@dimen/dp_18"
        android:src="@drawable/phone_add_page_color_selector"
        app:layout_constraintEnd_toStartOf="@id/color_black"
        app:layout_constraintStart_toEndOf="@id/color_white"
        app:layout_constraintTop_toTopOf="@id/color_white"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/color_black"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:background="@drawable/phone_add_page_black_background_selector"
        android:padding="@dimen/dp_18"
        android:src="@drawable/phone_add_page_color_selector"
        app:layout_constraintEnd_toStartOf="@id/color_green"
        app:layout_constraintStart_toEndOf="@id/color_yellow"
        app:layout_constraintTop_toTopOf="@id/color_white"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/color_green"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:background="@drawable/phone_add_page_green_background_selector"
        android:padding="@dimen/dp_18"
        android:src="@drawable/phone_add_page_color_selector"
        app:layout_constraintEnd_toStartOf="@+id/color_purple"
        app:layout_constraintStart_toEndOf="@id/color_black"
        app:layout_constraintTop_toTopOf="@id/color_white"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/color_purple"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:background="@drawable/phone_add_page_purple_background_selector"
        android:padding="@dimen/dp_18"
        android:src="@drawable/phone_add_page_color_selector"
        app:layout_constraintEnd_toStartOf="@id/color_blue"
        app:layout_constraintStart_toEndOf="@id/color_green"
        app:layout_constraintTop_toTopOf="@id/color_white"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/color_blue"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginEnd="@dimen/dp_148"
        android:background="@drawable/phone_add_page_blue_background_selector"
        android:padding="@dimen/dp_18"
        android:src="@drawable/phone_add_page_color_selector"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/color_purple"
        app:layout_constraintTop_toTopOf="@id/color_white"
        tools:ignore="ContentDescription" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/page_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_60"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/color_white" />


</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_156"
    android:layout_height="@dimen/dp_220">

    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/dp_156"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:background="@drawable/phone_create_note_cover_list_item_bg"
        android:elevation="@dimen/dp_3"
        android:scaleType="fitEnd"
        app:layout_constraintHeight_max="@dimen/dp_220"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="@dimen/dp_3"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/foreground"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:background="@drawable/note_cover_highlight_foreground"
        android:elevation="@dimen/dp_3"
        app:layout_constraintBottom_toBottomOf="@id/image"
        app:layout_constraintEnd_toEndOf="@id/image"
        app:layout_constraintHorizontal_bias="0.0739"
        app:layout_constraintStart_toStartOf="@id/image"
        app:layout_constraintTop_toTopOf="@id/image"
        app:layout_constraintWidth_percent="0.3184"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/selected"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_11"
        android:layout_marginBottom="@dimen/dp_11"
        android:elevation="@dimen/dp_3"
        android:src="@drawable/phone_create_note_icon_item_select"
        app:layout_constraintBottom_toBottomOf="@id/image"
        app:layout_constraintEnd_toEndOf="@id/image"
        tools:ignore="ContentDescription"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/delete"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:src="@drawable/decoupage_delete_image_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dp_11"
        android:layout_marginEnd="@dimen/dp_11"
        android:elevation="@dimen/dp_3"
        android:visibility="invisible"/>

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <View
            android:id="@+id/doodle_background"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/phone_edit_note_doodle_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/majorToolContainer" />

        <com.topstack.kilonotes.base.doodle.views.doodleview.DoodleView
            android:id="@+id/doodle"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/minor_tool_layer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/majorToolContainer" />

        <!--  major tool area -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/majorToolContainer"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_144"
            android:background="@drawable/phone_note_editor_title_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/back"
                android:layout_width="@dimen/dp_62"
                android:layout_height="@dimen/dp_62"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginBottom="@dimen/dp_27"
                android:src="@drawable/phone_icon_common_back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/show_thumbnail"
                android:layout_width="@dimen/dp_70"
                android:layout_height="@dimen/dp_70"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginBottom="@dimen/dp_18"
                android:src="@drawable/phone_note_main_tool_show_thumbnail"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/back" />

            <ImageView
                android:id="@+id/undo"
                android:layout_width="@dimen/dp_70"
                android:layout_height="@dimen/dp_70"
                android:layout_marginStart="@dimen/dp_56"
                android:scaleType="fitXY"
                android:src="@drawable/phone_note_tool_undo_background_selector"
                app:layout_constraintBottom_toBottomOf="@id/show_thumbnail"
                app:layout_constraintStart_toEndOf="@id/show_thumbnail"
                app:layout_constraintTop_toTopOf="@id/show_thumbnail" />

            <ImageView
                android:id="@+id/redo"
                android:layout_width="@dimen/dp_70"
                android:layout_height="@dimen/dp_70"
                android:layout_marginStart="@dimen/dp_56"
                android:scaleType="fitXY"
                android:src="@drawable/phone_note_tool_redo_background_selector"
                app:layout_constraintBottom_toBottomOf="@id/show_thumbnail"
                app:layout_constraintStart_toEndOf="@id/undo"
                app:layout_constraintTop_toTopOf="@id/show_thumbnail" />


            <ImageView
                android:id="@+id/new_page"
                android:layout_width="@dimen/dp_70"
                android:layout_height="@dimen/dp_70"
                android:layout_marginEnd="@dimen/dp_48"
                android:padding="@dimen/dp_6"
                android:src="@drawable/phone_icon_new_page"
                app:layout_constraintBottom_toBottomOf="@id/more"
                app:layout_constraintEnd_toStartOf="@id/more"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/more"
                android:layout_width="@dimen/dp_70"
                android:layout_height="@dimen/dp_70"
                android:layout_marginEnd="@dimen/dp_48"
                android:layout_marginBottom="@dimen/dp_22"
                android:src="@drawable/phone_note_main_tool_more"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/save"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/save"
                android:layout_width="@dimen/dp_70"
                android:layout_height="@dimen/dp_70"
                android:layout_marginEnd="@dimen/dp_48"
                android:layout_marginBottom="@dimen/dp_22"
                android:padding="@dimen/dp_6"
                android:src="@drawable/phone_note_mian_tool_save_document"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:ignore="ContentDescription" />

        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--  major tool area -->

        <!-- minor tool area start -->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/minor_tool_layer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent">

            <View
                android:id="@+id/minor_tool_area"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_196"
                android:background="@color/white"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/tool_mode_selector"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/tool_area"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_196"
                android:background="@color/white"
                android:clickable="true"
                android:focusable="true"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/back_to_list_level_one"
                    android:layout_width="@dimen/dp_120"
                    android:layout_height="@dimen/dp_120"
                    android:padding="@dimen/dp_28"
                    android:src="@drawable/phone_note_tool_icon_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@id/minor_tool_divider"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription" />

                <View
                    android:id="@+id/minor_tool_divider"
                    android:layout_width="@dimen/dp_2"
                    android:layout_height="@dimen/dp_63"
                    android:layout_marginStart="@dimen/dp_121"
                    android:background="@color/common_divider"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/tool_content_list"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/minor_tool_divider"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/three_area"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_196"
                android:background="@color/white"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/back_to_list_level_two"
                    android:layout_width="@dimen/dp_120"
                    android:layout_height="@dimen/dp_120"
                    android:padding="@dimen/dp_28"
                    android:src="@drawable/phone_note_tool_icon_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@id/three_minor_tool_divider"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription" />

                <View
                    android:id="@+id/three_minor_tool_divider"
                    android:layout_width="@dimen/dp_2"
                    android:layout_height="@dimen/dp_63"
                    android:layout_marginStart="@dimen/dp_121"
                    android:background="@color/common_divider"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/three_tool_content_list"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/three_minor_tool_divider"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/minor_tool_split_line"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:background="@color/bottom_sheet_split_line"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- minor tool area end -->

        <!--  floating indicator  -->
        <TextView
            android:id="@+id/page_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_32"
            android:layout_marginBottom="@dimen/dp_22"
            android:background="@drawable/page_indicator_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_20"
            android:paddingTop="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_8"
            android:textColor="#787878"
            android:textSize="@dimen/sp_22"
            app:layout_constraintBottom_toBottomOf="@id/doodle"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="1/1" />

        <TextView
            android:id="@+id/note_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_32"
            android:background="@drawable/page_indicator_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_20"
            android:paddingTop="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_8"
            android:textColor="#787878"
            android:textSize="@dimen/sp_22"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/page_indicator"
            app:layout_constraintStart_toEndOf="@id/page_indicator"
            tools:text="无标题笔记本-42"
            tools:visibility="visible" />
        <!--  floating indicator  -->

        <!--  text editor  -->
        <androidx.constraintlayout.helper.widget.Layer
            android:id="@+id/text_editor_layer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="text_editor_top_shadow, text_editor_container"
            app:layout_constraintBottom_toBottomOf="@id/text_editor_container"
            app:layout_constraintTop_toTopOf="@id/text_editor_top_shadow" />

        <View
            android:id="@+id/text_editor_top_shadow"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_30"
            android:background="@drawable/phone_select_color_window_top_shadow"
            app:layout_constraintBottom_toTopOf="@id/text_editor_container" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/text_editor_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:layout_constraintBottom_toTopOf="@id/minor_tool_layer">

            <com.topstack.kilonotes.base.doodle.views.textborderview.DoodleEditTextView
                android:id="@+id/text_editor"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_36"
                android:layout_marginEnd="@dimen/dp_6"
                android:background="@null"
                android:hint="@string/editor_hint_input_text"
                android:maxLines="3"
                android:paddingTop="@dimen/dp_36"
                android:paddingBottom="@dimen/dp_36"
                android:textCursorDrawable="@drawable/note_edit_text_cursor"
                android:textSize="@dimen/sp_42"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/text_editor_bottom_divider"
                app:layout_constraintEnd_toStartOf="@id/text_done_button"
                app:layout_constraintHeight_max="@dimen/dp_222"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/text_done_button"
                android:layout_width="@dimen/dp_130"
                android:layout_height="match_parent"
                android:paddingStart="@dimen/dp_30"
                android:paddingEnd="@dimen/dp_30"
                android:src="@drawable/color_selected"
                app:layout_constraintBottom_toBottomOf="@id/text_editor"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/text_editor" />

            <View
                android:id="@+id/text_editor_bottom_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:background="@color/bottom_sheet_split_line"
                app:layout_constraintBottom_toBottomOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--  text editor  -->

        <View
            android:id="@+id/material_dialog_outside_background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/note_material_dialog_outside_background"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/banner_ad_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:maxHeight="@dimen/dp_50" />
</LinearLayout>
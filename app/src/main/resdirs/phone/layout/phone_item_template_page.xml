<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/template_page_cover"
        android:layout_width="@dimen/dp_257"
        android:layout_height="@dimen/dp_345"
        android:padding="@dimen/dp_2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/template_page_name"
        tools:ignore="ContentDescription"
        tools:src="@drawable/cover1" />

    <TextView
        android:id="@+id/template_page_name"
        android:layout_width="@dimen/dp_257"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_24"
        android:gravity="center"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="日计划-01" />

    <ImageView
        android:id="@+id/vip_tag"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:background="@drawable/phone_add_template_vip_tag_bg"
        android:src="@drawable/template_vip_icon"
        app:layout_constraintEnd_toEndOf="@id/template_page_cover"
        app:layout_constraintTop_toTopOf="@id/template_page_cover"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/maker"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:background="#80000000"
        app:layout_constraintStart_toStartOf="@id/template_page_cover"
        app:layout_constraintEnd_toEndOf="@id/template_page_cover"
        app:layout_constraintTop_toTopOf="@id/template_page_cover"
        app:layout_constraintBottom_toBottomOf="@id/template_page_cover"
        tools:ignore="ContentDescription" />
</androidx.constraintlayout.widget.ConstraintLayout>
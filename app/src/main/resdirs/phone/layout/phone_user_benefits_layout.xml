<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/vip_store_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_127"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/user_benefit_layout_title_bg"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_240"
            android:background="@color/vip_store_title_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/user_benefit_layout_close"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_33"
            android:padding="@dimen/dp_18"
            android:src="@drawable/vip_store_close"
            app:layout_constraintBottom_toBottomOf="@+id/user_benefit_layout_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/user_benefit_layout_title" />

        <!--这个在自定义View中代码设置了AutoSize"-->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/user_benefit_layout_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_105"
            android:layout_marginBottom="@dimen/dp_30"
            android:maxLines="1"
            android:text="@string/kilonotes_membership_page"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_max="wrap" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/phone_benefits_bg"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/vip_scroll_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.Group
                android:id="@+id/account_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="user_icon, user_nickname, user_vip_icon, login_description, login" />

            <ImageView
                android:id="@+id/user_icon"
                android:layout_width="@dimen/dp_120"
                android:layout_height="@dimen/dp_120"
                android:layout_marginStart="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_20"
                android:padding="@dimen/dp_8"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/cover4" />

            <TextView
                android:id="@+id/user_nickname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginEnd="@dimen/dp_60"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxWidth="@dimen/dp_282"
                android:maxLines="1"
                android:text="@string/visitor"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_48"
                app:layout_constraintBottom_toTopOf="@id/login_description"
                app:layout_constraintStart_toEndOf="@id/user_icon"
                app:layout_constraintTop_toTopOf="@id/user_icon"
                app:layout_constraintVertical_chainStyle="packed" />

            <ImageView
                android:id="@+id/user_vip_icon"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_marginStart="@dimen/dp_12"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/user_nickname"
                app:layout_constraintStart_toEndOf="@+id/user_nickname"
                app:layout_constraintTop_toTopOf="@id/user_nickname" />

            <TextView
                android:id="@+id/login_description"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:includeFontPadding="false"
                android:text="@string/vip_store_vip_sync_vip_tip"
                android:textColor="@color/vip_store_vip_sync_vip_tip_text"
                android:textSize="@dimen/sp_30"
                app:layout_constraintBottom_toBottomOf="@id/user_icon"
                app:layout_constraintEnd_toStartOf="@id/login"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@id/user_nickname"
                app:layout_constraintTop_toBottomOf="@id/user_nickname"
                app:layout_constraintWidth_max="wrap" />

            <com.lihang.ShadowLayout
                android:id="@+id/login"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_36"
                app:hl_cornerRadius="@dimen/dp_40"
                app:layout_constraintBottom_toBottomOf="@id/user_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/user_icon">

                <TextView
                    android:id="@+id/login_btn_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/button_confirm_background"
                    android:gravity="center"
                    android:minWidth="@dimen/dp_220"
                    android:paddingHorizontal="@dimen/dp_30"
                    android:paddingVertical="@dimen/dp_20"
                    android:text="@string/login"
                    android:textColor="@color/book_name_edit_confirm_button_text"
                    android:textSize="@dimen/sp_36" />
            </com.lihang.ShadowLayout>

            <com.lihang.ShadowLayout
                android:id="@+id/member_benefit_slideshow_background"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_48"
                app:hl_cornerRadius="@dimen/dp_40"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_icon">

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/member_benefit_slideshow"
                    android:layout_width="@dimen/dp_984"
                    android:layout_height="@dimen/dp_274"
                    android:background="@color/vip_store_background_color" />

            </com.lihang.ShadowLayout>

            <ImageView
                android:id="@+id/member_benefit_crown"
                android:layout_width="@dimen/dp_72"
                android:layout_height="@dimen/dp_56"
                android:layout_marginStart="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_48"
                android:src="@drawable/phone_member_benefits_crown"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/member_benefit_slideshow_background" />

            <TextView
                android:id="@+id/member_benefit_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_36"
                android:text="@string/kilonotes_membership_benefits"
                android:textColor="@color/vip_store_membership_benefits"
                android:textSize="@dimen/sp_48"
                app:layout_constraintBottom_toBottomOf="@id/member_benefit_crown"
                app:layout_constraintStart_toEndOf="@id/member_benefit_crown"
                app:layout_constraintTop_toTopOf="@id/member_benefit_crown" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/benefits_list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/member_benefit_title"
                app:layout_constraintTop_toBottomOf="@+id/member_benefit_crown" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/vip_price_rv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_39"
                android:overScrollMode="never"
                android:paddingHorizontal="@dimen/dp_120"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/benefits_list"
                tools:itemCount="2" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/more_and_pick_up"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/dp_24"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/vip_price_rv"
                app:layout_constraintStart_toStartOf="@id/vip_price_rv"
                app:layout_constraintTop_toBottomOf="@id/vip_price_rv">

                <TextView
                    android:id="@+id/more_and_pick_up_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/kilonates_membership_price_more"
                    android:textColor="@color/vip_store_more_and_pick_up_text_color"
                    android:textSize="@dimen/sp_30"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/more_and_pick_up_icon"
                    android:layout_width="@dimen/dp_30"
                    android:layout_height="@dimen/dp_20"
                    android:layout_marginStart="@dimen/dp_16"
                    android:scaleType="centerCrop"
                    android:src="@drawable/phone_more_and_pick_up_icon"
                    app:layout_constraintBottom_toBottomOf="@id/more_and_pick_up_text"
                    app:layout_constraintStart_toEndOf="@id/more_and_pick_up_text"
                    app:layout_constraintTop_toTopOf="@id/more_and_pick_up_text" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.Group
                android:id="@+id/choose_pay_type_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="select_pay_type_rv" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/select_pay_type_rv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_48"
                android:background="@drawable/phone_vip_store_choose_pay_type_shape"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/more_and_pick_up"
                tools:layout_height="@dimen/dp_224" />

            <TextView
                android:id="@+id/subscription_price_description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_100"
                android:layout_marginTop="@dimen/dp_48"
                android:gravity="center_horizontal"
                android:scrollbars="vertical"
                android:text="@string/vip_duration_loading"
                android:textColor="@color/text_color_666666"
                android:textSize="@dimen/sp_30"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/vip_price_rv"
                app:layout_constraintStart_toStartOf="@id/vip_price_rv"
                app:layout_constraintTop_toBottomOf="@id/select_pay_type_rv"
                app:layout_constraintWidth_max="wrap" />

            <TextView
                android:id="@+id/restore_subscription"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_36"
                android:paddingHorizontal="@dimen/dp_48"
                android:text="@string/vip_store_restore_purchase"
                android:textColor="#333333"
                android:textSize="@dimen/sp_48"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/subscription_price_description"
                app:layout_constraintWidth_max="wrap" />

            <TextView
                android:id="@+id/policy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_48"
                android:maxLines="3"
                android:paddingHorizontal="@dimen/dp_48"
                android:paddingBottom="@dimen/dp_70"
                android:scrollbars="vertical"
                android:text="@string/vip_store_read_and_agree_text"
                android:textColor="@color/vip_store_vip_sync_vip_tip_text"
                android:textSize="@dimen/sp_36"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/restore_subscription" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/pay_related_content_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="vip_price_rv,policy" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/floating_jump_to_pay_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/floating_jump_to_pay_area_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">

        <View
            android:id="@+id/stroke"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_2"
            android:background="@color/user_benefits_blur_stroke"
            app:layout_constraintBottom_toTopOf="@id/blur"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <eightbitlab.com.blurview.BlurView
            android:id="@+id/blur"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/stroke">

            <com.lihang.ShadowLayout
                android:id="@+id/floating_jump_to_pay_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginVertical="@dimen/dp_44"
                app:hl_cornerRadius="@dimen/dp_100"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/floating_jump_to_pay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/vip_pay_button_bg"
                    android:duplicateParentState="true"
                    android:gravity="center"
                    android:minWidth="@dimen/dp_870"
                    android:paddingHorizontal="@dimen/dp_60"
                    android:paddingVertical="@dimen/dp_30"
                    android:text="@string/vip_store_pay_button_default_text"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_54"
                    android:textStyle="bold" />
            </com.lihang.ShadowLayout>

        </eightbitlab.com.blurview.BlurView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

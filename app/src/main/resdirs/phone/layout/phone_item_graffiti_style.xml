<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_166">

    <ImageView
        android:id="@+id/default_image"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginBottom="@dimen/dp_10"
        app:layout_constraintBottom_toTopOf="@id/name"
        app:layout_constraintEnd_toEndOf="@id/name"
        app:layout_constraintStart_toStartOf="@id/name"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.lihang.ShadowLayout
        android:id="@+id/style"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintTop_toTopOf="@id/default_image"
        app:layout_constraintStart_toStartOf="@id/default_image"
        app:layout_constraintEnd_toEndOf="@+id/default_image"
        app:layout_constraintBottom_toBottomOf="@id/default_image"
        app:hl_cornerRadius="@dimen/dp_12"
        android:layout_margin="@dimen/dp_9"
        android:visibility="invisible"
        android:clickable="true">
        <ImageView
            android:id="@+id/style_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="matrix"/>

    </com.lihang.ShadowLayout>

    <TextView
        android:id="@+id/name"
        android:layout_width="@dimen/dp_120"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="top|center_horizontal"
        android:maxLines="2"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/default_image" />

</androidx.constraintlayout.widget.ConstraintLayout>
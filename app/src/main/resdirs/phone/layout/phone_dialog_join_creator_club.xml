<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:padding="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_32"
        android:src="@drawable/dialog_setting_general_icon_close"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/background"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintHeight_percent="0.25"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/close"
        android:background="@drawable/creator_club_background">

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintTop_toBottomOf="@+id/background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_36"
            android:layout_marginBottom="@dimen/dp_29"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/msg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/dp_36"
                    android:layout_marginTop="@dimen/dp_36"
                    android:text="@string/creator_community_title"
                    android:textColor="@color/text_primary"
                    android:textSize="@dimen/sp_48"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/describe"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/dp_5"
                    android:paddingHorizontal="@dimen/dp_36"
                    android:layout_marginTop="@dimen/dp_24"
                    android:lineSpacingMultiplier="1.5"
                    android:text="@string/creator_community_describe"
                    android:textColor="#FF3D3B4F"
                    android:textSize="@dimen/sp_44" />

            </LinearLayout>

        </ScrollView>

        <TextView
            android:id="@+id/msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_24"
            android:text="@string/creator_community_msg"
            android:textColor="#FF3D3B4F"
            android:textSize="@dimen/sp_40"
            app:layout_constraintBottom_toTopOf="@+id/login_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.lihang.ShadowLayout
            android:id="@+id/login_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/dp_68"
            app:hl_cornerRadius="@dimen/dp_41"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/login"
                android:layout_width="@dimen/dp_480"
                android:layout_height="@dimen/dp_96"
                android:layout_gravity="center"
                android:background="@color/cover_selected_background"
                android:gravity="center"
                android:text="@string/apply_to_join"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_48" />
        </com.lihang.ShadowLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <View
        android:id="@+id/top_shadow"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/phone_select_color_window_top_shadow"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_20"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="@+id/top_shadow"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/color_title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_78"
        android:text="@string/color_picker_color"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/color_finish_edit"
        android:layout_width="@dimen/dp_64"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_46"
        android:src="@drawable/phone_select_color_icon_finish_edit"
        app:layout_constraintBottom_toBottomOf="@id/color_title_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/color_title_text"
        tools:ignore="ContentDescription" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/sub_title_background"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_80"
        android:background="@drawable/phone_select_color_button_group_shape"
        android:minHeight="@dimen/dp_90"
        app:layout_constraintTop_toBottomOf="@id/color_title_text">

        <TextView
            android:id="@+id/color_preset_text"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:background="@drawable/phone_select_color_button_stroke"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:text="@string/color_preset"
            android:textColor="@color/select_photo_button_text"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="@id/sub_title_background"
            app:layout_constraintEnd_toStartOf="@id/color_custom_text"
            app:layout_constraintStart_toStartOf="@id/sub_title_background"
            app:layout_constraintTop_toTopOf="@id/sub_title_background" />

        <TextView
            android:id="@+id/color_custom_text"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:background="@drawable/phone_select_color_button_stroke"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:text="@string/color_custom"
            android:textColor="@color/select_photo_button_text"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="@id/sub_title_background"
            app:layout_constraintEnd_toEndOf="@id/sub_title_background"
            app:layout_constraintStart_toEndOf="@id/color_preset_text"
            app:layout_constraintTop_toTopOf="@id/sub_title_background" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/color_list_view"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_719"
        android:layout_marginStart="@dimen/dp_22"
        android:layout_marginTop="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_17"
        android:fadeScrollbars="true"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sub_title_background" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/color_custom_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="color_pick_view,color_add_image,color_indicator,color_edit_text" />

    <com.topstack.kilonotes.base.component.view.ColorPickView
        android:id="@+id/color_pick_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_58"
        app:hue_panel_height="@dimen/dp_58"
        app:hue_tracker_color="@android:color/transparent"
        app:hue_tracker_height="@dimen/dp_42"
        app:hue_tracker_stroke_color="@color/white"
        app:hue_tracker_stroke_width="@dimen/dp_8"
        app:hue_tracker_width="@dimen/dp_42"
        app:layout_constraintBottom_toTopOf="@id/color_indicator"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sub_title_background"
        app:panel_interval="@dimen/dp_28"
        app:saturation_value_panel_height="@dimen/dp_432"
        app:saturation_value_tracker_radius="@dimen/dp_25"
        app:saturation_value_tracker_stroke_width="@dimen/dp_8" />

    <androidx.cardview.widget.CardView
        android:id="@+id/color_indicator"
        android:layout_width="@dimen/dp_104"
        android:layout_height="@dimen/dp_104"
        android:layout_marginTop="@dimen/dp_38"
        android:layout_marginBottom="@dimen/dp_28"
        app:cardCornerRadius="@dimen/dp_23"
        app:cardElevation="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/color_pick_view"
        app:layout_constraintTop_toBottomOf="@id/color_pick_view">

        <ImageView
            android:id="@+id/color_indicator_image"
            android:layout_width="@dimen/dp_70"
            android:layout_height="@dimen/dp_70"
            android:layout_gravity="center"
            android:src="@drawable/phone_select_color_icon_pick"
            tools:ignore="ContentDescription" />

    </androidx.cardview.widget.CardView>

    <EditText
        android:id="@+id/color_edit_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_32"
        android:background="@null"
        android:imeOptions="actionDone"
        android:importantForAutofill="no"
        android:inputType="textNoSuggestions"
        android:minWidth="@dimen/dp_10"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintBottom_toBottomOf="@id/color_indicator"
        app:layout_constraintStart_toEndOf="@id/color_indicator"
        app:layout_constraintTop_toTopOf="@id/color_indicator"
        tools:ignore="LabelFor" />

    <ImageView
        android:id="@+id/color_add_image"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:src="@drawable/phone_select_color_icon_add_clickable"
        app:layout_constraintBottom_toBottomOf="@id/color_indicator"
        app:layout_constraintEnd_toEndOf="@id/color_pick_view"
        app:layout_constraintTop_toTopOf="@id/color_indicator"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
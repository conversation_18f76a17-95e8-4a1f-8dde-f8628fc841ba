<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF2F2F7">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:text="@string/setting"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_54"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/backup_switch_container"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_48"
        android:background="@drawable/phone_dialog_backup_space_setting_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title">

        <ImageView
            android:id="@+id/image_back_up_icon"
            android:layout_width="@dimen/dp_70"
            android:layout_height="@dimen/dp_70"
            android:layout_marginStart="@dimen/dp_18"
            android:src="@drawable/note_icon_backup_setting"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_18"
            android:text="@string/backup"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_40"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/image_back_up_icon"
            app:layout_constraintTop_toTopOf="parent" />


        <Switch
            android:id="@+id/backup_switch"
            style="@style/PhoneSwitchStyle"
            android:layout_marginEnd="@dimen/dp_18"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="UseSwitchCompatOrMaterialXml" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
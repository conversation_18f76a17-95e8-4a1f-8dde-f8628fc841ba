<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_166">

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/dp_10"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toTopOf="@id/name"
        app:layout_constraintEnd_toEndOf="@id/name"
        app:layout_constraintStart_toStartOf="@id/name"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.topstack.kilonotes.phone.component.BezierCurveItemView
            android:id="@+id/side_bar_bezier_size_item"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

    <TextView
        android:id="@+id/name"
        android:layout_width="@dimen/dp_120"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="top|center_horizontal"
        android:maxLines="2"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/container" />

</androidx.constraintlayout.widget.ConstraintLayout>

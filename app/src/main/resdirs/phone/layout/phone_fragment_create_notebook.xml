<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg_color"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <com.topstack.kilonotes.base.component.view.CommonInputLayout
        android:id="@+id/new_note_book_input_layout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_94"
        android:layout_marginHorizontal="@dimen/dp_48"
        app:editTextPaddingEnd="@dimen/dp_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header"
        app:textColor="@color/book_name_edit_text"
        app:textSize="@dimen/sp_40" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_183"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/new_note_book_cancel"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_63"
            android:layout_marginStart="@dimen/dp_48"
            android:background="@null"
            android:scaleType="fitCenter"
            android:src="@drawable/phone_create_note_icon_cancel"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/title"
            android:layout_width="@dimen/dp_402"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_62"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:maxLines="2"
            android:text="@string/create_book"
            android:textColor="@color/note_tool_image_pick_tips_color"
            android:textSize="@dimen/sp_42"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/new_note_book_confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_48"
            android:background="@null"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="2"
            android:text="@string/next_step"
            android:textColor="@color/hint_text"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <ImageButton
            android:id="@+id/completed_confirm"
            android:layout_width="@dimen/dp_63"
            android:layout_height="@dimen/dp_63"
            android:layout_marginEnd="@dimen/dp_48"
            android:background="@null"
            android:scaleType="fitCenter"
            android:src="@drawable/phone_create_note_icon_confirm"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.topstack.kilonotes.phone.component.CoverPaperView
        android:id="@+id/cover_paper_view"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_803"
        android:layout_marginBottom="@dimen/dp_60"
        android:elevation="@dimen/dp_3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/new_note_book_input_layout" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/phone_fragment_create_notebook_bottom_sheet_background"
        android:elevation="@dimen/dp_60"
        app:layout_constraintBottom_toBottomOf="@id/bottom_sheet"
        app:layout_constraintTop_toTopOf="@id/bottom_sheet" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottom_sheet"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_60"
        android:background="@drawable/phone_fragment_create_notebook_bottom_sheet_background"
        android:elevation="@dimen/dp_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cover_paper_view">

        <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
            android:id="@+id/note_book_cover_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:scrollOrientation="vertical" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
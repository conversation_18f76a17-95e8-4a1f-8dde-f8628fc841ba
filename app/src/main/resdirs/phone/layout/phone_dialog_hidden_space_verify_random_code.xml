<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/phone_dialog_hidden_space_get_back_password_bg">

    <com.lihang.ShadowLayout
        android:id="@+id/input_code_box_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_60"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_layoutBackground="@color/vip_store_background_color"
        app:hl_strokeColor="@color/button_disabled"
        app:hl_strokeWith="@dimen/dp_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <EditText
            android:id="@+id/input_code_box"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:singleLine="true"
            android:scrollHorizontally="true"
            android:ellipsize="end"
            android:hint="@string/hidden_space_verify_random_code_placeholder"
            android:paddingStart="@dimen/dp_48"
            android:paddingEnd="@dimen/dp_48"
            android:paddingVertical="@dimen/dp_27"
            android:saveEnabled="false"
            android:textColor="@color/text_secondary"
            android:textColorHint="@color/backup_subtitle_color"
            android:textSize="@dimen/sp_48" />
    </com.lihang.ShadowLayout>

    <ImageView
        android:id="@+id/clear"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginEnd="@dimen/dp_30"
        android:src="@drawable/doodle_text_element_icon_clear"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/input_code_box_container"
        app:layout_constraintEnd_toEndOf="@id/input_code_box_container"
        app:layout_constraintTop_toTopOf="@id/input_code_box_container" />

    <TextView
        android:id="@+id/tips"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginVertical="@dimen/dp_48"
        android:scrollbars="vertical"
        android:text="@string/hidden_space_verify_random_code_tips"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintBottom_toTopOf="@id/confirm_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/input_code_box_container" />

    <com.lihang.ShadowLayout
        android:id="@+id/confirm_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_170"
        android:layout_marginBottom="@dimen/dp_60"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_endColor="@color/button_gradient_end"
        app:hl_startColor="@color/button_gradient_start"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_26"
            android:text="@string/confirm"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48" />
    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.lihang.ShadowLayout
        android:id="@+id/default_cover"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hl_cornerRadius="@dimen/dp_24"
        app:hl_shadowLimit="@dimen/dp_24"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/cover"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="h,984:761"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/cover4" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.lihang.ShadowLayout>

    <com.lihang.ShadowLayout
        android:id="@id/loading"
        android:layout_width="@dimen/dp_414"
        android:layout_height="@dimen/dp_135"
        android:visibility="invisible"
        app:hl_cornerRadius="@dimen/dp_18"
        app:hl_endColor="@color/user_benefit_layout_shadow_color"
        app:hl_startColor="@color/user_benefit_layout_shadow_color"
        app:layout_constraintBottom_toBottomOf="@id/default_cover"
        app:layout_constraintEnd_toEndOf="@id/default_cover"
        app:layout_constraintStart_toStartOf="@id/default_cover"
        app:layout_constraintTop_toTopOf="@id/default_cover"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.topstack.kilonotes.base.component.view.LoadingView
                android:layout_width="@dimen/dp_120"
                android:layout_height="@dimen/dp_75"
                android:layout_marginStart="@dimen/dp_60"
                android:layout_marginBottom="@dimen/dp_35"
                app:bounceHeight="@dimen/dp_5"
                app:dotRadius="@dimen/dp_7"
                app:dotSpace="@dimen/dp_17"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_60"
                android:text="@string/handbook_loading"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_36"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

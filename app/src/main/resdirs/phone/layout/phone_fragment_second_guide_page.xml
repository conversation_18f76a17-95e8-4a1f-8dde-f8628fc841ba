<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layoutDescription="@xml/phone_fragment_second_guide_page_scene">

    <TextView
        android:id="@+id/slice_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_132"
        android:layout_marginBottom="@dimen/dp_135"
        android:text="@string/phone_second_guide_page_title"
        android:textAlignment="center"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_70"
        app:layout_constraintBottom_toTopOf="@id/handbook"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/handbook"
        android:layout_width="@dimen/dp_680"
        android:layout_height="@dimen/dp_1100"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_second_guide_page_handbook"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/template_white"
        android:layout_width="@dimen/dp_450"
        android:layout_height="@dimen/dp_530"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_600"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_second_guide_page_template_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/template_blue"
        android:layout_width="@dimen/dp_450"
        android:layout_height="@dimen/dp_530"
        android:layout_marginTop="@dimen/dp_580"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_second_guide_page_template_blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/template_purple"
        android:layout_width="@dimen/dp_450"
        android:layout_height="@dimen/dp_530"
        android:layout_marginTop="@dimen/dp_590"
        android:layout_marginEnd="@dimen/dp_64"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_second_guide_page_template_purple"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/draw_tools"
        android:layout_width="@dimen/dp_1000"
        android:layout_height="@dimen/dp_220"
        android:layout_marginTop="@dimen/dp_930"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_second_guide_page_draw_tools"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/font_tools"
        android:layout_width="@dimen/dp_350"
        android:layout_height="@dimen/dp_140"
        android:layout_marginStart="@dimen/dp_60"
        android:layout_marginTop="@dimen/dp_270"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_second_guide_page_font_tools"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/text_tools"
        android:layout_width="@dimen/dp_330"
        android:layout_height="@dimen/dp_150"
        android:layout_marginTop="@dimen/dp_140"
        android:layout_marginEnd="@dimen/dp_78"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_second_guide_page_text_tools"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

</androidx.constraintlayout.motion.widget.MotionLayout>
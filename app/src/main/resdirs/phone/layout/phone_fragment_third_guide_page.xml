<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layoutDescription="@xml/phone_fragment_third_guide_page_scene">

    <TextView
        android:id="@+id/slice_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_132"
        android:layout_marginBottom="@dimen/dp_135"
        android:text="@string/phone_third_guide_page_title"
        android:textAlignment="center"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_70"
        app:layout_constraintBottom_toTopOf="@id/handbook_reference_position"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/handbook_reference_position"
        android:layout_width="@dimen/dp_680"
        android:layout_height="@dimen/dp_1100"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/template_yellow"
        android:layout_width="@dimen/dp_190"
        android:layout_height="@dimen/dp_230"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_yellow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/template_star"
        android:layout_width="@dimen/dp_200"
        android:layout_height="@dimen/dp_260"
        android:layout_marginTop="@dimen/dp_180"
        android:layout_marginEnd="@dimen/dp_200"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_star"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/template_yellow" />

    <ImageView
        android:id="@+id/template_purple"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_220"
        android:layout_marginStart="@dimen/dp_310"
        android:layout_marginBottom="@dimen/dp_120"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_purple"
        app:layout_constraintBottom_toTopOf="@id/template_yellow"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/template_green"
        android:layout_width="@dimen/dp_200"
        android:layout_height="@dimen/dp_250"
        android:layout_marginStart="@dimen/dp_200"
        android:layout_marginTop="@dimen/dp_120"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_green"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/template_yellow" />

    <ImageView
        android:id="@+id/template_blue"
        android:layout_width="@dimen/dp_250"
        android:layout_height="@dimen/dp_320"
        android:layout_marginEnd="@dimen/dp_356"
        android:layout_marginBottom="@dimen/dp_100"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_blue"
        app:layout_constraintBottom_toTopOf="@id/template_yellow"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/template_plan_day"
        android:layout_width="@dimen/dp_270"
        android:layout_height="@dimen/dp_320"
        android:layout_marginEnd="@dimen/dp_170"
        android:layout_marginBottom="@dimen/dp_40"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_plan_day"
        app:layout_constraintBottom_toBottomOf="@id/template_yellow"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/template_date_to_do"
        android:layout_width="@dimen/dp_240"
        android:layout_height="@dimen/dp_300"
        android:layout_marginEnd="@dimen/dp_140"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_date_to_do"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/template_yellow" />

    <ImageView
        android:id="@+id/template_daily_plan"
        android:layout_width="@dimen/dp_200"
        android:layout_height="@dimen/dp_270"
        android:layout_marginStart="@dimen/dp_158"
        android:layout_marginBottom="@dimen/dp_370"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_daily_plan"
        app:layout_constraintBottom_toBottomOf="@id/template_yellow"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/template_sport_plan"
        android:layout_width="@dimen/dp_230"
        android:layout_height="@dimen/dp_280"
        android:layout_marginStart="@dimen/dp_113"
        android:layout_marginTop="@dimen/dp_30"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_sport_plan"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/template_yellow" />

    <ImageView
        android:id="@+id/template_clock_in_days"
        android:layout_width="@dimen/dp_380"
        android:layout_height="@dimen/dp_500"
        android:layout_marginStart="@dimen/dp_109"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_clock_in_days"
        app:layout_constraintBottom_toBottomOf="@id/template_yellow"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/template_recite_words"
        android:layout_width="@dimen/dp_300"
        android:layout_height="@dimen/dp_400"
        android:layout_marginTop="@dimen/dp_200"
        android:layout_marginEnd="@dimen/dp_220"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_recite_words"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/template_yellow" />

    <ImageView
        android:id="@+id/template_template_december"
        android:layout_width="@dimen/dp_420"
        android:layout_height="@dimen/dp_540"
        android:layout_marginEnd="@dimen/dp_72"
        android:layout_marginBottom="@dimen/dp_150"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_third_guide_page_template_december"
        app:layout_constraintBottom_toBottomOf="@id/template_yellow"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/guide_start_use"
        android:layout_width="@dimen/dp_710"
        android:layout_height="@dimen/dp_100"
        android:layout_marginBottom="@dimen/dp_260"
        android:background="@drawable/phone_guide_button_confirm_background"
        android:gravity="center"
        android:text="@string/guide_start_use"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_46"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/guide_terms_and_policy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_46"
        android:gravity="center"
        android:textColor="@color/guide_terms_text_normal"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guide_start_use" />


</androidx.constraintlayout.motion.widget.MotionLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_98">

    <View
        android:id="@+id/color_background"
        android:layout_width="@dimen/dp_92"
        android:layout_height="@dimen/dp_92"
        android:background="@drawable/phone_color_list_item_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/color_add"
        android:layout_width="@dimen/dp_92"
        android:layout_height="@dimen/dp_92"
        android:scaleType="centerInside"
        android:src="@drawable/phone_color_list_item_icon_add"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/color_select"
        android:layout_width="@dimen/dp_45"
        android:layout_height="wrap_content"
        android:scaleType="centerInside"
        android:src="@drawable/phone_color_list_item_icon_selected"
        app:layout_constraintBottom_toBottomOf="@id/color_background"
        app:layout_constraintEnd_toEndOf="@id/color_background"
        app:layout_constraintStart_toStartOf="@id/color_background"
        app:layout_constraintTop_toTopOf="@id/color_background"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/color_delete"
        android:layout_width="@dimen/dp_43"
        android:layout_height="@dimen/dp_43"
        android:src="@drawable/phone_color_list_item_icon_delete"
        app:layout_constraintStart_toStartOf="@id/color_select"
        android:layout_marginStart="@dimen/dp_36"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/CircleImageStyle"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_round_corner_bg">

    <com.topstack.kilonotes.base.imagecrop.ImageCropView
        android:id="@+id/crop_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/green"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_90"
        android:layout_marginStart="@dimen/dp_60"
        android:padding="@dimen/dp_10"
        android:src="@drawable/base_icon_back"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_120"
        android:ellipsize="end"
        android:gravity="center"
        android:maxWidth="@dimen/dp_500"
        android:maxLines="2"
        android:text="@string/pick_photo"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/use_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_60"
        android:ellipsize="end"
        android:gravity="center"
        android:maxWidth="@dimen/dp_200"
        android:maxLines="2"
        android:padding="@dimen/dp_10"
        android:text="@string/use"
        android:textColor="@color/hint_text"
        android:textSize="@dimen/sp_48"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title" />

    <ProgressBar
        android:id="@+id/loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
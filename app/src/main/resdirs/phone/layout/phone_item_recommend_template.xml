<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/template_page_cover"
        android:layout_width="@dimen/dp_468"
        android:layout_height="@dimen/dp_832"
        android:scaleType="fitXY"
        android:padding="@dimen/dp_1"
        android:background="@drawable/phone_template_item_background"
        app:layout_constraintBottom_toTopOf="@id/template_page_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"
        tools:src="@drawable/cover1" />

    <TextView
        android:id="@+id/template_page_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_60"
        android:gravity="center"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_42"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/template_page_cover"
        app:layout_constraintStart_toStartOf="@id/template_page_cover"
        app:layout_constraintTop_toBottomOf="@id/template_page_cover"
        tools:text="日计划-01" />

    <ImageView
        android:id="@+id/vip_tag"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:background="@drawable/phone_template_flow_vip_tag_bg"
        android:src="@drawable/phone_template_vip_icon"
        app:layout_constraintEnd_toEndOf="@+id/template_page_cover"
        app:layout_constraintTop_toTopOf="@+id/template_page_cover"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/maker"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:background="@drawable/phone_template_flow_maker_bg"
        app:layout_constraintBottom_toBottomOf="@id/template_page_cover"
        app:layout_constraintEnd_toEndOf="@id/template_page_cover"
        app:layout_constraintStart_toStartOf="@id/template_page_cover"
        app:layout_constraintTop_toTopOf="@id/template_page_cover"
        tools:ignore="ContentDescription" />
</androidx.constraintlayout.widget.ConstraintLayout>

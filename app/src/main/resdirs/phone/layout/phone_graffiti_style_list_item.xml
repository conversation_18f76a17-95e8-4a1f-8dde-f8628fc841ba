<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_474"
    android:layout_height="@dimen/dp_150"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.lihang.ShadowLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:hl_cornerRadius="@dimen/dp_18">
        <ImageView
            android:id="@+id/style"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </com.lihang.ShadowLayout>
    <ImageView
        android:id="@+id/style_is_select"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/phone_select_graffiti_style_isselect_shape"
        android:visibility="invisible"/>

</androidx.constraintlayout.widget.ConstraintLayout>
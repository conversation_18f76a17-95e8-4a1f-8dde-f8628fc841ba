<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dialog_outline_create_bg">

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_78"
        android:layout_marginEnd="@dimen/dp_48"
        android:src="@drawable/dialog_login_icon_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_100"
        android:layout_marginBottom="@dimen/dp_60"
        android:text="@string/login_to_kilonotes"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_64"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/login_apple_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/login_apple_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.80"
        android:layout_marginBottom="@dimen/dp_40"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/login_phone_number_container"
        android:background="@drawable/login_button_background">

        <ImageView
            android:id="@+id/login_apple_icon"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_60"
            android:background="@drawable/login_using_apple_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/login_apple_desc"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_110"
            android:layout_marginTop="@dimen/dp_23"
            android:layout_marginBottom="@dimen/dp_23"
            android:text="@string/login_using_appleId"
            android:maxLines="1"
            android:textStyle="bold"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_40"
            app:autoSizeMaxTextSize="@dimen/sp_40"
            app:autoSizeMinTextSize="@dimen/sp_34"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/login_phone_number_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.80"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_40"
        app:layout_constraintTop_toBottomOf="@+id/login_apple_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/login_google_container"
        android:background="@drawable/login_button_background">

        <ImageView
            android:id="@+id/login_phone_number_icon"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_60"
            android:background="@drawable/login_using_phone_number_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/login_phone_number_desc"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_110"
            android:layout_marginTop="@dimen/dp_23"
            android:layout_marginBottom="@dimen/dp_23"
            android:text="@string/login_using_phone_number"
            android:maxLines="1"
            android:textStyle="bold"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_40"
            app:autoSizeMaxTextSize="@dimen/sp_40"
            app:autoSizeMinTextSize="@dimen/sp_34"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/login_google_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_40"
        app:layout_constraintWidth_percent="0.80"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/login_phone_number_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/login_wechat_container"
        android:background="@drawable/login_button_background">

        <ImageView
            android:id="@+id/login_google_icon"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_60"
            android:background="@drawable/login_using_google_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/login_google_desc"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_110"
            android:layout_marginTop="@dimen/dp_23"
            android:layout_marginBottom="@dimen/dp_23"
            android:text="@string/login_using_google"
            android:maxLines="1"
            android:textStyle="bold"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_40"
            app:autoSizeMaxTextSize="@dimen/sp_40"
            app:autoSizeMinTextSize="@dimen/sp_34"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/login_wechat_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_70"
        android:layout_marginBottom="@dimen/dp_40"
        app:layout_constraintWidth_percent="0.80"
        app:layout_constraintTop_toBottomOf="@+id/login_google_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/login_email_container"
        android:background="@drawable/login_button_background">

        <ImageView
            android:id="@+id/login_wechat_icon"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_60"
            android:background="@drawable/login_using_wechat_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/login_wechat_desc"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_110"
            android:layout_marginTop="@dimen/dp_23"
            android:layout_marginBottom="@dimen/dp_23"
            android:text="@string/login_using_wechat"
            android:maxLines="1"
            android:textStyle="bold"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_40"
            app:autoSizeMaxTextSize="@dimen/sp_40"
            app:autoSizeMinTextSize="@dimen/sp_34"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/login_email_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_70"
        android:layout_marginBottom="@dimen/dp_74"
        app:layout_constraintWidth_percent="0.80"
        app:layout_constraintTop_toBottomOf="@+id/login_wechat_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/description"
        android:background="@drawable/login_button_background">

        <ImageView
            android:id="@+id/login_email_icon"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_60"
            android:background="@drawable/login_using_email_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/login_email_desc"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_110"
            android:layout_marginTop="@dimen/dp_23"
            android:layout_marginBottom="@dimen/dp_23"
            android:text="@string/login_using_email"
            android:maxLines="1"
            android:textStyle="bold"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_40"
            app:autoSizeMaxTextSize="@dimen/sp_40"
            app:autoSizeMinTextSize="@dimen/sp_34"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_74"
        android:text="@string/login_instructions"
        android:textSize="@dimen/sp_32"
        android:textColor="@color/describe_text_color"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_email_container"
        app:layout_constraintBottom_toBottomOf="parent">

    </TextView>

</androidx.constraintlayout.widget.ConstraintLayout>


<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_196">

    <ImageView
        android:id="@+id/tool_image"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginTop="@dimen/dp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/vip_user_icon"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:layout_marginStart="@dimen/dp_62"
        android:layout_marginBottom="@dimen/dp_57"
        android:src="@drawable/phone_vip_input_mode_icon"
        app:layout_constraintBottom_toBottomOf="@id/tool_image"
        app:layout_constraintStart_toStartOf="@id/tool_image" />

    <TextView
        android:id="@+id/needVipIcon"
        android:layout_width="@dimen/dp_54"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_60"
        android:background="@drawable/phone_vip_input_mode_bg"
        android:gravity="center"
        android:text="@string/note_add_page_template_tag_vip"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_20"
        app:layout_constraintBottom_toBottomOf="@id/tool_image"
        app:layout_constraintStart_toEndOf="@id/tool_image" />


    <View
        android:id="@+id/sign_for_show_material"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:background="@drawable/sign_red_bg"
        app:layout_constraintTop_toTopOf="@id/tool_image"
        app:layout_constraintStart_toEndOf="@id/tool_image" />


    <TextView
        android:id="@+id/tool_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_24"
        android:ellipsize="end"
        android:gravity="top|center_horizontal"
        android:maxWidth="@dimen/dp_215"
        android:maxLines="2"
        android:minWidth="@dimen/dp_192"
        android:paddingHorizontal="@dimen/dp_36"
        android:text="name"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tool_image" />

</androidx.constraintlayout.widget.ConstraintLayout>
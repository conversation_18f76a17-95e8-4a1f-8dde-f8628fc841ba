<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true">


    <ImageButton
        android:id="@+id/back"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_63"
        android:scaleType="fitCenter"
        android:layout_marginStart="@dimen/dp_49"
        android:layout_marginTop="@dimen/dp_60"
        android:background="@null"
        android:src="@drawable/phone_create_note_icon_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />
    
    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/dp_900"
        android:maxLines="1"
        android:ellipsize="middle"
        android:layout_marginTop="@dimen/dp_56"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="无标题的笔记本"
        android:textSize="@dimen/sp_48"
        android:textColor="@color/text_primary"/>


    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/pages"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_57"
        app:layout_constraintBottom_toBottomOf="@id/split_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:scrollOrientation="vertical"
        />

    <View
        android:id="@+id/split_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/phone_select_color_window_top_shadow"
        android:layout_marginBottom="@dimen/dp_56"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pages"
        app:layout_constraintBottom_toTopOf="@id/add"/>

    <TextView
        android:id="@+id/add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/phone_catalog_add_page_bg"
        android:text="@string/add_page_title"
        android:gravity="center"
        android:textSize="@dimen/sp_45"
        android:textColor="@color/white"
        android:layout_marginBottom="@dimen/dp_30"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>



</androidx.constraintlayout.widget.ConstraintLayout>
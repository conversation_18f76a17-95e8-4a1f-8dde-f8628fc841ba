<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.lihang.ShadowLayout
        android:id="@+id/note_folder_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hl_cornerRadius_leftBottom="@dimen/dp_35"
        app:hl_cornerRadius_leftTop="@dimen/dp_21"
        app:hl_cornerRadius_rightBottom="@dimen/dp_35"
        app:hl_cornerRadius_rightTop="@dimen/dp_21"
        app:hl_shadowColor="@color/black_10"
        app:hl_shadowLimit="@dimen/dp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/note_folder_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:ignore="MissingConstraints">

            <com.topstack.kilonotes.base.component.view.InterceptClickRecyclerView
                android:id="@+id/note_folder"
                android:layout_width="@dimen/dp_418"
                android:layout_height="@dimen/dp_558"
                android:background="@drawable/phone_folder_cover"
                android:overScrollMode="never"
                android:paddingHorizontal="@dimen/dp_40"
                android:paddingTop="@dimen/dp_100"
                android:paddingBottom="@dimen/dp_20"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.lihang.ShadowLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/note_info_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_15"
        app:layout_constraintEnd_toEndOf="@id/note_folder_shadow"
        app:layout_constraintStart_toStartOf="@id/note_folder_shadow"
        app:layout_constraintTop_toBottomOf="@id/note_folder_shadow">

        <com.topstack.kilonotes.base.component.view.EllipsizedTextView
            android:id="@+id/note_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:ellipsize="middle"
            android:includeFontPadding="false"
            android:maxLines="3"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_42"
            app:layout_constraintEnd_toStartOf="@id/note_more_action"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="用户体验设计的商业价值是么ssssssssssss?" />

        <ImageView
            android:id="@+id/note_more_action"
            android:layout_width="@dimen/dp_45"
            android:layout_height="@dimen/dp_45"
            android:layout_marginTop="@dimen/dp_26"
            android:scaleType="fitXY"
            android:src="@drawable/note_icon_more_action"
            app:layout_constraintBottom_toBottomOf="@id/note_title"
            app:layout_constraintEnd_toEndOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/note_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_5"
            android:includeFontPadding="false"
            android:textColor="#FF727272"
            android:textSize="@dimen/sp_34"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/note_title"
            tools:text="2021-1-12   10:00" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
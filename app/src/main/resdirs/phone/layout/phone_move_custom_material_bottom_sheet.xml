<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    
    
    <ImageView
        android:id="@+id/create"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:src="@drawable/phone_note_material_new_classification_icon"
        android:layout_marginStart="@dimen/dp_48"
        app:layout_constraintTop_toTopOf="@id/close"
        app:layout_constraintStart_toStartOf="parent"/>


    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_126"
        android:layout_marginTop="@dimen/dp_60"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/phone_move_custom_material_bottom_sheet_title"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_54"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_63"
        android:layout_marginEnd="@dimen/dp_43"
        android:padding="@dimen/dp_5"
        android:src="@drawable/phone_redeem_code_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/other_classification_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_564"
        android:layout_marginTop="@dimen/dp_45"
        android:layout_marginBottom="@dimen/dp_90"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:scrollOrientation="vertical" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF2F2F7">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_370"
        android:background="@drawable/vip_store_header_background_image"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_91"
            android:text="@string/app_name"
            android:textColor="#FF131415"
            android:textSize="@dimen/sp_41"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_61"
            android:layout_height="@dimen/dp_61"
            android:layout_marginStart="@dimen/dp_51"
            android:src="@drawable/dialog_login_icon_back"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title" />


        <ImageView
            android:id="@+id/user_icon"
            android:layout_width="@dimen/dp_81"
            android:layout_height="@dimen/dp_81"
            android:layout_marginStart="@dimen/dp_54"
            android:layout_marginTop="@dimen/dp_34"
            android:padding="@dimen/dp_5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            tools:src="@drawable/cover4" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/user_nickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_34"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:text="@string/visitor"
            android:textColor="#FF131415"
            android:textSize="@dimen/sp_35"
            app:autoSizeMaxTextSize="@dimen/sp_35"
            app:autoSizeMinTextSize="@dimen/sp_20"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@id/login_description"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/user_icon"
            app:layout_constraintTop_toTopOf="@id/user_icon" />

        <ImageView
            android:id="@+id/user_vip_icon"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            app:layout_constraintBottom_toBottomOf="@id/user_icon"
            app:layout_constraintStart_toStartOf="@+id/user_nickname"
            app:layout_constraintTop_toBottomOf="@id/user_nickname"
            tools:src="@drawable/cover1" />

        <TextView
            android:id="@+id/login_description"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="3"
            android:text="@string/vip_store_vip_sync_vip_tip"
            android:textColor="#FF5F301A"
            android:textSize="@dimen/sp_30"
            app:layout_constraintBottom_toBottomOf="@id/user_vip_icon"
            app:layout_constraintEnd_toStartOf="@id/login_container"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/user_vip_icon"
            app:layout_constraintTop_toTopOf="@id/user_vip_icon"
            app:layout_constraintWidth_max="wrap" />


        <com.lihang.ShadowLayout
            android:id="@+id/login_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/dp_54"
            app:hl_cornerRadius="@dimen/dp_41"
            app:layout_constraintBottom_toBottomOf="@+id/user_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/user_icon">

            <TextView
                android:id="@+id/login_btn_text"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_81"
                android:layout_gravity="center"
                android:background="@drawable/phone_vip_store_login_container_background"
                android:gravity="center"
                android:minWidth="@dimen/dp_203"
                android:text="@string/login"
                android:textColor="#FFEDC79C"
                android:textSize="@dimen/sp_34" />
        </com.lihang.ShadowLayout>

        <TextView
            android:id="@+id/basic_vip_text"
            android:layout_width="@dimen/dp_398"
            android:layout_height="@dimen/dp_89"
            android:background="@drawable/bg_vip_store_membership_type"
            android:gravity="center"
            android:text="@string/basic_vip"
            android:textColor="#FF131415"
            android:textSize="@dimen/sp_35"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/ai_vip_text"
            android:layout_width="@dimen/dp_398"
            android:layout_height="@dimen/dp_89"
            android:gravity="center"
            android:text="@string/ai_points"
            android:textColor="#FF131415"
            android:textSize="@dimen/sp_35"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/basic_vip_text" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/price_list"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_29"
        android:clipToPadding="false"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_88"
        android:paddingEnd="@dimen/dp_88"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header"
        tools:layout_height="@dimen/dp_150"
        tools:layout_width="@dimen/dp_850" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/choose_pay_type_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="select_pay_type_rv" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/select_pay_type_rv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_54"
        android:layout_marginTop="@dimen/dp_25"
        android:layout_marginEnd="@dimen/dp_54"
        android:background="@drawable/vip_store_choose_pay_type_shape"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/price_list"
        tools:layout_height="@dimen/dp_180" />

    <TextView
        android:id="@+id/renewal_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_64"
        android:gravity="center"
        android:text="@string/vip_subs_price_des_without_introductory_year"
        android:textColor="#FF666666"
        android:textSize="@dimen/sp_34"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/select_pay_type_rv" />

    <com.lihang.ShadowLayout
        android:id="@+id/activate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:hl_cornerRadius="@dimen/dp_68"
        app:hl_shadowColor="#96EBCD99"
        app:hl_shadowHiddenLeft="true"
        app:hl_shadowHiddenRight="true"
        app:hl_shadowLimit="@dimen/dp_12"
        app:hl_shadowOffsetY="@dimen/dp_24"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/renewal_tip">

        <TextView
            android:id="@+id/activate_btn_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_115"
            android:layout_gravity="center"
            android:background="#FFEDC79C"
            android:gravity="center"
            android:minWidth="@dimen/dp_641"
            android:text="@string/vip_store_pay_button_default_text"
            android:textColor="#FF723D20"
            android:textSize="@dimen/sp_41" />
    </com.lihang.ShadowLayout>

    <TextView
        android:id="@+id/restore_subscription"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/vip_store_restore_purchase"
        android:textColor="#FF000000"
        android:textSize="@dimen/sp_34"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/activate" />

    <TextView
        android:id="@+id/policy"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="3"
        android:gravity="start"
        app:layout_constraintWidth_max="wrap"
        android:paddingHorizontal="@dimen/dp_5"
        android:paddingTop="@dimen/dp_12"
        android:scrollbars="vertical"
        android:text="@string/vip_store_read_and_agree_text"
        android:textColor="@color/vip_store_policy_and_agreement_color"
        android:textSize="@dimen/sp_34"
        app:layout_constraintEnd_toEndOf="@+id/vip_benefits_scroll_view"
        app:layout_constraintStart_toStartOf="@+id/vip_benefits_scroll_view"
        app:layout_constraintTop_toBottomOf="@+id/restore_subscription" />

    <ScrollView
        android:id="@+id/vip_benefits_scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_54"
        android:layout_marginEnd="@dimen/dp_54"
        android:layout_marginTop="@dimen/dp_28"
        android:layout_marginBottom="@dimen/dp_100"
        android:clipToPadding="false"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/policy"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/vip_store_vip_benefits_background">

                <TextView
                    android:id="@+id/vip_benefits"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_51"
                    android:text="@string/vip_shop_vip_benefits"
                    android:textColor="#FF723D20"
                    android:textSize="@dimen/sp_47"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/left_wheat_ear"
                    android:layout_width="@dimen/dp_41"
                    android:layout_height="@dimen/dp_84"
                    android:layout_marginTop="@dimen/dp_41"
                    android:layout_marginEnd="@dimen/dp_14"
                    android:src="@drawable/vip_store_left_wheat_ear_icon"
                    app:layout_constraintEnd_toStartOf="@+id/vip_benefits"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/right_wheat_ear"
                    android:layout_width="@dimen/dp_41"
                    android:layout_height="@dimen/dp_84"
                    android:layout_marginStart="@dimen/dp_14"
                    android:layout_marginTop="@dimen/dp_41"
                    android:src="@drawable/vip_store_right_wheat_ear_icon"
                    app:layout_constraintStart_toEndOf="@+id/vip_benefits"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/viP_star"
                    android:layout_width="@dimen/dp_147"
                    android:layout_height="@dimen/dp_24"
                    android:layout_marginTop="@dimen/dp_10"
                    android:src="@drawable/vip_store_star_icon"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/vip_benefits" />

                <GridView
                    android:id="@+id/vip_benefits_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_86"
                    android:layout_marginTop="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_86"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:columnWidth="@dimen/dp_230"
                    android:gravity="center"
                    android:horizontalSpacing="@dimen/dp_20"
                    android:listSelector="@android:color/transparent"
                    android:numColumns="auto_fit"
                    android:scrollbars="none"
                    android:stretchMode="columnWidth"
                    android:verticalSpacing="@dimen/dp_20"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/viP_star"
                    tools:layout_height="@dimen/dp_300"
                    tools:listitem="@layout/phone_item_vip_store_vip_benefits" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_24"
                android:background="@drawable/vip_store_vip_benefits_background">

                <TextView
                    android:id="@+id/document_content_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_48"
                    android:layout_marginTop="@dimen/dp_24"
                    android:text="@string/document_content"
                    android:textColor="#FF723D20"
                    android:textSize="@dimen/sp_34"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <GridView
                    android:id="@+id/document_content_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_86"
                    android:layout_marginTop="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_86"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:columnWidth="@dimen/dp_230"
                    android:gravity="center"
                    android:horizontalSpacing="@dimen/dp_20"
                    android:listSelector="@android:color/transparent"
                    android:numColumns="auto_fit"
                    android:scrollbars="none"
                    android:stretchMode="columnWidth"
                    android:verticalSpacing="@dimen/dp_20"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/document_content_title"
                    tools:layout_height="@dimen/dp_180"
                    tools:listitem="@layout/phone_item_vip_store_vip_benefits" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_24"
                android:background="@drawable/vip_store_vip_benefits_background">

                <TextView
                    android:id="@+id/document_editing_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_48"
                    android:layout_marginTop="@dimen/dp_24"
                    android:text="@string/document_editing"
                    android:textColor="#FF723D20"
                    android:textSize="@dimen/sp_34"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <GridView
                    android:id="@+id/document_editing_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_86"
                    android:layout_marginTop="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_86"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:columnWidth="@dimen/dp_230"
                    android:gravity="center"
                    android:horizontalSpacing="@dimen/dp_20"
                    android:numColumns="auto_fit"
                    android:scrollbars="none"
                    android:stretchMode="columnWidth"
                    android:verticalSpacing="@dimen/dp_20"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/document_editing_title"
                    tools:layout_height="@dimen/dp_180"
                    tools:listitem="@layout/phone_item_vip_store_vip_benefits" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_24"
                android:background="@drawable/vip_store_vip_benefits_background">

                <TextView
                    android:id="@+id/pencil_case_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_48"
                    android:layout_marginTop="@dimen/dp_24"
                    android:text="@string/pencil_case"
                    android:textColor="#FF723D20"
                    android:textSize="@dimen/sp_34"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <GridView
                    android:id="@+id/pencil_case_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_86"
                    android:layout_marginTop="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_86"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:columnWidth="@dimen/dp_230"
                    android:gravity="center"
                    android:horizontalSpacing="@dimen/dp_20"
                    android:numColumns="auto_fit"
                    android:scrollbars="none"
                    android:stretchMode="columnWidth"
                    android:verticalSpacing="@dimen/dp_20"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/pencil_case_title"
                    tools:layout_height="@dimen/dp_180"
                    tools:listitem="@layout/phone_item_vip_store_vip_benefits" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_24"
                android:layout_marginBottom="@dimen/dp_120"
                android:background="@drawable/vip_store_vip_benefits_background">

                <TextView
                    android:id="@+id/practical_tools_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_48"
                    android:layout_marginTop="@dimen/dp_24"
                    android:text="@string/practical_tools"
                    android:textColor="#FF723D20"
                    android:textSize="@dimen/sp_34"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <GridView
                    android:id="@+id/practical_tools_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_86"
                    android:layout_marginTop="@dimen/dp_24"
                    android:layout_marginEnd="@dimen/dp_86"
                    android:layout_marginBottom="@dimen/dp_27"
                    android:columnWidth="@dimen/dp_230"
                    android:gravity="center"
                    android:horizontalSpacing="@dimen/dp_20"
                    android:numColumns="auto_fit"
                    android:scrollbars="none"
                    android:stretchMode="columnWidth"
                    android:verticalSpacing="@dimen/dp_20"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/practical_tools_title"
                    tools:layout_height="@dimen/dp_180"
                    tools:listitem="@layout/phone_item_vip_store_vip_benefits" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg_color">

    <com.topstack.kilonotes.base.component.view.SideShadowLayout
        android:id="@+id/select_paper_shadow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="@color/base_bg_color"
        android:paddingBottom="@dimen/dp_22"
        app:layout_constraintTop_toTopOf="parent"
        app:shadow_direction="bottom"
        app:shadow_scope="@dimen/dp_22">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/select_paper"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/phone_fragment_create_notebook_page_top_background"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/back"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_63"
                android:layout_marginStart="@dimen/dp_49"
                android:src="@drawable/phone_image_crop_icon_cancel"
                app:layout_constraintBottom_toBottomOf="@id/title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/title" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_145"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"
                android:minHeight="@dimen/dp_59"
                android:paddingHorizontal="@dimen/dp_100"
                android:text="@string/create_note_paper_or_template"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_48"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/add_template"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_130"
                android:layout_marginTop="@dimen/dp_54"
                android:layout_marginBottom="@dimen/dp_46"
                android:breakStrategy="high_quality"
                android:gravity="center_horizontal"
                android:maxLines="2"
                android:text="@string/note_add_page_template_title"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/add_page"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title" />

            <View
                android:id="@+id/selected_flag"
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_10"
                android:layout_marginBottom="@dimen/dp_24"
                android:background="@drawable/phone_add_page_tab_layout_indicator"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/add_template"
                app:layout_constraintStart_toStartOf="@id/add_template" />

            <TextView
                android:id="@+id/add_page"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_54"
                android:layout_marginEnd="@dimen/dp_130"
                android:layout_marginBottom="@dimen/dp_46"
                android:breakStrategy="high_quality"
                android:gravity="center_horizontal"
                android:maxLines="2"
                android:text="@string/add_page_title"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_42"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/add_template"
                app:layout_constraintTop_toBottomOf="@id/title" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.topstack.kilonotes.base.component.view.SideShadowLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/template_list_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/select_paper_shadow">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/template_classification_select_list"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_200"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent" />

        <com.liaoinstan.springview.widget.SpringView
            android:id="@+id/spring_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/template_classification_select_list">

            <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
                android:id="@+id/template_list_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingHorizontal="@dimen/dp_24"
                android:visibility="visible"
                app:scrollOrientation="vertical" />
        </com.liaoinstan.springview.widget.SpringView>


        <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
            android:id="@+id/template_other_list_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/template_classification_select_list"
            app:scrollOrientation="vertical" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/mine_empty_data_bg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/template_classification_select_list">

            <ImageView
                android:id="@+id/empty_data_view"
                android:layout_width="@dimen/dp_450"
                android:layout_height="@dimen/dp_450"
                android:layout_marginTop="@dimen/dp_298"
                android:scaleType="centerInside"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/empty_data_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_48"
                android:text="@string/template_mine_empty_tips"
                android:textColor="@color/phone_template_mine_empty_tips"
                android:textSize="@dimen/sp_36"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/empty_data_view" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/paper_list_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/select_paper_shadow">
        <!--  使用了链 spread_inside为固定前后，中间等分  -->
        <ImageView
            android:id="@+id/color_white"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginStart="@dimen/dp_148"
            android:layout_marginTop="@dimen/dp_60"
            android:background="@drawable/phone_add_page_white_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@id/color_yellow"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_yellow"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:background="@drawable/phone_add_page_yellow_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@id/color_black"
            app:layout_constraintStart_toEndOf="@id/color_white"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_black"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:background="@drawable/phone_add_page_black_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@id/color_green"
            app:layout_constraintStart_toEndOf="@id/color_yellow"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_green"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:background="@drawable/phone_add_page_green_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@+id/color_purple"
            app:layout_constraintStart_toEndOf="@id/color_black"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_purple"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:background="@drawable/phone_add_page_purple_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toStartOf="@id/color_blue"
            app:layout_constraintStart_toEndOf="@id/color_green"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/color_blue"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginEnd="@dimen/dp_148"
            android:background="@drawable/phone_add_page_blue_background_selector"
            android:padding="@dimen/dp_18"
            android:src="@drawable/phone_add_page_color_selector"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/color_purple"
            app:layout_constraintTop_toTopOf="@id/color_white"
            tools:ignore="ContentDescription" />

        <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
            android:id="@+id/paper_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_60"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/color_white"
            app:scrollOrientation="vertical" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/template_download_dialog"
        layout="@layout/phone_include_template_download"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/vip_store_title_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_bar_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_127"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/close"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_33"
            android:padding="@dimen/dp_18"
            android:src="@drawable/vip_store_close"
            app:layout_constraintBottom_toBottomOf="@+id/store_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/store_title" />

        <TextView
            android:id="@+id/store_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_30"
            android:text="@string/kilonotes_store"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/handbook_list_rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_bar_container"
        android:orientation="vertical" />

</androidx.constraintlayout.widget.ConstraintLayout>
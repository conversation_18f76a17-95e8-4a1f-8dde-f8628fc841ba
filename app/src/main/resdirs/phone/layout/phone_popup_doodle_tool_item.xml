<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/menu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_30"
        android:paddingVertical="@dimen/dp_21"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_36"
        tools:background="@color/black"
        tools:text="@string/imagecrop_crop" />
</FrameLayout>
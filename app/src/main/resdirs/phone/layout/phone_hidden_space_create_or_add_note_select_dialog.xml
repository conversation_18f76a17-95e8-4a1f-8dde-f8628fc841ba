<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_480"
    android:layout_height="@dimen/dp_980">

    <TextView
        android:id="@+id/create"
        style="@style/PhoneHiddenSpaceCreateOrAddNoteDialogBtnStyle"
        android:text="@string/create"
        app:layout_constraintBottom_toTopOf="@id/add"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/add"
        style="@style/PhoneHiddenSpaceCreateOrAddNoteDialogBtnStyle"
        android:layout_marginTop="@dimen/dp_180"
        android:text="@string/base_button_add"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/create" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_144"
        android:background="@color/phone_pick_photo_head_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/chip_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_80"
            android:background="@drawable/phone_select_photo_button_group_shape"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/photo_tv"
                android:layout_width="@dimen/dp_232"
                android:layout_height="@dimen/dp_70"
                android:background="@drawable/phone_select_photo_button_stroke"
                android:gravity="center"
                android:text="@string/photo"
                android:textColor="@color/phone_select_photo_button_text"
                android:textSize="@dimen/sp_42" />

            <TextView
                android:id="@+id/album_tv"
                android:layout_width="@dimen/dp_232"
                android:layout_height="@dimen/dp_70"
                android:background="@drawable/phone_select_photo_button_stroke"
                android:gravity="center"
                android:text="@string/album"
                android:textColor="@color/phone_select_photo_button_text"
                android:textSize="@dimen/sp_42" />
        </LinearLayout>

        <TextView
            android:id="@+id/selected_category_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_24"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="最近项目" />

        <ImageView
            android:id="@+id/selected_category_icon"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_20"
            android:src="@drawable/phone_select_photo_category"
            app:layout_constraintBottom_toBottomOf="@id/selected_category_name"
            app:layout_constraintStart_toEndOf="@id/selected_category_name"
            app:layout_constraintTop_toTopOf="@id/selected_category_name" />


        <ImageView
            android:id="@+id/back_iv"
            android:layout_width="@dimen/dp_54"
            android:layout_height="@dimen/dp_54"
            android:layout_marginStart="@dimen/dp_48"
            android:src="@drawable/phone_select_photo_icon_back"
            app:layout_constraintBottom_toBottomOf="@+id/selected_category_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/selected_category_name"
            tools:ignore="ContentDescription" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/photo_rv"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header" />
</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layoutDescription="@xml/phone_fragment_first_guide_page_scene">

    <TextView
        android:id="@+id/slice_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_132"
        android:layout_marginBottom="@dimen/dp_135"
        android:text="@string/phone_first_guide_page_title"
        android:textAlignment="center"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_70"
        app:layout_constraintBottom_toTopOf="@id/handbook"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/template_travel_summary"
        android:layout_width="@dimen/dp_270"
        android:layout_height="@dimen/dp_360"
        android:layout_marginTop="@dimen/dp_300"
        android:layout_marginEnd="@dimen/dp_60"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_first_guide_page_travel_summary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/handbook"
        android:layout_width="@dimen/dp_680"
        android:layout_height="@dimen/dp_1100"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_first_guide_page_handbook"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/template_red"
        android:layout_width="@dimen/dp_400"
        android:layout_height="@dimen/dp_460"
        android:layout_marginTop="@dimen/dp_470"
        android:layout_marginEnd="@dimen/dp_112"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_first_guide_page_template_red"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/template_green"
        android:layout_width="@dimen/dp_390"
        android:layout_height="@dimen/dp_500"
        android:layout_marginStart="@dimen/dp_50"
        android:layout_marginTop="@dimen/dp_540"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_first_guide_page_template_green_tags"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/palette"
        android:layout_width="@dimen/dp_330"
        android:layout_height="@dimen/dp_120"
        android:layout_marginStart="@dimen/dp_120"
        android:layout_marginTop="@dimen/dp_140"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_first_guide_page_palette"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

    <ImageView
        android:id="@+id/tools"
        android:layout_width="@dimen/dp_1000"
        android:layout_height="@dimen/dp_220"
        android:layout_marginTop="@dimen/dp_930"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_first_guide_page_tools"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/handbook" />

</androidx.constraintlayout.motion.widget.MotionLayout>
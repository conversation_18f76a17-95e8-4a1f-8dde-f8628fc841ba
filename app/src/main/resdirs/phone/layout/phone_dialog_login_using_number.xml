<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dialog_outline_create_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_144"
        android:layout_marginTop="@dimen/dp_30"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginStart="@dimen/dp_45"
            android:layout_marginTop="@dimen/dp_48"
            android:padding="@dimen/dp_10"
            android:src="@drawable/dialog_login_icon_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <!-- 关闭按钮 -->
        <ImageView
            android:id="@+id/close"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_48"
            android:padding="@dimen/dp_10"
            android:src="@drawable/dialog_login_icon_close"
            app:layout_constraintBottom_toBottomOf="@+id/back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/back"
            tools:ignore="ContentDescription" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/login_with_number_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_to_kilonotes"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_48"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/back" />

        <TextView
            android:id="@+id/bind_new_phone_number_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bind_new_phone_number"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_24"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/back" />

        <TextView
            android:id="@+id/bind_new_phone_number_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_35"
            android:text="@string/bind_new_phone_number_tip"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_23"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bind_new_phone_number_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/phone_number_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/phone_number_title"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_40"
        app:layout_constraintStart_toStartOf="@+id/phone_number_container"
        app:layout_constraintTop_toBottomOf="@+id/header" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/phone_number_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_26"
        android:background="@drawable/login_phone_number_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/phone_number_title"
        app:layout_constraintWidth_percent="0.88">

        <TextView
            android:id="@+id/area_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_24"
            android:text="@string/china_area_code"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_34"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.14640884" />

        <!-- 竖线 -->
        <View
            android:id="@+id/divider_vertical"
            android:layout_width="@dimen/dp_2"
            android:layout_height="0dp"
            android:background="@color/thumbnail_list_right_stroke"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.55"
            app:layout_constraintStart_toStartOf="@+id/guideline_1"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/phone_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_23"
            android:layout_marginTop="@dimen/dp_23"
            android:layout_marginBottom="@dimen/dp_23"
            android:background="@null"
            android:hint="@string/phone_number_hint_tip"
            android:inputType="number"
            android:maxLength="11"
            android:maxLines="1"
            android:textColor="@color/page_overview_black"
            android:textColorHint="@color/backup_subtitle_color"
            android:textSize="@dimen/sp_34"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/divider_vertical"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/email_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_23"
            android:layout_marginTop="@dimen/dp_23"
            android:layout_marginBottom="@dimen/dp_23"
            android:background="@null"
            android:hint="@string/email_number_hint_tip"
            android:inputType="textEmailAddress"
            android:maxLines="1"
            android:textColor="@color/page_overview_black"
            android:textColorHint="@color/backup_subtitle_color"
            android:textSize="@dimen/sp_34"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/phone_number_error_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/phone_number_error_message"
        android:textColor="#FF1700"
        android:textSize="@dimen/sp_34"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/phone_number_container"
        app:layout_constraintTop_toBottomOf="@+id/phone_number_container" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/verification_code_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:background="@drawable/login_phone_number_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/phone_number_container"
        app:layout_constraintWidth_percent="0.88">

        <EditText
            android:id="@+id/verification_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_23"
            android:layout_marginBottom="@dimen/dp_23"
            android:background="@null"
            android:inputType="number"
            android:maxLength="6"
            android:maxLines="1"
            android:textColor="@color/page_overview_black"
            android:textColorHint="@color/backup_subtitle_color"
            android:textSize="@dimen/sp_34"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/divider_vertical_2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/verification_code_hint"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/verification_code_hint_tip"
            android:textColor="@color/backup_subtitle_color"
            android:textSize="@dimen/sp_34"
            android:maxLines="1"
            app:autoSizeMaxTextSize="@dimen/sp_34"
            app:autoSizeMinTextSize="@dimen/sp_24"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform"
            app:layout_constraintStart_toStartOf="@+id/verification_code"
            app:layout_constraintEnd_toEndOf="@+id/verification_code"
            app:layout_constraintBottom_toBottomOf="@+id/verification_code"
            app:layout_constraintTop_toTopOf="@+id/verification_code" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_70_percent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.7" />

        <!-- 竖线 -->
        <View
            android:id="@+id/divider_vertical_2"
            android:layout_width="@dimen/dp_2"
            android:layout_height="@dimen/dp_33"
            android:background="@color/thumbnail_list_right_stroke"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/guideline_70_percent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/send_verification_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_9"
            android:layout_marginTop="@dimen/dp_18"
            android:layout_marginEnd="@dimen/dp_9"
            android:layout_marginBottom="@dimen/dp_18"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/send_verification_code"
            android:textColor="@color/page_overview_black"
            android:textSize="@dimen/sp_34"
            app:autoSizeMaxTextSize="@dimen/sp_34"
            app:autoSizeMinTextSize="@dimen/sp_24"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/divider_vertical_2"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/verification_code_error_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/verification_code_error_message"
        android:textColor="#FF1700"
        android:textSize="@dimen/sp_34"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/verification_code_container"
        app:layout_constraintTop_toBottomOf="@+id/verification_code_container" />

    <com.lihang.ShadowLayout
        android:id="@+id/login_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_50"
        app:hl_cornerRadius="@dimen/dp_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/login"
            android:layout_width="@dimen/dp_480"
            android:layout_height="@dimen/dp_96"
            android:layout_gravity="center"
            android:background="@color/text_disable"
            android:gravity="center"
            android:text="@string/login"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48" />
    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
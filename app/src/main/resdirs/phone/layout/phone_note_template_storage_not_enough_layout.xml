<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:layout_width="@dimen/dp_416"
        android:layout_height="@dimen/dp_313"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_48"
        android:src="@drawable/note_material_reload"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/empty_data_txt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_48"
        android:layout_marginBottom="@dimen/dp_48"
        android:gravity="center_horizontal"
        android:text="@string/storage_not_enough_to_download_resource"
        android:textColor="@color/backup_subtitle_color"
        android:textSize="@dimen/sp_30" />

</LinearLayout>
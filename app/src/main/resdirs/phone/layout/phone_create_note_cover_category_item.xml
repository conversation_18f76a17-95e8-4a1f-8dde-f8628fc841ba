<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/cover_category_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/cover_list_recycle_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cover_category_name"
        app:scrollOrientation="horizontal" />

    <View
        android:id="@+id/split_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginStart="@dimen/dp_48"
        android:background="@drawable/pad_create_note_split_line"
        app:layout_constraintTop_toBottomOf="@id/cover_list_recycle_view" />

</androidx.constraintlayout.widget.ConstraintLayout>
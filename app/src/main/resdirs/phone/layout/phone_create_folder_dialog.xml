<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:text="@string/folder_rename"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.topstack.kilonotes.base.component.view.CommonInputLayout
        android:id="@+id/common_input"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_124"
        android:layout_marginHorizontal="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_118"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/title"
        app:textColor="@color/text_primary"
        app:textSize="@dimen/sp_42" />

    <TextView
        android:id="@+id/confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_70"
        android:paddingHorizontal="@dimen/dp_100"
        android:paddingVertical="@dimen/dp_30"
        android:text="@string/confirm"
        android:textColor="@color/hint_text"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/common_input" />


</androidx.constraintlayout.widget.ConstraintLayout>
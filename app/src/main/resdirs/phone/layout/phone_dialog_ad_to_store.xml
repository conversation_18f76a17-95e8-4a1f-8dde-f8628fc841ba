<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent">

   <com.lihang.ShadowLayout
       android:id="@id/container"
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       app:layout_constraintTop_toTopOf="parent"
       android:layout_marginTop="@dimen/dp_211"
       app:hl_cornerRadius="@dimen/dp_30"
       android:background="@drawable/phone_dialog_rate_background">
       <androidx.constraintlayout.widget.ConstraintLayout
           android:layout_width="match_parent"
           android:layout_height="wrap_content">
           <ImageView
               android:id="@+id/middle_background"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:scaleType="fitStart"
               android:src="@drawable/phone_dialog_ad_to_store_middle_background"
               app:layout_constraintTop_toTopOf="parent"/>
           <TextView
               android:id="@+id/title"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:text="@string/sticker_download_success"
               android:textSize="@dimen/sp_54"
               android:textStyle="bold"
               android:textColor="@color/black"
               android:maxLines="2"
               android:ellipsize="end"
               android:gravity="center"
               android:paddingHorizontal="@dimen/dp_120"
               app:layout_constraintTop_toTopOf="parent"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintEnd_toEndOf="parent"
               android:layout_marginTop="@dimen/dp_156"/>
           <TextView
               android:id="@+id/content"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:gravity="center"
               android:paddingHorizontal="@dimen/dp_120"
               android:textColor="@color/black"
               android:textSize="@dimen/sp_42"
               android:text="@string/ad_to_store_dialog_content"
               app:layout_constraintTop_toBottomOf="@id/title"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintEnd_toEndOf="parent"
               android:layout_marginTop="@dimen/dp_36"/>
           <androidx.constraintlayout.widget.ConstraintLayout
               android:id="@+id/go_to_store"
               android:layout_width="@dimen/dp_0"
               android:layout_height="@dimen/dp_120"
               android:layout_marginTop="@dimen/dp_70"
               android:paddingHorizontal="@dimen/dp_60"
               android:paddingVertical="@dimen/dp_26"
               android:layout_marginHorizontal="@dimen/dp_60"
               android:background="@drawable/phone_dialog_ad_to_store_confirm_background"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintTop_toBottomOf="@+id/content"
               app:layout_constraintBottom_toBottomOf="parent"
               android:layout_marginBottom="@dimen/dp_60">

               <TextView
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   android:layout_marginTop="@dimen/dp_20"
                   android:layout_marginBottom="@dimen/dp_20"
                   android:gravity="center"
                   android:text="@string/buy_vip"
                   android:textColor="@color/white"
                   android:textSize="@dimen/sp_42"
                   android:textStyle="bold"
                   android:maxLines="1"
                   android:ellipsize="end"
                   app:layout_constraintBottom_toBottomOf="parent"
                   app:layout_constraintEnd_toEndOf="parent"
                   app:layout_constraintStart_toStartOf="parent"
                   app:layout_constraintTop_toTopOf="parent" />
           </androidx.constraintlayout.widget.ConstraintLayout>
       </androidx.constraintlayout.widget.ConstraintLayout>
   </com.lihang.ShadowLayout>

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_98"
        android:layout_height="@dimen/dp_98"
        android:layout_marginTop="@dimen/dp_32"
        android:src="@drawable/phone_dialog_ad_to_store_close"
        app:layout_constraintTop_toBottomOf="@id/container"
        app:layout_constraintStart_toStartOf="@id/container"
        app:layout_constraintEnd_toEndOf="@id/container"/>


    <ImageView
        android:id="@+id/top_background"
        android:layout_width="@dimen/dp_748"
        android:layout_height="@dimen/dp_326"
        android:src="@drawable/phone_dialog_ad_to_store_top_background"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
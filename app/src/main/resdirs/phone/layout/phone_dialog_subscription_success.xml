<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_840"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_bg"
        android:layout_width="@dimen/dp_840"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_220"
        android:background="@drawable/dialog_subscription_success_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_210"
            android:breakStrategy="simple"
            android:gravity="center"
            android:text="@string/dialog_subscription_success_title"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_54"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:breakStrategy="simple"
            android:gravity="center"
            android:text="@string/dialog_subscription_success_desc"
            android:textColor="#FF999999"
            android:textSize="@dimen/sp_40"
            app:layout_constraintEnd_toEndOf="@id/title"
            app:layout_constraintStart_toStartOf="@id/title"
            app:layout_constraintTop_toBottomOf="@id/title" />

        <TextView
            android:id="@+id/close"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_120"
            android:layout_marginHorizontal="@dimen/dp_90"
            android:layout_marginTop="@dimen/dp_100"
            android:layout_marginBottom="@dimen/dp_100"
            android:background="@drawable/dialog_subscription_success_btn_bg"
            android:gravity="center"
            android:text="@string/know"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/desc" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/header"
        android:layout_width="@dimen/dp_500"
        android:layout_height="@dimen/dp_372"
        android:background="@drawable/vip_store_subscription_success_header"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
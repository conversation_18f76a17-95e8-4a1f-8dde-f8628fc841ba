<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_height="@dimen/dp_708"
    tools:layout_width="@dimen/dp_840">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/dialog_template_download_background_shape">

        <ImageView
            android:id="@+id/logo"
            android:layout_width="@dimen/dp_620"
            android:layout_height="@dimen/dp_415"
            android:layout_marginTop="@dimen/dp_33"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_25"
            android:gravity="center"
            android:text="@string/loading"
            android:textColor="@color/template_download"
            android:textSize="@dimen/sp_36"
            app:layout_constraintBottom_toTopOf="@id/line"
            app:layout_goneMarginBottom="@dimen/dp_100" />

        <TextView
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/note_tool_stroke"
            app:layout_constraintBottom_toTopOf="@id/cancel" />

        <TextView
            android:id="@+id/cancel"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_127"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_48"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
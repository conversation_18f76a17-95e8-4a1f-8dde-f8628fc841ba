<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/item_photo"
        android:layout_width="@dimen/dp_358"
        android:layout_height="@dimen/dp_425"
        android:scaleType="centerCrop"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/photo_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@id/item_photo"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_24"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_30" />

    <TextView
        android:id="@+id/photo_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/photo_count"
        android:layout_alignStart="@id/photo_count"
        android:maxLines="1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_36" />

</RelativeLayout>
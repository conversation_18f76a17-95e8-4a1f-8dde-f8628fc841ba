<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/note_cover"
        android:layout_width="@dimen/dp_96"
        android:layout_height="@dimen/dp_126"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_40"
        android:layout_marginBottom="@dimen/dp_35"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/note_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:text="用户体验设计的商业价值是什么? 用户体验设计的商业价值是什么?"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_38"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/confirm"
        app:layout_constraintStart_toEndOf="@+id/note_cover"
        app:layout_constraintTop_toTopOf="@+id/note_cover" />

    <TextView
        android:id="@+id/note_subheading"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_40"
        android:layout_marginBottom="@dimen/dp_8"
        android:maxLines="2"
        android:text="上次备份时间：2021年1月12日 10:00"
        android:textColor="@color/backup_item_time"
        android:textSize="@dimen/sp_32"
        app:layout_constraintBottom_toBottomOf="@+id/note_cover"
        app:layout_constraintEnd_toStartOf="@+id/confirm"
        app:layout_constraintStart_toStartOf="@+id/note_title" />

    <com.topstack.kilonotes.base.component.view.ProgressButton
        android:id="@+id/confirm"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_60"
        android:layout_marginEnd="@dimen/dp_40"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_4"
        android:textColor="@color/hint_text"
        android:textSize="@dimen/sp_36"
        app:autoSizeMaxTextSize="@dimen/sp_36"
        app:autoSizeMinTextSize="@dimen/sp_16"
        app:autoSizeStepGranularity="@dimen/sp_1"
        app:autoSizeTextType="uniform"
        app:buttonColor="@color/transparent"
        app:cornerRadius="@dimen/dp_30"
        app:layout_constraintBottom_toTopOf="@+id/delete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:max_progress="100"
        app:min_progress="0"
        app:progressBackColor="@color/progress_button_second_background"
        app:progressColor="@color/progress_bar"
        tools:text="Download" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/delete"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_60"
        android:background="@drawable/backup_space_download_note_delete_bg"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_4"
        android:text="@string/delete"
        android:textColor="@color/hidden_space_notice_kind_tips_color"
        android:textSize="@dimen/sp_36"
        app:autoSizeMaxTextSize="@dimen/sp_36"
        app:autoSizeMinTextSize="@dimen/sp_16"
        app:autoSizeStepGranularity="@dimen/sp_1"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/confirm"
        app:layout_constraintTop_toBottomOf="@+id/confirm" />
</androidx.constraintlayout.widget.ConstraintLayout>
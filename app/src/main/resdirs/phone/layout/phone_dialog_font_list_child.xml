
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
android:orientation="vertical"
android:layout_width="match_parent"
android:layout_height="wrap_content">
<TextView
    android:id="@+id/font_child_list"
    android:layout_width="0dp"
    android:layout_height="@dimen/dp_120"
    android:gravity="center_vertical"
    android:paddingStart="@dimen/dp_88"
    android:textSize="@dimen/sp_42"
    android:text="No data"
    android:maxLines="2"
    android:ellipsize="end"
    android:textColor="@color/black"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toStartOf="@id/font_child_state"/>
<ImageView
    android:id="@+id/font_child_state"
    android:layout_width="@dimen/dp_40"
    android:layout_height="@dimen/dp_40"
    android:scaleType="fitCenter"
    android:layout_marginEnd="@dimen/dp_77"
    app:layout_constraintTop_toTopOf="@id/font_child_list"
    app:layout_constraintBottom_toBottomOf="@id/font_child_list"
    app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
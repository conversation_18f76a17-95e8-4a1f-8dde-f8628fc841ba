<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/background"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_728"
        android:layout_marginHorizontal="@dimen/dp_82"
        android:layout_marginBottom="@dimen/dp_90"
        android:src="@drawable/dialog_instant_alpha_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:layout_marginBottom="@dimen/dp_2"
        android:src="@drawable/dialog_instant_alpha_close"
        app:layout_constraintEnd_toEndOf="@id/background"
        app:layout_constraintTop_toTopOf="@id/title" />

    <ImageView
        android:id="@+id/title"
        android:layout_width="@dimen/dp_808"
        android:layout_height="@dimen/dp_257"
        android:layout_marginHorizontal="@dimen/dp_55"
        android:layout_marginBottom="@dimen/dp_537"
        android:src="@drawable/phone_dialog_instant_title_en"
        app:layout_constraintBottom_toBottomOf="@id/background"
        app:layout_constraintEnd_toEndOf="@id/background"
        app:layout_constraintStart_toStartOf="@id/background" />

    <ImageView
        android:id="@+id/vip_exclusive"
        android:layout_width="@dimen/dp_160"
        android:layout_height="@dimen/dp_116"
        android:layout_marginStart="@dimen/dp_39"
        android:layout_marginBottom="@dimen/dp_181"
        android:src="@drawable/dialog_instant_alpha_vip_exclusive_en"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/background" />

    <ImageView
        android:id="@+id/limited_free"
        android:layout_width="@dimen/dp_206"
        android:layout_height="@dimen/dp_106"
        android:src="@drawable/dialog_instant_alpha_limited_free_en"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/title" />

    <ImageView
        android:id="@id/guide_gif"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_marginHorizontal="@dimen/dp_110"
        android:layout_marginBottom="@dimen/dp_78"
        app:layout_constraintBottom_toBottomOf="@id/background"
        app:layout_constraintEnd_toEndOf="@id/background"
        app:layout_constraintStart_toStartOf="@id/background"
        app:layout_constraintTop_toBottomOf="@id/limited_free" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<com.topstack.kilonotes.base.component.view.BottomDraggableLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:contentViewId="@id/content"
    app:dragViewId="@id/drag_view"
    app:invariableBottomViewId="@id/bottom_view_group"
    app:invariableTopViewId="@id/title_content"
    app:variableListViewId="@id/material_list_pager"
    app:variableViewId="@id/middle_view_content">

    <View
        android:id="@+id/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/drag_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/shadow"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_20"
            android:background="@drawable/phone_select_color_window_top_shadow"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:background="@drawable/phone_note_material_drag_view_shape"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/shadow" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:background="@color/white"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/drag">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/title_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/cancel"
                android:layout_width="@dimen/dp_230"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_54"
                android:layout_marginTop="@dimen/dp_8"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@string/cancel"
                android:textColor="@color/progress_bar"
                android:textSize="@dimen/sp_48"
                app:layout_constraintEnd_toStartOf="@id/title"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="@dimen/dp_450"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:ellipsize="end"
                android:gravity="center_horizontal"
                android:maxLines="2"
                android:text="@string/material_library"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/sp_51"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/cancel" />

            <ImageView
                android:id="@+id/material_tool"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:src="@drawable/phone_note_material_tool_icon"
                android:layout_marginEnd="@dimen/dp_48"
                app:layout_constraintBottom_toBottomOf="@id/cancel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/cancel"/>

            <View
                android:id="@+id/sign"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:background="@drawable/sign_red_bg"
                app:layout_constraintStart_toEndOf="@id/material_tool"
                app:layout_constraintTop_toTopOf="@id/material_tool" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/custom_material_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layout_constraintBottom_toBottomOf="@id/material_type_list"
                app:layout_constraintEnd_toStartOf="@id/material_type_list"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/material_type_list"
                tools:layout_height="@dimen/dp_80" />

            <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
                android:id="@+id/material_type_list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_60"
                android:nestedScrollingEnabled="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/custom_material_type"
                app:layout_constraintTop_toBottomOf="@id/title"
                app:scrollOrientation="horizontal"
                tools:layout_height="@dimen/dp_80" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/middle_view_content"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            app:layout_constraintBottom_toTopOf="@id/bottom_view_group"
            app:layout_constraintTop_toBottomOf="@id/title_content">

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/material_list_pager"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/reload_group"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@id/material_list_pager"
                app:layout_constraintEnd_toEndOf="@id/material_list_pager"
                app:layout_constraintStart_toStartOf="@id/material_list_pager"
                app:layout_constraintTop_toTopOf="@id/material_list_pager">

                <ImageView
                    android:id="@+id/reload_image"
                    android:layout_width="@dimen/dp_400"
                    android:layout_height="@dimen/dp_385"
                    android:layout_marginBottom="@dimen/dp_120"
                    android:scaleType="centerCrop"
                    android:src="@drawable/note_material_reload"
                    app:layout_constraintBottom_toTopOf="@id/reload_text"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/reload_text"
                    android:layout_width="@dimen/dp_706"
                    android:layout_height="@dimen/dp_100"
                    android:layout_marginVertical="@dimen/dp_24"
                    android:background="@drawable/note_material_reload_button_background"
                    android:gravity="center"
                    android:text="@string/reload"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_46"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/reload_image" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/bottom_view_group"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/phone_note_material_bottom_group_bg"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible">

            <TextView
                android:id="@+id/bottom_hint_text"
                android:layout_width="@dimen/dp_1005"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_36"
                android:gravity="center"
                android:text="@string/vip_free_to_use"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/sp_48"
                android:textStyle="bold" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_background"
                android:layout_width="@dimen/dp_706"
                android:layout_height="@dimen/dp_100"
                android:layout_marginVertical="@dimen/dp_25"
                android:background="@drawable/phone_note_material_confirm_button_background_selector">

                <ImageView
                    android:id="@+id/bottom_text_ad_tag"
                    android:layout_width="@dimen/dp_54"
                    android:layout_height="@dimen/dp_54"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:src="@drawable/sticker_or_template_ad_tag"
                    app:layout_constraintBottom_toBottomOf="@id/bottom_text"
                    app:layout_constraintEnd_toStartOf="@id/bottom_text"
                    app:layout_constraintTop_toTopOf="@id/bottom_text" />

                <TextView
                    android:id="@+id/bottom_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/download"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_46"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/bottom_text_tag"
                    android:layout_width="@dimen/dp_80"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_8"
                    android:src="@drawable/sticker_icon_pro"
                    app:layout_constraintStart_toEndOf="@id/bottom_text"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.topstack.kilonotes.base.component.view.BottomDraggableLayout>

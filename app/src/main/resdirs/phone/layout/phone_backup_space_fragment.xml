<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraint"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/backup_space_bg_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_bar_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_140"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/backup_space_close"
            android:layout_width="@dimen/dp_59"
            android:layout_height="@dimen/dp_59"
            android:layout_marginStart="@dimen/dp_45"
            android:layout_marginBottom="@dimen/dp_40"
            android:padding="@dimen/dp_5"
            android:src="@drawable/phone_backup_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/upload_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_28"
            android:background="@drawable/backup_space_common_btn_bg"
            android:paddingHorizontal="@dimen/dp_36"
            android:paddingVertical="@dimen/dp_14"
            android:text="@string/backup_space_uploading"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_36"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/backup_space_close"
            app:layout_constraintEnd_toStartOf="@+id/setting_icon"
            app:layout_constraintTop_toTopOf="@+id/backup_space_close" />

        <TextView
            android:id="@+id/setting_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_48"
            android:background="@drawable/backup_space_comme_btn_bg_selector"
            android:paddingHorizontal="@dimen/dp_36"
            android:paddingVertical="@dimen/dp_14"
            android:text="@string/setting"
            android:textColor="@color/backup_space_setting_text_color"
            android:textSize="@dimen/sp_36"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/backup_space_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/backup_space_close" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/backup_space_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_54"
        android:text="@string/backup_space"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_44"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/top_bar_container" />

    <TextView
        android:id="@+id/backup_used_space"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/backup_space_used_space"
        android:textSize="@dimen/sp_34"
        app:layout_constraintStart_toStartOf="@+id/backup_space_title"
        app:layout_constraintTop_toBottomOf="@+id/backup_space_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/devices_tab"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_37"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backup_used_space" />

    <TextView
        android:id="@+id/backup_description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_33"
        android:text="@string/backup_space_description"
        android:textColor="@color/backup_item_time"
        android:textSize="@dimen/sp_32"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/devices_tab"
        app:layout_constraintTop_toBottomOf="@+id/devices_tab" />

    <com.topstack.kilonotes.base.shadow.FixShadowLayout
        android:id="@+id/backup_notes_list_shadow"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_15"
        app:hl_cornerRadius="@dimen/dp_17"
        app:hl_layoutBackground="@color/white"
        app:hl_shadowColor="@color/backup_space_notes_shadow"
        app:hl_shadowHiddenBottom="true"
        app:hl_shadowHiddenLeft="true"
        app:hl_shadowHiddenRight="true"
        app:hl_shadowLimit="@dimen/dp_15"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backup_description">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/backup_notes_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.topstack.kilonotes.base.shadow.FixShadowLayout>

    <ImageView
        android:id="@+id/note_list_empty"
        android:layout_width="@dimen/dp_430"
        android:layout_height="@dimen/dp_430"
        android:src="@drawable/note_icon_backup_space_empty_list"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backup_description" />

    <ImageView
        android:id="@+id/open_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/backup_space_open_bg"
        app:layout_constraintBottom_toTopOf="@+id/open_vip_describe"
        app:layout_constraintDimensionRatio="833:405"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/open_vip_describe"
        android:layout_width="@dimen/dp_784"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_65"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/dp_10"
        android:text="@string/backup_space_open_vip_describe"
        android:textColor="@color/backup_item_time"
        android:textSize="@dimen/sp_38"
        app:layout_constraintBottom_toTopOf="@+id/purchase"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/open_bg" />


    <com.topstack.kilonotes.base.shadow.FixShadowLayout
        android:id="@+id/purchase"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_300"
        app:hl_cornerRadius="@dimen/dp_80"
        app:hl_layoutBackground="@color/hint_text"
        app:hl_shadowColor="@color/hint_text"
        app:hl_shadowLimit="@dimen/dp_15"
        app:hl_shadowOffsetY="@dimen/dp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/open_vip_describe">

        <TextView
            android:id="@+id/login_or_purchase_btn"
            android:layout_width="@dimen/dp_580"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_22"
            android:gravity="center"
            android:text="@string/vip_store_pay_button_default_text"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48" />

    </com.topstack.kilonotes.base.shadow.FixShadowLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/backup_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="devices_tab,backup_description,backup_notes_list_shadow,backup_used_space,backup_space_title" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/purchase_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="purchase,open_vip_describe,open_bg" />

</androidx.constraintlayout.widget.ConstraintLayout>
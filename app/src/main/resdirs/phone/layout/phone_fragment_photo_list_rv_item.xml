<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_267"
    android:layout_height="@dimen/dp_267">

    <ImageView
        android:id="@+id/item_photo"
        android:layout_width="@dimen/dp_267"
        android:layout_height="@dimen/dp_267"
        android:scaleType="centerCrop"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/camera_icon"
        android:layout_width="@dimen/dp_81"
        android:layout_height="@dimen/dp_72"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/dp_20"
        android:scaleType="centerCrop"
        android:src="@drawable/phone_icon_camera"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/camera_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_160"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:text="@string/take_photo_tips"
        android:textColor="@color/start_take_photo"
        android:textSize="@dimen/sp_36" />


</FrameLayout>
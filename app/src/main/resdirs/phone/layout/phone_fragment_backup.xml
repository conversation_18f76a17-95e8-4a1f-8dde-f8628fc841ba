<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_144"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_27"
            android:text="@string/data_backup"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_59"
            android:layout_height="@dimen/dp_59"
            android:layout_marginStart="@dimen/dp_45"
            android:padding="@dimen/dp_5"
            android:src="@drawable/phone_backup_back"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/empty"
        android:layout_width="@dimen/dp_480"
        android:layout_height="@dimen/dp_480"
        android:scaleType="centerInside"
        android:src="@drawable/note_backup_empty_background"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/list"
        app:layout_constraintEnd_toEndOf="@id/list"
        app:layout_constraintStart_toStartOf="@id/list"
        app:layout_constraintTop_toTopOf="@id/list"
        app:layout_constraintVertical_bias="0.4"
        tools:ignore="ContentDescription" />

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_48"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@id/footer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header"
        app:scrollOrientation="vertical"
        tools:listitem="@layout/phone_fragment_backup_item" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="@dimen/sp_48"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:id="@+id/select_all_group"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            app:layout_constraintBottom_toBottomOf="@id/select_all_checkbox"
            app:layout_constraintEnd_toEndOf="@id/select_all_text"
            app:layout_constraintStart_toStartOf="@+id/select_all_checkbox"
            app:layout_constraintTop_toTopOf="@id/select_all_checkbox" />

        <ImageView
            android:id="@+id/select_all_checkbox"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_48"
            android:src="@drawable/dialog_backup_checkbox"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/select_all_text"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:text="@string/select_all"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_36"
            app:layout_constraintBottom_toBottomOf="@id/select_all_checkbox"
            app:layout_constraintEnd_toStartOf="@id/confirm"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/select_all_checkbox"
            app:layout_constraintTop_toTopOf="@id/select_all_checkbox" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_30"
            android:background="@drawable/note_backup_confirm_button_background_selector"
            android:gravity="center"
            android:maxWidth="@dimen/dp_580"
            android:minWidth="@dimen/dp_580"
            android:minHeight="@dimen/dp_120"
            android:paddingHorizontal="@dimen/dp_24"
            android:paddingVertical="@dimen/dp_24"
            android:text="@string/backup_now"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <TextView
        android:id="@+id/price_text"
        android:layout_width="@dimen/dp_840"
        android:layout_height="@dimen/dp_132"
        android:background="@drawable/phone_pay_item_price_text_bg"
        android:gravity="center"
        android:paddingStart="@dimen/dp_100"
        android:paddingEnd="@dimen/dp_50"
        android:text="@string/vip_duration_loading"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_48"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/selector_point"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginStart="@dimen/dp_42"
        android:background="@drawable/phone_vip_store_pay_type_point_selector"
        app:layout_constraintBottom_toBottomOf="@id/price_text"
        app:layout_constraintStart_toStartOf="@id/price_text"
        app:layout_constraintTop_toTopOf="@id/price_text" />

    <TextView
        android:id="@+id/tag_text"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_55"
        android:layout_marginStart="@dimen/dp_66"
        android:background="@drawable/phone_vip_price_tag_bg"
        android:duplicateParentState="true"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_24"
        android:paddingTop="@dimen/dp_2"
        android:text="@string/vip_price_discounts_tag"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_33"
        android:translationY="@dimen/dp_20"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/price_text"
        app:layout_constraintEnd_toEndOf="@id/price_text"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/price_text"
        app:layout_constraintWidth_max="wrap"
        tools:text="月均价超值"
        tools:visibility="visible" />

</merge>
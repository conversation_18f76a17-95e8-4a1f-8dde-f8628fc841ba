<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_166">

    <TextView
        android:id="@+id/text_size"
        android:layout_width="@dimen/dp_100"
        android:layout_height="@dimen/dp_64"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/phone_note_tool_text_size_background"
        android:gravity="center"
        android:scaleType="centerInside"
        android:textColor="@color/note_tool_text_size_text_color"
        android:textSize="@dimen/sp_36"
        app:layout_constraintBottom_toTopOf="@id/text_size_name"
        app:layout_constraintEnd_toEndOf="@id/text_size_name"
        app:layout_constraintStart_toStartOf="@id/text_size_name"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/text_size_name"
        android:layout_width="@dimen/dp_120"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="top|center_horizontal"
        android:maxLines="2"
        android:text="@string/text_size"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_size" />

</androidx.constraintlayout.widget.ConstraintLayout>
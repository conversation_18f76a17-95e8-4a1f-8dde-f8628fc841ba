<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/dp_60">

    <ImageView
        android:id="@+id/top_background"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/rate_dialog_top_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_88"
        android:layout_height="@dimen/dp_88"
        android:layout_marginTop="@dimen/dp_18"
        android:layout_marginEnd="@dimen/dp_18"
        android:padding="@dimen/dp_12"
        android:src="@drawable/rate_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/rate_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_61"
        android:ellipsize="end"
        android:maxLines="2"
        android:paddingHorizontal="@dimen/dp_30"
        android:text="@string/rate_title"
        android:textColor="#333333"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/top_background" />

    <TextView
        android:id="@+id/rate_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_40"
        android:ellipsize="end"
        android:maxLines="2"
        android:paddingHorizontal="@dimen/dp_30"
        android:text="@string/rate_message"
        android:textColor="#666666"
        android:textSize="@dimen/sp_42"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rate_title" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/go_to_rate"
        android:layout_width="@dimen/dp_560"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_90"
        android:background="@drawable/rate_dialog_go_to_rate_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rate_message">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_20"
            android:text="@string/got_to_rate"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_42"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
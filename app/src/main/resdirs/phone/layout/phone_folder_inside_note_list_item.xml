<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="@dimen/dp_80"
    android:layout_height="@dimen/dp_107">

    <com.lihang.ShadowLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:hl_cornerRadius_leftBottom="@dimen/dp_2"
        app:hl_cornerRadius_leftTop="@dimen/dp_2"
        app:hl_cornerRadius_rightBottom="@dimen/dp_12"
        app:hl_cornerRadius_rightTop="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/note_cover"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:maxHeight="@dimen/dp_107"
            android:scaleType="centerCrop"
            tools:src="@drawable/h_cover1" />

    </com.lihang.ShadowLayout>
</androidx.constraintlayout.widget.ConstraintLayout>


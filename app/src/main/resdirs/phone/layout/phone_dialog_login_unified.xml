<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_outline_create_bg">

    <ViewFlipper
        android:id="@+id/view_flipper"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:autoStart="false"
        android:measureAllChildren="false"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <include
            android:id="@+id/main_include"
            layout="@layout/phone_dialog_login_android" />

        <include
            android:id="@+id/phone_login_include"
            layout="@layout/phone_dialog_login_using_number" />

        <include
            android:id="@+id/wechat_user_login_include"
            layout="@layout/phone_dialog_user_login" />

    </ViewFlipper>

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.lihang.ShadowLayout
        android:id="@+id/cover_image_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:hl_cornerRadius_leftTop="@dimen/dp_6"
        app:hl_cornerRadius_rightTop="@dimen/dp_24"
        app:hl_cornerRadius_rightBottom="@dimen/dp_24"
        app:hl_cornerRadius_leftBottom="@dimen/dp_6">
        <ImageView
            android:id="@+id/cover_image"
            android:layout_width="@dimen/dp_212"
            android:layout_height="@dimen/dp_300"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:maxHeight="@dimen/dp_300"/>
    </com.lihang.ShadowLayout>

    <ImageView
        android:id="@+id/cover_image_outline"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:src="@drawable/phone_cover_item_border"
        app:layout_constraintBottom_toBottomOf="@id/cover_image_shadow"
        app:layout_constraintEnd_toEndOf="@id/cover_image_shadow"
        app:layout_constraintStart_toStartOf="@id/cover_image_shadow"
        app:layout_constraintTop_toTopOf="@id/cover_image_shadow" />

    <ImageView
        android:id="@+id/cover_delete"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        android:padding="@dimen/dp_5"
        android:src="@drawable/cover_item_delete"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@id/cover_image_shadow"
        app:layout_constraintTop_toTopOf="@id/cover_image_shadow" />

    <ImageView
        android:id="@+id/cover_select"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_58"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_12"
        android:padding="@dimen/dp_5"
        android:src="@drawable/cover_item_selected"
        app:layout_constraintBottom_toBottomOf="@id/cover_image_shadow"
        app:layout_constraintEnd_toEndOf="@id/cover_image_shadow" />

</androidx.constraintlayout.widget.ConstraintLayout>
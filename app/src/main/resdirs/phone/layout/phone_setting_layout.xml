<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:text="@string/setting"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_54"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/data_backup_space"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title">

        <ImageView
            android:id="@+id/back_space_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/note_icon_backup_space"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/backup_space_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/backup_space"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/back_space_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/isVip_icon"
            android:layout_width="@dimen/dp_51"
            android:layout_height="@dimen/dp_42"
            android:layout_marginStart="@dimen/dp_15"
            android:scaleType="fitXY"
            android:src="@drawable/note_icon_backup_isvip"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/backup_space_desc"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/backup_space_next_menu"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:src="@drawable/phone_note_tool_icon_next_menu"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ai_points_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/data_backup_space"
        android:visibility="gone">

        <ImageView
            android:id="@+id/ai_points_indicator"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/note_icon_ai_integral"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/ai_points"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/ai_points_indicator"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ai_points_next_menu"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:src="@drawable/phone_note_tool_icon_next_menu"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/data_backup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ai_points_container">

        <ImageView
            android:id="@+id/back_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/setting_backup"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/data_backup"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/back_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/backup_next_menu"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:src="@drawable/phone_note_tool_icon_next_menu"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/redeem_code_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/data_backup">

        <ImageView
            android:id="@+id/redeem_code_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/redeem_code_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/redeem_code_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:layout_marginEnd="@dimen/dp_48"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/redeem_code"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/redeem_code_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/redeem_code_indicator"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:src="@drawable/phone_note_tool_icon_next_menu"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/personalized_recommendations_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/redeem_code_container">

        <ImageView
            android:id="@+id/personalized_recommendations_indicator"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/note_icon_personalized_recommendations"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:layout_marginEnd="@dimen/dp_48"
            android:ellipsize="end"
            android:text="@string/personalized_recommendations"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/personalized_recommendations_switch"
            app:layout_constraintStart_toEndOf="@id/personalized_recommendations_indicator"
            app:layout_constraintTop_toTopOf="parent" />

        <Switch
            android:id="@+id/personalized_recommendations_switch"
            style="@style/PhoneSwitchStyle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/image_optimization_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_51"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/personalized_recommendations_container">

        <ImageView
            android:id="@+id/image_optimization_indicator"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/note_icon_image_optimization"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:layout_marginEnd="@dimen/dp_48"
            android:ellipsize="end"
            android:text="@string/image_optimization"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/image_optimization_switch"
            app:layout_constraintStart_toEndOf="@id/image_optimization_indicator"
            app:layout_constraintTop_toTopOf="parent" />

        <Switch
            android:id="@+id/image_optimization_switch"
            style="@style/PhoneSwitchStyle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quick_scale_optimization_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_51"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image_optimization_container">

        <ImageView
            android:id="@+id/quick_scale_optimization_indicator"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/phone_doodle_quick_scale_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:layout_marginEnd="@dimen/dp_48"
            android:ellipsize="end"
            android:text="@string/doodle_quick_scale"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/quick_scale_optimization_switch"
            app:layout_constraintStart_toEndOf="@id/quick_scale_optimization_indicator"
            app:layout_constraintTop_toTopOf="parent" />

        <Switch
            android:id="@+id/quick_scale_optimization_switch"
            style="@style/PhoneSwitchStyle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UseSwitchCompatOrMaterialXml" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/about_us"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/quick_scale_optimization_container">

        <ImageView
            android:id="@+id/about_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/setting_about"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/about_us"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/about_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/about_next_menu"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:src="@drawable/phone_note_tool_icon_next_menu"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/creator_community_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/about_us">

        <ImageView
            android:id="@+id/creator_community_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/note_icon_creator_community"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/creator_community"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/creator_community_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/creator_community_next_menu"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:src="@drawable/phone_note_tool_icon_next_menu"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/qq_feedback"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/creator_community_container">

        <ImageView
            android:id="@+id/feedback_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/setting_feedback"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/feedback_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/feedback_qq_group"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/feedback_icon"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/privacy_policy_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/qq_feedback">

        <ImageView
            android:id="@+id/privacy_policy_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/privacy_policy_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/privacy_policy_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/privacy_policy_title"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/privacy_policy_icon"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/info_collect_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/privacy_policy_container">

        <ImageView
            android:id="@+id/info_collect_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/info_collect_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/info_collect_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/info_collect_title"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/info_collect_icon"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/third_party_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/info_collect_container">

        <ImageView
            android:id="@+id/third_party_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/third_party_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/third_party_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/third_party_title"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/third_party_icon"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/account_cancellation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_48"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/third_party_container">

        <ImageView
            android:id="@+id/cancellation_icon"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:src="@drawable/setting_account_cancel"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_48"
            android:text="@string/account_cancellation"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/cancellation_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/cancellation_next_menu"
            android:layout_width="@dimen/dp_33"
            android:layout_height="@dimen/dp_33"
            android:src="@drawable/phone_note_tool_icon_next_menu"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/split_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginTop="@dimen/dp_62"
        android:background="@color/bottom_sheet_split_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_cancellation" />

    <TextView
        android:id="@+id/back"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_141"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/split_line" />
</androidx.constraintlayout.widget.ConstraintLayout>
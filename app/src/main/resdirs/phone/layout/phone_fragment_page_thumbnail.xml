<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_43"
        android:layout_height="@dimen/dp_43"
        android:layout_marginTop="@dimen/dp_100"
        android:layout_marginEnd="@dimen/dp_46"
        android:src="@drawable/phone_page_list_thumbnail_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_57"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/close"
        app:scrollOrientation="vertical"
        tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        tools:listitem="@layout/item_page_list_thumbnail" />


</androidx.constraintlayout.widget.ConstraintLayout>
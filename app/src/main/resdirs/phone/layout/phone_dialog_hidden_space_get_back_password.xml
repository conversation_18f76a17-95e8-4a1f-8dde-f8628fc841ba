<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/phone_dialog_hidden_space_get_back_password_bg">

    <com.lihang.ShadowLayout
        android:id="@+id/random_code_box_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_60"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_layoutBackground="@color/vip_store_background_color"
        app:hl_strokeColor="@color/button_disabled"
        app:hl_strokeWith="@dimen/dp_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/random_code_box"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingVertical="@dimen/dp_27"
            android:paddingHorizontal="@dimen/dp_48"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_48"
            tools:text="sdf1234567890" />
    </com.lihang.ShadowLayout>

    <TextView
        android:id="@+id/copy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_48"
        android:text="@string/copy"
        android:textColor="@color/skip_text"
        android:textSize="@dimen/sp_48"
        app:layout_constraintBottom_toBottomOf="@id/random_code_box_container"
        app:layout_constraintEnd_toEndOf="@id/random_code_box_container"
        app:layout_constraintTop_toTopOf="@id/random_code_box_container" />
    
    <TextView
        android:id="@+id/tips"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginVertical="@dimen/dp_48"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:textSize="@dimen/sp_42"
        android:textColor="@color/text_secondary"
        android:scrollbars="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/random_code_box_container"
        app:layout_constraintBottom_toTopOf="@id/know_container"/>

    <com.lihang.ShadowLayout
        android:id="@+id/know_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_170"
        android:layout_marginBottom="@dimen/dp_60"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_endColor="@color/button_gradient_end"
        app:hl_startColor="@color/button_gradient_start"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/know"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_26"
            android:text="@string/know"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48" />
    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
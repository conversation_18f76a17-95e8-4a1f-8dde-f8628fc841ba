<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <View
        android:id="@+id/top_shadow"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/phone_select_color_window_top_shadow"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_20"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@+id/top_shadow" />

    <TextView
        android:id="@+id/style_title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_78"
        android:text="@string/graffiti_style"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/graffiti_type_list"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_90"
        android:layout_marginTop="@dimen/dp_60"
        android:nestedScrollingEnabled="false"
        app:layout_constraintBottom_toTopOf="@id/graffiti_style_recycleview"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/style_title_text" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/graffiti_style_recycleview"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_900"
        android:layout_marginTop="@dimen/dp_40"
        app:layout_constraintTop_toBottomOf="@id/graffiti_type_list" />

</androidx.constraintlayout.widget.ConstraintLayout>
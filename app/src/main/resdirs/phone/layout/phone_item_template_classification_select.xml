<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/item_template_classification_select"
        android:layout_width="wrap_content"
        android:minWidth="@dimen/dp_168"
        android:gravity="center"
        android:layout_height="@dimen/dp_80"
        android:paddingHorizontal="@dimen/dp_30"
        android:background="@drawable/phone_template_classification_unselect"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
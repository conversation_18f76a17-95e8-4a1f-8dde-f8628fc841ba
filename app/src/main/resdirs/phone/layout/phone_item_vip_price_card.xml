<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_506"
    android:layout_height="@dimen/dp_255"
    android:background="@drawable/selector_vip_price_card_bg">

    <TextView
        android:id="@+id/price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF131415"
        android:textSize="@dimen/sp_54"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="128" />

    <TextView
        android:id="@+id/price_symbol"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="¥"
        android:textStyle="bold"
        android:textColor="#FF131415"
        android:textSize="@dimen/sp_30"
        android:translationY="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/price" />

    <TextView
        android:id="@+id/vip_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/permanent_vip"
        android:textColor="#FFA76444"
        android:textSize="@dimen/sp_41"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/price" />

    <TextView
        android:id="@+id/lifetime_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_lifetime_tag"
        android:paddingHorizontal="@dimen/dp_34"
        android:paddingVertical="@dimen/dp_5"
        android:text="@string/lifetime_use"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/sp_30"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/gold_icon"
        android:layout_width="@dimen/dp_41"
        android:layout_height="@dimen/dp_41"
        android:src="@drawable/vip_store_gold_icon"
        android:translationY="@dimen/dp_10" />

    <TextView
        android:id="@+id/values"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="10000"
        android:textColor="#FFFD6C00"
        android:textSize="@dimen/sp_30"
        android:translationY="@dimen/dp_10" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/value_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="gold_icon,values">
    </androidx.constraintlayout.widget.Group>

    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/price_flow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_51"
        app:constraint_referenced_ids="price_symbol,price,gold_icon,values"
        app:flow_horizontalGap="@dimen/dp_5"
        app:flow_horizontalStyle="packed"
        app:flow_wrapMode="none"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
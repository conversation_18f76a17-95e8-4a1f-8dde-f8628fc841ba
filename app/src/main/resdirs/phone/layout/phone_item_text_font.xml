<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_166">

    <ImageView
        android:id="@+id/text_font"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginTop="@dimen/dp_6"
        android:src="@drawable/phone_note_tool_icon_font"
        app:layout_constraintBottom_toTopOf="@id/text_font_name"
        app:layout_constraintEnd_toEndOf="@id/text_font_name"
        app:layout_constraintStart_toStartOf="@id/text_font_name"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/text_font_name"
        android:layout_width="@dimen/dp_120"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="top|center_horizontal"
        android:maxLines="2"
        android:text="@string/text_font"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_font" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_constraint_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/decoupage_tool_background">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_tab_selector"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_82"
        android:layout_marginTop="@dimen/dp_120"
        android:background="@drawable/phone_instant_alpha_top_tab_selector_background"
        android:maxWidth="@dimen/dp_796"
        android:paddingHorizontal="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="@id/instant_alpha_view"
        app:layout_constraintStart_toStartOf="@id/instant_alpha_view"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/instant_alpha_tab"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_21"
            android:background="@drawable/instant_alpha_top_selector_item_background_selector"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp_48"
            android:paddingVertical="@dimen/dp_13"
            android:text="@string/instant_alpha_title"
            android:textColor="@color/select_photo_button_text"
            android:textSize="@dimen/sp_48"
            app:layout_constraintBottom_toBottomOf="@id/top_tab_selector"
            app:layout_constraintEnd_toStartOf="@id/image_matting_tab"
            app:layout_constraintStart_toStartOf="@id/top_tab_selector"
            app:layout_constraintTop_toTopOf="@id/top_tab_selector" />

        <TextView
            android:id="@+id/instant_alpha_usable_times_tips"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:layout_marginEnd="@dimen/dp_10"
            android:background="@drawable/sign_red_bg"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_32"
            app:layout_constraintEnd_toEndOf="@+id/instant_alpha_tab"
            app:layout_constraintTop_toTopOf="@id/instant_alpha_tab" />

        <TextView
            android:id="@+id/image_matting_tab"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_21"
            android:background="@drawable/instant_alpha_top_selector_item_background_selector"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp_48"
            android:paddingVertical="@dimen/dp_13"
            android:text="@string/image_matting_title"
            android:textColor="@color/select_photo_button_text"
            android:textSize="@dimen/sp_48"
            app:layout_constraintBottom_toBottomOf="@id/top_tab_selector"
            app:layout_constraintEnd_toEndOf="@id/top_tab_selector"
            app:layout_constraintStart_toEndOf="@id/instant_alpha_tab"
            app:layout_constraintTop_toTopOf="@id/top_tab_selector" />

        <TextView
            android:id="@+id/image_matting_usable_times_tips"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:layout_marginEnd="@dimen/dp_10"
            android:background="@drawable/sign_red_bg"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_32"
            app:layout_constraintEnd_toEndOf="@+id/image_matting_tab"
            app:layout_constraintTop_toTopOf="@id/image_matting_tab" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_96"
        android:layout_height="@dimen/dp_96"
        android:layout_marginStart="@dimen/dp_36"
        android:padding="@dimen/dp_12"
        android:src="@drawable/decoupage_close"
        app:layout_constraintBottom_toBottomOf="@+id/top_tab_selector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/top_tab_selector" />

    <com.topstack.kilonotes.opencv.InstantAlphaView
        android:id="@+id/instant_alpha_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_60"
        android:layout_marginTop="@dimen/dp_65"
        android:layout_marginEnd="@dimen/dp_60"
        android:layout_marginBottom="@dimen/dp_252"
        app:layout_constraintBottom_toTopOf="@id/save"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_tab_selector" />

    <com.topstack.kilonotes.base.imagemagnifier.ImageMagnifierView
        android:id="@+id/magnifier"
        android:layout_width="@dimen/dp_360"
        android:layout_height="@dimen/dp_360"
        android:layout_marginHorizontal="@dimen/dp_36"
        android:layout_marginTop="@dimen/dp_20"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_tab_selector" />

    <ImageView
        android:id="@+id/close_view"
        android:layout_width="@dimen/dp_92"
        android:layout_height="@dimen/dp_92"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:padding="@dimen/dp_10"
        android:src="@drawable/instant_alpha_close"
        app:layout_constraintEnd_toEndOf="@id/instant_alpha_view"
        app:layout_constraintTop_toTopOf="@id/instant_alpha_view" />


    <com.lihang.ShadowLayout
        android:id="@+id/import_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:hl_cornerRadius="@dimen/dp_30"
        app:hl_shadowLimit="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="@id/instant_alpha_view"
        app:layout_constraintEnd_toEndOf="@id/instant_alpha_view"
        app:layout_constraintStart_toStartOf="@id/instant_alpha_view"
        app:layout_constraintTop_toTopOf="@id/instant_alpha_view">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/import_layout"
            android:layout_width="@dimen/dp_400"
            android:layout_height="@dimen/dp_440">

            <ImageView
                android:id="@+id/import_img"
                android:layout_width="@dimen/dp_292"
                android:layout_height="@dimen/dp_192"
                android:layout_marginTop="@dimen/dp_73"
                android:paddingStart="@dimen/dp_25"
                android:src="@drawable/instant_alpha_import"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/import_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_36"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/instant_alpha_import_img"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_48"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/import_img" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.lihang.ShadowLayout>


    <ImageView
        android:id="@+id/instant_alpha_guide_gif"
        android:layout_width="@dimen/dp_240"
        android:layout_height="@dimen/dp_542"
        android:scaleType="fitXY"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/instant_alpha_view"
        app:layout_constraintEnd_toEndOf="@id/instant_alpha_view" />

    <TextView
        android:id="@+id/instant_alpha_guide_gif_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_182"
        android:background="@drawable/phone_instant_alpha_guide_bg"
        android:gravity="center"
        android:text="@string/instant_alpha_guide_tips"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_30"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/instant_alpha_guide_gif"
        app:layout_constraintTop_toTopOf="@id/instant_alpha_guide_gif" />

    <TextView
        android:id="@+id/image_matting_guide_gif_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/instant_alpha_guide_bg"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_34"
        android:text="@string/image_matting_guide_tips"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_36"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/image_matting_guide_gif"
        app:layout_constraintTop_toBottomOf="@id/image_matting_guide_gif" />

    <ImageView
        android:id="@+id/image_matting_guide_gif"
        android:layout_width="@dimen/dp_399"
        android:layout_height="@dimen/dp_399"
        android:scaleType="fitStart"
        app:layout_constraintStart_toStartOf="@id/instant_alpha_view"
        app:layout_constraintTop_toTopOf="@id/instant_alpha_view" />

    <ImageView
        android:id="@+id/remove_edge_seek_bar_down"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:background="@drawable/instant_alpha_remove_edge_seek_bar_down_icon"
        app:layout_constraintBottom_toBottomOf="@id/remove_edge_seek_bar"
        app:layout_constraintStart_toStartOf="@id/instant_alpha_view"
        app:layout_constraintTop_toTopOf="@id/remove_edge_seek_bar" />

    <ImageView
        android:id="@+id/remove_edge_seek_bar_up"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:background="@drawable/instant_alpha_remove_edge_seek_bar_up_icon"
        app:layout_constraintBottom_toBottomOf="@id/remove_edge_seek_bar"
        app:layout_constraintEnd_toEndOf="@id/instant_alpha_view"
        app:layout_constraintTop_toTopOf="@id/remove_edge_seek_bar" />

    <SeekBar
        android:id="@+id/remove_edge_seek_bar"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_20"
        android:background="@null"
        android:maxHeight="@dimen/dp_8"
        android:progress="100"
        android:progressDrawable="@drawable/phone_image_tools_alpha_seekbar_progress_background"
        android:splitTrack="false"
        android:thumbOffset="@dimen/dp_0"
        app:layout_constraintBottom_toTopOf="@id/bottom_tool"
        app:layout_constraintEnd_toStartOf="@id/remove_edge_seek_bar_up"
        app:layout_constraintStart_toEndOf="@id/remove_edge_seek_bar_down"
        app:layout_constraintTop_toBottomOf="@id/instant_alpha_view" />

    <HorizontalScrollView
        android:id="@+id/bottom_tool"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_60"
        android:layout_marginBottom="@dimen/dp_44"
        app:layout_constraintBottom_toTopOf="@id/save"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/remove_edge_seek_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/reset"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_90"
                android:src="@drawable/instant_alpha_reset_selector"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/undo"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_90"
                android:layout_marginStart="@dimen/dp_128"
                android:src="@drawable/instant_alpha_undo_selector"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/reset"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/redo"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_90"
                android:layout_marginStart="@dimen/dp_128"
                android:background="@drawable/instant_alpha_redo_selector"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/undo"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/remove_edge"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_90"
                android:layout_marginStart="@dimen/dp_128"
                android:background="@drawable/instant_alpha_remove_edge_selector"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/redo"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/compare"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_90"
                android:layout_marginStart="@dimen/dp_128"
                android:background="@drawable/instant_alpha_compare_selector"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/remove_edge"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </HorizontalScrollView>


    <TextView
        android:id="@+id/save"
        android:layout_width="@dimen/dp_450"
        android:layout_height="@dimen/dp_96"
        android:layout_marginBottom="@dimen/dp_48"
        android:background="@drawable/instant_alpha_save_selector"
        android:gravity="center"
        android:text="@string/instant_alpha_save"
        android:textColor="@color/instant_alpha_save_color"
        android:textSize="@dimen/sp_45"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/save_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_75"
        android:background="@drawable/instant_alpha_save_tips_background"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxWidth="@dimen/dp_450"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_15"
        android:paddingVertical="@dimen/dp_4"
        android:text="@string/instant_alpha_tool_free_tip"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_28"
        app:layout_constraintBottom_toBottomOf="@id/save"
        app:layout_constraintEnd_toEndOf="@id/save" />

    <TextView
        android:id="@+id/tool_tips"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_58"
        android:layout_marginBottom="@dimen/dp_400"
        android:background="@drawable/instant_alpha_guide_bg"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_56"
        android:paddingTop="@dimen/dp_5"
        android:paddingEnd="@dimen/dp_56"
        android:paddingBottom="@dimen/dp_5"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_34"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="还原"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/rounded_32dp_white">

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginTop="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_12"
        android:padding="@dimen/dp_12"
        android:src="@drawable/dialog_store_buy_icon_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_102"
        android:layout_marginTop="@dimen/dp_48"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="4"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="购买下午茶时光手账本" />

    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_452"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_24"
        android:scaleType="centerInside"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:ignore="ContentDescription"
        tools:src="@drawable/vip_store_vip_benefits1" />

    <View
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_60"
        android:background="@drawable/dialog_store_buy_pay_type_background"
        app:layout_constraintBottom_toTopOf="@id/confirm"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/subtitle" />

    <TextView
        android:id="@+id/subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_48"
        android:text="@string/vip_store_chose_pay_type_text"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_36"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image"
        app:layout_goneMarginTop="@dimen/dp_60" />

    <ImageView
        android:id="@+id/alipay_icon"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginStart="@dimen/dp_84"
        android:layout_marginTop="@dimen/dp_60"
        android:src="@drawable/dialog_store_buy_icon_alipay"
        app:layout_constraintBottom_toTopOf="@id/wechat_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/subtitle"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/alipay_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_24"
        android:text="@string/pay_type_alipay_title"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_36"
        app:layout_constraintBottom_toBottomOf="@id/alipay_icon"
        app:layout_constraintEnd_toStartOf="@id/alipay_space"
        app:layout_constraintStart_toEndOf="@id/alipay_icon"
        app:layout_constraintTop_toTopOf="@id/alipay_icon" />

    <Space
        android:id="@+id/alipay_space"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="@id/alipay_icon"
        app:layout_constraintEnd_toStartOf="@id/alipay_selector"
        app:layout_constraintStart_toEndOf="@id/alipay_text"
        app:layout_constraintTop_toTopOf="@id/alipay_icon" />

    <ImageView
        android:id="@+id/alipay_selector"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginEnd="@dimen/dp_84"
        android:background="@drawable/vip_store_dialog_type_type_bg_selector"
        android:padding="@dimen/dp_10"
        android:src="@drawable/pad_login_dialog_checkbox_image_src"
        app:layout_constraintBottom_toBottomOf="@id/alipay_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/alipay_space"
        app:layout_constraintTop_toTopOf="@id/alipay_icon"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/wechat_icon"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginStart="@dimen/dp_84"
        android:layout_marginTop="@dimen/dp_48"
        android:src="@drawable/dialog_store_buy_icon_wechat"
        app:layout_constraintBottom_toTopOf="@id/confirm"
        app:layout_constraintEnd_toStartOf="@id/wechat_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/alipay_icon"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/wechat_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_24"
        android:text="@string/pay_type_wechat_title"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_36"
        app:layout_constraintBottom_toBottomOf="@id/wechat_icon"
        app:layout_constraintEnd_toStartOf="@id/wechat_space"
        app:layout_constraintStart_toEndOf="@id/wechat_icon"
        app:layout_constraintTop_toTopOf="@id/wechat_icon" />

    <Space
        android:id="@+id/wechat_space"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="@id/wechat_icon"
        app:layout_constraintEnd_toStartOf="@id/wechat_selector"
        app:layout_constraintStart_toEndOf="@id/wechat_text"
        app:layout_constraintTop_toTopOf="@id/wechat_icon" />

    <ImageView
        android:id="@+id/wechat_selector"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginEnd="@dimen/dp_84"
        android:background="@drawable/vip_store_dialog_type_type_bg_selector"
        android:padding="@dimen/dp_10"
        android:src="@drawable/pad_login_dialog_checkbox_image_src"
        app:layout_constraintBottom_toBottomOf="@id/wechat_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/wechat_space"
        app:layout_constraintTop_toTopOf="@id/wechat_icon"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_96"
        android:layout_marginBottom="@dimen/dp_60"
        android:background="@drawable/dialog_store_buy_pay_button_background"
        android:gravity="center"
        android:minWidth="@dimen/dp_596"
        android:padding="@dimen/dp_30"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_42"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wechat_icon"
        tools:text="立即以108购买" />

</androidx.constraintlayout.widget.ConstraintLayout>
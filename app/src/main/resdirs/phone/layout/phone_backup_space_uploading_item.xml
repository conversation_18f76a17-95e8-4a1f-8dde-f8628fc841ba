<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/cover"
        android:layout_width="@dimen/dp_96"
        android:layout_height="@dimen/dp_126"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_40"
        android:layout_marginBottom="@dimen/dp_35"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/cover1" />

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_20"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_42"
        app:autoSizeMaxTextSize="@dimen/sp_42"
        app:autoSizeMinTextSize="@dimen/sp_18"
        app:autoSizeStepGranularity="@dimen/sp_1"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toStartOf="@+id/upload_status"
        app:layout_constraintStart_toEndOf="@id/cover"
        app:layout_constraintTop_toTopOf="@+id/cover"
        app:layout_constraintBottom_toBottomOf="@+id/cover"
        tools:text="title" />

    <ProgressBar
        android:id="@+id/upload_progress"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_40"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:max="100"
        android:progress="0"
        android:progressDrawable="@drawable/backup_space_upload_progress_bar"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintBottom_toBottomOf="@+id/cover"
        app:layout_constraintEnd_toEndOf="@+id/upload_status"
        app:layout_constraintStart_toStartOf="@id/title"
        tools:progress="50" />

    <TextView
        android:id="@+id/upload_status"
        android:layout_width="@dimen/dp_180"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_40"
        android:paddingVertical="@dimen/dp_5"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/uploading"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_36"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/cover"
        app:layout_constraintBottom_toBottomOf="@+id/cover" />


</androidx.constraintlayout.widget.ConstraintLayout>
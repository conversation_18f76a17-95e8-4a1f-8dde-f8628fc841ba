<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_228"
    android:layout_height="@dimen/dp_228">

    <ImageView
        android:id="@+id/paper_cut_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_24"
        tools:background="@color/black"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/paper_cut_del"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:src="@drawable/paper_cut_delete_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone" />
    
    <ImageView
        android:id="@+id/paper_cut_select"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:src="@drawable/phone_backup_checkbox_select"
        android:layout_marginBottom="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="invisible"/>

</androidx.constraintlayout.widget.ConstraintLayout>
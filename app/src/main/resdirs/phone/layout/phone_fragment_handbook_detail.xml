<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/vip_store_background_color">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/detail_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/vip_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingBottom="@dimen/dp_23"
        android:background="@drawable/handbook_detail_top_bar_background">
        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_88"
            android:layout_height="@dimen/dp_88"
            android:layout_marginStart="@dimen/dp_10"
            android:padding="@dimen/dp_20"
            android:src="@drawable/handbook_detail_back"
            app:layout_constraintBottom_toBottomOf="@id/confirm"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/confirm"
            tools:ignore="ContentDescription" />

        <com.topstack.kilonotes.base.component.view.ProgressButton
            android:id="@+id/confirm"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_82"
            android:layout_marginTop="@dimen/dp_130"
            android:layout_marginEnd="@dimen/dp_30"
            android:gravity="center"
            android:minWidth="@dimen/dp_210"
            android:paddingHorizontal="@dimen/dp_30"
            android:text="@string/no_network_price"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_42"
            app:cornerRadius="@dimen/dp_41"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:max_progress="100"
            app:min_progress="0"
            app:progressBackColor="@color/progress_button_second_background"
            app:progressColor="@color/progress_bar" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <LinearLayout
        android:id="@+id/vip_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:minHeight="@dimen/dp_168"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">

        <TextView
            android:id="@+id/vip_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_25"
            android:layout_marginBottom="@dimen/dp_25"
            android:layout_weight="1"
            android:text="@string/handbook_vip_description"
            android:textColor="@color/handbook_detail_vip_tip_text"
            android:textSize="@dimen/sp_42" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/vip_buy"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_82"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_30"
            android:minWidth="@dimen/dp_252"
            android:background="@drawable/rate_dialog_go_to_rate_background">

            <TextView
                android:id="@+id/vip_buy_btn_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dp_40"
                android:gravity="center"
                android:text="@string/handbook_vip_buy"
                android:textColor="@color/handbook_detail_buy_text_color"
                android:textSize="@dimen/sp_42"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:paddingHorizontal="@dimen/dp_15"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
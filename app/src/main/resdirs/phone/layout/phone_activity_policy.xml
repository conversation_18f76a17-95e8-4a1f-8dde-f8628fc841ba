<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_144"
        android:background="@color/agreement_title_bg"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/policy_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_24"
            android:text="@string/terms_of_users_service"
            android:textColor="@color/book_name_edit_text"
            android:textSize="@dimen/sp_42"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/policyClose"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:layout_marginStart="@dimen/dp_46"
            android:src="@drawable/phone_agreement_icon_close"
            app:layout_constraintBottom_toBottomOf="@id/policy_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/policy_title"
            tools:ignore="ContentDescription" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <WebView
        android:id="@+id/policy_web_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_marginHorizontal="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_22"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header" />
</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/security_question_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_30"
        android:text="@string/security_question"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_48" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/select_question_group"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_120">

        <TextView
            android:id="@+id/select_security_question_text"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/phone_security_question_text_bg"
            android:gravity="start|center_vertical"
            android:paddingHorizontal="@dimen/dp_30"
            android:text="@string/security_question_default_text"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_42" />

        <ImageView
            android:id="@+id/question_pull_down_icon"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_30"
            android:src="@drawable/phone_security_question_icon_show"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/security_answer_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:layout_marginBottom="@dimen/dp_30"
        android:text="@string/security_answer"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_48" />

    <com.topstack.kilonotes.base.component.view.FormatVerificationInputLayout
        android:id="@+id/set_security_answer_text"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_120"
        android:gravity="start|center_vertical"
        app:verifyType="not_verify"
        app:hint_text="@string/security_answer_default_text"
        app:hint_text_color="@color/set_security_answer_default_color"
        app:text_background="@drawable/phone_security_question_text_bg"
        app:text_color="@color/text_secondary"
        app:text_padding_end="@dimen/dp_120"
        app:text_padding_start="@dimen/dp_30"
        app:text_size="@dimen/sp_42" />

</LinearLayout>
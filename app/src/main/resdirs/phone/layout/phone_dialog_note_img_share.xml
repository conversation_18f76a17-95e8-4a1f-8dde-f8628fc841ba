<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_1002"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_184"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/selected_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_secondary"
            android:textSize="@dimen/sp_48"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <ImageView
            android:id="@+id/close_btn"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginEnd="@dimen/dp_48"
            android:src="@drawable/phone_redeem_code_dialog_close"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/pages"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_677"
        android:layout_marginHorizontal="@dimen/dp_48"
        app:scrollOrientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_bar"
        app:layout_constraintBottom_toTopOf="@id/submit_btn" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_20"
        android:background="@drawable/phone_select_color_window_top_shadow"
        app:layout_constraintBottom_toTopOf="@id/submit_btn" />

    <TextView
        android:id="@+id/submit_btn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_141"
        android:textSize="@dimen/sp_48"
        android:textColor="@color/note_img_share_btn_not_allow_color"
        android:text="@string/confirm"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
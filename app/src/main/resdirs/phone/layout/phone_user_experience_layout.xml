<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:minHeight="@dimen/dp_1369">

    <ImageView
        android:id="@+id/user_experience_back"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_58"
        android:layout_marginStart="@dimen/dp_47"
        android:scaleType="fitCenter"
        android:src="@drawable/phone_create_note_icon_cancel"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/title"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_63"
        android:text="@string/about_user_experience"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/sub_title_user_experience"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_103"
        android:gravity="center"
        android:text="@string/about_user_experience"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_42"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/user_experience_switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_46"
        android:theme="@style/Theme.Kilonotes.SwitchCompat"
        app:layout_constraintBottom_toBottomOf="@id/sub_title_user_experience"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/sub_title_user_experience"
        app:switchMinWidth="@dimen/dp_104" />

    <TextView
        android:id="@+id/user_experience_spec"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_68"
        android:lineSpacingExtra="@dimen/dp_8"
        android:text="@string/about_user_experience_spec"
        android:textColor="@color/text_disable"
        android:textSize="@dimen/sp_34"
        app:layout_constraintTop_toBottomOf="@id/sub_title_user_experience" />

    <!-- TODO: 先用 ConstraintLayout 实现 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/third_party_ad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_68"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/user_experience_spec"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/third_party_ad_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/about_third_party_ad_title"
            android:textColor="#323438"
            android:textSize="@dimen/sp_34"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/third_party_ad_share_data_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_40"
            android:text="@string/about_third_party_ad_share_data_title"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_42"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/third_party_ad_title" />

        <TextView
            android:id="@+id/third_party_ad_share_data_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@string/about_third_party_ad_share_data_spec"
            android:textColor="#FF909090"
            android:textSize="@dimen/sp_34"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/third_party_ad_share_data_title" />

        <ImageView
            android:id="@+id/third_party_ad_share_data_icon"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:src="@drawable/custom_tool_icon_enter"
            app:layout_constraintBottom_toBottomOf="@id/third_party_ad_share_data_desc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/third_party_ad_share_data_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
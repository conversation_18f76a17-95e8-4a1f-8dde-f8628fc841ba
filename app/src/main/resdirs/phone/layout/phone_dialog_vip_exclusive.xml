<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/vip_content_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_vip_exclusive_bg_middle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/vip_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_178"
            android:gravity="center"
            android:text="@string/vip_exclusive_content"
            android:textColor="@color/vip_exclusive_content_color"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/vip_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_405"
        android:background="@drawable/dialog_vip_exclusive_bg_top"
        android:gravity="center"
        android:scaleType="fitEnd"
        app:layout_constraintBottom_toTopOf="@id/vip_content_layout" />

    <ImageView
        android:id="@+id/vip_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_416"
        android:background="@drawable/dialog_vip_exclusive_bg_bottom"
        android:gravity="center"
        android:scaleType="fitStart"
        app:layout_constraintTop_toBottomOf="@id/vip_content_layout" />

    <TextView
        android:id="@+id/vip_exclusive_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_256"
        android:ellipsize="end"
        android:maxWidth="@dimen/dp_724"
        android:maxLines="2"
        android:text="@string/vip_exclusive_title"
        android:textColor="@color/vip_exclusive_title_color"
        android:textSize="@dimen/sp_54"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/vip_top"
        app:layout_constraintStart_toStartOf="@id/vip_top"
        app:layout_constraintTop_toTopOf="@id/vip_top" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/vip_buy"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_90"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="@dimen/dp_223"
        android:background="@drawable/rate_dialog_go_to_rate_background"
        android:minWidth="@dimen/dp_438"
        app:layout_constraintEnd_toEndOf="@id/vip_bottom"
        app:layout_constraintStart_toStartOf="@id/vip_bottom"
        app:layout_constraintTop_toTopOf="@id/vip_bottom">

        <TextView
            android:id="@+id/vip_buy_btn_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dp_40"
            android:ellipsize="end"
            android:gravity="center"
            android:maxWidth="@dimen/dp_890"
            android:maxLines="2"
            android:paddingHorizontal="@dimen/dp_15"
            android:text="@string/buy_vip"
            android:textColor="@color/handbook_detail_buy_text_color"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginBottom="@dimen/dp_20"
        android:src="@drawable/dialog_instant_alpha_close"
        app:layout_constraintEnd_toEndOf="@id/vip_bottom"
        app:layout_constraintStart_toStartOf="@id/vip_bottom"
        app:layout_constraintTop_toBottomOf="@id/vip_bottom" />

</androidx.constraintlayout.widget.ConstraintLayout>
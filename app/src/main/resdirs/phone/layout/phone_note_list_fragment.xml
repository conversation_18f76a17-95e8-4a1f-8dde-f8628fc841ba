<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/phone_note_list_fragment_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_140"
        android:paddingBottom="@dimen/dp_24"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_46"
            android:text="@string/app_name"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_58"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/hidden_space_home_btn"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp_50"
            android:scaleType="fitXY"
            android:src="@drawable/hidden_space_home_icon"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toStartOf="@id/hidden_space_create_folder_btn"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/hidden_space_create_folder_btn"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp_50"
            android:scaleType="fitXY"
            android:src="@drawable/phone_create_folder_icon"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toStartOf="@id/hidden_space_data_backup_btn"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/hidden_space_data_backup_btn"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp_50"
            android:scaleType="fitXY"
            android:src="@drawable/hidden_space_backup_icon"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toStartOf="@id/store_btn"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/store_btn"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp_55"
            android:scaleType="fitXY"
            android:src="@drawable/note_icon_store_new"
            app:layout_constraintBottom_toBottomOf="@id/importBtn"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toStartOf="@id/importBtn"
            app:layout_constraintTop_toTopOf="@id/importBtn"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/importBtn"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp_50"
            android:scaleType="fitXY"
            android:src="@drawable/note_list_import_icon_new"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toStartOf="@id/createFolderBtn"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/createFolderBtn"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp_50"
            android:scaleType="fitXY"
            android:src="@drawable/phone_create_folder_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toStartOf="@id/aboutBtn"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/aboutBtn"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp_50"
            android:scaleType="fitXY"
            android:src="@drawable/note_icon_setting_new"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            tools:ignore="ContentDescription" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header">

        <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
            android:id="@+id/noteRv"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_33"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_33"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:scrollOrientation="vertical" />

        <com.topstack.kilonotes.phone.component.view.PhoneHiddenSpaceNoticeTipsLayout
            android:id="@+id/hidden_space_notice_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/hidden_space_vip_and_security_tips"
            tools:visibility="visible" />

        <com.topstack.kilonotes.phone.component.view.PhoneHiddenSpaceVipAndSecurityTipsLayout
            android:id="@+id/hidden_space_vip_and_security_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_40"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/noteRv"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/security_question_dialog"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
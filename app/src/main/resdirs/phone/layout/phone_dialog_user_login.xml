<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_32dp_white">

    <!-- 返回按钮 -->
    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginStart="@dimen/dp_45"
        android:layout_marginTop="@dimen/dp_78"
        android:padding="@dimen/dp_10"
        android:src="@drawable/dialog_login_icon_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginEnd="@dimen/dp_48"
        android:padding="@dimen/dp_10"
        android:src="@drawable/dialog_login_icon_close"
        app:layout_constraintBottom_toBottomOf="@+id/back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/back"
        tools:ignore="ContentDescription" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login_to_kilonotes"
        android:textColor="@color/page_overview_black"
        android:textSize="@dimen/sp_48"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/back" />

    <TextView
        android:id="@+id/description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_30"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxWidth="@dimen/dp_800"
        android:text="@string/user_login_description"
        android:textColor="@color/user_login_description_text"
        android:textSize="@dimen/sp_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <ImageView
        android:id="@+id/wechat_qrcode"
        android:layout_width="@dimen/dp_261"
        android:layout_height="@dimen/dp_261"
        android:layout_marginTop="@dimen/dp_52"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wechat_login" />

    <View
        android:id="@+id/wechat_login"
        android:layout_width="@dimen/dp_110"
        android:layout_height="@dimen/dp_110"
        android:layout_marginTop="@dimen/dp_236"
        android:background="@drawable/user_login_wechat_login"
        app:layout_constraintBottom_toTopOf="@id/wechat_qrcode_login"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <com.lihang.ShadowLayout
        android:id="@+id/wechat_qrcode_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_52"
        app:hl_cornerRadius="@dimen/dp_60"
        app:hl_layoutBackground="#FF2E82FF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wechat_login">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:maxWidth="@dimen/dp_700"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp_63"
            android:paddingVertical="@dimen/dp_16"
            android:text="@string/user_login_wechat_qrcode_login"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48"
            app:autoSizeMaxTextSize="@dimen/sp_48"
            app:autoSizeMinTextSize="@dimen/sp_31"
            app:autoSizeStepGranularity="@dimen/sp_1"
            app:autoSizeTextType="uniform" />
    </com.lihang.ShadowLayout>

    <View
        android:id="@+id/checkbox_click_area"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_180"
        app:layout_constraintBottom_toBottomOf="@+id/checkbox_trigger"
        app:layout_constraintEnd_toEndOf="@+id/checkbox_trigger"
        app:layout_constraintStart_toStartOf="@+id/checkbox_trigger"
        app:layout_constraintTop_toTopOf="@+id/checkbox_trigger" />

    <FrameLayout
        android:id="@+id/checkbox_trigger"
        android:layout_width="@dimen/dp_47"
        android:layout_height="@dimen/dp_47"
        app:layout_constraintBottom_toBottomOf="@id/policy"
        app:layout_constraintEnd_toStartOf="@id/policy"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/policy">

        <ImageView
            android:id="@+id/checkbox"
            android:layout_width="@dimen/dp_27"
            android:layout_height="@dimen/dp_27"
            android:layout_gravity="center"
            android:background="@drawable/pad_login_dialog_checkbox_bg"
            android:clickable="false"
            android:contextClickable="false"
            tools:ignore="ContentDescription" />
    </FrameLayout>

    <TextView
        android:id="@+id/policy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_18"
        android:layout_marginBottom="@dimen/dp_81"
        android:maxWidth="@dimen/dp_720"
        android:text="@string/user_login_read_and_agree"
        android:textColor="@color/vip_store_vip_sync_vip_tip_text"
        android:textSize="@dimen/sp_34"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/checkbox_trigger" />

    <LinearLayout
        android:id="@+id/agreement_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_51"
        android:layout_marginTop="@dimen/dp_83"
        android:layout_marginEnd="@dimen/dp_51"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wechat_qrcode_login">

        <Space
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:layout_width="@dimen/dp_51"
            android:layout_height="@dimen/dp_51"
            android:layout_gravity="center_vertical"
            android:background="@drawable/pad_login_dialog_checkbox_bg"
            android:padding="@dimen/dp_7"
            android:src="@drawable/pad_login_dialog_checkbox_image_src" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_18"
            android:text="@string/user_login_read_and_agree"
            android:textColor="@color/vip_store_vip_sync_vip_tip_text"
            android:textSize="@dimen/sp_30" />

        <TextView
            android:id="@+id/user_agreement"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/guide_terms_hint_part_2"
            android:textColor="#FF2E82FF"
            android:textSize="@dimen/sp_30"
            android:visibility="gone" />

        <TextView
            android:id="@+id/privacy_policy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/guide_terms_hint_part_4"
            android:textColor="#FF2E82FF"
            android:textSize="@dimen/sp_30"
            android:visibility="gone" />

        <Space
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>

    <Space
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_55"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/policy" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/page_thumbnail"
        android:layout_width="@dimen/dp_288"
        android:layout_height="@dimen/dp_403"
        android:background="@drawable/phone_page_list_thumbnail"
        android:padding="@dimen/dp_4"
        android:scaleType="fitCenter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"
        tools:src="@drawable/cover4" />

    <TextView
        android:id="@+id/page_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/page_thumbnail"
        tools:text="14" />

    <ImageView
        android:id="@+id/page_select"
        android:layout_width="@dimen/dp_52"
        android:layout_height="@dimen/dp_52"
        android:layout_marginEnd="@dimen/dp_14"
        android:layout_marginBottom="@dimen/dp_14"
        android:background="@drawable/phone_note_tool_select_export_pages_checked_background_shape"
        android:scaleType="centerInside"
        android:padding="@dimen/dp_10"
        android:src="@drawable/phone_note_tool_select_export_pages_checked_foreground"
        app:layout_constraintBottom_toBottomOf="@id/page_thumbnail"
        app:layout_constraintEnd_toEndOf="@id/page_thumbnail"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/member_benefit_article_tick"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_48"
        android:src="@drawable/phone_member_benefit_tick_icon"
        app:layout_constraintBottom_toBottomOf="@+id/member_benefit_article_slogan"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/member_benefit_article_slogan" />

    <TextView
        android:id="@+id/member_benefit_article_slogan"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_24"
        android:text="@string/kilonotes_membership_article_stickers_benefits"
        android:textColor="@color/text_color_666666"
        android:textSize="@dimen/sp_36"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/member_benefit_article_tick"
        app:layout_constraintTop_toTopOf="@+id/member_benefit_article_tick" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/device_tab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/backup_space_download_note_bg_selector"
        android:enabled="true"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_32"
        android:paddingVertical="@dimen/dp_15"
        android:textColor="@color/backup_space_device_tab_color"
        android:textSize="@dimen/sp_36"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="当前设备" />

</androidx.constraintlayout.widget.ConstraintLayout>
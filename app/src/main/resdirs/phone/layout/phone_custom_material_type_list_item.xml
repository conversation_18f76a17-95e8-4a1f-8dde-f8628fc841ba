<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/phone_note_material_type_background_selector"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_30"
        android:paddingVertical="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="@color/note_material_type_text_color_selector"
        android:textSize="@dimen/sp_45"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/export" />

    <View
        android:id="@+id/sign_for_text"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:visibility="invisible"
        android:background="@drawable/sign_red_bg"
        app:layout_constraintTop_toTopOf="@id/text"
        app:layout_constraintBottom_toTopOf="@id/text"
        app:layout_constraintEnd_toEndOf="@id/text" />

</androidx.constraintlayout.widget.ConstraintLayout>
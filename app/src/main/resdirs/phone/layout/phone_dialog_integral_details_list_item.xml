<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/ai_function_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_36"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_39"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="AI对话"/>

    <TextView
        android:id="@+id/date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_21"
        android:textColor="@color/backup_item_time"
        android:textSize="@dimen/sp_37"
        app:layout_constraintTop_toBottomOf="@id/ai_function_title"
        app:layout_constraintStart_toStartOf="@+id/ai_function_title"
        tools:text="2025年5月26日 11:56"/>

    <TextView
        android:id="@+id/spending"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_36"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_37"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ai_function_title"
        app:layout_constraintBottom_toBottomOf="@+id/ai_function_title"
        tools:text="+ 300"/>

    <TextView
        android:id="@+id/balance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/backup_item_time"
        android:textSize="@dimen/sp_37"
        app:layout_constraintEnd_toEndOf="@+id/spending"
        app:layout_constraintTop_toTopOf="@+id/date"
        app:layout_constraintBottom_toBottomOf="@+id/date"
        tools:text="剩余：1008546"/>

    <!-- 横线 -->
    <View
        android:id="@+id/divider_horizontal"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_1"
        android:layout_marginTop="@dimen/dp_24"
        android:background="@color/backup_space_notes_line_color"
        app:layout_constraintTop_toBottomOf="@+id/date"
        app:layout_constraintStart_toStartOf="@+id/ai_function_title"
        app:layout_constraintEnd_toEndOf="@+id/balance" />


</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/decoupage_tool_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_200"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_120"
            android:text="@string/decoupage_tool_title"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_84"
            android:layout_height="@dimen/dp_84"
            android:layout_marginStart="@dimen/dp_48"
            android:padding="@dimen/dp_6"
            android:src="@drawable/decoupage_preview_back"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title" />

        <ImageView
            android:id="@+id/close"
            android:layout_width="@dimen/dp_84"
            android:layout_height="@dimen/dp_84"
            android:layout_marginStart="@dimen/dp_48"
            android:padding="@dimen/dp_6"
            android:src="@drawable/decoupage_close"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title" />

        <ImageView
            android:id="@+id/save"
            android:layout_width="@dimen/dp_84"
            android:layout_height="@dimen/dp_84"
            android:layout_marginEnd="@dimen/dp_48"
            android:padding="@dimen/dp_6"
            android:src="@drawable/decoupage_preview_save"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar">

        <TextView
            android:id="@+id/previos_step"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/dp_70"
            android:minWidth="@dimen/dp_180"
            android:layout_marginStart="@dimen/dp_48"
            android:layout_marginBottom="@dimen/dp_62"
            android:background="@drawable/decoupage_previous_step_background"
            android:gravity="center_horizontal"
            android:paddingHorizontal="@dimen/dp_36"
            android:paddingVertical="@dimen/dp_10"
            android:text="@string/previous_step"
            android:textColor="@color/decoupage_previous_step"
            android:textSize="@dimen/sp_36"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="@+id/step2"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/step1"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginStart="@dimen/dp_102"
            android:background="@drawable/decoupage_step_background_selector"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/decoupage_step_selected"
            android:textSize="@dimen/sp_38"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/dotted_line_between_1_and_2"
            app:layout_constraintTop_toTopOf="@+id/step2" />

        <TextView
            android:id="@+id/step1_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_300"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="center"
            android:text="@string/decoupage_select_paper"
            android:layout_marginTop="@dimen/dp_10"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_42"
            app:layout_constraintEnd_toEndOf="@+id/step1"
            app:layout_constraintStart_toStartOf="@+id/step1"
            app:layout_constraintTop_toBottomOf="@+id/step1" />

        <View
            android:id="@+id/dotted_line_between_1_and_2"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_10"
            android:background="@drawable/decoupage_step_dotted_line_selector"
            app:layout_constraintBottom_toBottomOf="@+id/step2"
            app:layout_constraintStart_toEndOf="@id/step1"
            app:layout_constraintEnd_toStartOf="@id/step2"
            app:layout_constraintTop_toTopOf="@+id/step2" />

        <TextView
            android:id="@+id/step2"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginTop="@dimen/dp_162"
            android:background="@drawable/decoupage_step_background_selector"
            android:gravity="center"
            android:text="2"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_38"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/step2_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_300"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="center"
            android:text="@string/decoupage_select_fold_type"
            android:layout_marginTop="@dimen/dp_10"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_42"
            app:layout_constraintEnd_toEndOf="@+id/step2"
            app:layout_constraintStart_toStartOf="@+id/step2"
            app:layout_constraintTop_toBottomOf="@+id/step2" />

        <View
            android:id="@+id/dotted_line_between_2_and_3"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_10"
            android:background="@drawable/decoupage_step_dotted_line_selector"
            app:layout_constraintBottom_toBottomOf="@+id/step2"
            app:layout_constraintStart_toEndOf="@id/step2"
            app:layout_constraintEnd_toStartOf="@id/step3"
            app:layout_constraintTop_toTopOf="@+id/step2" />

        <TextView
            android:id="@+id/step3"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_102"
            android:background="@drawable/decoupage_step_background_selector"
            android:gravity="center"
            android:text="3"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_38"
            app:layout_constraintStart_toEndOf="@id/dotted_line_between_2_and_3"
            app:layout_constraintTop_toTopOf="@id/step2"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/step3_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_300"
            android:maxLines="2"
            android:ellipsize="end"
            android:text="@string/decoupage_cut"
            android:layout_marginTop="@dimen/dp_10"
            android:gravity="center"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_42"
            app:layout_constraintEnd_toEndOf="@+id/step3"
            app:layout_constraintStart_toStartOf="@+id/step3"
            app:layout_constraintTop_toBottomOf="@+id/step3" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/next_step_background"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/dp_70"
            android:minWidth="@dimen/dp_180"
            android:layout_marginEnd="@dimen/dp_48"
            android:layout_marginBottom="@dimen/dp_62"
            android:background="@drawable/decoupage_next_step_background"
            android:paddingVertical="@dimen/dp_10"
            app:layout_constraintBottom_toTopOf="@+id/step2"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/next_step"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_horizontal"
                android:maxLines="2"
                android:text="@string/next_step"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_36"
                android:paddingHorizontal="@dimen/dp_36"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/complete"
                android:layout_width="60dp"
                android:layout_height="@dimen/dp_40"
                android:src="@drawable/decoupage_complete"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.topstack.kilonotes.base.materialtool.decoupage.DecoupageView
        android:id="@+id/decoupage_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toTopOf="@id/select_paper_tool_bar"
        app:layout_constraintTop_toBottomOf="@id/top_bar"
        android:layout_marginHorizontal="@dimen/dp_48" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/select_paper_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_200"
        android:background="@color/select_fold_type_recycle_view_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/decoupage_view">
        <com.lihang.ShadowLayout
            android:id="@+id/select_image_shadow"
            android:layout_width="@dimen/dp_120"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp_48"
            android:layout_marginVertical="@dimen/dp_40"
            app:layout_constraintEnd_toStartOf="@id/select_paper_recycle_view"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:hl_cornerRadius="@dimen/dp_18">
            <ImageView
                android:id="@+id/select_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/select_fold_type_recycle_view_color"
                android:src="@drawable/decoupage_image_icon"/>
            <ImageView
                android:id="@+id/select_image_is_selected"
                android:src="@drawable/decoupage_image_is_selected_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="invisible"/>
        </com.lihang.ShadowLayout>
        <ImageView
            android:id="@+id/delete_image"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            app:layout_constraintTop_toTopOf="@+id/select_image_shadow"
            app:layout_constraintEnd_toEndOf="@id/select_image_shadow"
            android:src="@drawable/decoupage_delete_image_icon"
            android:visibility="invisible"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_8"/>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/select_paper_recycle_view"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintStart_toEndOf="@+id/select_image_shadow"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/select_fold_type_recycle_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        android:visibility="invisible"
        android:paddingTop="@dimen/dp_30"
        android:paddingBottom="@dimen/dp_18"
        android:background="@color/select_fold_type_recycle_view_color"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="@+id/select_paper_tool_bar"
        app:layout_constraintTop_toTopOf="@+id/select_paper_tool_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/select_crop_size_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="invisible"
        android:background="@color/select_fold_type_recycle_view_color"
        app:layout_constraintBottom_toBottomOf="@+id/select_paper_tool_bar"
        app:layout_constraintTop_toTopOf="@+id/select_paper_tool_bar">

        <ImageView
            android:id="@+id/undo"
            android:layout_width="@dimen/dp_108"
            android:layout_height="@dimen/dp_108"
            android:layout_marginStart="@dimen/dp_48"
            android:src="@drawable/decoupage_cut_undo_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/redo"
            android:layout_width="@dimen/dp_108"
            android:layout_height="@dimen/dp_108"
            android:layout_marginStart="@dimen/dp_60"
            android:src="@drawable/decoupage_cut_redo_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/undo"
            app:layout_constraintTop_toTopOf="parent" />

        <com.topstack.kilonotes.pad.component.CircleSizeSelectorItemView
            android:id="@+id/crop_size_small"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:padding="@dimen/dp_16"
            app:layout_constraintTop_toTopOf="parent"
            app:radius="@dimen/dp_7"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/redo"
            android:layout_marginStart="@dimen/dp_120"/>
        <com.topstack.kilonotes.pad.component.CircleSizeSelectorItemView
            android:id="@+id/crop_size_medium"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:padding="@dimen/dp_12"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/crop_size_small"
            app:layout_constraintEnd_toStartOf="@id/crop_size_large"/>
        <com.topstack.kilonotes.pad.component.CircleSizeSelectorItemView
            android:id="@+id/crop_size_large"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:padding="@dimen/dp_6"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/dp_120"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
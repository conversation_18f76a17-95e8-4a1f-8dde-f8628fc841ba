<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_30"
    android:paddingVertical="@dimen/dp_48">

    <View
        android:id="@+id/empty_title"
        android:layout_width="@dimen/dp_434"
        android:layout_height="@dimen/dp_60"
        android:background="#DEDEDE"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/empty_content1"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_37"
        android:layout_marginTop="@dimen/dp_24"
        android:background="#EFEFEF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/empty_title" />

    <View
        android:id="@+id/empty_content2"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_37"
        android:layout_marginTop="@dimen/dp_15"
        android:background="#EFEFEF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/empty_content1" />

    <View
        android:id="@+id/empty_content3"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_37"
        android:layout_marginTop="@dimen/dp_15"
        android:background="#EFEFEF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/empty_content2" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/empty_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="empty_title,empty_content1,empty_content2,empty_content3" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#333333"
        android:textSize="@dimen/sp_45"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="下午茶时光手帐册" />

    <TextView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="#666666"
        android:textSize="@dimen/sp_36"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:text="下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册下午茶时光手帐册" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/description_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="title,content" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/empty_data_view"
        android:layout_width="@dimen/dp_480"
        android:layout_height="@dimen/dp_461"
        android:layout_marginTop="@dimen/dp_298"
        android:src="@drawable/note_material_reload"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/empty_data_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bad_network_tips"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dp_48"
        android:textSize="@dimen/sp_30"
        android:textColor="@color/backup_subtitle_color"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/empty_data_view"
        />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/template_list_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </androidx.recyclerview.widget.RecyclerView>
</androidx.constraintlayout.widget.ConstraintLayout>

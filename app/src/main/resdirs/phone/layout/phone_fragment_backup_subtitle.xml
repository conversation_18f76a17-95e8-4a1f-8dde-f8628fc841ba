<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/backup_subtitle_1"
        android:textColor="@color/backup_subtitle_color"
        android:textSize="@dimen/sp_36" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/backup_subtitle_2"
        android:textColor="@color/backup_subtitle_color"
        android:textSize="@dimen/sp_36" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/backup_subtitle_3"
        android:textColor="@color/backup_subtitle_color"
        android:textSize="@dimen/sp_36" />
</LinearLayout>
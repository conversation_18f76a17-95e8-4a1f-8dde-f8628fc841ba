<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:layout_height="@dimen/dp_430"
    tools:layout_weight="@dimen/dp_1080">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:text="@string/text_paragraph"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/text_align_start"
        android:layout_width="@dimen/dp_120"
        android:layout_height="@dimen/dp_120"
        android:layout_marginEnd="@dimen/dp_180"
        android:src="@drawable/phone_note_tool_text_align_start_selector"
        app:layout_constraintEnd_toStartOf="@id/text_align_center"
        app:layout_constraintTop_toTopOf="@id/text_align_center" />

    <ImageView
        android:id="@+id/text_align_center"
        android:layout_width="@dimen/dp_120"
        android:layout_height="@dimen/dp_120"
        android:layout_marginTop="@dimen/dp_90"
        android:src="@drawable/phone_note_tool_text_align_center_selector"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <ImageView
        android:id="@+id/text_align_end"
        android:layout_width="@dimen/dp_120"
        android:layout_height="@dimen/dp_120"
        android:layout_marginStart="@dimen/dp_180"
        android:src="@drawable/phone_note_tool_text_align_end_selector"
        app:layout_constraintStart_toEndOf="@id/text_align_center"
        app:layout_constraintTop_toTopOf="@id/text_align_center" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_156"
    android:layout_height="@dimen/dp_320">

    <ImageView
        android:id="@+id/image"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_200"
        android:adjustViewBounds="true"
        android:background="@drawable/note_paper_bg"
        android:elevation="@dimen/dp_3"
        android:scaleType="fitEnd"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/type"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_100"
        android:ellipsize="end"
        android:gravity="top|center_horizontal"
        android:maxLines="2"
        android:textColor="@color/select_cover_paper_text"
        android:textSize="@dimen/sp_40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/image"
        app:layout_constraintStart_toStartOf="@id/image" />

    <ImageView
        android:id="@+id/selected"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_11"
        android:layout_marginBottom="@dimen/dp_11"
        android:elevation="@dimen/dp_3"
        android:src="@drawable/phone_create_note_icon_item_select"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/image"
        app:layout_constraintEnd_toEndOf="@id/image"
        tools:ignore="ContentDescription"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
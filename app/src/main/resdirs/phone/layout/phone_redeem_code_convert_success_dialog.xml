<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.topstack.kilonotes.base.component.dialog.AlterLottieAnimationView
        android:id="@+id/animation_view"
        android:layout_width="@dimen/dp_990"
        android:layout_height="@dimen/dp_750"
        android:layout_marginTop="@dimen/dp_300"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/redeem_code_convert_success_title"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_54"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/animation_view" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_78"
        android:layout_height="@dimen/dp_78"
        android:layout_marginTop="@dimen/dp_27"
        android:padding="@dimen/dp_5"
        android:src="@drawable/dialog_redeem_code_convert_success_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content" />


</androidx.constraintlayout.widget.ConstraintLayout>
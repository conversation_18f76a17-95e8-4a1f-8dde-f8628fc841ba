<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:layout_height="@dimen/dp_361"
    tools:layout_width="@dimen/dp_1080">


    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:text="@string/image_tool_alpha"
        android:textColor="@color/note_tool_text_alpha_title_color"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/alpha_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_212"
        android:text="@string/image_alpha_min"
        android:textColor="@color/note_tool_text_alpha_progress_value_color"
        android:textSize="@dimen/sp_36"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_312"
        android:text="@string/image_alpha_max"
        android:textColor="@color/note_tool_text_alpha_progress_value_color"
        android:textSize="@dimen/sp_36"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/alpha_start" />

    <TextView
        android:id="@+id/alpha"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_204"
        android:layout_marginEnd="@dimen/dp_104"
        android:textColor="#2E82FF"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="47%" />

    <SeekBar
        android:id="@+id/alpha_seekBar"
        android:layout_width="@dimen/dp_600"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_92"
        android:maxHeight="@dimen/dp_12"
        android:progress="100"
        android:progressDrawable="@drawable/phone_image_tools_alpha_seekbar_progress_background"
        tools:thumb="@drawable/pen_and_text_size_tracker_background"
        app:layout_constraintBottom_toBottomOf="@id/alpha_start"
        app:layout_constraintStart_toEndOf="@id/alpha_start"
        app:layout_constraintTop_toTopOf="@id/alpha_start" />


</androidx.constraintlayout.widget.ConstraintLayout>
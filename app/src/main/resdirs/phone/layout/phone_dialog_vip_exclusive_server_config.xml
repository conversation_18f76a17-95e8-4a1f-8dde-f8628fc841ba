<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/vip_content_layout"
        android:layout_width="@dimen/dp_940"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_222"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/vip_exclusive_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_68"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:text="@string/vip_exclusive_title"
            android:textColor="@color/vip_exclusive_title_color"
            android:textSize="@dimen/sp_54"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/vip_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_60"
            android:ellipsize="end"
            android:gravity="top|center"
            android:maxLines="3"
            android:text="@string/vip_exclusive_content"
            android:textColor="@color/vip_exclusive_content_color"
            android:textSize="@dimen/sp_42"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vip_exclusive_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageView
        android:id="@+id/vip_top"
        android:layout_width="@dimen/dp_940"
        android:layout_height="@dimen/dp_340"
        android:gravity="center"
        android:scaleType="fitEnd"
        app:layout_constraintBottom_toTopOf="@id/vip_content_layout"
        app:layout_constraintEnd_toEndOf="@id/vip_content_layout"
        app:layout_constraintStart_toStartOf="@id/vip_content_layout" />

    <ImageView
        android:id="@+id/vip_bottom"
        android:layout_width="@dimen/dp_940"
        android:layout_height="@dimen/dp_320"
        android:gravity="center"
        android:scaleType="fitStart"
        app:layout_constraintEnd_toEndOf="@id/vip_content_layout"
        app:layout_constraintStart_toStartOf="@id/vip_content_layout"
        app:layout_constraintTop_toBottomOf="@id/vip_content_layout" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/vip_buy"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_170"
        android:layout_gravity="center_vertical"
        android:layout_marginBottom="@dimen/dp_80"
        app:layout_constraintBottom_toBottomOf="@id/vip_bottom"
        app:layout_constraintEnd_toEndOf="@id/vip_bottom"
        app:layout_constraintStart_toStartOf="@id/vip_bottom">

        <TextView
            android:id="@+id/vip_buy_btn_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dp_60"
            android:layout_marginTop="@dimen/dp_47"
            android:layout_marginBottom="@dimen/dp_56"
            android:ellipsize="end"
            android:gravity="center"
            android:maxWidth="@dimen/dp_820"
            android:maxLines="2"
            android:minWidth="@dimen/dp_640"
            android:text="@string/buy_vip"
            android:textColor="@color/handbook_detail_buy_text_color"
            android:textSize="@dimen/sp_48"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_78"
        android:layout_height="@dimen/dp_78"
        android:layout_marginTop="@dimen/dp_40"
        android:src="@drawable/dialog_instant_alpha_close"
        app:layout_constraintEnd_toEndOf="@id/vip_content_layout"
        app:layout_constraintStart_toStartOf="@id/vip_content_layout"
        app:layout_constraintTop_toBottomOf="@id/vip_bottom" />

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/doodle_tools_popup_bg">


    <TextView
        android:id="@+id/filling_color"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="8.5dp"
        android:paddingStart="12dp"
        android:paddingEnd="10dp"
        android:text="@string/graph_to_filling"
        android:textColor="#ffffff"
        android:textSize="@dimen/sp_36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>

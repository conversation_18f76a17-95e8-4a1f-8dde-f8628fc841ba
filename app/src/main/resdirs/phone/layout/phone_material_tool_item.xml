<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="MissingDefaultResource">

    <ImageView
        android:id="@+id/tool_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_512"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_26"
        android:paddingHorizontal="@dimen/dp_34"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/phone_instant_alpha_guide" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tool_tips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_82"
        android:layout_marginTop="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_200"
        android:maxLines="1"
        android:text="@string/instant_alpha_tool_title"
        android:textColor="@color/smart_cutout_tool_tips"
        android:textSize="@dimen/sp_56"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/tool_bg"
        app:layout_constraintStart_toStartOf="@id/tool_bg"
        app:layout_constraintTop_toTopOf="@id/tool_bg" />

    <TextView
        android:id="@+id/tool_eg_tips"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_350"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/instant_alpha_tool_subtitle"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_40"
        app:layout_constraintEnd_toEndOf="@+id/tool_bg"
        app:layout_constraintStart_toStartOf="@id/tool_tips"
        app:layout_constraintTop_toBottomOf="@id/tool_tips" />


    <TextView
        android:id="@+id/instant_alpha_img_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_31"
        android:background="@drawable/instant_alpha_save_tips_background"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxWidth="@dimen/dp_560"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_15"
        android:paddingVertical="@dimen/dp_11"
        android:text="@string/instant_alpha_tool_free_tip"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toEndOf="@id/tool_bg"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/instant_alpha_vip_log"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_54"
        android:layout_marginTop="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_66"
        android:src="@drawable/phone_instant_alpha_guide_vip"
        app:layout_constraintEnd_toEndOf="@id/tool_bg"
        app:layout_constraintTop_toTopOf="@id/tool_bg" />

    <ImageView
        android:id="@+id/upgrade_tips"
        android:layout_width="@dimen/dp_126"
        android:layout_height="@dimen/dp_126"
        android:layout_marginStart="@dimen/dp_34"
        android:layout_marginTop="@dimen/dp_10"
        android:src="@drawable/instant_alpha_upgrade_tips"
        app:layout_constraintStart_toStartOf="@id/tool_bg"
        app:layout_constraintTop_toTopOf="@id/tool_bg" />


</androidx.constraintlayout.widget.ConstraintLayout>
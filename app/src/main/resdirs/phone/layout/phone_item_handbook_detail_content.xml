<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_36"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1024:768"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider" />

    <View
        android:id="@+id/empty_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#F4F4F4"
        app:layout_constraintDimensionRatio="1024:768"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider" />

    <ImageView
        android:id="@+id/empty_image"
        android:layout_width="@dimen/dp_370"
        android:layout_height="@dimen/dp_219"
        android:src="@drawable/handbook_detail_banner_default"
        app:layout_constraintBottom_toBottomOf="@id/empty_bg"
        app:layout_constraintEnd_toEndOf="@id/empty_bg"
        app:layout_constraintStart_toStartOf="@id/empty_bg"
        app:layout_constraintTop_toTopOf="@id/empty_bg" />

    <TextView
        android:id="@+id/empty_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_36"
        android:text="@string/handbook_detail_loading"
        android:textColor="#909090"
        android:textSize="@dimen/sp_30"
        app:layout_constraintEnd_toStartOf="@id/loading"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/empty_image"
        app:layout_constraintTop_toBottomOf="@id/empty_image" />

    <com.topstack.kilonotes.base.component.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="@dimen/dp_65"
        android:layout_height="@dimen/dp_50"
        android:layout_marginStart="@dimen/dp_12"
        app:bounceHeight="@dimen/dp_20"
        app:dotRadius="@dimen/dp_4"
        app:dotSpace="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@id/empty_text"
        app:layout_constraintEnd_toEndOf="@id/empty_image"
        app:layout_constraintStart_toEndOf="@id/empty_text" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/empty_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="empty_bg,empty_image,empty_text,loading" />


</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_round_corner_bg"
    android:padding="@dimen/dp_48"
    android:rotationY="180">

    <com.topstack.kilonotes.base.component.view.EllipsizedTextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_36"
        android:ellipsize="middle"
        android:maxLines="3"
        android:rotationY="180"
        android:textColor="#FF333333"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/msg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="千本笔记" />

    <TextView
        android:id="@+id/msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:rotationY="180"
        android:textColor="#FF727272"
        android:textSize="@dimen/sp_42"
        app:layout_constraintBottom_toTopOf="@id/button_flow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:text="要访问您的照片" />

    <TextView
        android:id="@+id/positiveBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minWidth="@dimen/dp_112"
        android:rotationY="180"
        android:textColor="#FF2E82FF"
        android:textSize="@dimen/sp_48"
        tools:text="前往设置" />

    <TextView
        android:id="@+id/neutralBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minWidth="@dimen/dp_112"
        android:rotationY="180"
        android:textColor="#FF2E82FF"
        android:textSize="@dimen/sp_48"
        tools:text="中立" />

    <TextView
        android:id="@+id/additionalBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minWidth="@dimen/dp_112"
        android:rotationY="180"
        android:textColor="#FF2E82FF"
        android:textSize="@dimen/sp_48"
        tools:text="中立" />

    <TextView
        android:id="@+id/negativeBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minWidth="@dimen/dp_112"
        android:rotationY="180"
        android:textColor="#FF2E82FF"
        android:textSize="@dimen/sp_48"
        tools:text="好的" />

    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/button_flow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        app:constraint_referenced_ids="positiveBtn,neutralBtn,additionalBtn,negativeBtn"
        app:flow_firstHorizontalStyle="packed"
        app:flow_horizontalGap="@dimen/dp_32"
        app:flow_verticalAlign="bottom"
        app:flow_verticalGap="@dimen/dp_20"
        app:flow_wrapMode="aligned"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/msg" />

</androidx.constraintlayout.widget.ConstraintLayout>
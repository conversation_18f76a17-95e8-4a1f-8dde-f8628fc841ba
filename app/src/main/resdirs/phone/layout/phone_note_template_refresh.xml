<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.topstack.kilonotes.base.component.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="@dimen/dp_160"
        android:layout_height="@dimen/dp_70"
        app:bounceHeight="@dimen/dp_50"
        app:dotRadius="@dimen/dp_10"
        app:dotSpace="@dimen/dp_20"
        android:layout_marginVertical="@dimen/dp_30"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/no_data_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/note_template_no_more_data_tip"
        android:textSize="@dimen/sp_40"
        android:textColor="@color/backup_subtitle_color"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
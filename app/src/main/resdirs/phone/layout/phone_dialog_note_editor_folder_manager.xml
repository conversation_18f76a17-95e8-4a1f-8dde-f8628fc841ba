<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_1733">

    <TextView
        android:id="@+id/cancel"
        android:layout_width="@dimen/dp_230"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_60"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/cancel"
        android:textColor="@color/progress_bar"
        android:textSize="@dimen/sp_48"
        app:layout_constraintBottom_toTopOf="@id/select_folder_list"
        app:layout_constraintEnd_toStartOf="@id/title"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/dp_450"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:text="@string/folder_chosen_position"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cancel" />

    <ImageView
        android:id="@+id/add_folder"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginEnd="@dimen/dp_48"
        android:src="@drawable/phone_note_editor_add_folder"
        app:layout_constraintBottom_toBottomOf="@id/cancel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/cancel" />

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/select_folder_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1425"
        android:paddingTop="@dimen/dp_80"
        app:layout_constraintBottom_toTopOf="@id/bottom_view_group"
        app:scrollOrientation="vertical" />

    <ImageView
        android:id="@+id/empty_img"
        android:layout_width="@dimen/dp_480"
        android:layout_height="@dimen/dp_480"
        android:scaleType="centerInside"
        android:src="@drawable/note_backup_empty_background"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/select_folder_list"
        app:layout_constraintEnd_toEndOf="@id/select_folder_list"
        app:layout_constraintStart_toStartOf="@id/select_folder_list"
        app:layout_constraintTop_toTopOf="@id/select_folder_list"
        app:layout_constraintVertical_bias="0.4"
        />

    <TextView
        android:id="@+id/empty_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/folder_move_empty_tips"
        android:visibility="invisible"
        android:gravity="center"
        android:layout_marginTop="@dimen/dp_36"
        android:textColor="@color/backup_subtitle_color"
        android:textSize="@dimen/sp_36"
        app:layout_constraintStart_toStartOf="@id/empty_img"
        app:layout_constraintEnd_toEndOf="@id/empty_img"
        app:layout_constraintTop_toBottomOf="@id/empty_img"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="empty_img,empty_tips"/>

    <LinearLayout
        android:id="@+id/bottom_view_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/phone_note_material_bottom_group_bg"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/move_note_to_folder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_30"
            android:background="@drawable/phone_note_material_confirm_button_background_selector"
            android:gravity="center"
            android:maxWidth="@dimen/dp_984"
            android:maxLines="1"
            android:ellipsize="end"
            android:minWidth="@dimen/dp_580"
            android:paddingHorizontal="@dimen/dp_36"
            android:paddingVertical="@dimen/dp_27"
            android:text="@string/folder_move_to_chosen_position"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
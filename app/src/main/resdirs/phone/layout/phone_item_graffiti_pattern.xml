<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/phone_note_graffiti_pattern_background_selector"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_24"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_15"
        android:textColor="@color/note_material_type_text_color_selector"
        android:textSize="@dimen/sp_38"
        tools:text="@string/export" />

</FrameLayout>
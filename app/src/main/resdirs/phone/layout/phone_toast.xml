<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@android:id/message"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/toast_background"
    android:elevation="@dimen/dp_8"
    android:gravity="center"
    android:includeFontPadding="false"
    android:paddingHorizontal="@dimen/dp_24"
    android:paddingVertical="@dimen/dp_16"
    android:textColor="@color/white"
    android:textSize="@dimen/sp_48"
    tools:text="<PERSON><PERSON> Note's Toast" />

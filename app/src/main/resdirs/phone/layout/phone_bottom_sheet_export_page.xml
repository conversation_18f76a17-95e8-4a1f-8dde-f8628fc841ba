<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_104"
        android:layout_height="@dimen/dp_122"
        android:layout_marginStart="@dimen/dp_14"
        android:padding="@dimen/dp_40"
        android:scaleType="centerInside"
        android:src="@drawable/phone_note_export_icon_back"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/title"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_48"
        android:maxLines="2"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/sp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="7/8" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/page_thumbnail_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_988"
        android:layout_marginTop="@dimen/dp_90"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <View
        android:id="@+id/split_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_marginTop="@dimen/dp_26"
        android:background="@color/bottom_sheet_split_line"
        app:layout_constraintTop_toBottomOf="@id/page_thumbnail_list" />

    <TextView
        android:id="@+id/select_all"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_143"
        android:gravity="center"
        android:textSize="@dimen/sp_48"
        android:textColor="@color/text_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/split_line"
        tools:text="@string/export_page_cancel_select_all" />

</androidx.constraintlayout.widget.ConstraintLayout>
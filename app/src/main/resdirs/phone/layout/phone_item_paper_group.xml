<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/paper_group_name"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_115"
        android:paddingStart="@dimen/dp_48"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />

    <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
        android:id="@+id/paper_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_20"
        app:layout_constraintTop_toBottomOf="@id/paper_group_name"
        app:scrollOrientation="vertical" />

</androidx.constraintlayout.widget.ConstraintLayout>
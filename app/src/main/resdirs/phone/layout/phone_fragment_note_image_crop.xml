<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_constraint_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.topstack.kilonotes.base.imagecrop.ImageCropView
        android:id="@+id/crop_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_62"
        android:layout_height="@dimen/dp_62"
        android:layout_marginStart="@dimen/dp_33"
        android:src="@drawable/phone_image_crop_icon_cancel"
        app:layout_constraintBottom_toBottomOf="@id/complete"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/complete" />

    <TextView
        android:id="@+id/restore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/imagecrop_restore"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/complete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/complete" />

    <ImageView
        android:id="@+id/complete"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_56"
        android:layout_marginEnd="@dimen/dp_46"
        android:src="@drawable/dialog_icon_image_crop_complete_selector"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/mode_switch"
        android:layout_width="@dimen/dp_988"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_80"
        android:background="@drawable/phone_note_image_crop_button_group_shape"
        android:minHeight="@dimen/dp_90"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/complete">

        <TextView
            android:id="@+id/regular_mode"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/phone_dialog_image_crop_button_stroke"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:text="@string/imagecrop_regular_crop"
            android:textColor="@color/select_photo_button_text"
            android:textSize="@dimen/sp_42" />

        <TextView
            android:id="@+id/irregular_mode"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/phone_dialog_image_crop_button_stroke"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:text="@string/imagecrop_irregular_crop"
            android:textColor="@color/select_photo_button_text"
            android:textSize="@dimen/sp_42" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/keep_image_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_36"
        android:layout_marginBottom="@dimen/dp_36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/keep_image"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:background="@drawable/dialog_image_crop_save_original_image_bg"
            android:padding="@dimen/dp_8"
            android:src="@drawable/dialog_image_crop_save_original_image_src" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_96"
            android:gravity="center"
            android:text="@string/imagecrop_keep_original_image"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_42" />
    </FrameLayout>

    <TextView
        android:id="@+id/tips"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_58"
        android:layout_marginBottom="@dimen/dp_255"
        android:background="@drawable/phone_imagecrop_tips_background"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_56"
        android:paddingTop="@dimen/dp_5"
        android:paddingEnd="@dimen/dp_56"
        android:paddingBottom="@dimen/dp_5"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_34"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="规则裁剪"
        tools:visibility="visible" />


    <com.lihang.ShadowLayout
        android:id="@+id/import_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:hl_cornerRadius="@dimen/dp_30"
        app:hl_shadowLimit="@dimen/dp_20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/import_layout"
            android:layout_width="@dimen/dp_400"
            android:layout_height="@dimen/dp_440">

            <ImageView
                android:id="@+id/import_img"
                android:layout_width="@dimen/dp_292"
                android:layout_height="@dimen/dp_192"
                android:layout_marginTop="@dimen/dp_73"
                android:paddingStart="@dimen/dp_25"
                android:src="@drawable/instant_alpha_import"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/import_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_36"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/instant_alpha_import_img"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_48"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/import_img" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.lihang.ShadowLayout>


    <com.topstack.kilonotes.base.imagemagnifier.ImageMagnifierView
        android:id="@+id/magnifier"
        android:layout_width="@dimen/dp_360"
        android:layout_height="@dimen/dp_360"
        android:layout_marginTop="@dimen/dp_20"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mode_switch" />

    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/loading"
        style="?android:attr/progressBarStyle"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:layout_gravity="center"
        android:progressDrawable="@color/black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
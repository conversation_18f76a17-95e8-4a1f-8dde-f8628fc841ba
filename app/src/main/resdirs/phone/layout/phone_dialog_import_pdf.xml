<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/popup_import_pdf_background">


    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_44"
        android:layout_height="@dimen/dp_44"
        android:layout_marginTop="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_48"
        android:src="@drawable/phone_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />


    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_48"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginTop="@dimen/dp_100"
        android:paddingHorizontal="@dimen/dp_32"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/import_pdf_success" />

    <TextView
        android:id="@+id/subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="3"
        android:ellipsize="end"
        android:textSize="@dimen/sp_36"
        tools:text="用户体验设计的商业价值是什么? 用户体验"
        android:textColor="#666666"
        android:layout_marginTop="@dimen/dp_36"
        android:paddingHorizontal="@dimen/dp_32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        />



    <TextView
        android:id="@+id/positive_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_10"
        android:gravity="center"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginBottom="@dimen/dp_20"
        android:visibility="invisible"
        android:text="@string/know"
        android:textColor="@color/progress_bar"
        android:textSize="@dimen/sp_28"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <FrameLayout
        android:id="@+id/progress_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_126"
        app:layout_constraintTop_toBottomOf="@id/subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ProgressBar
            android:id="@+id/progress"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_24"
            android:max="100"
            android:progress="0"
            android:progressDrawable="@drawable/progress_bar"
            android:visibility="visible"
            tools:progress="50"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/mask"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_24"
            android:src="@drawable/progress_bar_mask"
            tools:ignore="ContentDescription" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
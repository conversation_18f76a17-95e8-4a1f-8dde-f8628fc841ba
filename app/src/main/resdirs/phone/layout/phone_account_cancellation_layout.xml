<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_1350">

    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_68"
        android:layout_marginEnd="@dimen/dp_48"
        android:src="@drawable/phone_redeem_code_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_60"
        android:text="@string/account_cancellation"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/sp_48"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_828"
        android:layout_marginHorizontal="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_80"
        android:lineSpacingExtra="@dimen/dp_8"
        android:scrollbars="vertical"
        android:text="@string/account_cancellation_warning"
        android:textColor="@color/text_disable"
        android:textSize="@dimen/sp_36"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <TextView
        android:id="@+id/cancel_user_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:text="@string/cancel_user_id"
        android:textSize="@dimen/sp_36"
        app:layout_constraintStart_toStartOf="@+id/content"
        app:layout_constraintTop_toBottomOf="@+id/content" />

    <com.lihang.ShadowLayout
        android:id="@+id/copy_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/dp_37"
        app:hl_cornerRadius="@dimen/dp_41"
        app:layout_constraintStart_toEndOf="@+id/cancel_user_id"
        app:layout_constraintTop_toTopOf="@+id/cancel_user_id"
        app:layout_constraintBottom_toBottomOf="@+id/cancel_user_id">

        <TextView
            android:id="@+id/copy"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingVertical="@dimen/dp_5"
            android:paddingHorizontal="@dimen/dp_25"
            android:layout_gravity="center"
            android:background="@color/redeem_code_convert_success_duration_color"
            android:gravity="center"
            android:text="@string/copy"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_36" />
    </com.lihang.ShadowLayout>

    <View
        android:id="@+id/divider_horizontal"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_2"
        android:background="@color/user_benefits_blur_stroke"
        android:layout_marginBottom="@dimen/dp_170"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.lihang.ShadowLayout
        android:id="@+id/know_container"
        android:layout_width="@dimen/dp_580"
        android:layout_height="@dimen/dp_120"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_24"
        android:layout_gravity="center"
        app:hl_cornerRadius="@dimen/dp_60"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/divider_horizontal"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/know"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="@color/redeem_code_convert_success_duration_color"
            android:gravity="center"
            android:text="@string/storage_not_enough_confirm"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_48" />
    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
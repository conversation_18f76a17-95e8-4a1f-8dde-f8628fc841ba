<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="CoverPaperTabTextStyle" parent="Base.Widget.Design.TabLayout">
        <item name="android:textSize">@dimen/sp_42</item>
        <item name="android:textColor">@color/text_disable</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="popupWindowAnim" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/popupwindow_show</item>
        <item name="android:windowExitAnimation">@anim/popupwindow_hide</item>
    </style>

    <style name="fontPopupWindowAnim" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/font_popupwindow_show</item>
        <item name="android:windowExitAnimation">@anim/font_popupwindow_hide</item>
    </style>

    <style name="noteMaterialBottomSheetStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:backgroundDimAmount">0</item>
    </style>

    <style name="noteMaterialBottomSheetAnim">
        <item name="android:windowEnterAnimation">@anim/note_material_dialog_show</item>
        <item name="android:windowExitAnimation">@anim/note_material_dialog_hide</item>
    </style>

    <style name="HiddenSpacePasswordNumViewStyle">
        <item name="android:layout_width">@dimen/dp_15</item>
        <item name="android:layout_height">@dimen/dp_15</item>
        <item name="android:background">@drawable/phone_hidden_space_password_num_selector</item>
    </style>

    <style name="HiddenSpacePasswordNumViewStyleOneThirdVertical">
        <item name="android:layout_width">@dimen/dp_12</item>
        <item name="android:layout_height">@dimen/dp_12</item>
        <item name="android:background">@drawable/phone_hidden_space_password_num_selector</item>
    </style>

    <style name="PhoneHiddenSpacePasswordNumViewStyle">
        <item name="android:layout_width">@dimen/dp_40</item>
        <item name="android:layout_height">@dimen/dp_40</item>
        <item name="android:background">@drawable/phone_hidden_space_password_num_selector</item>
    </style>

    <style name="HiddenSpaceKeyboardNumViewStyle">
        <item name="android:layout_width">@dimen/dp_90</item>
        <item name="android:layout_height">@dimen/dp_90</item>
        <item name="android:textSize">@dimen/sp_36</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/phone_hidden_space_keyboard_num_bg</item>
    </style>

    <style name="HiddenSpaceKeyboardNumViewStyleOneThirdVertical">
        <item name="android:layout_width">@dimen/dp_73</item>
        <item name="android:layout_height">@dimen/dp_73</item>
        <item name="android:textSize">@dimen/sp_32</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/phone_hidden_space_keyboard_num_bg</item>
    </style>

    <style name="PhoneHiddenSpaceKeyboardNumViewStyle">
        <item name="android:layout_width">@dimen/dp_200</item>
        <item name="android:layout_height">@dimen/dp_200</item>
        <item name="android:textSize">@dimen/dp_80</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/phone_hidden_space_keyboard_num_bg</item>
    </style>

    <style name="PhoneHiddenSpaceCreateOrAddNoteDialogBtnStyle">
        <item name="android:layout_width">@dimen/dp_300</item>
        <item name="android:layout_height">@dimen/dp_300</item>
        <item name="android:textSize">@dimen/dp_60</item>
        <item name="android:textColor">#FF2E82FF</item>
        <item name="android:gravity">center</item>
        <item name="android:background">
            @drawable/phone_hidden_space_create_or_add_note_select_dialog_btn_background
        </item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">2</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="PhoneHiddenSpaceNoticeMessageTitleStyle">
        <item name="android:layout_width">@dimen/dp_0</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/sp_54</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginStart">@dimen/dp_48</item>
        <item name="android:layout_marginEnd">@dimen/dp_48</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="PhoneHiddenSpaceNoticeMessageSignificantContentStyle">
        <item name="android:layout_width">@dimen/dp_0</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginEnd">@dimen/dp_60</item>
        <item name="android:layout_marginStart">@dimen/dp_24</item>
        <item name="android:textSize">@dimen/sp_45</item>
        <item name="android:textColor">#FF8460FF</item>
    </style>

    <style name="PhoneHiddenSpaceNoticeMessageContentStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginEnd">@dimen/dp_60</item>
        <item name="android:layout_marginStart">@dimen/dp_60</item>
        <item name="android:textSize">@dimen/sp_45</item>
        <item name="android:textColor">#FF323438</item>
    </style>

    <style name="PhoneHiddenSpaceNoticeMessageContainerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingTop">@dimen/dp_60</item>
        <item name="android:paddingBottom">@dimen/dp_60</item>
        <item name="android:layout_marginStart">@dimen/dp_48</item>
        <item name="android:layout_marginEnd">@dimen/dp_48</item>
        <item name="android:background">
            @drawable/phone_hidden_space_notice_dialog_message_container_background
        </item>
    </style>


</resources>
<vector xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="54dp" android:viewportHeight="54" android:viewportWidth="54" android:width="54dp">
      
    <path android:fillColor="#4E6EFF" android:fillType="nonZero" android:pathData="M16.284,6L16.284,8L10.316,8L10.316,6L16.284,6ZM25.237,6L25.237,8L19.268,8L19.268,6L25.237,6ZM34.189,6L34.189,8L28.221,8L28.221,6L34.189,6ZM43.142,6L43.142,8L37.174,8L37.174,6L43.142,6ZM48,13.651L46,13.651L46,10.316C46,9.667 45.733,9.064 45.27,8.628L46.64,7.171C47.501,7.98 48,9.109 48,10.316L48,13.651ZM48,22.603L46,22.603L46,16.635L48,16.635L48,22.603ZM48,31.556L46,31.556L46,25.588L48,25.588L48,31.556ZM48,40.509L46,40.509L46,34.54L48,34.54L48,40.509ZM42.983,48L42.983,46L43.684,46C44.963,46 46,44.963 46,43.684L46,43.493L48,43.493L48,43.684C48,46.068 46.068,48 43.684,48L42.983,48ZM34.03,48L34.03,46L39.999,46L39.999,48L34.03,48ZM25.077,48L25.077,46L31.046,46L31.046,48L25.077,48ZM16.125,48L16.125,46L22.093,46L22.093,48L16.125,48ZM6.713,46.062L8.382,44.959C8.809,45.605 9.528,46 10.316,46L13.141,46L13.141,48L10.316,48C8.849,48 7.507,47.262 6.713,46.062ZM6,36.664L8,36.664L8,42.632L6,42.632L6,36.664ZM6,27.711L8,27.711L8,33.679L6,33.679L6,27.711ZM6,18.758L8,18.758L8,24.727L6,24.727L6,18.758ZM6.05,9.655L8.027,9.959C8.009,10.076 8,10.195 8,10.316L8,15.774L6,15.774L6,10.316C6,10.093 6.017,9.873 6.05,9.655ZM10.217,6.001L10.262,8.001C9.883,8.009 9.521,8.108 9.197,8.288L8.228,6.538C8.834,6.203 9.513,6.017 10.217,6.001Z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#87D7FF" android:fillType="evenOdd" android:pathData="M21.854,14.117L33.252,14.117A6.632,6.632 0,0 1,39.883 20.748L39.883,32.146A6.632,6.632 0,0 1,33.252 38.778L21.854,38.778A6.632,6.632 0,0 1,15.222 32.146L15.222,20.748A6.632,6.632 0,0 1,21.854 14.117z" android:strokeColor="#4E6EFF" android:strokeWidth="1.86513158"/>
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M20.921,33.079C21.436,30.285 22.663,28.888 24.603,28.888C27.514,28.888 28.897,29.881 31.075,28.067C33.253,26.253 31.978,23.132 36.395,23.132" android:strokeColor="#4E6EFF" android:strokeLineCap="round" android:strokeWidth="2.48684211"/>
      
    <path android:fillColor="#4E6EFF" android:fillType="evenOdd" android:pathData="M23.684,21.474m-2.763,0a2.763,2.763 0,1 1,5.526 0a2.763,2.763 0,1 1,-5.526 0" android:strokeColor="#00000000" android:strokeWidth="1"/>
    
</vector>

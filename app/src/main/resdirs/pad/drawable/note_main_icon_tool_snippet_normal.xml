<vector xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="40dp" android:viewportHeight="40" android:viewportWidth="40" android:width="40dp">
      
    <path android:fillAlpha="0" android:fillColor="#FF0000" android:fillType="evenOdd" android:pathData="M0,0h40v40h-40z" android:strokeAlpha="0" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M15,6.667L35,6.667L35,28.333M23.333,36.667L3.333,36.667L3.333,16.667" android:strokeColor="#313334" android:strokeWidth="2.5"/>
      
    <path android:fillColor="#313334" android:fillType="evenOdd" android:pathData="M3.333,5L10,5C10.92,5 11.667,5.746 11.667,6.667C11.667,7.587 10.92,8.333 10,8.333L5,8.333L5,8.333L5,13.333C5,14.254 4.254,15 3.333,15C2.413,15 1.667,14.254 1.667,13.333L1.667,6.667C1.667,5.746 2.413,5 3.333,5Z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#313334" android:fillType="evenOdd" android:pathData="M35,38.333L28.333,38.333C27.413,38.333 26.667,37.587 26.667,36.667C26.667,35.746 27.413,35 28.333,35L33.333,35L33.333,35L33.333,30C33.333,29.08 34.08,28.333 35,28.333C35.92,28.333 36.667,29.08 36.667,30L36.667,36.667C36.667,37.587 35.92,38.333 35,38.333Z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M9.167,17.5L29.167,17.5" android:strokeColor="#313334" android:strokeLineCap="round" android:strokeWidth="2.5"/>
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M9.167,25.833L22.5,25.833" android:strokeColor="#313334" android:strokeLineCap="round" android:strokeWidth="2.5"/>
    
</vector>

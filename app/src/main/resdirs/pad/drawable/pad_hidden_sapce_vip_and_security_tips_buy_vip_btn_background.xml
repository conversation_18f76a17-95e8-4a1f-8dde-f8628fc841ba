<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:endColor="@color/button_gradient_end"
                android:startColor="@color/button_gradient_start" />
            <corners android:radius="@dimen/dp_100" />
        </shape>
    </item>
    <item
        android:bottom="@dimen/dp_3"
        android:left="@dimen/dp_3"
        android:right="@dimen/dp_3"
        android:top="@dimen/dp_3">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/dp_100" />
            <solid android:color="#FFFFFFFF" />
        </shape>
    </item>
</layer-list>
<vector xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="54dp" android:viewportHeight="54" android:viewportWidth="54" android:width="54dp">
      
    <path android:fillColor="#4E6EFF" android:fillType="nonZero" android:pathData="M16.286,6L16.286,8L10.316,8L10.316,6L16.286,6ZM25.24,6L25.24,8L19.27,8L19.27,6L25.24,6ZM34.194,6L34.194,8L28.224,8L28.224,6L34.194,6ZM43.148,6L43.148,8L37.178,8L37.178,6L43.148,6ZM48,13.659L46,13.659L46,10.316C46,9.669 45.735,9.067 45.273,8.631L46.646,7.177C47.504,7.986 48,9.113 48,10.316L48,13.659<PERSON>M48,22.613L46,22.613L46,16.643L48,16.643L48,22.613ZM48,31.567L46,31.567L46,25.597L48,25.597L48,31.567ZM48,40.521L46,40.521L46,34.551L48,34.551L48,40.521ZM42.969,48L42.969,46L43.684,46C44.963,46 46,44.963 46,43.684L46,43.505L48,43.505L48,43.684C48,46.068 46.068,48 43.684,48L42.969,48ZM34.014,48L34.014,46L39.984,46L39.984,48L34.014,48ZM25.06,48L25.06,46L31.03,46L31.03,48L25.06,48ZM16.106,48L16.106,46L22.076,46L22.076,48L16.106,48ZM6.697,46.036L8.373,44.945C8.798,45.599 9.522,46 10.316,46L13.122,46L13.122,48L10.316,48C8.838,48 7.487,47.251 6.697,46.036ZM6,36.642L8,36.642L8,42.612L6,42.612L6,36.642ZM6,27.687L8,27.687L8,33.657L6,33.657L6,27.687ZM6,18.733L8,18.733L8,24.703L6,24.703L6,18.733ZM6.056,9.62L8.03,9.941C8.01,10.064 8,10.189 8,10.316L8,15.749L6,15.749L6,10.316C6,10.081 6.019,9.849 6.056,9.62ZM10.217,6.001L10.262,8.001C9.89,8.009 9.534,8.105 9.214,8.278L8.26,6.52C8.858,6.196 9.525,6.017 10.217,6.001Z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#87D7FF" android:fillType="evenOdd" android:pathData="M32.553,39.158C34.981,37.817 36.947,35.351 36.947,31.421C36.947,29.763 36.382,27.553 34.737,25.342C31.798,21.392 27.709,17.053 25.96,17.053C25.352,17.053 25.848,20.03 21.566,23.684C17.967,26.525 16.622,28.658 17.171,32.526C17.564,35.29 19.659,37.794 22.664,39.158" android:strokeColor="#4E6EFF" android:strokeWidth="1.86513158"/>
      
    <path android:fillColor="#4E6EFF" android:fillType="evenOdd" android:pathData="M14.877,22.959l-1.705,-0.933l1.705,-0.933l0.933,-2.584l0.933,2.584l1.705,0.933l-1.705,0.933l-0.933,2.584z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#4E6EFF" android:fillType="evenOdd" android:pathData="M35.16,20.083l-2.131,-1.166l2.131,-1.166l1.166,-3.23l1.166,3.23l2.131,1.166l-2.131,1.166l-1.166,3.23z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#4E6EFF" android:fillType="evenOdd" android:pathData="M17.053,39.296C17.053,39.906 17.547,40.401 18.158,40.401L36.814,40.401C37.436,40.401 37.95,39.917 37.987,39.296C38.021,38.721 37.583,38.227 37.007,38.193C36.987,38.191 36.966,38.191 36.945,38.191L18.158,38.191C17.547,38.191 17.053,38.686 17.053,39.296Z" android:strokeColor="#00000000" android:strokeWidth="1"/>
    
</vector>

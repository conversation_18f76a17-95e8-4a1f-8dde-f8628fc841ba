<vector xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="54dp" android:viewportHeight="54" android:viewportWidth="54" android:width="54dp">
      
    <path android:fillColor="#4E6EFF" android:fillType="nonZero" android:pathData="M16.284,6L16.284,8L10.316,8L10.316,6L16.284,6ZM25.237,6L25.237,8L19.268,8L19.268,6L25.237,6ZM34.189,6L34.189,8L28.221,8L28.221,6L34.189,6ZM43.142,6L43.142,8L37.174,8L37.174,6L43.142,6ZM48,13.651L46,13.651L46,10.316C46,9.667 45.733,9.064 45.27,8.628L46.64,7.171C47.501,7.98 48,9.109 48,10.316L48,13.651ZM48,22.603L46,22.603L46,16.635L48,16.635L48,22.603ZM48,31.556L46,31.556L46,25.588L48,25.588L48,31.556ZM48,40.509L46,40.509L46,34.54L48,34.54L48,40.509ZM42.983,48L42.983,46L43.684,46C44.963,46 46,44.963 46,43.684L46,43.493L48,43.493L48,43.684C48,46.068 46.068,48 43.684,48L42.983,48ZM34.03,48L34.03,46L39.999,46L39.999,48L34.03,48ZM25.077,48L25.077,46L31.046,46L31.046,48L25.077,48ZM16.125,48L16.125,46L22.093,46L22.093,48L16.125,48ZM6.713,46.062L8.382,44.959C8.809,45.605 9.528,46 10.316,46L13.141,46L13.141,48L10.316,48C8.849,48 7.507,47.262 6.713,46.062ZM6,36.664L8,36.664L8,42.632L6,42.632L6,36.664ZM6,27.711L8,27.711L8,33.679L6,33.679L6,27.711ZM6,18.758L8,18.758L8,24.727L6,24.727L6,18.758ZM6.05,9.655L8.027,9.959C8.009,10.076 8,10.195 8,10.316L8,15.774L6,15.774L6,10.316C6,10.093 6.017,9.873 6.05,9.655ZM10.217,6.001L10.262,8.001C9.883,8.009 9.521,8.108 9.197,8.288L8.228,6.538C8.834,6.203 9.513,6.017 10.217,6.001Z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M32.876,38.701C27.552,38.701 27.552,38.701 21.539,38.701L20.966,38.701L20.707,38.19C20.03,36.857 17.8,33.11 17.461,32.266C17.114,31.405 16.949,30.569 16.949,29.596C16.949,26.666 18.676,23.686 21.477,20.92C23.544,18.878 26.128,17.087 27.024,17.087C27.967,17.087 30.64,18.861 32.786,20.908C35.678,23.668 37.465,26.655 37.465,29.596C37.465,30.109 37.428,30.56 37.345,31.006C37.065,32.505 36.62,33.346 33.675,38.248L33.404,38.701L32.876,38.701Z" android:strokeColor="#1C599E" android:strokeWidth="1.86513158"/>
      
    <path android:fillColor="#87D7FF" android:fillType="evenOdd" android:pathData="M20.966,38.701L33.404,38.701L33.675,38.248C36.706,33.202 37.07,32.482 37.345,31.006C37.427,30.567 37.465,30.117 37.465,29.596C37.465,26.784 35.846,23.828 32.786,20.908C30.549,18.774 27.934,17.087 27.024,17.087C26.164,17.087 23.646,18.776 21.477,20.92C18.515,23.846 16.949,26.795 16.949,29.596C16.949,30.56 17.111,31.396 17.461,32.266C17.605,32.625 18.095,33.523 19.079,35.262C19.094,35.287 19.094,35.287 19.108,35.312C20.274,37.371 20.456,37.695 20.707,38.19L20.966,38.701Z" android:strokeColor="#4E6EFF" android:strokeWidth="1.86513158"/>
      
    <path android:fillColor="#4E6EFF" android:fillType="nonZero" android:pathData="M25.715,15.533L25.715,27.967C25.715,28.482 26.133,28.9 26.648,28.9C27.163,28.9 27.58,28.482 27.58,27.967L27.58,15.533C27.58,15.018 27.163,14.6 26.648,14.6C26.133,14.6 25.715,15.018 25.715,15.533Z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#4E6EFF" android:fillType="nonZero" android:pathData="M26.504,18.02C27.535,18.02 28.377,17.188 28.377,16.155C28.377,15.121 27.535,14.289 26.504,14.289C25.473,14.289 24.632,15.121 24.632,16.155C24.632,17.188 25.473,18.02 26.504,18.02Z" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#4E6EFF" android:fillType="evenOdd" android:pathData="M26.586,28.589m-1.865,0a1.865,1.865 0,1 1,3.73 0a1.865,1.865 0,1 1,-3.73 0" android:strokeColor="#00000000" android:strokeWidth="1"/>
      
    <path android:fillColor="#4E6EFF" android:fillType="evenOdd" android:pathData="M19.125,37.914L35.289,37.914A1.243,1.243 0,0 1,36.533 39.158L36.533,39.158A1.243,1.243 0,0 1,35.289 40.401L19.125,40.401A1.243,1.243 0,0 1,17.882 39.158L17.882,39.158A1.243,1.243 0,0 1,19.125 37.914z" android:strokeColor="#00000000" android:strokeWidth="1"/>
    
</vector>

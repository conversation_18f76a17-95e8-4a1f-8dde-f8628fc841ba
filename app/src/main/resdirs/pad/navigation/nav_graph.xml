<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/note_list">
    <fragment
        android:id="@+id/note_list"
        android:name="com.topstack.kilonotes.pad.note.NoteListFragment"
        android:label="NoteList"
        tools:layout="@layout/note_list_fragment">
        <action
            android:id="@+id/edit"
            app:destination="@id/note_editor">
            <argument
                android:name="isShowGraphToolWindow"
                android:defaultValue="true"
                app:argType="boolean" />
            <argument
                android:name="isShowDraftPaperMorePopupGuide"
                android:defaultValue="true"
                app:argType="boolean" />
        </action>
        <action
            android:id="@+id/create"
            app:destination="@+id/note_create">
            <argument
                android:name="editMode"
                android:defaultValue="false"
                app:argType="boolean" />
        </action>

        <action
            android:id="@+id/edit_cover"
            app:destination="@+id/note_create">
            <argument
                android:name="editMode"
                android:defaultValue="true"
                app:argType="boolean" />
        </action>
        <action
            android:id="@+id/action_note_list_to_vip_store"
            app:destination="@id/vip_store">
            <argument
                android:name="source"
                android:defaultValue="H_ICON"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/action_buy_membership_store"
            app:destination="@id/vip_store_new_edition">
            <argument
                android:name="source"
                android:defaultValue="H_WINDOW"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/action_note_list_to_templateListFragment"
            app:destination="@id/template_list" />
        <action
            android:id="@+id/action_note_list_to_hand_write_snippet_edit"
            app:destination="@id/hand_write_snippet_edit" />
        <action
            android:id="@+id/action_note_list_to_create_snippet"
            app:destination="@id/create_snippet_fragment">
            <argument
                android:name="isEditMode"
                android:defaultValue="false"
                app:argType="boolean" />
        </action>

    </fragment>

    <fragment
        android:id="@+id/font_list"
        android:name="com.topstack.kilonotes.pad.select.FontListWindow"
        tools:layout="@layout/phone_dialog_font_list" />

    <fragment
        android:id="@+id/note_editor"
        android:name="com.topstack.kilonotes.pad.note.NoteEditorFragment"
        android:label="NoteEditor"
        tools:layout="@layout/fragment_note_editor">

        <action
            android:id="@+id/action_note_editor_to_page_sizing"
            app:destination="@+id/page_resizing_fragment" />

        <action
            android:id="@+id/action_note_list_to_font_list"
            app:destination="@id/font_list" />

        <action
            android:id="@+id/list_notes"
            app:destination="@+id/note_list" />

        <action
            android:id="@+id/show_thumbnail"
            app:destination="@+id/page_thumbnail_multi_window" />

        <action
            android:id="@+id/action_note_editor_to_vip_store"
            app:destination="@id/vip_store_new_edition"></action>

        <action
            android:id="@+id/buy_template"
            app:destination="@id/handbook_detail">
            <argument
                android:name="noteId"
                app:argType="long" />
            <argument
                android:name="source"
                android:defaultValue="EDIT_TEMPLATE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

        <action
            android:id="@+id/action_note_editor_to_decoupage_fragment"
            app:destination="@id/decoupage_fragment" />

        <action
            android:id="@+id/action_note_editor_to_instant_alpha_fragment"
            app:destination="@+id/instant_alpha_fragment" />

    </fragment>

    <fragment
        android:id="@+id/note_create"
        android:name="com.topstack.kilonotes.pad.note.CreateNoteFragment"
        android:label="NoteNew"
        tools:layout="@layout/fragment_create_notebook">
        <argument
            android:name="editMode"
            android:defaultValue="false"
            app:argType="boolean" />

        <action
            android:id="@+id/edit"
            app:destination="@+id/note_editor" />

        <action
            android:id="@+id/action_note_create_to_vip_store"
            app:destination="@id/vip_store_new_edition">
            <argument
                android:name="source"
                android:defaultValue="CREATE_NOTE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

        <action
            android:id="@+id/buy_template"
            app:destination="@id/handbook_detail">
            <argument
                android:name="noteId"
                app:argType="long" />
        </action>
    </fragment>

    <fragment
        android:id="@+id/page_thumbnail_multi_window"
        android:name="com.topstack.kilonotes.pad.note.PageThumbnailFragment"
        android:label="PageThumbnailMultiWindow"
        tools:layout="@layout/fragment_page_thumbnail" />

    <activity
        android:id="@+id/about"
        android:name="com.topstack.kilonotes.pad.about.AboutActivity"
        android:label="About"
        tools:layout="@layout/about_activity" />

    <fragment
        android:id="@+id/vip_store"
        android:name="com.topstack.kilonotes.pad.vip.VipStoreFragment"
        android:label="VipStore"
        tools:layout="@layout/fragment_vip_store">
        <argument
            android:name="source"
            android:defaultValue="STORE"
            app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        <action
            android:id="@+id/action_vip_store_to_vip_store_new"
            app:destination="@+id/vip_store_new_edition" />
        <action
            android:id="@+id/show_detail"
            app:destination="@id/handbook_detail">
            <argument
                android:name="noteId"
                app:argType="long" />
            <argument
                android:name="source"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
        <action
            android:id="@+id/action_vip_store_to_note_list"
            app:destination="@id/note_list"
            app:popUpTo="@id/nav_graph"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/handbook_detail"
        android:name="com.topstack.kilonotes.pad.vip.HandbookDetailFragment"
        android:label="HandbookDetail"
        tools:layout="@layout/fragment_handbook_detail">
        <argument
            android:name="noteId"
            app:argType="long" />
        <argument
            android:name="source"
            android:defaultValue="STORE"
            app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        <action
            android:id="@+id/action_handbook_detail_to_note_list"
            app:destination="@id/note_list" />
        <action
            android:id="@+id/action_handbook_detail_to_vip_store_new"
            app:destination="@+id/vip_store_new_edition" />
        <action
            android:id="@+id/action_handbook_detail_to_vip_store"
            app:destination="@id/vip_store">
            <argument
                android:name="source"
                android:defaultValue="STORE"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>
    </fragment>

    <fragment
        android:id="@+id/template_list"
        android:name="com.topstack.kilonotes.pad.templatetool.TemplateListFragment"
        android:label="TemplateSelectFragment"
        tools:layout="@layout/fragment_template_list">
        <action
            android:id="@+id/action_templateListFragment_to_templateShowFragment"
            app:destination="@id/template_show" />
    </fragment>
    <fragment
        android:id="@+id/template_show"
        android:name="com.topstack.kilonotes.pad.templatetool.TemplateShowFragment"
        android:label="TemplateShowFragment"
        tools:layout="@layout/fragment_show_template">
        <action
            android:id="@+id/action_templateShowFragment_to_templateListFragment"
            app:destination="@id/template_list" />
    </fragment>

    <fragment
        android:id="@+id/decoupage_fragment"
        android:name="com.topstack.kilonotes.pad.note.DecoupageFragment"
        android:label="DecoupageFragment" />

    <fragment
        android:id="@+id/instant_alpha_fragment"
        android:name="com.topstack.kilonotes.pad.note.InstantAlphaFragment"
        android:label="InstantAlphaFragment"
        tools:layout="@layout/fragment_instant_alpha">

        <argument
            android:name="source"
            android:defaultValue="banner"
            app:argType="string" />

        <argument
            android:name="image_uri"
            android:defaultValue="@null"
            app:argType="android.net.Uri"
            app:nullable="true" />

        <argument
            android:name="alpha"
            android:defaultValue="255"
            app:argType="integer" />

        <action
            android:id="@+id/action_instant_alpha_to_vip_store"
            app:destination="@id/vip_store">
            <argument
                android:name="source"
                android:defaultValue="INSTANT_ALPHA"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

    </fragment>

    <action
        android:id="@+id/action_vip_exclusive_dialog_to_vip_store"
        app:destination="@id/vip_store_new_edition">
        <argument
            android:name="source"
            android:defaultValue="INSTANT_ALPHA"
            app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
    </action>

    <fragment
        android:id="@+id/hand_write_snippet_edit"
        android:name="com.topstack.kilonotes.pad.snippet.HandWriteSnippetEditFragment"
        android:label="HandWriteSnippetEdit"
        tools:layout="@layout/fragment_hand_write_snippet_edit">

        <action
            android:id="@+id/action_hand_write_snippet_edit_to_vip_store"
            app:destination="@id/vip_store_new_edition">
            <argument
                android:name="source"
                android:defaultValue="EDIT_MATERIAL"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

    </fragment>

    <fragment
        android:id="@+id/create_snippet_fragment"
        android:name="com.topstack.kilonotes.pad.note.NoteSnippetCreateFragment"
        android:label="CreateSnippet"
        tools:layout="@layout/fragment_note_snippet_create">
        <argument
            android:name="isEditMode"
            android:defaultValue="false"
            app:argType="boolean" />
        <action
            android:id="@+id/action_create_snippet_to_hand_write_snippet_edit"
            app:destination="@id/hand_write_snippet_edit" />
    </fragment>

    <fragment
        android:id="@+id/page_resizing_fragment"
        android:name="com.topstack.kilonotes.base.pageresizing.BasePageResizingFragment"
        android:label="PageResizing"
        tools:layout="@layout/fragment_page_resizing" />

    <fragment
        android:id="@+id/backup_space_fragment"
        android:name="com.topstack.kilonotes.pad.backup.fragment.PadBackupSpaceFragment"
        android:label="BackupSpace"
        tools:layout="@layout/fragment_backup_space" />

    <fragment
        android:id="@+id/vip_store_new_edition"
        android:name="com.topstack.kilonotes.pad.vip.PadUserVipStoreFragment"
        android:label="VipStoreNew"
        tools:layout="@layout/pad_vip_store_new_edition">

        <argument
            android:name="source"
            android:defaultValue="STORE"
            app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />

    </fragment>

    <dialog
        android:id="@+id/unified_integral_dialog"
        android:name="com.topstack.kilonotes.base.ai.dialog.UnifiedIntegralDialog"
        android:label="UnifiedIntegralDialog"
        tools:layout="@layout/dialog_ai_integral_unified">

        <action
            android:id="@+id/action_unified_integral_dialog_to_vip_store_new"
            app:destination="@+id/vip_store_new_edition">
            <argument
                android:name="source"
                android:defaultValue="AI_FUEL_PACK"
                app:argType="com.topstack.kilonotes.base.component.fragment.NaviEnum" />
        </action>

    </dialog>

</navigation>
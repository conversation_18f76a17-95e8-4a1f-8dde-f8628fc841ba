<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_360"
    android:layout_height="wrap_content"
    android:layout_gravity="center">

    <com.topstack.kilonotes.base.shadow.FixShadowLayout
        android:id="@+id/password_keyboard"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_507"
        app:hl_cornerRadius="@dimen/dp_15"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_27"
                android:text="@string/hidden_space_input_password_title"
                android:textColor="@color/text_secondary"
                android:textSize="@dimen/sp_20"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/password_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_115"
                android:layout_marginTop="@dimen/dp_20"
                app:layout_constraintTop_toBottomOf="@id/title">

                <View
                    android:id="@+id/password_one"
                    style="@style/HiddenSpacePasswordNumViewStyleOneThirdVertical"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/password_two" />

                <View
                    android:id="@+id/password_two"
                    style="@style/HiddenSpacePasswordNumViewStyleOneThirdVertical"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/password_one"
                    app:layout_constraintEnd_toStartOf="@id/password_three" />

                <View
                    android:id="@+id/password_three"
                    style="@style/HiddenSpacePasswordNumViewStyleOneThirdVertical"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/password_two"
                    app:layout_constraintEnd_toStartOf="@id/password_four" />

                <View
                    android:id="@+id/password_four"
                    style="@style/HiddenSpacePasswordNumViewStyleOneThirdVertical"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/password_three"
                    app:layout_constraintEnd_toEndOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.Group
                android:id="@+id/keyboard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="keyboard_zero,keyboard_one,
                keyboard_two,keyboard_three,keyboard_four,keyboard_five,
                keyboard_six,keyboard_seven,keyboard_eight,keyboard_nine" />

            <TextView
                android:id="@+id/keyboard_one"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:layout_marginTop="@dimen/dp_64"
                app:layout_constraintVertical_chainStyle="spread_inside"
                android:text="1"
                app:layout_constraintTop_toBottomOf="@id/title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/keyboard_two"
                app:layout_constraintBottom_toTopOf="@id/keyboard_four" />

            <TextView
                android:id="@+id/keyboard_two"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:layout_marginTop="@dimen/dp_64"
                app:layout_constraintVertical_chainStyle="spread_inside"
                android:text="2"
                app:layout_constraintTop_toBottomOf="@id/title"
                app:layout_constraintStart_toEndOf="@id/keyboard_one"
                app:layout_constraintEnd_toStartOf="@id/keyboard_three"
                app:layout_constraintBottom_toTopOf="@id/keyboard_five" />

            <TextView
                android:id="@+id/keyboard_three"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:layout_marginTop="@dimen/dp_64"
                app:layout_constraintVertical_chainStyle="spread_inside"
                android:text="3"
                app:layout_constraintTop_toBottomOf="@id/title"
                app:layout_constraintStart_toEndOf="@id/keyboard_two"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/keyboard_six" />

            <TextView
                android:id="@+id/keyboard_four"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:text="4"
                app:layout_constraintTop_toBottomOf="@id/keyboard_one"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/keyboard_five"
                app:layout_constraintBottom_toTopOf="@id/keyboard_seven" />

            <TextView
                android:id="@+id/keyboard_five"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:text="5"
                app:layout_constraintStart_toEndOf="@id/keyboard_four"
                app:layout_constraintTop_toBottomOf="@id/keyboard_two"
                app:layout_constraintEnd_toStartOf="@id/keyboard_six"
                app:layout_constraintBottom_toTopOf="@id/keyboard_eight" />

            <TextView
                android:id="@+id/keyboard_six"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:text="6"
                app:layout_constraintStart_toEndOf="@id/keyboard_five"
                app:layout_constraintTop_toBottomOf="@id/keyboard_three"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/keyboard_nine" />

            <TextView
                android:id="@+id/keyboard_seven"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:text="7"
                android:layout_marginBottom="@dimen/dp_123"
                app:layout_constraintTop_toBottomOf="@id/keyboard_four"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/keyboard_eight"
                app:layout_constraintBottom_toBottomOf="parent" />

            <TextView
                android:id="@+id/keyboard_eight"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:text="8"
                app:layout_constraintStart_toEndOf="@id/keyboard_seven"
                app:layout_constraintTop_toBottomOf="@id/keyboard_five"
                app:layout_constraintEnd_toStartOf="@id/keyboard_nine"
                app:layout_constraintBottom_toTopOf="@id/keyboard_zero" />

            <TextView
                android:id="@+id/keyboard_nine"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:text="9"
                android:layout_marginBottom="@dimen/dp_123"
                app:layout_constraintStart_toEndOf="@id/keyboard_eight"
                app:layout_constraintTop_toBottomOf="@id/keyboard_six"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

            <TextView
                android:id="@+id/keyboard_zero"
                style="@style/HiddenSpaceKeyboardNumViewStyleOneThirdVertical"
                android:layout_marginBottom="@dimen/dp_25"
                android:text="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/keyboard_eight"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />


            <ImageView
                android:id="@+id/delete_password"
                android:layout_width="@dimen/dp_73"
                android:layout_height="@dimen/dp_73"
                android:padding="@dimen/dp_17"
                android:src="@drawable/phone_hidden_space_delete_icon"
                app:layout_constraintStart_toEndOf="@id/keyboard_zero"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/keyboard_nine"
                app:layout_constraintBottom_toBottomOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.topstack.kilonotes.base.shadow.FixShadowLayout>

    <TextView
        android:id="@+id/forget_password"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:textSize="@dimen/sp_24"
        android:textColor="@color/white"
        android:visibility="gone"
        android:text="@string/hidden_space_forget_password"
        app:layout_constraintTop_toBottomOf="@id/password_keyboard"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

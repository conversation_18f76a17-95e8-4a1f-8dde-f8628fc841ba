<?xml version="1.0" encoding="utf-8"?>
<com.lihang.ShadowLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:hl_cornerRadius="@dimen/dp_0">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/close"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_24"
            android:src="@drawable/dialog_icon_image_crop_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/restore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/imagecrop_restore"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/sp_32"
            android:textStyle="bold"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/complete"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/complete" />

        <ImageView
            android:id="@+id/complete"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_18"
            android:layout_marginEnd="@dimen/dp_24"
            android:src="@drawable/dialog_icon_image_crop_complete_selector"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/mode_switch"
            android:layout_width="@dimen/dp_360"
            android:layout_height="@dimen/dp_45"
            android:layout_marginTop="@dimen/dp_40"
            android:background="@drawable/pad_note_image_crop_button_group_shape"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/restore">

            <TextView
                android:id="@+id/regular_mode"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/dialog_image_crop_button_stroke"
                android:gravity="center"
                android:text="@string/imagecrop_regular_crop"
                android:textColor="@color/select_photo_button_text"
                android:textSize="@dimen/sp_22" />

            <TextView
                android:id="@+id/irregular_mode"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/dialog_image_crop_button_stroke"
                android:gravity="center"
                android:text="@string/imagecrop_irregular_crop"
                android:textColor="@color/select_photo_button_text"
                android:textSize="@dimen/sp_22" />
        </LinearLayout>

        <com.topstack.kilonotes.base.imagecrop.ImageCropView
            android:id="@+id/crop_view"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_620"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_24"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mode_switch" />

        <com.topstack.kilonotes.base.imagemagnifier.ImageMagnifierView
            android:id="@+id/magnifier"
            android:layout_width="@dimen/dp_240"
            android:layout_height="@dimen/dp_240"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mode_switch" />

        <TextView
            android:id="@+id/alpha_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/image_alpha_min"
            android:textColor="@color/note_tool_text_alpha_progress_value_color"
            android:textSize="@dimen/sp_24"
            app:layout_constraintBottom_toTopOf="@id/keep_image_container"
            app:layout_constraintStart_toStartOf="@id/crop_view"
            app:layout_constraintTop_toBottomOf="@id/crop_view" />

        <TextView
            android:id="@+id/alpha_end"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/image_alpha_max"
            android:textColor="@color/note_tool_text_alpha_progress_value_color"
            android:textSize="@dimen/sp_24"
            app:layout_constraintBottom_toBottomOf="@id/alpha_start"
            app:layout_constraintEnd_toStartOf="@id/alpha"
            app:layout_constraintTop_toTopOf="@id/alpha_start" />

        <TextView
            android:id="@+id/alpha"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_20"
            android:ems="3"
            android:gravity="end"
            android:textColor="#2E82FF"
            android:textSize="@dimen/sp_36"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/keep_image_container"
            app:layout_constraintEnd_toEndOf="@id/crop_view"
            app:layout_constraintTop_toBottomOf="@id/crop_view"
            tools:text="100%" />

        <SeekBar
            android:id="@+id/alpha_seek_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_24"
            android:maxHeight="@dimen/dp_10"
            android:progress="100"
            android:progressDrawable="@drawable/phone_image_tools_alpha_seekbar_progress_background"
            android:thumb="@drawable/pen_and_text_size_tracker_background"
            app:layout_constraintBottom_toBottomOf="@id/alpha_start"
            app:layout_constraintEnd_toStartOf="@id/alpha_end"
            app:layout_constraintStart_toEndOf="@id/alpha_start"
            app:layout_constraintTop_toTopOf="@id/alpha_start"
            tools:thumb="@drawable/pen_and_text_size_tracker_background" />

        <FrameLayout
            android:id="@+id/keep_image_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginBottom="@dimen/dp_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:id="@+id/keep_image"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:background="@drawable/dialog_image_crop_save_original_image_bg"
                android:padding="@dimen/dp_8"
                android:src="@drawable/dialog_image_crop_save_original_image_src" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_44"
                android:gravity="center"
                android:text="@string/imagecrop_keep_original_image"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/sp_20" />
        </FrameLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/loading"
        style="?android:attr/progressBarStyle"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:layout_gravity="center"
        android:progressDrawable="@color/black" />

</com.lihang.ShadowLayout>
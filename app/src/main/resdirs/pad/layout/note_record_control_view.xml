<?xml version="1.0" encoding="utf-8"?>
<eightbitlab.com.blurview.BlurView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/blur_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    app:blurOverlayColor="@color/note_record_control_view_background_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_54"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/play"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_40"
            android:src="@drawable/note_record_control_view_play_and_pause_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/speed"
            android:layout_width="@dimen/dp_63"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_30"
            android:gravity="center"
            android:src="@drawable/playback_speed_1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/play"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_30"
            android:src="@drawable/note_record_control_view_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/speed"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/forward"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_30"
            android:src="@drawable/note_record_control_view_forward"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/current_time"
            android:layout_width="@dimen/dp_100"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_80"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/note_record_control_view_time_text_color"
            android:textSize="@dimen/sp_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/forward"
            app:layout_constraintTop_toTopOf="parent" />


        <com.topstack.kilonotes.pad.component.NoteRecordProgressView
            android:id="@+id/progress"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:background="@null"
            android:maxHeight="@dimen/dp_3"
            android:minHeight="@dimen/dp_3"
            android:progressDrawable="@drawable/note_record_progress_view_progress_drawable"
            android:saveEnabled="false"
            android:splitTrack="false"
            android:thumb="@drawable/note_record_progress_view_thumb"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/total_time"
            app:layout_constraintStart_toEndOf="@id/current_time"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/total_time"
            android:layout_width="@dimen/dp_100"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_80"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/note_record_control_view_time_text_color"
            android:textSize="@dimen/sp_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/add_tag"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/add_tag"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginEnd="@dimen/dp_30"
            android:src="@drawable/note_record_control_view_add_tag"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/show_list"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/show_list"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginEnd="@dimen/dp_40"
            android:src="@drawable/note_record_control_view_show_list"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/split_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/note_tool_split_line_color"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</eightbitlab.com.blurview.BlurView>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/decoupage_tool_background">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_50"
        android:text="@string/decoupage_tool_title"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_32"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/back"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginStart="@dimen/dp_36"
        android:padding="@dimen/dp_12"
        android:src="@drawable/decoupage_preview_back"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title" />

    <ImageView
        android:id="@+id/close"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginStart="@dimen/dp_36"
        android:padding="@dimen/dp_12"
        android:src="@drawable/decoupage_close"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title" />

    <ImageView
        android:id="@+id/save"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginEnd="@dimen/dp_36"
        android:padding="@dimen/dp_12"
        android:src="@drawable/decoupage_preview_save"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title">

        <TextView
            android:id="@+id/previos_step"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_90"
            android:background="@drawable/decoupage_previous_step_background"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_30"
            android:paddingVertical="@dimen/dp_6"
            android:text="@string/previous_step"
            android:textColor="@color/decoupage_previous_step"
            android:textSize="@dimen/sp_20"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/step2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/step2" />

        <TextView
            android:id="@+id/step1"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_50"
            android:background="@drawable/decoupage_step_background_selector"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/decoupage_step_selected"
            android:textSize="@dimen/sp_22"
            app:layout_constraintEnd_toStartOf="@+id/dotted_line_between_1_and_2"
            app:layout_constraintTop_toTopOf="@+id/step2" />

        <TextView
            android:id="@+id/step1_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:text="@string/decoupage_select_paper"
            android:textColor="@color/decoupage_step_selected"
            android:textSize="@dimen/sp_22"
            app:layout_constraintEnd_toEndOf="@+id/step1"
            app:layout_constraintStart_toStartOf="@+id/step1"
            app:layout_constraintTop_toBottomOf="@+id/step1" />

        <View
            android:id="@+id/dotted_line_between_1_and_2"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_10"
            android:layout_marginHorizontal="@dimen/dp_50"
            android:background="@drawable/decoupage_step_dotted_line_selector"
            app:layout_constraintBottom_toBottomOf="@+id/step2"
            app:layout_constraintEnd_toStartOf="@id/step2"
            app:layout_constraintTop_toTopOf="@+id/step2" />

        <TextView
            android:id="@+id/step2"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_42"
            android:background="@drawable/decoupage_step_background_selector"
            android:gravity="center"
            android:paddingBottom="@dimen/dp_6"
            android:text="2"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_22"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/step2_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:text="@string/decoupage_select_fold_type"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_22"
            app:layout_constraintEnd_toEndOf="@+id/step2"
            app:layout_constraintStart_toStartOf="@+id/step2"
            app:layout_constraintTop_toBottomOf="@+id/step2" />

        <View
            android:id="@+id/dotted_line_between_2_and_3"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_10"
            android:layout_marginHorizontal="@dimen/dp_50"
            android:background="@drawable/decoupage_step_dotted_line_selector"
            app:layout_constraintBottom_toBottomOf="@+id/step2"
            app:layout_constraintStart_toEndOf="@id/step2"
            app:layout_constraintTop_toTopOf="@+id/step2" />

        <TextView
            android:id="@+id/step3"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_marginStart="@dimen/dp_50"
            android:background="@drawable/decoupage_step_background_selector"
            android:gravity="center"
            android:text="3"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_22"
            app:layout_constraintStart_toEndOf="@id/dotted_line_between_2_and_3"
            app:layout_constraintTop_toTopOf="@id/step2" />

        <TextView
            android:id="@+id/step3_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:text="@string/decoupage_cut"
            android:textColor="@color/decoupage_step_unselected"
            android:textSize="@dimen/sp_22"
            app:layout_constraintEnd_toEndOf="@+id/step3"
            app:layout_constraintStart_toStartOf="@+id/step3"
            app:layout_constraintTop_toBottomOf="@+id/step3" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/next_step_background"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_90"
            android:background="@drawable/decoupage_next_step_background"
            app:layout_constraintBottom_toBottomOf="@+id/step2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/step2"
            app:layout_constraintWidth_min="@dimen/dp_120">

            <TextView
                android:id="@+id/next_step"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"
                android:paddingHorizontal="@dimen/dp_8"
                android:text="@string/next_step"
                android:textColor="@color/white"
                android:textSize="@dimen/dp_20"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/complete"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_36"
                android:paddingVertical="@dimen/dp_6"
                android:src="@drawable/decoupage_complete"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/next_step"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/next_step" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.topstack.kilonotes.base.materialtool.decoupage.DecoupageView
        android:id="@+id/decoupage_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_50"
        android:layout_marginBottom="@dimen/dp_83"
        app:layout_constraintBottom_toTopOf="@id/select_paper_tool_bar"
        app:layout_constraintTop_toBottomOf="@id/top_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/select_paper_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_144"
        android:background="@color/select_fold_type_recycle_view_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/decoupage_view">

        <com.lihang.ShadowLayout
            android:id="@+id/select_image_shadow"
            android:layout_width="@dimen/dp_96"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:layout_marginVertical="@dimen/dp_24"
            app:hl_cornerRadius="@dimen/dp_18"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/select_paper_recycle_view"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:id="@+id/select_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/select_fold_type_recycle_view_color"
                android:src="@drawable/decoupage_image_icon" />

            <ImageView
                android:id="@+id/select_image_is_selected"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/decoupage_image_is_selected_background"
                android:visibility="invisible" />
        </com.lihang.ShadowLayout>

        <ImageView
            android:id="@+id/delete_image"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_marginEnd="@dimen/dp_6"
            android:src="@drawable/decoupage_delete_image_icon"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="@id/select_image_shadow"
            app:layout_constraintTop_toTopOf="@+id/select_image_shadow" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/select_paper_recycle_view"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/select_image_shadow"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/select_fold_type_recycle_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/select_fold_type_recycle_view_color"
        android:orientation="horizontal"
        android:paddingTop="@dimen/dp_26"
        android:paddingBottom="@dimen/dp_14"
        android:visibility="invisible"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="@+id/select_paper_tool_bar"
        app:layout_constraintTop_toTopOf="@+id/select_paper_tool_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/select_crop_size_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/select_fold_type_recycle_view_color"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/select_paper_tool_bar"
        app:layout_constraintTop_toTopOf="@+id/select_paper_tool_bar">

        <ImageView
            android:id="@+id/undo"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_90"
            android:src="@drawable/decoupage_cut_undo_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/redo"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_60"
            android:src="@drawable/decoupage_cut_redo_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/undo"
            app:layout_constraintTop_toTopOf="parent" />

        <com.topstack.kilonotes.pad.component.CircleSizeSelectorItemView
            android:id="@+id/crop_size_small"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginEnd="@dimen/dp_120"
            android:padding="@dimen/dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/crop_size_medium"
            app:layout_constraintTop_toTopOf="parent"
            app:radius="@dimen/dp_7" />

        <com.topstack.kilonotes.pad.component.CircleSizeSelectorItemView
            android:id="@+id/crop_size_medium"
            android:layout_width="27dp"
            android:layout_height="26dp"
            android:padding="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.topstack.kilonotes.pad.component.CircleSizeSelectorItemView
            android:id="@+id/crop_size_large"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_120"
            android:padding="@dimen/dp_6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/crop_size_medium"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
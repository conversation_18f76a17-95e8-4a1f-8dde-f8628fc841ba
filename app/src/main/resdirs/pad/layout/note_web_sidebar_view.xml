<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:clickable="true"
    android:focusable="true"
    android:paddingTop="@dimen/dp_5">

    <com.topstack.kilonotes.pad.component.NoteSidebarCommonBorderView
        android:id="@+id/web_border"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        app:button_height="@dimen/dp_64"
        app:button_radius="@dimen/dp_17"
        app:button_width="@dimen/dp_29"
        app:icon_color="@color/note_material_hide_icon_color"
        app:icon_height="@dimen/dp_20"
        app:icon_stroke_width="@dimen/dp_2"
        app:icon_width="@dimen/dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:shadow_color="@color/note_material_shadow_color"
        app:shadow_radius="@dimen/dp_14" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/web_content"
        android:layout_width="@dimen/dp_0"
        android:layout_height="match_parent"
        android:background="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/web_border">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            app:layout_constraintBottom_toTopOf="@id/top_divider"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/web_search_edit_text"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_50"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_32"
            android:background="@drawable/note_web_search_edit_text_background"
            android:hint="@string/note_search_edit_hint"
            android:imeOptions="actionGo"
            android:importantForAutofill="no"
            android:inputType="text"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_49"
            android:paddingEnd="@dimen/dp_52"
            android:textColor="@color/note_web_search_edit_text"
            android:textColorHint="@color/note_web_search_edit_hint"
            android:textSize="@dimen/sp_20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/web_search_image_view"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_13"
            android:src="@drawable/note_web_search_icon"
            app:layout_constraintBottom_toBottomOf="@id/web_search_edit_text"
            app:layout_constraintStart_toStartOf="@id/web_search_edit_text"
            app:layout_constraintTop_toTopOf="@id/web_search_edit_text"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/web_clear_image_view"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:padding="@dimen/dp_10"
            android:src="@drawable/doodle_text_element_icon_clear"
            app:layout_constraintBottom_toBottomOf="@id/web_search_edit_text"
            app:layout_constraintEnd_toEndOf="@id/web_search_edit_text"
            app:layout_constraintTop_toTopOf="@id/web_search_edit_text"
            tools:ignore="ContentDescription" />

        <View
            android:id="@+id/top_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@color/note_web_search_divide_line"
            app:layout_constraintTop_toBottomOf="@id/web_search_edit_text" />

        <View
            android:id="@+id/bottom_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/note_web_search_divide_line"
            app:layout_constraintBottom_toTopOf="@+id/bottom_background" />

        <eightbitlab.com.blurview.BlurView
            android:id="@+id/bottom_background"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_82"
            android:clickable="true"
            android:focusable="true"
            app:blurOverlayColor="@color/note_web_bottom_overlay"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/back"
                    android:layout_width="@dimen/dp_40"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginStart="@dimen/dp_30"
                    android:src="@drawable/note_web_back_selector"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription" />

                <ImageView
                    android:id="@+id/forward"
                    android:layout_width="@dimen/dp_40"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginStart="@dimen/dp_30"
                    android:src="@drawable/note_web_forward_selector"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/back"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription" />

                <ImageView
                    android:id="@+id/refresh"
                    android:layout_width="@dimen/dp_40"
                    android:layout_height="@dimen/dp_40"
                    android:layout_marginStart="@dimen/dp_30"
                    android:src="@drawable/note_web_refresh_icon"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/forward"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </eightbitlab.com.blurview.BlurView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
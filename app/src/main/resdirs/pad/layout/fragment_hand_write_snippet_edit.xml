<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/pad_edit_hand_write_snippet_background">

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/blur_view" />

    <com.lihang.ShadowLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="0dp"
        app:hl_shadowColor="@color/hand_write_snippet_edit_doodle_shadow_color"
        app:hl_shadowLimit="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="@id/space_bottom"
        app:layout_constraintEnd_toEndOf="@id/space_end"
        app:layout_constraintStart_toStartOf="@id/space_start"
        app:layout_constraintTop_toTopOf="@id/space_top">
        <!--    填充一个不绘制的view，否则ShadowLayout不会生效    -->
        <Space
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.lihang.ShadowLayout>

    <Space
        android:id="@+id/space_start"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="@id/doodle"
        app:layout_constraintEnd_toStartOf="@id/doodle"
        app:layout_constraintTop_toTopOf="@id/doodle" />

    <Space
        android:id="@+id/space_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dp_20"
        app:layout_constraintBottom_toTopOf="@id/doodle"
        app:layout_constraintEnd_toEndOf="@id/doodle"
        app:layout_constraintStart_toStartOf="@id/doodle" />

    <Space
        android:id="@+id/space_end"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="@id/doodle"
        app:layout_constraintStart_toEndOf="@id/doodle"
        app:layout_constraintTop_toTopOf="@id/doodle" />

    <Space
        android:id="@+id/space_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_20"
        app:layout_constraintEnd_toEndOf="@id/doodle"
        app:layout_constraintStart_toStartOf="@id/doodle"
        app:layout_constraintTop_toBottomOf="@id/doodle" />

    <com.topstack.kilonotes.base.doodle.views.doodleview.DoodleView
        android:id="@+id/doodle"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/dp_100"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1400:840"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_max="@dimen/dp_840"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/blur_view"
        app:layout_constraintWidth_max="@dimen/dp_1400" />

    <!--  major tool area -->
    <eightbitlab.com.blurview.BlurView
        android:id="@+id/blur_view"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/majorToolContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:id="@+id/mask_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white_60" />

            <HorizontalScrollView
                android:id="@+id/main_tool_scroller"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_25"
                android:layout_marginEnd="@dimen/dp_25"
                android:scrollbars="none"
                app:layout_constraintBottom_toBottomOf="@id/confirm"
                app:layout_constraintEnd_toStartOf="@id/confirm"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintTop_toTopOf="@id/confirm">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main_tool_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:orientation="horizontal">

                    <com.topstack.kilonotes.base.doodle.views.DoubleImageLayerToolView
                        android:id="@+id/noteMainToolPen"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"
                        android:layout_marginStart="@dimen/dp_15"
                        app:borderLayerSrc="@drawable/note_tool_gel_pen_normal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/noteMainToolHighlighter"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tintLayerSrc="@drawable/note_tool_gel_pen_selected"
                        tools:ignore="ContentDescription" />

                    <com.topstack.kilonotes.base.doodle.views.DoubleImageLayerToolView
                        android:id="@+id/noteMainToolHighlighter"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"
                        android:layout_marginStart="@dimen/dp_27"
                        app:borderLayerSrc="@drawable/note_main_icon_tool_highlighter_normal"
                        app:layout_constraintBottom_toBottomOf="@id/noteMainToolPen"
                        app:layout_constraintEnd_toStartOf="@id/noteMainToolEraser"
                        app:layout_constraintStart_toEndOf="@id/noteMainToolPen"
                        app:layout_constraintTop_toTopOf="@id/noteMainToolPen"
                        app:tintLayerSrc="@drawable/note_main_icon_tool_highlighter_selected"
                        tools:ignore="ContentDescription" />

                    <ImageView
                        android:id="@+id/noteMainToolEraser"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"
                        android:layout_marginStart="@dimen/dp_27"
                        android:src="@drawable/note_main_tool_eraser"
                        app:layout_constraintBottom_toBottomOf="@id/noteMainToolPen"
                        app:layout_constraintEnd_toStartOf="@id/noteMainToolLasso"
                        app:layout_constraintStart_toEndOf="@id/noteMainToolHighlighter"
                        app:layout_constraintTop_toTopOf="@id/noteMainToolPen"
                        tools:ignore="ContentDescription" />

                    <ImageView
                        android:id="@+id/noteMainToolLasso"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"
                        android:layout_marginStart="@dimen/dp_27"
                        android:src="@drawable/note_main_tool_lasso"
                        app:layout_constraintBottom_toBottomOf="@id/noteMainToolPen"
                        app:layout_constraintEnd_toStartOf="@id/noteMainToolPic"
                        app:layout_constraintStart_toEndOf="@id/noteMainToolEraser"
                        app:layout_constraintTop_toTopOf="@id/noteMainToolPen"
                        tools:ignore="ContentDescription" />

                    <ImageView
                        android:id="@+id/noteMainToolPic"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"
                        android:layout_marginStart="@dimen/dp_27"
                        android:src="@drawable/note_main_tool_pic"
                        app:layout_constraintBottom_toBottomOf="@id/noteMainToolPen"
                        app:layout_constraintEnd_toStartOf="@id/noteMainToolText"
                        app:layout_constraintStart_toEndOf="@id/noteMainToolLasso"
                        app:layout_constraintTop_toTopOf="@id/noteMainToolPen"
                        tools:ignore="ContentDescription" />

                    <ImageView
                        android:id="@+id/noteMainToolText"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"
                        android:layout_marginStart="@dimen/dp_27"
                        android:src="@drawable/note_main_tool_text"
                        app:layout_constraintBottom_toBottomOf="@id/noteMainToolPen"
                        app:layout_constraintEnd_toStartOf="@id/noteMainToolGraffiti"
                        app:layout_constraintStart_toEndOf="@id/noteMainToolPic"
                        app:layout_constraintTop_toTopOf="@id/noteMainToolPen"
                        tools:ignore="ContentDescription" />


                    <ImageView
                        android:id="@+id/noteMainToolGraffiti"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"
                        android:layout_marginStart="@dimen/dp_27"
                        android:src="@drawable/note_main_tool_graffiti"
                        app:layout_constraintBottom_toBottomOf="@id/noteMainToolPen"
                        app:layout_constraintEnd_toEndOf="@+id/noteMainToolGraph"
                        app:layout_constraintStart_toEndOf="@id/noteMainToolText"
                        app:layout_constraintTop_toTopOf="@id/noteMainToolPen"
                        tools:ignore="ContentDescription" />

                    <ImageView
                        android:id="@+id/noteMainToolGraph"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"
                        android:layout_marginStart="@dimen/dp_27"
                        android:src="@drawable/note_main_tool_graph"
                        app:layout_constraintBottom_toBottomOf="@id/noteMainToolPen"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/noteMainToolGraffiti"
                        app:layout_constraintTop_toTopOf="@id/noteMainToolPen"
                        tools:ignore="ContentDescription" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </HorizontalScrollView>

            <ImageView
                android:id="@+id/confirm"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_marginEnd="@dimen/dp_30"
                android:layout_marginBottom="@dimen/dp_4"
                android:src="@drawable/hand_write_snippet_icon_confirm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/back"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginBottom="@dimen/dp_4"
                android:src="@drawable/note_main_icon_tool_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:ignore="ContentDescription" />

            <View
                android:id="@+id/split_line"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:background="@color/note_tool_split_line_color"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </eightbitlab.com.blurview.BlurView>
    <!--  major tool area -->

    <View
        android:id="@+id/hide_add_page_layout_cover"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/blur_view" />

    <!-- toolbar show view -->
    <com.lihang.ShadowLayout
        android:id="@+id/tool_bar_show"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:hl_cornerRadius_rightTop="@dimen/dp_12"
        app:hl_shadowColor="@color/note_tool_view_shadow"
        app:hl_shadowHiddenBottom="true"
        app:hl_shadowHiddenLeft="true"
        app:hl_shadowLimit="@dimen/dp_30"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_70"
            android:background="@color/white"
            android:padding="@dimen/dp_20"
            android:scaleType="centerInside"
            android:src="@drawable/tool_bar_show_icon" />
    </com.lihang.ShadowLayout>
    <!-- toolbar show view -->

    <LinearLayout
        android:id="@+id/text_operation_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/note_tool_text_operation_container_shape"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/text_strikethrough"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_32"
            android:src="@drawable/note_tool_text_strikethrough_selector" />

        <ImageView
            android:id="@+id/text_bold"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_32"
            android:src="@drawable/note_tool_text_bold_selector" />

        <ImageView
            android:id="@+id/text_underline"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_32"
            android:layout_marginEnd="@dimen/dp_32"
            android:src="@drawable/note_tool_text_underline_selector" />
    </LinearLayout>

    <View
        android:id="@+id/tool_bar_selection_substitute"
        android:layout_width="@dimen/dp_54"
        android:layout_height="0dp"
        android:background="@color/black_20"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/blur_view" />


    <com.topstack.kilonotes.base.component.view.AdsorptionEdgeLayout
        android:id="@+id/tool_bar"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:visibility="invisible"
        app:dragPoint="@id/drag_point"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/blur_view"
        app:topView="@id/blur_view">
        <!--  minor tool area  -->

        <com.topstack.kilonotes.base.shadow.FixShadowLayout
            android:id="@+id/minorToolContainer"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:hl_shadowColor="@color/note_tool_view_shadow"
            app:hl_shadowHiddenBottom="true"
            app:hl_shadowHiddenLeft="true"
            app:hl_shadowHiddenTop="true"
            app:hl_shadowLimit="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/minor_tool_content"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@color/transparent"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <View
                    android:id="@+id/drag_point"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">


                </View>

                <View
                    android:id="@+id/drag_point_icon"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_5"
                    android:background="@drawable/note_tool_bar_drag_point_icon"
                    app:layout_constraintBottom_toBottomOf="@id/drag_point"
                    app:layout_constraintEnd_toEndOf="@id/drag_point"
                    app:layout_constraintStart_toStartOf="@id/drag_point"
                    app:layout_constraintTop_toTopOf="@id/drag_point" />


                <ImageView
                    android:id="@+id/undo"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    android:scaleType="fitXY"
                    android:src="@drawable/note_tool_undo_background_selector"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/drag_point" />

                <ImageView
                    android:id="@+id/redo"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    android:scaleType="fitXY"
                    android:src="@drawable/note_tool_redo_background_selector"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/undo" />

                <com.topstack.kilonotes.base.component.view.CustomMaxHeightRecycleView
                    android:id="@+id/minorToolRecyclerView"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/dp_40"
                    android:layout_weight="1"
                    android:background="@color/transparent"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toTopOf="@id/suppressibleToolRecyclerView"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/redo" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/suppressibleToolRecyclerView"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    android:background="@color/transparent"
                    android:orientation="vertical"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/tool_bar_hide"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/minorToolRecyclerView"
                    app:layout_constraintVertical_bias="0" />

                <ImageView
                    android:id="@+id/tool_bar_hide"
                    android:layout_width="@dimen/dp_60"
                    android:layout_height="@dimen/dp_60"
                    android:src="@drawable/tool_bar_hide_icon"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.topstack.kilonotes.base.shadow.FixShadowLayout>
        <!--  minor tool area  -->
    </com.topstack.kilonotes.base.component.view.AdsorptionEdgeLayout>

    <!-- toolbar end boundary -->
    <View
        android:id="@+id/tool_bar_end_boundary"
        android:layout_width="@dimen/dp_1"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/blur_view" />
    <!-- toolbar end boundary -->

    <com.topstack.kilonotes.pad.component.WebSidebarView
        android:id="@+id/translate_web_view"
        android:layout_width="@dimen/dp_450"
        android:layout_height="match_parent"
        android:visibility="invisible"
        app:layout_constraintStart_toEndOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white"
        android:clipChildren="false">

        <View
            android:id="@+id/doodle_bg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#F2F2F7"
            app:layout_constraintBottom_toBottomOf="@id/doodle"
            app:layout_constraintEnd_toEndOf="@id/doodle"
            app:layout_constraintStart_toStartOf="@id/doodle"
            app:layout_constraintTop_toTopOf="@id/doodle" />

        <!--  doodle  -->
        <com.topstack.kilonotes.base.doodle.views.doodleview.DoodleView
            android:id="@+id/doodle"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/blur_view" />
        <!--  doodle  -->

        <!--  major tool area -->
        <eightbitlab.com.blurview.BlurView
            android:id="@+id/blur_view"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_56"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.topstack.kilonotes.pad.customtool.view.MajorToolLayout
                android:id="@+id/note_major_tool_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </eightbitlab.com.blurview.BlurView>
        <!--  major tool area -->

        <View
            android:id="@+id/side_list_top_boundary"
            android:layout_width="match_parent"
            android:layout_height="1px"
            app:layout_constraintTop_toBottomOf="@id/record_control_view" />

        <View
            android:id="@+id/side_list_bottom_boundary"
            android:layout_width="match_parent"
            android:layout_height="1px"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!--  page thumbnail and snippet  -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/thumbnail_or_snippet_list"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="@color/white"
            android:clickable="true"
            android:focusable="true"
            android:minWidth="@dimen/dp_840"
            app:layout_constraintBottom_toBottomOf="@id/side_list_bottom_boundary"
            app:layout_constraintEnd_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/side_list_top_boundary">
            <!--  page thumbnail  -->
            <com.topstack.kilonotes.pad.component.NoteThumbnailAndOutlineView
                android:id="@+id/thumbnail_and_outline_view"
                android:layout_width="@dimen/dp_420"
                android:layout_height="match_parent"
                app:layout_constraintEnd_toEndOf="parent" />
            <!--  page thumbnail   -->
            <!-- page snippet -->
            <com.topstack.kilonotes.pad.component.SnippetLayout
                android:id="@+id/page_snippet_list"
                android:layout_width="@dimen/dp_420"
                android:layout_height="match_parent"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent" />
            <!-- page snippet -->
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/page_thumbnail_list_right_stroke"
            android:layout_width="@dimen/dp_1"
            android:layout_height="0dp"
            android:background="@color/thumbnail_list_right_stroke"
            app:layout_constraintBottom_toBottomOf="@id/thumbnail_or_snippet_list"
            app:layout_constraintStart_toEndOf="@id/thumbnail_or_snippet_list"
            app:layout_constraintTop_toTopOf="@id/thumbnail_or_snippet_list" />
        <!--  page thumbnail   -->

        <!--  sync group with thumbnail animation  -->
        <androidx.constraintlayout.helper.widget.Layer
            android:id="@+id/layer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="thumbnail_or_snippet_list,tool_bar_show,page_thumbnail_list_right_stroke,page_indicator,note_title,search_page_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <!--  sync group with thumbnail animation  -->


        <!--  floating indicator  -->
        <TextView
            android:id="@+id/page_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_80"
            android:layout_marginBottom="@dimen/dp_22"
            android:background="@drawable/page_indicator_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_20"
            android:paddingTop="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_8"
            android:textColor="#787878"
            android:textSize="@dimen/sp_22"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/search_page_back"
            app:layout_goneMarginStart="@dimen/dp_80"
            tools:text="1/1" />

        <ImageView
            android:id="@+id/search_page_back"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_48"
            android:background="@drawable/page_indicator_bg"
            android:scaleType="centerInside"
            android:src="@drawable/search_page_back"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/page_indicator"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/page_indicator" />

        <TextView
            android:id="@+id/note_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:background="@drawable/page_indicator_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_20"
            android:paddingTop="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_8"
            android:textColor="#787878"
            android:textSize="@dimen/sp_22"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/page_indicator"
            app:layout_constraintStart_toEndOf="@id/page_indicator"
            tools:text="无标题笔记本-42"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/text_operation_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/note_tool_text_operation_container_shape"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/dp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/text_strikethrough"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_marginStart="@dimen/dp_32"
                android:src="@drawable/note_tool_text_strikethrough_selector" />

            <ImageView
                android:id="@+id/text_bold"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_marginStart="@dimen/dp_32"
                android:src="@drawable/note_tool_text_bold_selector" />

            <ImageView
                android:id="@+id/text_underline"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_marginStart="@dimen/dp_32"
                android:layout_marginEnd="@dimen/dp_32"
                android:src="@drawable/note_tool_text_underline_selector" />
        </LinearLayout>
        <!--  floating indicator  -->

        <View
            android:id="@+id/tool_bar_selection_substitute"
            android:layout_width="@dimen/dp_54"
            android:layout_height="0dp"
            android:background="@color/black_20"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/thumbnail_or_snippet_list"
            app:layout_constraintTop_toBottomOf="@id/blur_view" />

        <com.topstack.kilonotes.pad.component.NoteRecordControlView
            android:id="@+id/record_control_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/blur_view" />

        <!--  tool bar   -->
        <com.topstack.kilonotes.base.component.view.AdsorptionEdgeLayout
            android:id="@+id/tool_bar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            app:dragPoint="@id/drag_point"
            app:layout_constraintStart_toEndOf="@id/thumbnail_or_snippet_list"
            app:layout_constraintTop_toBottomOf="@id/record_control_view"
            app:leftView="@id/note_material_view"
            app:rightView="@id/thumbnail_or_snippet_list"
            app:topView="@id/record_control_view">
            <!--  minor tool area  -->
            <com.topstack.kilonotes.base.shadow.FixShadowLayout
                android:id="@+id/minorToolContainer"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                app:hl_layoutBackground="@color/white_85"
                app:hl_shadowColor="@color/note_tool_view_shadow"
                app:hl_shadowHiddenBottom="true"
                app:hl_shadowHiddenLeft="true"
                app:hl_shadowHiddenTop="true"
                app:hl_shadowLimit="@dimen/dp_12">


                <eightbitlab.com.blurview.BlurView
                    android:id="@+id/tool_bar_blur_view"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/minor_tool_content"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:background="@color/transparent"
                        android:orientation="vertical"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <View
                            android:id="@+id/drag_point"
                            android:layout_width="@dimen/dp_54"
                            android:layout_height="@dimen/dp_54"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">


                        </View>

                        <View
                            android:id="@+id/drag_point_icon"
                            android:layout_width="@dimen/dp_21"
                            android:layout_height="@dimen/dp_4"
                            android:background="@drawable/note_tool_bar_drag_point_icon"
                            app:layout_constraintBottom_toBottomOf="@id/drag_point"
                            app:layout_constraintEnd_toEndOf="@id/drag_point"
                            app:layout_constraintStart_toStartOf="@id/drag_point"
                            app:layout_constraintTop_toTopOf="@id/drag_point" />


                        <ImageView
                            android:id="@+id/undo"
                            android:layout_width="@dimen/dp_54"
                            android:layout_height="@dimen/dp_54"
                            android:scaleType="fitXY"
                            android:src="@drawable/note_tool_undo_background_selector"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/drag_point" />

                        <ImageView
                            android:id="@+id/redo"
                            android:layout_width="@dimen/dp_54"
                            android:layout_height="@dimen/dp_54"
                            android:scaleType="fitXY"
                            android:src="@drawable/note_tool_redo_background_selector"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/undo" />

                        <com.topstack.kilonotes.base.component.view.CustomMaxHeightRecycleView
                            android:id="@+id/minorToolRecyclerView"
                            android:layout_width="@dimen/dp_54"
                            android:layout_height="0dp"
                            android:layout_marginTop="@dimen/dp_40"
                            android:layout_weight="1"
                            android:background="@color/transparent"
                            android:orientation="vertical"
                            app:layout_constraintBottom_toTopOf="@id/suppressibleToolRecyclerView"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/redo" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/suppressibleToolRecyclerView"
                            android:layout_width="@dimen/dp_54"
                            android:layout_height="@dimen/dp_54"
                            android:background="@color/transparent"
                            android:orientation="vertical"
                            android:visibility="gone"
                            app:layout_constraintBottom_toTopOf="@id/tool_bar_hide"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/minorToolRecyclerView"
                            app:layout_constraintVertical_bias="0" />

                        <ImageView
                            android:id="@+id/tool_bar_hide"
                            android:layout_width="@dimen/dp_54"
                            android:layout_height="@dimen/dp_54"
                            android:src="@drawable/tool_bar_hide_icon"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </eightbitlab.com.blurview.BlurView>

            </com.topstack.kilonotes.base.shadow.FixShadowLayout>

            <!--  minor tool area  -->
        </com.topstack.kilonotes.base.component.view.AdsorptionEdgeLayout>

        <View
            android:id="@+id/tool_bar_end_boundary"
            android:layout_width="@dimen/dp_1"
            android:layout_height="0dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/blur_view" />

        <com.lihang.ShadowLayout
            android:id="@+id/tool_bar_show"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:hl_cornerRadius_leftTop="@dimen/dp_12"
            app:hl_shadowColor="@color/note_tool_view_shadow"
            app:hl_shadowHiddenBottom="true"
            app:hl_shadowHiddenRight="true"
            app:hl_shadowLimit="@dimen/dp_30"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/page_thumbnail_list_right_stroke">

            <ImageView
                android:layout_width="@dimen/dp_60"
                android:layout_height="@dimen/dp_70"
                android:background="@color/white"
                android:padding="@dimen/dp_20"
                android:scaleType="centerInside"
                android:src="@drawable/tool_bar_show_icon" />

        </com.lihang.ShadowLayout>
        <!--  tool bar  -->

        <FrameLayout
            android:id="@+id/other_doc_preview_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <FrameLayout
            android:id="@+id/ai_container"
            android:layout_width="@dimen/dp_552"
            android:layout_height="0dp"
            android:clickable="true"
            android:focusable="true"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/side_list_bottom_boundary"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/side_list_top_boundary" />

        <FrameLayout
            android:id="@+id/ai_one_third_container"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- note material -->
        <com.topstack.kilonotes.pad.component.NoteMaterialLayout
            android:id="@+id/note_material_view"
            android:layout_width="@dimen/dp_469"
            android:layout_height="0dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/side_list_bottom_boundary"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/side_list_top_boundary" />
        <!-- note material -->

        <com.topstack.kilonotes.pad.component.NoteAddPageLayout
            android:id="@+id/note_add_page_layout"
            android:layout_width="@dimen/dp_480"
            android:layout_height="0dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/side_list_bottom_boundary"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/side_list_top_boundary" />

        <!-- draftPaper show view -->
        <com.lihang.ShadowLayout
            android:id="@+id/draft_paper_show_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_22"
            android:visibility="invisible"
            app:hl_cornerRadius_leftBottom="@dimen/dp_12"
            app:hl_cornerRadius_leftTop="@dimen/dp_12"
            app:hl_shadowColor="@color/note_tool_view_shadow"
            app:hl_shadowHiddenRight="true"
            app:hl_shadowLimit="@dimen/dp_30"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:layout_width="@dimen/dp_60"
                android:layout_height="@dimen/dp_70"
                android:background="@color/white"
                android:paddingHorizontal="@dimen/dp_10"
                android:paddingVertical="@dimen/dp_15"
                android:scaleType="centerInside"
                android:src="@drawable/draft_paper_icon" />
        </com.lihang.ShadowLayout>
        <!-- draftPaper show view -->

        <!--  template download loading -->
        <include
            android:id="@+id/template_download_dialog"
            layout="@layout/include_template_download"
            android:visibility="gone" />
        <!--  template download loading   -->

        <!--  doodle high light view -->
        <com.topstack.kilonotes.base.doodle.views.doodleview.DoodleHighLightView
            android:id="@+id/high_light_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />
        <!--  doodle high light view -->

        <com.topstack.kilonotes.pad.component.WebSidebarView
            android:id="@+id/web_sidebar_view"
            android:layout_width="@dimen/dp_479"
            android:layout_height="0dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/side_list_bottom_boundary"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/side_list_top_boundary" />

        <!--  snippet preview view -->
        <com.topstack.kilonotes.pad.component.SnippetPreviewImageView
            android:id="@+id/expanded_image_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible" />
        <!--  snippet preview view -->

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/snippet_document_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.topstack.kilonotes.base.shadow.FixShadowLayout
            android:id="@+id/scale_lock_and_ratio_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_18"
            app:hl_cornerRadius="@dimen/dp_30"
            app:hl_layoutBackground="@color/transparent"
            app:hl_shadowColor="@color/note_tool_view_shadow"
            app:hl_shadowLimit="@dimen/dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/blur_view">

            <com.lihang.ShadowLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:hl_cornerRadius="@dimen/dp_30"
                app:hl_layoutBackground="@color/transparent">

                <eightbitlab.com.blurview.BlurView
                    android:id="@+id/ratio_blur_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:blurRadius="21"
                    app:overlayColor="@color/note_editor_tool_bar_background_overlay_color">

                    <LinearLayout
                        android:id="@+id/scale_lock_and_ratio_tool_content"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_36"
                        android:background="@color/transparent"
                        android:orientation="horizontal"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/scale_lock"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"
                            android:layout_marginStart="@dimen/dp_8"
                            android:scaleType="fitXY"
                            android:src="@drawable/draft_paper_scale_lock_background_selector" />

                        <TextView
                            android:id="@+id/scale_ratio"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_22"
                            android:layout_gravity="center"
                            android:layout_marginStart="@dimen/dp_4"
                            android:layout_marginEnd="@dimen/dp_8"
                            android:textAlignment="center"
                            android:textColor="@color/black"
                            android:textSize="@dimen/sp_16" />

                    </LinearLayout>
                </eightbitlab.com.blurview.BlurView>
            </com.lihang.ShadowLayout>

        </com.topstack.kilonotes.base.shadow.FixShadowLayout>

        <!-- 放置屏幕外侧，且以Start为基准，防止首次动画闪烁以及拖转尺寸以Start边为基准 -->
        <com.topstack.kilonotes.base.component.view.DraggableLayout
            android:id="@+id/draft_paper_fragment_container"
            android:layout_width="@dimen/dp_720"
            android:layout_height="@dimen/dp_485"
            android:layout_marginTop="@dimen/dp_100"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:minHeight="@dimen/dp_406"
            app:minWidth="@dimen/dp_460" />

        <com.lihang.ShadowLayout
            android:id="@+id/tips_resizing_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_20"
            android:visibility="gone"
            app:hl_cornerRadius="@dimen/dp_15"
            app:hl_layoutBackground="@color/white"
            app:hl_shadowLimit="@dimen/dp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/blur_view">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_100"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_24"
                    android:text="@string/note_tips_title_page_resizing"
                    android:textColor="@color/bottom_sheet_black"
                    android:textSize="@dimen/sp_24" />

                <TextView
                    android:id="@+id/tips_resizing_to_set"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_18"
                    android:background="@drawable/note_tips_page_resizing_set_background"
                    android:paddingHorizontal="@dimen/dp_24"
                    android:paddingVertical="@dimen/dp_4"
                    android:text="@string/go_to_set"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_20" />

                <ImageView
                    android:id="@+id/tips_resizing_close"
                    android:layout_width="@dimen/dp_44"
                    android:layout_height="@dimen/dp_44"
                    android:layout_marginStart="@dimen/dp_28"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:padding="@dimen/dp_10"
                    android:src="@drawable/dialog_import_close" />
            </androidx.appcompat.widget.LinearLayoutCompat>

        </com.lihang.ShadowLayout>

        <com.topstack.kilonotes.base.doodle.views.doodleview.ConsoleGestureLayer
            android:id="@+id/console_gesture_layer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.topstack.kilonotes.pad.note.ConsoleInstructionsView
            android:id="@+id/console_instruction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/blur_view" />

        <com.topstack.kilonotes.pad.component.RoundCornerShadowLayout
            android:id="@+id/console_guide"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_110"
            android:visibility="invisible"
            app:border_corner_radius="@dimen/dp_15"
            app:border_shadow_color="@color/black_20"
            app:border_shadow_size="@dimen/dp_22"
            app:border_shadow_y_offset="@dimen/dp_3"
            app:border_stroke_width="@dimen/dp_3"
            app:inner_border_color="@color/console_note_btn_inner_border"
            app:inside_background_color="@color/console_note_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:outer_border_color="@color/black_15">

            <ImageView
                android:id="@+id/console_guide_img"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_margin="@dimen/dp_13"
                android:background="@drawable/console_guide"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </com.topstack.kilonotes.pad.component.RoundCornerShadowLayout>

        <com.topstack.kilonotes.pad.component.RoundCornerShadowLayout
            android:id="@+id/console_note"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_120"
            android:layout_marginBottom="@dimen/dp_420"
            android:visibility="invisible"
            app:border_corner_radius="@dimen/dp_15"
            app:border_shadow_color="@color/black_20"
            app:border_shadow_size="@dimen/dp_26"
            app:border_shadow_y_offset="@dimen/dp_3"
            app:border_stroke_width="@dimen/dp_3"
            app:inner_border_color="@color/console_note_btn_inner_border"
            app:inside_background_color="@color/console_preview_page_list_background_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:outer_border_color="@color/black_15">

            <ImageView
                android:id="@+id/console_note_icon"
                android:layout_width="@dimen/dp_56"
                android:layout_height="@dimen/dp_56"
                android:background="@drawable/console_note"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </com.topstack.kilonotes.pad.component.RoundCornerShadowLayout>


        <com.topstack.kilonotes.pad.component.RoundCornerShadowLayout
            android:id="@+id/console_last_document_rv_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_774"
            android:visibility="gone"
            app:border_corner_radius="@dimen/dp_15"
            app:border_shadow_color="@color/black_40"
            app:border_shadow_size="@dimen/dp_48"
            app:border_shadow_y_offset="@dimen/dp_4"
            app:border_stroke_width="@dimen/dp_3"
            app:inner_border_color="@color/console_document_list_background_inner_border"
            app:inside_background_color="@color/console_document_list_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:outer_border_color="@color/console_document_list_background_outer_border">

            <com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
                android:id="@+id/console_last_document_rv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/dp_9"
                android:paddingVertical="@dimen/dp_21"
                app:scrollOrientation="horizontal"
                tools:visibility="visible" />


        </com.topstack.kilonotes.pad.component.RoundCornerShadowLayout>


        <com.topstack.kilonotes.pad.console.view.ConsoleKeyImageView
            android:id="@+id/console"
            android:layout_width="@dimen/dp_102"
            android:layout_height="@dimen/dp_102"
            android:layout_marginStart="@dimen/dp_102"
            android:layout_marginBottom="@dimen/dp_220"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.topstack.kilonotes.pad.component.RoundCornerShadowLayout
            android:id="@+id/console_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_80"
            android:layout_marginBottom="@dimen/dp_200"
            app:border_corner_radius="@dimen/dp_55"
            app:border_shadow_color="@color/black_20"
            app:border_shadow_size="@dimen/dp_26"
            app:border_shadow_y_offset="@dimen/dp_4"
            app:border_stroke_width="@dimen/dp_3"
            app:inner_border_color="@color/console_note_btn_inner_border"
            app:inside_background_color="@color/console_note_btn"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:outer_border_color="@color/black_15">

            <ImageView
                android:id="@+id/console_icon"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_margin="@dimen/dp_30"
                android:background="@drawable/console_btn_bg_states"
                app:layout_constraintBottom_toBottomOf="@id/parent"
                app:layout_constraintEnd_toEndOf="@id/parent"
                app:layout_constraintStart_toStartOf="@id/parent"
                app:layout_constraintTop_toTopOf="@id/parent" />
        </com.topstack.kilonotes.pad.component.RoundCornerShadowLayout>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/console_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="console,console_bg" />

        <!-- Console Toast Container -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/console_toast_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_60"
            android:background="@color/white_75"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/blur_view">

            <TextView
                android:id="@+id/console_toast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/sp_20"
                android:textStyle="bold"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/console_toast_undo"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/console_toast_undo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_60"
                android:gravity="center_vertical"
                android:textColor="@color/hint_text"
                android:textSize="@dimen/sp_20"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/console_toast"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.topstack.kilonotes.pad.console.view.ConsoleUndoRedoView
            android:id="@+id/console_undo_redo"
            android:layout_width="@dimen/dp_262"
            android:layout_height="@dimen/dp_262"
            android:layout_marginEnd="@dimen/dp_102"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/console"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/console" />

        <com.topstack.kilonotes.base.doodle.views.pagepreviewview.PagePreviewListLayout
            android:id="@+id/page_preview_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_277"
            android:layout_marginBottom="@dimen/dp_5"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/page_preview_arrow"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_115"
            android:src="@drawable/page_preview_arrow"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/page_preview_list"
            app:layout_constraintEnd_toEndOf="@+id/page_preview_list" />

        <ImageView
            android:id="@+id/page_preview_page_turn_rate"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_20"
            android:layout_marginStart="@dimen/dp_4"
            android:src="@drawable/page_preview_page_turn_rate_x1"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/page_preview_arrow"
            app:layout_constraintStart_toEndOf="@+id/page_preview_arrow"
            app:layout_constraintTop_toTopOf="@+id/page_preview_arrow" />

        <com.topstack.kilonotes.pad.component.RoundCornerShadowLayout
            android:id="@+id/undo_redo_toast_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_70"
            android:visibility="invisible"
            app:border_corner_radius="@dimen/dp_18"
            app:border_shadow_color="@color/black_30"
            app:border_shadow_size="@dimen/dp_12"
            app:inside_background_color="@color/transparent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/blur_view">

            <TextView
                android:id="@+id/undo_redo_toast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/toast_smaller_background"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_42"
                android:paddingVertical="@dimen/dp_8"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_15"
                tools:text="Kilo Note's Toast" />
        </com.topstack.kilonotes.pad.component.RoundCornerShadowLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/banner_ad_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:maxHeight="@dimen/dp_50" />
</LinearLayout>
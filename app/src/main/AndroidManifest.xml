<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />

    <queries>
        <package android:name="com.mywallpaper.customizechanger" />
    </queries>

    <application
        android:name=".KiloApp"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_conf"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.Kilonotes"
        tools:replace="android:allowBackup"
        tools:targetApi="n">
        <activity
            android:name=".base.ad.AppOpenAdLoadingActivity"
            android:launchMode="singleTop"
            android:exported="false" />
        <activity
            android:name=".MainActivity"
            android:exported="false" />

        <service
            android:name=".base.audio.AudioRecordService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <profileable
            android:shell="true"
            tools:targetApi="q" /> <!-- xuanhu使用，该app投放的渠道 -->
        <meta-data
            android:name="app.build.channel"
            android:value="${APP_BUILD_CHANNEL}" />

        <activity
            android:name=".base.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.Kilonotes.NoTitleBar.FullScreen">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".pad.MainActivity"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".pad.sync.BaiduLoginActivity"
            android:configChanges="orientation|screenLayout|smallestScreenSize|screenSize"
            android:theme="@style/Theme.Kilonotes.Dialog" />
        <activity
            android:name=".pad.guide.PadGuideActivity"
            android:theme="@style/Theme.Kilonotes.NoTitleBar.FullScreen" />
        <activity
            android:name=".pad.about.AboutActivity"
            android:theme="@style/Theme.Kilonotes.Dialog" />
        <activity
            android:name=".pad.agreement.UserAgreementActivity"
            android:theme="@style/Theme.Kilonotes.Dialog" />
        <activity
            android:name=".pad.agreement.DomesticInfoCollectActivity"
            android:theme="@style/Theme.Kilonotes.Dialog" />
        <activity
            android:name=".pad.agreement.UserExperienceActivity"
            android:theme="@style/Theme.Kilonotes.Dialog" />
        <activity
            android:name=".pad.account.AccountCancellationActivity"
            android:theme="@style/Theme.Kilonotes.Dialog" />
        <activity
            android:name=".pad.select.SelectPhotoDialogActivity"
            android:theme="@style/Theme.Kilonotes.Dialog" />
        <activity
            android:name="com.dropbox.core.android.AuthActivity"
            android:configChanges="orientation|keyboard"
            android:exported="true"
            android:launchMode="singleTask">

            <!--
                 Your activity starting authorization flow should also configured with android:launchMode="singleTask".
                 If that activity is configured with android:taskAffinity, this AuthActivity should also configured
                 with the same android:taskAffinity so the auth result can be correctly passed back.
            -->
            <intent-filter>
                <data android:scheme="db-${dropboxKey}" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <!-- Additional intent-filter required as a workaround for Apps using targetSdk=33 until the fix in the Dropbox app is available to all users. https://github.com/dropbox/dropbox-sdk-java/issues/406 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".base.importfile.ImportFileActivity"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:mimeType="application/pdf"
                    android:scheme="content" />
                <data
                    android:mimeType="application/zip"
                    android:scheme="content" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:mimeType="application/pdf"
                    android:scheme="file" />
                <data
                    android:mimeType="application/zip"
                    android:scheme="file" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="application/pdf" />
                <data android:mimeType="application/zip" />
            </intent-filter>
        </activity>
        <activity
            android:name=".phone.PhoneMainActivity"
            android:launchMode="singleTask"
            android:resizeableActivity="false"
            android:screenOrientation="portrait"
            android:taskAffinity="com.topstack.kilonotes.not_resizeable"
            android:windowSoftInputMode="adjustResize"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".phone.guide.PhoneGuideActivity"
            android:launchMode="singleTask"
            android:resizeableActivity="false"
            android:screenOrientation="portrait"
            android:taskAffinity="com.topstack.kilonotes.not_resizeable"
            android:theme="@style/Theme.Kilonotes.NoTitleBar.FullScreen"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".phone.agreement.PhoneUserAgreementActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Kilonotes.NoTitleBar.FullScreen"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".phone.agreement.PhoneDomesticInfoCollectActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Kilonotes.NoTitleBar.FullScreen"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".phone.select.PhoneSelectPhotoActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" /> <!-- share -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <receiver android:name=".pad.promotion.checkin.CheckInNotificationReceiver" />
    </application>

</manifest>
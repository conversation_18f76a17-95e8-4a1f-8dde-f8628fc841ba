package com.topstack.kilonotes.phone.component

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target.SIZE_ORIGINAL
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.doc.io.ResourceManager
import com.topstack.kilonotes.base.note.contants.covers
import com.topstack.kilonotes.base.note.model.NotebookCover
import com.topstack.kilonotes.base.note.model.NotebookCoverType
import com.topstack.kilonotes.base.util.DEFAULT_COVER
import com.topstack.kilonotes.databinding.PhoneCreateNoteCoverListItemBinding
import java.io.File

class CoverSelectView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val granularRoundedCorners = GranularRoundedCorners(
        context.resources.getDimension(R.dimen.dp_4),
        context.resources.getDimension(R.dimen.dp_12),
        context.resources.getDimension(R.dimen.dp_12),
        context.resources.getDimension(R.dimen.dp_4)
    )

    private lateinit var coverAdapter: CoverAdapter

    fun setSelectPosition(position: Int) {
        if (!::coverAdapter.isInitialized) return
        if (position >= coverAdapter.coverList.size) return
        coverAdapter.currentSelectPosition = position
    }

    private fun setup(
        initialPosition: Int,
        coverList: List<NotebookCover>,
        myItemDecoration: ItemDecoration?,
        myLayoutManager: LayoutManager,
        resourceManager: ResourceManager?,
        onDeleteClick: (NotebookCover) -> Boolean,
        onCoverSelected: (NotebookCover, Boolean) -> Boolean
    ) {
        CoverAdapter(
            initialPosition,
            coverList,
            resourceManager,
            granularRoundedCorners,
            onDeleteClick
        ) { position ->
            if (onCoverSelected(coverAdapter.coverList[position], false)) {
                coverAdapter.currentSelectPosition = position
            }
        }.let {
            coverAdapter = it
            adapter = it
        }
        while (itemDecorationCount > 0) {
            removeItemDecorationAt(0)
        }
        if (myItemDecoration != null) {
            addItemDecoration(myItemDecoration)
        }
        layoutManager = myLayoutManager
        onCoverSelected(coverAdapter.coverList[coverAdapter.currentSelectPosition], true)
    }

    fun setupHorizontal(
        initialPosition: Int,
        coverList: List<NotebookCover>,
        resourceManager: ResourceManager?,
        onDeleteClick: (NotebookCover) -> Boolean,
        onCoverSelected: (NotebookCover, Boolean) -> Boolean
    ) {
        val myItemDecoration = object : RecyclerView.ItemDecoration() {
            private val startEndInterval = context.resources.getDimensionPixelSize(R.dimen.dp_46)
            private val horizontalInterval = context.resources.getDimensionPixelSize(R.dimen.dp_34)

            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: State
            ) {
                val position = getChildAdapterPosition(view)
                outRect.left = if (position == 0) {
                    startEndInterval
                } else {
                    horizontalInterval
                }
                outRect.right = if (coverList.size == position + 1) {
                    startEndInterval
                } else {
                    horizontalInterval
                }
            }
        }
        val myLayoutManager = LinearLayoutManager(context, HORIZONTAL, false)
        setup(
            initialPosition,
            coverList,
            myItemDecoration,
            myLayoutManager,
            resourceManager,
            onDeleteClick,
            onCoverSelected
        )
    }

    fun setupGrid(
        spanCount: Int,
        initialPosition: Int,
        coverList: List<NotebookCover>,
        resourceManager: ResourceManager?,
        onDeleteClick: (NotebookCover) -> Boolean,
        onCoverSelected: (NotebookCover, Boolean) -> Boolean
    ) {
        val myItemDecoration = object : RecyclerView.ItemDecoration() {
            private val horizontalInterval = context.resources.getDimensionPixelSize(R.dimen.dp_20)
            private val topInterval = context.resources.getDimensionPixelSize(R.dimen.dp_30)

            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: State
            ) {
                outRect.top = topInterval
                outRect.left = horizontalInterval
                outRect.right = horizontalInterval
            }
        }
        val myLayoutManager = GridLayoutManager(context, spanCount).apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    if (position < coverAdapter.coverList.size - 1) {
                        if (covers[position].coverType != covers[position + 1].coverType) {
                            return spanCount - (position - getFirstCoverPositionWithSameType(
                                position, coverAdapter.coverList
                            )) % spanCount
                        }
                    }
                    return 1
                }
            }
        }
        setup(
            initialPosition,
            coverList,
            myItemDecoration,
            myLayoutManager,
            resourceManager,
            onDeleteClick,
            onCoverSelected
        )
    }

    fun setCustomCoverUrl(url: String) {
        (adapter as CoverAdapter).customCoverUrl = url
    }
}

class CoverAdapter(
    val initialPosition: Int,
    val coverList: List<NotebookCover>,
    resourceManager: ResourceManager?,
    private val granularRoundedCorners: GranularRoundedCorners,
    private val onDeleteClick: (NotebookCover) -> Boolean,
    private val onClick: (position: Int) -> Unit
) : RecyclerView.Adapter<CoverAdapter.CoverHolder>() {
    companion object {
        const val DEFAULT_INDEX = 1
    }

    var currentSelectPosition: Int = initialPosition
        set(value) {
            if (field != value) {
                notifyItemChanged(field)
                field = value
                notifyItemChanged(value)
            }
        }

    var customCoverUrl: String? = null
        set(value) {
            field = value
            notifyItemChanged(0)
        }

    var resourceManager: ResourceManager? = resourceManager


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CoverHolder {
        val itemBinding =
            PhoneCreateNoteCoverListItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return CoverHolder(itemBinding, granularRoundedCorners, onDeleteClick)
    }

    override fun onBindViewHolder(holder: CoverHolder, position: Int) {
        holder.bind(
            coverList[position],
            position != 0 && currentSelectPosition == position,
            customCoverUrl,
            resourceManager,
            onDeleteClick = {
                onDeleteClick(coverList[DEFAULT_INDEX])
                currentSelectPosition = DEFAULT_INDEX
                customCoverUrl = null
            },
            onClick = {
                onClick(position)
            }
        )
    }

    override fun getItemCount(): Int = coverList.size

    class CoverHolder(
        private val binding: PhoneCreateNoteCoverListItemBinding,
        private val granularRoundedCorners: GranularRoundedCorners,
        private val onDeleteClick: (NotebookCover) -> Boolean
    ) : RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("CheckResult")
        fun bind(
            cover: NotebookCover,
            selected: Boolean,
            customCoverUrl: String?,
            resourceManager: ResourceManager?,
            onDeleteClick: () -> Unit,
            onClick: () -> Unit
        ) {
            binding.selected.visibility = if (selected) View.VISIBLE else View.INVISIBLE
            binding.delete.visibility =
                if (cover.coverType == NotebookCoverType.CUSTOM && customCoverUrl != null) {
                    View.VISIBLE
                } else {
                    View.INVISIBLE
                }
            binding.image.apply {
                if (cover.coverType == NotebookCoverType.CUSTOM) {
                    var customCover: File? = null
                    layoutParams = layoutParams.apply {
                        height = ViewGroup.LayoutParams.MATCH_PARENT
                    }
                    if (customCoverUrl == null) {
                        scaleType = ImageView.ScaleType.CENTER
                    } else {
                        scaleType = ImageView.ScaleType.CENTER_CROP
                        if (ResourceManager.isKiloPath(customCoverUrl)) {
                            val resource = resourceManager ?: return
                            customCover = resource.openFile(customCoverUrl)
                        }
                    }
                    val image = customCover ?: customCoverUrl ?: R.drawable.phone_create_note_custom_cover
                    Glide.with(context)
                        .load(image)
                        .apply {
                            if (customCoverUrl == null) {
                                override(
                                    context.resources.getDimensionPixelSize(R.dimen.dp_48),
                                    context.resources.getDimensionPixelSize(R.dimen.dp_42)
                                )
                            } else {
                                apply(
                                    RequestOptions.bitmapTransform(
                                        granularRoundedCorners
                                    )
                                )
                            }
                        }
                        .placeholder(DEFAULT_COVER)
                        .into(this)
                } else {
                    layoutParams = layoutParams.apply {
                        height = ViewGroup.LayoutParams.WRAP_CONTENT
                    }
                    scaleType = ImageView.ScaleType.FIT_END
                    Glide.with(context)
                        .load(cover.res)
                        .override(SIZE_ORIGINAL)
                        .apply(
                            RequestOptions.bitmapTransform(
                                granularRoundedCorners
                            )
                        )
                        .placeholder(DEFAULT_COVER)
                        .into(this)
                }
            }
            binding.foreground.visibility = if (cover.coverType == NotebookCoverType.CUSTOM) {
                View.INVISIBLE
            } else {
                View.VISIBLE
            }
            binding.delete.setOnClickListener {
                onDeleteClick()
            }
            binding.image.setOnClickListener {
                onClick()
            }
        }
    }
}

fun getFirstCoverPositionWithSameType(position: Int, coverList: List<NotebookCover>): Int {
    for (i in position downTo 0) {
        if (coverList[position].coverType != coverList[i].coverType) {
            return i + 1
        }
    }
    return 0
}
package com.topstack.kilonotes.phone.note

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Dialog
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Bundle
import android.os.SystemClock
import android.util.TypedValue
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.TranslateAnimation
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.bumptech.glide.Glide
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.liaoinstan.springview.widget.SpringView
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.ad.AdHelper
import com.topstack.kilonotes.ad.AdLoadCallback
import com.topstack.kilonotes.ad.AdUnit
import com.topstack.kilonotes.ad.RewardCallback
import com.topstack.kilonotes.base.ad.AdViewModel
import com.topstack.kilonotes.base.component.dialog.AlertDialog
import com.topstack.kilonotes.base.component.fragment.BaseBottomSheetDialogFragment
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.datareporter.DataType
import com.topstack.kilonotes.base.datareporter.datacollection.TemplateDataCollection
import com.topstack.kilonotes.base.db.HandbookDatabase
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.io.PaperManager
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.download.DownloadRepository
import com.topstack.kilonotes.base.fonts.FontDownloadProgressDialog
import com.topstack.kilonotes.base.handbook.model.NO_TEMPLATE_ASSOCIATED_NOTE_ID
import com.topstack.kilonotes.base.handbook.model.Template
import com.topstack.kilonotes.base.handbook.model.TemplateCategory
import com.topstack.kilonotes.base.handbook.model.TemplateCategoryWithList
import com.topstack.kilonotes.base.ktx.safeDismiss
import com.topstack.kilonotes.base.ktx.safeNavigate
import com.topstack.kilonotes.base.ktx.safeShow
import com.topstack.kilonotes.base.note.model.CreatePageType
import com.topstack.kilonotes.base.note.model.InsertPosition
import com.topstack.kilonotes.base.note.model.TemplateListType
import com.topstack.kilonotes.base.note.viewmodel.*
import com.topstack.kilonotes.base.track.event.AdEvent
import com.topstack.kilonotes.base.track.event.AddPageEvent
import com.topstack.kilonotes.base.track.event.AddTemplateEvent
import com.topstack.kilonotes.base.track.event.AddTemplateEvent.sendTemplateSelectionClickEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.base.vip.viewmodel.HandbookViewModel
import com.topstack.kilonotes.databinding.PhoneBottomSheetAddPageBinding
import com.topstack.kilonotes.infra.foundation.thread.ThreadUtils
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.infra.network.PageResult
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.pad.note.adapter.TemplateStorageNotEnoughAdapter
import com.topstack.kilonotes.phone.component.dialog.LoadingDialog
import com.topstack.kilonotes.phone.component.dialog.PhoneLogoLoadingDialog
import com.topstack.kilonotes.phone.component.view.springview.TemplateFooter
import com.topstack.kilonotes.phone.component.view.springview.TemplateHeader
import com.topstack.kilonotes.phone.note.adapter.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AddPageBottomSheet : BaseBottomSheetDialogFragment(), View.OnClickListener {

    private val addPageViewModel by activityViewModels<AddPageViewModel>()
    private val noteViewModel: NoteViewModel by activityViewModels()
    private val templateViewModel by activityViewModels<TemplateViewModel>()
    private val phoneTemplateViewModel: PhoneTemplateViewModel by activityViewModels()
    private val handbookViewModel: HandbookViewModel by activityViewModels()
    private val fontDownloadViewModel: FontDownloadViewModel by activityViewModels()
    private val adViewModel: AdViewModel by activityViewModels()
    private lateinit var templateOtherLayoutManager: LinearLayoutManager
    private lateinit var templateLayoutManager: StaggeredGridLayoutManager
    private var templateListAdapter: PhoneNoteTemplateAdapter? = null
    private var blankTemplateAdapter: PhoneBlankTemplateAdapter? = null
    private lateinit var addTemplateOtherAdapter: PhoneNoteTemplateOtherAdapter
    private var addPaperAdapter: PhoneNoteAddPaperAdapter? = null
    private var currentListType = if (Preferences.noteAddPageDefaultTab == ADD_PAGE) {
        CreatePageType.PAPER
    } else {
        CreatePageType.TEMPLATE
    }
    private var logoLoadingDialog: PhoneLogoLoadingDialog? = null
    var templateOtherList = listOf<TemplateCategoryWithList>()
    private var insertPosition: InsertPosition?
        get() = addPageViewModel.insertPosition.value
        set(value) {
            addPageViewModel.changeInsertPosition(value!!)
        }

    private lateinit var binding: PhoneBottomSheetAddPageBinding
    private var classificationAdapter: PhoneTemplateClassificationAdapter? = null

    private val templateClassificationListItemDecorations = object : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State,
        ) {
            val position = parent.getChildAdapterPosition(view)
            if (position == 0) {
                outRect.left = resources.getDimension(R.dimen.dp_49).toInt()
            } else {
                outRect.left = resources.getDimension(R.dimen.dp_36).toInt()
            }
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = PhoneBottomSheetAddPageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (AddPageSource.document == null || AddPageSource.paper == null) {
            addPageViewModel.hideAddPageView()
            return
        }
        templateViewModel.resetDownloadState()
        Glide.with(binding.root)
            .asGif()
            .load(R.drawable.template_download)
            .into(binding.templateDownloadDialog.downloadImg)

        initTemplateClassificationSelectList()
        initTemplateList()
        initPaperListView()
        initClick()
        initListener()
        initAddPageSelectView()

        binding.templateDownloadDialog.downloadCancel.setOnClickListener {
            AddTemplateEvent.sendAddtemplateSlightlythumbnailCancel(AddTemplateEvent.EDIT_PAGE)
            templateViewModel.cancelCurrentDownload()
        }
        addPageViewModel.changeInsertPosition(InsertPosition.NEXT)
        binding.next.isSelected = true
        insertPosition?.let {
            updatePosition(it)
        }
        if (AddPageViewModel.source == NoteTemplateHelper.SOURCE.CATALOG.value) {
            initCatalogView()
        }
    }

    private fun initCatalogView() {
        val constraintSet = ConstraintSet()
        constraintSet.clone(context, R.layout.phone_bottom_sheet_add_page)
        constraintSet.clear(R.id.template_list_content, ConstraintSet.TOP)
        constraintSet.clear(R.id.paper_list_view, ConstraintSet.TOP)
        constraintSet.connect(
            R.id.template_list_content,
            ConstraintSet.TOP,
            R.id.add_page_select, ConstraintSet.BOTTOM,
            -resources.getDimensionPixelSize(R.dimen.dp_60)
        )
        constraintSet.connect(
            R.id.paper_list_view,
            ConstraintSet.TOP,
            R.id.add_page_select,
            ConstraintSet.BOTTOM,
            -resources.getDimensionPixelSize(R.dimen.dp_60)
        )
        constraintSet.applyTo(binding.root)
        addPageViewModel.changeInsertPosition(InsertPosition.LAST)
        binding.navigation.isVisible = false
    }

    private fun initTemplateList() {
        binding.springView.header = TemplateHeader()
        binding.springView.footer = TemplateFooter()
        binding.springView.setListener(object : SpringView.OnFreshListener {
            override fun onRefresh() {
                when (phoneTemplateViewModel.editCurrentTemplateType.value) {
                    TemplateListType.RECOMMEND -> {
                        phoneTemplateViewModel.refreshTemplateRecommendPage(true) {
                            binding.springView.onFinishFreshAndLoadDelay(NoteTemplateHelper.DELAY_FINISH_TIME)
                        }
                    }

                    TemplateListType.NEW -> {
                        phoneTemplateViewModel.refreshTemplateNewPage(true) {
                            binding.springView.onFinishFreshAndLoadDelay(NoteTemplateHelper.DELAY_FINISH_TIME)
                        }
                    }

                    else -> {}
                }
            }

            override fun onLoadmore() {
                if (isNetworkAvailable()) {
                    when (phoneTemplateViewModel.editCurrentTemplateType.value) {
                        TemplateListType.RECOMMEND -> {
                            phoneTemplateViewModel.loadTemplateRecommendNextPage {
                                binding.springView.onFinishFreshAndLoad()
                            }
                        }

                        TemplateListType.NEW -> {
                            phoneTemplateViewModel.loadTemplateNewNextPage {
                                binding.springView.onFinishFreshAndLoad()
                            }
                        }

                        else -> {}
                    }
                } else {
                    binding.springView.onFinishFreshAndLoad()
                }
            }

        })


        templateLayoutManager = StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL)
        binding.templateListView.overScrollRecyclerView.layoutManager = templateLayoutManager
        setTemplateAdapter()
        templateOtherLayoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        binding.templateOtherListView.overScrollRecyclerView.layoutManager =
            templateOtherLayoutManager
        addTemplateOtherAdapter =
            PhoneNoteTemplateOtherAdapter(
                requireContext(),
                lifecycleScope
            ).apply {
                downloadedHandbookCallback = { notebookId ->
                    handbookViewModel.getHandbookIsFree(notebookId)
                }
                addTemplateCallback = { template, _ ->
                    sendTemplateSelectionClickEvent(AddPageViewModel.source)
                    templateOtherAddClick(template)
                }
                downTemplateCallback = { template ->
                    sendTemplateSelectionClickEvent(AddPageViewModel.source)
                    templateOtherDownloadClick(template)
                }
                buyTemplateCallback = {
                    templateBuyClick(it)
                }
            }

        binding.templateOtherListView.overScrollRecyclerView.adapter = addTemplateOtherAdapter

        addPageViewModel.insertPosition.observe(viewLifecycleOwner) {
            if (it == InsertPosition.REPLACE) {
                NoteTemplateHelper.isTemplateMarkVisible = drawsIsNotEmpty()
            } else {
                NoteTemplateHelper.isTemplateMarkVisible = false
            }
            templateListAdapter?.notifyDataSetChanged()
            addTemplateOtherAdapter?.notifyDataSetChanged()
        }

        templateViewModel.currentDownloadState.observe(viewLifecycleOwner) { downloadState ->
            when (downloadState) {
                BaseTemplateViewModel.DownloadState.DOWNLOAD_INITIAL -> {
                    binding.templateDownloadDialog.root.visibility = View.GONE
                }

                BaseTemplateViewModel.DownloadState.DOWNLOADING -> {
                    binding.templateDownloadDialog.root.visibility = View.VISIBLE
                }

                BaseTemplateViewModel.DownloadState.DOWNLOADED -> {
                    lifecycleScope.launch(Dispatchers.Main) {
                        templateViewModel.currentDownloadTemplate?.let {
                            val document = BaseTemplateViewModel.getDocument(it.file)
                            if (document != null) {
                                addTemplate(it, document)
                            }
                        }
                        templateViewModel.resetDownloadState()
                    }
                }

                BaseTemplateViewModel.DownloadState.DOWNLOAD_ERROR -> {
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            getString(R.string.network_connection_timed_out)
                        )
                    }
                    templateViewModel.resetDownloadState()
                }

                BaseTemplateViewModel.DownloadState.DOWNLOAD_CANCELLED -> {
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            getString(R.string.template_download_cancel)
                        )
                    }
                    templateViewModel.resetDownloadState()
                }

                else -> {}
            }
        }

        fontDownloadViewModel.currentDownloadState.observe(viewLifecycleOwner) { downloadState ->
            when (downloadState) {
                is FontDownloadViewModel.DownloadCompleted -> {
                    getFontDownloadDialog()?.dismiss()
                    fontDownloadViewModel.removeNetworkChangeListener()
                    fontDownloadViewModel.resetDownloadState()
                    fontDownloadViewModel.document?.let { document ->
                        fontDownloadViewModel.template?.let { template ->
                            addTemplate(template, document)
                        }
                    }
                }

                is FontDownloadViewModel.DownloadNoNet -> {
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            R.string.toast_no_internet
                        )
                    }
                }

                is FontDownloadViewModel.DownloadLostNet -> {
                    getFontDownloadDialog()?.dismiss()
                    fontDownloadViewModel.cancelAllDownload()
                    fontDownloadViewModel.removeNetworkChangeListener()
                    fontDownloadViewModel.resetDownloadState()
                    context?.let {
                        ToastUtils.topCenter(
                            it,
                            R.string.template_download_fail
                        )
                    }
                }

                is FontDownloadViewModel.DownloadError -> {
                    getFontDownloadDialog()?.dismiss()
                    fontDownloadViewModel.cancelAllDownload()
                    fontDownloadViewModel.removeNetworkChangeListener()
                    fontDownloadViewModel.resetDownloadState()
                    val alertDialog = AlertDialog.Builder()
                        .setMsg(resources.getString(R.string.template_resource_download_fail))
                        .setNegativeBtn(resources.getString(R.string.cancel)) {
                        }
                        .setPositiveBtn(resources.getString(R.string.retry)) {
                            fontDownloadViewModel.document?.let { document ->
                                lifecycleScope.launch(Dispatchers.IO) {
                                    fontDownloadViewModel.checkFontResources(document)
                                }
                            }
                        }
                        .build()
                    alertDialog.show(parentFragmentManager, null)
                }

                is FontDownloadViewModel.DownloadInitial -> {

                }

                is FontDownloadViewModel.DownloadProgress -> {
                    getFontDownloadDialog()?.updateDialogContent(
                        downloadState.progress
                    )
                }

                is FontDownloadViewModel.DownloadStart -> {
                    fontDownloadViewModel.addNetworkChangeListener()
                    showFontDownloadDialog()
                }
            }
        }

        phoneTemplateViewModel.showBottomGuide.observe(viewLifecycleOwner) {
            if (it) {
                if (UserManager.isVip()) {
                    phoneTemplateViewModel.changeBottomGuideVisibility(false)
                } else {
                    showGuideBottomSheet()
                }
            } else {
                hideGuideBottomSheet()
            }
        }

        adViewModel.loadingShow.observe(viewLifecycleOwner) { isLoadingShow ->
            if (isLoadingShow) {
                showAdLoadingDialog()
            } else {
                hideAdLoadingDialog()
            }
        }
        adViewModel.retryShow.observe(viewLifecycleOwner) { isRetryShow ->
            if (isRetryShow) {
                phoneTemplateViewModel.editCurrentTemplate?.let {
                    showAdLoadErrorDialog(it)
                }
            } else {
                hideAdLoadErrorDialog()
            }
        }
    }

    fun isNetworkAvailable(): Boolean {
        return if (NetworkUtils.isNetworkAvailable()) {
            true
        } else {
            ToastUtils.topCenter(requireContext(), R.string.toast_no_internet)
            false
        }
    }

    private fun initAddPageSelectView() {
        binding.selectedFlag.isVisible = false
        binding.addPageSelect.post {
            if (!isAdded || activity?.isFinishing != false) return@post
            val templateListContent = binding.templateListContent
            val paperList = binding.paperListView
            val addTemplateX = (binding.addTemplate.width / 2).toFloat()
            val addPageX = (binding.addPage.width / 2).toFloat()
            when (currentListType) {
                CreatePageType.TEMPLATE -> {
                    AddTemplateEvent.sendTemplateSelectionShow()
                    changeStyle(binding.addTemplate, binding.addPage)
                    templateListContent.visibility = View.VISIBLE
                    paperList.visibility = View.GONE
                    binding.selectedFlag.isVisible = true
                }

                CreatePageType.PAPER -> {
                    AddPageEvent.sendPaperSelectionShow()
                    binding.selectedFlag.translationX = addTemplateX + addPageX
                    changeStyle(binding.addPage, binding.addTemplate)
                    templateListContent.visibility = View.GONE
                    paperList.visibility = View.VISIBLE
                    binding.selectedFlag.isVisible = true
                }

                else -> {}
            }
            if (phoneTemplateViewModel.editCurrentTemplateType.value == null) {
                phoneTemplateViewModel.setEditCurrentTemplateType(TemplateListType.RECOMMEND)
            }
        }
    }

    private var adLoadingDialog: LoadingDialog? = null
    private fun showAdLoadingDialog() {
        val dialog = parentFragmentManager.findFragmentByTag(LoadingDialog.TAG)
        if (dialog != null && dialog is LoadingDialog) {
            adLoadingDialog = dialog
            return
        }
        if (adLoadingDialog == null) {
            adLoadingDialog = LoadingDialog()
            adLoadingDialog?.safeShow(parentFragmentManager, LoadingDialog.TAG)
        }
    }

    private fun hideAdLoadingDialog() {
        adLoadingDialog?.safeDismiss(parentFragmentManager)
        adLoadingDialog = null
    }

    private fun setTemplateAdapter(listType: TemplateListType? = null) {
        blankTemplateAdapter = PhoneBlankTemplateAdapter(requireContext()).apply {
            templateClickAction = {
                val paper = PaperManager.getPaperWithColor(
                    Paper.COLOR_TYPE_WHITE,
                    Paper.STYLE_TYPE_PHONE_BLANK
                )
                val pageVersion =
                    AddPageSource.document!!.pages[AddPageSource.document!!.viewingPageIndex].version
                paperAddClick(paper, pageVersion)
                Preferences.noteAddPageDefaultTab = 0
                if (AddPageViewModel.source == NoteTemplateHelper.SOURCE.CATALOG.value) {
                    AddTemplateEvent.sendAddBlankTemplate(AddTemplateEvent.CATALOG_PAGE)
                } else {
                    AddTemplateEvent.sendAddBlankTemplate(AddTemplateEvent.EDIT_PAGE)
                }
            }
        }
        templateListAdapter = PhoneNoteTemplateAdapter(
            requireContext(),
            lifecycleScope
        ) { template ->
            sendTemplateSelectionClickEvent(AddPageViewModel.source)
            lifecycleScope.launch(Dispatchers.IO) {
                val templateState = NoteTemplateHelper.getTemplateState(
                    template,
                    templateViewModel,
                    handbookViewModel
                )

                withContext(Dispatchers.Main) {
                    when (templateState) {
                        TemplateState.DOWNLOADED -> {
                            templateAddClick(template)
                        }

                        else -> {
                            templateDownloadClick(templateState, template)
                        }
                    }
                }
            }
        }.apply {
            setTemplateGetWidthAndHeightAction {
                phoneTemplateViewModel.onOldLocalTemplateWidthAndHeightUpdate(it)
            }
        }
        if (listType == TemplateListType.RECOMMEND) {
            binding.templateListView.overScrollRecyclerView.adapter =
                ConcatAdapter(blankTemplateAdapter, templateListAdapter)
        } else {
            binding.templateListView.overScrollRecyclerView.adapter = templateListAdapter
        }
        binding.templateListView.post {
            templateListAdapter?.templateCategory2ListMap?.let {
                it.forEach { (categoryId, templateList) ->
                    TemplateDataCollection.templateListDataOfCategoryId(
                        templateList.toList(),
                        categoryId,
                        DataType.NOTEBOOKS_TEMPLATE_VIEW
                    )
                }
                it.clear()
            }
        }
        val templateListScrollListener: RecyclerView.OnScrollListener = object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val hasEnd = newState == RecyclerView.SCROLL_STATE_IDLE
                if (hasEnd) {
                    templateListAdapter?.templateCategory2ListMap?.let {
                        it.forEach { (categoryId, templateList) ->
                            TemplateDataCollection.templateListDataOfCategoryId(
                                templateList.toList(),
                                categoryId,
                                DataType.NOTEBOOKS_TEMPLATE_VIEW
                            )
                        }
                        it.clear()
                    }
                }
            }
        }
        binding.templateListView.overScrollRecyclerView.addOnScrollListener(
            templateListScrollListener
        )
    }

    private fun initTemplateClassificationSelectList() {
        /**
         * 模板分类列表
         */
        binding.templateClassificationSelectList.addItemDecoration(
            templateClassificationListItemDecorations
        )
        binding.templateClassificationSelectList.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        classificationAdapter =
            PhoneTemplateClassificationAdapter(
                requireContext(),
                NoteTemplateHelper.classificationList
            ) {
                phoneTemplateViewModel.setEditCurrentTemplateType(
                    NoteTemplateHelper.getTemplateTypeByPosition(
                        it
                    )
                )
            }
        binding.templateClassificationSelectList.adapter = classificationAdapter
    }

    private fun addTemplate(template: Template, document: Document) {
        if (addPageViewModel.insertPosition.value!! == InsertPosition.REPLACE) {
            AlertDialog.Builder()
                .setTitle(resources.getString(R.string.page_replace_title))
                .setMsg(resources.getString(R.string.page_replace_tip))
                .setPositiveBtn(resources.getString(R.string.add_page_replace)) {
                    lifecycleScope.launch(Dispatchers.IO) {
                        template.lastUseTime = SystemClock.elapsedRealtime()
                        HandbookDatabase.getDatabase().templateDao()
                            .insertTemplate(listOf(template))
                    }
                    removeUsedTemplate(template)
                    updateTemplateRecentlyUsedPages(template)
                    addPageViewModel.addTemplate(
                        template,
                        addPageViewModel.insertPosition.value!!,
                        document,
                        0
                    )
                    sentTemplateUseEvent(template.id)
                    addPageViewModel.hideAddPageView()
                }
                .setNegativeBtnColor(AppUtils.getColor(R.color.text_secondary))
                .setNegativeBtn(resources.getString(R.string.cancel)) { }
                .build().show(childFragmentManager, "")
        } else {
            lifecycleScope.launch(Dispatchers.IO) {
                template.lastUseTime = SystemClock.elapsedRealtime()
                HandbookDatabase.getDatabase().templateDao()
                    .insertTemplate(listOf(template))
            }
            removeUsedTemplate(template)
            if (AddPageViewModel.source == NoteTemplateHelper.SOURCE.CATALOG.value) {
                AddPageSource.file = document
                AddPageSource.addType = ADD_TEMPLATE
                AddPageSource.template = template
                addPageViewModel.changeCatalogViewState(false)
            } else {
                updateTemplateRecentlyUsedPages(template)
                addPageViewModel.addTemplate(
                    template,
                    addPageViewModel.insertPosition.value!!,
                    document,
                    0
                )
            }
            sentTemplateUseEvent(template.id)
            addPageViewModel.hideAddPageView()
        }
    }

    private fun updateTemplateRecentlyUsedPages(template: Template) {
        if (phoneTemplateViewModel.templateRecentlyUsedPages.value.isNullOrEmpty()) {
            phoneTemplateViewModel.setRecentlyUsedTemplatePage(
                mutableListOf(
                    PageResult(
                        data = mutableListOf(
                            template
                        )
                    )
                )
            )
        } else {
            val recentlyUsedPagesTemplates =
                phoneTemplateViewModel.templateRecentlyUsedPages.value!!.first().data
            phoneTemplateViewModel.templateRecentlyUsedPages.value!!.first().data =
                recentlyUsedPagesTemplates + template
        }
    }

    private fun removeUsedTemplate(template: Template) {
        phoneTemplateViewModel.removeRecentlyUsedTemplate(template)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        // 宽充满屏幕的宽
        dialog.behavior.maxWidth = DimensionUtil.getScreenDimensions(requireContext()).widthPixels
        return dialog
    }

    private fun initClick() {
        binding.replace.setOnClickListener(this)
        binding.pre.setOnClickListener(this)
        binding.next.setOnClickListener(this)
        binding.last.setOnClickListener(this)
        binding.cancel.setOnClickListener(this)
        binding.addPage.setOnClickListener(this)
        binding.addTemplate.setOnClickListener(this)
        binding.colorWhite.setOnClickListener(this)
        binding.colorYellow.setOnClickListener(this)
        binding.colorBlack.setOnClickListener(this)
        binding.colorGreen.setOnClickListener(this)
        binding.colorPurple.setOnClickListener(this)
        binding.colorBlue.setOnClickListener(this)

        dialog?.let {
            it.setOnKeyListener { dialog, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    addPageViewModel.hideAddPageView()
                    true
                } else {
                    false
                }
            }
        }
    }

    private val templatePagesObserve =
        Observer<MutableList<PageResult<Template>>> { pageTemplates ->
            if (phoneTemplateViewModel.editCurrentTemplateType.value == TemplateListType.RECOMMEND) {
                recommendTemplateLoad(pageTemplates)
            } else {
                refreshTemplateList(pageTemplates)
            }
        }

    private fun recommendTemplateLoad(pageTemplates: MutableList<PageResult<Template>>) {
        if (pageTemplates.last().isLastPage()) {
            refreshTemplateList(pageTemplates)
        } else {
            var templateCount = 0
            pageTemplates.forEach {
                templateCount += it.data.size
            }
            if (templateCount < RECOMMEND_TEMPLATE_REFRESH_CRITICAL) {
                phoneTemplateViewModel.loadTemplateRecommendNextPage()
            } else {
                refreshTemplateList(pageTemplates)
            }
        }
    }


    private fun refreshTemplateList(pageTemplates: MutableList<PageResult<Template>>) {
        templateListAdapter?.updateTemplatePages(pageTemplates)
        val lastLoadedPage = pageTemplates.lastOrNull()
        (binding.springView.footer as TemplateFooter).haveMoreData(lastLoadedPage?.isLastPage() != true)
    }

    private fun switchObserveSource(listType: TemplateListType) {
        binding.springView.onFinishFreshAndLoad()
        phoneTemplateViewModel.templateRecommendPages.removeObserver(templatePagesObserve)
        phoneTemplateViewModel.templateNewPages.removeObserver(templatePagesObserve)
        phoneTemplateViewModel.templateRecentlyUsedPages.removeObserver(templatePagesObserve)
        when (listType) {
            TemplateListType.RECOMMEND -> {
                phoneTemplateViewModel.templateRecommendPages.observe(
                    viewLifecycleOwner,
                    templatePagesObserve
                )
            }

            TemplateListType.NEW -> {
                phoneTemplateViewModel.templateNewPages.observe(
                    viewLifecycleOwner,
                    templatePagesObserve
                )
            }

            TemplateListType.USED -> {
                phoneTemplateViewModel.templateRecentlyUsedPages.observe(
                    viewLifecycleOwner,
                    templatePagesObserve
                )
            }

            TemplateListType.OTHER -> {
                binding.mineEmptyDataBg.visibility = View.GONE
                binding.templateListView.visibility = View.GONE
                binding.templateOtherListView.visibility = View.GONE
                if (this.templateOtherList.isEmpty()) {
                    if (!NetworkUtils.isNetworkAvailable()) {
                        binding.emptyDataTxt.isVisible = true
                        binding.emptyDataTxt.setText(R.string.bad_network_tips)
                    } else if (templateViewModel.isStorageNotEnoughToDownload()) {
                        binding.emptyDataTxt.isVisible = true
                        binding.emptyDataTxt.setText(R.string.storage_not_enough_to_download_resource)
                    } else {
                        binding.emptyDataTxt.isVisible = false
                    }
                    binding.emptyDataView.setImageResource(R.drawable.note_material_reload)
                    binding.mineEmptyDataBg.visibility = View.VISIBLE
                } else {
                    binding.templateOtherListView.visibility = View.VISIBLE
                }
            }
        }
    }

    private fun initListener() {
        templateViewModel.templateCategoryList.observe(viewLifecycleOwner) { templateCategoryList ->
            templateCategoryList.forEach { templateCategoryWithList ->
                templateCategoryWithList.templateList =
                    templateCategoryWithList.templateList.filter { template -> template.device == NoteTemplateHelper.PAD_DEVICE }
            }
            templateOtherList =
                templateCategoryList.filter { templateCategoryWithList -> templateCategoryWithList.templateList.isNotEmpty() }
            addTemplateOtherAdapter.updateAll(templateOtherList)
        }

        //恢复其他列表位置
        addPageViewModel.restoreTemplateState.value?.let { pair ->
            binding.templateOtherListView.post {
                if (!isAdded || activity?.isFinishing != false) return@post
                if (pair.first < templateOtherLayoutManager.itemCount) {
                    templateOtherLayoutManager.scrollToPositionWithOffset(pair.first, pair.second)
                }
                addPageViewModel.clearTemplateState()
            }
        }

        //恢复推荐/我的列表位置
        phoneTemplateViewModel.restoreTemplateState.value?.let { pair ->
            binding.templateListView.post {
                if (!isAdded || activity?.isFinishing != false) return@post
                if (pair.first < templateLayoutManager.itemCount) {
                    templateLayoutManager.scrollToPositionWithOffset(pair.first, pair.second)
                }
                phoneTemplateViewModel.clearTemplateState()
            }
        }


        templateViewModel.remoteNotDownload.observe(viewLifecycleOwner) { remoteNotDownload ->
            if (remoteNotDownload && ::addTemplateOtherAdapter.isInitialized) {
                binding.templateListView.overScrollRecyclerView.adapter = ConcatAdapter(
                    addTemplateOtherAdapter,
                    TemplateStorageNotEnoughAdapter()
                )
            }
        }

        phoneTemplateViewModel.editCurrentTemplateType.observe(viewLifecycleOwner) {
            binding.springView.isEnableHeader =
                it == TemplateListType.RECOMMEND || it == TemplateListType.NEW
            binding.springView.isEnableFooter =
                it == TemplateListType.RECOMMEND || it == TemplateListType.NEW
            classificationAdapter?.refresh(NoteTemplateHelper.getPositionByTemplateType(it))
            switchList(it)
        }

        phoneTemplateViewModel.showLoading.observe(viewLifecycleOwner) { show ->
            if (show) {
                showLoadingDialog()
            } else {
                hideLoadingDialog()
                onLoadingFinish()
            }
        }
    }

    private fun getFontDownloadDialog(): FontDownloadProgressDialog? {
        val fragmentManager = activity?.supportFragmentManager ?: return null
        val dialog = fragmentManager.findFragmentByTag(FontDownloadProgressDialog.TAG)
        return if (dialog is FontDownloadProgressDialog) {
            dialog
        } else {
            null
        }
    }

    private fun showFontDownloadDialog(): FontDownloadProgressDialog? {
        val fragmentManager = activity?.supportFragmentManager ?: return null
        val dialog = fragmentManager.findFragmentByTag(FontDownloadProgressDialog.TAG)
        if (dialog is FontDownloadProgressDialog && dialog.isVisible) return null
        return FontDownloadProgressDialog().apply {
            setOnCloseListener {
                fontDownloadViewModel.cancelAllDownload()
                fontDownloadViewModel.removeNetworkChangeListener()
                fontDownloadViewModel.resetDownloadState()
            }
            show(fragmentManager, FontDownloadProgressDialog.TAG)
        }
    }

    private fun initPaperListView() {
        when (AddPageSource.paperColor) {
            Paper.COLOR_TYPE_WHITE -> binding.colorWhite.isSelected = true
            Paper.COLOR_TYPE_YELLOW -> binding.colorYellow.isSelected = true
            Paper.COLOR_TYPE_BLACK -> binding.colorBlack.isSelected = true
            Paper.COLOR_TYPE_GREEN -> binding.colorGreen.isSelected = true
            Paper.COLOR_TYPE_PURPLE -> binding.colorPurple.isSelected = true
            Paper.COLOR_TYPE_BLUE -> binding.colorBlue.isSelected = true
        }
        binding.paperList.overScrollRecyclerView.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        addPaperAdapter = PhoneNoteAddPaperAdapter(
            requireContext(),
            AddPageSource.document!!,
            getFinallyPaperList(),
            AddPageSource.document!!.viewingPageIndex
        ) { paper, pageVersion ->
            paperAddClick(paper, pageVersion)
        }
        binding.paperList.overScrollRecyclerView.adapter = addPaperAdapter
    }

    private fun changeStyle(selectedView: TextView, unselectView: TextView) {
        val selectedTypeface = Typeface.defaultFromStyle(Typeface.BOLD)
        val unselectTypeface = Typeface.defaultFromStyle(Typeface.NORMAL)
        val selectedColor = resources.getColor(R.color.text_secondary, null)
        val unselectColor = resources.getColor(R.color.text_color_666666, null)
        val selectedTextSize = resources.getDimensionPixelSize(R.dimen.sp_48).toFloat()
        val unselectTextSize = resources.getDimensionPixelSize(R.dimen.sp_42).toFloat()
        selectedView.apply {
            typeface = selectedTypeface
            setTextColor(selectedColor)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, selectedTextSize)
        }
        unselectView.apply {
            typeface = unselectTypeface
            setTextColor(unselectColor)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, unselectTextSize)
        }
    }

    private fun updatePosition(position: InsertPosition) {
        if (insertPosition != position) {
            insertPosition = position
            binding.replace.isSelected = false
            binding.pre.isSelected = false
            binding.next.isSelected = false
            binding.last.isSelected = false
            when (position) {
                InsertPosition.REPLACE -> binding.replace.isSelected = true
                InsertPosition.NEXT -> binding.next.isSelected = true
                InsertPosition.LAST -> binding.last.isSelected = true
                InsertPosition.PREVIOUS -> binding.pre.isSelected = true
            }
        }
    }

    private fun onLoadingFinish() {
        binding.mineEmptyDataBg.visibility = View.GONE
        binding.templateListView.visibility = View.GONE
        binding.templateOtherListView.visibility = View.GONE
        when (phoneTemplateViewModel.editCurrentTemplateType.value) {
            TemplateListType.RECOMMEND -> {
                binding.templateListView.visibility = View.VISIBLE
            }

            TemplateListType.NEW -> {
                val templatePageList = phoneTemplateViewModel.templateNewPages.value
                val isPageEmpty =
                    templatePageList.isNullOrEmpty() || (templatePageList.size == 1 && templatePageList.first().data.isEmpty())
                if (isPageEmpty) {
                    binding.emptyDataTxt.isVisible = true
                    if (!NetworkUtils.isNetworkAvailable()) {
                        binding.emptyDataView.setImageResource(R.drawable.note_material_reload)
                        binding.emptyDataTxt.setText(R.string.bad_network_tips)
                    } else if (templateViewModel.isStorageNotEnoughToDownload()) {
                        binding.emptyDataView.setImageResource(R.drawable.phone_paper_cut_empty_tips)
                        binding.emptyDataTxt.setText(R.string.storage_not_enough_to_download_resource)
                    } else {
                        binding.emptyDataView.setImageResource(R.drawable.phone_paper_cut_empty_tips)
                        binding.emptyDataTxt.setText(R.string.template_new_empty_data)
                    }
                    binding.mineEmptyDataBg.visibility = View.VISIBLE

                } else {
                    binding.templateListView.visibility = View.VISIBLE
                }
            }

            TemplateListType.USED -> {
                val templatePageList = phoneTemplateViewModel.templateRecentlyUsedPages.value
                val isPageEmpty =
                    templatePageList.isNullOrEmpty() || (templatePageList.size == 1 && templatePageList.first().data.isEmpty())
                if (isPageEmpty) {
                    binding.emptyDataTxt.isVisible = true
                    binding.emptyDataTxt.setText(R.string.template_mine_empty_tips)
                    binding.emptyDataView.setImageResource(R.drawable.phone_template_category_use)
                    binding.mineEmptyDataBg.visibility = View.VISIBLE
                } else {
                    binding.templateListView.visibility = View.VISIBLE
                }

            }

            else -> {}
        }
    }

    override fun onClick(v: View?) {
        if (v == null) return
        val duration = 200
        when (v) {
            binding.replace -> {
                updatePosition(InsertPosition.REPLACE)
                binding.navigation.transitionToState(R.id.to_replace, duration)
            }

            binding.pre -> {
                updatePosition(InsertPosition.PREVIOUS)
                binding.navigation.transitionToState(R.id.to_pre, duration)
            }

            binding.next -> {
                updatePosition(InsertPosition.NEXT)
                binding.navigation.transitionToState(R.id.to_next, duration)
            }

            binding.last -> {
                updatePosition(InsertPosition.LAST)
                binding.navigation.transitionToState(R.id.to_last, duration)
            }

            binding.cancel -> {
                phoneTemplateViewModel.editCurrentTemplate = null
                addPageViewModel.hideAddPageView()
            }

            binding.colorWhite -> {
                AddPageSource.paperColor = Paper.COLOR_TYPE_WHITE
                updateColorStatus(v)
            }

            binding.colorBlack -> {
                AddPageSource.paperColor = Paper.COLOR_TYPE_BLACK
                updateColorStatus(v)
            }

            binding.colorYellow -> {
                AddPageSource.paperColor = Paper.COLOR_TYPE_YELLOW
                updateColorStatus(v)
            }

            binding.colorGreen -> {
                AddPageSource.paperColor = Paper.COLOR_TYPE_GREEN
                updateColorStatus(v)
            }

            binding.colorPurple -> {
                AddPageSource.paperColor = Paper.COLOR_TYPE_PURPLE
                updateColorStatus(v)
            }

            binding.colorBlue -> {
                AddPageSource.paperColor = Paper.COLOR_TYPE_BLUE
                updateColorStatus(v)
            }

            binding.addPage -> {
                if (currentListType != CreatePageType.PAPER) {
                    switchPage(CreatePageType.PAPER)
                    Preferences.noteAddPageDefaultTab = ADD_PAGE
                }
            }

            binding.addTemplate -> {
                if (currentListType != CreatePageType.TEMPLATE) {
                    switchPage(CreatePageType.TEMPLATE)
                    classificationAdapter?.refresh(0)
                    phoneTemplateViewModel.setEditCurrentTemplateType(TemplateListType.RECOMMEND)
                    Preferences.noteAddPageDefaultTab = ADD_TEMPLATE
                }
            }
        }
    }

    private fun switchPage(
        listType: CreatePageType,
        animationDuration: Long = 300L,
    ) {
        /**
         * 切页动画
         */
        val showTemplateAnimation = TranslateAnimation(
            Animation.RELATIVE_TO_PARENT, if (KiloApp.isLayoutRtl) 1F else -1F,
            Animation.RELATIVE_TO_PARENT, 0F,
            Animation.RELATIVE_TO_PARENT, 0F,
            Animation.RELATIVE_TO_PARENT, 0F
        ).apply {
            duration = animationDuration
            interpolator = AnimationUtils
                .loadInterpolator(
                    context,
                    android.R.anim.accelerate_decelerate_interpolator
                )
        }
        val showPaperAnimation = TranslateAnimation(
            Animation.RELATIVE_TO_PARENT, if (KiloApp.isLayoutRtl) -1F else 1F,
            Animation.RELATIVE_TO_PARENT, 0F,
            Animation.RELATIVE_TO_PARENT, 0F,
            Animation.RELATIVE_TO_PARENT, 0F
        ).apply {
            duration = animationDuration
            interpolator = AnimationUtils
                .loadInterpolator(
                    context,
                    android.R.anim.accelerate_decelerate_interpolator
                )
        }

        /**
         * 标志线动画
         */
        val addTemplateX = (binding.addTemplate.width / 2).toFloat()
        val addPageX = (binding.addPage.width / 2).toFloat()
        val addTemplateSelectedAnimator =
            ObjectAnimator.ofFloat(
                binding.selectedFlag,
                View.TRANSLATION_X,
                0F
            ).apply {
                duration = animationDuration
            }

        val addPageSelectedAnimator =
            ObjectAnimator.ofFloat(
                binding.selectedFlag,
                View.TRANSLATION_X,
                if (KiloApp.isLayoutRtl) -(addTemplateX + addPageX) else addTemplateX + addPageX
            ).apply {
                duration = animationDuration
            }

        val templateListContent = binding.templateListContent
        val paperList = binding.paperListView
        templateListContent.visibility = View.GONE
        paperList.visibility = View.GONE
        currentListType = listType
        when (listType) {
            CreatePageType.TEMPLATE -> {
                AddTemplateEvent.sendTemplateSelectionShow()
                templateListContent.startAnimation(showTemplateAnimation)
                addTemplateSelectedAnimator.start()
                changeStyle(binding.addTemplate, binding.addPage)
                templateListContent.visibility = View.VISIBLE
                paperList.visibility = View.GONE
            }

            CreatePageType.PAPER -> {
                AddPageEvent.sendPaperSelectionShow()
                paperList.startAnimation(showPaperAnimation)
                addPageSelectedAnimator.start()
                changeStyle(binding.addPage, binding.addTemplate)
                templateListContent.visibility = View.GONE
                paperList.visibility = View.VISIBLE
            }

            else -> {}
        }
    }

    private fun switchList(listType: TemplateListType) {
        binding.templateListView.overScrollRecyclerView.stopScroll()
        binding.templateOtherListView.overScrollRecyclerView.stopScroll()
        setTemplateAdapter(listType)
        switchObserveSource(listType)
    }

    private fun updateColorStatus(view: View) {
        binding.colorYellow.isSelected = false
        binding.colorWhite.isSelected = false
        binding.colorBlack.isSelected = false
        binding.colorGreen.isSelected = false
        binding.colorPurple.isSelected = false
        binding.colorBlue.isSelected = false
        view.isSelected = true
        updatePaperList()
    }

    private fun updatePaperList() {
        addPaperAdapter?.updateDataList(
            getFinallyPaperList()
        )
    }

    private fun showLoadingDialog() {
        //此处认为logoLoadingDialog != null就是显示状态
        //否则可能导致弹两次的问题
        if (logoLoadingDialog != null || currentListType != CreatePageType.TEMPLATE) return
        val dialog = parentFragmentManager.findFragmentByTag(PhoneLogoLoadingDialog.TAG)
        if (dialog is PhoneLogoLoadingDialog) {
            logoLoadingDialog = dialog
            return
        }
        logoLoadingDialog = PhoneLogoLoadingDialog().apply {
            isCancelable = false
        }
        logoLoadingDialog?.show(parentFragmentManager, PhoneLogoLoadingDialog.TAG)
    }

    private fun hideLoadingDialog() {
        if (logoLoadingDialog != null && logoLoadingDialog!!.isVisible) {
            logoLoadingDialog?.dismiss()
            logoLoadingDialog = null
            return
        }
        val dialog = parentFragmentManager.findFragmentByTag(PhoneLogoLoadingDialog.TAG)
        if (dialog is PhoneLogoLoadingDialog) {
            logoLoadingDialog = null
            dialog.dismiss()
            return
        }
    }


    private fun templateDownloadClick(templateState: TemplateState, template: Template) {
        phoneTemplateViewModel.editCurrentTemplate = template
        if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE && drawsIsNotEmpty()) return
        val hasProduct = NoteTemplateHelper.hasProduct(template, templateViewModel)
        if (UserManager.isVip() || hasProduct) {
            downloadTemplate(template)
        } else {
            when (templateState) {
                TemplateState.FREE -> {
                    downloadTemplate(template)
                }

                TemplateState.ONLY_AD, TemplateState.ONLY_VIP, TemplateState.VIP_OR_AD -> {
                    phoneTemplateViewModel.changeBottomGuideVisibility(true)
                }

                else -> {}
            }
        }
    }

    private fun templateOtherDownloadClick(template: Template) {
        phoneTemplateViewModel.editCurrentTemplate = template
        if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE && drawsIsNotEmpty()) return
        var downloadedHandbook = false
        templateViewModel.getTemplateCategory(template)?.notebookId?.let {
            downloadedHandbook =
                handbookViewModel.getHandbookIsFree(it)
        }
        if (NoteTemplateHelper.templateIsNeedBuy(
                template,
                templateViewModel
            ) && !downloadedHandbook
        ) {
            ToastUtils.topCenter(requireContext(), R.string.note_add_template_need_buy)
        } else {
            downloadTemplate(template)
        }
    }

    private fun templateAddClick(template: Template) {
        phoneTemplateViewModel.editCurrentTemplate = template
        if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE && drawsIsNotEmpty()) return
        preAddTemplate(template)
    }

    private fun paperAddClick(paper: Paper, pageVersion: Int) {
        if (addPageViewModel.insertPosition.value!! == InsertPosition.REPLACE) {
            AlertDialog.Builder()
                .setTitle(resources.getString(R.string.page_replace_title))
                .setMsg(resources.getString(R.string.page_replace_tip))
                .setPositiveBtn(resources.getString(R.string.add_page_replace)) {
                    addPageViewModel.addPage(
                        addPageViewModel.insertPosition.value!!,
                        paper,
                        pageVersion
                    )
                    addPageViewModel.hideAddPageView()
                }
                .setNegativeBtnColor(AppUtils.getColor(R.color.text_secondary))
                .setNegativeBtn(resources.getString(R.string.cancel)) { }
                .build().show(childFragmentManager, "")
        } else {
            if (AddPageViewModel.source == NoteTemplateHelper.SOURCE.CATALOG.value) {
                AddPageSource.paper = paper
                AddPageSource.version = pageVersion
                AddPageSource.addType = ADD_PAGE
                addPageViewModel.changeCatalogViewState(false)
            } else {
                addPageViewModel.addPage(
                    addPageViewModel.insertPosition.value!!,
                    paper,
                    pageVersion
                )
            }
            addPageViewModel.hideAddPageView()
        }
        if (paper.isBuiltin()) {
            AddPageEvent.sendPaperType(NoteTemplateHelper.getPaperType(paper.builtinStyleType))
        }
        addPageViewModel.addPaperColor = AddPageSource.paperColor
    }

    private fun templateOtherAddClick(template: Template) {
        phoneTemplateViewModel.editCurrentTemplate = template
        if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE && drawsIsNotEmpty()) return
        if (NoteTemplateHelper.templateIsNeedBuy(template, templateViewModel)) {
            ToastUtils.topCenter(requireContext(), R.string.note_add_template_need_buy)
        } else {
            preAddTemplate(template)
        }
    }

    private fun templateBuyClick(templateCategory: TemplateCategory) {
        addPageViewModel.setAddPageViewShowState()
        // 记录第一个可视item的偏移量，用于返回时恢复布局
        templateOtherLayoutManager.getChildAt(0)?.let { childView ->
            val position = templateOtherLayoutManager.getPosition(childView)
            val offset = childView.top
            addPageViewModel.setTemplateState(position, offset)
        }
        if (templateCategory.noteId == NO_TEMPLATE_ASSOCIATED_NOTE_ID || handbookViewModel.findHandbookCoverByNoteId(
                templateCategory.noteId
            ) == null
        ) {
            val action = if (AddPageViewModel.source == NoteTemplateHelper.SOURCE.CATALOG.value) {
                PhoneNoteCatalogFragmentDirections.actionPhoneNoteCatalogToVipStore()
            } else {
                NoteEditorFragmentDirections.actionPhoneNoteEditorToVipStore()
            }
            findNavController().navigate(action)
        } else {
            val action = if (AddPageViewModel.source == NoteTemplateHelper.SOURCE.CATALOG.value) {
                PhoneNoteCatalogFragmentDirections.buyTemplate(templateCategory.noteId)
            } else {
                NoteEditorFragmentDirections.buyTemplate(templateCategory.noteId)
            }
            AddTemplateEvent.sendBuyTemplateClick("add_t")
            findNavController().navigate(action)
        }
    }


    private fun preAddTemplate(template: Template) {
        lifecycleScope.launch(Dispatchers.Main) {
            val document = BaseTemplateViewModel.getDocument(template.file)
            fontDownloadViewModel.template = template
            document?.let { file ->
                if (NetworkUtils.isNetworkAvailable()) {
                    lifecycleScope.launch(Dispatchers.IO) {
                        val job = async {
                            fontDownloadViewModel.checkFontResources(file)
                        }
                        val result = job.await()
                        if (result) {
                            withContext(Dispatchers.Main) {
                                addTemplate(template, file)
                            }
                        }
                    }
                } else {
                    addTemplate(template, file)
                }
            }
        }
    }

    private fun downloadTemplate(template: Template) {
        if (NetworkUtils.isNetworkAvailable()) {
            templateViewModel.downloadTemplate(template)
        } else {
            ToastUtils.topCenter(requireContext(), R.string.toast_no_internet)
        }
    }

    private fun sentTemplateUseEvent(templateId: Long) {
        when (phoneTemplateViewModel.editCurrentTemplateType.value) {
            TemplateListType.RECOMMEND -> {
                AddTemplateEvent.sendRecommendTemplateUse(templateId, AddPageViewModel.source)
            }

            TemplateListType.NEW -> {
                AddTemplateEvent.sendNewTemplateUse(templateId, AddPageViewModel.source)
            }

            TemplateListType.USED -> {
                AddTemplateEvent.sendMyTemplateUse(templateId, AddPageViewModel.source)
            }

            TemplateListType.OTHER -> {
                AddTemplateEvent.sendOtherTemplateUse(
                    templateId,
                    AddPageViewModel.source
                )
            }

            else -> {}
        }
    }

    private fun showGuideBottomSheet() {
        lifecycleScope.launch(Dispatchers.IO) {
            val template = phoneTemplateViewModel.editCurrentTemplate ?: return@launch
            val type =
                NoteTemplateHelper.getTemplateState(template, templateViewModel, handbookViewModel)
            withContext(Dispatchers.Main) {
                try {
                    val fragment =
                        childFragmentManager.findFragmentByTag(PhoneNoteTemplateGuideBottomSheet.TAG)
                    if (fragment is PhoneNoteTemplateGuideBottomSheet) {
                        return@withContext
                    }
                    PhoneNoteTemplateGuideBottomSheet().also {
                        it.type = type
                        it.onVipClickedAction = {
                            addPageViewModel.setAddPageViewShowState()
                            // 记录第一个可视item的偏移量，用于返回时恢复布局
                            templateLayoutManager.getChildAt(0)?.let { childView ->
                                val position = templateLayoutManager.getPosition(childView)
                                val offset = childView.top
                                phoneTemplateViewModel.setTemplatePosition(position, offset)
                            }
                            var target: NavDirections?
                            var srcDestination: Int?
                            if (AddPageViewModel.source == NoteTemplateHelper.SOURCE.CATALOG.value) {
                                srcDestination = R.id.note_catalog
                                target =
                                    PhoneNoteCatalogFragmentDirections.actionPhoneNoteCatalogToVipStore()
                            } else {
                                srcDestination = R.id.note_editor
                                target =
                                    NoteEditorFragmentDirections.actionPhoneNoteEditorToVipStore()
                            }
                            safeNavigate(srcDestination, target)
                            it.dismiss()
                            dismiss()
                        }
                        it.onAdClickedAction = {
                            showAd(template)
                        }
                        it.onDismissListener = {
                            phoneTemplateViewModel.changeBottomGuideVisibility(false)
                        }
                    }.safeShow(
                        childFragmentManager,
                        PhoneNoteTemplateGuideBottomSheet.TAG
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                preloadAd()
            }
        }

    }

    private fun hideGuideBottomSheet() {
        try {
            val fragment =
                childFragmentManager.findFragmentByTag(PhoneNoteTemplateGuideBottomSheet.TAG)
            if (fragment is PhoneNoteTemplateGuideBottomSheet) {
                fragment.dismiss()
            } else if (fragment == null) {
                for (childFragment in childFragmentManager.fragments) {
                    if (childFragment is PhoneNoteTemplateGuideBottomSheet) {
                        childFragment.dismiss()
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @SuppressLint("UseRequireInsteadOfGet")
    private fun preloadAd() {
        if (activity == null) {
            return
        }
        AdHelper.loadRewardAd(activity!!, AdUnit.DOMESTIC_REWARD_VIDEO, object : AdLoadCallback {
            override fun onStartLoad() {
                AdEvent.sendMvadStartLoad("template")
            }

            override fun onAdLoaded(sid: String) {
                AdEvent.sendMvadLoadSuccess("template")
            }

            override fun onError(message: String) {
                AdEvent.sendMvadLoadFail("template")
            }

            override fun onAdIsLoading() {

            }

            override fun onAdIsLoaded() {

            }

            override fun onInitFailed() {
                AdEvent.sendMvadLoadFail("template")
            }
        })
    }

    private fun showAd(template: Template, isRetry: Boolean = false) {
        if (!NetworkUtils.isNetworkAvailable()) {
            ToastUtils.topCenter(requireContext(), R.string.toast_no_internet)
            return
        }
        if (adViewModel.isAdAlreadyClick()) return
        adViewModel.onAdButtonClick()
        val parentActivity = activity
        if (parentActivity != null) {
            var verified = false
            AdHelper.showRewardAd(parentActivity, "", object : RewardCallback {
                override fun onRewardVerified() {
                    verified = true
                }

                override fun onClose() {
                    if (verified) {
                        lifecycleScope.launch(Dispatchers.IO) {
                            DownloadRepository.insertDownloadType(
                                template.id,
                                DownloadRepository.template,
                                DownloadRepository.ad
                            )
                        }
                        ThreadUtils.postMainThread {
                            phoneTemplateViewModel.changeBottomGuideVisibility(false)
                            downloadTemplate(template)
                        }
                    }
                }

                override fun onError() {
                    adViewModel.onAdExit()
                    if (isRetry) {
                        AdEvent.sendNoAdsAcquiredRetry("fail", "template")
                    }
                    AdEvent.sendMvadShowFail("template")
                    adViewModel.showRetryDialog()
                }

                override fun onShow() {
                    adViewModel.onAdExit()
                    if (isRetry) {
                        AdEvent.sendNoAdsAcquiredRetry("success", "template")
                    }
                    AdEvent.sendMvadShow("template")
                }

                override fun onClicked() {
                    AdEvent.sendMvadClick("template")
                }

                override fun onInitFailed() {
                    adViewModel.onAdExit()
                }

            }, object : AdLoadCallback {
                override fun onStartLoad() {
                    AdEvent.sendMvadStartLoad("template")
                }

                override fun onAdLoaded(sid: String) {
                    AdEvent.sendMvadLoadSuccess("template")
                }

                override fun onError(message: String) {
                    AdEvent.sendMvadLoadFail("template")
                    adViewModel.onAdExit()
                    if (isRetry) {
                        AdEvent.sendNoAdsAcquiredRetry("fail", "template")
                    }
                    adViewModel.showRetryDialog()
                }

                override fun onAdIsLoading() {

                }

                override fun onAdIsLoaded() {
                }

                override fun onInitFailed() {
                    AdEvent.sendMvadLoadFail("template")
                }

            })
        }
    }

    private var adLoadErrorDialog: AlertDialog? = null

    private fun showAdLoadErrorDialog(template: Template) {
        val fragment =
            parentFragmentManager.findFragmentByTag(PhoneCreateNotePageFragment.TAG_AD_LOAD_ERROR_DIALOG)
        if (fragment is AlertDialog) {
            adLoadErrorDialog = fragment
            return
        }
        adLoadErrorDialog = AlertDialog.Builder()
            .setTitle(getString(R.string.no_ads_title))
            .setMsg(getString(R.string.no_ads_message))
            .setPositiveBtn(getString(R.string.retry)) {
                adViewModel.hideRetryDialog()
                showAd(template)
            }
            .setNegativeBtn(getString(R.string.cancel)) {
                adViewModel.hideRetryDialog()
                AdEvent.sendNoAdsAcquiredCancel("template")
            }
            .setNegativeBtnColor(KiloApp.app.getColor(R.color.text_secondary))
            .build()
        adLoadErrorDialog?.safeShow(
            parentFragmentManager, PhoneCreateNotePageFragment.TAG_AD_LOAD_ERROR_DIALOG
        )
        AdEvent.sendNoAdsAcquiredShow("template")
    }

    private fun hideAdLoadErrorDialog() {
        adLoadErrorDialog?.safeDismiss(parentFragmentManager)
        adLoadErrorDialog = null
    }


    private fun drawsIsNotEmpty(): Boolean {
        val doc = noteViewModel.currentDoc
        val pageIndex = doc?.viewingPageIndex ?: return true
        return doc[pageIndex].draws.isNotEmpty()
    }

    /**
     * 处理纸张列表
     */
    @JvmName("getFinallyPaperList1")
    private fun getFinallyPaperList(): MutableList<PaperManager.paperGroup> {
        val paperList = mutableListOf<PaperManager.paperGroup>()
        val paperPhoneGroup = PaperManager.paperGroup()
        paperPhoneGroup.groupType = Paper.STYLE_TYPE_PHONE
        if (AddPageViewModel.source != NoteTemplateHelper.SOURCE.CATALOG.value) {
            paperPhoneGroup.paperlist.add(AddPageSource.paper)
        }
        paperPhoneGroup.paperlist.addAll(
            PaperManager.getBuiltinPhonePapersByColorType(
                AddPageSource.paperColor
            )
        )
        paperList.add(paperPhoneGroup)
        val paperVerticalGroup = PaperManager.paperGroup()
        paperVerticalGroup.groupType = Paper.STYLE_TYPE_VERTICAL
        paperVerticalGroup.paperlist.addAll(
            PaperManager.getBuiltinPapersByColorType(
                AddPageSource.paperColor
            )
        )
        paperList.add(paperVerticalGroup)
        val paperHorizontalGroup = PaperManager.paperGroup()
        paperHorizontalGroup.groupType = Paper.STYLE_TYPE_HORIZONTAL
        paperHorizontalGroup.paperlist.addAll(
            PaperManager.getBuiltinHorizontalPapersByColorType(
                AddPageSource.paperColor
            )
        )
        paperList.add(paperHorizontalGroup)
        return paperList
    }

    companion object {
        const val TAG: String = "AddPageBottomSheet"
        const val ADD_PAGE = 1
        const val ADD_TEMPLATE = 0
        const val TAG_AD_LOAD_ERROR_DIALOG = "AdLoadErrorDialog"
        const val RECOMMEND_TEMPLATE_REFRESH_CRITICAL = 3
    }

    override fun onDestroyView() {
        super.onDestroyView()
        templateViewModel.cancelCurrentDownload(true)
        getFontDownloadDialog()?.dismiss()
        if (AddPageViewModel.source == NoteTemplateHelper.SOURCE.CATALOG.value) {
            addPageViewModel.resetCatalog()
        } else {
            addPageViewModel.reset()
        }
    }

    override fun onStart() {
        super.onStart()
        view?.let {
            val parent = it.parent
            if (parent is ViewGroup) {
                val params = parent.layoutParams
                params.height = ViewGroup.LayoutParams.MATCH_PARENT
                parent.layoutParams = params
                it.post {
                    // 修复红米note8上内容延伸到界面下方的问题
                    if (parent.top != 0) {
                        parent.setPadding(0, 0, 0, parent.top)
                    }
                }
            }
        }
    }
}
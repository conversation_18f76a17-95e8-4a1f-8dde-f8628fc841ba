package com.topstack.kilonotes.phone.note

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.constraintlayout.helper.widget.Flow
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.topstack.kilonotes.BuildConfig
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.ad.AdHelper
import com.topstack.kilonotes.ad.AdLoadCallback
import com.topstack.kilonotes.ad.AdUnit
import com.topstack.kilonotes.ad.RewardCallback
import com.topstack.kilonotes.base.ad.AdViewModel
import com.topstack.kilonotes.base.component.dialog.AlertDialog
import com.topstack.kilonotes.base.component.dialog.CustomizeChangerDownloadingDialog
import com.topstack.kilonotes.base.component.fragment.NaviEnum
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.component.view.model.BottomDraggableLayoutState
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.customizechanger.CustomizeChangerApkDownloadCallBack
import com.topstack.kilonotes.base.datareporter.DataType
import com.topstack.kilonotes.base.datareporter.datacollection.StickerDataCollection
import com.topstack.kilonotes.base.download.DownloadRepository
import com.topstack.kilonotes.base.flavor.Channel
import com.topstack.kilonotes.base.ktx.outsideArea
import com.topstack.kilonotes.base.ktx.safeDismiss
import com.topstack.kilonotes.base.ktx.safeNavigate
import com.topstack.kilonotes.base.ktx.safeShow
import com.topstack.kilonotes.base.ktx.showStorageNotEnoughToDownloadResourceDialog
import com.topstack.kilonotes.base.material.repository.NoteMaterialRepository
import com.topstack.kilonotes.base.mymaterial.model.CUSTOM_MATERIAL_CATEGORY_ID_DECOUPAGE
import com.topstack.kilonotes.base.mymaterial.model.CUSTOM_MATERIAL_CATEGORY_ID_INSTANT_ALPHA
import com.topstack.kilonotes.base.mymaterial.model.CUSTOM_MATERIAL_CATEGORY_ID_MORE
import com.topstack.kilonotes.base.mymaterial.model.CUSTOM_MATERIAL_CATEGORY_ID_MY_ADDED
import com.topstack.kilonotes.base.mymaterial.model.CustomMaterial
import com.topstack.kilonotes.base.mymaterial.model.CustomMaterialCategory
import com.topstack.kilonotes.base.note.viewmodel.EditorViewModel
import com.topstack.kilonotes.base.note.viewmodel.NoteMaterialViewModel
import com.topstack.kilonotes.base.push.PushManager
import com.topstack.kilonotes.base.track.event.AdEvent
import com.topstack.kilonotes.base.track.event.EditEvent
import com.topstack.kilonotes.base.track.event.MaterialEvent
import com.topstack.kilonotes.base.util.InstallApkUtils
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.databinding.PhoneNoteMaterialBinding
import com.topstack.kilonotes.infra.foundation.thread.ThreadUtils
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.pad.note.model.PaperCutTool
import com.topstack.kilonotes.pay.order.PayHelper
import com.topstack.kilonotes.phone.component.dialog.LoadingDialog
import com.topstack.kilonotes.phone.component.dialog.PhoneAdToStoreDialog
import com.topstack.kilonotes.phone.component.dialog.PhoneCreateFolderDialog
import com.topstack.kilonotes.phone.component.dialog.PhoneCreateMaterialClassifyDialog
import com.topstack.kilonotes.phone.note.adapter.PhoneMaterialCategoryAdapter
import com.topstack.kilonotes.phone.note.adapter.PhonePaperCutToolsAdapter
import com.topstack.kilonotes.push.PushTag
import com.topstack.kilonotes.xuanhu.XuanhuHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.lang.ref.WeakReference

class PhoneNoteMaterialBottomSheet : DialogFragment() {

    companion object {
        const val TAG = "PhoneNoteMaterialBottomSheet"
        const val TAG_AD_LOAD_ERROR_DIALOG = "AdLoadErrorDialog"
        const val TAG_CUSTOMIZE_CHANGER_INSTALL_DIALOG = "CustomizeChangerInstallDialog"
        const val XUANHU_TAG = "xuanhuTag"
        const val PACKAGE_NAME_CUSTOMIZE_CHANGER = "com.mywallpaper.customizechanger"
        const val URI_CUSTOMIZE_CHANGER =
            "https://meiapps.ipolaris-tech.com/mywallpaper/apk/chaomengyadl.apk"
    }

    private val noteMaterialViewModel: NoteMaterialViewModel by activityViewModels()
    private val editorViewModel: EditorViewModel by activityViewModels()
    private val adViewModel: AdViewModel by activityViewModels()

    private lateinit var binding: PhoneNoteMaterialBinding


    private lateinit var pagerCallback: OnPageChangeCallback
    private var materialPagerAdapter: PhoneMaterialPagerAdapter? = null
    private var isDrag = false
    private var isNeedPagerScroll = false
    private var dragBottom = 0
    private var dragRight = 0

    private var materialCategoryAdapter: PhoneMaterialCategoryAdapter? = null
    private var phoneAdToStoreDialog: PhoneAdToStoreDialog? = null

    private val paperCutToolsName = arrayOf(PaperCutTool.CUSTOM_MATERIAL)

    var onCustomMaterialClick: ((CustomMaterial) -> Unit)? = null
    var onCustomMaterialToolClick: (() -> Unit)? = null

    private var customMaterialGuideWindow: PhoneCustomMaterialGuideWindow? = null

    private var customizeChangerDownloadingDialog: CustomizeChangerDownloadingDialog? = null

    private var customizeChangerInstallDialog: AlertDialog? = null


    private val materialListScrollListener: RecyclerView.OnScrollListener =
        object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val hasEnd = newState == RecyclerView.SCROLL_STATE_IDLE
                if (hasEnd && recyclerView.layoutManager != null) {
                    val layoutManager = recyclerView.layoutManager as? LinearLayoutManager
                    layoutManager?.let { linearManager ->
                        val stickerList =
                            noteMaterialViewModel.currentMaterialStickerList.value?.map { it.noteMaterialSticker }
                        val categoryId =
                            noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.noteMaterialCategory?.id
                        if (stickerList != null && categoryId != null) {
                            StickerDataCollection.stickerListData(
                                stickerList,
                                categoryId,
                                linearManager.findFirstVisibleItemPosition(),
                                linearManager.findLastVisibleItemPosition(),
                                DataType.NOTEBOOKS_STICKER_VIEW
                            )
                        }
                    }
                }
            }
        }

    private val paperCutToolsAdapter: PhonePaperCutToolsAdapter by lazy(LazyThreadSafetyMode.NONE) {
        PhonePaperCutToolsAdapter(requireContext(), paperCutToolsName).apply {
            doOnPaperCutTypeSelected {
                isDrag = false
                selectPaperType(it)
                if (it == PaperCutTool.PAPER_CUT_TOOL && UserUsageConfig.isFirstUseMaterialTool) {
                    onCustomMaterialToolClick?.invoke()
                }
                noteMaterialViewModel.selectNoteMaterialSticker(null)
                noteMaterialViewModel.changeCurrentSelectPaperCutCategory(it)
            }
        }
    }

    private var stickerInsertAction: ((filePath: Uri) -> Unit)? = null

    private var materialShowInfoAction: ((filePath: String, view: View) -> Unit)? = null

    private var materialHideInfoAction: (() -> Unit)? = null

    private var userDismissAction: (() -> Unit)? = null

    private var parentActivity: WeakReference<Activity>? = null

    private var createClassifyDialog: PhoneCreateMaterialClassifyDialog? = null
    private var moveBottomSheet: PhoneMoveCustomMaterialBottomSheet? = null
    private var customMoveMaterials: List<CustomMaterial> = mutableListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        binding = PhoneNoteMaterialBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        setStyle(STYLE_NO_TITLE, R.style.noteMaterialBottomSheetStyle)
        return super.onCreateDialog(savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (savedInstanceState == null) {
            PushManager.addTag(requireContext(), PushTag.OPEN_MATERIAL_USER)
        }
        parentActivity = WeakReference(requireActivity())
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
        )
        dialog?.window?.setWindowAnimations(R.style.noteMaterialBottomSheetAnim)
        initView()
        initClickListener()
        initObserve()
        if (Preferences.needReportByXuanhu && Preferences.needReportShowStickerByXuanhu) {
            XuanhuHelper.sendEvent("qb_sticker_show")
            LogHelper.d(XUANHU_TAG, "悬壶：打开素材库")
            Preferences.needReportShowStickerByXuanhu = false
        }
        phoneAdToStoreDialog =
            parentFragmentManager.findFragmentByTag(PhoneAdToStoreDialog.TAG) as? PhoneAdToStoreDialog
    }

    override fun onResume() {
        super.onResume()
        noteMaterialViewModel.refreshNoteMaterialCategoryState()
        if (noteMaterialViewModel.isCurrentCategoryAllowToDownload()) {
            hideGuideBottomSheet()
        }
        binding.materialListPager.registerOnPageChangeCallback(pagerCallback)
    }

    private fun initView() {
        binding.root.apply {
            editorViewModel.currentState.value?.let { currentState ->
                this.currentState = currentState
            }
            setBottomSheetStateListener { currentState ->
                if (currentState != editorViewModel.currentState.value) {
                    editorViewModel.setCurrentState(currentState)
                }
                if (currentState == BottomDraggableLayoutState.AT_BOTTOM) {
                    userDismissAction?.invoke()
                    <EMAIL>()
                }
            }

            setIsCanDragListener {
                return@setIsCanDragListener noteMaterialViewModel.currentStickerCategoryState.value != NoteMaterialViewModel.MaterialCategoryState.DOWNLOADING
            }

            dragListener = {
                refresh()
            }
        }
        binding.sign.isVisible = UserUsageConfig.isFirstUseMaterialTool
        binding.customMaterialType.run {
            adapter = paperCutToolsAdapter
            layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                private val startInterval = context.resources.getDimensionPixelSize(R.dimen.dp_36)
                private val itemInterval = context.resources.getDimensionPixelSize(R.dimen.dp_15)

                override fun getItemOffsets(
                    outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
                ) {
                    if (parent.getChildAdapterPosition(view) == 0) {
                        outRect.left = startInterval
                    }
                    outRect.right = itemInterval
                }
            })
        }
        pagerCallback = object : OnPageChangeCallback() {
            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                if (noteMaterialViewModel.currentSelectPaperCutCategory.value == PaperCutTool.CUSTOM_MATERIAL) {
                    if (state == 0) {
                        materialPagerAdapter?.invokeOnCustomMaterialShow()
                    }
                }
            }

            override fun onPageScrolled(
                position: Int, positionOffset: Float, positionOffsetPixels: Int
            ) {
                isDrag = true
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
            }

            override fun onPageSelected(position: Int) {
                if (!isDrag) return
                super.onPageSelected(position)
                var info = materialPagerAdapter?.getSelectedInfo(position)
                if (info is PaperCutTool) {
                    when (info) {
                        PaperCutTool.CUSTOM_MATERIAL -> {
                            selectPaperType(PaperCutTool.CUSTOM_MATERIAL)
                            noteMaterialViewModel.selectNoteMaterialSticker(null)
                            noteMaterialViewModel.changeCurrentSelectPaperCutCategory(PaperCutTool.CUSTOM_MATERIAL)
                        }

                        else -> {}
                    }
                }
                if (info is NoteMaterialRepository.NoteMaterialStickerCategoryInfo) {
                    noteMaterialViewModel.selectNoteMaterialSticker(info)
                    binding.materialTypeList.overScrollRecyclerView.scrollToPosition(position - paperCutToolsName.size)
                }
            }
        }
    }

    private fun initClickListener() {
        binding.bottomBackground.setOnClickListener(AntiShakeClickListener {
            prepareDownload()
        })
        binding.bottomText.setOnClickListener(AntiShakeClickListener {
            prepareDownload()
        })
        binding.bottomTextTag.setOnClickListener(AntiShakeClickListener {
            prepareDownload()
        })
        binding.reloadText.setOnClickListener {
            if (NetworkUtils.isNetworkAvailable()) {
                noteMaterialViewModel.reload()
            } else {
                showToast(R.string.toast_no_internet)
            }
        }
        binding.cancel.setOnClickListener {
            userDismissAction?.invoke()
            dismiss()
        }
        binding.background.setOnClickListener {
            userDismissAction?.invoke()
            dismiss()
        }
        binding.materialTool.setOnClickListener {
            binding.sign.isVisible = false
            onCustomMaterialToolClick?.invoke()
            editorViewModel.switchMaterialToolViewVisibility()
            if (noteMaterialViewModel.currentSelectPaperCutCategory.value == PaperCutTool.CUSTOM_MATERIAL) {
                materialPagerAdapter?.resetEditMode()
            }
        }
    }

    private fun initObserve() {
        editorViewModel.showMaterialToolView.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                showMaterialToolBottomSheet()
            }
        }

        noteMaterialViewModel.materialCategoryStickerList.observe(viewLifecycleOwner) { materialCategoryStickerList ->
            refreshTypeList(
                materialCategoryStickerList,
                noteMaterialViewModel.customMaterialCategories,
                noteMaterialViewModel.customMaterials
            )
        }
        noteMaterialViewModel.currentSelectMaterialCategoryInfo.observe(viewLifecycleOwner) { currentSelectMaterialCategoryInfo ->
            currentSelectMaterialCategoryInfo?.let {
                noteMaterialViewModel.selectedMaterial = true
                noteMaterialViewModel.changeCurrentSelectPaperCutCategory(null)
                select(it)
            }
        }
        noteMaterialViewModel.currentMaterialStickerList.observe(viewLifecycleOwner) { currentMaterialStickerList ->
            if (noteMaterialViewModel.selectedMaterial) {
                currentMaterialStickerList?.let { refreshStickerList(it) }
            }
        }
        noteMaterialViewModel.currentStickerCategoryState.observe(viewLifecycleOwner) { categoryState ->
            when (categoryState) {
                NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD -> {
                    showBottomDownloadView(DownloadType.FREE)
                }

                NoteMaterialViewModel.MaterialCategoryState.ONLY_AD_DOWNLOAD -> {
                    showBottomDownloadView(DownloadType.AD)
                }

                NoteMaterialViewModel.MaterialCategoryState.DOWNLOADING -> {
                    showBottomDownloadView(DownloadType.DOWNLOADING)
                }

                NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                    showBottomDownloadView(DownloadType.VIP)
                }

                NoteMaterialViewModel.MaterialCategoryState.VIP_OR_AD_DOWNLOAD -> {
                    showBottomDownloadView(DownloadType.VIP_OR_AD)
                }

                NoteMaterialViewModel.MaterialCategoryState.NET_LOST -> {
                    hideBottomView()
                }

                NoteMaterialViewModel.MaterialCategoryState.NO_NEED_DOWNLOAD -> {
                    hideBottomView()
                }

                else -> {
                    hideBottomView()
                    noteMaterialViewModel.changeBottomGuideVisibility(false)
                    noteMaterialViewModel.isNeedShowAdToStoreDialog.value?.let {
                        if (it) {
                            MaterialEvent.sendAdMemberOrientationShow()
                            showAdToStoreDialog()
                        } else {
                            phoneAdToStoreDialog?.dismiss()
                        }
                    }
                }
            }
        }
        noteMaterialViewModel.showMaterialReloadView.observe(viewLifecycleOwner) { showMaterialReloadView ->
            if (showMaterialReloadView) {
                showReloadView()
            } else {
                hideReloadView()
            }
        }
        noteMaterialViewModel.currentSelectPaperCutCategory.observe(viewLifecycleOwner) { currentSelectPaperCutCategory ->
            currentSelectPaperCutCategory?.let {
                if (it == PaperCutTool.CUSTOM_MATERIAL) {
                    noteMaterialViewModel.refreshCustomMaterialListList()
                }
                val position = materialPagerAdapter?.getSelectPaperCutItem(it)
                if (position != null && position != -1 && binding.materialListPager.currentItem != position) {
                    binding.materialListPager.post {
                        if (activity?.isFinishing == true || !this.isAdded) return@post
                        binding.materialListPager.setCurrentItem(position, isNeedPagerScroll)
                        isNeedPagerScroll = true
                    }
                }
                noteMaterialViewModel.selectedMaterial = false
                noteMaterialViewModel.refreshPaperCutStatus()
                selectPaperType(it)
            }
        }
        noteMaterialViewModel.currentCustomMaterialList.observe(viewLifecycleOwner) { currentCustomMaterialList ->
            if (!noteMaterialViewModel.selectedMaterial && noteMaterialViewModel.currentSelectPaperCutCategory.value == PaperCutTool.CUSTOM_MATERIAL) {
                materialPagerAdapter?.refreshCustomStickList(
                    PaperCutTool.CUSTOM_MATERIAL,
                    currentCustomMaterialList
                )
            }
        }
        noteMaterialViewModel.showBottomGuide.observe(viewLifecycleOwner) { showBottomGuide ->
            if (showBottomGuide) {
                if (noteMaterialViewModel.isCurrentCategoryAllowToDownload()) {
                    hideGuideBottomSheet()
                } else {
                    val info = noteMaterialViewModel.currentSelectMaterialCategoryInfo.value
                        ?: return@observe
                    val showAdDownload = info.noteMaterialCategory.isOpenAd
                    showGuideBottomSheet(showAdDownload)
                }
            } else {
                hideGuideBottomSheet()
            }
        }
        adViewModel.loadingShow.observe(viewLifecycleOwner) { isLoadingShow ->
            if (isLoadingShow) {
                showLoadingDialog()
            } else {
                hideLoadingDialog()
            }
        }
        adViewModel.retryShow.observe(viewLifecycleOwner) { isRetryShow ->
            if (isRetryShow) {
                noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.let {
                    val needVip = it.noteMaterialCategory.isVip
                    val openAd = it.noteMaterialCategory.isOpenAd
                    showAdLoadErrorDialog(!needVip && openAd)
                }
            } else {
                hideAdLoadErrorDialog()
            }
        }
        editorViewModel.currentState.observe(viewLifecycleOwner) { currentState ->
            binding.root.setBottomSheetCurrentState(currentState)
        }
        noteMaterialViewModel.showCustomizeChangerDownloadingDialog.observe(viewLifecycleOwner) { isShow ->
            customizeChangerDownloadingDialog?.updateProgressInternal(0f)
            if (isShow) {
                showCustomizeChangerDownloadingDialog()
            } else {
                hideCustomizeChangerDownloadingDialog()
            }
        }

        noteMaterialViewModel.showCustomizeChangerInstallDialog.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                showCustomizeChangerInstallDialog(noteMaterialViewModel.customizeChangerApkPath)
            } else {
                hideCustomizeChangerInstallDialog()
            }
        }
    }

    private fun prepareDownload() {
        val info = noteMaterialViewModel.currentSelectMaterialCategoryInfo.value ?: return
        val state = noteMaterialViewModel.currentStickerCategoryState.value ?: return
        val needVip = info.noteMaterialCategory.isVip
        val openAd = info.noteMaterialCategory.isOpenAd
        EditEvent.sendEditMaterialDownloadClick(
            info.noteMaterialCategory.name, if (needVip && !openAd) {
                "download_pro"
            } else {
                "download"
            }
        )
        if (PayHelper.isSubscriptionSupported()) {
            when (state) {
                NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD -> download()
                NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                    val action = NoteEditorFragmentDirections.actionPhoneNoteEditorToVipStore()
                    safeNavigate(
                        R.id.note_editor, action
                    )
                    dismiss()
                }

                else -> return
            }
        } else {
            when (state) {
                NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD -> download()
                NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD,
                NoteMaterialViewModel.MaterialCategoryState.VIP_OR_AD_DOWNLOAD -> {
                    noteMaterialViewModel.changeBottomGuideVisibility(true)
                }

                NoteMaterialViewModel.MaterialCategoryState.ONLY_AD_DOWNLOAD -> {
                    showAd(true)
                    MaterialEvent.sendEditMaterialDownloadAdClick()
                }

                else -> {}
            }
        }
    }

    private fun download(ad: Boolean = false) {
        noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.let {
            EditEvent.sendEditMaterialDownloadClick(it.noteMaterialCategory.name)
        }
        if (NetworkUtils.isNetworkAvailable()) {
            if (noteMaterialViewModel.isStorageNotEnoughToDownload()) {
                showStorageNotEnoughToDownloadResourceDialog()
            } else {
                noteMaterialViewModel.downloadMaterialSticker(object :
                    NoteMaterialViewModel.StickerItemDownloadCallback {
                    override fun onProgress(
                        stickerInfo: NoteMaterialRepository.NoteMaterialStickerInfo, progress: Float
                    ) {
                        refreshSticker(stickerInfo, progress)
                    }

                    override fun onSuccess(
                        stickerInfo: NoteMaterialRepository.NoteMaterialStickerInfo, file: String
                    ) {
                        refreshSticker(stickerInfo, file)
                    }
                }) { categoryId, name, allDownloaded ->
                    if (ad) {
                        lifecycleScope.launch(Dispatchers.IO) {
                            DownloadRepository.insertDownloadType(
                                categoryId,
                                DownloadRepository.stickerCategory,
                                DownloadRepository.ad
                            )
                        }
                        EditEvent.sendEditMaterialWatchADDownload(allDownloaded)
                    }
                    if (allDownloaded) {
                        showToast(
                            KiloApp.app.resources.getString(
                                R.string.download_success, name
                            )
                        )
                        noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.let {
                            if (Preferences.needReportByXuanhu && it.noteMaterialCategory.isOpenAd) {
                                Preferences.downloadStickerByAdTimes =
                                    Preferences.downloadStickerByAdTimes + 1
                                when (Preferences.downloadStickerByAdTimes) {
                                    1 -> {
                                        XuanhuHelper.sendEvent("qb_sticker_ad_dl1")
                                        LogHelper.d(XUANHU_TAG, "悬壶：通过广告下载过1组贴纸")
                                    }

                                    3 -> {
                                        XuanhuHelper.sendEvent("qb_sticker_ad_dl3")
                                        LogHelper.d(XUANHU_TAG, "悬壶：通过广告下载过3组贴纸")
                                    }

                                    5 -> {
                                        XuanhuHelper.sendEvent("qb_sticker_ad_dl5")
                                        LogHelper.d(XUANHU_TAG, "悬壶：通过广告下载过5组贴纸")
                                    }

                                    else -> {}
                                }
                            }
                            if (Preferences.needReportByXuanhu && Preferences.needReportDownloadStickerByVipOrAdByXuanhu && (it.noteMaterialCategory.isVip && it.noteMaterialCategory.isOpenAd)) {
                                XuanhuHelper.sendEvent("qb_sticke_notfree_dl")
                                Preferences.needReportDownloadStickerByVipOrAdByXuanhu = false
                                LogHelper.d(XUANHU_TAG, "悬壶：下载VIP或广告贴纸")
                            }
                            StickerDataCollection.stickerCategoryData(
                                it.noteMaterialCategory,
                                DataType.NOTEBOOKS_STICKER_CATEGORY_DOWNLOAD
                            )
                        }
                    }
                }
            }
        } else {
            showToast(R.string.toast_no_internet)
        }
    }

    private fun showGuideBottomSheet(showAdDownload: Boolean) {
        try {
            val fragment =
                childFragmentManager.findFragmentByTag(PhoneNoteMaterialGuideBottomSheet.TAG)
            if (fragment is PhoneNoteMaterialGuideBottomSheet) {
                return
            }
            PhoneNoteMaterialGuideBottomSheet().also {
                it.showAdDownload = showAdDownload
                it.onVipClickedAction = {
                    val name =
                        noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.noteMaterialCategory?.name
                    if (name != null) {
                        EditEvent.sendEditMaterialGoToVipClick(name)
                    }
                    safeNavigate(R.id.note_editor,
                        NoteEditorFragmentDirections.actionPhoneNoteEditorToVipStore().apply {
                            source = if (showAdDownload) {
                                NaviEnum.AD_FREE
                            } else {
                                NaviEnum.PAY
                            }
                        })
                    it.dismiss()
                    dismiss()
                }
                it.onAdClickedAction = {
                    showAd(false)
                }
                it.onDismissListener = {
                    noteMaterialViewModel.changeBottomGuideVisibility(false)
                }
            }.safeShow(
                childFragmentManager, PhoneNoteMaterialGuideBottomSheet.TAG
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
        preloadAd()
    }

    private fun hideGuideBottomSheet() {
        try {
            val fragment =
                childFragmentManager.findFragmentByTag(PhoneNoteMaterialGuideBottomSheet.TAG)
            if (fragment is PhoneNoteMaterialGuideBottomSheet) {
                fragment.dismiss()
            } else if (fragment == null) {
                for (childFragment in childFragmentManager.fragments) {
                    if (childFragment is PhoneNoteMaterialGuideBottomSheet) {
                        childFragment.dismiss()
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private var adLoadErrorDialog: AlertDialog? = null

    private fun showAdLoadErrorDialog(isNeedShowAdToStoreDialog: Boolean) {
        val fragment = childFragmentManager.findFragmentByTag(TAG_AD_LOAD_ERROR_DIALOG)
        if (fragment != null && fragment is AlertDialog) {
            adLoadErrorDialog = fragment
            return
        }
        adLoadErrorDialog = AlertDialog.Builder().setTitle(getString(R.string.no_ads_title))
            .setMsg(getString(R.string.no_ads_message)).setPositiveBtn(getString(R.string.retry)) {
                adViewModel.hideRetryDialog()
                showAd(false, isNeedShowAdToStoreDialog)
            }.setNegativeBtn(getString(R.string.cancel)) {
                adViewModel.hideRetryDialog()
                AdEvent.sendNoAdsAcquiredCancel("material")
            }.setNegativeBtnColor(KiloApp.app.getColor(R.color.text_secondary)).build()
        adLoadErrorDialog?.safeShow(childFragmentManager, TAG_AD_LOAD_ERROR_DIALOG)
        AdEvent.sendNoAdsAcquiredShow("material")
    }

    private fun hideAdLoadErrorDialog() {
        adLoadErrorDialog?.safeDismiss(childFragmentManager)
        adLoadErrorDialog = null
    }

    private var loadingDialog: LoadingDialog? = null

    private fun showLoadingDialog() {
        if (loadingDialog == null) {
            loadingDialog = LoadingDialog()
            loadingDialog?.safeShow(childFragmentManager, LoadingDialog.TAG)
        }
    }

    private fun hideLoadingDialog() {
        loadingDialog?.safeDismiss(childFragmentManager)
        loadingDialog = null
    }

    override fun onDestroyView() {
        super.onDestroyView()
        hideLoadingDialog()
        binding.materialListPager.unregisterOnPageChangeCallback(pagerCallback)
    }

    @SuppressLint("UseRequireInsteadOfGet")
    private fun preloadAd() {
        if (activity == null) {
            return
        }
        AdHelper.loadRewardAd(activity!!, AdUnit.DOMESTIC_REWARD_VIDEO, object : AdLoadCallback {
            override fun onStartLoad() {
                AdEvent.sendMvadStartLoad("material")
            }

            override fun onAdLoaded(sid: String) {
                AdEvent.sendMvadLoadSuccess("material")
            }

            override fun onError(message: String) {
                AdEvent.sendMvadLoadFail("material")
            }

            override fun onAdIsLoading() {

            }

            override fun onAdIsLoaded() {

            }

            override fun onInitFailed() {
                AdEvent.sendMvadLoadFail("material")
            }
        })
    }

    private fun showAd(isNeedShowAdToStoreDialog: Boolean, isRetry: Boolean = false) {
        if (!NetworkUtils.isNetworkAvailable()) {
            showToast(R.string.toast_no_internet)
            return
        }
        if (adViewModel.isAdAlreadyClick()) return
        adViewModel.onAdButtonClick()
        val parentActivity = activity
        if (parentActivity != null) {
            AdHelper.showRewardAd(parentActivity, "", object : RewardCallback {
                override fun onRewardVerified() {
                    ThreadUtils.postMainThread {
                        noteMaterialViewModel.changeBottomGuideVisibility(false)
                        download(true)
                    }
                    noteMaterialViewModel.setIsNeedShowAdToStoreDialog(isNeedShowAdToStoreDialog)
                }

                override fun onClose() {
                }

                override fun onError() {
                    adViewModel.onAdExit()
                    if (isRetry) {
                        AdEvent.sendNoAdsAcquiredRetry("fail", "material")
                    }
                    AdEvent.sendMvadShowFail("meterial")
                    adViewModel.showRetryDialog()
                }

                override fun onShow() {
                    adViewModel.onAdExit()
                    if (isRetry) {
                        AdEvent.sendNoAdsAcquiredRetry("success", "material")
                    }
                    AdEvent.sendMvadShow("material")
                }

                override fun onClicked() {
                    AdEvent.sendMvadClick("material")
                }

                override fun onInitFailed() {
                    adViewModel.onAdExit()
                    EditEvent.sendMvadInitaialization("fail")
                }

            }, object : AdLoadCallback {
                override fun onStartLoad() {
                    AdEvent.sendMvadStartLoad("material")
                }

                override fun onAdLoaded(sid: String) {
                    AdEvent.sendMvadLoadSuccess("material")
                }

                override fun onError(message: String) {
                    AdEvent.sendMvadLoadFail("material")
                    adViewModel.onAdExit()
                    if (isRetry) {
                        AdEvent.sendNoAdsAcquiredRetry("fail", "material")
                    }
                    adViewModel.showRetryDialog()
                }

                override fun onAdIsLoading() {

                }

                override fun onAdIsLoaded() {

                }

                override fun onInitFailed() {
                    AdEvent.sendMvadLoadFail("material")
                }

            })
        }
    }

    fun setOnStickerInsertAction(action: (uri: Uri) -> Unit) {
        stickerInsertAction = action
    }

    fun setOnMaterialShowInfoAction(action: (file: String, view: View) -> Unit) {
        materialShowInfoAction = action
    }

    fun setOnMaterialHideInfoAction(action: () -> Unit) {
        materialHideInfoAction = action
    }

    private fun select(materialCategory: NoteMaterialRepository.NoteMaterialStickerCategoryInfo) {
        paperCutToolsAdapter.clearSelected()
        StickerDataCollection.stickerCategoryData(
            materialCategory.noteMaterialCategory, DataType.NOTEBOOKS_STICKER_CATEGORY_VIEW
        )
        materialCategoryAdapter?.setCurrentSelect(materialCategory)
        val currentItem = materialPagerAdapter?.getSelectedStickerItem(materialCategory)
        binding.materialListPager.post {
            if (binding.materialListPager.currentItem == currentItem) return@post
            if (currentItem != null && currentItem != -1) {
                binding.materialListPager.setCurrentItem(currentItem, isNeedPagerScroll)
                isNeedPagerScroll = true
            }
        }
    }

    private fun refreshTypeList(
        categoryInfoList: List<NoteMaterialRepository.NoteMaterialStickerCategoryInfo>,
        customMaterialCategory: List<CustomMaterialCategory>,
        customMaterials: List<CustomMaterial>
    ) {
        if (materialCategoryAdapter == null) {
            materialCategoryAdapter = PhoneMaterialCategoryAdapter(
                categoryInfoList.toMutableList(),
            ).apply {
                doOnNewTypeSelected { position, info ->
                    isDrag = false
                    EditEvent.sendEditMaterialCategoryClick(info.noteMaterialCategory.name)
                    noteMaterialViewModel.selectNoteMaterialSticker(info)
                    val currentItem = materialPagerAdapter?.getSelectedStickerItem(info)
                    if (currentItem != null && currentItem != -1) {
                        binding.materialListPager.setCurrentItem(currentItem, isNeedPagerScroll)
                        isNeedPagerScroll = true
                    }
                    binding.materialTypeList.overScrollRecyclerView.scrollToPosition(position)
                }
            }
            binding.materialTypeList.overScrollRecyclerView.run {
                adapter = materialCategoryAdapter
                layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
                addItemDecoration(object : RecyclerView.ItemDecoration() {
                    private val itemInterval =
                        context.resources.getDimensionPixelSize(R.dimen.dp_30)
                    private val startInterval =
                        context.resources.getDimensionPixelSize(R.dimen.dp_15)

                    override fun getItemOffsets(
                        outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
                    ) {
                        if (parent.getChildAdapterPosition(view) == 0) {
                            outRect.left = startInterval
                        }
                        outRect.right = itemInterval
                    }
                })
            }
        } else {
            materialCategoryAdapter?.refreshList(categoryInfoList)
        }
        binding.materialTypeList.post {
            if (activity?.isFinishing == true || !this.isAdded) return@post
            var position = 0
            noteMaterialViewModel.materialCategoryStickerList.value?.indexOf(
                noteMaterialViewModel.currentSelectMaterialCategoryInfo.value
            )?.let {
                position = it
            }
            if (position >= 0) {
                binding.materialTypeList.overScrollRecyclerView.adapter?.let {
                    if (position < it.itemCount) {
                        binding.materialTypeList.overScrollRecyclerView.scrollToPosition(position)
                    }
                }
            }
        }

        if (materialPagerAdapter == null) {
            materialPagerAdapter = PhoneMaterialPagerAdapter(
                requireContext(),
                noteMaterialViewModel.customMaterialsCurrentPosition,
                customMaterials,
                categoryInfoList,
                customMaterialCategory
            ).apply {
                customMaterialCategoryClickListener = {
                    noteMaterialViewModel.customMaterialsCurrentPosition = it
                }
                customMaterialCategoryDeleteListener = { category ->
                    AlertDialog.Builder().setTitle(
                        resources.getString(
                            R.string.custom_material_category_delete_tips,
                            category.getName()
                        )
                    )
                        .setPositiveBtn(resources.getString(R.string.cancel)) {
                        }
                        .setPositiveBtnColor(AppUtils.getColor(R.color.text_secondary))
                        .setNegativeBtnColor(AppUtils.getColor(R.color.sign_red))
                        .setNegativeBtn(resources.getString(R.string.delete)) {
                            val deleteCategoryPosition =
                                noteMaterialViewModel.customMaterialCategories.indexOf(category)
                            if (deleteCategoryPosition == -1) {
                                return@setNegativeBtn
                            }
                            if (category.categoryId == CUSTOM_MATERIAL_CATEGORY_ID_MORE) {
                                MaterialEvent.sendMaterialLibraryCustomCategoryMoreDelete()
                            } else {
                                MaterialEvent.sendMaterialLibraryCustomCategoryDelete()
                            }
                            noteMaterialViewModel.customMaterialCategories.remove(category)
                            materialPagerAdapter?.refreshCustomStickCategoryList(
                                noteMaterialViewModel.customMaterialCategories,
                                deleteCategoryPosition
                            )
                            noteMaterialViewModel.deleteCustomMaterialCategory(category)
                        }
                        .setIsCancelable(false)
                        .build()
                        .show(childFragmentManager, "")
                }
                onStickerClicked = {
                    when (noteMaterialViewModel.currentStickerCategoryState.value) {
                        NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                            showToast(R.string.need_buy_vip)
                        }

                        NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD, NoteMaterialViewModel.MaterialCategoryState.DOWNLOADED -> {
                            val filePath = it.file
                            if (filePath == null) {
                                showToast(R.string.need_download)
                            } else {
                                val file = File(filePath)
                                if (file.exists()) {
                                    val uri = Uri.fromFile(file)
                                    userDismissAction?.invoke()
                                    dismiss()
                                    stickerInsertAction?.invoke(uri)
                                    StickerDataCollection.stickerData(
                                        it.noteMaterialSticker, DataType.NOTEBOOKS_STICKER_USE
                                    )
                                } else {
                                    showToast(R.string.need_download)
                                }
                            }
                        }

                        else -> {}
                    }
                }
                materialShowInfoAction = <EMAIL>
                materialHideInfoAction = <EMAIL>
                onCustomMaterialItemRemoveClicked = { customMaterial, position ->
                    AlertDialog.Builder().setTitle(resources.getString(R.string.paper_cut_del_tips))
                        .setPositiveBtn(resources.getString(R.string.paper_cut_del_confirm)) {
                            noteMaterialViewModel.deleteCustomMaterial(customMaterial)
                        }.setNegativeBtnColor(AppUtils.getColor(R.color.text_secondary))
                        .setNegativeBtn(resources.getString(R.string.cancel)) { }.build()
                        .show(childFragmentManager, "")
                }
                onCustomMaterialSwapFinish = {
                    noteMaterialViewModel.saveCustomMaterials(it)
                }
                onCustomMaterialItemClick = {
                    userDismissAction?.invoke()
                    onCustomMaterialClick?.invoke(it)
                    dismiss()
                }
                onCustomMaterialsRemoveClicked = { customMaterials ->
                    customMoveMaterials = customMaterials
                    AlertDialog.Builder().setTitle(
                        resources.getString(
                            R.string.paper_cut_delete_tips,
                            customMaterials.size
                        )
                    )
                        .setPositiveBtn(resources.getString(R.string.cancel)) {
                            materialPagerAdapter?.getCustomMaterialSelected()
                                ?.let { customMaterialCategory ->
                                    when (customMaterialCategory.categoryId) {
                                        CUSTOM_MATERIAL_CATEGORY_ID_INSTANT_ALPHA -> {
                                            MaterialEvent.sendMaterialLibraryCustomMaterialDelete(
                                                "instant_alpha_material",
                                                "cancel"
                                            )
                                        }

                                        CUSTOM_MATERIAL_CATEGORY_ID_DECOUPAGE -> {
                                            MaterialEvent.sendMaterialLibraryCustomMaterialDelete(
                                                "decoupage_material",
                                                "cancel"
                                            )
                                        }

                                        CUSTOM_MATERIAL_CATEGORY_ID_MY_ADDED -> {
                                            MaterialEvent.sendMaterialLibraryCustomMaterialDelete(
                                                "my_addition",
                                                "cancel"
                                            )

                                        }

                                        else -> {
                                            MaterialEvent.sendMaterialLibraryCustomMaterialDelete(
                                                "user_defined",
                                                "cancel"
                                            )
                                        }
                                    }
                                }
                        }
                        .setPositiveBtnColor(AppUtils.getColor(R.color.text_secondary))
                        .setNegativeBtnColor(AppUtils.getColor(R.color.sign_red))
                        .setNegativeBtn(resources.getString(R.string.delete)) {
                            materialPagerAdapter?.getCustomMaterialSelected()
                                ?.let { customMaterialCategory ->
                                    when (customMaterialCategory.categoryId) {
                                        CUSTOM_MATERIAL_CATEGORY_ID_INSTANT_ALPHA -> {
                                            MaterialEvent.sendMaterialLibraryCustomMaterialDelete(
                                                "instant_alpha_material",
                                                "success"
                                            )
                                        }

                                        CUSTOM_MATERIAL_CATEGORY_ID_DECOUPAGE -> {
                                            MaterialEvent.sendMaterialLibraryCustomMaterialDelete(
                                                "decoupage_material",
                                                "success"
                                            )
                                        }

                                        CUSTOM_MATERIAL_CATEGORY_ID_MY_ADDED -> {
                                            MaterialEvent.sendMaterialLibraryCustomMaterialDelete(
                                                "my_addition",
                                                "success"
                                            )

                                        }

                                        else -> {
                                            MaterialEvent.sendMaterialLibraryCustomMaterialDelete(
                                                "user_defined",
                                                "success"
                                            )
                                        }
                                    }
                                }
                            noteMaterialViewModel.deleteCustomMaterials(customMaterials)
                        }
                        .setIsCancelable(false)
                        .build()
                        .show(childFragmentManager, "")
                }
                onCustomMaterialsMoveClicked = { customMaterials ->
                    customMoveMaterials = customMaterials
                    showMoveCustomMaterialBottomSheet()
                }
                onCustomMaterialCategoryGuideShow = { anchor ->
                    if (Preferences.needShowCustomMaterialGuide) {
                        if (binding.materialListPager.scrollState == ViewPager2.SCROLL_STATE_IDLE) {
                            showCustomMaterialGuideWindow(anchor)
                        }
                    }
                }
                onCustomMaterialCategoryMoreBannerClick = {
                    jumpToCustomizeChanger()
                }
                onStickerScrollListener = materialListScrollListener
            }
        } else {
            materialPagerAdapter?.refreshList(customMaterials, categoryInfoList)
        }
        binding.materialListPager.post {
            if (activity?.isFinishing == true || !this.isAdded) return@post
            binding.materialListPager.adapter = materialPagerAdapter
        }
        binding.materialListPager.addOnLayoutChangeListener { _, _, _, right, bottom, _, _, _, oldBottom ->
            dragBottom = bottom
            dragRight = right
            if (bottom != oldBottom) {
                materialPagerAdapter?.changeLayout(
                    right, bottom, binding.materialListPager.currentItem
                )
            }
        }
    }

    private fun refreshStickerList(stickerInfoList: List<NoteMaterialRepository.NoteMaterialStickerInfo>) {
        if (noteMaterialViewModel.currentSelectPaperCutCategory.value == PaperCutTool.CUSTOM_MATERIAL) return
        if (noteMaterialViewModel.currentSelectPaperCutCategory.value == PaperCutTool.PAPER_CUT_TOOL) return
        if (context == null) {
            return
        }
        materialPagerAdapter?.refreshStickList(
            stickerInfoList, noteMaterialViewModel.currentSelectMaterialCategoryInfo.value
        )
    }

    fun refreshSticker(
        stickerInfo: NoteMaterialRepository.NoteMaterialStickerInfo,
        param: Any
    ) {
        materialPagerAdapter?.refreshItem(
            binding.materialListPager.currentItem,
            stickerInfo,
            param
        )
    }

    fun refreshVariableView() {
        if (::binding.isInitialized) {
            binding.root.refresh()
        }
    }

    private fun hideBottomView() {
        refreshBottomView(
            showText = false,
            downloadType = DownloadType.FREE,
        )
    }

    private fun showBottomDownloadView(showTag: DownloadType) {
        refreshBottomView(
            showText = true,
            downloadType = showTag,
        )
    }

    private fun refreshBottomView(
        showText: Boolean, downloadType: DownloadType
    ) {
        if (showText) {
            binding.bottomViewGroup.visibility = View.VISIBLE
            when (downloadType) {
                DownloadType.DOWNLOADING -> {
                    binding.bottomText.text = resources.getString(R.string.downloading)
                }

                else -> {
                    if (downloadType == DownloadType.VIP && PayHelper.isSubscriptionSupported()) {
                        binding.bottomText.text = resources.getString(R.string.go_to_vip)
                    } else {
                        binding.bottomText.text = resources.getString(R.string.download)
                    }
                }
            }
            binding.bottomBackground.isEnabled = downloadType != DownloadType.DOWNLOADING
            binding.bottomHintText.visibility =
                if (downloadType == DownloadType.VIP && PayHelper.isSubscriptionSupported()) {
                    View.VISIBLE
                } else {
                    View.GONE
                }
            binding.bottomTextTag.isVisible =
                downloadType == DownloadType.VIP && !PayHelper.isSubscriptionSupported()
            binding.bottomTextAdTag.isVisible =
                downloadType == DownloadType.AD && !PayHelper.isSubscriptionSupported()
        } else {
            binding.bottomViewGroup.visibility = View.GONE
        }
        binding.root.refresh()
    }

    private fun showReloadView() {
        binding.reloadGroup.visibility = View.VISIBLE
        hideBottomView()
    }

    private fun hideReloadView() {
        binding.reloadGroup.visibility = View.INVISIBLE
    }

    fun setOnUserDismissAction(action: () -> Unit) {
        userDismissAction = action
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        userDismissAction?.invoke()
    }

    private fun showToast(@StringRes resId: Int) {
        parentActivity?.get()?.let {
            ToastUtils.windowTopCenter(it, resId)
        }
    }

    private fun showToast(message: String) {
        parentActivity?.get()?.let {
            Toast(it).apply {
                view = LayoutInflater.from(it).inflate(R.layout.phone_toast, null).apply {
                    findViewById<TextView>(android.R.id.message).text = message
                }
                setGravity(
                    Gravity.BOTTOM, 0, binding.content.height + binding.dragView.height
                )
                duration = Toast.LENGTH_SHORT
            }.show()
        }
    }

    fun selectPaperType(type: PaperCutTool) {
        hideReloadView()
        materialCategoryAdapter?.clearSelected()
        paperCutToolsAdapter.setCurrentSelect(type)
        if (type == PaperCutTool.PAPER_CUT_TOOL) {
            EditEvent.sendMaterialToolClick()
        } else {
            EditEvent.sendCustomMaterialClick()
        }
    }

    private fun showAdToStoreDialog() {
        if (childFragmentManager.findFragmentByTag(PhoneAdToStoreDialog.TAG) != null) {
            return
        }
        phoneAdToStoreDialog = PhoneAdToStoreDialog(onVipClickedAction = {
            dismiss()
            MaterialEvent.sendAdMemberOrientationClick()
            safeNavigate(R.id.note_editor,
                NoteEditorFragmentDirections.actionPhoneNoteEditorToVipStore().apply {
                    source = NaviEnum.AD_FREE
                })
        })
        phoneAdToStoreDialog?.setOnDismissListener {
            noteMaterialViewModel.setIsNeedShowAdToStoreDialog(false)
        }
        phoneAdToStoreDialog?.safeShow(childFragmentManager, PhoneAdToStoreDialog.TAG)
    }

    private fun showMaterialToolBottomSheet() {
        try {
            val fragment =
                childFragmentManager.findFragmentByTag(PhoneNoteMaterialToolBottomSheet.TAG)

            if (fragment is PhoneNoteMaterialToolBottomSheet) {
                return
            }
            PhoneNoteMaterialToolBottomSheet().apply {
                onBannerClick = {
                    <EMAIL>()
                }
                this.userDismissAction = {
                    materialPagerAdapter?.changeLayout(
                        dragRight, dragBottom, binding.materialListPager.currentItem
                    )
                    editorViewModel.changeMaterialToolViewVisibility(false)
                }
                onBackgroundClick = {
                    <EMAIL>()
                    <EMAIL>?.invoke()
                    editorViewModel.changeMaterialToolViewVisibility(false)
                }
                onLayoutChanged = { right, bottom ->
                    dragBottom = bottom
                    dragRight = right
                }
                doOnShowAnimationEnd = {
                    binding.root.visibility = View.INVISIBLE
                }
                doOnHideAnimationStart = {
                    binding.root.visibility = View.VISIBLE
                }
            }.safeShow(childFragmentManager, PhoneNoteMaterialToolBottomSheet.TAG)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun showMoveCustomMaterialBottomSheet() {
        if (childFragmentManager.findFragmentByTag(PhoneMoveCustomMaterialBottomSheet.TAG) != null) return
        val currentCategory = materialPagerAdapter?.getCustomMaterialSelected()
        val moveTargetCategorys = if (currentCategory == null) {
            noteMaterialViewModel.customMaterialCategories
        } else {
            noteMaterialViewModel.customMaterialCategories.filter { category ->
                category.getName() != currentCategory.getName() && category.categoryId != CUSTOM_MATERIAL_CATEGORY_ID_MORE
            }
        }
        moveBottomSheet = PhoneMoveCustomMaterialBottomSheet().apply {
            //新建分类点击回调
            setOnNewClassificationClick {
                showCreateMaterialClassifyDialog()
            }
            //分类列表
            setClassificationList(
                moveTargetCategorys
            )
            //其它分类点击回调，position：分类在列表中的坐标
            setOnOtherClassificationClick { position ->
                currentCategory?.let { customMaterialCategory ->
                    when (customMaterialCategory.categoryId) {
                        CUSTOM_MATERIAL_CATEGORY_ID_INSTANT_ALPHA -> {
                            MaterialEvent.sendMaterialLibraryCustomMaterialMove(
                                "instant_alpha_material",
                                "success"
                            )
                        }

                        CUSTOM_MATERIAL_CATEGORY_ID_DECOUPAGE -> {
                            MaterialEvent.sendMaterialLibraryCustomMaterialMove(
                                "decoupage_material",
                                "success"
                            )
                        }

                        CUSTOM_MATERIAL_CATEGORY_ID_MY_ADDED -> {
                            MaterialEvent.sendMaterialLibraryCustomMaterialMove(
                                "my_addition",
                                "success"
                            )

                        }

                        else -> {
                            MaterialEvent.sendMaterialLibraryCustomMaterialMove(
                                "user_defined",
                                "success"
                            )
                        }
                    }
                }
                noteMaterialViewModel.moveCustomMaterial(
                    moveTargetCategorys[position],
                    customMoveMaterials
                )
                context?.let { context ->
                    ToastUtils.topCenter(
                        context,
                        context.getString(
                            R.string.custom_material_move_success_tips,
                            moveTargetCategorys[position].getName()
                        )
                    )
                }
                createClassifyDialog?.dismiss()
                moveBottomSheet?.dismiss()
            }

            setCancelClick {
                currentCategory?.let { customMaterialCategory ->
                    when (customMaterialCategory.categoryId) {
                        CUSTOM_MATERIAL_CATEGORY_ID_INSTANT_ALPHA -> {
                            MaterialEvent.sendMaterialLibraryCustomMaterialMove(
                                "instant_alpha_material",
                                "cancel"
                            )
                        }

                        CUSTOM_MATERIAL_CATEGORY_ID_DECOUPAGE -> {
                            MaterialEvent.sendMaterialLibraryCustomMaterialMove(
                                "decoupage_material",
                                "cancel"
                            )
                        }

                        CUSTOM_MATERIAL_CATEGORY_ID_MY_ADDED -> {
                            MaterialEvent.sendMaterialLibraryCustomMaterialMove(
                                "my_addition",
                                "cancel"
                            )

                        }

                        else -> {
                            MaterialEvent.sendMaterialLibraryCustomMaterialMove(
                                "user_defined",
                                "cancel"
                            )
                        }
                    }
                }
            }
        }
        moveBottomSheet?.safeShow(childFragmentManager, PhoneMoveCustomMaterialBottomSheet.TAG)
    }

    private fun showCreateMaterialClassifyDialog() {
        if (createClassifyDialog == null) {
            createClassifyDialog = PhoneCreateMaterialClassifyDialog()
            createClassifyDialog?.setCommitAction(this::commitClassifyName)
        }
        createClassifyDialog?.setOnDismissListener {
            createClassifyDialog?.clearContent()
        }
        createClassifyDialog?.show(childFragmentManager, PhoneCreateFolderDialog.TAG)
    }

    private fun commitClassifyName(classifyName: String) {
        val result =
            noteMaterialViewModel.customMaterialCategories.find { customMaterialCategory ->
                customMaterialCategory.getName() == classifyName
            }
        if (result != null) {
            val alertDialog = AlertDialog.Builder()
                .setMsg(resources.getString(R.string.custom_material_category_name_repeat))
                .setPositiveBtn(resources.getString(R.string.confirm)) {
                }
                .setIsCancelable(false)
                .setFlowOrientation(Flow.VERTICAL)
                .build()
            alertDialog.show(childFragmentManager, null)
            createClassifyDialog?.clearFocus()
            return
        }
        noteMaterialViewModel.createCustomMaterialCategory(classifyName, customMoveMaterials)
        MaterialEvent.sendMaterialLibraryCustomCategoryAdd(classifyName)
        materialPagerAdapter?.refreshCustomStickCategoryList(noteMaterialViewModel.customMaterialCategories)
        context?.let { context ->
            ToastUtils.topCenter(
                context,
                context.getString(R.string.custom_material_move_success_tips, classifyName)
            )
        }
        createClassifyDialog?.dismiss()
        moveBottomSheet?.dismiss()
    }

    fun showCustomMaterialGuideWindow(anchor: View) {
        customMaterialGuideWindow?.let { window ->
            if (window.isShowing) return
        }
        customMaterialGuideWindow = PhoneCustomMaterialGuideWindow(requireContext()).apply {
            setTips(resources.getString(R.string.custom_material_tips))
        }
        customMaterialGuideWindow!!.setTouchInterceptor { v, event ->
            var isOuterClick = customMaterialGuideWindow!!.contentView.outsideArea(event)
            if (isOuterClick) {
                return@setTouchInterceptor true
            }
            false
        }
        customMaterialGuideWindow?.setOnDismissClick {
            Preferences.needShowCustomMaterialGuide = false
        }
        customMaterialGuideWindow?.show(anchor)
    }

    //跳转至超萌鸭
    private fun jumpToCustomizeChanger() {
        //屏蔽GP渠道包操作
        if (BuildConfig.FLAVOR_CHANNEL == Channel.PLAY.channel) return
        val isInstalled = isAppInstalled(requireContext(), PACKAGE_NAME_CUSTOMIZE_CHANGER)
        //已安装「超萌鸭」，唤起「超萌鸭」APP
        if (isInstalled) {
            EditEvent.sendMaterialLibraryCustomMaterialCategoryMoreBannerClick("open")
            val intent =
                requireContext().packageManager.getLaunchIntentForPackage(
                    PACKAGE_NAME_CUSTOMIZE_CHANGER
                )
            intent?.let {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
            }
        } else {
            //未安装「超萌鸭」
            //已下载「超萌鸭」apk，直接跳转安装
            if (noteMaterialViewModel.hasDownloadedCustomizeChangerApk()) {
                EditEvent.sendMaterialLibraryCustomMaterialCategoryMoreBannerClick("install")
                InstallApkUtils.installApk(
                    requireContext(),
                    noteMaterialViewModel.customizeChangerApkPath
                )
            } else {
                //未下载「超萌鸭」apk
                //网络正常，下载「超萌鸭」apk
                if (NetworkUtils.isNetworkAvailable()) {
                    noteMaterialViewModel.showCustomizeChangerDownloadingDialog.value?.let { isShow ->
                        if (isShow) {
                            //正在下载
                            return
                        } else {
                            noteMaterialViewModel.changeShowCustomizeChangerDownloadingDialog(true)
                        }
                    }
                    noteMaterialViewModel.downloadCustomizeChangerApk(object :
                        CustomizeChangerApkDownloadCallBack {
                        override fun onProgress(progress: Float) {
                            //  0 <= progress <= 1.0
                            customizeChangerDownloadingDialog?.updateProgressInternal(progress)
                        }

                        override fun onSuccess(filePath: String) {
                            EditEvent.sendCustomizeChangerDownloadStatus("success")
                        }

                        override fun onFailed(
                            errorCode: Int,
                            msg: String,
                            extraCode: Int,
                            extraInfo: String
                        ) {
                            EditEvent.sendCustomizeChangerDownloadStatus("failed")
                        }
                    })
                    EditEvent.sendMaterialLibraryCustomMaterialCategoryMoreBannerClick("download")
                    if (Preferences.needReportDownloadCustomizeChangerByXuanhu) {
                        XuanhuHelper.sendEvent(
                            "xh_tracking_dl",
                            mapOf("targetPkg" to PACKAGE_NAME_CUSTOMIZE_CHANGER)
                        )
                        Preferences.needReportDownloadCustomizeChangerByXuanhu = false
                    }
                } else {
                    //无网络提示
                    ToastUtils.windowTopCenter(requireActivity(), R.string.network_not_connected)
                }
            }
        }
    }

    //通过包名判断app是否安装
    private fun isAppInstalled(context: Context, packageName: String): Boolean {
        return if (TextUtils.isEmpty(packageName)) {
            false
        } else {
            try {
                val info = context.getPackageManager()
                    .getApplicationInfo(packageName, PackageManager.MATCH_UNINSTALLED_PACKAGES)
                true
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
                false
            }
        }
    }

    private fun showCustomizeChangerDownloadingDialog() {
        if (customizeChangerDownloadingDialog == null) {
            customizeChangerDownloadingDialog = CustomizeChangerDownloadingDialog().apply {
                setOnCloseListener {
                    EditEvent.sendCustomizeChangerDownloadStatus("cancel")
                    noteMaterialViewModel.cancelDownloadCustomizeChanger()
                    ToastUtils.windowTopCenter(
                        requireActivity(),
                        R.string.base_button_cancel_download
                    )
                }
            }
        }
        customizeChangerDownloadingDialog?.safeShow(
            childFragmentManager,
            CustomizeChangerDownloadingDialog.TAG
        )
    }

    private fun hideCustomizeChangerDownloadingDialog() {
        customizeChangerDownloadingDialog?.dismiss()
    }

    private fun showCustomizeChangerInstallDialog(path: String) {
        if (customizeChangerInstallDialog == null) {
            customizeChangerInstallDialog = AlertDialog.Builder()
                .setTitle(resources.getString(R.string.custom_material_customize_changer_install_dialog_title))
                .setIsCancelable(false)
                .setNegativeBtn(resources.getString(R.string.cancel)) {
                    noteMaterialViewModel.changeShowCustomizeChangerInstallDialog(false)
                }
                .setNegativeBtnColor(
                    resources.getColor(
                        R.color.text_secondary,
                        null
                    )
                )
                .setPositiveBtn(resources.getString(R.string.base_button_install)) {
                    InstallApkUtils.installApk(requireContext(), path)
                    noteMaterialViewModel.changeShowCustomizeChangerInstallDialog(false)
                }
                .build()
        }
        customizeChangerInstallDialog?.safeShow(
            childFragmentManager,
            TAG_CUSTOMIZE_CHANGER_INSTALL_DIALOG
        )
    }

    private fun hideCustomizeChangerInstallDialog() {
        customizeChangerInstallDialog?.dismiss()
    }

    enum class DownloadType {
        FREE, AD, VIP, VIP_OR_AD, DOWNLOADING
    }
}
package com.topstack.kilonotes.phone.note

import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.LayerDrawable
import android.text.Editable
import android.text.TextWatcher
import android.view.*
import android.widget.TextView
import androidx.core.graphics.ColorUtils
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.BubbleLayout
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.doodle.views.selectview.ILayer
import com.topstack.kilonotes.base.note.BaseSelectColorWindow
import com.topstack.kilonotes.base.note.model.ColorWindowItem
import com.topstack.kilonotes.base.note.model.ColorWindowItemType
import com.topstack.kilonotes.base.track.event.EditEvent
import com.topstack.kilonotes.base.util.SoftInputUtil
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.base.util.WindowInsetsUtils
import com.topstack.kilonotes.base.util.isValidColorWithoutAlpha
import com.topstack.kilonotes.databinding.PhoneColorListItemBinding
import com.topstack.kilonotes.databinding.PhoneColorListTitleBinding
import com.topstack.kilonotes.databinding.PhoneWindowSelectColorBinding
import java.util.*

class PhoneSelectColorWindow @JvmOverloads constructor(
    val context: Context,
    initialColor: Int,
    colorList: List<ColorWindowItem>,
    alpha: Int,
    bottomHeight: Int = 0,
    fillingFlag: Boolean = false,
    val type: Int,
    showDefaultSelect: Boolean = true
) : BaseSelectColorWindow(initialColor, colorList, alpha, showDefaultSelect) {

    companion object {
        private const val COLOR_LIST_SPAN_COUNT = 5
        var ToolBarBottomHeight: Int = 0
        const val TYPE_TEXT = 0
        const val TYPE_PEN = 1
        const val TYPE_HIGHLIGHTER = 2
        const val TYPE_LASSO = 3

        const val ITEM_TYPE_COLOR = 0
        const val ITEM_TYPE_TITLE = 1

        const val RECENT_USE_COLOR_LIST_MAX_SIZE = 9
        const val ADD_COLOR_ICON_COUNT = 1
        const val TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST = 1

        /**
         * 画笔、文本、荧光笔
         */
        const val PAINTBRUSH = "paintbrush"
        const val TEXT = "text"
        const val HIGHLIGHTER = "highlighter"
    }

    private val binding = PhoneWindowSelectColorBinding.inflate(LayoutInflater.from(context))

    private val colorAdapter: ColorListAdapter

    private var showPickColor = false

    private var backgroundView: View? = null

    private val anchorLocation = IntArray(2)

    private var softInputShow = false

    private var fillingColorFlag: Boolean

    private var navigationBarBottomHeight: Int = 0

    private val presetColorList: MutableList<Int>

    private val colorTextWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) = Unit

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) = Unit

        override fun afterTextChanged(s: Editable?) {
            s?.let {
                val colorString = it.toString()
                if (isValidColorWithoutAlpha(colorString)) {
                    binding.colorPickView.currentColor = Color.parseColor(colorString)
                }
            }
        }

    }

    init {
        this.animationStyle = R.style.popupWindowAnim
        contentView = binding.root.apply {
            measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
        }
        binding.subTitleBackground.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        binding.colorPresetText.height = binding.subTitleBackground.measuredHeight
        binding.colorCustomText.height = binding.subTitleBackground.measuredHeight
        binding.colorPresetText.maxLines = 2
        binding.colorCustomText.maxLines = 2
        width = ViewGroup.LayoutParams.MATCH_PARENT
        height = ViewGroup.LayoutParams.WRAP_CONTENT
        isFocusable = true
        isOutsideTouchable = true
        softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
        fillingColorFlag = fillingFlag
        navigationBarBottomHeight = bottomHeight
        presetColorList = if (fillingColorFlag && type != TYPE_LASSO) {
            UserUsageConfig.fillingAllColors.toMutableList()
        } else {
            when (type) {
                TYPE_TEXT -> UserUsageConfig.textAllColors.toMutableList()
                TYPE_HIGHLIGHTER -> UserUsageConfig.highlighterAllColors.toMutableList()
                else -> UserUsageConfig.penAllColors.toMutableList()
            }
        }
        colorAdapter = ColorListAdapter(
            initialColor, type, mutableColorList, presetColorList,
            onColorSelect = { color ->
                binding.colorPickView.currentColor = color.res
                currentColor = color.res
                onColorSelectedAction?.invoke(color)
                updateColorAddImageView()
            },
            onColorDelete = {
                updateColorAddImageView()
                onPresetColorChangedAction?.invoke(mutableColorList)
            },
            onAddColor = {
                showPickColor = true
                updateMainContent()
            },
            fillingColorFlag,
            context,
            showDefaultSelect
        )
        binding.colorPickView.apply {
            currentColor = initialColor
            onColorChangedCallback = this@PhoneSelectColorWindow::onColorChanged
        }
        binding.colorListView.apply {
            adapter = colorAdapter
            addItemDecoration(
                object : RecyclerView.ItemDecoration() {
                    val colorVerticalSpacing =
                        context.resources.getDimensionPixelSize(R.dimen.dp_60)
                    val titleVerticalSpacing =
                        context.resources.getDimensionPixelSize(R.dimen.dp_20)
                    val titleHorizontalSpacing =
                        context.resources.getDimensionPixelSize(R.dimen.dp_26)

                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        if (view.findViewById<TextView>(R.id.title) != null) {
                            outRect.top = titleVerticalSpacing
                            outRect.left = titleHorizontalSpacing
                        }
                        outRect.bottom = colorVerticalSpacing
                    }
                }
            )
            layoutManager = GridLayoutManager(context, COLOR_LIST_SPAN_COUNT).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        if (colorAdapter.getItemViewType(position) == ITEM_TYPE_TITLE) {
                            return 5
                        } else {
                            return 1
                        }
                    }

                }
            }
            ItemTouchHelper(ColorItemTouchCallback {
                if (!colorAdapter.editMode) {
                    colorAdapter.changeEditMode(true)
                    updateFinishEdit()
                }

            }).attachToRecyclerView(this)
        }
        binding.colorEditText.apply {
            onFocusChangeListener =
                View.OnFocusChangeListener { _, hasFocus ->
                    if (hasFocus) {
                        addTextChangedListener(colorTextWatcher)
                    } else {
                        removeTextChangedListener(colorTextWatcher)
                        WindowInsetsUtils.hideSoftKeyBoard(binding.colorEditText)
                    }
                }
        }
        binding.colorAddImage.setOnClickListener {
            val color = ColorUtils.setAlphaComponent(binding.colorPickView.currentColor, alpha)
            if (colorAdapter.addAndSetColor(color, fillingColorFlag)) {
                binding.colorListView.scrollToPosition(presetColorList.size)
                showPickColor = false
                updateMainContent()
                onColorAddAction?.invoke(color)
                if (!fillingColorFlag) {
                    onColorSelectedAction?.invoke(
                        ColorWindowItem(
                            color,
                            ColorWindowItemType.RECENT_USE_COLOR
                        )
                    )
                }
                updateColorAddImageView()
                when (type) {
                    TYPE_TEXT -> EditEvent.sendColorEditSet("add", TEXT)
                    TYPE_HIGHLIGHTER -> EditEvent.sendColorEditSet("add", HIGHLIGHTER)
                    else -> EditEvent.sendColorEditSet("add", PAINTBRUSH)
                }
            }
            SoftInputUtil.hideSoftInput(it)
        }
        binding.colorPresetText.setOnClickListener {
            if (showPickColor) {
                showPickColor = false
                updateMainContent()
            }
        }
        binding.colorCustomText.setOnClickListener {
            if (!showPickColor) {
                showPickColor = true
                updateMainContent()
            }
        }
        binding.colorFinishEdit.setOnClickListener {
            colorAdapter.changeEditMode(false)
            updateFinishEdit()
            onPresetColorChangedAction?.invoke(mutableColorList)

        }
        binding.colorIndicator.setOnClickListener {
            onRequestPickWindowColorAction?.invoke()
        }
        updateFinishEdit()
        onColorChanged(initialColor)
        updateMainContent()
    }

    override fun show(anchor: View) {
        val context = anchor.context
        if (context !is Activity) {
            return
        }
        val window = context.window
        if (backgroundView == null) {
            backgroundView = View(context).apply {
                setBackgroundColor(Color.BLACK)
                alpha = 0.08F
            }
        }
        val params = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            anchor.top
        )
        backgroundView?.parent?.let {
            (it as ViewGroup).removeView(backgroundView)
        }
        window.addContentView(backgroundView, params)
        val location = IntArray(2)
        val listener = ViewTreeObserver.OnGlobalLayoutListener {
            binding.root.getLocationOnScreen(location)
            val show = location[1] + contentView.measuredHeight < anchorLocation[1]
            if (softInputShow != show) {
                softInputShow = show
                if (!softInputShow && binding.colorEditText.hasFocus()) {
                    binding.colorEditText.clearFocus()
                }
            }
        }
        binding.root.viewTreeObserver.addOnGlobalLayoutListener(listener)
        setOnDismissListener {
            backgroundView?.parent?.let {
                (it as ViewGroup).removeView(backgroundView)
            }
            binding.root.viewTreeObserver.removeOnGlobalLayoutListener(listener)
            onColorSelectFinishedAction?.invoke(currentColor)
        }
        anchor.getLocationOnScreen(anchorLocation)
        showAtLocation(
            anchor,
            Gravity.TOP or Gravity.START,
            0,
            anchorLocation[1] - contentView.measuredHeight
        )
    }

    override fun show(anchor: View, orientation: BubbleLayout.BubbleOrientation) {
    }

    override fun show(parentView: View, layer: ILayer, mSrcRect: Rect) {
        setOnDismissListener {
            onColorSelectFinishedAction?.invoke(currentColor)
        }
        showAtLocation(
            parentView,
            Gravity.BOTTOM or Gravity.START,
            0,
            ToolBarBottomHeight + navigationBarBottomHeight
        )
    }

    override fun show(
        parentView: View,
        layer: ILayer,
        mSrcRect: Rect,
        color: Int
    ) {
        binding.colorPickView.currentColor = color
        updateColorAddImageView()
        this.show(parentView, layer, mSrcRect)
    }

    override fun update(anchor: View) {
        update(anchor, 0, -anchor.height - contentView.measuredHeight, -1, -1)
    }

    private fun onColorChanged(color: Int) {
        binding.colorIndicator.setCardBackgroundColor(color)
        val colorString = String.format("#%06X", 0xFFFFFF and color)
        binding.colorEditText.apply {
            if (text.toString().uppercase() != colorString) {
                if (hasFocus()) {
                    removeTextChangedListener(colorTextWatcher)
                }
                setText(colorString)
                if (hasFocus()) {
                    addTextChangedListener(colorTextWatcher)
                }
            }
        }
        updateColorAddImageView()
    }

    private fun updateMainContent() {
        if (showPickColor) {
            binding.colorPresetText.isSelected = false
            binding.colorCustomText.isSelected = true
            binding.colorListView.visibility = View.INVISIBLE
            binding.colorCustomGroup.visibility = View.VISIBLE
        } else {
            binding.colorPresetText.isSelected = true
            binding.colorCustomText.isSelected = false
            binding.colorListView.visibility = View.VISIBLE
            binding.colorCustomGroup.visibility = View.INVISIBLE
        }
    }

    private fun updateFinishEdit() {
        binding.colorFinishEdit.visibility = if (colorAdapter.editMode) {
            View.VISIBLE
        } else {
            View.INVISIBLE
        }
    }

    private fun updateColorAddImageView() {
        val color = ColorUtils.setAlphaComponent(binding.colorPickView.currentColor, alpha)
        binding.colorAddImage.setImageResource(
            if (mutableColorList.firstOrNull {
                    ColorUtils.setAlphaComponent(it.res, alpha) == color
                            && it.type == ColorWindowItemType.RECENT_USE_COLOR
                } != null
            ) {
                R.drawable.phone_select_color_icon_add_unclickable
            } else {
                R.drawable.phone_select_color_icon_add_clickable
            }
        )
    }

    private inner class ColorItemTouchCallback(private val onEditMode: () -> Unit) :
        ItemTouchHelper.Callback() {

        var originalPos: Int? = null
        var fromPos: Int? = null
        var toPos: Int? = null
        var originalItem: ColorWindowItem? = null
        var targetItem: ColorWindowItem? = null


        override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
            super.onSelectedChanged(viewHolder, actionState)
            if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
                onEditMode()
            }
        }

        override fun getMovementFlags(
            recyclerView: RecyclerView,
            viewHolder: RecyclerView.ViewHolder
        ): Int {
            val dragFlags =
                if (viewHolder.itemViewType == ITEM_TYPE_TITLE || (viewHolder as ColorViewHolder).color?.type == ColorWindowItemType.ADD_COLOR) {
                    0
                } else {
                    ItemTouchHelper.UP or ItemTouchHelper.DOWN or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
                }
            return makeMovementFlags(dragFlags, 0)
        }

        override fun onMove(
            recyclerView: RecyclerView,
            viewHolder: RecyclerView.ViewHolder,
            target: RecyclerView.ViewHolder
        ): Boolean {
            if (originalPos == null) {
                originalPos = viewHolder.bindingAdapterPosition
            }
            fromPos = viewHolder.bindingAdapterPosition
            toPos = target.bindingAdapterPosition
            if (fromPos!! >= mutableColorList.size || toPos!! >= mutableColorList.size) {
                fromPos = null
                toPos = null
                return false
            }
            fromPos?.let {
                (viewHolder as ColorViewHolder).color?.let {
                    targetItem = ColorWindowItem(it.res, ColorWindowItemType.PRESET_COLOR)
                }
            }
            originalPos?.let {
                (viewHolder as ColorViewHolder).color?.let {
                    originalItem = ColorWindowItem(it.res, it.type, it.category)
                }
            }
            if (fromPos!! < toPos!!) {
                for (i in fromPos!! until toPos!!) {
                    Collections.swap(mutableColorList, i, i + 1)
                }
            } else {
                for (i in fromPos!! downTo toPos!! + 1) {
                    Collections.swap(mutableColorList, i, i - 1)
                }
            }
            recyclerView.adapter?.notifyItemMoved(fromPos!!, toPos!!)
            return true
        }

        override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        }

        override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
            super.clearView(recyclerView, viewHolder)
            recyclerView.post {
                //最近使用或色卡拖动到预设中
                if (originalPos != null && toPos != null && targetItem != null && originalItem != null
                    && originalPos!! >= presetColorList.size && toPos!! <= presetColorList.size
                ) {
                    //颜色重复
                    if (presetColorList.contains(targetItem!!.res)) {
                        if (originalPos!! < toPos!!) {
                            for (i in toPos!! downTo originalPos!! + 1) {
                                Collections.swap(mutableColorList, i, i - 1)
                            }
                        } else {
                            for (i in toPos!! until originalPos!!) {
                                Collections.swap(mutableColorList, i, i + 1)
                            }
                        }
                        recyclerView.adapter?.notifyItemMoved(toPos!!, originalPos!!)
                        ToastUtils.topCenter(
                            context,
                            context.resources.getString(R.string.color_has_added)
                        )
                        //拖动到最近使用标题上
                    } else if (toPos!! == presetColorList.size) {
                        recyclerView.adapter?.notifyItemRemoved(toPos!!)
                        mutableColorList.removeAt(
                            toPos!!
                        )
                        mutableColorList.add(
                            toPos!!,
                            ColorWindowItem(
                                targetItem!!.res,
                                ColorWindowItemType.PRESET_COLOR
                            )
                        )
                        presetColorList.add(toPos!!, targetItem!!.res)
                        mutableColorList.add(
                            originalPos!! + 1,
                            ColorWindowItem(
                                originalItem!!.res,
                                originalItem!!.type,
                                originalItem!!.category
                            )
                        )
                        (recyclerView.adapter as? ColorListAdapter)?.let {
                            it.notifyItemInserted(originalPos!!)
                            it.notifyItemInserted(toPos!!)
                            it.currentIndex ?: return@let
                            if (it.currentIndex!!.position >= toPos!!) {
                                it.currentIndex =
                                    ColorIndex(it.currentIndex!!.position + 1, null)
                            }
                        }
                        when (type) {
                            TYPE_TEXT -> EditEvent.sendColorEditSet("drag", TEXT)
                            TYPE_HIGHLIGHTER -> EditEvent.sendColorEditSet("drag", HIGHLIGHTER)
                            else -> EditEvent.sendColorEditSet("drag", PAINTBRUSH)
                        }
                        onPresetColorChangedAction?.invoke(mutableColorList)
                        //颜色添加
                    } else {
                        recyclerView.adapter?.notifyItemRemoved(toPos!!)
                        mutableColorList.removeAt(
                            toPos!!
                        )
                        mutableColorList.add(
                            toPos!!,
                            ColorWindowItem(
                                targetItem!!.res,
                                ColorWindowItemType.PRESET_COLOR
                            )
                        )
                        presetColorList.add(toPos!!, targetItem!!.res)
                        mutableColorList.add(
                            originalPos!! + 1,
                            ColorWindowItem(
                                originalItem!!.res,
                                originalItem!!.type,
                                originalItem!!.category
                            )
                        )
                        (recyclerView.adapter as? ColorListAdapter)?.let {
                            it.notifyItemInserted(originalPos!!)
                            it.notifyItemInserted(toPos!!)
                            it.currentIndex ?: return@let
                            if (it.currentIndex!!.position >= toPos!!) {
                                it.currentIndex =
                                    ColorIndex(it.currentIndex!!.position + 1, null)
                            }
                        }
                        val holder =
                            colorAdapter.currentIndex?.viewHolder
                        if (holder != null) {
                            colorAdapter.currentIndex =
                                ColorIndex(holder.bindingAdapterPosition, null)
                        }
                        when (type) {
                            TYPE_TEXT -> EditEvent.sendColorEditSet("drag", TEXT)
                            TYPE_HIGHLIGHTER -> EditEvent.sendColorEditSet("drag", HIGHLIGHTER)
                            else -> EditEvent.sendColorEditSet("drag", PAINTBRUSH)
                        }
                        onPresetColorChangedAction?.invoke(mutableColorList)
                    }
                    //预设排序
                } else if (fromPos != null && toPos != null && targetItem != null && fromPos!! >= 0 && fromPos!! < presetColorList.size && toPos!! >= 0 && toPos!! < presetColorList.size) {
                    val holder =
                        colorAdapter.currentIndex?.viewHolder
                    if (holder != null) {
                        colorAdapter.currentIndex =
                            ColorIndex(holder.bindingAdapterPosition, holder)
                    }
                    onPresetColorChangedAction?.invoke(mutableColorList)
                    //无效的拖动
                } else {
                    if (originalPos != null && toPos != null) {
                        if (originalPos!! < toPos!!) {
                            for (i in toPos!! downTo originalPos!! + 1) {
                                Collections.swap(mutableColorList, i, i - 1)
                            }
                        } else {
                            for (i in toPos!! until originalPos!!) {
                                Collections.swap(mutableColorList, i, i + 1)
                            }
                        }
                        recyclerView.adapter?.notifyItemMoved(toPos!!, originalPos!!)
                    }
                }
                originalPos = null
                originalItem = null
                fromPos = null
                toPos = null
                targetItem = null
            }
        }
    }

    private class ColorListAdapter(
        initialColor: Int,
        type: Int,
        private val colorList: MutableList<ColorWindowItem>,
        private val presetColorList: MutableList<Int>,
        private val onColorSelect: (ColorWindowItem) -> Unit,
        private val onColorDelete: (ColorWindowItem) -> Unit,
        private val onAddColor: () -> Unit,
        private val fillingColorFlag: Boolean,
        private val context: Context,
        private val showDefaultSelect: Boolean = true
    ) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
        val type = type
        var editMode: Boolean = false
            private set

        private var currentColor: Int = initialColor

        private var recentUseColorList: MutableList<Int> =
            ArrayList(RECENT_USE_COLOR_LIST_MAX_SIZE)

        var currentIndex: ColorIndex? = null
            set(value) {
                var oldAbsolutePosition: Int? = null
                var newAbsolutePosition: Int? = null
                field?.let {
                    oldAbsolutePosition = it.viewHolder?.bindingAdapterPosition
                }
                value?.let {
                    newAbsolutePosition = it.position
                    when (type) {
                        TYPE_TEXT -> {
                            if (it.position < presetColorList.size) {
                                EditEvent.sendTextColorUse("preset")
                            } else if (it.position < presetColorList.size + ADD_COLOR_ICON_COUNT + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + recentUseColorList.size) {
                                EditEvent.sendTextColorUse("recently")
                            } else {
                                val config = Configuration(context.resources.configuration)
                                config.setLocale(Locale.CHINA)
                                val title =
                                    context.createConfigurationContext(config).resources.getString(
                                        colorList.get(it.position).category
                                    )
                                EditEvent.sendTextColorUse(
                                    "title:${
                                        title
                                    }"
                                )
                            }
                        }
                        TYPE_HIGHLIGHTER -> {
                            if (it.position < presetColorList.size) {
                                EditEvent.sendHighlighterColorUse("preset")
                            } else if (it.position < presetColorList.size + ADD_COLOR_ICON_COUNT + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + recentUseColorList.size) {
                                EditEvent.sendHighlighterColorUse("recently")
                            } else {
                                val config = Configuration(context.resources.configuration)
                                config.setLocale(Locale.CHINA)
                                val title =
                                    context.createConfigurationContext(config).resources.getString(
                                        colorList.get(it.position).category
                                    )
                                EditEvent.sendHighlighterColorUse(
                                    "title:${
                                        title
                                    }"
                                )
                            }
                        }
                        else -> {
                            if (it.position < presetColorList.size) {
                                EditEvent.sendPaintBrushColorUse("preset")
                            } else if (it.position < presetColorList.size + ADD_COLOR_ICON_COUNT + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + recentUseColorList.size) {
                                EditEvent.sendPaintBrushColorUse("recently")
                            } else {
                                val config = Configuration(context.resources.configuration)
                                config.setLocale(Locale.CHINA)
                                val title =
                                    context.createConfigurationContext(config).resources.getString(
                                        colorList.get(it.position).category
                                    )
                                EditEvent.sendPaintBrushColorUse(
                                    "title:${
                                        title
                                    }"
                                )
                            }
                        }
                    }
                }
                field = value
                oldAbsolutePosition?.let {
                    notifyItemChanged(it)
                }
                newAbsolutePosition?.let {
                    notifyItemChanged(it)
                }
            }

        init {
            setHasStableIds(false)
            when (type) {
                TYPE_TEXT -> {
                    EditEvent.sendColorModeSelection("default", TEXT)
                    for (color in UserUsageConfig.textRecentUseColors) {
                        recentUseColorList.add(
                            color
                        )
                    }
                }
                TYPE_HIGHLIGHTER -> {
                    EditEvent.sendColorModeSelection("default", HIGHLIGHTER)
                    for (color in UserUsageConfig.highlighterRecentUseColors) {
                        recentUseColorList.add(
                            color
                        )
                    }
                }
                else -> {
                    EditEvent.sendColorModeSelection("default", PAINTBRUSH)
                    for (color in UserUsageConfig.penRecentUseColors) {
                        recentUseColorList.add(
                            color
                        )
                    }
                }
            }
            if (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                recentUseColorList = recentUseColorList.subList(0, 9)
            }
            for (index in colorList.indices) {
                if (colorList[index].res == currentColor && colorList[index].type.ordinal != ColorWindowItemType.ADD_COLOR.ordinal) {
                    currentIndex = ColorIndex(index, null)
                    break
                }
            }
            if (currentIndex == null) {
                var presetColorListSize: Int = presetColorList.size
                colorList.add(
                    presetColorListSize + ADD_COLOR_ICON_COUNT + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST,
                    ColorWindowItem(currentColor, ColorWindowItemType.RECENT_USE_COLOR)
                )
                when (type) {
                    TYPE_TEXT -> {
                        while (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                            recentUseColorList.removeAt(recentUseColorList.size - 1)
                        }
                        recentUseColorList.add(0, currentColor)
                        UserUsageConfig.textRecentUseColors = recentUseColorList
                    }
                    TYPE_HIGHLIGHTER -> {
                        while (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                            recentUseColorList.removeAt(recentUseColorList.size - 1)
                        }
                        recentUseColorList.add(0, currentColor)
                        UserUsageConfig.highlighterRecentUseColors = recentUseColorList
                    }
                    else -> {
                        while (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                            recentUseColorList.removeAt(recentUseColorList.size - 1)
                        }
                        recentUseColorList.add(0, currentColor)
                        UserUsageConfig.penRecentUseColors = recentUseColorList
                    }
                }
                currentIndex = ColorIndex(
                    presetColorListSize + ADD_COLOR_ICON_COUNT + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST,
                    null
                )
            }
        }

        fun addAndSetColor(color: Int, fillingColorFlag: Boolean = false): Boolean {
            return if (!recentUseColorList.contains(color)) {
                while (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                    var presetColorListSize: Int = presetColorList.size
                    colorList.removeAt(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                    notifyItemRemoved(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                    recentUseColorList.removeAt(recentUseColorList.size - 1)
                }
                recentUseColorList.add(
                    0,
                    color
                )
                when (type) {
                    TYPE_TEXT -> {
                        UserUsageConfig.textRecentUseColors = recentUseColorList
                    }
                    TYPE_HIGHLIGHTER -> {

                        UserUsageConfig.highlighterRecentUseColors = recentUseColorList
                    }
                    else -> {
                        UserUsageConfig.penRecentUseColors = recentUseColorList
                    }
                }
                var presetColorListSize: Int = presetColorList.size
                colorList.add(
                    presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT,
                    ColorWindowItem(color, ColorWindowItemType.RECENT_USE_COLOR)
                )
                notifyItemInserted(presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT)

                if (!fillingColorFlag) {
                    currentColor = color
                    currentIndex = ColorIndex(
                        presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT,
                        null
                    )
                } else {
                    currentIndex?.viewHolder?.let {
                        currentIndex = ColorIndex(it.bindingAdapterPosition, null)
                    }
                }
                true
            } else {
                false
            }
        }

        override fun getItemViewType(position: Int): Int {
            return when (colorList.get(position).type) {
                ColorWindowItemType.TITLE -> ITEM_TYPE_TITLE
                else -> ITEM_TYPE_COLOR
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return when (viewType) {
                ITEM_TYPE_TITLE -> TitleViewHolder(
                    PhoneColorListTitleBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )

                else -> ColorViewHolder(
                    PhoneColorListItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }
        }

        override fun onBindViewHolder(
            holder: RecyclerView.ViewHolder,
            position: Int,
            payloads: MutableList<Any>
        ) {
            if (payloads.isNotEmpty() && payloads[0] == ChangeType.EDIT_MODE) {
                if (holder.itemViewType == ITEM_TYPE_COLOR) {
                    val curIndex = currentIndex
                    if (colorList.get(position).type != ColorWindowItemType.ADD_COLOR) {
                        val selected = if (curIndex != null) {
                            if (curIndex.position == position) {
                                currentIndex?.viewHolder = holder
                                true
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                        val colorItem = colorList[position]
                        val presetColorListSize: Int =
                            if (fillingColorFlag) presetColorList.size + 1 else presetColorList.size
                        if (position < presetColorListSize) {
                            (holder as ColorViewHolder).changeEditMode(
                                colorItem,
                                editMode,
                                selected,
                                onColorClick = {
                                    currentColor = colorItem.res
                                    onColorSelect(colorItem)
                                    if (!recentUseColorList.contains(colorItem.res) && currentColor != Color.TRANSPARENT) {
                                        var presetColorListSize: Int = presetColorList.size
                                        while (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                                            colorList.removeAt(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                                            notifyItemRemoved(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                                            recentUseColorList.removeAt(recentUseColorList.size - 1)
                                        }
                                        recentUseColorList.add(
                                            0,
                                            colorItem.res
                                        )
                                        colorList.add(
                                            presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT,
                                            ColorWindowItem(
                                                colorItem.res,
                                                ColorWindowItemType.RECENT_USE_COLOR
                                            )
                                        )
                                        notifyItemInserted(presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT)
                                    }
                                    currentIndex = ColorIndex(holder.bindingAdapterPosition, holder)
                                    when (type) {
                                        TYPE_TEXT -> {
                                            UserUsageConfig.textRecentUseColors = recentUseColorList
                                        }
                                        TYPE_HIGHLIGHTER -> {
                                            UserUsageConfig.highlighterRecentUseColors =
                                                recentUseColorList
                                        }
                                        else -> {
                                            UserUsageConfig.penRecentUseColors = recentUseColorList
                                        }
                                    }
                                },
                                onDeleteClick = {
                                    val index = colorList.indexOf(colorItem)
                                    if (index >= 0) {
                                        colorList.removeAt(index)
                                        if (index == currentIndex?.position) {
                                            currentIndex = null
                                        }
                                        notifyItemRemoved(index)
                                        currentIndex?.let {
                                            if (it.position > index) {
                                                currentIndex = ColorIndex(it.position - 1, null)
                                            }
                                        }
                                        presetColorList.removeAt(index)
                                        when (type) {
                                            TYPE_TEXT -> EditEvent.sendColorEditSet("delete", TEXT)
                                            TYPE_HIGHLIGHTER -> EditEvent.sendColorEditSet(
                                                "delete",
                                                HIGHLIGHTER
                                            )
                                            else -> EditEvent.sendColorEditSet("delete", PAINTBRUSH)
                                        }
                                    }
                                    onColorDelete(colorItem)
                                },
                                fillingColorFlag,
                                context,
                                showDefaultSelect
                            )
                        } else {
                            (holder as ColorViewHolder).changeEditMode(
                                colorItem,
                                editMode,
                                selected,
                                onColorClick = {
                                    currentColor = colorItem.res
                                    onColorSelect(colorItem)
                                    if (!recentUseColorList.contains(colorItem.res)) {
                                        var presetColorListSize: Int = presetColorList.size
                                        while (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                                            colorList.removeAt(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                                            notifyItemRemoved(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                                            recentUseColorList.removeAt(recentUseColorList.size - 1)
                                        }
                                        recentUseColorList.add(
                                            0,
                                            colorItem.res
                                        )


                                        colorList.add(
                                            presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT,
                                            ColorWindowItem(
                                                colorItem.res,
                                                ColorWindowItemType.RECENT_USE_COLOR
                                            )
                                        )
                                        notifyItemInserted(presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT)
                                    }
                                    currentIndex = ColorIndex(holder.bindingAdapterPosition, holder)
                                    when (type) {
                                        TYPE_TEXT -> {
                                            UserUsageConfig.textRecentUseColors = recentUseColorList
                                        }
                                        TYPE_HIGHLIGHTER -> {
                                            UserUsageConfig.highlighterRecentUseColors =
                                                recentUseColorList
                                        }
                                        else -> {
                                            UserUsageConfig.penRecentUseColors = recentUseColorList
                                        }
                                    }
                                },
                                onDeleteClick = null,
                                fillingColorFlag,
                                context,
                                showDefaultSelect
                            )
                        }

                        return
                    }
                }
                super.onBindViewHolder(holder, position, payloads)
            } else {
                super.onBindViewHolder(holder, position, payloads)
            }
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            val curIndex = currentIndex
            when (holder.itemViewType) {
                ITEM_TYPE_TITLE -> {
                    (holder as TitleViewHolder).apply {
                        title.text = context.resources.getString(colorList[position].res)
                    }
                }
                else -> {
                    var presetColorListSize: Int = presetColorList.size
                    if (position < presetColorListSize) {
                        val selected = if (curIndex != null) {
                            if (curIndex.position == position) {
                                currentIndex?.viewHolder = holder
                                true
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                        val colorItem = colorList[position]
                        (holder as ColorViewHolder).bind(
                            colorItem,
                            editMode,
                            selected,
                            type,
                            onColorClick = {
                                currentColor = colorItem.res
                                if (!recentUseColorList.contains(colorItem.res) && currentColor != Color.TRANSPARENT) {
                                    var presetColorListSize: Int = presetColorList.size
                                    while (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                                        colorList.removeAt(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                                        notifyItemRemoved(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                                        recentUseColorList.removeAt(recentUseColorList.size - 1)
                                    }
                                    recentUseColorList.add(
                                        0,
                                        colorItem.res
                                    )
                                    colorList.add(
                                        presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT,
                                        ColorWindowItem(
                                            colorItem.res,
                                            ColorWindowItemType.RECENT_USE_COLOR
                                        )
                                    )
                                    notifyItemInserted(presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT)
                                }
                                currentIndex = ColorIndex(holder.bindingAdapterPosition, holder)
                                onColorSelect(colorItem)
                                when (type) {
                                    TYPE_TEXT -> {
                                        UserUsageConfig.textRecentUseColors = recentUseColorList
                                    }
                                    TYPE_HIGHLIGHTER -> {
                                        UserUsageConfig.highlighterRecentUseColors =
                                            recentUseColorList
                                    }
                                    else -> {
                                        UserUsageConfig.penRecentUseColors = recentUseColorList
                                    }
                                }
                            },
                            onDeleteClick = {
                                val index = colorList.indexOf(colorItem)
                                if (index >= 0) {
                                    colorList.removeAt(index)
                                    if (index == currentIndex?.position) {
                                        currentIndex = null
                                    }
                                    notifyItemRemoved(index)
                                    currentIndex?.let {
                                        if (it.position > index) {
                                            currentIndex = ColorIndex(it.position - 1, null)
                                        }
                                    }
                                    presetColorList.removeAt(index)
                                }
                                onColorDelete(colorItem)
                            },
                            fillingColorFlag,
                            position,
                            context,
                            showDefaultSelect
                        )
                    } else if (colorList[position].type == ColorWindowItemType.ADD_COLOR) {
                        (holder as ColorViewHolder).bindAddPickView(
                            editMode,
                            colorList[position],
                            onAddColor
                        )
                    } else {
                        val colorItem = colorList[position]
                        val selected = if (curIndex != null) {
                            if (curIndex.position == position) {
                                currentIndex?.viewHolder = holder
                                true
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                        (holder as ColorViewHolder).bind(
                            colorItem,
                            editMode,
                            selected,
                            type,
                            onColorClick = {
                                currentColor = colorItem.res
                                if (!recentUseColorList.contains(colorItem.res)) {
                                    var presetColorListSize: Int = presetColorList.size
                                    while (recentUseColorList.size >= RECENT_USE_COLOR_LIST_MAX_SIZE) {
                                        colorList.removeAt(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                                        notifyItemRemoved(presetColorListSize + recentUseColorList.size + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST)
                                        recentUseColorList.removeAt(recentUseColorList.size - 1)
                                    }
                                    recentUseColorList.add(
                                        0,
                                        colorItem.res
                                    )
                                    colorList.add(
                                        presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT,
                                        ColorWindowItem(
                                            colorItem.res,
                                            ColorWindowItemType.RECENT_USE_COLOR
                                        )
                                    )
                                    notifyItemInserted(presetColorListSize + TITLE_COUNT_BEFORE_RECENT_USE_COLOR_LIST + ADD_COLOR_ICON_COUNT)
                                }
                                currentIndex = ColorIndex(holder.bindingAdapterPosition, holder)
                                onColorSelect(colorItem)
                                when (type) {
                                    TYPE_TEXT -> {
                                        UserUsageConfig.textRecentUseColors = recentUseColorList
                                    }
                                    TYPE_HIGHLIGHTER -> {
                                        UserUsageConfig.highlighterRecentUseColors =
                                            recentUseColorList
                                    }
                                    else -> {
                                        UserUsageConfig.penRecentUseColors = recentUseColorList
                                    }
                                }
                            },
                            onDeleteClick = null,
                            fillingColorFlag,
                            position,
                            context,
                            showDefaultSelect
                        )
                    }
                }
            }
        }

        override fun getItemCount(): Int = colorList.size

        override fun getItemId(position: Int): Long {
            val colorWindowItem = colorList[position]
            return (colorWindowItem.hashCode()).toLong()
        }

        fun changeEditMode(isEditMode: Boolean) {
            editMode = isEditMode
            if (editMode) {
                when (type) {
                    TYPE_TEXT -> EditEvent.sendColorModeSelection("edit", TEXT)
                    TYPE_HIGHLIGHTER -> EditEvent.sendColorModeSelection("edit", HIGHLIGHTER)
                    else -> EditEvent.sendColorModeSelection("edit", PAINTBRUSH)
                }
            } else {
                when (type) {
                    TYPE_TEXT -> EditEvent.sendColorModeSelection("default", TEXT)
                    TYPE_HIGHLIGHTER -> EditEvent.sendColorModeSelection("default", HIGHLIGHTER)
                    else -> EditEvent.sendColorModeSelection("default", PAINTBRUSH)
                }
            }
            notifyItemRangeChanged(0, itemCount, ChangeType.EDIT_MODE)
        }

        private enum class ChangeType {
            EDIT_MODE
        }

    }

    private class ColorViewHolder(private val binding: PhoneColorListItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        var color: ColorWindowItem? = null

        fun changeEditMode(
            item: ColorWindowItem,
            editMode: Boolean,
            selected: Boolean,
            onColorClick: () -> Unit,
            onDeleteClick: (() -> Unit)? = null,
            fillingColorFlag: Boolean,
            context: Context,
            showDefaultSelect: Boolean
        ) {
            color = item
            val isTransparent = item.res == Color.TRANSPARENT
            binding.colorDelete.visibility =
                if (editMode && !isTransparent && item.type == ColorWindowItemType.PRESET_COLOR) {
                    View.VISIBLE
                } else {
                    View.INVISIBLE
                }
            binding.colorSelect.visibility = if (selected && showDefaultSelect) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }

            if (fillingColorFlag && isTransparent) {
                if (selected) {
                    binding.colorSelect.setImageResource(R.drawable.phone_filling_selected)
                } else {
                    binding.colorSelect.visibility = View.VISIBLE
                    binding.colorSelect.setImageResource(R.drawable.phone_filling_unselected)
                }
                val layoutParams = binding.colorSelect.layoutParams
                layoutParams.width = context.resources.getDimensionPixelSize(R.dimen.dp_88)
                binding.colorSelect.layoutParams = layoutParams
            } else {
                binding.colorSelect.setImageResource(R.drawable.phone_color_list_item_icon_selected)
                val layoutParams = binding.colorSelect.layoutParams
                layoutParams.width = context.resources.getDimensionPixelSize(R.dimen.dp_45)
                binding.colorSelect.layoutParams = layoutParams
            }
            binding.root.setOnClickListener {
                if (editMode && !isTransparent) {
                    onDeleteClick?.invoke()
                } else {
                    onColorClick()
                }
            }
            //长按进入编辑模式消费事件，屏蔽down，up点击事件
            binding.root.setOnLongClickListener {
                true
            }
        }

        fun bind(
            item: ColorWindowItem,
            editMode: Boolean,
            selected: Boolean,
            type: Int,
            onColorClick: () -> Unit,
            onDeleteClick: (() -> Unit)? = null,
            fillingColorFlag: Boolean,
            position: Int,
            context: Context,
            showDefaultSelect: Boolean
        ) {
            color = item
            val isTransparent = item.res == Color.TRANSPARENT
            changeEditMode(
                item,
                editMode,
                selected,
                onColorClick,
                onDeleteClick,
                fillingColorFlag,
                context,
                showDefaultSelect
            )
            binding.colorAdd.visibility = View.INVISIBLE
            binding.colorBackground.apply {
                visibility = View.VISIBLE
                val drawable = (background as? LayerDrawable)
                val colorDrawable = drawable?.findDrawableByLayerId(R.id.color)
                if (colorDrawable is GradientDrawable) {
                    if (isTransparent) {
                        colorDrawable.color = ColorStateList.valueOf(Color.WHITE)
                    } else {
                        colorDrawable.color = ColorStateList.valueOf(item.res)
                    }
                }
                val shadowDrawable = drawable?.findDrawableByLayerId(R.id.shadow)
                if (shadowDrawable is GradientDrawable) {

                    if (type == TYPE_HIGHLIGHTER) {
                        val shadowColor =
                            context.getColor(R.color.highlighter_color_list_item_shadow)
                        shadowDrawable.colors = intArrayOf(
                            shadowColor,
                            Color.TRANSPARENT
                        )
                        shadowDrawable.color = ColorStateList.valueOf(shadowColor)
                    } else {
                        val shadowColor = context.getColor(R.color.color_list_item_shadow)
                        shadowDrawable.colors = intArrayOf(
                            shadowColor,
                            Color.TRANSPARENT
                        )
                        shadowDrawable.color = ColorStateList.valueOf(shadowColor)
                    }

                }

                background = drawable
            }
        }

        fun bindAddPickView(editMode: Boolean, item: ColorWindowItem, onAddColor: () -> Unit) {
            color = item
            binding.root.setOnLongClickListener {
                true
            }
            binding.root.setOnClickListener {
                onAddColor()
            }
            binding.colorAdd.visibility = View.VISIBLE
            binding.colorDelete.visibility = View.INVISIBLE
            binding.colorSelect.visibility = View.INVISIBLE
            binding.colorBackground.visibility = View.INVISIBLE
        }
    }

    private class TitleViewHolder(private val binding: PhoneColorListTitleBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val title = binding.title
    }


    data class ColorIndex(val position: Int, var viewHolder: RecyclerView.ViewHolder?)
}
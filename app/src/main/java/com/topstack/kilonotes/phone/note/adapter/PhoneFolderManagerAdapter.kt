package com.topstack.kilonotes.phone.note.adapter

import android.content.Context
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.doc.Folder
import com.topstack.kilonotes.databinding.PhoneItemFolderListBinding
import com.topstack.kilonotes.phone.note.adapter.PhoneNoteListAdapter.NoteListFolderAdapter

class PhoneFolderManagerAdapter(
    val context: Context,
    private var folders: MutableList<Folder>,
) :
    RecyclerView.Adapter<PhoneFolderManagerAdapter.FolderManagerViewHolder>() {


    private var currentPosition = 0

    private var onSelectChanged: ((position: Int) -> Unit)? = null

    fun setSelectChanged(changed: ((position: Int) -> Unit)) {
        onSelectChanged = changed
    }

    companion object {
        const val FOLDER_SPAN_COUNT = 3
        const val ITEM_UPDATE = 100
    }


    class FolderManagerViewHolder(val binding: PhoneItemFolderListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val title = binding.title
        val noteFolder = binding.noteFolder
        val checkBox = binding.checkbox
        val root = binding.container

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FolderManagerViewHolder {
        var binding =
            PhoneItemFolderListBinding.inflate(LayoutInflater.from(context), parent, false)
        val folderInsideAdapter = NoteListFolderAdapter(context, 0.45f)
        binding.noteFolder.apply {
            adapter = folderInsideAdapter
            layoutManager = object : GridLayoutManager(
                context,
                FOLDER_SPAN_COUNT,
                RecyclerView.VERTICAL,
                false
            ) {
                override fun canScrollHorizontally(): Boolean {
                    return false
                }

                override fun canScrollVertically(): Boolean {
                    return false
                }
            }
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                private val itemWidth = context.resources.getDimensionPixelSize(R.dimen.dp_39)
                private val itemHeight = context.resources.getDimensionPixelSize(R.dimen.dp_45)
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    val childAdapterPosition = parent.getChildAdapterPosition(view)
                    val column = childAdapterPosition % FOLDER_SPAN_COUNT
                    val row = childAdapterPosition / FOLDER_SPAN_COUNT
                    val viewPortWidth = parent.width - parent.paddingStart - parent.paddingEnd
                    val viewPortHeight = parent.height - parent.paddingTop - parent.paddingBottom
                    val spaceV =
                        (viewPortHeight - FOLDER_SPAN_COUNT * itemHeight) / (FOLDER_SPAN_COUNT - 1)
                    if (row != 0) {
                        outRect.top = spaceV
                    }
                    val spaceH =
                        (viewPortWidth - FOLDER_SPAN_COUNT * itemWidth) / (FOLDER_SPAN_COUNT - 1)
                    // p为每个Item都需要减去的间距
                    val p =
                        (FOLDER_SPAN_COUNT - 1) * spaceH * 1f / FOLDER_SPAN_COUNT
                    val left = column * (spaceH - p)
                    val right = p - left
                    outRect.left = left.toInt()
                    outRect.right = right.toInt()
                }
            })
        }
        return FolderManagerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: FolderManagerViewHolder, position: Int) {
        var folder = folders.get(position)
        holder.title.text = folder.title
        var adapter = holder.noteFolder.adapter as NoteListFolderAdapter
        adapter.setFolder(folder)
        holder.checkBox.isSelected = currentPosition == position
        holder.root.setOnClickListener {
            holder.checkBox.isSelected = !holder.checkBox.isSelected
            setCurrentPosition(position)
        }
    }

    override fun onBindViewHolder(
        holder: FolderManagerViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isNotEmpty()) {
            var param = payloads.get(0)
            if (param.equals(ITEM_UPDATE)) {
                holder.checkBox.isSelected = currentPosition == position
                return
            }
        }
        super.onBindViewHolder(holder, position, payloads)
    }

    override fun getItemCount(): Int {
        return folders.size
    }

    fun addFolder(folder: Folder) {
        folders.add(0, folder)
        currentPosition = 0
        notifyItemRangeChanged(0,folders.size)
        notifyItemInserted(0)
    }

    private fun setCurrentPosition(position: Int) {
        val oldValue = currentPosition
        currentPosition = position
        notifyItemChanged(oldValue, ITEM_UPDATE)
        onSelectChanged?.invoke(position)
    }
}
package com.topstack.kilonotes.base.vip.adapter

import android.content.Context
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.TextViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.infra.device.DeviceUtils
import com.topstack.kilonotes.infra.util.AppUtils

abstract class BaseVipUserStatusAdapter(context: Context, @LayoutRes layoutResId: Int): RecyclerView.Adapter<BaseVipUserStatusAdapter.VipUserStatusViewHolder>(){

    private var onOpenVipClick: (() -> Unit)? = null
    private var currentLayoutResId: Int = layoutResId
    private var currentContext = context

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VipUserStatusViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(currentLayoutResId, parent, false)
        return VipUserStatusViewHolder(currentContext, itemView)
    }

    override fun getItemCount(): Int {
        return 1
    }

    override fun onBindViewHolder(holder: VipUserStatusViewHolder, position: Int) {
        holder.vipContainer.setOnClickListener {
            count()
            onOpenVipClick?.invoke()
        }

        if (UserManager.isVip()) {
            when (UserManager.vipType) {
                UserManager.VIP_TYPE_ANNUALLY, UserManager.VIP_TYPE_PERMANENT -> {
                    holder.jumpToUserLoginBtn.apply {
                        text = AppUtils.getString(R.string.vip_center)
                        setTextColor(AppUtils.getColor(R.color.white))
                        background =
                            AppUtils.getDrawable(R.drawable.pad_vip_center_confirm_background)
                    }
                    holder.jumpToUserLoginBackground.setImageResource(if(DeviceUtils.isPhoneType(KiloApp.deviceType)) R.drawable.phone_vip_gold_membership_entrance_background else R.drawable.vip_store_golden_membership_background)
                    holder.purchaseSlogan.apply {
                        text = AppUtils.getString(R.string.purchase_slogan)
                        setTextColor(AppUtils.getColor(R.color.vip_store_vip_state_bar_purchase_gold_vip_slogan_color))
                    }
                    holder.vipIcon.setImageResource(if(DeviceUtils.isPhoneType(KiloApp.deviceType)) R.drawable.phone_vip_gold_membership_emblem else R.drawable.vip_store_golden_membership_icon)
                }

                UserManager.VIP_TYPE_QUARTERLY -> {
                    holder.jumpToUserLoginBtn.apply {
                        text = AppUtils.getString(R.string.vip_center)
                        setTextColor(AppUtils.getColor(R.color.white))
                        background =
                            AppUtils.getDrawable(R.drawable.pad_vip_center_confirm_background)
                    }
                    holder.jumpToUserLoginBackground.setImageResource(if(DeviceUtils.isPhoneType(KiloApp.deviceType)) R.drawable.phone_vip_platinum_membership_entrance_background else R.drawable.vip_store_platinum_membership_background)

                    holder.purchaseSlogan.apply {
                        text = AppUtils.getString(R.string.purchase_slogan)
                        setTextColor(AppUtils.getColor(R.color.vip_store_vip_state_bar_purchase_platinum_vip_slogan_color))
                    }
                    holder.vipIcon.setImageResource(if(DeviceUtils.isPhoneType(KiloApp.deviceType)) R.drawable.phone_vip_platinum_membership_emblem else R.drawable.vip_store_platinum_membership_icon)
                }

                UserManager.VIP_TYPE_MONTHLY, UserManager.VIP_TYPE_WEEKLY -> {
                    holder.jumpToUserLoginBtn.apply {
                        text = AppUtils.getString(R.string.vip_center)
                        setTextColor(AppUtils.getColor(R.color.white))
                        background =
                            AppUtils.getDrawable(R.drawable.pad_vip_center_confirm_background)
                    }
                    holder.jumpToUserLoginBackground.setImageResource(if(DeviceUtils.isPhoneType(KiloApp.deviceType)) R.drawable.phone_vip_silver_membership_entrance_background else R.drawable.vip_store_silver_membership_background)

                    holder.purchaseSlogan.apply {
                        text = AppUtils.getString(R.string.purchase_slogan)
                        setTextColor(AppUtils.getColor(R.color.vip_store_vip_state_bar_purchase_silver_vip_slogan_color))
                    }
                    holder.vipIcon.setImageResource(if(DeviceUtils.isPhoneType(KiloApp.deviceType)) R.drawable.phone_vip_silver_membership_emblem else R.drawable.vip_store_silver_membership_icon)
                }
            }
        } else {
            holder.jumpToUserLoginBtn.apply {
                text = AppUtils.getString(R.string.go_to_vip)
                background = AppUtils.getDrawable(R.drawable.button_confirm_background)
            }
            holder.jumpToUserLoginBackground.setImageResource(if(DeviceUtils.isPhoneType(KiloApp.deviceType)) R.drawable.phone_vip_membership_entrance_background else R.drawable.vip_store_entrance_background)

            holder.purchaseSlogan.apply {
                text = AppUtils.getString(R.string.purchase_slogan_for_nonmember)
                setTextColor(AppUtils.getColor(R.color.black))
            }
            holder.vipIcon.setImageResource(if(DeviceUtils.isPhoneType(KiloApp.deviceType)) R.drawable.phone_vip_gold_membership_emblem else R.drawable.vip_store_golden_membership_icon)
        }
    }

    fun setOpenVipListener(onClickListener:() -> Unit){
        onOpenVipClick =  onClickListener
    }

    abstract fun count()

    class VipUserStatusViewHolder(context: Context, val itemView: View) : RecyclerView.ViewHolder(itemView){
        val jumpToUserLoginBtn : TextView = itemView.findViewById(R.id.jump_to_user_login_btn)
        val jumpToUserLoginBackground : ImageView = itemView.findViewById(R.id.jump_to_user_login_background)
        val purchaseSlogan: AppCompatTextView = itemView.findViewById(R.id.purchase_slogan)
        val vipIcon: ImageView = itemView.findViewById(R.id.vip_icon)
        var vipContainer: ConstraintLayout = itemView.findViewById(R.id.vip_container)

        init {
            val resources = context.resources
            TextViewCompat.setAutoSizeTextTypeWithDefaults(
                purchaseSlogan,
                TextViewCompat.AUTO_SIZE_TEXT_TYPE_UNIFORM
            )
            TextViewCompat.setAutoSizeTextTypeUniformWithConfiguration(
                purchaseSlogan, resources.getDimensionPixelSize(
                    R.dimen.sp_20
                ), resources.getDimensionPixelSize(
                    R.dimen.sp_40
                ), resources.getDimensionPixelSize(
                    R.dimen.sp_1
                ), TypedValue.COMPLEX_UNIT_PX
            )
        }
    }

}
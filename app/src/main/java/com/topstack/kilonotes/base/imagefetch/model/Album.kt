package com.topstack.kilonotes.base.imagefetch.model

import android.os.Parcel
import android.os.Parcelable
import com.topstack.kilonotes.base.util.readBooleanCompat
import com.topstack.kilonotes.base.util.writeBooleanCompat

data class Album(
    val aid: String,
    val name: String,
    val isPredefined: Boolean = false
) : Parcelable {
    companion object {

        @JvmField
        val CREATOR: Parcelable.Creator<Album> = object : Parcelable.Creator<Album> {
            override fun createFromParcel(`in`: Parcel): Album {
                val aid: String = `in`.readString() ?: ""
                val name: String = `in`.readString() ?: ""
                val imageArray: Array<Image> = `in`.createTypedArray(Image.CREATOR) ?: emptyArray()

                val isPredefined: Boolean = `in`.readBooleanCompat()
                return Album(aid, name, isPredefined).apply {
                    images.addAll(imageArray)
                }
            }

            override fun newArray(size: Int): Array<Album?> {
                return arrayOfNulls(size)
            }
        }
    }

    var images: MutableList<Image> = mutableListOf()

    var count: Int = 0
        get() = images.size
        private set

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(aid)
        parcel.writeString(name)
        parcel.writeTypedArray(images.toTypedArray(), flags)

        parcel.writeBooleanCompat(isPredefined)
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Album

        if (aid != other.aid) return false
        if (name != other.name) return false
        if (isPredefined != other.isPredefined) return false
        if (count != other.count) return false

        return true
    }

    override fun hashCode(): Int {
        var result = aid.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + isPredefined.hashCode()
        return result
    }


}
package com.topstack.kilonotes.base.doodle.views.selectview;

import android.content.ClipData;
import android.content.ClipDescription;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.net.Uri;
import android.os.Build;
import android.os.PersistableBundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.topstack.kilonotes.KiloApp;
import com.topstack.kilonotes.R;
import com.topstack.kilonotes.base.constant.MimeType;
import com.topstack.kilonotes.base.db.HandbookDatabase;
import com.topstack.kilonotes.base.doc.io.ResourceManager;
import com.topstack.kilonotes.base.doodle.listeners.ICallReplaceImageListener;
import com.topstack.kilonotes.base.doodle.listeners.ILongPressListener;
import com.topstack.kilonotes.base.doodle.listeners.ISingleTapUpListener;
import com.topstack.kilonotes.base.doodle.manager.modelmager.IInsertableObjectLayerEditor;
import com.topstack.kilonotes.base.doodle.manager.modelmager.IModelManager;
import com.topstack.kilonotes.base.doodle.model.InsertableObject;
import com.topstack.kilonotes.base.doodle.model.InsertableObjectType;
import com.topstack.kilonotes.base.doodle.model.LassoType;
import com.topstack.kilonotes.base.doodle.model.image.InsertableBitmap;
import com.topstack.kilonotes.base.doodle.views.doodleview.DoodleView;
import com.topstack.kilonotes.base.doodle.views.doodleview.InputMode;
import com.topstack.kilonotes.base.doodle.views.guideline.GuideLineLayer;
import com.topstack.kilonotes.infra.device.DeviceUtils;
import com.topstack.kilonotes.infra.draganddrop.DragShadowWithAddIconBuilder;
import com.topstack.kilonotes.infra.foundation.thread.ThreadUtils;
import com.topstack.kilonotes.infra.util.LogHelper;
import com.topstack.kilonotes.infra.util.Md5Utils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.function.Consumer;

import kotlinx.coroutines.Dispatchers;

/**
 *
 */
public class SelectViewTwoDrag extends SelectView<List<InsertableObject>> {

    private DoodleView mDoodleView;

    private IModelManager modelManager;

    private ImageToolWindow imageToolWindow;

    private GraphicLayer panel;

    private GuideLineLayer guideLineLayer;

    private RotateLayer rotateDragLayer = null;

    private ClickableAnchorLayer cancelLayer = null;

    private ScaleLayer uniformScaleLayer = null;

    private ScaleLayer horizontalScaleLayer = null;

    private ScaleLayer verticalScaleLayer = null;

    private boolean mNotShowToolWindow = false;

    public static final String SHARE_IMAGE_LABEL = "kilonotes-shareImage";
    public static final String SHARE_IMAGE_EXTRA_INFO_ALPHA = "alpha";
    private static final String TAG = "SelectViewTwoDrag";


    public SelectViewTwoDrag(Context context, List<InsertableObject> object, DoodleView parentView) {
        super(context, object, parentView);
        mDoodleView = parentView;
        modelManager = parentView.getModelManager();
    }

    @Override
    protected void createPanelLayer(LayerParent layerParent) {
        panel = createRestrictRegion(layerParent);
    }

    @Override
    protected void onCreateDecorateLayer(LayerParent layerParent) {
        final boolean isPhone = DeviceUtils.INSTANCE.isPhoneType(KiloApp.Companion.getDeviceType());

        //这个是选中后的- - -
        final Rect region = panel.getSrcRect();
        final Matrix matrix = panel.getMatrix();

        float[] points = {region.left, region.centerY(), region.right, region.centerY()};
        matrix.mapPoints(points);
        float initDegree = (float) Math.toDegrees(Math.atan2(points[3] - points[1], points[2] - points[0]));

        Resources res = mContext.getResources();
        Bitmap cancelBitmap = BitmapFactory.decodeResource(res, R.drawable.doodle_element_delete);
        Bitmap rotateBitmap = BitmapFactory.decodeResource(res, R.drawable.doodle_element_rotation);
        Bitmap vScaleBitmap = BitmapFactory.decodeResource(res, R.drawable.doodle_element_scale_vertical);
        Bitmap hScaleBitmap = BitmapFactory.decodeResource(res, R.drawable.doodle_element_scale_horizontal);
        Bitmap uniformScaleBitmap = BitmapFactory.decodeResource(res, R.drawable.doodle_element_icon_scale);

        int padding = 0; // 扩大按钮触摸区域

        int btnSize = isPhone ?
                res.getDimensionPixelSize(R.dimen.phone_select_view_button_size)
                : res.getDimensionPixelSize(R.dimen.select_view_button_size);
        int halfBtnSize = btnSize / 2;

        int rotationBtnWidth = isPhone ?
                res.getDimensionPixelSize(R.dimen.phone_select_view_rotation_button_width)
                : res.getDimensionPixelSize(R.dimen.select_view_rotation_button_width);
        int halfRotationBtnWidth = rotationBtnWidth / 2;
        int rotationBtnHeight = isPhone ?
                res.getDimensionPixelSize(R.dimen.phone_select_view_rotation_button_height)
                : res.getDimensionPixelSize(R.dimen.select_view_rotation_button_height);
        int halfRotationBtnHeight = rotationBtnHeight / 2;

        int rotationOffsetY = isPhone ?
                res.getDimensionPixelSize(R.dimen.phone_select_view_rotation_button_offset)
                : res.getDimensionPixelSize(R.dimen.select_view_rotation_button_offset);
        rotateDragLayer = LayerFactory.createRotateDragLayer
                (panel, new PointF(region.centerX(), region.bottom),
                        new RectF(region.right - halfRotationBtnWidth - padding, region.bottom - halfRotationBtnHeight - padding, region.right + halfRotationBtnWidth + padding, region.bottom + halfRotationBtnHeight + padding),
                        Gravity.CENTER, 0F, halfRotationBtnHeight - rotationOffsetY, new LayerFactory.BitmapDrawable(rotateBitmap,
                                new Paint(Paint.ANTI_ALIAS_FLAG), new Rect(padding, padding, rotationBtnWidth + padding, rotationBtnHeight + padding)),
                        layerParent, panel, initDegree);
        rotateDragLayer.setClipRect(mDoodleView.getClipRect());

        cancelLayer = LayerFactory.createClickableAnchorLayer(
                panel, new PointF(region.left, region.top),
                new RectF(region.left - halfBtnSize - padding, region.top - halfBtnSize - padding, region.left + halfBtnSize + padding, region.top + halfBtnSize + padding),
                new LayerFactory.BitmapDrawable(cancelBitmap, new Paint(Paint.ANTI_ALIAS_FLAG), new Rect(padding, padding, btnSize + padding, btnSize + padding)),
                layerParent, initDegree);
        cancelLayer.setClipRect(mDoodleView.getClipRect());
        cancelLayer.setClickedListener(() -> {
            if (mDeleteListener != null) {
                mDeleteListener.onClicked();
            }
        });

        uniformScaleLayer = LayerFactory.createScaleDragLayer
                (panel, new PointF(region.right, region.bottom),
                        new RectF(region.right - halfBtnSize - padding, region.bottom - halfBtnSize - padding, region.right + halfBtnSize + padding, region.bottom + halfBtnSize + padding),
                        new LayerFactory.BitmapDrawable(uniformScaleBitmap,
                                new Paint(Paint.ANTI_ALIAS_FLAG), new Rect(padding, padding, btnSize + padding, btnSize + padding)),
                        initDegree);
        uniformScaleLayer.setScaleMode(ScaleLayer.ScaleMode.UniformScale);
        uniformScaleLayer.setClipRect(mDoodleView.getClipRect());


        int scaleBtnWidth = isPhone ?
                res.getDimensionPixelSize(R.dimen.phone_select_view_scale_button_width)
                : res.getDimensionPixelSize(R.dimen.select_view_scale_button_width);
        int halfScaleBtnWidth = scaleBtnWidth / 2;
        int scaleBtnHeight = isPhone ?
                res.getDimensionPixelSize(R.dimen.phone_select_view_scale_button_height)
                : res.getDimensionPixelSize(R.dimen.select_view_scale_button_height);
        int halfScaleBtnHeight = scaleBtnHeight / 2;

        RectF regionF = new RectF(region);
        PointF anchor = new PointF(regionF.right, regionF.centerY());
        horizontalScaleLayer = LayerFactory.createScaleDragLayer
                (panel, anchor,
                        new RectF(anchor.x - halfScaleBtnWidth - padding, anchor.y - halfScaleBtnHeight - padding, anchor.x + halfScaleBtnWidth + padding, anchor.y + halfScaleBtnHeight + padding),
                        new LayerFactory.BitmapDrawable(hScaleBitmap,
                                new Paint(Paint.ANTI_ALIAS_FLAG), new Rect(padding, padding, scaleBtnWidth + padding, scaleBtnHeight + padding)),
                        initDegree);
        horizontalScaleLayer.setScaleMode(ScaleLayer.ScaleMode.KeepHeight);
        horizontalScaleLayer.setClipRect(mDoodleView.getClipRect());

        anchor = new PointF(regionF.centerX(), regionF.top);
        verticalScaleLayer = LayerFactory.createScaleDragLayer
                (panel, anchor,
                        new RectF(anchor.x - halfScaleBtnHeight - padding, anchor.y - halfScaleBtnWidth - padding, anchor.x + halfScaleBtnHeight + padding, anchor.y + halfScaleBtnWidth + padding),
                        new LayerFactory.BitmapDrawable(vScaleBitmap,
                                new Paint(Paint.ANTI_ALIAS_FLAG), new Rect(padding, padding, scaleBtnHeight + padding, scaleBtnWidth + padding)),
                        initDegree);
        verticalScaleLayer.setScaleMode(ScaleLayer.ScaleMode.KeepWidth);
        verticalScaleLayer.setClipRect(mDoodleView.getClipRect());
        updateLayers();
    }

    @Override
    protected void updateLayers(LayerParent layerParent) {
        if (isSingleImage()) {
            InsertableBitmap insertableBitmap = (InsertableBitmap) mData.get(0);
            if (insertableBitmap.isLocked()) {
                removeAllIconLayer(layerParent);
                layerParent.setInterruptTransformChanged(true);
                if (mShowView != null) {
                    mShowView.invalidate();
                }
                return;
            } else {
                layerParent.setInterruptTransformChanged(false);
            }
        }
        List<ILayer> layers = layerParent.getLayers();
        if (!layers.contains(rotateDragLayer) && rotateDragLayer != null) {
            layerParent.addLayer(rotateDragLayer);
        }
        if (!layers.contains(cancelLayer) && cancelLayer != null) {
            layerParent.addLayer(cancelLayer);
        }
        if (!layers.contains(uniformScaleLayer) && uniformScaleLayer != null) {
            layerParent.addLayer(uniformScaleLayer);
        }
        boolean needShowScaleLayer = true;
        for (InsertableObject object : mData) {
            if (object.getType() != InsertableObjectType.IMAGE) {
                needShowScaleLayer = false;
                break;
            }
        }

        if (!needShowScaleLayer) {
            if (mShowView != null) {
                mShowView.invalidate();
            }
            return;
        }
        if (!layers.contains(horizontalScaleLayer) && horizontalScaleLayer != null) {
            layerParent.addLayer(horizontalScaleLayer);
        }
        if (!layers.contains(verticalScaleLayer) && verticalScaleLayer != null) {
            layerParent.addLayer(verticalScaleLayer);
        }
        if (mShowView != null) {
            mShowView.invalidate();
        }
    }

    private void removeAllIconLayer(LayerParent layerParent) {
        List<ILayer> layers = layerParent.getLayers();
        if (rotateDragLayer != null) {
            layers.remove(rotateDragLayer);
        }
        if (cancelLayer != null) {
            layers.remove(cancelLayer);
        }
        if (uniformScaleLayer != null) {
            layers.remove(uniformScaleLayer);
        }
        if (horizontalScaleLayer != null) {
            layers.remove(horizontalScaleLayer);
        }
        if (verticalScaleLayer != null) {
            layers.remove(verticalScaleLayer);
        }
    }

    private ImageView dragImageView = null;

    @Override
    protected void createDragListener(LayerParent mLayerParent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (mData.size() == 1 && mData.get(0) instanceof InsertableBitmap) {
                mLayerParent.setLongPressListener(new ILongPressListener() {
                    @Override
                    public void onLongPress(@NonNull View v) {
                        if (mData.size() == 1) {
                            InsertableObject insertableObject = mData.get(0);

                            if (insertableObject instanceof InsertableBitmap && mDoodleView.getInputMode() == InputMode.IMAGE) {
                                InsertableBitmap insertableBitmap = (InsertableBitmap) insertableObject;
                                if (insertableBitmap.isLocked()) return;
                                String attachFilePath = insertableBitmap.getAttachFilePath();
                                ResourceManager resManager = insertableBitmap.getResManager();
                                Uri uri = null;
                                try {
                                    if (resManager != null) {
                                        File file = resManager.openFile(attachFilePath);
                                        uri = Uri.fromFile(file);
                                    }
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                                if (uri == null) return;
                                if (dragImageView == null) {
                                    int dragWidth;
                                    if (DeviceUtils.INSTANCE.isPadType(KiloApp.Companion.getDeviceType())) {
                                        dragWidth = mContext.getResources().getDimensionPixelSize(R.dimen.dp_200);
                                    } else {
                                        dragWidth = mContext.getResources().getDimensionPixelSize(R.dimen.dp_400);
                                    }
                                    dragImageView = new ImageView(mContext);
                                    dragImageView.setLayoutParams(new ViewGroup.LayoutParams(dragWidth, dragWidth));
                                    mDoodleView.addView(dragImageView);
                                }
                                dragImageView.setImageURI(uri);
                                dragImageView.setImageAlpha(220);
                                dragImageView.setVisibility(View.INVISIBLE);
                                ClipData.Item item = new ClipData.Item(uri);
                                ClipData clipData = new ClipData(SHARE_IMAGE_LABEL, new String[]{MimeType.IMAGE.getMime()}, item);
                                ClipDescription clipDescription = clipData.getDescription();
                                PersistableBundle bundle = new PersistableBundle();
                                bundle.putInt(SHARE_IMAGE_EXTRA_INFO_ALPHA, insertableBitmap.getAlpha());
                                clipDescription.setExtras(bundle);
                                View.DragShadowBuilder dragShadowBuilder;
                                int addIconSize = mContext.getResources().getDimensionPixelSize(R.dimen.dp_30);
                                //Android 9 有系统级bug，需要处理一下
                                dragShadowBuilder = new DragShadowWithAddIconBuilder(dragImageView, addIconSize) {
                                    @Override
                                    public void onProvideShadowMetrics(Point outShadowSize, Point outShadowTouchPoint) {
                                        ViewGroup.LayoutParams layoutParams = dragImageView.getLayoutParams();
                                        outShadowSize.set(layoutParams.width + addIconSize / 2, layoutParams.height + addIconSize / 2);
                                        outShadowTouchPoint.set(outShadowSize.x / 2, outShadowSize.y / 2);
                                        setOutWidth(outShadowSize.x);
                                        setOutHeight(outShadowSize.y);
                                    }
                                };
                                dragImageView.post(() -> {
                                    dragImageView.startDragAndDrop(clipData, dragShadowBuilder, dragShadowBuilder, 0);
                                });
                            }

                        }
                    }
                });
            }
        }

    }


    @Override
    protected void createOnClickListener(LayerParent mLayerParent) {
        mLayerParent.setSingleTapUpListener(new ISingleTapUpListener() {
            @Override
            public void onSingleTapUp(@NonNull View v) {

            }
        });
    }

    @Override
    protected void createOnDoubleTapListener(LayerParent mLayerParent) {
        mLayerParent.setOnDoubleTapListener(() -> {
            if (isSingleImage() && !((InsertableBitmap) mData.get(0)).isLocked()) {
                ICallReplaceImageListener listener = modelManager.getCallReplaceImageListener();
                if (!modelManager.getInConsoleState() && listener != null) {
                    listener.onCallReplace();
                }
            }
        });
    }

    //这里显示图形，可以直接用
    @Override
    protected void createBackgroundLayer(LayerParent layerParent) {
        Matrix matrix = new Matrix();
        matrix.postTranslate(-mSrcView.getScrollX(), -mSrcView.getScrollY());
        matrix.postConcat(mRenderMatrix);
        SelectedObjectsLayer bgLayer = new SelectedObjectsLayer(matrix, SelectViewPolicy.getSelectViewPolicy(mContext).getTouchRestrictTolerance(), mData, mDoodleView);

        layerParent.addLayer(bgLayer);
    }

    protected GraphicLayer createRestrictRegion(LayerParent layerParent) {
        final Resources resources = mContext.getResources();
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setStyle(Paint.Style.STROKE);
        paint.setColor(resources.getColor(R.color.select_view_frame_line_color));
        paint.setStrokeWidth(resources.getDimension(R.dimen.select_view_line_stroke_width));
        paint.setStrokeCap(Paint.Cap.ROUND);
        paint.setPathEffect(new DashPathEffect(new float[]{resources.getDimension(R.dimen.select_view_line_length), resources.getDimension(R.dimen.select_view_line_interval)}, 0));
        RectF unionRect = getDataRect();
        Matrix matrix = getRenderMatrix();

        RectF actionRectF = new RectF(unionRect.left, unionRect.top, unionRect.right, unionRect.bottom);
        Path actionPath = new Path();
        actionPath.addRect(actionRectF, Path.Direction.CCW);

        GraphicLayer panel = new GraphicLayer(mContext, matrix, new Rect((int) actionRectF.left, (int) actionRectF.top, (int) actionRectF.right, (int) actionRectF.bottom), new PathDrawable(actionPath, paint, mDoodleView.getClipRect()));
        panel.setSupportScale(mSupportScale);
        panel.setClipRect(mDoodleView.getClipRect());
        layerParent.addLayer(panel);

        return panel;
    }

    @Override
    protected void onCheckUpdateDataRect() {
        updateGuideLineLayerSelectedRect();
    }

    private void updateGuideLineLayerSelectedRect() {
        if (guideLineLayer != null) {
            RectF initRect = getDataRect();
            Matrix renderMatrix = getRenderMatrix();
            guideLineLayer.updateSelectedRect(initRect, renderMatrix);
        }
    }

    private RectF getDataRect() {
        final RectF unionRect = new RectF();
        if (mData.size() == 1 && mData.get(0).getType() == InsertableObjectType.IMAGE) {
            unionRect.set(mData.get(0).getInitRectF());
        } else {
            for (InsertableObject object : mData) {
                RectF objectRect = InsertableObject.getTransformedRectF(object);
                unionRect.union(objectRect);
            }
        }
        return unionRect;
    }

    private Matrix getRenderMatrix() {
        Matrix matrix = new Matrix();
        if (mData.size() == 1 && mData.get(0).getType() == InsertableObjectType.IMAGE) {
            matrix.set(mData.get(0).getMatrix());
        }
        matrix.postTranslate(-mSrcView.getScrollX(), -mSrcView.getScrollY());
        matrix.postConcat(mRenderMatrix);
        return matrix;
    }


    @Override
    public void dismissSelectView() {
        super.dismissSelectView();
        if (imageToolWindow != null) {
            imageToolWindow.dismiss();
        }
        if (dragImageView != null && dragImageView.getParent() != null) {
            mDoodleView.removeView(dragImageView);
        }
    }

    @Override
    protected void onSelectViewShow(View layerView, LayerParent layerParent, Set<LassoType> mSelectionContentType) {
        super.onSelectViewShow(layerView, layerParent, mSelectionContentType);
        if (mSelectionContentType.equals(LassoType.LassoImage.INSTANCE.singleType()) && !mNotShowToolWindow) {
            imageToolWindow = new ImageToolWindow(mContext, layerParent, toolType -> {
                mImageToolClickListener.onClick(toolType);
                if (toolType == ImageToolType.LOCK || toolType == ImageToolType.UNLOCK) {

                } else if (toolType == ImageToolType.CUTOUT) {
                    showCutoutToolWindow();
                } else {
                    dismissSelectView();
                }
            });
            showImageToolWindow();
        }
    }

    @Override
    protected void createGuideLineLayer(LayerParent layerParent) {
        guideLineLayer = new GuideLineLayer(mDoodleView);
        guideLineLayer.setGuideLineShowListener(isShow -> {
            for (ILayer layer : layerParent.getLayers()) {
                if (layer instanceof ClickableAnchorLayer) {
                    ClickableAnchorLayer clickableAnchorLayer = (ClickableAnchorLayer) layer;
                    clickableAnchorLayer.changeShowSwitch(!isShow);
                }

                if (layer instanceof GraphicLayer) {
                    GraphicLayer graphicLayer = (GraphicLayer) layer;
                    graphicLayer.changeShowSwitch(!isShow);
                }
            }
        });
        updateGuideLineLayerSelectedRect();
        layerParent.addLayer(guideLineLayer);
    }

    @Override
    public void onOutSidePressed() {
        super.onOutSidePressed();
        if (imageToolWindow != null && dragImageView == null) {
            imageToolWindow.dismiss();
        }
    }

    @Override
    public void onAction(SelectViewEnum.ActionType t, ILayer layer) {
        super.onAction(t, layer);
        if (t == SelectViewEnum.ActionType.begin) {
            if (imageToolWindow != null) {
                imageToolWindow.dismiss();
            }
        } else {
            showImageToolWindow();
        }
    }

    @Override
    public boolean hasEditing() {
        return mShowView != null && mShowView.getRenderLayerChildCount() > 0;
    }

    private static class PathDrawable implements IDrawableSelf {
        private final Path mSrcPath;
        private final Path mDrawPath = new Path();
        private final Paint mDrawPaint;
        private final Rect mClipRect;

        public PathDrawable(Path path, Paint drawPaint, Rect clipRect) {
            mSrcPath = new Path(path);
            mDrawPaint = new Paint(drawPaint);
            mClipRect = new Rect(clipRect);
        }

        @Override
        public void onDrawSelf(Canvas canvas) {
            canvas.drawPath(mDrawPath, mDrawPaint);
        }

        @Override
        public void onDrawSelf(Canvas canvas, Matrix matrix) {
            int saveCount = canvas.save();
            if (!mClipRect.isEmpty()) {
                canvas.clipRect(mClipRect);
            }
            mSrcPath.transform(matrix, mDrawPath);
            onDrawSelf(canvas);
            canvas.restoreToCount(saveCount);
        }
    }

    @Override
    public void resetSelectView() {
        super.resetSelectView();
        if (imageToolWindow != null) {
            imageToolWindow.dismiss();
        }
        imageToolWindow = null;
    }

    private boolean isSingleImage() {
        return mData.size() == 1 && mData.get(0) instanceof InsertableBitmap;
    }

    private void showImageToolWindow() {
        if (imageToolWindow == null) return;
        getImageToolMenus(imageToolTypes -> {
            ThreadUtils.postMainThread(() -> {
                if (mDoodleView != null && mDoodleView.isAttachedToWindow() && imageToolWindow != null) {
                    imageToolWindow.show(mDoodleView, imageToolTypes);
                }
            });
        });
    }

    private void getImageToolMenus(Consumer<List<ImageToolType>> menusCallback) {
        Executor ioExecutor = (Executor) Dispatchers.getIO();
        ioExecutor.execute(() -> {
            try {
                ArrayList<ImageToolType> menus = new ArrayList<>();
                boolean phoneType = DeviceUtils.INSTANCE.isPhoneType(KiloApp.Companion.getDeviceType());
                if (!isSingleImage()) {
                    menusCallback.accept(menus);
                    return;
                }
                InsertableBitmap insertableBitmap = (InsertableBitmap) mData.get(0);
                if (phoneType) {
                    if (insertableBitmap.isLocked()) {
                        menusCallback.accept(menus);
                        return;
                    } else {
                        if (!isInCustomMaterialOfInsertableBitmap(insertableBitmap)) {
                            menus.add(ImageToolType.SAVE);
                        }
                    }
                } else {
                    if (insertableBitmap.isLocked()) {
                        menus.add(ImageToolType.UNLOCK);
                        menusCallback.accept(menus);
                        return;
                    } else {
                        menus.add(ImageToolType.CROP);
                        if (mDoodleView.getDoodleModeConfig().getSupportJumpToInstantAlpha()) {
                            menus.add(ImageToolType.CUTOUT);
                        }
                        menus.add(ImageToolType.COPY);
                        menus.add(ImageToolType.TRANSPARENCY);
                        menus.add(ImageToolType.LOCK);
                    }
                }

                IInsertableObjectLayerEditor editor = modelManager.getInsertableObjectLayerEditor();
                int prevLayerIndex = editor.getPrevLayerIndex(insertableBitmap, true);
                if (prevLayerIndex != -1) {
                    if (editor.getLayerCount(insertableBitmap, true) > 2) {
                        menus.add(ImageToolType.TOP_LAYER);
                    }
                    menus.add(ImageToolType.PREV_LAYER);
                }

                int nextLayerIndex = editor.getNextLayerIndex(insertableBitmap, true);
                if (nextLayerIndex != -1) {
                    menus.add(ImageToolType.NEXT_LAYER);
                    if (editor.getLayerCount(insertableBitmap, true) > 2) {
                        menus.add(ImageToolType.BOTTOM_LAYER);
                    }
                }
                menus.add(ImageToolType.SHARE);
                menusCallback.accept(menus);
            } catch (Exception exception) {
                LogHelper.e(TAG, "getImageToolMenus: " + exception.getMessage());
            }
        });
    }

    private List<ImageToolType> getCutoutToolMenus() {
        ArrayList<ImageToolType> menus = new ArrayList<>();
        boolean isPadType = DeviceUtils.INSTANCE.isPadType(KiloApp.Companion.getDeviceType());
        if (!isSingleImage()) {
            return menus;
        }
        InsertableBitmap insertableBitmap = (InsertableBitmap) mData.get(0);
        if (isPadType) {
            menus.add(ImageToolType.IMAGE_MATTING);
            menus.add(ImageToolType.IMAGE_SEGMENTATION);
            menus.add(ImageToolType.REGULAR_CROP);
            menus.add(ImageToolType.IRREGULAR_CROP);
        }
        return menus;
    }


    public void reShowToolWindow() {
        if (imageToolWindow != null) {
            imageToolWindow.dismiss();
            if (!mNotShowToolWindow) {
                showImageToolWindow();
            }
        }
    }

    @Override
    public void setNotShowToolWindow(boolean notShowToolWindow) {
        mNotShowToolWindow = notShowToolWindow;
    }

    public void showCutoutToolWindow() {
        if (imageToolWindow != null) {
            imageToolWindow.dismiss();
            if (!mNotShowToolWindow) {
                imageToolWindow.show(mDoodleView, getCutoutToolMenus());
            }
        }
    }

    public void dismissToolWindow() {
        if (imageToolWindow != null) {
            imageToolWindow.dismiss();
        }
    }

    private boolean isInCustomMaterialOfInsertableBitmap(InsertableBitmap insertableBitmap) {
        try {
            String md5 = getMd5ByInsertableBitmapFile(insertableBitmap);
            return HandbookDatabase.Companion.getDatabase().getCustomMaterialDao().isMd5Exists(md5);
        } catch (Exception exception) {
            LogHelper.e(TAG, "isInCustomMaterialOfInsertableBitmap: " + exception.getMessage());
            return false;
        }
    }

    private String getMd5ByInsertableBitmapFile(InsertableBitmap insertableBitmap) {
        try {
            String attachFilePath = insertableBitmap.getAttachFilePath();
            ResourceManager resManager = insertableBitmap.getResManager();
            if (resManager != null) {
                File file = resManager.openFile(attachFilePath);
                if (file.exists()) {
                    return Md5Utils.INSTANCE.getMd5String(file);
                } else {
                    return "";
                }
            }
        } catch (IOException exception) {
            return "";
        }
        return "";
    }

}

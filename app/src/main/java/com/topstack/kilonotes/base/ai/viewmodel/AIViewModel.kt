package com.topstack.kilonotes.base.ai.viewmodel

import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.base.ai.chat.ChatBot
import com.topstack.kilonotes.base.ai.manager.AiAccessManager
import com.topstack.kilonotes.base.ai.model.AiAccessToken
import com.topstack.kilonotes.base.ai.model.AiAgents
import com.topstack.kilonotes.base.ai.model.AiChatModel
import com.topstack.kilonotes.base.ai.model.AiIntegralValue
import com.topstack.kilonotes.base.ai.model.AiUserModels
import com.topstack.kilonotes.base.ai.model.Completion
import com.topstack.kilonotes.base.ai.requester.AiChatV1Requester
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.track.event.AiEvent
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.pad.note.model.AiFunction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.util.UUID

class AIViewModel : ViewModel() {
    companion object {
        const val AI_CHAT = 2
        const val AI_DEEP_SEEK = 3
        const val AI_SOLUTION_EXPERT = 4

        private const val MAX_TIMES = 5
        private const val PROBLEM_SOLVING = "解题助手"
    }

    private val TAG = "AIViewModel"
    private val chatBot: ChatBot = ChatBot().apply {
        chatEventListener = object : ChatBot.ChatEventListener {
            override fun onBusyStatusChanged(isBusy: Boolean) {
                _isAiBusy.postValue(isBusy)
                LogHelper.d(TAG, "onBusyStatusChanged: isBusy = $isBusy")
            }

            override fun onNewConversation(
                completion: Completion,
                exceptionQuestion: Completion?
            ) {
                LogHelper.d(
                    TAG,
                    "onNewConversation: completion = $completion, exceptionQuestion = $exceptionQuestion"
                )
                onNewConversationCallback?.invoke(completion, exceptionQuestion)
            }

            override fun onUpdateConversation(
                completion: Completion,
                exceptionQuestion: Completion?
            ) {
                LogHelper.d(
                    TAG,
                    "onUpdateConversation: completion = $completion, exceptionQuestion = $exceptionQuestion"
                )
                onUpdateConversation?.invoke(completion, exceptionQuestion)
            }

            override fun onConversationComplete(completion: Completion) {
                LogHelper.d(TAG, "onConversationComplete: completion = $completion")
                AiEvent.sendAiResponseSuccess()
                onUpdateConversation?.invoke(completion, null)
            }
        }
    }

    var onNewConversationCallback: ((completion: Completion, exceptionQuestion: Completion?) -> Unit)? =
        null
    var onUpdateConversation: ((completion: Completion, exceptionQuestion: Completion?) -> Unit)? =
        null

    var aiChatListScrollPosition: Pair<Int, Int>? = null
    private val _isAiBusy: MutableLiveData<Boolean> = MutableLiveData(false)
    val isAiBusy: MutableLiveData<Boolean> = _isAiBusy

    private val _aiIntegralMain = MutableLiveData<AiIntegralValue?>()
    val aiIntegralMain: LiveData<AiIntegralValue?> = _aiIntegralMain

    private val _showAddPictureLayout: MutableLiveData<Boolean> = MutableLiveData(false)
    val showAddPictureLayout: MutableLiveData<Boolean> = _showAddPictureLayout

    var completions = mutableListOf<Completion>()

    val errorQuestionUUIDList = mutableSetOf<UUID>()

    var currentPageIndex = 0

    private val _currentAiFunction: MutableLiveData<AiFunction?> =
        MutableLiveData(UserUsageConfig.selectedAiFunction)
    val currentAiFunction: MutableLiveData<AiFunction?> = _currentAiFunction

    var cameraPhotoUri: Uri? = null

    private val _addPictureUri: MutableLiveData<Uri?> = MutableLiveData(null)
    val addPictureUri: MutableLiveData<Uri?> = _addPictureUri

    private var aiUserModels: AiUserModels? = null

    private var aiChatModels: List<AiChatModel>? = null

    private var aiAgents: List<AiAgents>? = null

    private var aiAccessTokens: MutableList<AiAccessToken> = mutableListOf()

    private val _currentAiModel: MutableLiveData<String> = MutableLiveData()
    val currentAiModel: MutableLiveData<String> = _currentAiModel

    private val _previewImageUri: MutableLiveData<Uri?> = MutableLiveData()
    val previewImageUri: MutableLiveData<Uri?> = _previewImageUri

    private var currentAccessToken: AiAccessToken? = null

    private val _editorViewQuestion: MutableLiveData<String?> = MutableLiveData()
    val editorViewQuestion: MutableLiveData<String?> = _editorViewQuestion

    fun setAddPictureUri(uri: Uri?) {
        _addPictureUri.postValue(uri)
    }

    fun setEditorViewQuestion(question: String?) {
        _editorViewQuestion.postValue(question)
    }

    fun showPreviewImageDialog(uri: Uri) {
        _previewImageUri.value = uri
    }

    fun hidePreviewImageDialog() {
        _previewImageUri.value = null
    }

    private val refreshAiModelsJobs = mutableMapOf<AiFunction?, Job>()

    fun refreshUserAiModel(aiFunction: AiFunction?) {
        if (refreshAiModelsJobs[aiFunction]?.isActive == true) {
            LogHelper.d(TAG, "refreshUserAiModel: Job is already running for $aiFunction")
            return
        }

        refreshAiModelsJobs[aiFunction] = viewModelScope.launch {
            if (aiUserModels == null) {
                aiUserModels = AiChatV1Requester.requestAiUserModels()
            }
            if (aiChatModels == null) {
                aiChatModels = AiChatV1Requester.requestAiChatModels()
            }
            if (aiAgents == null) {
                aiAgents = AiChatV1Requester.requestAiChatAgents()
            }
            val modelCode = getAiModelCode(aiFunction)
            modelCode?.let {
                aiChatModels?.find { it.code == modelCode }
                    ?.let {
                        withContext(Dispatchers.Main) {
                            _currentAiModel.value = it.name
                        }
                    }
                if (aiFunction == AiFunction.SOLUTION_EXPERT) {
                    val problemSolvingAgent = aiAgents?.find {
                        it.name == PROBLEM_SOLVING
                    }
                    LogHelper.d(
                        TAG,
                        "refreshUserAiModel problemSolvingAgent: $problemSolvingAgent"
                    )
                    val aiAgentCode = problemSolvingAgent?.code
                    currentAccessToken = if (aiAgentCode == null) {
                        LogHelper.i(
                            TAG,
                            "refreshUserAiModel:  解题专家模式缺少 aiAgentCode"
                        )
                        null
                    } else {
                        refreshAccessToken(modelCode, getAiChatType(aiFunction), aiAgentCode)
                    }
                } else {
                    currentAccessToken =
                        refreshAccessToken(modelCode, getAiChatType(aiFunction))
                }
            }
        }
    }

    private suspend fun refreshAIIntegralMain() {
        if (UserManager.hasLoggedInUser()) {
            AiAccessManager.updateUserAiAccess()
            _aiIntegralMain.postValue(AiAccessManager.aiIntegralMain)
        } else {
            _aiIntegralMain.postValue(null)
        }
    }

    private fun getAiModelCode(aiFunction: AiFunction?): String? {
        return when (aiFunction) {
            AiFunction.DEEP_SEEK -> {
                aiUserModels?.table?.deepThink?.first()
            }


            AiFunction.SOLUTION_EXPERT -> {
                aiUserModels?.table?.problemSolver?.first()
            }


            null -> {
                aiUserModels?.table?.defaultMode?.first()
            }
        }
    }

    fun getCurrentAiChatType(): Int {
        return getAiChatType(_currentAiFunction.value)
    }

    private fun getAiChatType(aiFunction: AiFunction?): Int {
        return when (aiFunction) {
            AiFunction.DEEP_SEEK -> {
                AI_DEEP_SEEK
            }


            AiFunction.SOLUTION_EXPERT -> {
                AI_SOLUTION_EXPERT
            }


            null -> {
                AI_CHAT
            }
        }
    }

    private val aiAccessTokensMutex = Mutex()

    private suspend fun refreshAccessToken(
        modelCode: String,
        aiChatType: Int,
        businessCode: String? = null
    ): AiAccessToken? {
        if (!UserManager.hasLoggedInUser()) return null
        val openId = UserManager.loggedInUser?.openId ?: return null
        var aiAccessToken: AiAccessToken? = aiAccessTokens.find { it.model == modelCode }
        val existingToken = aiAccessToken

        if (existingToken == null) {
            // 增加同步 double check，避免多次请求
            aiAccessTokensMutex.withLock {
                aiAccessToken = aiAccessTokens.find { it.model == modelCode }
                if (aiAccessToken == null) {
                    val accessToken = AiChatV1Requester.requestAiAccessToken(
                        openId,
                        businessCode,
                        modelCode,
                        aiChatType
                    )
                    if (accessToken != null) {
                        aiAccessToken = AiAccessToken(
                            accessToken.sessionId,
                            modelCode,
                            0
                        ).also {
                            aiAccessTokens.add(it)
                        }
                    }
                }
            }
        } else {
            if (existingToken.times >= MAX_TIMES) {
                aiAccessTokens.remove(existingToken)
                val accessToken = AiChatV1Requester.requestAiAccessToken(
                    openId,
                    businessCode,
                    modelCode,
                    aiChatType
                )
                if (accessToken != null) {
                    aiAccessToken = AiAccessToken(
                        accessToken.sessionId,
                        modelCode,
                        0
                    ).also {
                        aiAccessTokens.add(it)
                    }
                }
            }
        }
        return aiAccessToken
    }


    fun changeShowPictureLayoutStatus() {
        val isShow = _showAddPictureLayout.value ?: return
        _showAddPictureLayout.value = !isShow
    }

    fun setCurrentAiFunction(aiFunction: AiFunction?) {
        _currentAiFunction.postValue(aiFunction)
        UserUsageConfig.selectedAiFunction = aiFunction
    }

    fun getCurrentAiIntegralValue(callBack: ((AiIntegralValue?) -> Unit)? = null) {
        val currentAiIntegralMain = _aiIntegralMain.value
        if (currentAiIntegralMain != null) {
            callBack?.invoke(currentAiIntegralMain)
        } else {
            viewModelScope.launch(Dispatchers.IO) {
                refreshAIIntegralMain()
                withContext(Dispatchers.Main) {
                    callBack?.invoke(_aiIntegralMain.value)
                }
            }
        }
    }

    fun acquireAIIntegral() {
        viewModelScope.launch(Dispatchers.IO) {
            refreshAIIntegralMain()
        }
    }

    fun getHistoryByPage(
        pageIndex: Int,
        historyCompletionsCallback: (List<Completion>) -> Unit
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val historyCompletions = chatBot.getHistoryByPage(pageIndex)
            withContext(Dispatchers.Main) {
                historyCompletionsCallback.invoke(historyCompletions)
            }
        }
    }

    fun getPageCount(pageCountCallback: (Int) -> Unit) {
        viewModelScope.launch(Dispatchers.IO) {
            val pageCount = chatBot.getPageCount()
            withContext(Dispatchers.Main) {
                pageCountCallback.invoke(pageCount)
            }
        }
    }

    fun startSession() {
        chatBot.startNewSession()
    }

    fun endSession() {
        chatBot.endSession()
    }

    fun sendToChatBot(question: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val aiFunction = _currentAiFunction.value
            val modelCode = getAiModelCode(aiFunction)
            if (aiAgents == null) {
                aiAgents = AiChatV1Requester.requestAiChatAgents()
            }

            modelCode?.let {
                if (aiFunction == AiFunction.SOLUTION_EXPERT) {
                    val problemSolvingAgent = aiAgents?.find {
                        it.name == PROBLEM_SOLVING
                    }
                    LogHelper.d(
                        TAG,
                        "sendToChatBot problemSolvingAgent: $problemSolvingAgent"
                    )
                    val aiAgentCode = problemSolvingAgent?.code
                    currentAccessToken = if (aiAgentCode == null) {
                        LogHelper.i(
                            TAG,
                            "sendToChatBot:  解题专家模式缺少 aiAgentCode"
                        )
                        null
                    } else {
                        refreshAccessToken(modelCode, getAiChatType(aiFunction), aiAgentCode)
                    }
                } else {
                    currentAccessToken =
                        refreshAccessToken(modelCode, getAiChatType(aiFunction))
                }
            }

            val accessToken = currentAccessToken
            val fileUri = _addPictureUri.value
            if (aiFunction == AiFunction.SOLUTION_EXPERT) {
                val contentType = when {
                    fileUri != null && question.isNotBlank() -> {
                        AiEvent.ContentType.MIXED
                    }

                    fileUri != null && question.isBlank() -> {
                        AiEvent.ContentType.IMAGE
                    }

                    fileUri == null && question.isNotBlank() -> {
                        AiEvent.ContentType.TEXT
                    }

                    else -> {
                        AiEvent.ContentType.TEXT
                    }
                }

                AiEvent.sendAiSolveProblemSendContentType(contentType)
            }

            if (fileUri != null) {
                setAddPictureUri(null)
                chatBot.send(
                    question,
                    accessToken?.sessionId,
                    accessToken?.model,
                    fileUri
                )
                accessToken?.apply {
                    times += 1
                }
            } else {
                chatBot.send(
                    question,
                    accessToken?.sessionId,
                    accessToken?.model
                )
                accessToken?.apply {
                    times += 1
                }
            }

        }
    }

    fun resendToChatBot(completion: Completion) {
        viewModelScope.launch(Dispatchers.IO) {
            chatBot.resend(completion)
        }
    }
}
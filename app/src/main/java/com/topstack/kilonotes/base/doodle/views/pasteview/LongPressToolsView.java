package com.topstack.kilonotes.base.doodle.views.pasteview;

import android.content.Context;
import android.graphics.Point;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.topstack.kilonotes.R;
import com.topstack.kilonotes.base.doodle.listeners.IClickedListener;
import com.topstack.kilonotes.databinding.DoodleToolsPopupLayoutBinding;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class LongPressToolsView {
    private Context mContext = null;
    private ViewGroup mSrcView = null;
    private IClickedListener mPasteClickListener = null;
    private IClickedListener mJumpToPageByLinkListener = null;
    private IClickedListener mUnlockListener = null;

    private IClickedListener mDeleteListener = null;


    public LongPressToolsView(Context context, ViewGroup parentView) {
        mContext = context;
        mSrcView = parentView;
    }

    public void setPasteClickListener(IClickedListener listener) {
        mPasteClickListener = listener;
    }

    public void setJumpToPageByLinkListener(IClickedListener listener) {
        mJumpToPageByLinkListener = listener;
    }

    public void setUnlockListener(IClickedListener listener) {
        mUnlockListener = listener;
    }

    public void setDeleteListener(IClickedListener listener) {
        mDeleteListener = listener;
    }

    public void showLongPressToolsView(Point anchor, boolean isLinkGroupShow, boolean isPasteGroupShow, boolean isUnlockShow, boolean isDeleteShow) {
        dismissLongPressToolsView();
        ensureInitUi(anchor, isLinkGroupShow, isPasteGroupShow, isUnlockShow, isDeleteShow);
    }

    private PopupWindow mToolWindow;

    private void ensureInitUi(Point anchor, boolean isLinkGroupShow, boolean isPasteGroupShow, boolean isUnlockShow, boolean isDeleteShow) {
        DoodleToolsPopupLayoutBinding binding = DoodleToolsPopupLayoutBinding.inflate(LayoutInflater.from(mContext));
        List<LongPressType> menus = new ArrayList<>();
        if (isPasteGroupShow) {
            menus.add(LongPressType.PASTE);
        }
        if (isLinkGroupShow) {
            menus.add(LongPressType.LINK);
        }
        if (isUnlockShow) {
            menus.add(LongPressType.UNLOCK);
        }
        if (isDeleteShow) {
            menus.add(LongPressType.DELETE);
        }
        if (menus.isEmpty()) return;
        LongPressToolsAdapter adapter = new LongPressToolsAdapter(mContext, menus, new Function1<LongPressType, Unit>() {
            @Override
            public Unit invoke(LongPressType longPressType) {
                if (longPressType == LongPressType.PASTE) {
                    if (mPasteClickListener != null) {
                        mPasteClickListener.onClicked();
                    }
                } else if (longPressType == LongPressType.LINK) {
                    if (mJumpToPageByLinkListener != null) {
                        mJumpToPageByLinkListener.onClicked();
                    }
                } else if (longPressType == LongPressType.DELETE) {
                    if (mDeleteListener != null) {
                        mDeleteListener.onClicked();
                    }
                } else {
                    if (mUnlockListener != null) {
                        mUnlockListener.onClicked();
                    }
                }
                return Unit.INSTANCE;
            }
        });
        DividerItemDecoration itemDecoration = new DividerItemDecoration(mContext, DividerItemDecoration.HORIZONTAL);
        itemDecoration.setDrawable(new ColorDrawable(mContext.getColor(R.color.common_divider)));
        binding.doodleToolsContent.setAdapter(adapter);
        binding.doodleToolsContent.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
        binding.doodleToolsContent.addItemDecoration(itemDecoration);
        int measuredWidth = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        int measuredHeight = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        binding.getRoot().measure(measuredWidth, measuredHeight);
        binding.getRoot().layout(0, 0, binding.getRoot().getMeasuredWidth(), binding.getRoot().getMeasuredHeight());
        int[] location = new int[2];
        mSrcView.getLocationInWindow(location);

        mToolWindow = new PopupWindow(binding.getRoot(), ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, false);
        mToolWindow.setOutsideTouchable(true);
        mToolWindow.setClippingEnabled(true);
        mToolWindow.showAtLocation(mSrcView, Gravity.NO_GRAVITY, (int) (location[0] + anchor.x - binding.getRoot().getWidth() / 2f), location[1] + anchor.y - binding.getRoot().getHeight());
    }

    public void dismissLongPressToolsView() {
        if (mToolWindow != null) {
            mToolWindow.dismiss();
            mToolWindow = null;
        }
    }

    public boolean isShowing() {
        return mToolWindow != null && mToolWindow.isShowing();
    }

    public void dismissLongPressToolsView(boolean isPasteViewDismissFlag) {
        if (mToolWindow != null && isPasteViewDismissFlag) {
            mToolWindow.dismiss();
            mToolWindow = null;
        }
    }
}

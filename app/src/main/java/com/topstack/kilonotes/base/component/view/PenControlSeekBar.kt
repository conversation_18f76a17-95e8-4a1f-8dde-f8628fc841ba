package com.topstack.kilonotes.base.component.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.topstack.kilonotes.R
import com.topstack.kilonotes.infra.util.AppUtils
import java.lang.Float.min
import kotlin.math.max
import kotlin.math.roundToInt

class PenControlSeekBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {


    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val progressNormalPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val knobPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val knobBgPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private val path = Path()
    private val rectF = RectF()

    private var knobPosition = context.resources.getDimension(R.dimen.dp_15)

    private val barWidth = context.resources.getDimension(R.dimen.dp_312)
    private val xOffset = context.resources.getDimension(R.dimen.dp_15)

    var onValueChanged: ((value: Int, byUser: Boolean, isFinial: Boolean) -> Unit)? = null
        set(value) {
            field = value
            onValueChanged?.invoke(_value, false, true)
        }

    init {
        progressPaint.color = context.getColor(R.color.graph_shape_point_active_color)
        progressNormalPaint.color = AppUtils.getColor(R.color.pen_control_seek_bar_bg)
        knobPaint.color = context.getColor(R.color.pad_note_tool_pen_color_list_2)
        knobBgPaint.color = context.getColor(R.color.pen_control_seek_bar_knob_bg)
        circlePaint.color = context.getColor(R.color.dot_indicator_unselected)

        progressPaint.style = Paint.Style.FILL
        progressNormalPaint.style = Paint.Style.FILL
        knobPaint.style = Paint.Style.FILL
        knobBgPaint.style = Paint.Style.FILL
        circlePaint.style = Paint.Style.FILL
    }

    private var _value: Int = 0

    val maxValue: Int = 100
    val minValue: Int = 0
    val length: Float get() = (maxValue - minValue).toFloat()

    fun setValue(newValue: Int) {
        _value = newValue
        knobPosition = newValue / length * barWidth + xOffset
        invalidate()
        onValueChanged?.invoke(newValue, false, true)
    }

    fun getValue(): Int = _value

    fun setPercent(percent: Float) {
        setValue((percent * length).toInt())
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        knobPosition = _value / length * barWidth + xOffset
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val height = height.toFloat()

        // 绘制整体进度条
        rectF.set(
            xOffset,
            context.resources.getDimension(R.dimen.dp_13),
            xOffset + barWidth,
            context.resources.getDimension(R.dimen.dp_16)
        )
        path.reset()
        path.addRoundRect(rectF, height / 10, height / 10, Path.Direction.CW)
        canvas.drawPath(path, progressNormalPaint)

        // 绘制左边蓝色进度
        rectF.set(
            xOffset,
            context.resources.getDimension(R.dimen.dp_13),
            knobPosition,
            context.resources.getDimension(R.dimen.dp_16)
        )
        path.reset()
        path.addRoundRect(rectF, height / 10, height / 10, Path.Direction.CW)
        canvas.drawPath(path, progressPaint)

        // 绘制五个灰色实心圆点
        for (i in 0 until 5) {
            val x =
                i * context.resources.getDimension(R.dimen.dp_77) + xOffset
            canvas.drawCircle(
                x,
                context.resources.getDimension(R.dimen.dp_8),
                context.resources.getDimension(R.dimen.dp_5) / 2,
                circlePaint
            )
        }
        // 绘制可拖动的白色圆点的bg
        canvas.drawCircle(
            knobPosition,
            height / 2,
            xOffset,
            knobBgPaint
        )

        // 绘制可拖动的白色圆点
        canvas.drawCircle(
            knobPosition,
            height / 2,
            context.resources.getDimension(R.dimen.dp_13),
            knobPaint
        )
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                knobPosition = min(
                    max(event.x, xOffset),
                    xOffset + barWidth
                )
                _value = ((knobPosition - xOffset) / barWidth * length).toInt()
                onValueChanged?.invoke(_value, true, false)
                invalidate()
                return true
            }

            MotionEvent.ACTION_UP -> {
                calculateNearestPosition()
                onValueChanged?.invoke(_value, true, true)
                invalidate()
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    private fun calculateNearestPosition() {
        val divisionWidth = barWidth / 4
        val index = ((knobPosition - xOffset) / divisionWidth).roundToInt()
        _value = (length * index * 0.25F).toInt()
        val nearestPosition =
            index * divisionWidth
        knobPosition = min(
            max(nearestPosition, 0f),
            barWidth
        ) + xOffset
    }

}
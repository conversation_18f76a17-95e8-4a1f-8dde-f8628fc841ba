package com.topstack.kilonotes.base.doodle.model.graph.shape

import android.graphics.RectF
import com.topstack.kilonotes.base.doodle.model.graph.model.GraphPoint
import com.topstack.kilonotes.base.doodle.model.graph.model.GraphSegment
import com.topstack.kilonotes.base.doodle.model.graph.model.GraphShape

class Curve : GraphShape() {
    companion object {
        private const val START_POINT = "start"
        private const val TOUCH_POINT = " touchPoint"
        private const val END_POINT = "end"
    }

    val start = GraphPoint(0F, 0F, START_POINT)
    val touchPoint = GraphPoint(0F, 0F, TOUCH_POINT)
    val end = GraphPoint(0F, 0F, END_POINT)

    override fun initGraphPoint(graphPoints: List<GraphPoint>?) {
        if (!graphPoints.isNullOrEmpty()) {
            graphPoints.forEach { point ->
                changeGraphPoint(point.alias, point.x, point.y)
            }
        } else {
            changeGraphPoint(START_POINT, 0F, 0F)
            changeGraphPoint(TOUCH_POINT, 7F, 2F)
            changeGraphPoint(END_POINT, 10F, 10F)
        }
    }

    override fun getGraphPointByAlias(alias: String): GraphPoint? {
        return when (alias) {
            start.alias -> start
            touchPoint.alias -> touchPoint
            end.alias -> end
            else -> null
        }
    }

    override fun initGraphSegment() {
        graphSegmentList = listOf(
            GraphSegment(
                mutableListOf(
                    start,
                    touchPoint,
                    end
                )
            )
        )
    }

    override fun initAssociatedPoint() {
        start.addAssociate(
            GraphPoint.AssociatedPoint(
                GraphPoint.ShapeChangeType.SYNTENY,
                start
            )
        )
        touchPoint.addAssociate(
            GraphPoint.AssociatedPoint(
                GraphPoint.ShapeChangeType.SYNTENY,
                touchPoint
            )
        )
        end.addAssociate(
            GraphPoint.AssociatedPoint(
                GraphPoint.ShapeChangeType.SYNTENY,
                end
            )
        )
    }

    override fun getGraphCategories(): GraphCategories {
        return GraphCategories.LINE
    }

    override fun getGraphPoints(): List<GraphPoint> {
        return listOf(start, touchPoint, end)
    }

    override fun getInnerControlPoints(): List<GraphPoint> {
        return listOf(touchPoint)
    }

    override fun getEndPoint(): List<GraphPoint> {
        return listOf(start, end)
    }

    /**
     * 曲线Path的computeBounds是根据贝塞尔点计算的误差较大,自己实现曲线的getBounds
     */
    override fun getBounds(): RectF {
        val bounds = RectF()
        getStrokeFillPath().computeBounds(bounds, true)
        return bounds
    }

}
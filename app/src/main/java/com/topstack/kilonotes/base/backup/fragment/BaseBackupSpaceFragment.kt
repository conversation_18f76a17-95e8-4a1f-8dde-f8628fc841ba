package com.topstack.kilonotes.base.backup.fragment

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.net.ConnectivityManager
import android.net.Network
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.base.backup.adapter.BackupSpaceDeviceTabAdapter
import com.topstack.kilonotes.base.backup.adapter.BaseBackupSpaceNotesAdapter
import com.topstack.kilonotes.base.backup.dialog.BackupSpaceSettingPopWindow
import com.topstack.kilonotes.base.backup.dialog.BackupSpaceUploadingPopWindow
import com.topstack.kilonotes.base.backup.viewmodel.BackupDownloadViewModel
import com.topstack.kilonotes.base.backup.viewmodel.BackupSpaceUploadViewModel
import com.topstack.kilonotes.base.component.dialog.AlertDialog
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.component.fragment.NaviEnum
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.importfile.BaseHandleImportActivity
import com.topstack.kilonotes.base.ktx.isLayoutRtl
import com.topstack.kilonotes.base.ktx.safeShow
import com.topstack.kilonotes.base.note.viewmodel.NoteViewModel
import com.topstack.kilonotes.base.shadow.FixShadowLayout
import com.topstack.kilonotes.base.track.event.CloudBackupEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.cloudbackup.config.BackupConfig
import com.topstack.kilonotes.cloudbackup.download.model.BackupDownloadState
import com.topstack.kilonotes.cloudbackup.upload.DocumentBackupStatus
import com.topstack.kilonotes.infra.device.DeviceType
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.phone.note.PhoneBackupSpaceSettingFragment
import com.topstack.kilonotes.phone.note.PhoneBackupSpaceUploadingFragment
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import kotlin.math.min

abstract class BaseBackupSpaceFragment : BaseFragment() {
    companion object {
        const val NO_DATA_UPLOADED = 0

        //用户远端备份空间大小，默认2GB
        private const val DEFAULT_USER_REMOTE_BACKUP_SPACE = "2GB"
        private const val MAX_REMOTE_SPACE = 2L
        private const val ONE_MEGA_BYTE = 1024
    }

    lateinit var container: ConstraintLayout
    lateinit var rootView: View
    lateinit var backupSpaceClose: ImageView
    lateinit var devicesTabList: RecyclerView
    lateinit var backupUsedSpace: TextView
    lateinit var uploadIcon: TextView
    lateinit var settingIcon: TextView
    lateinit var backupDescription: TextView
    lateinit var backupNotesList: RecyclerView
    lateinit var backupNotesListShadow: FixShadowLayout
    lateinit var noteListEmptyBg: ImageView
    lateinit var backupGroup: Group
    lateinit var purchaseGroup: Group
    lateinit var purchaseBtn: FixShadowLayout
    lateinit var loginOrPurchaseBtn: TextView
    lateinit var openBg: ImageView
    lateinit var backupSpaceTitle: TextView
    private var backupNotesAdapter: BaseBackupSpaceNotesAdapter? = null
    private var backupSpaceDeviceTabAdapter: BackupSpaceDeviceTabAdapter? = null

    private var uploadingWindow: BackupSpaceUploadingPopWindow? = null
    private var settingWindow: BackupSpaceSettingPopWindow? = null

    private var uploadingDialog: PhoneBackupSpaceUploadingFragment? = null
    private var settingDialog: PhoneBackupSpaceSettingFragment? = null
    private val networkChange: ConnectivityManager.NetworkCallback by lazy {
        object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
            }


            override fun onLost(network: Network) {
                super.onLost(network)
                backupDownloadViewModel.cancelAllDownloadTask()
            }
        }
    }

    protected val backupDownloadViewModel by activityViewModels<BackupDownloadViewModel>()
    private val noteViewModel by activityViewModels<NoteViewModel>()
    private val backupSpaceUploadViewModel by activityViewModels<BackupSpaceUploadViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        rootView = inflater.inflate(containerLayoutId(), container, false)
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindView()
        initView()
        initObserve()
        NetworkUtils.addNetworkChangeCallback(networkChange)
    }

    fun initObserve() {
        if (UserManager.hasLoggedInUser() && UserManager.isVip()) {
            lifecycleScope.launch {
                backupDownloadViewModel.notesPagingData.collectLatest { pagingData ->
                    backupNotesAdapter?.submitData(pagingData)
                }
            }
        }

        backupSpaceUploadViewModel.updateDataState.observe(viewLifecycleOwner) { state ->
            if (state.isCompleted() && state.state == DocumentBackupStatus.BackupState.SUCCESS) {
                if (UserManager.hasLoggedInUser() && UserManager.isVip()) {
                    backupDownloadViewModel.requestAllDeviceTabInfo()
                    backupNotesAdapter?.refresh()
                }
            }
        }

        backupDownloadViewModel.selectedDeviceTabInfo.observe(viewLifecycleOwner) { deviceTabInfo ->
            if (deviceTabInfo == null) return@observe
            backupNotesAdapter?.refresh()
            backupDescription.text = AppUtils.getString(
                R.string.backup_space_description,
                if (deviceTabInfo.isCurrentTk) {
                    AppUtils.getString(R.string.backup_space_current_selected_device)
                } else {
                    deviceTabInfo.model
                }
            )

        }

        backupDownloadViewModel.allDeviceTabInfo.observe(viewLifecycleOwner) { allDeviceInfo ->
            backupSpaceDeviceTabAdapter?.submitList(allDeviceInfo)
            val sumUsage = allDeviceInfo.sumOf {
                it.usage
            }
            BackupConfig.isBackupSpaceFull = sumUsage >= MAX_REMOTE_SPACE * ONE_MEGA_BYTE
            backupUsedSpace.text = formatStorage(sumUsage, BackupConfig.isBackupSpaceFull)
            devicesTabList.post {
                if (isAdded) {
                    backupDownloadViewModel.selectedDeviceTabInfo.value?.let {
                        backupSpaceDeviceTabAdapter?.setSelectedItem(it)
                    }
                }
            }
        }

        lifecycleScope.launch {
            backupDownloadViewModel.changeBackupId.collect { backupId ->
                val backupLoadState =
                    backupDownloadViewModel.getBackupDownloadStateByBackupId(backupId)
                if (backupLoadState != null) {
                    if (backupLoadState.state == BackupDownloadState.State.DOWNLOADED) {
                        backupDownloadViewModel.removeBackupDownloadState(backupId)
                        backupDownloadViewModel.addBackupDownloadedNote(
                            backupId,
                            backupLoadState
                        )
                        val downloadNoteInfo = backupNotesAdapter?.getDownloadNoteInfo(backupId)
                            ?: backupLoadState.downloadNoteInfo
                        downloadNoteInfo?.let {
                            ToastUtils.topCenter(
                                AppUtils.appContext,
                                "${downloadNoteInfo.noteName}${AppUtils.getString(R.string.backup_space_download_success)}"
                            )
                        }
                    }
                    backupNotesAdapter?.refreshItemStatus(backupId, backupLoadState)
                }
            }
        }

        backupSpaceUploadViewModel.updateUploadList()
        backupSpaceUploadViewModel.isShowUploadingPopWindow.observe(viewLifecycleOwner) { isNeedShow ->
            if (isNeedShow && isAdded && activity != null && !requireActivity().isFinishing) {
                if (uploadingWindow == null || !uploadingWindow!!.isShowing) {
                    showUploadingWindow()
                }
            } else if (!isNeedShow) {
                uploadingWindow?.dismiss()
            }
        }

        backupSpaceUploadViewModel.isShowSettingPopWindow.observe(viewLifecycleOwner) { isNeedShow ->
            if (isNeedShow && isAdded && activity != null && !requireActivity().isFinishing) {
                if (settingWindow == null || !settingWindow!!.isShowing) {
                    showSettingWindow()
                }
            } else if (!isNeedShow) {
                settingWindow?.dismiss()
            }
        }

        // 观察上传数据列表变化
        backupSpaceUploadViewModel.uploadDataList.observe(viewLifecycleOwner) { dataList ->
            val uploadingCount =
                dataList.count {
                    it.state == DocumentBackupStatus.BackupState.UPLOADING
                            || it.state == DocumentBackupStatus.BackupState.WAITING
                            || it.state == DocumentBackupStatus.BackupState.PREPARING
                }
            val failedCount = dataList.count {
                it.state == DocumentBackupStatus.BackupState.FAILED
                        || it.state == DocumentBackupStatus.BackupState.CANCELED
                        || it.state == DocumentBackupStatus.BackupState.SHOULD_RETRY
            }

            if (failedCount > 0) {
                uploadIcon.text = getString(R.string.backup_space_upload_fail, failedCount)
            } else if (uploadingCount > 0) {
                uploadIcon.text = getString(R.string.backup_space_uploading, uploadingCount)
            } else {
                uploadIcon.text = getString(R.string.backup_space_uploading, NO_DATA_UPLOADED)
            }

            // 展示数据
            if (uploadingWindow?.isShowing == true) {
                uploadingWindow?.updateDataList(dataList)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (UserManager.hasLoggedInUser() && UserManager.isVip()) {
            backupGroup.visibility = View.VISIBLE
            purchaseGroup.visibility = View.GONE
            backupDownloadViewModel.requestAllDeviceTabInfo()
        } else {
            backupGroup.visibility = View.GONE
            purchaseGroup.visibility = View.VISIBLE
        }
    }

    protected open fun bindView() {
        container = rootView.findViewById(R.id.constraint)
        backupSpaceClose = rootView.findViewById(R.id.backup_space_close)
        devicesTabList = rootView.findViewById(R.id.devices_tab)
        backupUsedSpace = rootView.findViewById(R.id.backup_used_space)
        uploadIcon = rootView.findViewById(R.id.upload_icon)
        settingIcon = rootView.findViewById(R.id.setting_icon)
        backupDescription = rootView.findViewById(R.id.backup_description)
        backupNotesList = rootView.findViewById(R.id.backup_notes_list)
        backupNotesListShadow = rootView.findViewById(R.id.backup_notes_list_shadow)
        noteListEmptyBg = rootView.findViewById(R.id.note_list_empty)
        backupGroup = rootView.findViewById(R.id.backup_group)
        purchaseGroup = rootView.findViewById(R.id.purchase_group)
        purchaseBtn = rootView.findViewById(R.id.purchase)
        loginOrPurchaseBtn = rootView.findViewById(R.id.login_or_purchase_btn)
        openBg = rootView.findViewById(R.id.open_bg)
        backupSpaceTitle = rootView.findViewById(R.id.backup_space_title)
    }

    protected open fun initView() {
        initBackupNotesAdapter()
        initBackupSpaceDeviceTabAdapter()
        if (UserManager.isVip() && !UserManager.hasLoggedInUser()) {
            loginOrPurchaseBtn.text = AppUtils.getString(R.string.backup_space_login_text)
        } else {
            loginOrPurchaseBtn.text = AppUtils.getString(R.string.vip_store_pay_button_default_text)
        }
        lifecycleScope.launch {
            backupNotesAdapter?.addLoadStateListener {
                val itemCount = backupNotesAdapter?.itemCount
                when (it.refresh) {
                    is LoadState.NotLoading, is LoadState.Loading -> {
                        itemCount?.let {
                            if (itemCount > 0) {
                                if (noteListEmptyBg.visibility != View.GONE) {
                                    noteListEmptyBg.visibility = View.GONE
                                }
                                if (backupNotesListShadow.visibility != View.VISIBLE) {
                                    backupNotesListShadow.visibility = View.VISIBLE
                                }
                            } else {
                                noteListEmptyBg.visibility = View.VISIBLE
                                backupNotesListShadow.visibility = View.GONE
                            }
                        }
                    }


                    is LoadState.Error -> {
                        noteListEmptyBg.visibility = View.VISIBLE
                        backupNotesListShadow.visibility = View.GONE
                    }
                }
            }
        }

        purchaseBtn.setOnClickListener {
            (activity as? BaseHandleImportActivity)?.jumpToVipStore(
                getCurrentNavigationId(),
                NaviEnum.CLOUD_BACKUP
            )
        }

        backupSpaceClose.setOnClickListener {
            safePopBackStack()
        }

        uploadIcon.setOnClickListener {
            backupSpaceUploadViewModel.showUploadingPopWindow()
        }

        if (UserManager.hasLoggedInUser() && UserManager.isVip()) {
            settingIcon.isEnabled = true
            settingIcon.isSelected = true
            settingIcon.setOnClickListener {
                backupSpaceUploadViewModel.showSettingPopWindow()
            }
        } else {
            settingIcon.isSelected = false
            settingIcon.isEnabled = false
        }
    }

    private fun initBackupSpaceDeviceTabAdapter() {
        devicesTabList.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)
        devicesTabList.addItemDecoration(object : ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                val position = parent.getChildAdapterPosition(view)
                val itemCount = parent.adapter?.itemCount ?: 0

                if (position == 0) {
                    if (view.isLayoutRtl()) {
                        outRect.right = getDevicesTabListPaddingHorizontal()
                    } else {
                        outRect.left = getDevicesTabListPaddingHorizontal()
                    }
                }

                if (position == itemCount - 1) {
                    if (view.isLayoutRtl()) {
                        outRect.right = getDevicesTabListPaddingHorizontal()
                    } else {
                        outRect.left = getDevicesTabListPaddingHorizontal()
                    }
                }

                if (position in 1..<itemCount) {
                    if (view.isLayoutRtl()) {
                        outRect.right = getDevicesTabListLeftSpace()
                    } else {
                        outRect.left = getDevicesTabListLeftSpace()
                    }
                }
            }
        })
        backupSpaceDeviceTabAdapter =
            BackupSpaceDeviceTabAdapter(requireContext(), doOnDeviceTabClick = { deviceTab ->
                backupDownloadViewModel.setSelectedDeviceTabInfo(deviceTab)
            })
        devicesTabList.adapter = backupSpaceDeviceTabAdapter
    }

    private fun initBackupNotesAdapter() {
        backupNotesList.itemAnimator = null
        backupNotesList.layoutManager = LinearLayoutManager(requireContext())
        backupNotesList.addItemDecoration(object : ItemDecoration() {
            private val linePaint = Paint().apply {
                strokeWidth = resources.getDimension(R.dimen.dp_0_5)
                color = AppUtils.getColor(R.color.backup_space_notes_line_color)
            }

            override fun onDrawOver(
                canvas: Canvas,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.onDrawOver(canvas, parent, state)
                val childCount = parent.childCount
                for (i in 0 until childCount - 1) {
                    val currentView = parent.getChildAt(i) ?: continue
                    val viewLeft = currentView.left.toFloat()
                    val viewWidth = currentView.width.toFloat()
                    val viewBottom = currentView.bottom.toFloat()
                    canvas.drawLine(
                        viewLeft + getNotesListLineLeftPadding(),
                        viewBottom,
                        viewLeft + viewWidth - getNotesListLineRightPadding(),
                        viewBottom,
                        linePaint
                    )
                }
            }

            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                val position = parent.getChildAdapterPosition(view)
                if (position == 0) {
                    outRect.top = getBackupNotesListTopSpace()
                }
            }
        })
        backupNotesAdapter = getBackupSpaceNotesAdapter().apply {
            doOnDeleteClick = { downloadNoteInfo ->
                AlertDialog.Builder()
                    .setIsCancelable(false)
                    .setTitle(
                        getString(
                            R.string.backup_space_delete_remote_note_title,
                            downloadNoteInfo.noteName
                        )
                    )
                    .setNegativeBtn(
                        getString(R.string.delete),
                        AntiShakeClickListener {
                            if (checkNetwork()) {
                                CloudBackupEvent.sendCloudBackupCategoryDeleteClick()
                                backupDownloadViewModel.deleteBackupNote(downloadNoteInfo)
                            }
                        })
                    .setPositiveBtn(
                        getString(R.string.backup_space_not_delete)
                    ) {
                    }.build().safeShow(parentFragmentManager, null)
            }

            doOnConfirmClick = { downloadNoteInfo ->
                if (checkNetwork()) {
                    backupDownloadViewModel.downBackupNote(
                        downloadNoteInfo,
                        noteViewModel
                    )
                }
            }
        }
        backupNotesList.adapter = backupNotesAdapter
    }

    protected abstract fun containerLayoutId(): Int

    protected abstract fun getBackupNotesListTopSpace(): Int

    protected abstract fun getDevicesTabListLeftSpace(): Int

    protected abstract fun getDevicesTabListPaddingHorizontal(): Int

    protected abstract fun getNotesListLineLeftPadding(): Float

    protected abstract fun getNotesListLineRightPadding(): Float

    protected abstract fun getBackupSpaceNotesAdapter(): BaseBackupSpaceNotesAdapter

    override fun onWindowInsetsChange(
        isSoftKeyboardShowing: Boolean,
        softKeyboardHeight: Int,
        isStatusBarShowing: Boolean,
        statusBarHeight: Int,
        isNavigationBarShowing: Boolean,
        navigationBarHeight: Int
    ) {
        super.onWindowInsetsChange(
            isSoftKeyboardShowing,
            softKeyboardHeight,
            isStatusBarShowing,
            statusBarHeight,
            isNavigationBarShowing,
            navigationBarHeight
        )

        // 检查当前屏幕尺寸是否需要切换设置弹窗
        val isOneThirdScreen =
            DimensionUtil.isLandAndOneThirdScreen(context) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                context
            )
        val currentSettingFragment =
            childFragmentManager.findFragmentByTag(PhoneBackupSpaceSettingFragment.TAG)
        val currentUploadingFragment =
            childFragmentManager.findFragmentByTag(PhoneBackupSpaceUploadingFragment.TAG)

        if (!isOneThirdScreen && KiloApp.deviceType != DeviceType.Phone) {
            // 关闭所有已存在的Fragment
            if (currentSettingFragment != null) {
                childFragmentManager.beginTransaction()
                    .remove(currentSettingFragment)
                    .commit()
                // 显示PopWindow
                showSettingWindow()
            }
            if (currentUploadingFragment != null) {
                childFragmentManager.beginTransaction()
                    .remove(currentUploadingFragment)
                    .commit()
                // 显示PopWindow
                showUploadingWindow()
            }
        }

        if (isNavigationBarShowing) {
            container.setPadding(
                container.paddingLeft,
                container.paddingTop,
                container.paddingRight,
                navigationBarHeight
            )
        } else {
            container.setPadding(
                container.paddingLeft,
                container.paddingTop,
                container.paddingRight,
                0
            )
        }
    }

    override fun isListenerWindowInsets(): Boolean {
        return true
    }

    override fun getCurrentNavigationId(): Int {
        return R.id.backup_space_fragment
    }

    private fun showUploadingWindow() {
        if (KiloApp.deviceType == DeviceType.Phone) {
            backupSpaceUploadViewModel.hideUploadingPopWindow()
            uploadingDialog = PhoneBackupSpaceUploadingFragment()
            uploadingDialog!!.show(childFragmentManager, PhoneBackupSpaceUploadingFragment.TAG)
        } else {
            if (DimensionUtil.isLandAndOneThirdScreen(context) ||
                DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)
            ) {
                backupSpaceUploadViewModel.hideUploadingPopWindow()
                uploadingDialog = PhoneBackupSpaceUploadingFragment()
                uploadingDialog!!.show(childFragmentManager, PhoneBackupSpaceUploadingFragment.TAG)
            } else {
                // 确保先关闭已存在的弹窗
                uploadingWindow?.dismiss()
                uploadingWindow = null

                // 创建新的弹窗
                uploadingWindow =
                    BackupSpaceUploadingPopWindow(requireContext())

                // 设置实现uploadingWindow重试监听器
                uploadingWindow!!.onRetryListener = { documentId ->
                    backupSpaceUploadViewModel.retryUpload(documentId)
                }


                uploadIcon.post {
                    if (uploadIcon.windowToken != null) {
                        uploadingWindow!!.show(uploadIcon)
                        // 初始化数据
                        uploadingWindow!!.updateDataList(
                            backupSpaceUploadViewModel.uploadDataList.value ?: emptyList()
                        )
                        uploadingWindow!!.setOnDismissListener {
                            backupSpaceUploadViewModel.hideUploadingPopWindow()
                        }
                    }
                }
            }
        }
    }

    private fun showSettingWindow() {
        if (KiloApp.deviceType == DeviceType.Phone) {
            backupSpaceUploadViewModel.hideSettingPopWindow()
            settingDialog = PhoneBackupSpaceSettingFragment()
            settingDialog!!.show(childFragmentManager, PhoneBackupSpaceSettingFragment.TAG)
        } else {
            if (DimensionUtil.isLandAndOneThirdScreen(context) ||
                DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)
            ) {
                backupSpaceUploadViewModel.hideSettingPopWindow()
                settingDialog = PhoneBackupSpaceSettingFragment()
                settingDialog!!.show(childFragmentManager, PhoneBackupSpaceSettingFragment.TAG)
            } else {
                // 确保先关闭已存在的弹窗
                settingWindow?.dismiss()
                settingWindow = null

                // 创建新的弹窗
                settingWindow = BackupSpaceSettingPopWindow(requireContext())
                settingIcon.post {
                    if (settingIcon.windowToken != null) {
                        settingWindow!!.show(settingIcon)
                        settingWindow!!.setOnDismissListener {
                            backupSpaceUploadViewModel.hideSettingPopWindow()
                        }
                    }
                }
            }
        }
    }

    fun checkNetwork(): Boolean {
        return if (NetworkUtils.isNetworkAvailable()) {
            true
        } else {
            ToastUtils.topCenter(requireContext(), R.string.toast_no_internet)
            false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        backupDownloadViewModel.clearDownloadedNoteMap()
        NetworkUtils.removeNetworkChangeCallback(networkChange)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (uploadingWindow != null && uploadingWindow!!.isShowing) {
            uploadingWindow!!.setOnDismissListener { }
            uploadingWindow!!.dismiss()
        }
        if (settingWindow != null && settingWindow!!.isShowing) {
            settingWindow!!.setOnDismissListener { }
            settingWindow!!.dismiss()
        }
    }

    private fun formatStorage(mbValue: Double, isOverBackupSpace: Boolean): String {
        if (isOverBackupSpace) {
            return AppUtils.getString(
                R.string.backup_space_used_space,
                "2GB",
                DEFAULT_USER_REMOTE_BACKUP_SPACE
            ) + "\n${AppUtils.getString(R.string.backup_remote_space_full)}"
        }
        if (mbValue < 1024) {
            return AppUtils.getString(
                R.string.backup_space_used_space,
                "${removeDecimalZero(mbValue)}MB",
                DEFAULT_USER_REMOTE_BACKUP_SPACE
            )  // 直接返回整数MB
        }

        val gb = BigDecimal(mbValue / ONE_MEGA_BYTE)
            .setScale(2, RoundingMode.CEILING)

        val usedSize = removeDecimalZero(min(gb.toDouble(), 2.00)) + "GB"

        var usedSpaceContent = AppUtils.getString(
            R.string.backup_space_used_space, usedSize, DEFAULT_USER_REMOTE_BACKUP_SPACE
        )
        if (gb.toLong() >= MAX_REMOTE_SPACE) {
            usedSpaceContent += "\n${AppUtils.getString(R.string.backup_remote_space_full)}"
        }
        return usedSpaceContent
    }

    private fun removeDecimalZero(value: Double): String {
        val df = DecimalFormat("#.##") // 自动省略末尾零[6](@ref)
        return df.format(value)
    }
}
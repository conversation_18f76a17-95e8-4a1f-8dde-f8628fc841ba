package com.topstack.kilonotes.base.doodle.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Rect
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.i18n.isSimplifiedChineseLanguage
import com.topstack.kilonotes.base.util.ImageUtils
import com.topstack.kilonotes.infra.foundation.thread.ThreadUtils
import com.topstack.kilonotes.infra.util.LogHelper
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.withContext
import kotlin.math.roundToInt

object PictureCreator {
    private const val TAG = "LongPictureCreator"
    private const val MAX_SUPPORT_PAGE_SIZE = 5
    private const val DEFAULT_OUTPUT_BITMAP_WIDTH = 1080
    private const val DEFAULT_WATERMARK_ICON_HEIGHT = 67
    private const val DEFAULT_WATERMARK_MARGIN_RIGHT = 40
    private const val DEFAULT_WATERMARK_MARGIN_BOTTOM = 40

    @JvmStatic
    suspend fun generateLongPicture(
        context: Context,
        document: Document,
        pageIndexList: List<Int>,
        outputWidth: Int = DEFAULT_OUTPUT_BITMAP_WIDTH,
        onProgress: ((current: Int, total: Int) -> Unit)? = null
    ): Bitmap? {
        var outputHeight = 0
        if (pageIndexList.isEmpty()) {
            LogHelper.d(TAG, "pageIndexList is empty")
            return null
        }
        val pageCount = document.pageCount
        val generatePageList = mutableListOf<Page>()
        //筛选合法的page 并计算需要生成的bitmap高度
        for (index in pageIndexList.indices) {
            if (index >= MAX_SUPPORT_PAGE_SIZE) break
            val pageIndex = pageIndexList[index]
            if (pageIndex < 0 || pageIndex >= pageCount) {
                LogHelper.d(TAG, "pageIndex is invalid!!")
                continue
            }
            val currentPage = document[pageIndex]
            val pageSize = currentPage.pageSizeInPoint
            generatePageList.add(currentPage)
            outputHeight += ((pageSize.height / pageSize.width) * outputWidth).roundToInt()
        }

        if (outputHeight == 0 || generatePageList.isEmpty()) {
            return null
        }

        val resultBitmap = Bitmap.createBitmap(outputWidth, outputHeight, Bitmap.Config.ARGB_8888)
        try {
            generateLongPictureInternal(
                context,
                resultBitmap,
                document,
                generatePageList,
                outputWidth,
                onProgress
            )
        } catch (_: CancellationException) {
            //Cancel
        }
        return resultBitmap
    }

    @JvmStatic
    private suspend fun generateLongPictureInternal(
        context: Context,
        bitmap: Bitmap,
        document: Document,
        generatePageList: List<Page>,
        outputWidth: Int,
        onProgress: ((current: Int, total: Int) -> Unit)? = null
    ) = withContext(Dispatchers.IO) {
        val canvas = Canvas(bitmap)
        var bitmapOffset = 0F
        generatePageList.forEachIndexed { index, page ->
            val pageSize = page.pageSizeInPoint
            val outputHeight = ((pageSize.height / pageSize.width) * outputWidth).roundToInt()
            ensureActive()
            val currentBitmap = ThumbnailUtils.generatePageImage(
                document,
                page,
                outputWidth,
                outputHeight,
                true
            )
            canvas.drawBitmap(currentBitmap, 0F, bitmapOffset, null)
            ThreadUtils.postMainThread {
                onProgress?.invoke(index + 1, generatePageList.size)
            }
            bitmapOffset += currentBitmap.height
            currentBitmap.recycle()
        }
        ensureActive()
        if (!UserManager.isVip()) {
            addWatermark(context, canvas)
        }
    }

    @JvmStatic
    fun addWatermark(context: Context, canvas: Canvas) {
        //简体中文
        val watermarkResId = if (isSimplifiedChineseLanguage(context)) {
            R.drawable.watermark_cn
        } else {
            R.drawable.watermark_en
        }
        val options = BitmapFactory.Options()
        val watermarkBitmap =
            BitmapFactory.decodeResource(context.resources, watermarkResId, options)
        val waterMarkWidth =
            ((options.outWidth / options.outHeight.toFloat()) * DEFAULT_WATERMARK_ICON_HEIGHT).roundToInt()
        val longPictureWidth = canvas.width
        val longPictureHeight = canvas.height
        val targetRect = Rect(
            longPictureWidth - waterMarkWidth - DEFAULT_WATERMARK_MARGIN_RIGHT,
            longPictureHeight - DEFAULT_WATERMARK_ICON_HEIGHT - DEFAULT_WATERMARK_MARGIN_BOTTOM,
            longPictureWidth - DEFAULT_WATERMARK_MARGIN_RIGHT,
            longPictureHeight - DEFAULT_WATERMARK_MARGIN_BOTTOM
        )
        ImageUtils.addWatermark(canvas, watermarkBitmap, targetRect)
        watermarkBitmap.recycle()
    }
}
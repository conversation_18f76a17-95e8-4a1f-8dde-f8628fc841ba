package com.topstack.kilonotes.base.doodle.views.doodleview

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Point
import android.graphics.PointF
import android.graphics.RectF
import android.os.Build
import android.view.Display
import android.view.DragEvent
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.MotionEvent.TOOL_TYPE_STYLUS
import android.view.View
import android.view.ViewConfiguration
import androidx.core.animation.addListener
import androidx.core.app.ActivityCompat.requestDragAndDropPermissions
import androidx.dynamicanimation.animation.FlingAnimation
import androidx.dynamicanimation.animation.FloatValueHolder
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.compat.CustomGestureDetector
import com.topstack.kilonotes.base.compat.ScaleGestureDetector
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.constant.Alpha
import com.topstack.kilonotes.base.constant.MimeType
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.Link
import com.topstack.kilonotes.base.doc.io.ThumbnailManager.getThumbnailFile
import com.topstack.kilonotes.base.doodle.manager.modelmager.Clipboard
import com.topstack.kilonotes.base.doodle.manager.modelmager.IModelManager
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.doodle.model.TransformType
import com.topstack.kilonotes.base.doodle.utils.ScaleUtils
import com.topstack.kilonotes.base.doodle.utils.ThumbnailUtils
import com.topstack.kilonotes.base.doodle.views.doodleview.frame.FrameTransform
import com.topstack.kilonotes.base.doodle.views.doodleview.layer.IDoodleEditLayer
import com.topstack.kilonotes.base.doodle.views.selectview.SelectViewTwoDrag
import com.topstack.kilonotes.base.doodle.views.snippetview.SnippetView
import com.topstack.kilonotes.base.ktx.approximateEquals
import com.topstack.kilonotes.base.ktx.scale
import com.topstack.kilonotes.base.memory.BitmapLruCachePool
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippet
import com.topstack.kilonotes.base.track.event.DraftPaperEvent
import com.topstack.kilonotes.base.track.event.EditEvent
import com.topstack.kilonotes.base.util.MimeUtils
import com.topstack.kilonotes.base.util.PDFManager.getNotePageIndexFromPdfLink
import com.topstack.kilonotes.base.util.findActivity
import com.topstack.kilonotes.infra.device.DeviceUtils
import com.topstack.kilonotes.infra.size.ptToPx
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.infra.util.MathUtils
import com.topstack.kilonotes.stylus.StylusToolKit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.UUID
import kotlin.coroutines.resume
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.hypot
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sin


/**
 * 负责 Touch 事件处理，缩略图的显示
 * 其中缩略图显示部分交由 [VirtualPager] 代理
 */
@SuppressLint("ViewConstructor")
class DoodleTouchLayer2(
    context: Context,
    private val modelManager: IModelManager,
    private val doodleEditLayer: IDoodleEditLayer,
    private val doodleStickyOnTopLayer: DoodleStickyOnTopLayer
) : View(context) {

    init {
        StylusToolKit.obtainPointEstimator()?.onDoodleViewCreate(this)
    }

    private val pager = VirtualPager(this)

    /**
     * 触发翻页监听
     */
    var onPageChangeListener: OnPageChangeListener? = null

    /**
     *  手势监听
     */
    var onFullScreenTriggerListener: OnFullScreenTriggerListener? = null

    private val onScaleChangeListeners = ArrayList<OnScaleChangeListener>()
    private val onTransformChangeListeners = ArrayList<OnTransformChangedListener>()

    fun addOnTransformChangeListeners(listener: OnTransformChangedListener) {
        if (onTransformChangeListeners.contains(listener)) {
            onTransformChangeListeners.remove(listener)
        }
        onTransformChangeListeners.add(listener)
    }

    fun removeOnTransformChangeListeners(listener: OnTransformChangedListener) {
        onTransformChangeListeners.remove(listener)
    }

    fun addOnScaleChangeListener(listener: OnScaleChangeListener) {
        onScaleChangeListeners.add(listener)
    }

    fun removeOnScaleChangeListener(listener: OnScaleChangeListener) {
        onScaleChangeListeners.remove(listener)
    }

    /**
     * 事件处理方式
     * 在 [MODE_TOUCH] 模式下，单指进行绘制，多指进行拖动和缩放
     * 在 [MODE_STYLUS] 模式下，单指进行拖动，多指进行拖动和缩放，仅仅画笔事件进行绘制
     * 在 [MODE_VIEW] 模式下，单指进行拖动，多指进行拖动和缩放，拦截所有事件
     *
     * @see DoodleView.MODE_TOUCH
     * @see DoodleView.DEVICE_MODE_STYLUS
     * @see DoodleView.MODE_VIEW
     */
    var mode: Int = DoodleView.MODE_TOUCH

    var deviceMode: Int = DoodleView.DEVICE_MODE_DEFAULT

    var currentInputMode: InputMode = UserUsageConfig.inputMode

    var isConnectBluetoothPen: Boolean = false

    /**
     * trigger size change
     */
    var triggerSizeChanged = false

    lateinit var doodleModeConfig: DoodleModeConfig

    private val enableScale: Boolean
        get() = doodleModeConfig.enableScale

    private val enableScroll: Boolean
        get() = doodleModeConfig.enableScroll

    var isAddPageAnimation: Boolean = false
    var isFlipOverAnimation: Boolean = false
    var interruptTransform: Boolean = false

    /**
     * 滑动和点击距离判定阈值
     * （ >= scaledTouchSlop 进入滚动判定 ）
     * （ < scaledTouchSlop  进入触摸判定 ）
     */
    private val scaledTouchSlop = ViewConfiguration.get(context).scaledTouchSlop

    /**
     * 动态计算的最小缩放，目的是避免宽高同时低于可视区域的宽高
     */
    private var minScale = 1F

    /**
     * 实际缩放画板时，可缩放到的最小值，之后会回弹至真实最小缩放值  minScale
     */
    private val operableMinScale: Float
        get() = minScale / 2

    /**
     * 屏幕比例变化时，画板视觉上大小保持不变，实际缩放大小变化，
     * 留白操作会画板缩放比例过大，给一个最大限制
     */
    private val screenSizeChangeMaxScale: Float
        get() = SCALE_MAX * 1.5F

    /**
     * 最小缩放值动态计算，最大缩放相对于最小缩放值，乘以最小缩放值
     */
    private val SCALE_MAX: Float
        get() {
            if (doodleModeConfig.maxScale != DoodleModeConfig.SCALE_NOT_SET) return doodleModeConfig.maxScale
            return if (DeviceUtils.isPhoneType(KiloApp.deviceType)) {
                15.0F
            } else {
                15.0F
            } * minScale
        }

    private var fillDoodleScale = 1F
        set(value) {
            field = if (value > SCALE_MAX) {
                SCALE_MAX
            } else {
                value
            }
        }

    private var pageSideFillDoodleSideScale = 1F
        set(value) {
            field = if (value > SCALE_MAX) {
                SCALE_MAX
            } else {
                value
            }
        }

    /**
     * Scroll 的过程中 X 的移动总路程
     */
    private var sumDistanceX: Float = 0F

    /**
     * Scroll 的过程中 Y 的移动总路程
     */
    private var sumDistanceY: Float = 0F


    /**
     * 表示是否已经进行足够长距离的移动，我们需要据此判断是否可以进行缩放操作
     */
    private val isInScroll: Boolean
        get() = (sumDistanceX > scaledTouchSlop || sumDistanceY > scaledTouchSlop)

    private val isInFling: Boolean
        get() = flingAnimation?.isRunning ?: false

    private var scaleDetector =
        ScaleGestureDetector(context, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
                // when current state in scroll state, ignore rest of gesture
                return enableScale && !isInScroll
            }

            override fun onScale(detector: ScaleGestureDetector): Boolean {
                scaleBy(detector.scaleFactor, detector.focusX, detector.focusY)
                return true
            }

            override fun onScaleEnd(detector: ScaleGestureDetector?) {
                // 重置本次缩放开始时，手指移动的距离
                sumDistanceX = 0F
                sumDistanceY = 0F
                resetScaleValue(detector?.focusX ?: 0F, detector?.focusY ?: 0F)
            }
        })

    var customGestureListener: CustomGestureDetector.CustomGestureListener? = null

    private var customDetector = CustomGestureDetector(
        context,
        object : CustomGestureDetector.CustomGestureListener {
            override fun onThreeFingerKneading() {
                customGestureListener?.onThreeFingerKneading()
            }

            override fun onThreeFingerSpreading() {
                customGestureListener?.onThreeFingerSpreading()
            }

            override fun onTwoFingerDoubleTap() {
                customGestureListener?.onTwoFingerDoubleTap()
            }

        })

    /**
     * 当前滑动方向
     */
    private var scrollDirection = SCROLL_DIRECTION_UNKNOWN

    /**
     * 手指离开前滑动趋势
     */
    private var lastDirectionTrend = SCROLL_DIRECTION_UNKNOWN

    /**
     * 连续水平滑动次数
     */
    private var continuousHorizontalDirectionCount = 0

    private var flingAnimation: FlingAnimation? = null

    private var lastFlingDistance = 0F

    /**
     * 翻页监听
     */
    var onTurnPageListener: OnTurnPageListener? = null
    private val turnPageAreaWidth = context.resources.getDimensionPixelSize(R.dimen.dp_200)

    /**
     * simple gesture detector (tap、 fling、 scroll、 press)
     */
    private val scrollDetector =
        GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            private val startBounds = RectF()

            override fun onDown(e: MotionEvent): Boolean {
                renderMatrix.mapRect(startBounds, bounds)
                flingAnimation?.cancel()
                return true
            }

            override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                if (mode == DoodleView.MODE_VIEW) {
                    //预览模式优先判定点击区域是否为播放按钮
                    if (checkAndTriggerTopObjectTapConfirmed(e)) return true
                    //点击区域是否存在跳转链接
                    if (isTapConfirmedByPdfLink(e)) return true

                    //判断是否点击在翻页区域内
                    val turnPreviousBounds = turnPageAreaWidth
                    val turnNextBounds = visualViewport.right - turnPageAreaWidth
                    if (e.x <= turnPreviousBounds) {
                        onTurnPageListener?.turnPreviousPage()
                        return true
                    }
                    if (e.x >= turnNextBounds) {
                        onTurnPageListener?.turnNextPage()
                        return true
                    }

                    if (!doodleModeConfig.enableFullScreenTrigger) return true
                    triggerSizeChanged = true
                    onFullScreenTriggerListener?.onFullScreenTrigger()
                }

                return true
            }

            override fun onDoubleTap(e: MotionEvent): Boolean {
                if (!doodleModeConfig.enableDoodleQuickScale) return true
                // check touch point in render rect
                if (!getRenderRect().contains(e.x, e.y)) {
                    return true
                }

                if (doodleEditLayer.hasAnyLayerViewEditing()) return true
                //开关没有打开
                if (!UserUsageConfig.isOpenDoodleQuickScale) return true

                if (deviceMode != DoodleView.DEVICE_MODE_STYLUS
                    && (currentInputMode == InputMode.DRAW
                            || currentInputMode == InputMode.PEN
                            || currentInputMode == InputMode.PAINTBRUSH
                            || currentInputMode == InputMode.HIGHLIGHTER
                            || currentInputMode == InputMode.GRAFFITI
                            || currentInputMode == InputMode.PRESENTATION)
                ) return false

                val connectStylusInputByHands =
                    deviceMode == DoodleView.DEVICE_MODE_STYLUS && e.getToolType(0) != TOOL_TYPE_STYLUS
                if (deviceMode == DoodleView.DEVICE_MODE_DEFAULT || connectStylusInputByHands) {
                    if (!currentScale.approximateEquals(fillDoodleScale)) {
                        scaleToWithAnimation(fillDoodleScale, e.x, e.y)
                    } else {
                        scaleToWithAnimation(minScale, e.x, e.y)
                    }
                }

                return true
            }

            override fun onFling(
                e1: MotionEvent?,
                e2: MotionEvent,
                velocityX: Float,
                velocityY: Float,
            ): Boolean {
                if (!isInScroll) return false
                val velocity = hypot(velocityX, velocityY)
                //移动的弧度
                val flingRadio = atan2(velocityY, velocityX)

                lastFlingDistance = 0F
                val tempRect = RectF()
                var bothReachBoundary = false
                val viewport = if (doodleModeConfig.enableCustomScrollRect) {
                    doodleModeConfig.renderViewport!!
                } else {
                    visualViewport
                }
                flingAnimation = FlingAnimation(FloatValueHolder()).apply {
                    setStartVelocity(velocity)
                        .setMinimumVisibleChange(10F)
                        .setFriction(2F)
                        .addUpdateListener { animation, value, velocity ->
                            val increasedDistance = value - lastFlingDistance

                            //如果向下滑，PDF是向上移动，所以取反
                            var increasedDistanceX = -(increasedDistance * cos(flingRadio))
                            var increasedDistanceY = -(increasedDistance * sin(flingRadio))
                            val isYReachBoundary =
                                (increasedDistanceY < 0 && tempRect.top >= viewport.top) || (increasedDistanceY > 0 && tempRect.bottom <= viewport.bottom)
                            val isXReachBoundary =
                                (increasedDistanceX < 0 && tempRect.left >= viewport.left) || (increasedDistanceX > 0 && tempRect.right <= viewport.right)
                            if (isXReachBoundary && isYReachBoundary) {
                                bothReachBoundary = true
                                cancel()
                                return@addUpdateListener
                            }
                            if (isXReachBoundary) increasedDistanceX = 0F
                            if (isYReachBoundary) increasedDistanceY = 0F
                            sumDistanceX += increasedDistanceX
                            sumDistanceY += increasedDistanceY
                            translate(increasedDistanceX, increasedDistanceY)
                            lastFlingDistance = value
                            renderMatrix.mapRect(tempRect, bounds)

                        }
                        .addEndListener { animation, canceled, value, velocity ->
                            lastFlingDistance = 0F
                            if (canceled && !bothReachBoundary) {
                                return@addEndListener
                            }
                            val endBounds = RectF()
                            renderMatrix.mapRect(endBounds, bounds)
                            onScrollFinish(
                                velocityX,
                                evaluateFastPageTurningCondition(
                                    velocityX,
                                    velocityY,
                                    startBounds,
                                    endBounds,
                                    viewport
                                )
                            )
                        }
                }
                flingAnimation?.start()
                return true
            }

            private fun evaluateFastPageTurningCondition(
                velocityX: Float,
                velocityY: Float,
                startRect: RectF,
                endRect: RectF,
                viewPort: RectF,
            ): Boolean {
                var reached = false
                if (abs(velocityX) > pageTurningVelocityXThreshold) {
                    if ((velocityX > 0
                                && startRect.left > viewPort.left - 1  // 滑动开始时，页面的左边界在视图左边界内才触发快速翻页，起始区域计算有一定的误差，所以这里减一
                                && abs(endRect.left - startRect.left) > pageTurningOffset)
                        || (velocityX < 0
                                && startRect.right < viewPort.right + 1 // 右边界
                                && abs(endRect.right - startRect.right) > pageTurningOffset)
                    ) {
                        reached = true
                    }
                }

                return reached
            }

            /**
             * Params:
             * e1 – The first down motion event that started the scrolling.
             * e2 – The move motion event that triggered the current onScroll.
             */
            override fun onScroll(
                e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float,
            ): Boolean {
                /**
                 * GestureDetector 首个传入的事件不是ACTION_DOWN，e1可能会为空，为SDK bug，后续发布版本修复
                 * https://issuetracker.google.com/issues/238920463
                 */
                // 画板缩放中
                if (!enableScroll || scaleDetector.isInProgress) {
                    return false
                }

                val distanceXWithFactor = translateWithFactor(distanceX)

                // 在触摸模式下，
                // 非画笔设备模式 1指滑动事件响应为绘图事件 画笔设备模式 1指响应为滑动时间
                // 2 指以上的事件响应为滑动事件
                if (mode == DoodleView.MODE_TOUCH && e2.pointerCount == 1) {
                    if (deviceMode == DoodleView.DEVICE_MODE_STYLUS) {
                        if (doodleEditLayer.isTextBorderShowing
                            || doodleEditLayer.isMarkdownBorderShowing
                            || doodleEditLayer.isSelectViewShowing
                            || doodleEditLayer.isGraphShapeViewShowing
                        ) {
                            return false
                        }
                        if (e2.getToolType(0) == TOOL_TYPE_STYLUS) {
                            return false
                        }
                    } else {
                        return false
                    }
                }

                sumDistanceX += abs(distanceX)
                sumDistanceY += abs(distanceY)

                val scaled = currentScale > pageSideFillDoodleSideScale

                val canScrollVertical =
                    (isInScroll && scaled)
                            //自定义了窗口，并且窗口比原view小
                            || (doodleModeConfig.enableCustomScrollRect && doodleModeConfig.renderViewport!!.height() < visualViewport.height())

                if (canScrollVertical) {
                    val scrollAngle = (atan2(distanceY, distanceX) * (180 / Math.PI)).toInt()
                    LogHelper.d(TAG, "$scrollAngle")

                    /**
                     *  left scroll angel
                     */
                    val moveLeft = scrollAngle in -15..15

                    /**
                     * right scroll angel
                     *
                     */
                    val moveRight = scrollAngle in 165..180 || scrollAngle in -180..-165
                    scrollDirection = if (moveLeft || moveRight) {
                        SCROLL_DIRECTION_HORIZONTAL
                    } else {
                        SCROLL_DIRECTION_VERTICAL
                    }

                    if (lastDirectionTrend == SCROLL_DIRECTION_UNKNOWN) {

                        translate(distanceX, distanceY)

                        // scroll direction decided
                        if (scrollDirection == SCROLL_DIRECTION_HORIZONTAL) {
                            continuousHorizontalDirectionCount++
                        } else {
                            // reset
                            continuousHorizontalDirectionCount = 0
                        }

                        if (continuousHorizontalDirectionCount >= SCROLL_HORIZONTAL_DIRECTION_SLOP) {
                            LogHelper.d(TAG, "$continuousHorizontalDirectionCount")
                            lastDirectionTrend = SCROLL_DIRECTION_HORIZONTAL
                        }

                    } else {

                        // scroll direction has been decided
                        if (lastDirectionTrend == SCROLL_DIRECTION_HORIZONTAL) {
                            translate(distanceXWithFactor, 0f)
                            return true
                        } else {
                            translate(distanceX, distanceY)
                        }

                    }

                } else {
                    translate(distanceXWithFactor, 0f)
                }
                return true
            }

            //长按选中交给modelanager消费
            override fun onLongPress(e: MotionEvent) {
                dispatchLongPressToModelManager(e)
            }
        })

    private fun checkAndTriggerTopObjectTapConfirmed(e: MotionEvent): Boolean {
        if (!doodleModeConfig.enableStickTopLayerClick) return false
        val event = MotionEvent.obtain(e)
        event.transform(touchMatrix)
        val result = doodleStickyOnTopLayer.checkAndTriggerTopObjectTapConfirmed(event.x, event.y)
        event.recycle()
        return result
    }

    private fun isTapConfirmedByPdfLink(e: MotionEvent): Boolean {
        if (!doodleModeConfig.enableJumpPageByPdfLink) return false
        val page = modelManager.currentPage ?: return false
        val point = floatArrayOf(e.x, e.y)
        if (page.pdfLinks != null) {
            modelManager.inverseRenderMatrix.mapPoints(point)
            page.pdfLinks.forEach { link ->
                if (link.pdfBounds.contains(point[0], point[1])) {
                    jumpToTargetPageByLink(link)
                    return true
                }
            }
        }
        return false
    }

    private fun jumpToTargetPageByLink(link: Link) {
        val targetPageIndex =
            getNotePageIndexFromPdfLink(modelManager.document, modelManager.page, link)
        if (targetPageIndex >= 0) {
            when (targetPageIndex) {
                modelManager.document.viewingPageIndex - 1 -> {
                    automaticFlipOverByLink(
                        OnPageChangeListener.Direction.PREVIOUS
                    )
                }

                modelManager.document.viewingPageIndex + 1 -> {
                    automaticFlipOverByLink(
                        OnPageChangeListener.Direction.NEXT
                    )
                }

                else -> {
                    pageChange(
                        OnPageChangeListener.Direction.PDF_LINK_SKIP,
                        targetPageIndex
                    )
                }
            }
        }
    }


    /**
     *  resetErrorRange页面回正后存在的误差范围
     */
    private val RESET_ERROR_RANGE = 10

    /**
     * 滑动过程带有阻尼效果
     */
    fun translateWithFactor(distanceX: Float): Float {
        if (!isFirstPage && !isLastPage) return distanceX

        val current = RectF()
        renderMatrix.mapRect(current, bounds)
        val halfDiff = (visualViewport.width() - current.width()) / 2F
        val maxOffset = visualViewport.width() / SCROLL_DAMPING_FACTOR
        val rightDistanceToScreenBoundary =
            visualViewport.width() - current.right - max(0F, halfDiff)
        val leftDistanceToScreenBoundary = current.left - max(0F, halfDiff)

        if (leftDistanceToScreenBoundary > RESET_ERROR_RANGE) {
            if (isFirstPage && distanceX < 0) {
                //反比例函数。间距越接近1/4屏幕距离，偏移越小，直到等于0
                val progress = maxOffset / leftDistanceToScreenBoundary
                return distanceX - distanceX / progress
            } else if (isFirstPage && distanceX > 0) {
                val progress = maxOffset / (maxOffset - leftDistanceToScreenBoundary)
                return distanceX - distanceX / progress
            }
        }

        if (rightDistanceToScreenBoundary > RESET_ERROR_RANGE) {
            if (isLastPage && distanceX > 0) {
                val progress = maxOffset / rightDistanceToScreenBoundary
                return distanceX - distanceX / progress
            } else if (isLastPage && distanceX < 0) {
                val progress = maxOffset / (maxOffset - rightDistanceToScreenBoundary)
                return distanceX - distanceX / progress
            }
        }

        return distanceX
    }

    /**
     *  transform by touch
     *
     *  include: translate scale
     */
    private var touchTransformed: Boolean = false

    /**
     * transform by scale
     *
     * include: scale
     */
    private var scaleTransformed: Boolean = false


    /**
     * 当前  Render Matrix 中保存的缩放值
     *
     * 因 [Matrix.MSCALE_X] 和 [Matrix.MSCALE_Y] 的值保存一致，所以直接可以调用 [Matrix.mapRadius] 简便获取
     */
    private val currentScale: Float
        get() = renderMatrix.mapRadius(1F)

    //默认位置让他超出屏幕
    private var dragStartX = -1F
    private var dragStartY = -1F

    init {
        scaleDetector.isQuickScaleEnabled = false
        scaleDetector.isStylusScaleEnabled = false
        setOnDragListener { v, event ->
            when (event.action) {
                DragEvent.ACTION_DRAG_STARTED -> {
                    if (!doodleModeConfig.enableDragAction) return@setOnDragListener false
                    doodleEditLayer.mInputTextView?.dismissTextView(false)
                    dragStartX = event.x
                    dragStartY = event.y
                    var isActionDrag = false
                    val clipDescription = event.clipDescription
                    if (clipDescription != null && clipDescription.mimeTypeCount > 0) {
                        val mimeType = clipDescription.getMimeType(0)
                        allowDragMimeTypeList.forEach { targetMimeType ->
                            val compareResult = MimeUtils.compareMimeTypes(mimeType, targetMimeType)
                            if (compareResult) {
                                isActionDrag = true
                                return@forEach
                            }
                        }
                    }
                    return@setOnDragListener isActionDrag
                }

                DragEvent.ACTION_DROP -> {
                    val pointDistance = MathUtils.pointDistance(
                        dragStartX.toDouble(),
                        dragStartY.toDouble(),
                        event.x.toDouble(),
                        event.y.toDouble()
                    )
                    if (pointDistance <= scaledTouchSlop) {
                        LogHelper.d(
                            TAG,
                            "pointDistance < scaledTouchSlop => $pointDistance < $scaledTouchSlop"
                        )
                        return@setOnDragListener false
                    }
                    val clipDescription = event.clipDescription ?: return@setOnDragListener false
                    if (clipDescription.mimeTypeCount <= 0) return@setOnDragListener false
                    val mimeType = clipDescription.getMimeType(0)
                    var handled = true
                    if (MimeUtils.mimeTypeIsImage(mimeType)) {
                        dragImage(event)
                    } else if (mimeType == MimeType.TEXT.mime) {
                        dragText(event)
                    } else if (mimeType == MimeType.HAND_WRITE_SNIPPET.mime) {
                        dragHandWriteSnippet(event)
                    } else if (mimeType == MimeType.KILO_STOKE.mime) {
                        handled = dragStroke(event)
                    } else {
                        throw IllegalStateException("did you forget handle this mimeType = $mimeType")
                    }
                    return@setOnDragListener handled
                }
            }
            return@setOnDragListener false
        }
    }

    private fun dragImage(event: DragEvent) {
        val clipData = event.clipData
        val dateItem = clipData.getItemAt(0)
        val isUseImageCompress = UserUsageConfig.isUseImageCompress
        if (SelectViewTwoDrag.SHARE_IMAGE_LABEL == event.clipDescription?.label) {
            UserUsageConfig.isUseImageCompress = false
            EditEvent.sendPictureMoveCopy()
        } else if (SnippetView.ADD_IMAGE_LABEL == event.clipDescription?.label) {
            EditEvent.sendSnippetDragAddImage()
        }
        val uri = dateItem.uri
        val alpha = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            clipData.description?.extras?.getInt(
                SelectViewTwoDrag.SHARE_IMAGE_EXTRA_INFO_ALPHA, Alpha.PERCENT_100.value
            ) ?: Alpha.PERCENT_100.value
        } else {
            Alpha.PERCENT_100.value
        }
        val requestDragAndDropPermissions =
            requestDragAndDropPermissions(context as Activity, event)
        modelManager.mOnCallInsertDragObjectListener?.callInsertImage(
            uri, event.x, event.y, alpha
        ) {
            UserUsageConfig.isUseImageCompress = isUseImageCompress
            requestDragAndDropPermissions?.release()
        }
    }

    private fun dragText(event: DragEvent) {
        val item = event.clipData.getItemAt(0)
        val text = item.text
        modelManager.textEditor.pasteText(
            text.toString(),
            Point(event.x.toInt(), event.y.toInt()),
            Clipboard.FIT_TEXT_WIDTH_TYPE_TOUCH_POINT
        )
    }

    private fun dragHandWriteSnippet(event: DragEvent) {
        val item = event.clipData.getItemAt(0)
        val snippetUUid = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            item.intent.getSerializableExtra(NoteSnippet.SHARE_SNIPPET_ID_KEY, UUID::class.java)
        } else {
            item.intent.getSerializableExtra(NoteSnippet.SHARE_SNIPPET_ID_KEY) as UUID?
        } ?: return
        modelManager.mOnCallInsertDragObjectListener?.callInsertHandWriteSnippet(
            snippetUUid,
            event.x,
            event.y
        )
    }

    private fun dragStroke(event: DragEvent): Boolean {
        renderMatrix.mapRect(rect, bounds)
        if (rect.contains(event.x, event.y)) {
            modelManager.mOnCallInsertDragObjectListener?.callInsertClipboardObjects(
                Clipboard.ClipboardTarget.SECONDARY,
                event.x,
                event.y
            )
            DraftPaperEvent.sendDraftPaperDragToEditorSuccessfully()
            return true
        }
        return false
    }

    private fun scaleBy(scale: Float, focusX: Float, focusY: Float) {
        val curScale = currentScale
        if (((SCALE_MAX - curScale) < 0.001 && scale >= 1) || ((curScale - operableMinScale) < 0.001 && scale <= 1)) {
            return
        }

        touchTransformed = true
        scaleTransformed = true

        // region 限定缩放的上下限
        val newScale = curScale * scale
        val newFactor = when {
            scale >= 1 && newScale > SCALE_MAX -> {
                SCALE_MAX / curScale
            }

            newScale < operableMinScale -> {
                operableMinScale / curScale
            }

            else -> {
                scale
            }
        }
        // endregion

        // TODO 如果需要在缩放时调整页面位置，可以在此处进行
        // 可能的场景是在放大到超出可视区域后，在侧边进行缩小时会导致页面偏向当前侧边
        // 如果需要进行居中校正可在此进行

        renderMatrix.postScale(newFactor, newFactor, focusX, focusY)
        applyTransform(TransformType.SCALE)
        onScaleFinish(true)
    }

    private fun scaleToWithAnimation(
        scale: Float,
        pointX: Float,
        pointY: Float,
        duration: Long = 300,
    ) {
        val curScale = currentScale
        if (abs(curScale - scale) < 0.001) {
            LogHelper.d(TAG, "scale difference less than 0.001")
            return
        }

        val viewport = if (doodleModeConfig.enableCustomScrollRect) {
            doodleModeConfig.renderViewport!!
        } else {
            visualViewport
        }

        touchTransformed = true
        scaleTransformed = true

        //这次缩放相当于上次缩放的缩放值
        val newFactor = when (scale) {
            SCALE_MAX -> {
                SCALE_MAX / curScale
            }

            minScale -> {
                minScale / curScale
            }

            else -> {
                scale / curScale
            }
        }

        if (newFactor >= 1) {
            EditEvent.sendDoodleQuickScale("amplify")
        } else {
            EditEvent.sendDoodleQuickScale("reduce")
        }

        //目标Matrix
        val targetMatrix = Matrix(renderMatrix).apply {
            postScale(newFactor, newFactor, pointX, pointY)
        }
        //进行dx,dy矫正
        val current = RectF()
        targetMatrix.mapRect(current, bounds)
        val dx = when {
            // 页面宽度小于屏幕宽度通过中心点的位置进行左右居中
            current.width() < viewport.width() -> viewport.centerX() - current.centerX()
            // 右滑 页面向左移动对齐左边距
            current.left > 0 -> -current.left
            // 左滑 页面向右移动对齐右边距
            current.right < viewport.right -> viewport.right - current.right
            else -> 0F
        }

        val dy = when {
            // 进行上下居中
            current.height() < viewport.height() -> viewport.centerY() - current.centerY()
            // 向上移动对齐顶部到 0 位置
            current.top > 0 -> -current.top
            // 向下移动对齐底部
            current.bottom < viewport.bottom -> viewport.bottom - current.bottom
            else -> 0F
        }
        targetMatrix.postTranslate(dx, dy)

        val currentMatrix = Matrix(renderMatrix)
        //https://stackoverflow.com/questions/33712067/android-how-to-animation-move-the-view-by-using-a-matrix
        val a = FloatArray(9)
        val b = FloatArray(9)
        currentMatrix.getValues(a)
        targetMatrix.getValues(b)
        val scaleStart = PointF(a[Matrix.MSCALE_X], a[Matrix.MSCALE_Y])
        val scaleEnd = PointF(b[Matrix.MSCALE_X], b[Matrix.MSCALE_Y])
        val translateStart = PointF(a[Matrix.MTRANS_X], a[Matrix.MTRANS_Y])
        val translateEnd = PointF(b[Matrix.MTRANS_X], b[Matrix.MTRANS_Y])
        ValueAnimator.ofFloat(0F, 1F).apply {
            setDuration(duration)
            addUpdateListener { animation ->
                val interpolatedTime = animation.animatedFraction
                val sFactor = PointF(
                    scaleEnd.x * interpolatedTime / scaleStart.x + 1 - interpolatedTime,
                    scaleEnd.y * interpolatedTime / scaleStart.y + 1 - interpolatedTime
                )
                val tFactor = PointF(
                    (translateEnd.x - translateStart.x) * interpolatedTime,
                    (translateEnd.y - translateStart.y) * interpolatedTime
                )
                renderMatrix.reset()
                renderMatrix.postScale(scaleStart.x, scaleStart.y, 0f, 0f)
                renderMatrix.postScale(sFactor.x, sFactor.y, 0f, 0f)
                renderMatrix.postTranslate(translateStart.x, translateStart.y)
                renderMatrix.postTranslate(tFactor.x, tFactor.y)
                applyTransform(TransformType.SCALE)
            }
            addListener(onEnd = {
                onScaleFinish()
                notifyTransformFinished(TransformType.SCALE, true)
            })
        }.start()
    }

    private fun translate(dx: Float, dy: Float) {
        touchTransformed = true
        var translateY = dy
        val current = RectF()
        renderMatrix.mapRect(current, bounds)
        val viewport = if (doodleModeConfig.enableCustomScrollRect) {
            doodleModeConfig.renderViewport!!
        } else {
            visualViewport
        }

        // filter maximum vertical distance
        if (dy < 0) {
            // move down
            if (current.top - viewport.top > MAXIMUM_VERTICAL_DISTANCE) {
                translateY = 0F
            }
        } else {
            // move up
            if ((viewport.bottom - current.bottom) > MAXIMUM_VERTICAL_DISTANCE) {
                translateY = 0F
            }
        }

        renderMatrix.postTranslate(-dx, -translateY)
        applyTransform(TransformType.TRANSLATE)
    }

    override fun onDraw(canvas: Canvas) {
        pager.onDraw(canvas, isFirstPage, isLastPage, doodleModeConfig)
    }

    private fun applyTransform(transformType: TransformType) {
        renderMatrix.mapRect(rect, bounds)
        pager.update(renderMatrix, scaleTransformed)
        //刷新
        invalidate()

        notifyTransformChanged(transformType)
    }

    private fun notifyTransformChanged(transformType: TransformType) {
        onTransformChangeListeners.forEach {
            it.onTransformChanged(renderMatrix, transformType, actualDoodleScale())
        }
        if (transformType == TransformType.SCALE || transformType == TransformType.SCALE_TRANSLATE) {
            notifyScaleChange()
        }
    }

    private fun notifyTransformFinished(transformType: TransformType, isReDraw: Boolean) {
        onTransformChangeListeners.forEach {
            it.onTransformFinished(renderMatrix, transformType, isReDraw)
        }
        if (transformType == TransformType.SCALE || transformType == TransformType.SCALE_TRANSLATE) {
            notifyScaleFinish()
        }
    }

    private fun notifyScaleFinish() {
        onScaleChangeListeners.forEach {
            it.onScaleFinish(currentScale)
        }
    }

    private fun notifyScaleChange() {
        onScaleChangeListeners.forEach {
            it.onScaleChanged(currentScale)
        }
    }

    private fun onScaleFinish(isFollowUserFinger: Boolean = false) {
        scaleTransformed = false
        if (!isFollowUserFinger) {
            touchTransformed = false
        }
    }

    @JvmField
    val touchMatrix = Matrix()
    val renderMatrix = Matrix()

    var interceptEvent = false
        private set
    private var dispatchedDownEventToScrollDetector = false

    private fun onScrollFinish(
        velocityX: Float = 0F,
        reachedFastPageTurningCondition: Boolean = false,
    ) {
        sumDistanceX = 0F
        sumDistanceY = 0F
        if (touchTransformed) {
            touchTransformed = false
            LogHelper.d(TAG, "resetScrollPosition invoke")
            resetScrollPosition(velocityX, reachedFastPageTurningCondition)
        }
        lastDirectionTrend = SCROLL_DIRECTION_UNKNOWN
        continuousHorizontalDirectionCount = 0
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (!pager.isReady() || event == null) return true
        LogHelper.d(TAG, "isFlipOverAnimation = $isFlipOverAnimation")
        if (isFlipOverAnimation || isAddPageAnimation) return true

        /**
         * GestureDetector 首个传入的事件不是ACTION_DOWN，其回调可能包含空事件，为SDK bug，后续发布版本修复
         * https://issuetracker.google.com/issues/238920463
         */
        val dispatchToScrollDetector =
            event.actionMasked == MotionEvent.ACTION_DOWN || dispatchedDownEventToScrollDetector
        if (event.actionMasked == MotionEvent.ACTION_CANCEL || event.actionMasked == MotionEvent.ACTION_UP) {
            dispatchedDownEventToScrollDetector = false
        }

        //音频编辑状态及k键模式屏蔽画板手势事件
        if (!interruptTransform) {
            if (!scaleDetector.isInProgress && !scaleTransformed) {
                if (customDetector.onTouchEvent(event)) {
                    return true
                }
            }

            if (!isInScroll) {
                // current state is not in scroll state
                scaleDetector.onTouchEvent(event)
            }

            // current state is in scale state
            if (!scaleDetector.isInProgress && dispatchToScrollDetector) {
                scrollDetector.onTouchEvent(event)
                if (event.actionMasked == MotionEvent.ACTION_DOWN) {
                    dispatchedDownEventToScrollDetector = true
                }
            }

            if (modelManager.inputMode == InputMode.PRESENTATION) {
                // current state is in scroll state or scale state
                if (!isInScroll && !scaleDetector.isInProgress) {
                    if (event.actionMasked == MotionEvent.ACTION_UP) {
                        if (!isInFling && !scaleTransformed) {
                            onScrollFinish()
                        }
                    }
                    return false
                }
            }
        }


        if (event.actionMasked == MotionEvent.ACTION_DOWN) {
            interceptEvent = false
        } else if (event.actionMasked == MotionEvent.ACTION_UP) {
            if (!isInFling && !scaleTransformed && touchTransformed) {
                onScrollFinish()
            }

        } else if (event.actionMasked == MotionEvent.ACTION_POINTER_DOWN) {
            // 给 ModelManager 发送一个 [MotionEvent.ACTION_CANCEL]
            interceptEvent = true
            dispatchToModelManager(
                MotionEvent.obtain(
                    event.downTime,
                    event.eventTime,
                    MotionEvent.ACTION_CANCEL,
                    event.x,
                    event.y,
                    event.metaState
                )
            )
        }

        if (doodleEditLayer.receiveTouchEvent()) {
            if (deviceMode == DoodleView.DEVICE_MODE_STYLUS) {
                // 并非都是手指，转发给 Model Manager 处理
                if (!event.isFinger() || doodleEditLayer.isRecordViewEditing) {
                    dispatchToModelManager(event)
                }
            } else {
                dispatchToModelManager(event)
            }
        } else if (mode != DoodleView.MODE_VIEW && !interceptEvent) {
            if (deviceMode == DoodleView.DEVICE_MODE_STYLUS) {
                // 并非都是手指，转发给 Model Manager 处理

                if (!event.isFinger() || modelManager.inputMode == InputMode.TEXT || modelManager.inputMode == InputMode.IMAGE || modelManager.inputMode == InputMode.GRAPH || modelManager.inputMode == InputMode.SNIPPET) {
                    dispatchToModelManager(event)
                }

                if (event.isFinger() && modelManager.inputMode != InputMode.TEXT && modelManager.inputMode != InputMode.IMAGE && modelManager.inputMode != InputMode.GRAPH && modelManager.inputMode != InputMode.SNIPPET) {
                    modelManager.resetToolViewsStylusWithFinger()
                    modelManager.exitSelectionMode()
                    modelManager.unselect()
                }
                // else: Drop Finger Event
            } else {
                dispatchToModelManager(event)
            }
        }

        return true
    }

    private fun getScrollDirection(
        velocityX: Float,
        reachedFastPageTurningCondition: Boolean = false,
    ): OnPageChangeListener.Direction {
        val current = RectF()
        renderMatrix.mapRect(current, bounds)
        var direction = OnPageChangeListener.Direction.UNSET
        if (!scaleTransformed) {

            // region 计算翻页的方向 （ 翻页判定：手指向左和右滑动距离大于屏幕1/3）
            //  手指向左滑  意图：翻下一页  滑动距离 = （原始页面右边距离 = 原始页面右边 + 原始页面间隔*1/2 = 屏幕总宽最右边） - （当前页面右边距离 = 当前页面右边 + 原始页面间隔*1/2） -》 如果向左滑动距离等于或超过1/3，当前页面最右边距离推导出等于或小于0.67 * 屏幕宽度
            //  手指向右滑  意图：翻上一页  滑动距离 = （当前页面左边距离 = 当前页面左边 - 原始页面间隔*1/2） - （原始页面左边距离 = 原始页面左边 - 原始页面间隔*1/2 = 屏幕宽度最左边） -》 如果向右滑动距离等于或超过1/3，当前页面最左边距离推导出等于或大于0.33 * 屏幕宽度
            val halfDiff = (visualViewport.width() - current.width()) / 2F
            val right = current.right + max(0F, halfDiff)
            val left = current.left - max(0F, halfDiff)

            /**
             * current.width()当前页面宽度 visualViewport.width()屏幕宽度包括页面之间的空隙
             * 当前页面右边界+页面间隙 表示进行右滑时 下一页的左边界超出当前屏幕的3/4时翻页
             * 当前页面左边-页面间接 表示进行左滑时 下一页的右边界超出当前屏幕的1/4时翻页
             **/
            direction = when {
                reachedFastPageTurningCondition && velocityX > 0 && doodleModeConfig.enableScrollSwitchPage && !isFirstPage -> {
                    OnPageChangeListener.Direction.PREVIOUS
                }

                reachedFastPageTurningCondition && velocityX < 0 && doodleModeConfig.enableScrollSwitchPage && !isLastPage -> {
                    OnPageChangeListener.Direction.NEXT
                }

                right < visualViewport.width() * 3 / 4f -> {
                    if (isLastPage) {
                        if (!doodleModeConfig.enableScrollCreatePage || !doodleModeConfig.enableScrollSwitchPage) {
                            OnPageChangeListener.Direction.UNSET
                        } else {
                            OnPageChangeListener.Direction.CREATE_NEXT
                        }
                    } else {
                        if (!doodleModeConfig.enableScrollSwitchPage) {
                            OnPageChangeListener.Direction.UNSET
                        } else {
                            OnPageChangeListener.Direction.NEXT
                        }
                    }
                }

                left > visualViewport.width() / 4f -> {
                    if (isFirstPage) {
                        if (!doodleModeConfig.enableScrollCreatePage || !doodleModeConfig.enableScrollSwitchPage) {
                            OnPageChangeListener.Direction.UNSET
                        } else {
                            OnPageChangeListener.Direction.CREATE_PREVIOUS
                        }
                    } else {
                        if (!doodleModeConfig.enableScrollSwitchPage) {
                            OnPageChangeListener.Direction.UNSET
                        } else {
                            OnPageChangeListener.Direction.PREVIOUS
                        }
                    }
                }

                else -> OnPageChangeListener.Direction.UNSET
            }
            // endregion
        }
        return direction
    }

    fun clickToTurnPage(direction: OnPageChangeListener.Direction) {
        if (isFlipOverAnimation || isAddPageAnimation) return
        val current = RectF()
        renderMatrix.mapRect(current, bounds)

        val dx = when (direction) {
            OnPageChangeListener.Direction.PREVIOUS -> {
                if (current.left < 0) {
                    pager.adjustEdge(OnPageChangeListener.Direction.PREVIOUS) + current.left
                } else {
                    pager.adjustEdge(OnPageChangeListener.Direction.PREVIOUS)
                }
            }

            OnPageChangeListener.Direction.NEXT -> {
                if (current.right > visualViewport.right) {
                    -(current.right - visualViewport.right + pager.adjustEdge(OnPageChangeListener.Direction.NEXT))
                } else {
                    -pager.adjustEdge(OnPageChangeListener.Direction.NEXT)
                }
            }

            else -> 0F
        }

        val flipOverAnimation = ObjectAnimator.ofFloat(0F, 1F).apply {
            duration = 300
            addUpdateListener {
                val fraction = it.animatedFraction
                val currentX = dx * fraction
                renderMatrix.postTranslate(currentX - lastX, 0F)
                applyTransform(TransformType.TRANSLATE)
                lastX = currentX
            }
            addListener(onStart = {
                lastX = 0f
                isFlipOverAnimation = true
            }, onEnd = {
                isFlipOverAnimation = false
                post {
                    EditEvent.sendEditPageTurning(mode == DoodleView.MODE_VIEW)
                    pageChange(direction)
                }
            }, onCancel = {
                isFlipOverAnimation = false
            })
        }
        flipOverAnimation.start()
    }

    fun onViewportUpdate() {
        val current = RectF()
        renderMatrix.mapRect(current, bounds)

        val viewport = if (doodleModeConfig.enableCustomScrollRect) {
            doodleModeConfig.renderViewport!!
        } else {
            visualViewport
        }

        val dx = when {
            // 页面宽度小于屏幕宽度通过中心点的位置进行左右居中
            current.width() < viewport.width() -> viewport.centerX() - current.centerX()
            // 右滑 页面向左移动对齐左边距
            current.left > 0 -> -current.left
            // 左滑 页面向右移动对齐右边距
            current.right < viewport.right -> viewport.right - current.right
            else -> 0F
        }

        val dy = when {
            // 进行上下居中
            current.height() < viewport.height() -> viewport.centerY() - current.centerY()
            // 向上移动对齐顶部到 0 位置
            current.top > 0 -> -current.top
            // 向下移动对齐底部
            current.bottom < viewport.bottom -> viewport.bottom - current.bottom
            else -> 0F
        }

        if (dx == 0F && dy == 0F) {
            return
        }
        renderMatrix.postTranslate(dx, dy)
        applyTransform(TransformType.TRANSLATE)
    }

    fun onViewportUpdateFinish() {
        notifyTransformFinished(TransformType.TRANSLATE, true)
    }

    private fun resetScaleValue(pointX: Float, pointY: Float) {
        if (currentScale < minScale) {
            scaleToWithAnimation(minScale, pointX, pointY)
        }
    }

    private fun resetScrollPosition(
        velocityX: Float,
        reachedFastPageTurningCondition: Boolean,
    ) {
        val current = RectF()
        renderMatrix.mapRect(current, bounds)
        val direction = getScrollDirection(velocityX, reachedFastPageTurningCondition)

        val viewport = if (doodleModeConfig.enableCustomScrollRect) {
            doodleModeConfig.renderViewport!!
        } else {
            visualViewport
        }

        val dx = when {
            // 页面宽度小于屏幕宽度通过中心点的位置进行左右居中
            current.width() < viewport.width() -> viewport.centerX() - current.centerX()
            // 右滑 页面向左移动对齐左边距
            current.left > 0 -> -current.left
            // 左滑 页面向右移动对齐右边距
            current.right < viewport.right -> viewport.right - current.right
            else -> 0F
        } + when (direction) { // 根据页面行进方向，决定使用的虚拟框位置，进行框框校正
            OnPageChangeListener.Direction.NEXT -> {
                // 表示继续左移，目标左虚拟框，使下一页进入中间
                -pager.adjustEdge(direction)
            }

            OnPageChangeListener.Direction.PREVIOUS -> {
                // 表示继续右移，目标右虚拟框，使上一页进入中间
                +pager.adjustEdge(direction)
            }

            else -> 0F // 目标中虚拟框，使用上面的计算值即可，无需校正
        }

        val dy = when {
            // 进行上下居中
            current.height() < viewport.height() -> viewport.centerY() - current.centerY()
            // 向上移动对齐顶部到 0 位置
            current.top > 0 -> -current.top
            // 向下移动对齐底部
            current.bottom < viewport.bottom -> viewport.bottom - current.bottom
            else -> 0F
        }

        if (dx == 0F && dy == 0F) {
            notifyTransformFinished(TransformType.TRANSLATE, true)
            return
        }

        val createPageFlipDeviantX = when (direction) {
            OnPageChangeListener.Direction.CREATE_NEXT -> -pager.adjustEdge(direction)
            OnPageChangeListener.Direction.CREATE_PREVIOUS -> pager.adjustEdge(direction)
            else -> 0f
        }

        val flipOverAnimation = ObjectAnimator.ofFloat(0F, 1F).apply {
            duration = 300
            addUpdateListener {
                val fraction = it.animatedFraction
                val currentX = dx * fraction
                val currentY = dy * fraction

                renderMatrix.postTranslate(currentX - lastX, currentY - lastY)
                applyTransform(TransformType.TRANSLATE)

                lastX = currentX
                lastY = currentY
            }

            addListener(onStart = {
                lastX = 0f
                lastY = 0f
                isFlipOverAnimation = true
            }, onEnd = {
                isFlipOverAnimation = false
                post {
                    if (direction != OnPageChangeListener.Direction.UNSET) {
                        EditEvent.sendEditPageTurning(mode == DoodleView.MODE_VIEW)
                        pageChange(direction)
                    } else {
                        notifyTransformFinished(TransformType.TRANSLATE, true)
                    }
                }
            }, onCancel = {
                isFlipOverAnimation = false
            })
        }

        if (direction == OnPageChangeListener.Direction.CREATE_NEXT || direction == OnPageChangeListener.Direction.CREATE_PREVIOUS) {
            val springBackAnimation = ObjectAnimator.ofFloat(0F, 1F).apply {
                duration = 400
                addUpdateListener {
                    val fraction = it.animatedFraction
                    val currentX = dx * fraction
                    val currentY = dy * fraction

                    renderMatrix.postTranslate(currentX - lastX, currentY - lastY)
                    applyTransform(TransformType.TRANSLATE)

                    lastX = currentX
                    lastY = currentY
                }

                addListener(onStart = {
                    lastX = 0f
                    lastY = 0f
                    isAddPageAnimation = true
                }, onEnd = {
                    val thumbnail =
                        pager.getSlideAddPageThumbnail()?.copy(Bitmap.Config.ARGB_8888, false)
                            ?: return@addListener
                    pager.clearSlideAddPageThumbnailView()
                    if (direction == OnPageChangeListener.Direction.CREATE_NEXT) {
                        pager.setThumbnailNext(
                            thumbnail
                        )
                    } else if (direction == OnPageChangeListener.Direction.CREATE_PREVIOUS) {
                        pager.setThumbnailPrevious(
                            thumbnail
                        )
                    }
                })
            }

            val createPageFlipOverAnimation = ObjectAnimator.ofFloat(0F, 1F).apply {
                duration = 500
                addUpdateListener {
                    val fraction = it.animatedFraction
                    val currentX = createPageFlipDeviantX * fraction

                    renderMatrix.postTranslate(currentX - lastX, 0f)
                    applyTransform(TransformType.TRANSLATE)
                    lastX = currentX
                }

                addListener(onStart = {
                    lastX = 0F
                }, onEnd = {
                    post {
                        EditEvent.sendEditPageTurning(mode == DoodleView.MODE_VIEW)
                        pageChange(direction)
                    }
                    isAddPageAnimation = false
                })
            }

            val pagingDelayTime = 800L

            AnimatorSet().apply {
                this.play(springBackAnimation)
                this.play(createPageFlipOverAnimation).after(pagingDelayTime)
            }.start()

        } else {
            flipOverAnimation.start()
        }
    }

    fun automaticFlipOverByLink(direction: OnPageChangeListener.Direction) {
        val current = RectF()
        renderMatrix.mapRect(current, bounds)
        val flipOverDeviantX = when (direction) {
            OnPageChangeListener.Direction.NEXT -> -pager.adjustEdge(OnPageChangeListener.Direction.NEXT)
            OnPageChangeListener.Direction.PREVIOUS -> pager.adjustEdge(OnPageChangeListener.Direction.PREVIOUS)
            else -> 0f
        }

        ObjectAnimator.ofFloat(0F, 1F).apply {
            duration = 500
            addUpdateListener {
                val fraction = it.animatedFraction
                val currentX = flipOverDeviantX * fraction

                renderMatrix.postTranslate(currentX - lastX, 0f)
                applyTransform(TransformType.TRANSLATE)
                lastX = currentX
            }

            addListener(onStart = {
                lastX = 0F
            }, onEnd = {
                post {
                    pageChange(direction)
                }
                notifyTransformFinished(TransformType.TRANSLATE, true)
            })
        }.start()
    }


    fun pageChange(direction: OnPageChangeListener.Direction, index: Int = 0) {
        onPageChangeListener?.onPageChanged(direction, index)
        pager.clearCurrentThumbnail()
    }

    private var lastX = 0F
    private var lastY = 0F

    private fun dispatchToModelManager(event: MotionEvent): Boolean {
        val multiPoint = event.pointerCount > 1
        if (multiPoint) {
            return false
        }
        return doodleEditLayer.onTouchEvent(
            event, renderMatrix, touchMatrix, isInScroll, scaleDetector.isInProgress, deviceMode
        )
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        val mActivity = findActivity(context)
        if (visibility == VISIBLE && mActivity != null && mActivity.windowManager != null) {
            val display: Display = mActivity.windowManager.defaultDisplay
            val refreshRate = display.refreshRate
            StylusToolKit.obtainPointEstimator()?.setRefreshRate(refreshRate)
        }
    }

    private fun dispatchLongPressToModelManager(event: MotionEvent): Boolean {
        val multiPoint = event.pointerCount > 1
        if (multiPoint) {
            return false
        }
        return doodleEditLayer.detectGesture(event, deviceMode)
    }

    private var pageWidth = 0
    private var pageHeight = 0
    private var sizeConfirmed = false

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        sizeConfirmed = true
        pager.onSizeChanged(w, h, oldw, oldh)
        visualViewport.set(0F, 0F, w.toFloat(), h.toFloat())
        if (triggerSizeChanged) {
            onDoodleSizeChanged(w, h, oldw, oldh)
            triggerSizeChanged = !triggerSizeChanged
        }
        updateSideToSideScale()
        updateFillDoodleScale()

        StylusToolKit.obtainPointEstimator()?.onDoodleViewSizeChanged(this, w, h, oldw, oldh)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        StylusToolKit.obtainPointEstimator()?.onDoodleViewAttachedToWindow(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        StylusToolKit.obtainPointEstimator()?.onDoodleViewDetachedFromWindow(this)
    }

    fun updateTransform() {
        applyTransform(TransformType.SCALE_TRANSLATE)
        notifyTransformFinished(TransformType.SCALE_TRANSLATE, true)
        onScaleFinish()
    }

    private fun applyInitTransform(
        scale: Float,
        isReDraw: Boolean,
        needCenterRenderRectInPdfPx: RectF?,
    ) {
        renderMatrix.postScale(scale, scale)
        if (renderMatrix.scale < minScale) renderMatrix.postScale(minScale, minScale)
        adjustRenderToCenter(needCenterRenderRectInPdfPx)
        renderMatrix.mapRect(rect, bounds)
        pager.update(renderMatrix, scaleTransformed)
        //刷新
        invalidate()
    }

    /**
     *  渲染区域，此区域最大为屏幕大小
     */
    fun getRenderRect(): RectF {
        renderMatrix.mapRect(rect, bounds)
        rect.intersect(visualViewport)
        return rect
    }

    /**
     * 页面变换后的区域
     */
    fun getTransformPageRect(): RectF {
        val destRectF = RectF()
        renderMatrix.mapRect(destRectF, bounds)
        return destRectF
    }

    fun getOriginalRectInPdf(): RectF {
        val originalRectInPdf = getRenderRect()
        currentPage?.let { page ->
            val invertRenderMatrix = Matrix()
            CoordinateTransformUtils.transformTargetSize2SourceSize(
                renderMatrix, invertRenderMatrix, page.initialPageWidthInPixel.toFloat(),
                ptToPx(page.pageSizeInPoint.width)
            )
            invertRenderMatrix.mapRect(originalRectInPdf)
        }
        return originalRectInPdf
    }

    /**
     * 页面的原始大小和位置，可以组合 Render Matrix 跟踪绘制区域
     */
    private val bounds = RectF()

    /**
     * 跟踪当前绘制区域，充当临时变量使用，使用前需要使用 [Matrix.mapRect] 进行映射
     *
     *    renderMatrix.mapRect(rect, bounds)
     */
    private val rect = RectF()

    /**
     * 显示窗口的大小和位置
     */
    private val visualViewport = RectF()

    /**
     * 校正初始布局至屏幕中央
     */
    private fun adjustRenderToCenter(needCenterRenderRectInPdfPx: RectF?) {
        renderMatrix.mapRect(rect, bounds)
        var dx = 0F
        var dy = 0F
        val viewport = if (doodleModeConfig.enableCustomScrollRect) {
            doodleModeConfig.renderViewport!!
        } else {
            visualViewport
        }
        //原始逻辑不变
        if (needCenterRenderRectInPdfPx == null) {
            dx = if (rect.width() < viewport.width()) { // 宽度超出，进行 x 校正
                viewport.centerX() - rect.centerX()
            } else {
                0F
            }
            dy = if (rect.height() < viewport.height()) { // 宽度超出，进行 y 校正
                viewport.centerY() - rect.centerY()
            } else {
                0F
            }
        } else {
            val matrix = Matrix()
            val scale = currentPage?.initialScale ?: 1F
            matrix.postScale(scale, scale)
            val transformRectF = RectF()
            matrix.mapRect(transformRectF, needCenterRenderRectInPdfPx)
            renderMatrix.mapRect(transformRectF)
            //这部分是把需要居中的区域居中
            dx += viewport.centerX() - transformRectF.centerX()
            dy += viewport.centerY() - transformRectF.centerY()
            rect.offset(
                dx,
                dy
            )
            //这部分是把居中以后的位置摆正
            dx += when {
                // 页面宽度小于屏幕宽度通过中心点的位置进行左右居中
                rect.width() < viewport.width() -> viewport.centerX() - rect.centerX()
                // 右滑 页面向左移动对齐左边距
                rect.left > 0 -> -rect.left
                // 左滑 页面向右移动对齐右边距
                rect.right < viewport.right -> viewport.right - rect.right
                else -> 0F
            }

            dy += when {
                // 进行上下居中
                rect.height() < visualViewport.height() -> visualViewport.centerY() - rect.centerY()
                // 向上移动对齐顶部到 0 位置
                rect.top > 0 -> -rect.top
                // 向下移动对齐底部
                rect.bottom < visualViewport.bottom -> visualViewport.bottom - rect.bottom
                else -> 0F
            }
        }

        renderMatrix.postTranslate(dx, dy)
    }

    fun actualDoodleScale(): Float {
        return currentScale / minScale
    }

    private var isFirstInit: Boolean = true

    private var currentPage: Page? = null

    private var isFirstPage: Boolean = false

    private var isLastPage: Boolean = false

    fun updateCurrentPageInfo(
        curPage: Page,
        isReDraw: Boolean,
        needCenterRenderRectInPdfPx: RectF? = null,
    ) {
        currentPage = curPage
        pageWidth = curPage.initialPageWidthInPixel
        pageHeight = curPage.initialPageHeightInPixel
        var curScale = 1f
        if (actualDoodleScale() >= 1f) {
            curScale = actualDoodleScale()
        }
        renderMatrix.reset()
        if (doodleModeConfig.minScale != DoodleModeConfig.SCALE_NOT_SET) {
            minScale = doodleModeConfig.minScale
        } else {
            minScale = min(
                width / pageWidth.toFloat(),
                height / pageHeight.toFloat()
            ) // 因为目前默认的缩放方式是以等宽度为基准的，所以当前设置的宽度即为最小缩放
            renderMatrix.postScale(minScale, minScale)
        }
        val scale = minScale
        renderMatrix.postScale(curScale, curScale)
        bounds.set(0F, 0F, pageWidth.toFloat(), pageHeight.toFloat())
        updateSideToSideScale()

        updateFillDoodleScale()
        val renderRectInPdf = if (doodleModeConfig.renderRectInPdf != null) {
            doodleModeConfig.renderRectInPdf
        } else {
            needCenterRenderRectInPdfPx
        }
        //只有第一次需要将纸张放大缩小，填充满整个屏幕，后面只需要使用记住对缩放就可以
        if (isFirstInit) {
            var initScale = if (doodleModeConfig.initScale != DoodleModeConfig.SCALE_NOT_SET) {
                doodleModeConfig.initScale
            } else {
                scale
            }
            if (doodleModeConfig.lastPageInitialScale != DoodleModeConfig.SCALE_NOT_SET && initScale != minScale && doodleModeConfig.autoAdaptScale) {
                initScale = initScale * doodleModeConfig.lastPageInitialScale / curPage.initialScale
                initScale = max(initScale, minScale)
                initScale = min(initScale, screenSizeChangeMaxScale)
            }
            applyInitTransform(
                initScale,
                isReDraw || ScaleUtils.getBitmapScale(initScale) != ScaleUtils.getBitmapScale(
                    pageSideFillDoodleSideScale
                ),
                renderRectInPdf
            )
            isFirstInit = false
        } else {
            applyInitTransform(
                1F,
                isReDraw || ScaleUtils.getBitmapScale(scale) != ScaleUtils.getBitmapScale(
                    pageSideFillDoodleSideScale
                ),
                renderRectInPdf
            )
            doodleModeConfig.renderRectInPdf = null
        }
    }

    fun updatePageThumbnail(document: Document, page: Page, bitmap: Bitmap) {
        if (currentPage !== page) return
        BitmapLruCachePool.put(getPageThumbnailCacheKey(document, page), bitmap)
        pager.setCurrentThumbnail(bitmap, page)
        invalidate()
    }

    private fun updateSideToSideScale() {
        pageSideFillDoodleSideScale =
            min(width / pageWidth.toFloat(), height / pageHeight.toFloat())
    }

    private fun updateFillDoodleScale() {
        fillDoodleScale = max(width / pageWidth.toFloat(), height / pageHeight.toFloat())
    }

    private fun onDoodleSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        minScale = min(w / pageWidth.toFloat(), h / pageHeight.toFloat())
        var newScale: Float
        // 因为目前默认的缩放方式是以等宽度为基准的，所以当前设置的宽度即为最小缩放
        val pageProportion: Float = pageHeight / pageWidth.toFloat()
        val doodleProportion: Float = h / w.toFloat()
        val oldDoodleProportion: Float = oldh / oldw.toFloat()
        val scale: Float = if (pageProportion < oldDoodleProportion && currentScale <= minScale) {
            minScale
        } else {
            doodleProportion / oldDoodleProportion
        }
        if (minScale >= SCALE_MAX) {
            newScale = SCALE_MAX
        } else {
            newScale = scale * currentScale
            newScale = newScale.coerceIn(minScale, SCALE_MAX)
        }
        if (newScale != currentScale) {
            val realZoomFactor = newScale / currentScale
            renderMatrix.postScale(realZoomFactor, realZoomFactor)
        }
        adjustRenderToCenter(null)
        applyTransform(TransformType.SCALE_TRANSLATE)
        onScaleFinish()
        notifyTransformFinished(TransformType.SCALE_TRANSLATE, false)
    }

    suspend fun updateThumbnail(
        document: Document,
        current: Page,
        previous: Page?,
        next: Page?
    ) {
        val currentThumbnail = fetchPageThumbnail(document, current)
        withContext(Dispatchers.Main) {
            pager.setCurrentThumbnail(currentThumbnail, current)
        }

        if (previous == null) {
            pager.setThumbnailPrevious(null, 0, 0)
        } else {
            val previousThumbnail = fetchPageThumbnail(document, previous)
            withContext(Dispatchers.Main) {
                pager.setThumbnailPrevious(
                    previousThumbnail,
                    previous.initialPageWidthInPixel,
                    previous.initialPageHeightInPixel
                )
            }
        }

        if (next == null) {
            pager.setThumbnailNext(null, 0, 0)
        } else {
            val nextThumbnail = fetchPageThumbnail(document, next)
            withContext(Dispatchers.Main) {
                pager.setThumbnailNext(
                    nextThumbnail,
                    next.initialPageWidthInPixel,
                    next.initialPageHeightInPixel
                )
            }
        }

    }

    suspend fun updateThumbnailForSlideAddPag(
        document: Document,
        current: Page,
        previous: Page?,
        next: Page?
    ) {
        isFirstPage = previous == null
        isLastPage = next == null

        if (isFirstPage || isLastPage) {
            val thumbnail = fetchPageBackgroundThumbnail(document, current)
            pager.setThumbnailForSlideAddPage(thumbnail)
        } else {
            pager.setThumbnailForSlideAddPage(null)
        }
    }

    private suspend fun fetchPageThumbnail(document: Document, page: Page): Bitmap? {
        val cacheKey = getPageThumbnailCacheKey(document, page)
        val cacheThumbnail = BitmapLruCachePool.get(cacheKey)
        if (cacheThumbnail != null) {
            return cacheThumbnail
        }
        return withContext(Dispatchers.IO) {
            val thumbnailFile = getThumbnailFile(document, page)
            val bitmap = if (thumbnailFile != null && thumbnailFile.exists()) {
                BitmapFactory.decodeFile(thumbnailFile.absolutePath)
            } else {
                null
            }
            bitmap ?: ThumbnailUtils.generatePageImage(
                document,
                page,
                page.initialPageWidthInPixel,
                page.initialPageHeightInPixel
            )
        }
    }

    private suspend fun fetchPageBackgroundThumbnail(document: Document, page: Page): Bitmap {
        val cacheKey = getPageBackgroundThumbnailCacheKey(document, page)
        val cacheThumbnail = BitmapLruCachePool.get(cacheKey)
        if (cacheThumbnail != null) {
            return cacheThumbnail
        }
        return suspendCancellableCoroutine { cont ->
            ThumbnailUtils.generateDrawsImage(
                document,
                page,
                emptyList(),
                frameTransform = FrameTransform(
                    page.initialPageWidthInPixel,
                    page.initialPageHeightInPixel,
                    page.initialPageWidthInPixel,
                    page.initialPageHeightInPixel,
                    page.actualPaperWidthInPixel,
                    page.actualPaperHeightInPixel,
                    page.actualPaperOffsetInPixel
                )
            ) { bitmap ->
                cont.resume(bitmap)
            }
        }
    }

    private fun getPageThumbnailCacheKey(document: Document, page: Page): String {
        return "doodle_thumbnail_${document.uuid}_${page.uuid}"
    }

    private fun getPageBackgroundThumbnailCacheKey(document: Document, page: Page): String {
        return "doodle_background_thumbnail_${document.uuid}_${page.uuid}"
    }

    companion object {
        private val allowDragMimeTypeList =
            listOf(
                MimeType.IMAGE.mime,
                MimeType.TEXT.mime,
                MimeType.HAND_WRITE_SNIPPET.mime,
                MimeType.KILO_STOKE.mime
            )

        /**
         * 竖直方向上最大移动距离
         */
        private const val MAXIMUM_VERTICAL_DISTANCE = 150

        private const val TAG = "DoodleTouchLayer2"

        /**
         *  默认滑动方向
         */
        private const val SCROLL_DIRECTION_UNKNOWN = 0

        /**
         * 水平滑动方向
         */
        private const val SCROLL_DIRECTION_HORIZONTAL = 1

        /**
         * 垂直滑动方向
         */
        private const val SCROLL_DIRECTION_VERTICAL = 2

        /**
         * 连续水平滑动次数后判定水平滑阈值
         */
        private const val SCROLL_HORIZONTAL_DIRECTION_SLOP = 1000 / 100

        /**
         * 滑动添加页面时的阻尼因数
         */
        private const val SCROLL_DAMPING_FACTOR = 3.3F

        private const val pageTurningVelocityXThreshold = 2000
        private const val pageTurningOffset = 300
    }
}

/**
 * 判断 Event 中所有的事件是否都是 Finger
 */
fun MotionEvent.isFinger(): Boolean {
    for (i in 0 until pointerCount) {
        if (getToolType(i) != MotionEvent.TOOL_TYPE_FINGER) {
            return false
        }
    }

    return true
}
package com.topstack.kilonotes.base.doc

import android.annotation.SuppressLint
import android.graphics.Color
import androidx.annotation.Keep
import androidx.annotation.WorkerThread
import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Ignore
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.topstack.kilonotes.BuildConfig
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.base.crash.CrashHelper
import com.topstack.kilonotes.base.doc.io.PaperManager
import com.topstack.kilonotes.base.doc.io.ResourceManager
import com.topstack.kilonotes.base.doc.io.ThumbnailManager
import com.topstack.kilonotes.base.doc.record.RecordManager
import com.topstack.kilonotes.base.doodle.model.Offset
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.doodle.model.text.InsertableText
import com.topstack.kilonotes.base.doodle.utils.ThumbnailUtils
import com.topstack.kilonotes.base.search.SearchManager
import com.topstack.kilonotes.base.util.PDFManager
import com.topstack.kilonotes.infra.size.PtSize
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.notedata.NoteRepository
import com.topstack.kilonotes.pad.note.outline.OutlineEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.LinkedList
import java.util.UUID
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.CountDownLatch

@Entity(tableName = "document", primaryKeys = ["uuid"])
class Document : MetaDocument {

    @ColumnInfo(name = "versionCode")
    @Expose
    @SerializedName("versionCode")
    var versionCode = DOCUMENT_VERSION_CODE

    @ColumnInfo(name = "versionName")
    @Expose
    @SerializedName("versionName")
    var versionName = DOCUMENT_VERSION_NAME

    @Ignore
    @Expose
    @SerializedName("platform")
    val platform = "android"

    @ColumnInfo(name = "imported")
    @Expose
    @SerializedName("imported")
    var imported: Boolean

    @Ignore
    @Expose
    @SerializedName("cover")
    @Deprecated("not used after 1.23.0")
    var cover: Cover? = null
        set(value) {
            if (field != value) {
                field = value
            }
        }

    @ColumnInfo(name = "coverCategoryId")
    @Expose
    @SerializedName("coverCategoryId")
    var coverCategoryId: Long? = null

    @ColumnInfo(name = "coverId")
    @Expose
    @SerializedName("coverId")
    var coverId: Long? = null

    @ColumnInfo(name = "coverImageUrl")
    @Expose
    @SerializedName("coverImageUrl")
    var coverImageUrl: String? = ""

    @ColumnInfo(name = "viewingPageIndex")
    @Expose
    @SerializedName("viewingPageIndex")
    var viewingPageIndex: Int = 0

    @Embedded(prefix = "pageOffsetInPercent_")
    @Expose
    @SerializedName("pageViewPortOffsetToPaperInPercent")
    var pageViewPortOffsetToPaperInPercent: Offset? = null

    @ColumnInfo(name = "pageViewPortBackgroundColor")
    @Expose
    @SerializedName("pageViewPortBackgroundColor")
    var pageViewPortBackgroundColor: Int = Color.WHITE

    @ColumnInfo(name = "pageBackgroundDrawableTemplate")
    @Expose
    @SerializedName("pageBackgroundDrawableTemplate")
    var pageBackgroundDrawableTemplate: String? = null

    @ColumnInfo(name = "isValid")
    @Expose
    @SerializedName("isValid")
    var isValid: Boolean = true

    @ColumnInfo(name = "useType")
    var useType: UseType = UseType.GENERAL

    @ColumnInfo(name = "isDeleted")
    @Volatile
    var isDeleted: Boolean = false

    @ColumnInfo(name = "deletedTime")
    var deletedTime: Long = 0L

    override fun updateAndStoreModifiedTime() {
        super.updateAndStoreModifiedTime()
    }

    suspend fun setPageViewPortToPaper(
        leftOffsetInPercent: Float,
        topOffsetInPercent: Float,
        rightOffsetInPercent: Float,
        bottomOffsetInPercent: Float,
        backgroundColor: Int = pageViewPortBackgroundColor,
        backgroundDrawableTemplate: String? = null,
        applyToCover: Boolean = false,
    ) {
        if (!Offset.isValid(
                leftOffsetInPercent,
                topOffsetInPercent,
                rightOffsetInPercent,
                bottomOffsetInPercent
            )
        ) return
        withContext(Dispatchers.IO) {
            if (pageViewPortOffsetToPaperInPercent == null) {
                pageViewPortOffsetToPaperInPercent = Offset(
                    leftOffsetInPercent,
                    topOffsetInPercent,
                    rightOffsetInPercent,
                    bottomOffsetInPercent
                )
            } else {
                pageViewPortOffsetToPaperInPercent!!.set(
                    leftOffsetInPercent,
                    topOffsetInPercent,
                    rightOffsetInPercent,
                    bottomOffsetInPercent
                )
            }
            pageViewPortBackgroundColor = backgroundColor
            pageBackgroundDrawableTemplate = backgroundDrawableTemplate
            val currentPage = pages[viewingPageIndex]
            val cover = pages.first()
            pages.forEach { page ->
                if (!applyToCover && page === cover) return@forEach
                val updatePageThumbnail = page.pageViewPortOffsetToPaperInPoint == null
                page.pageViewPortBackgroundColor = backgroundColor
                page.setPageViewPortToPaperInPercent(
                    leftOffsetInPercent,
                    topOffsetInPercent,
                    rightOffsetInPercent,
                    bottomOffsetInPercent
                )
                page.backgroundDrawableTemplate = backgroundDrawableTemplate
                if (currentPage === page || cover === page) {
                    if (page.cachedDoodleWidth <= 0 || page.cachedDoodleHeight <= 0) {
                        page.updatePageSizeByDoodleSize(
                            currentPage.cachedDoodleWidth,
                            currentPage.cachedDoodleHeight
                        )
                    }
                    val pageImage = ThumbnailUtils.generatePageImage(
                        this@Document,
                        page,
                        page.initialPageWidthInPixel,
                        page.initialPageHeightInPixel
                    )
                    ThumbnailManager.updateThumbnail(this@Document, page, pageImage)
                } else {
                    if (updatePageThumbnail) {
                        ThumbnailManager.updateThumbnailByNewViewPort(this@Document, page)
                    }
                }
                if (page === cover) {
                    clearCoverInfo()
                }
                NoteRepository.savePageInfo(page)
            }
            withContext(Dispatchers.Main) {
                DocumentManager.waitStoreFinished(this@Document)
            }
            updateAndStoreModifiedTime()
            NoteRepository.saveDocumentInfo(this@Document)
        }
    }

    @ColumnInfo(name = "outlineList")
    @Expose
    @SerializedName("outlineList")
    var outlineList: MutableList<OutlineEntity> = LinkedList<OutlineEntity>()

    fun addOutline(outlineEntity: OutlineEntity, afterSortedCallback: (() -> Unit)? = null) {
        synchronized(outlineList) {
            outlineList.add(outlineEntity)
        }
        sortOutlineList(afterSortedCallback)
        updateAndStoreModifiedTime()
    }

    private fun sortOutlineList(afterSortedCallback: (() -> Unit)? = null) {
        outlineList.sortWith { entity1, entity2 ->
            if (entity1.linkedPageUUID != entity2.linkedPageUUID) {
                pageIds.indexOf(entity1.linkedPageUUID)
                    .compareTo(pageIds.indexOf(entity2.linkedPageUUID))
            } else {
                entity1.createTime.compareTo(entity2.createTime)
            }
        }
        afterSortedCallback?.invoke()
    }

    fun updateOutline(afterSortedCallback: (() -> Unit)? = null) {
        sortOutlineList(afterSortedCallback)
        updateAndStoreModifiedTime()
    }

    fun deleteOutline(outlineEntity: OutlineEntity, afterDeleteCallback: (() -> Unit)? = null) {
        outlineList.remove(outlineEntity)
        afterDeleteCallback?.invoke()
        updateAndStoreModifiedTime()
    }

    fun deleteOutlineInBatches(
        outlineEntities: List<OutlineEntity>,
        afterDeleteCallback: (() -> Unit)? = null,
    ) {
        if (outlineEntities.isEmpty()) return
        for (outlineEntity in outlineEntities) {
            outlineList.remove(outlineEntity)
        }
        afterDeleteCallback?.invoke()
        updateAndStoreModifiedTime()
    }

    fun deleteOutlineByUUID(uuid: UUID, afterDeleteCallback: (() -> Unit)? = null) {
        val iterator = outlineList.iterator()
        while (iterator.hasNext()) {
            if (uuid == iterator.next().linkedPageUUID) {
                iterator.remove()
            }
        }
        afterDeleteCallback?.invoke()
        updateAndStoreModifiedTime()
    }

    fun updateOutlinePageUUID(sourcePageUUID: UUID, targetPageUUID: UUID) {
        outlineList.forEach {
            if (it.linkedPageUUID == sourcePageUUID) {
                it.linkedPageUUID = targetPageUUID
            }
        }
        updateAndStoreModifiedTime()
    }

    override var modifiedTime: Long
        get() = super.modifiedTime
        set(value) {
            super.modifiedTime = value
        }

    fun specifyModifiedTime(specifiedModifiedTime: Long) {
        if (specifiedModifiedTime > 0L) {
            modifiedTime = specifiedModifiedTime
        }
    }

    fun updateOpenedTime() {
        openedTime = obtainCurrentTime()
    }

    /**
     * 页面的 Id 集合
     */
    // 为了后续的兼容性，页面内容单独存放
    @Expose
    @ColumnInfo("pages")
    @SerializedName("pages")
    var pageIds = CopyOnWriteArrayList<UUID>()

    @Ignore
    val pages = object : CopyOnWriteArrayList<Page>() {
        private val pagesTag = "clear"
        override fun clear() {
            super.clear()
            CrashHelper.getCurrentActivity()?.let { activity ->
                val currentFragment =
                    CrashHelper.getCurrentFragment(activity)?.javaClass?.simpleName
                if ("PhoneNoteCatalogFragment" == currentFragment) {
                    LogHelper.d(
                        pagesTag,
                        "pages clear on PhoneNoteCatalogFragment",
                        Throwable(),
                        true
                    )
                }
            }
        }
    }

    @Ignore
    var resources: ResourceManager = ResourceManager.from(uuid = uuid.toString())
        get() {
            if (field == null) { // Gson 反序列化后有可能为null
                field = ResourceManager.from(uuid = uuid.toString())
            }
            return field
        }
        set(value) {
            field?.deleteIfEmpty() // Gson 反序列化后有可能为null
            field = value
        }

    val recordManager: RecordManager by lazy {
        RecordManager(this)
    }

    constructor() : super(DocumentType.DOCUMENT) {
        this.imported = false
    }

    @Ignore
    constructor(title: String) : super(DocumentType.DOCUMENT, title) {
        this.imported = false
    }

    @Ignore
    constructor(title: String, imported: Boolean) : super(DocumentType.DOCUMENT, title) {
        this.imported = imported
    }

    @Ignore
    constructor(document: Document, imported: Boolean) : super(DocumentType.DOCUMENT) {
        this.title = document.title
        this.cover = document.cover
        this.imported = imported
        this.outlineList = document.outlineList
        this.colorTags = document.colorTags
        this.pageViewPortOffsetToPaperInPercent = document.pageViewPortOffsetToPaperInPercent
        this.pageViewPortBackgroundColor = document.pageViewPortBackgroundColor
    }

    @Ignore
    constructor(rootDir: File) : super(DocumentType.DOCUMENT) {
        resources = ResourceManager.from(uuid = uuid.toString(), root = rootDir)
        this.imported = false
    }

    fun clearCoverInfo() {
        cover = null
        coverCategoryId = null
        coverId = null
        coverImageUrl = null
    }

    fun isEmpty(): Boolean = pageIds.isEmpty()

    @WorkerThread
    fun newPage(
        next: Int,
        paper: Paper = PaperManager.defaultPaper,
        pageVersion: Int = Page.PAGE_VERSION_LATEST,
        pdfLinks: List<Link>? = null,
        pageViewPortOffsetToPaperInPoint: Offset? = null,
        pageViewPortBackgroundColor: Int = Color.WHITE,
        pageBackgroundDrawableTemplate: String? = null,
    ): Page = Page().also {
        it.documentId = uuid
        it.paper = if (paper.file.isNotBlank() && !ResourceManager.isKiloPath(paper.file)) {
            // 缓存的时候会修改file路径，克隆一个防止修改了预定义内置纸张数据
            val clonedPaper = paper.clone()
            PaperManager.cacheExternalPaperIfNeeded(this, clonedPaper)
            clonedPaper
        } else {
            paper
        }
        it.markDrawsLoaded()
        it.version = pageVersion
        it.pdfLinks = pdfLinks
        it.pageCreatedTime = System.currentTimeMillis()

        setPageViewPort(it, pageViewPortOffsetToPaperInPoint, pageViewPortBackgroundColor)
        it.backgroundDrawableTemplate =
            pageBackgroundDrawableTemplate ?: this.pageBackgroundDrawableTemplate

        pageIds.add(next, it.uuid)
        pages.add(next, it)
        NoteRepository.runInNoteOperationScopeAsync {
            updateDocumentInfo(this@Document)
            commitNewPage(it)
        }

        SearchManager.updatePagePdfContentSearchIndexAsyncIfNeeded(this, it, true)
        SearchManager.updatePageContentSearchIndexAsyncIfNeeded(this, it, true)
    }

    private fun setPageViewPort(
        page: Page,
        pageViewPortOffsetToPaperInPoint: Offset? = null,
        pageViewPortBackgroundColor: Int = Color.WHITE,
    ) {
        if (pageViewPortOffsetToPaperInPoint == null) {
            val offsetToPaper = pageViewPortOffsetToPaperInPercent
            if (offsetToPaper != null) {
                page.pageViewPortBackgroundColor = this.pageViewPortBackgroundColor
                page.setPageViewPortToPaperInPercent(
                    offsetToPaper.leftOffset,
                    offsetToPaper.topOffset,
                    offsetToPaper.rightOffset,
                    offsetToPaper.bottomOffset,
                )
            }
        } else {
            page.pageViewPortOffsetToPaperInPoint = pageViewPortOffsetToPaperInPoint
            page.pageViewPortBackgroundColor = pageViewPortBackgroundColor
        }
    }

    @WorkerThread
    fun newPaperFromPdf(pdfPath: String): Paper? {
        val originalPaperPdfFile = File(pdfPath)
        if (!originalPaperPdfFile.exists()) {
            return null
        }

        // 保存 pdf 至笔记目录
        val paperUri = originalPaperPdfFile.inputStream().use { coverInputStream ->
            resources.storeAttachment(coverInputStream)
        }

        val paperPdfFile = resources.openFile(paperUri.toString())
        val pageSizePoint = PDFManager.getPDFPagePointSize(paperPdfFile, 0)

        return Paper.custom(paperUri.toString(), 0, pageSizePoint.width, pageSizePoint.height)
    }

    @WorkerThread
    fun newPageFromPdf(pdfPath: String): Boolean {
        val paper = newPaperFromPdf(pdfPath) ?: return false

        newPage(
            pageIds.size,
            paper
        )

        return true
    }

    @WorkerThread
    fun newCoverFromPdf(pdfPath: String): Boolean {
        val originalCoverPdfFile = File(pdfPath)
        if (!originalCoverPdfFile.exists()) {
            return false
        }

        // 保存封面至笔记目录
        val coverUri = originalCoverPdfFile.inputStream().use { coverInputStream ->
            resources.storeAttachment(coverInputStream)
        }

        // 生成封面页
        val coverPdfFile = resources.openFile(coverUri.toString())
        val pageSizePoint = PDFManager.getPDFPagePointSize(coverPdfFile, 0)
        val oldCover = pages.firstOrNull()
        val page = newPage(
            0, Paper.custom(coverUri.toString(), 0, pageSizePoint.width, pageSizePoint.height)
        )
        if (oldCover != null) {
            page.setPageViewPortToPaperInPercent(oldCover.pageViewPortOffsetToPaperInPercent)
        }
        val countDownLatch = CountDownLatch(1)
        // 生成封面页缩略图
        try {
            val deferred = ThumbnailManager.savePageThumbnail(this@Document, page)
            deferred.invokeOnCompletion {
                countDownLatch.countDown()
            }
            countDownLatch.await()
        } catch (e: Exception) {
            e.printStackTrace()
            countDownLatch.countDown()
        }
        updateAndStoreModifiedTime()
        return true
    }

    @SuppressLint("DiscouragedApi")
    fun getCoverAttachment(): Any? {
        return if (versionCode < DOCUMENT_VERSION_CODE_COVER_CHANGE) {
            val documentCover = cover
            if (documentCover == null) {
                null
            } else {
                if (documentCover.isCustomCover) {
                    val attachment = documentCover.attachment
                    return if (attachment == null) {
                        null
                    } else {
                        if (ResourceManager.isKiloPath(attachment)) {
                            resources.openFile(attachment).absolutePath
                        } else {
                            attachment
                        }
                    }
                } else {
                    val name = documentCover.type
                    return if (name == null) {
                        null
                    } else {
                        val identifier = KiloApp.app.resources.getIdentifier(
                            name, "drawable", BuildConfig.APPLICATION_ID
                        )
                        if (identifier != 0) {
                            identifier
                        } else {
                            null
                        }
                    }
                }
            }
        } else {
            // 版本大于10时封面使用第一页缩略图
            getCoverFilePathByFirstPage().ifEmpty {
                null
            }
        }
    }

    fun getCoverFilePathByFirstPage(): String {
        val firstPageId = pageIds.firstOrNull() ?: return ""

        var expectCoverUri =
            "${ResourceManager.URI_PREFIX}/${ResourceManager.ATTACHMENTS}/${ResourceManager.THUMBNAIL}/$firstPageId"
        var coverFile = resources.openFile(expectCoverUri)
        if (coverFile.exists()) {
            return coverFile.absolutePath
        }

        expectCoverUri =
            "${ResourceManager.URI_PREFIX}/${ResourceManager.ATTACHMENTS}/${ResourceManager.THUMBNAIL}/${firstPageId}.jpg"

        coverFile = resources.openFile(expectCoverUri)
        if (coverFile.exists()) {
            return coverFile.absolutePath
        }
        return ""
    }

    fun replace(
        currentPage: Int,
        paper: Paper = PaperManager.defaultPaper,
        pageVersion: Int,
        pageViewPortOffsetToPaperInPoint: Offset? = null,
        pageViewPortBackgroundColor: Int = Color.WHITE,
        pageBackgroundDrawableTemplate: String? = null,
    ): Page {
        val replacedPage = pages[currentPage]
        val preReplacedInitialScale = replacedPage.initialScale
        val offsetInPercent = replacedPage.pageViewPortOffsetToPaperInPercent
        replacedPage.version = pageVersion
        replacedPage.paper =
            if (paper.file.isNotBlank() && !ResourceManager.isKiloPath(paper.file)) {
                val clonedPaper = paper.clone()
                PaperManager.cacheExternalPaperIfNeeded(this, clonedPaper)
                clonedPaper
            } else {
                paper
            }
        if (pageViewPortOffsetToPaperInPoint == null) {
            if (replacedPage.pageViewPortOffsetToPaperInPoint != null) {
                replacedPage.pageViewPortOffsetToPaperInPoint = Offset(
                    offsetInPercent.leftOffset * replacedPage.paper.widthInPoint,
                    offsetInPercent.topOffset * replacedPage.paper.heightInPoint,
                    offsetInPercent.rightOffset * replacedPage.paper.widthInPoint,
                    offsetInPercent.bottomOffset * replacedPage.paper.heightInPoint,
                )
            }
        } else {
            replacedPage.pageViewPortBackgroundColor = pageViewPortBackgroundColor
        }
        if (pageBackgroundDrawableTemplate != null) {
            replacedPage.backgroundDrawableTemplate = pageBackgroundDrawableTemplate
        }
        replacedPage.clearBackground()
        val postReplacedInitialScale = replacedPage.initialScale

        val scale = preReplacedInitialScale / postReplacedInitialScale
        replacedPage.draws.forEach {
            if (it is InsertableText) {
                it.borderWidth = PtSize(it.borderWidth.value * scale)
                it.borderMaxHeight = PtSize(it.borderMaxHeight.value * scale)
                it.textStyle.textSize = PtSize(it.textStyle.textSize.value * scale)
            } else {
                it.matrix.postScale(scale, scale)
            }
        }
        return replacedPage
    }

    val pageCount: Int
        get() = pages.size

    operator fun get(index: Int): Page {
        return when {
            index < 0 -> {
                pages[0]
            }

            index > pages.lastIndex -> {
                pages.last()
            }

            else -> {
                pages[index]
            }
        }
    }

    fun markDeleted() {
        isDeleted = true
        deletedTime = System.currentTimeMillis()
    }

    fun markNotDeleted() {
        isDeleted = false
        deletedTime = 0L
    }

    /**
     * 是否是可以被支持的文档格式
     */
    val isSupported: Boolean
        get() = versionCode in 1..DOCUMENT_VERSION_CODE

    fun getCurrentPage(): Page {
        return pages[viewingPageIndex.coerceIn(0, pages.size - 1)]
    }

    fun getPageByUUID(uuid: UUID): Page? {
        return pages.find {
            it.uuid == uuid
        }
    }

    fun getPageIndexByUUID(uuid: UUID): Int {
        return pageIds.indexOf(uuid)
    }

    fun isStoredInObsoleteKiloNotesRoom(): Boolean = versionCode < DOCUMENT_VERSION_MIGRATE_TO_DATABASE

    fun isStoredInDatabase(): Boolean = versionCode >= DOCUMENT_VERSION_MIGRATE_TO_DATABASE

    /**
     * 文档版本。升级时同步更新
     * https://rqqmslsz9y.feishu.cn/sheets/shtcnGHDOoLUERcdOGLfKIm0IJf
     *
     * version 4 px -> pt
     * version 5 添加了InsertableObjectPattern
     * version 7 添加了InsertableObjectTexture
     * version 8 添加了InsertableObjectEffects
     * version 9 InsertableObjectTexture添加angle属性
     * version 10 新增封面页；图片锁定
     * version 11 添加文件夹信息
     * version 12 添加隐藏属性 isHid
     * version 13 添加摘取卡片类型 InsertableObjectSnippet
     * version 14 添加录音类型 InsertableRecord
     * version 15 大纲
     * version 16 纸张留白
     * version 17 文本支持旋转缩放
     * version 18 纸张留白支持模板
     * version 19 添加颜色标签，打开时间等排序所需字段
     * version 20 添加笔记本是否异常判断标签
     * version 21 图形工具
     * version 22 记忆胶带
     * version 23 多级文件夹，钢笔
     * version 24 画笔
     * version 25 图形识别添加曲线识别以及闭合属性
     * version 26 数据库存储
     * version 27 增加 markdown 元素
     */
    companion object {
        const val DOCUMENT_VERSION_CODE_PX_TO_PT = 4
        const val DOCUMENT_VERSION_CODE = 27
        const val DOCUMENT_VERSION_NAME = "1.0.27"
        const val DOCUMENT_VERSION_CODE_COVER_CHANGE = 10
        const val DOCUMENT_VERSION_MIGRATE_TO_DATABASE = 26 // 升级至数据库

        @JvmStatic
        val EMPTY = Document()
    }

    @Keep
    enum class UseType {
        GENERAL, // 普通笔记
        DRAFT_PAPER, // 草稿纸
        SNIPPET, // 卡片
        TEMPLATE, // 模板
    }
}
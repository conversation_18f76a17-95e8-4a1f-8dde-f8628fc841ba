package com.topstack.kilonotes.base.combine.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.combine.adapter.DocumentCombineItemDecoration
import com.topstack.kilonotes.base.combine.adapter.DocumentCombineSelectAdapter
import com.topstack.kilonotes.base.combine.adapter.DocumentCombineSortAdapter
import com.topstack.kilonotes.base.combine.viewmodel.DocumentCombineViewModel
import com.topstack.kilonotes.base.component.dialog.CommonScreenAdaptiveDialog
import com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
import com.topstack.kilonotes.base.note.viewmodel.NoteViewModel

class DocumentCombineSelectionDialog : CommonScreenAdaptiveDialog() {

    companion object {
        const val TAG = "DocumentCombineSelectionDialog"
    }

    private val combineViewModel: DocumentCombineViewModel by viewModels(
        ownerProducer = { requireParentFragment() }
    )
    private val noteViewModel: NoteViewModel by activityViewModels()

    private lateinit var close: ImageView
    private lateinit var title: TextView
    private lateinit var subtitle: TextView
    private lateinit var selectList: OverScrollCoordinatorRecyclerView
    private lateinit var sortList: OverScrollCoordinatorRecyclerView
    private lateinit var emptyViewGroup: Group
    private lateinit var confirm: TextView
    private lateinit var root: View

    private var selectAdapter: DocumentCombineSelectAdapter? = null
    private var sortAdapter: DocumentCombineSortAdapter? = null

    override fun isDialogCancelable() = true

    override fun getLayoutRes(): Int {
        return when (layout) {
            LayoutType.LAYOUT_NORMAL -> R.layout.combine_documents_selection_dialog
            LayoutType.LAYOUT_PHONE -> throw NotImplementedError()
            else -> R.layout.combine_documents_selection_dialog
        }
    }

    override fun bindView(view: View) {
        view.apply {
            root = findViewById(R.id.root)
            close = findViewById(R.id.close)
            title = findViewById(R.id.title)
            subtitle = findViewById(R.id.subtitle)
            emptyViewGroup = findViewById(R.id.empty)
            selectList = findViewById(R.id.select_list)
            sortList = findViewById(R.id.sort_list)
            confirm = findViewById(R.id.confirm)
        }
    }

    override fun initView() {
        root.apply {
            clipToOutline = true
            if (layout == LayoutType.LAYOUT_ONE_THIRD_LANDSCAPE_SCREEN) {
                background = ColorDrawable(Color.WHITE)
            }
        }
        close.setOnClickListener {
            combineViewModel.resetState()
            dismiss()
        }
        confirm.setOnClickListener {
            showDocumentCombineOptionsDialog()
            dismiss()
        }
        emptyViewGroup.visibility = if (combineViewModel.allDocumentList.isEmpty()) {
            View.VISIBLE
        } else {
            View.INVISIBLE
        }
        subtitle.visibility = if (combineViewModel.allDocumentList.isEmpty()) {
            View.INVISIBLE
        } else {
            View.VISIBLE
        }
        combineViewModel.documentSelectMap.observe(viewLifecycleOwner) { documentSelectMap ->
            selectAdapter?.updateSelect(documentSelectMap)
                ?: selectList.overScrollRecyclerView.apply {
                    layoutManager =
                        LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
                    addItemDecoration(
                        DocumentCombineItemDecoration(
                            when (layout) {
                                LayoutType.LAYOUT_PHONE -> throw NotImplementedError()
                                else -> resources.getDimension(R.dimen.dp_20).toInt()
                            },
                            when (layout) {
                                LayoutType.LAYOUT_PHONE -> throw NotImplementedError()
                                else -> resources.getDimension(R.dimen.dp_36).toInt()
                            },
                            true
                        )
                    )
                    adapter = DocumentCombineSelectAdapter(
                        combineViewModel.allDocumentList,
                        documentSelectMap,
                        when (layout) {
                            LayoutType.LAYOUT_PHONE -> throw NotImplementedError()
                            else -> R.layout.combine_documents_select_list_item
                        }
                    ) { position ->
                        combineViewModel.changeSelection(position)
                    }.also {
                        it.stateRestorationPolicy =
                            RecyclerView.Adapter.StateRestorationPolicy.PREVENT_WHEN_EMPTY
                        selectAdapter = it
                    }
                }
        }
        combineViewModel.combineDocumentList.observe(viewLifecycleOwner) { documentList ->
            sortAdapter?.update(documentList) ?: sortList.overScrollRecyclerView.apply {
                adapter = DocumentCombineSortAdapter(
                    documentList,
                    when (layout) {
                        LayoutType.LAYOUT_PHONE -> throw NotImplementedError()
                        else -> R.layout.combine_documents_sort_list_item
                    }
                ).also {
                    sortAdapter = it
                }
                layoutManager =
                    LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)
                addItemDecoration(
                    DocumentCombineItemDecoration(
                        when (layout) {
                            LayoutType.LAYOUT_PHONE -> throw NotImplementedError()
                            else -> resources.getDimension(R.dimen.dp_36).toInt()
                        },
                        when (layout) {
                            LayoutType.LAYOUT_PHONE -> throw NotImplementedError()
                            else -> resources.getDimension(R.dimen.dp_24).toInt()
                        },
                        false
                    )
                )
                ItemTouchHelper(object : ItemTouchHelper.SimpleCallback(
                    ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT,
                    0
                ) {
                    override fun onMove(
                        recyclerView: RecyclerView,
                        viewHolder: RecyclerView.ViewHolder,
                        target: RecyclerView.ViewHolder
                    ): Boolean {
                        val from = viewHolder.bindingAdapterPosition
                        val to = target.bindingAdapterPosition
                        combineViewModel.moveCombineDocuments(from, to)
                        return true
                    }

                    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
                    }

                }).attachToRecyclerView(this)
            }
            sortList.post {
                if (isAdded) {
                    sortList.visibility = if (documentList.isEmpty()) {
                        View.GONE
                    } else {
                        View.VISIBLE
                    }
                }
            }
        }
        combineViewModel.combineDocumentList.observe(viewLifecycleOwner) {
            confirm.isEnabled = it.size > 1
        }
    }

    private fun showDocumentCombineOptionsDialog() {
        val dialog = parentFragmentManager.findFragmentByTag(DocumentCombineOptionsDialog.TAG)
        if (dialog is DocumentCombineOptionsDialog) {
            return
        }
        DocumentCombineOptionsDialog().show(
            parentFragmentManager,
            DocumentCombineOptionsDialog.TAG
        )
    }
}
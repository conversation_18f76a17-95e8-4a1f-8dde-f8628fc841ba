package com.topstack.kilonotes.base.vip

import android.os.Bundle
import android.view.View
import androidx.annotation.LayoutRes
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.OrderType
import com.topstack.kilonotes.account.UserInfo
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.base.account.WechatQrcodePayDialog
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.handbook.model.HandbookCover
import com.topstack.kilonotes.base.track.event.StoreAndLoginEvent
import com.topstack.kilonotes.base.util.VipTypeCheckUtils
import com.topstack.kilonotes.base.util.WechatUtils
import com.topstack.kilonotes.base.vip.viewmodel.HandbookViewModel
import com.topstack.kilonotes.base.vip.viewmodel.PayTypeViewModel
import com.topstack.kilonotes.base.vip.viewmodel.PayViewModel
import com.topstack.kilonotes.infra.device.DeviceUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.pad.MainActivity
import com.topstack.kilonotes.pad.vip.dialog.PayDetainmentDialogFragment
import com.topstack.kilonotes.pay.PayItem
import com.topstack.kilonotes.pay.order.PayHelper
import com.topstack.kilonotes.pay.order.PayServiceState
import com.topstack.kilonotes.pay.order.PayType
import com.topstack.kilonotes.sns.wechat.WXHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 *
 */
abstract class BasePayHandleFragment : BaseFragment {
    companion object {
        const val TAG = "BasePayHandleFragment"
        const val CREATE_HANDBOOK_SUCCESS_DIALOG_TAG = "create_handbook_success_dialog_tag"
    }

    constructor() : super()
    constructor(@LayoutRes contentLayoutId: Int) : super(contentLayoutId)

    protected val pre20AnnuallyVipGoogleProductId = "kilonotes_annually_20220406"
    protected val pre20QuarterlyVipGoogleProductId = "kilonotes_quarterly_20220407"
    protected val preAiVipGoogleProductId = "ai_assistant"

    /**
     * 2121 = release_v2.12.1
     */
    protected val pre2121QuarterlyVipGoogleProductId = "kilonotes_quarterly_vip_country"

    protected val payViewModel: PayViewModel by activityViewModels()
    protected val handbookViewModel: HandbookViewModel by activityViewModels()

    protected val payTypeViewModel: PayTypeViewModel by viewModels() // 不要改成 activityViewModels
    protected val subscribedProducts = mutableListOf<String>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recoverPayDetainmentDialog()
        recoverStoreBuyDialog()
        UserManager.addOnUserInfoChangedListener(onUserInfoChangedListener)
    }

    override fun onStart() {
        super.onStart()
        if (checkNetwork()) {
            payViewModel.querySubscriptions()
            payViewModel.updatePurchasedProducts()
            lifecycleScope.launch {
                UserManager.updateVipStatus()
            }
        }
    }

    protected open fun initObserver(savedInstanceState: Bundle?) {
        payViewModel.payCanceled.observe(viewLifecycleOwner) { canceledNativePayInfo ->
            canceledNativePayInfo?.let {
                val orderType = canceledNativePayInfo.orderType
                val payItem = canceledNativePayInfo.payItem
                val payType = canceledNativePayInfo.payType
                if (payViewModel.needShowDetainmentPayResult(orderType)) {
                    if (PayHelper.isSubscriptionSupported()) {
                        payViewModel.setDetainmentPayResult(payType, payItem, orderType)
                        showPayDetainmentDialog()
                    } else {
                        payViewModel.setDetainmentPayResult(payType, payItem, orderType)
                        if (payViewModel.paymentFromDetainmentDialog || payType == PayType.WECHAT && !payTypeViewModel.pendingShowSelectPayTypeDialog || payType == PayType.ALIPAY) {
                            showPayDetainmentDialog()
                        }
                    }
                }
                if (payType == PayType.WECHAT && payTypeViewModel.pendingShowSelectPayTypeDialog) {
                    val handbookToBuy = payTypeViewModel.pendingHandbook
                    val vipToBuy = payTypeViewModel.pendingPayItem
                    if (orderType == OrderType.MERCHANDISE && handbookToBuy != null) {
                        payTypeViewModel.pendingHandbook = null
                        callBuy(handbookToBuy)
                    } else if (OrderType.isVipOrderType(orderType) && vipToBuy != null) {
                        payTypeViewModel.pendingPayItem = null
                        callBuy(vipToBuy, payType)
                    }
                }
                payTypeViewModel.pendingShowSelectPayTypeDialog = false
                payViewModel.resetNativePayCancelStatus()
            }
        }

        payViewModel.subscriptions.observe(viewLifecycleOwner) { subscriptions ->
            if (PayHelper.isSubscriptionSupported()) {
                if (subscriptions == null) {

                } else {
                    subscribedProducts.clear()
                    subscribedProducts.addAll(subscriptions)
                }
                LogHelper.d(
                    TAG, "subscriptions: $subscriptions " +
                            "subscribedProducts: $subscribedProducts "
                )

                lifecycleScope.launch(Dispatchers.IO) {
                    withContext(Dispatchers.Main) {
                        if (isAdded) {
                            updateUI()
                            handbookViewModel.updateHandbookStatus()
                        }
                    }
                }
            }
        }

        payViewModel.loginCanceled.observe(viewLifecycleOwner) { isCancel ->
            if (isCancel) {
                sendPendingToBuyEvent()
                payViewModel.resetCancelLogin()
            }
        }
    }

    fun showPayDetainmentDialog() {
        payTypeViewModel.pendingShowSelectPayTypeDialog = false
        val activity = requireActivity()
        if (activity is BasePayHandleActivity) {
            activity.showPayDetainmentDialog(
                tag = PayDetainmentDialogFragment.TAG,
                doOnAbandon = {
                    payViewModel.paymentFromDetainmentDialog = false
                    payTypeViewModel.pendingShowSelectPayTypeDialog = false
                    payViewModel.resetDetainmentPayResult()
                },
                doOnAccess = {
                    payViewModel.paymentFromDetainmentDialog = true
                    payViewModel.getDetainmentPayResult()?.let {
                        callBuy(it.payItem, it.payType)
                    }
                    payViewModel.resetDetainmentPayResult()
                }
            )
        }
    }

    private fun recoverPayDetainmentDialog() {
        val activity = requireActivity()
        if (activity is BasePayHandleActivity) {
            activity.restartPayDetainmentDialog(
                tag = PayDetainmentDialogFragment.TAG,
                doOnAbandon = {
                    payViewModel.getDetainmentPayResult()?.let { payResult ->
                        payTypeViewModel.pendingShowSelectPayTypeDialog = true
                        if (payResult.payType == PayType.WECHAT
                            && payTypeViewModel.pendingShowSelectPayTypeDialog
                        ) {
                            val handbookToBuy = payTypeViewModel.pendingHandbook
                            val vipToBuy = payTypeViewModel.pendingPayItem
                            if (payResult.orderType == OrderType.MERCHANDISE && handbookToBuy != null) {
                                payTypeViewModel.pendingHandbook = null
                                callBuy(handbookToBuy)
                            } else if (OrderType.isVipOrderType(payResult.orderType) && vipToBuy != null) {
                                payTypeViewModel.pendingPayItem = null
                                callBuy(vipToBuy, payResult.payType)
                            }
                        }
                    }
                    payViewModel.paymentFromDetainmentDialog = false
                    payTypeViewModel.pendingShowSelectPayTypeDialog = false
                    payViewModel.resetDetainmentPayResult()
                },
                doOnAccess = {
                    payViewModel.paymentFromDetainmentDialog = true
                    payViewModel.getDetainmentPayResult()?.let {
                        callBuy(it.payItem, it.payType)
                    }
                    payViewModel.resetDetainmentPayResult()
                }
            )
        }
    }

    open fun updateUI() {

    }

    protected fun showGoogleLoginToast(resultCode: String) {
        val activity = requireActivity()
        if (activity is BasePayHandleActivity) {
            activity.showGoogleLoginToast(resultCode)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        UserManager.removeOnUserInfoChangedListener(onUserInfoChangedListener)
    }

    protected fun callBuy(handbook: HandbookCover) {
        if (checkNetwork()) {
            if (PayHelper.isSubscriptionSupported()) {
                PayHelper.prepareForPay { success, resultCode ->
                    if (PayHelper.checkServiceState(PayType.GOOGLE) != PayServiceState.READY) {
                        // Google Play 服务连接失败
                        showGoogleLoginToast(resultCode)
                    } else {
                        payViewModel.pay(
                            requireActivity(),
                            this,
                            PayType.GOOGLE,
                            OrderType.MERCHANDISE,
                            PayItem().apply {
                                handbookTitle = handbook.title
                                productId = handbook.googleProductId
                                totalFee = handbook.price * 100
                            })
                        StoreAndLoginEvent.sendStoreNoteBookBuyClick(
                            getSource(),
                            handbook.title,
                            (handbook.price).toString(),
                            getLocation(),
                            ""
                        )
                    }
                }
            } else {
                val fragment = childFragmentManager.findFragmentByTag(StoreBuyDialog.TAG)
                if (fragment is StoreBuyDialog) return
                if (!payTypeViewModel.pendingShowSelectPayTypeDialog
                    && !(DeviceUtils.isPhoneType(KiloApp.deviceType) && !WXHelper.hasWxInstalled())
                ) {
                    payTypeViewModel.pendingShowSelectPayTypeDialog = true
                    payTypeViewModel.pendingHandbook = handbook
                    if (WXHelper.hasWxInstalled()) {
                        payViewModel.pay(
                            requireActivity(),
                            requireActivity(),
                            PayType.WECHAT,
                            OrderType.MERCHANDISE,
                            PayItem().apply {
                                productId = handbook.productId
                                totalFee = handbook.price * 100
                            }
                        )
                        StoreAndLoginEvent.sendStoreNoteBookBuyClick(
                            getSource(),
                            handbook.title,
                            (handbook.price).toString(),
                            getLocation(),
                            "wx"
                        )
                    } else {
                        if (WechatUtils.needNativePay(PayType.WECHAT, true)) {
                            StoreAndLoginEvent.sendWxCodeShow(handbook.title)
                            WechatQrcodePayDialog.show(
                                requireActivity().supportFragmentManager,
                                PayItem().apply {
                                    productId = handbook.productId
                                    totalFee = handbook.price * 100
                                },
                                OrderType.MERCHANDISE,
                                getSource(),
                                getLocation()
                            )
                            StoreAndLoginEvent.sendStoreNoteBookBuyClick(
                                getSource(),
                                handbook.title,
                                (handbook.price).toString(),
                                getLocation(),
                                "code"
                            )
                        }
                    }
                } else {
                    payTypeViewModel.pendingShowSelectPayTypeDialog = false
                    StoreBuyDialog().apply {
                        params = StoreBuyDialogParams.fromHandbook(
                            <EMAIL>(),
                            handbook,
                            PayType.ALIPAY,
                            getSource(),
                            getLocation()
                        )
                    }.show(childFragmentManager, StoreBuyDialog.TAG)
                }
            }
        }
    }

    protected fun callBuy(
        payItem: PayItem,
        payType: PayType = if (PayHelper.isSubscriptionSupported()) PayType.GOOGLE else PayType.WECHAT
    ) {
        if (checkNetwork()) {
            val vipType = VipTypeCheckUtils.getMemberType(payItem)
            if (PayHelper.isSubscriptionSupported()) {
                PayHelper.prepareForPay { success, resultCode ->
                    if (success) {
                        payViewModel.pay(
                            requireActivity(),
                            this,
                            PayType.GOOGLE,
                            payItem.orderType,
                            payItem
                        )
                        StoreAndLoginEvent.sendStoreMemberBuybtn(
                            getSource(),
                            vipType,
                            payItem.priceText,
                            getLocation(),
                            ""
                        )
                    }
                }
            } else {
                val fragment = childFragmentManager.findFragmentByTag(StoreBuyDialog.TAG)
                if (fragment is StoreBuyDialog) return
                if (!payTypeViewModel.pendingShowSelectPayTypeDialog
                    && !(DeviceUtils.isPhoneType(KiloApp.deviceType) && !WXHelper.hasWxInstalled())
                ) {
                    payTypeViewModel.pendingShowSelectPayTypeDialog = true
                    payTypeViewModel.pendingPayItem = payItem

                    when (payType) {
                        PayType.ALIPAY -> {
                            payViewModel.pay(
                                requireActivity(),
                                requireActivity(),
                                PayType.ALIPAY,
                                payItem.orderType,
                                payItem
                            )
                        }

                        PayType.WECHAT -> {
                            if (WXHelper.hasWxInstalled()) {
                                payViewModel.pay(
                                    requireActivity(),
                                    requireActivity(),
                                    PayType.WECHAT,
                                    payItem.orderType,
                                    payItem
                                )
                                StoreAndLoginEvent.sendStoreMemberBuybtn(
                                    getSource(),
                                    vipType,
                                    getPriceWithUnit(payItem, isInUnitsOfFen = true),
                                    getLocation(),
                                    "wx"
                                )
                            } else {
                                if (WechatUtils.needNativePay(PayType.WECHAT, true)) {
                                    StoreAndLoginEvent.sendWxCodeShow(vipType)
                                    WechatQrcodePayDialog.show(
                                        requireActivity().supportFragmentManager,
                                        payItem,
                                        payItem.orderType,
                                        getSource(),
                                        getLocation()
                                    )
                                    StoreAndLoginEvent.sendStoreMemberBuybtn(
                                        getSource(),
                                        vipType,
                                        getPriceWithUnit(payItem, isInUnitsOfFen = true),
                                        getLocation(),
                                        "code"
                                    )
                                }
                            }
                        }

                        else -> {}
                    }
                } else {
                    payTypeViewModel.pendingShowSelectPayTypeDialog = false
                    StoreBuyDialog().apply {
                        setOnCloseAction {
                            val payResult = payViewModel.getDetainmentPayResult()
                            if (payResult != null) {
                                if (DeviceUtils.isPadType(KiloApp.deviceType) && OrderType.isVipOrderType(
                                        payResult.orderType
                                    )
                                ) {
                                    showPayDetainmentDialog()
                                }
                            }
                        }
                        params = StoreBuyDialogParams.fromPayItem(
                            <EMAIL>(),
                            payItem,
                            PayType.ALIPAY,
                            getSource(),
                            getLocation()
                        )
                    }.show(childFragmentManager, StoreBuyDialog.TAG)
                }
            }
        }
    }

    private fun recoverStoreBuyDialog() {
        val dialog = childFragmentManager.findFragmentByTag(StoreBuyDialog.TAG)
        if (dialog is StoreBuyDialog) {
            dialog.setOnCloseAction {
                val payResult = payViewModel.getDetainmentPayResult()
                if (payResult != null) {
                    if (DeviceUtils.isPadType(KiloApp.deviceType) && OrderType.isVipOrderType(
                            payResult.orderType
                        )
                    ) {
                        showPayDetainmentDialog()
                    }
                }
            }
        }
    }

    protected fun showCreateNoteLimitDialog(source: String) {
        val activity = activity
        if (activity is MainActivity) {
            activity.showCreateNoteLimitDialog(source)
        }
    }

    // 打点用
    fun initInformationForTrackEvent() {
        payViewModel.source = getSource()
        payViewModel.location = getLocation()
    }

    protected abstract fun getSource(): String
    protected abstract fun getLocation(): String

    protected fun checkNetwork(): Boolean {
        val activity = requireActivity()
        return if (activity is BasePayHandleActivity) {
            activity.checkNetwork()
        } else {
            false
        }
    }

    protected fun callLogin() {
        val activity = requireActivity()
        if (activity is BasePayHandleActivity) {
            activity.callLogin()
        }
    }

    private fun sendPendingToBuyEvent() {
        if (payTypeViewModel.pendingToBuy) {
            payTypeViewModel.pendingPayItem?.let { payItem ->
                val vipType = VipTypeCheckUtils.getMemberType(payItem)
                StoreAndLoginEvent.sendStoreMemberBuyFail(
                    getSource(),
                    vipType,
                    getPriceWithUnit(payItem, isInUnitsOfFen = true),
                    getLocation(),
                    "code",
                    "cancel"
                )
            }
            payTypeViewModel.pendingHandbook?.let { handbookCover ->
                StoreAndLoginEvent.sendStoreNoteBookBuyFail(
                    getSource(),
                    handbookCover.title,
                    handbookCover.price.toString(),
                    getLocation(),
                    "code",
                    "cancel"
                )
            }
        }
        payTypeViewModel.resetPending()
    }

    protected fun callLoginIfNeed() {
        if (!UserManager.hasLoggedInUser() && checkNetwork()) {
            callLogin()
        }
    }

    private val onUserInfoChangedListener = { userInfo: UserInfo?, isSubscriber: Boolean ->
        onUserInfoUpdated(userInfo, isSubscriber)
    }

    protected open fun onUserInfoUpdated(userInfo: UserInfo?, isSubscriber: Boolean) {}

}
package com.topstack.kilonotes.base.doodle.views.multiselectview

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.RectF
import android.view.Gravity
import com.topstack.kilonotes.KiloApp.Companion.deviceType
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.doodle.model.InsertableObject
import com.topstack.kilonotes.base.doodle.model.InsertableObjectType
import com.topstack.kilonotes.base.doodle.views.doodleview.DoodleView
import com.topstack.kilonotes.base.doodle.views.guideline.GuideLineLayer
import com.topstack.kilonotes.base.doodle.views.selectview.ChangeType
import com.topstack.kilonotes.base.doodle.views.selectview.ClickableAnchorLayer
import com.topstack.kilonotes.base.doodle.views.selectview.GraphicLayer
import com.topstack.kilonotes.base.doodle.views.selectview.IDrawableSelf
import com.topstack.kilonotes.base.doodle.views.selectview.ILayer
import com.topstack.kilonotes.base.doodle.views.selectview.ITransformChanged
import com.topstack.kilonotes.base.doodle.views.selectview.LayerFactory
import com.topstack.kilonotes.base.doodle.views.selectview.LayerParent
import com.topstack.kilonotes.base.doodle.views.selectview.RotateLayer
import com.topstack.kilonotes.base.doodle.views.selectview.ScaleLayer
import com.topstack.kilonotes.base.doodle.views.selectview.SelectViewEnum
import com.topstack.kilonotes.base.doodle.views.selectview.SelectViewPolicy
import com.topstack.kilonotes.base.doodle.views.selectview.SelectedObjectsLayer
import com.topstack.kilonotes.infra.device.DeviceUtils.isPhoneType

class SelectView(
    context: Context,
    doodleView: DoodleView,
    val selection: Selection
) : ITransformChanged {
    private val mDoodleView: DoodleView = doodleView
    private val mContext: Context = context
    private val mRenderMatrix = Matrix()
    private val mInverseRenderMatrix = Matrix()
    private var guideLineLayer: GuideLineLayer? = null
    private var panel: GraphicLayer? = null

    private var rotateDragLayer: RotateLayer? = null
    private var cancelLayer: ClickableAnchorLayer? = null
    private var uniformScaleLayer: ScaleLayer? = null
    private var horizontalScaleLayer: ScaleLayer? = null
    private var verticalScaleLayer: ScaleLayer? = null
    private var mData = selection.objects

    private var multiSelectionTransformChangedAction: OnMultiSelectionOperationPerformListener? =
        null

    fun createSelectLayerParent(
        renderMatrix: Matrix,
        multiLayerView: MultiLayerView
    ): LayerParent {
        mRenderMatrix.set(renderMatrix)
        renderMatrix.invert(mInverseRenderMatrix)
        val mLayerParent = LayerFactory.createLayerParent(multiLayerView)
        mLayerParent?.addTransformChanged(this)
        mLayerParent?.apply {
            createBackgroundLayer(this)
            createPanelLayer(this)
            onCreateDecorateLayer(this, multiLayerView)
            createGuideLineLayer(this)
        }
        return mLayerParent
    }

    private fun createPanelLayer(layerParent: LayerParent) {
        panel = createRestrictRegion(layerParent)
    }

    private fun createRestrictRegion(layerParent: LayerParent): GraphicLayer {
        val resources = mContext.resources
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.STROKE
        paint.color = resources.getColor(R.color.select_view_frame_line_color)
        paint.strokeWidth = resources.getDimension(R.dimen.select_view_frame_line_width)
        paint.pathEffect = DashPathEffect(
            floatArrayOf(
                resources.getDimension(R.dimen.select_view_frame_line_height),
                resources.getDimension(R.dimen.select_view_frame_line_intervals)
            ), 0f
        )
        val unionRect = getDataRect()
        val matrix: Matrix = getRenderMatrix()
        val actionRectF = RectF(unionRect.left, unionRect.top, unionRect.right, unionRect.bottom)
        val actionPath = Path()
        actionPath.addRect(actionRectF, Path.Direction.CCW)
        val panel = GraphicLayer(
            mContext,
            matrix,
            Rect(
                actionRectF.left.toInt(),
                actionRectF.top.toInt(),
                actionRectF.right.toInt(),
                actionRectF.bottom.toInt()
            ),
            PathDrawable(actionPath, paint, mDoodleView.clipRect)
        )
        panel.setSupportScale(false)
        panel.setClipRect(mDoodleView.clipRect)
        layerParent.addLayer(panel)
        return panel
    }

    private class PathDrawable(path: Path?, drawPaint: Paint?, clipRect: Rect?) :
        IDrawableSelf {
        private val mSrcPath: Path
        private val mDrawPath = Path()
        private val mDrawPaint: Paint
        private val mClipRect: Rect

        init {
            mSrcPath = Path(path)
            mDrawPaint = Paint(drawPaint)
            mClipRect = Rect(clipRect)
        }

        override fun onDrawSelf(canvas: Canvas) {
            canvas.drawPath(mDrawPath, mDrawPaint)
        }

        override fun onDrawSelf(canvas: Canvas, matrix: Matrix) {
            val saveCount = canvas.save()
            if (!mClipRect.isEmpty) {
                canvas.clipRect(mClipRect)
            }
            mSrcPath.transform(matrix, mDrawPath)
            onDrawSelf(canvas)
            canvas.restoreToCount(saveCount)
        }
    }

    private fun onCreateDecorateLayer(
        layerParent: LayerParent,
        multiLayerView: MultiLayerView
    ) {
        val isPhone = isPhoneType(deviceType)

        //这个是选中后的- - -
        val region = panel!!.srcRect
        val matrix = panel!!.matrix
        val points = floatArrayOf(
            region.left.toFloat(),
            region.centerY().toFloat(),
            region.right.toFloat(),
            region.centerY().toFloat()
        )
        matrix.mapPoints(points)
        val initDegree = Math.toDegrees(
            Math.atan2(
                (points[3] - points[1]).toDouble(),
                (points[2] - points[0]).toDouble()
            )
        ).toFloat()
        val res = mContext.resources
        val cancelBitmap = BitmapFactory.decodeResource(res, R.drawable.doodle_element_delete)
        val rotateBitmap = BitmapFactory.decodeResource(res, R.drawable.doodle_element_rotation)
        val vScaleBitmap =
            BitmapFactory.decodeResource(res, R.drawable.doodle_element_scale_vertical)
        val hScaleBitmap =
            BitmapFactory.decodeResource(res, R.drawable.doodle_element_scale_horizontal)
        val uniformScaleBitmap =
            BitmapFactory.decodeResource(res, R.drawable.doodle_element_icon_scale)
        val padding = 0 // 扩大按钮触摸区域
        val btnSize =
            if (isPhone) res.getDimensionPixelSize(R.dimen.phone_select_view_button_size) else res.getDimensionPixelSize(
                R.dimen.select_view_button_size
            )
        val halfBtnSize = btnSize / 2
        val rotationBtnWidth =
            if (isPhone) res.getDimensionPixelSize(R.dimen.phone_select_view_rotation_button_width) else res.getDimensionPixelSize(
                R.dimen.select_view_rotation_button_width
            )
        val halfRotationBtnWidth = rotationBtnWidth / 2
        val rotationBtnHeight =
            if (isPhone) res.getDimensionPixelSize(R.dimen.phone_select_view_rotation_button_height) else res.getDimensionPixelSize(
                R.dimen.select_view_rotation_button_height
            )
        val halfRotationBtnHeight = rotationBtnHeight / 2
        val rotationOffsetY =
            if (isPhone) res.getDimensionPixelSize(R.dimen.phone_select_view_rotation_button_offset) else res.getDimensionPixelSize(
                R.dimen.select_view_rotation_button_offset
            )
        rotateDragLayer = LayerFactory.createRotateDragLayer(
            panel,
            PointF(region.centerX().toFloat(), region.bottom.toFloat()),
            RectF(
                (region.right - halfRotationBtnWidth - padding).toFloat(),
                (region.bottom - halfRotationBtnHeight - padding).toFloat(),
                (region.right + halfRotationBtnWidth + padding).toFloat(),
                (region.bottom + halfRotationBtnHeight + padding).toFloat()
            ),
            Gravity.CENTER,
            0f,
            (halfRotationBtnHeight - rotationOffsetY).toFloat(),
            LayerFactory.BitmapDrawable(
                rotateBitmap,
                Paint(Paint.ANTI_ALIAS_FLAG),
                Rect(padding, padding, rotationBtnWidth + padding, rotationBtnHeight + padding)
            ),
            layerParent,
            panel,
            initDegree
        )
        rotateDragLayer?.setClipRect(mDoodleView.clipRect)
        cancelLayer = LayerFactory.createClickableAnchorLayer(
            panel, PointF(region.left.toFloat(), region.top.toFloat()),
            RectF(
                (region.left - halfBtnSize - padding).toFloat(),
                (region.top - halfBtnSize - padding).toFloat(),
                (region.left + halfBtnSize + padding).toFloat(),
                (region.top + halfBtnSize + padding).toFloat()
            ),
            LayerFactory.BitmapDrawable(
                cancelBitmap,
                Paint(Paint.ANTI_ALIAS_FLAG),
                Rect(padding, padding, btnSize + padding, btnSize + padding)
            ),
            layerParent, initDegree
        )
        cancelLayer?.setClipRect(mDoodleView.clipRect)
        cancelLayer?.setClickedListener {
            multiSelectionTransformChangedAction?.onDelete(selection, layerParent)
        }
        uniformScaleLayer = LayerFactory.createScaleDragLayer(
            panel, PointF(region.right.toFloat(), region.bottom.toFloat()),
            RectF(
                (region.right - halfBtnSize - padding).toFloat(),
                (region.bottom - halfBtnSize - padding).toFloat(),
                (region.right + halfBtnSize + padding).toFloat(),
                (region.bottom + halfBtnSize + padding).toFloat()
            ),
            LayerFactory.BitmapDrawable(
                uniformScaleBitmap,
                Paint(Paint.ANTI_ALIAS_FLAG),
                Rect(padding, padding, btnSize + padding, btnSize + padding)
            ),
            initDegree
        )
        uniformScaleLayer?.setScaleMode(ScaleLayer.ScaleMode.UniformScale)
        uniformScaleLayer?.setClipRect(mDoodleView.clipRect)
        val scaleBtnWidth =
            if (isPhone) res.getDimensionPixelSize(R.dimen.phone_select_view_scale_button_width) else res.getDimensionPixelSize(
                R.dimen.select_view_scale_button_width
            )
        val halfScaleBtnWidth = scaleBtnWidth / 2
        val scaleBtnHeight =
            if (isPhone) res.getDimensionPixelSize(R.dimen.phone_select_view_scale_button_height) else res.getDimensionPixelSize(
                R.dimen.select_view_scale_button_height
            )
        val halfScaleBtnHeight = scaleBtnHeight / 2
        val regionF = RectF(region)
        var anchor = PointF(regionF.right, regionF.centerY())
        horizontalScaleLayer = LayerFactory.createScaleDragLayer(
            panel, anchor,
            RectF(
                anchor.x - halfScaleBtnWidth - padding,
                anchor.y - halfScaleBtnHeight - padding,
                anchor.x + halfScaleBtnWidth + padding,
                anchor.y + halfScaleBtnHeight + padding
            ),
            LayerFactory.BitmapDrawable(
                hScaleBitmap,
                Paint(Paint.ANTI_ALIAS_FLAG),
                Rect(padding, padding, scaleBtnWidth + padding, scaleBtnHeight + padding)
            ),
            initDegree
        )
        horizontalScaleLayer?.setScaleMode(ScaleLayer.ScaleMode.KeepHeight)
        horizontalScaleLayer?.setClipRect(mDoodleView.clipRect)
        anchor = PointF(regionF.centerX(), regionF.top)
        verticalScaleLayer = LayerFactory.createScaleDragLayer(
            panel, anchor,
            RectF(
                anchor.x - halfScaleBtnHeight - padding,
                anchor.y - halfScaleBtnWidth - padding,
                anchor.x + halfScaleBtnHeight + padding,
                anchor.y + halfScaleBtnWidth + padding
            ),
            LayerFactory.BitmapDrawable(
                vScaleBitmap,
                Paint(Paint.ANTI_ALIAS_FLAG),
                Rect(padding, padding, scaleBtnHeight + padding, scaleBtnWidth + padding)
            ),
            initDegree
        )
        verticalScaleLayer?.setScaleMode(ScaleLayer.ScaleMode.KeepWidth)
        verticalScaleLayer?.setClipRect(mDoodleView.clipRect)
        updateLayers(layerParent, multiLayerView)
    }

    private fun updateLayers(layerParent: LayerParent, multiLayerView: MultiLayerView) {
        val layers = layerParent.layers
        if (!layers.contains(rotateDragLayer as RotateLayer) && rotateDragLayer != null) {
            layerParent.addLayer(rotateDragLayer)
        }
        if (!layers.contains(cancelLayer as ClickableAnchorLayer) && cancelLayer != null) {
            layerParent.addLayer(cancelLayer)
        }
        if (!layers.contains(uniformScaleLayer as ScaleLayer) && uniformScaleLayer != null) {
            layerParent.addLayer(uniformScaleLayer)
        }
        var needShowScaleLayer = true
        for (insertObject in mData) {
            if (insertObject.type != InsertableObjectType.IMAGE) {
                needShowScaleLayer = false
                break
            }
        }
        if (!needShowScaleLayer) {
            multiLayerView.invalidate()
            return
        }
        if (!layers.contains(horizontalScaleLayer as ScaleLayer) && horizontalScaleLayer != null) {
            layerParent.addLayer(horizontalScaleLayer)
        }
        if (!layers.contains(verticalScaleLayer as ScaleLayer) && verticalScaleLayer != null) {
            layerParent.addLayer(verticalScaleLayer)
        }
        multiLayerView.invalidate()
    }

    private fun createBackgroundLayer(layerParent: LayerParent) {
        val matrix = Matrix()
        matrix.postTranslate(-mDoodleView.scrollX.toFloat(), -mDoodleView.scrollY.toFloat())
        matrix.postConcat(mRenderMatrix)
        val bgLayer = SelectedObjectsLayer(
            matrix,
            SelectViewPolicy.getSelectViewPolicy(mContext).touchRestrictTolerance,
            mData,
            mDoodleView
        )
        layerParent.addLayer(bgLayer)
    }

    private fun createGuideLineLayer(layerParent: LayerParent) {
        guideLineLayer = GuideLineLayer(mDoodleView)
        guideLineLayer?.setGuideLineShowListener { isShow: Boolean ->
            for (layer in layerParent.layers) {
                if (layer is ClickableAnchorLayer) {
                    layer.changeShowSwitch(!isShow)
                }
                if (layer is GraphicLayer) {
                    layer.changeShowSwitch(!isShow)
                }
            }
        }
        updateGuideLineLayerSelectedRect()
        layerParent.addLayer(guideLineLayer)
    }

    private fun updateGuideLineLayerSelectedRect() {
        if (guideLineLayer != null) {
            val initRect = getDataRect()
            val renderMatrix = getRenderMatrix()
            guideLineLayer?.updateSelectedRect(initRect, renderMatrix)
        }
    }

    private fun getRenderMatrix(): Matrix {
        val matrix = Matrix()
        if (mData.size == 1 && mData[0].type == InsertableObjectType.IMAGE) {
            matrix.set(mData[0].matrix)
        }
        matrix.postTranslate(-mDoodleView.scrollX.toFloat(), -mDoodleView.scrollY.toFloat())
        matrix.postConcat(mRenderMatrix)
        return matrix
    }

    private fun getDataRect(): RectF {
        val unionRect = RectF()
        if (mData.size == 1 && mData[0].type == InsertableObjectType.IMAGE) {
            unionRect.set(mData[0].initRectF)
        } else {
            for (insertObject in mData) {
                val objectRect = InsertableObject.getTransformedRectF(insertObject)
                unionRect.union(objectRect)
            }
        }
        return unionRect
    }

    fun setTransformChangedAction(listener: OnMultiSelectionOperationPerformListener) {
        multiSelectionTransformChangedAction = listener
    }

    override fun onTranslate(matrix: Matrix) {
        multiSelectionTransformChangedAction?.onTranslate(selection, updateMatrix(matrix))
    }

    override fun onScaled(matrix: Matrix) {
        multiSelectionTransformChangedAction?.onScale(selection, updateMatrix(matrix))
    }

    override fun onRotate(matrix: Matrix) {
        multiSelectionTransformChangedAction?.onRotate(selection, updateMatrix(matrix))
        onCheckUpdateDataRect()
    }

    private fun onCheckUpdateDataRect() {
        updateGuideLineLayerSelectedRect()
    }

    override fun onAction(t: SelectViewEnum.ActionType?, layer: ILayer?) {
        multiSelectionTransformChangedAction?.onAction(selection, t, layer)
    }

    private fun updateMatrix(matrix: Matrix): Matrix {
        return Matrix().apply {
            set(mRenderMatrix)
            postConcat(matrix)
            postConcat(mInverseRenderMatrix)
        }
    }

    override fun onAttributeChange(changeType: ChangeType, attribute: Any) {
        multiSelectionTransformChangedAction?.onAttributeChange(selection, changeType, attribute)
    }
}
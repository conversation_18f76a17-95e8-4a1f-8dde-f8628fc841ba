package com.topstack.kilonotes.base.search

import android.graphics.Matrix
import android.graphics.Rect
import android.graphics.RectF
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.graphics.toRect
import androidx.core.graphics.toRectF
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.DocumentManager
import com.topstack.kilonotes.base.doodle.model.InsertableObject
import com.topstack.kilonotes.base.doodle.model.Offset
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.doodle.model.text.InsertableText
import com.topstack.kilonotes.base.ktx.hasSpecialUnicode
import com.topstack.kilonotes.base.note.recognition.text.TextRecognitionManager
import com.topstack.kilonotes.base.note.snippet.SnippetManager
import com.topstack.kilonotes.base.note.snippet.data.SnippetTag
import com.topstack.kilonotes.base.search.model.AbsSearchResult
import com.topstack.kilonotes.base.search.model.DocRenderInfo
import com.topstack.kilonotes.base.search.model.DocSearchResult
import com.topstack.kilonotes.base.search.model.HighlightRect
import com.topstack.kilonotes.base.search.model.SearchDocInfo
import com.topstack.kilonotes.base.search.model.SearchIndexCreated
import com.topstack.kilonotes.base.search.model.SearchIndexEntity
import com.topstack.kilonotes.base.search.model.SearchIndexEntityState
import com.topstack.kilonotes.base.search.model.SearchIndexMissingContent
import com.topstack.kilonotes.base.search.model.SearchIndexNotCreated
import com.topstack.kilonotes.base.search.model.SearchState
import com.topstack.kilonotes.base.search.model.SearchSuccess
import com.topstack.kilonotes.base.search.model.SearchType
import com.topstack.kilonotes.base.search.model.Searching
import com.topstack.kilonotes.base.search.model.SnippetSearchResultWrapper
import com.topstack.kilonotes.base.search.repository.SearchRepository
import com.topstack.kilonotes.base.track.event.SearchEvent
import com.topstack.kilonotes.base.util.PDFManager
import com.topstack.kilonotes.base.util.TextViewLinesUtil
import com.topstack.kilonotes.infra.config.PersonalInfoCollectionCounter
import com.topstack.kilonotes.infra.foundation.threadpool.Executors
import com.topstack.kilonotes.infra.size.ptIntToPx
import com.topstack.kilonotes.infra.size.ptIntToPxInt
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.mlkit.recognition.text.model.TextRecognitionResult
import com.topstack.kilonotes.notedata.NoteRepository
import com.topstack.pdfrun.PdfScope
import com.topstack.pdfrun.doc.PdfDocument
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.lang.Float.min
import java.lang.ref.WeakReference
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.resume
import kotlin.math.ceil
import kotlin.system.measureTimeMillis

/**
 * 搜索管理类
 * 索引相关增删改使用searchWorkDispatcher
 * 其他查询等不重要操作正常切Dispatchers.IO
 */
object SearchManager {
    private const val TAG = "SearchManager"

    private const val OCR_BITMAP_MAX_WIDTH = 960
    private val searchWorkThreadPool = Executors.newIOThreadPool()
    private val searchWorkDispatcher by lazy(LazyThreadSafetyMode.NONE) { searchWorkThreadPool.asCoroutineDispatcher() }
    private var weakPdfScope: WeakReference<PdfScope>? = null
    private val pdfScope: PdfScope
        get() = weakPdfScope?.get() ?: PdfScope().also {
            weakPdfScope = WeakReference(it)
        }
    private val specialPdf: List<String> = listOf("3f2868a9bb6f2e3fd505a1421b975fe2edc24201")

    suspend fun searchDocument(
        keyWords: String,
        document: Document,
        searchStateCallback: suspend (SearchState) -> Unit
    ) {
        PersonalInfoCollectionCounter.searchCount++
        val pageSearchResultList = mutableListOf<DocSearchResult>()
        val pdfSearchResultList = mutableListOf<DocSearchResult>()
        createSearchIndexAndSearch(keyWords, document,
            documentCreateSearchIndexTimeMillisCallback = { estimateTimeMillis ->
                searchStateCallback.invoke(
                    Searching(
                        symbols = keyWords,
                        docTitle = document.title,
                        estimateTimeMillis = estimateTimeMillis,
                        (pageSearchResultList + pdfSearchResultList).isEmpty()
                    )
                )
            }, pageSearchResultCallback = {
                pageSearchResultList.addAll(it)
                if ((pageSearchResultList + pdfSearchResultList).isNotEmpty()) {
                    searchStateCallback.invoke(
                        SearchSuccess(
                            keyWords,
                            (pageSearchResultList + pdfSearchResultList),
                            false
                        )
                    )
                }
            },
            pdfSearchResultCallback = {
                pdfSearchResultList.addAll(it)
                if ((pageSearchResultList + pdfSearchResultList).isNotEmpty()) {
                    searchStateCallback.invoke(
                        SearchSuccess(
                            keyWords,
                            (pageSearchResultList + pdfSearchResultList),
                            false
                        )
                    )
                }
            })
        searchStateCallback.invoke(
            SearchSuccess(
                keyWords,
                (pageSearchResultList + pdfSearchResultList),
                true
            )
        )
    }

    suspend fun createSearchIndexAndSearch(
        keyWords: String,
        document: Document,
        documentCreateSearchIndexTimeMillisCallback: suspend ((Long) -> Unit),
        pdfSearchResultCallback: suspend ((List<DocSearchResult>) -> Unit),
        pageSearchResultCallback: suspend ((List<DocSearchResult>) -> Unit)
    ) = withContext(searchWorkDispatcher) {
        LogHelper.d(TAG, "createSearchIndexAndSearch  start")
        //先给每一页建立空白索引
        createEmptySearchIndexList(document)
        if (!isActive) return@withContext
        val pageUUID2SearchIndexTimeMillisMap = ConcurrentHashMap<String, Long>()
        if (!isActive) return@withContext
        val allSearchIndexEntity = SearchRepository.getAllSearchIndexEntityByDocId(document.uuid)
        document.pages.forEachIndexed { index, page ->
            if (!isActive) return@withContext
            val currentPageIndexEntity =
                allSearchIndexEntity.find { it.searchDocInfo.pageId == page.uuid }
            val executionTime = measureTimeMillis {
                var needSearchPage = false
                if (currentPageIndexEntity?.pageContent == null) {
                    updatePageContentSearchIndex(document, page, false) {
                        needSearchPage = it.contains(keyWords, true)
                    }
                } else {
                    needSearchPage = currentPageIndexEntity.pageContent.contains(keyWords, true)
                }
                if (needSearchPage) {
                    searchPageContent(
                        keyWords,
                        document,
                        listOf(page.uuid)
                    ).let { pageDocSearchResultList ->
                        if (pageDocSearchResultList.isNotEmpty()) {
                            pageSearchResultCallback.invoke(pageDocSearchResultList)
                        }
                    }
                }
                var needSearchPdf = false
                if (currentPageIndexEntity?.pdfContent == null) {
                    updatePagePdfContentSearchIndex(document, page, false) {
                        needSearchPdf = it.contains(keyWords, true)
                    }
                } else {
                    needSearchPdf = currentPageIndexEntity.pdfContent.contains(keyWords, true)
                }
                if (needSearchPdf) {
                    searchPdfContent(
                        keyWords,
                        document,
                        listOf(page.uuid)
                    ).let { pdfDocSearchResultList ->
                        if (pdfDocSearchResultList.isNotEmpty()) {
                            pdfSearchResultCallback.invoke(pdfDocSearchResultList)
                        }
                    }
                }
            }
            pageUUID2SearchIndexTimeMillisMap[page.uuid.toString()] = executionTime
            pageUUID2SearchIndexTimeMillisMap.values.let { timeMillis ->
                documentCreateSearchIndexTimeMillisCallback.invoke(timeMillis.sum() / timeMillis.size * (document.pageIds.size - index - 1))
            }
        }
    }

    suspend fun needCreateSearchIndexing(document: Document): Boolean {
        val searchIndexEntityList = SearchRepository.getAllSearchIndexEntityByDocId(document.uuid)
        document.pageIds.forEach { pageId ->
            val searchIndexEntity = searchIndexEntityList.find { it.searchDocInfo.pageId == pageId }
            if (searchIndexEntity == null) {
                return true
            } else {
                if (!searchIndexEntity.isFinished) {
                    return true
                }
            }
        }
        return false
    }

    private suspend fun createEmptySearchIndexList(document: Document): List<SearchIndexEntity> =
        withContext(searchWorkDispatcher) {
            //找到所有已经创建过索引的信息
            val allEntityPageIds =
                SearchRepository.findDocAllEntityPageId(document.uuid)
            val emptySearchIndexList = mutableListOf<SearchIndexEntity>()
            if (document.isEmpty()) return@withContext emptyList<SearchIndexEntity>()
            if (document.pages.isEmpty()) {
                if (document.isStoredInObsoleteKiloNotesRoom()) {
                    DocumentManager.parsePagesInfo(document)
                } else {
                    NoteRepository.inflateDocumentPages(document)
                }
            }
            document.pages.forEach { page ->
                if (!allEntityPageIds.contains(page.uuid)) {
                    emptySearchIndexList.add(
                        SearchIndexEntity(
                            SearchDocInfo(
                                document.uuid,
                                page.uuid
                            )
                        )
                    )
                }
            }
            SearchRepository.addSearchIndexEntityList(emptySearchIndexList)
            emptySearchIndexList
        }

    @JvmOverloads
    @JvmStatic
    fun updatePagePdfContentSearchIndexAsyncIfNeeded(
        document: Document,
        page: Page,
        force: Boolean = true
    ) {
        GlobalScope.launch(Dispatchers.IO) {
            if (SearchRepository.existsDocEntity(document.uuid)) {
                val pageEntityExists =
                    SearchRepository.findCurrentSearchIndexEntity(document.uuid, page.uuid) != null
                if (!pageEntityExists) {
                    SearchRepository.addSearchIndexEntityList(
                        listOf(
                            SearchIndexEntity(
                                SearchDocInfo(
                                    document.uuid,
                                    page.uuid
                                )
                            )
                        )
                    )
                }
                updatePagePdfContentSearchIndex(document, page, force)
            }
        }
    }

    suspend fun updatePagePdfContentSearchIndex(
        document: Document,
        page: Page,
        force: Boolean = true,
        pdfContentCallback: ((String) -> Unit)? = null
    ) = withContext(searchWorkDispatcher) {
        //不强制更新，检查索引是否缺少页面内容，不缺少则不需要更新
        if (!force) {
            val entityState =
                getPageSearchIndexEntityState(SearchDocInfo(document.uuid, page.uuid))
            if (entityState !is SearchIndexNotCreated && entityState is SearchIndexMissingContent && !entityState.isMissingPdfContent) {
                return@withContext
            }
        }

        var pdfContent = ""
        val pdfBounds = mutableListOf<Rect>()
        ensureActive()
        val pdfFile = document.resources.openFile(page.paper.file)
        val pdfPageIndex = page.paper.pageIndex
        if (!pdfFile.exists()) {
            LogHelper.d(TAG, "pdfFile not exists path = ${pdfFile.absolutePath}")
            return@withContext
        }
        //内置纸张 ,内容为空，或含有特殊字符的需要ocr
        val isBuiltinPDF = page.paper.isBuiltin()

        var pdfPageWidthInPt = 0
        var pdfPageHeightInPt = 0

        pdfScope.run { pdfContext ->
            PdfDocument.loadFrom(pdfContext, pdfFile.absolutePath)?.use { pdfDocument ->
                pdfDocument.openPage(pdfPageIndex)?.use { pdfPage ->
                    //openPage 会顺便给pdfPage赋值宽高
                    pdfPageWidthInPt = pdfPage.width
                    pdfPageHeightInPt = pdfPage.height
                    //内置纸张跳过读取文字
                    if (!isBuiltinPDF && !specialPdf.contains(
                            pdfFile.name
                        )
                    ) {
                        pdfContent = pdfPage.getText()
                    }
                }
            }
        }
        if (isBuiltinPDF || pdfContent.isEmpty() || pdfContent.hasSpecialUnicode()) {
            //判断特殊字符比较耗时，所以用前两个条件来判断
            if (!isBuiltinPDF && pdfContent.isNotEmpty()) {
                SearchEvent.sendNoteSearchException(SearchEvent.SEARCH_EXCEPTION_UNUSUAL_FORMATTING_IN_NOTES)
            }
            var textRecognitionCallback: ((result: TextRecognitionResult, taskId: UUID) -> Unit)? =
                null
            suspendCancellableCoroutine { cont ->
                val scale = min(
                    OCR_BITMAP_MAX_WIDTH / pdfPageWidthInPt.toFloat(),
                    OCR_BITMAP_MAX_WIDTH / pdfPageHeightInPt.toFloat()
                )
                val bitmapWidth = ceil(scale * pdfPageWidthInPt).toInt()
                val bitmapHeight = ceil(scale * pdfPageHeightInPt).toInt()
                var currentTaskId: UUID = UUID.randomUUID()
                val ocrBitmap =
                    PDFManager.pdfPage2Bitmap(pdfFile, pdfPageIndex, bitmapWidth, bitmapHeight)
                textRecognitionCallback = { result: TextRecognitionResult, taskId: UUID ->
                    if (currentTaskId == taskId) {
                        //TODO 获取段落分割，并且将获取到的相对于图片位置的Rect转换成相对于PDF的Rect
                        val tempMatrix = Matrix()
                        pdfContent = result.symbols
                        tempMatrix.postScale(1 / scale, 1 / scale)
                        result.symbolsRectList.forEach { symbolsRect ->
                            val destRectF = symbolsRect.toRectF()
                            tempMatrix.mapRect(destRectF)
                            pdfBounds.add(destRectF.toRect())
                        }
                        ocrBitmap.recycle()
                        cont.resume(Unit)
                    }
                }
                TextRecognitionManager.addLowPriorityTaskFinishCallback(textRecognitionCallback!!)

                currentTaskId = TextRecognitionManager.addRecognitionTask(
                    ocrBitmap,
                    TextRecognitionManager.LOW_PRIORITY
                )
            }
            if (textRecognitionCallback != null) {
                TextRecognitionManager.removeLowPriorityTaskFinishCallback(textRecognitionCallback!!)
            }
        }
        ensureActive()
        SearchRepository.updatePdfContent(
            document.uuid,
            page.uuid,
            pdfContent,
            pdfBounds
        )
        if (pdfContent.isBlank()) {
            SearchEvent.sendNoteSearchException(SearchEvent.SEARCH_EXCEPTION_DOC_PDF_CONTENT)
        } else {
            pdfContentCallback?.invoke(pdfContent)
        }
    }

    @JvmOverloads
    @JvmStatic
    fun updatePageContentSearchIndexAsyncIfNeeded(
        document: Document,
        page: Page,
        force: Boolean = true
    ) {
        GlobalScope.launch(Dispatchers.IO) {
            if (SearchRepository.existsDocEntity(document.uuid)) {
                val pageEntityExists =
                    SearchRepository.findCurrentSearchIndexEntity(document.uuid, page.uuid) != null
                if (!pageEntityExists) {
                    SearchRepository.addSearchIndexEntityList(
                        listOf(
                            SearchIndexEntity(
                                SearchDocInfo(
                                    document.uuid,
                                    page.uuid
                                )
                            )
                        )
                    )
                }
                updatePageContentSearchIndex(document, page, force)
            }
        }
    }

    suspend fun updatePageContentSearchIndex(
        document: Document,
        page: Page,
        force: Boolean = true,
        pageContentCallback: ((String) -> Unit)? = null
    ) = withContext(searchWorkDispatcher) {
        //不强制更新，检查索引是否缺少页面内容，不缺少则不需要更新
        if (!force) {
            val entityState =
                getPageSearchIndexEntityState(SearchDocInfo(document.uuid, page.uuid))
            if (entityState !is SearchIndexNotCreated && entityState is SearchIndexMissingContent && !entityState.isMissingPageContent) {
                return@withContext
            }
        }
        val needLoadDraws = !page.isDrawsLoaded
        val draws = if (needLoadDraws) {
            if (document.isStoredInObsoleteKiloNotesRoom()) {
                DocumentManager.getPageDrawsFromFile(document, page)?.draws ?: emptyList()
            } else {
                NoteRepository.inflatePageDrawingElements(page)
                page.draws
            }
        } else {
            page.draws
        }
        if (draws == null) {
            LogHelper.d(TAG, "draws is null, don't need update")
            return@withContext
        }
        val pageContentBuilder = StringBuilder()
        draws.forEach { insertableObject ->
            if (insertableObject is InsertableText) {
                if (pageContentBuilder.isNotEmpty()) {
                    pageContentBuilder.append("\u0020")
                }
                pageContentBuilder.append(insertableObject.text)
            }
        }
        if (needLoadDraws) {
            page.unloadDraws()
        }
        SearchRepository.updatePageContent(
            document.uuid,
            page.uuid,
            pageContentBuilder.toString()
        )
        if (pageContentBuilder.toString().isBlank()) {
            SearchEvent.sendNoteSearchException(SearchEvent.SEARCH_EXCEPTION_DOC_PAGE_CONTENT)
        } else {
            pageContentCallback?.invoke(pageContentBuilder.toString())
        }
    }

    suspend fun searchContainsKeyWordDocInfo(keyWords: String): List<SearchDocInfo> =
        withContext(Dispatchers.IO) {
            SearchRepository.getSearchDocInfoByWord(keyWords)
        }

    suspend fun searchContainsKeyWordDocInfo(
        keyWords: String,
        documents: List<Document>
    ): List<SearchDocInfo> =
        withContext(Dispatchers.IO) {
            SearchRepository.getSearchDocInfoByWord(keyWords, documents)
        }

    suspend fun search(
        keyWords: String,
        searchType: SearchType,
        currentDocument: Document,
        allDocument: List<Document>
    ): List<AbsSearchResult> {
        PersonalInfoCollectionCounter.searchCount++
        return when (searchType) {
            SearchType.SNIPPET -> {
                val searchSnippet = searchSnippet(keyWords)
                searchSnippet.mapNotNullTo(mutableListOf()) { snippetSearchResult ->
                    val documentId = snippetSearchResult.snippet.documentId
                    val pageId = snippetSearchResult.snippet.pageId
                    SnippetSearchResultWrapper(
                        documentId,
                        pageId,
                        keyWords,
                        snippetSearchResult = snippetSearchResult
                    )
                }
            }

            SearchType.CURRENT_DOC -> {
                val searchDocInfos =
                    searchContainsKeyWordDocInfo(keyWords, listOf(currentDocument))
                if (searchDocInfos.isEmpty()) {
                    emptyList()
                } else {
                    val pageIds = searchDocInfos.mapTo(mutableListOf()) { searchDocInfo ->
                        searchDocInfo.pageId
                    }
                    val searchResults = mutableListOf<AbsSearchResult>()
                    searchResults.addAll(searchPageContent(keyWords, currentDocument, pageIds))
                    searchResults.addAll(searchPdfContent(keyWords, currentDocument, pageIds))

                    searchResults
                }
            }

            SearchType.OTHER_DOC -> {
                val otherDocuments = allDocument.filter { it.uuid != currentDocument.uuid }
                val searchDocInfos =
                    searchContainsKeyWordDocInfo(keyWords, otherDocuments)
                if (searchDocInfos.isEmpty()) {
                    emptyList()
                } else {
                    searchInDocuments(keyWords, otherDocuments, searchDocInfos)
                }
            }
        }
    }


    fun inflateSearchResult(
        document: Document,
        searchResult: DocSearchResult
    ): DocSearchResult {
        if (searchResult.needInflate && document.uuid == searchResult.docId) {
            // 目前只有 PDF 文本内容搜索需要填充文本位置信息
            var targetPage = document.pages.find { it.uuid == searchResult.pageId }
            if (targetPage == null) {
                if (document.isStoredInObsoleteKiloNotesRoom()) {
                    DocumentManager.parsePagesInfo(document, listOf(searchResult.pageId))
                    targetPage = document.pages.find { it.uuid == searchResult.pageId }
                } else {
                    runBlocking(Dispatchers.IO) {
                        targetPage =
                            NoteRepository.retrievePageById(document.uuid, searchResult.pageId)
                    }
                }
            }

            targetPage?.let { page ->
                val pdfFile = document.resources.openFile(page.paper.file)
                if (pdfFile.exists()) {
                    pdfScope.run { pdfContext ->
                        PdfDocument.loadFrom(pdfContext, pdfFile.absolutePath)
                            ?.use { pdfDocument ->
                                pdfDocument.openPage(page.paper.pageIndex)?.use { pdfPage ->
                                    val pageAllHighlightRectList =
                                        mutableListOf<HighlightRect>()
                                    val findResults =
                                        pdfPage.findAll(searchResult.searchKeywords)
                                    if (searchResult.inflateIndex in findResults.indices) {
                                        val findResult = findResults[searchResult.inflateIndex]
                                        val containedBoxes = mutableListOf<RectF>()
                                        findResult.boxes.forEach { textBox ->
                                            val boxRectInPixel = RectF(
                                                ptIntToPx(textBox.left),
                                                ptIntToPx(textBox.top),
                                                ptIntToPx(textBox.right),
                                                ptIntToPx(textBox.bottom),
                                            )
                                            containedBoxes.add(boxRectInPixel)
                                        }
                                        if (containedBoxes.isNotEmpty()) {
                                            val highlightRects =
                                                containedBoxes.map {
                                                    HighlightRect(
                                                        Matrix(),
                                                        it.toRect()
                                                    )
                                                }
                                            pageAllHighlightRectList.addAll(highlightRects)
                                            searchResult.run {
                                                needInflate = false
                                                docRenderInfo =
                                                    DocRenderInfo(
                                                        highlightRects,
                                                        pageAllHighlightRectList
                                                    )
                                            }
                                        }
                                    }
                                }

                            }
                    }
                }
            }
        }

        return searchResult
    }

    private suspend fun searchSnippet(keyWords: String) =
        SnippetManager.search(keyWords, SnippetTag(pseudoTagFlag = SnippetTag.FLAG_TAG_PSEUDO_ALL))

    private suspend fun searchPageContent(
        keyWords: String,
        document: Document,
        searchPageIds: List<UUID> = document.pageIds
    ): List<DocSearchResult> =
        withContext(Dispatchers.IO) {
            val searchDocList = mutableListOf<DocSearchResult>()
            if (document.pageIds.isEmpty()) return@withContext searchDocList
            if (searchPageIds.isEmpty()) return@withContext searchDocList
            if (document.pages.isEmpty()) {
                if (document.isStoredInObsoleteKiloNotesRoom()) {
                    DocumentManager.parsePagesInfo(document)
                } else {
                    NoteRepository.inflateDocumentPages(document)
                }
            }
            if (!isActive) return@withContext searchDocList
            val pages = mutableListOf<Page>().apply { addAll(document.pages) }
            if (pages.isEmpty()) return@withContext searchDocList
            //appContext创建的view 生命周期长，并且不支持主题
            //传入activity/fragment context的话 会导致内存泄露
            //考虑使用mainActivity
            val tempEditText = AppCompatEditText(AppUtils.appContext)
            for (page in pages) {
                if (!isActive) break
                if (!searchPageIds.contains(page.uuid)) continue
                val pageAllHighlightRectList = mutableListOf<HighlightRect>()
                val pageSearchResult = mutableListOf<DocSearchResult>()
                val needLoadDraws = !page.isDrawsLoaded
                val draws = if (!needLoadDraws) {
                    mutableListOf<InsertableObject>().apply {
                        addAll(page.draws)
                    }
                } else {
                    if (document.isStoredInObsoleteKiloNotesRoom()) {
                        DocumentManager.getPageDrawsFromFile(document, page)?.draws ?: emptyList()
                    } else {
                        NoteRepository.inflatePageDrawingElements(page)
                        page.draws
                    }
                }
                if (!isActive) {
                    if (needLoadDraws) {
                        page.unloadDraws()
                    }
                    break
                }
                if (draws.isEmpty()) continue
                val resizedRectFInPixel = page.resizedRectFInPixel
                for (draw in draws) {
                    if (draw is InsertableText &&
                        isInsertableTextInResizedPage(resizedRectFInPixel, draw) &&
                        isInsertableTextContainsKeyword(keyWords, draw)
                    ) {
                        if (!isActive) {
                            if (needLoadDraws) {
                                page.unloadDraws()
                            }
                            break
                        }
                        draw.buildEditText(AppUtils.appContext, tempEditText)
                        val keywordsBounds = TextViewLinesUtil.getKeywordsBounds(
                            tempEditText,
                            keyWords
                        )
                        for (keywordsBound in keywordsBounds) {
                            val hasAnyBoundsInPdfPxResizedPage = hasAnyBoundsInPdfPxResizedPage(
                                keywordsBound,
                                resizedRectFInPixel,
                                draw.matrix
                            )
                            if (!hasAnyBoundsInPdfPxResizedPage) continue
                            val highlightRects = keywordsBound.mapTo(mutableListOf()) { rect ->
                                HighlightRect(Matrix(draw.matrix), rect)
                            }
                            pageAllHighlightRectList.addAll(highlightRects)
                            val docSearchResult = DocSearchResult(
                                document.uuid,
                                page.uuid,
                                searchKeywords = keyWords
                            ).apply {
                                docRenderInfo =
                                    DocRenderInfo(highlightRects, pageAllHighlightRectList)
                            }
                            pageSearchResult.add(docSearchResult)
                        }
                    }
                }
                if (needLoadDraws) {
                    page.unloadDraws()
                }
                searchDocList.addAll(pageSearchResult)
            }

            searchDocList
        }

    private suspend fun searchPdfContent(
        keyWords: String,
        document: Document,
        searchPageIds: List<UUID> = document.pageIds
    ): List<DocSearchResult> =
        withContext(Dispatchers.IO) {
            val searchDocList = mutableListOf<DocSearchResult>()
            if (document.pageIds.isEmpty()) return@withContext searchDocList
            if (searchPageIds.isEmpty()) return@withContext searchDocList
            if (document.pages.isEmpty()) {
                if (document.isStoredInObsoleteKiloNotesRoom()) {
                    DocumentManager.parsePagesInfo(document)
                } else {
                    NoteRepository.inflateDocumentPages(document)
                }
            }
            if (!isActive) return@withContext searchDocList
            val pages = mutableListOf<Page>().apply { addAll(document.pages) }
            if (pages.isEmpty()) return@withContext searchDocList

            for (page in pages) {
                if (!isActive) break
                if (!searchPageIds.contains(page.uuid)) continue
                val pageAllHighlightRectList = mutableListOf<HighlightRect>()
                val searchResultsInPage = mutableListOf<DocSearchResult>()

                val resizedRectFInPixel = page.resizedRectFInPixel

                val pdfFile = document.resources.openFile(page.paper.file)
                if (!pdfFile.exists()) {
                    continue
                }
                if (!isActive) break
                val searchIndexEntity =
                    SearchRepository.findCurrentSearchIndexEntity(document.uuid, page.uuid)
                        ?: continue
                if (!isActive) break
                val pdfContentBounds = searchIndexEntity.pdfContentBounds
                if (pdfContentBounds.isEmpty()) {
                    //pdf内容搜索
                    if (!isActive) break
                    val pageViewPortOffset =
                        page.pageViewPortOffsetToPaperInPoint ?: Offset()
                    if (pageViewPortOffset.leftOffset > 0
                        || pageViewPortOffset.topOffset > 0
                        || pageViewPortOffset.rightOffset < 0
                        || pageViewPortOffset.bottomOffset < 0
                    ) {
                        // 纸张被裁，得获取文本位置以确定是否被裁
                        pdfScope.run { pdfContext ->
                            PdfDocument.loadFrom(pdfContext, pdfFile.absolutePath)
                                ?.use { pdfDocument ->
                                    pdfDocument.openPage(page.paper.pageIndex)?.use { pdfPage ->
                                        pdfPage.findAll(keyWords).forEach { findResult ->
                                            val containedBoxes = mutableListOf<RectF>()
                                            findResult.boxes.forEach { textBox ->
                                                val boxRectInPixel = RectF(
                                                    ptIntToPx(textBox.left),
                                                    ptIntToPx(textBox.top),
                                                    ptIntToPx(textBox.right),
                                                    ptIntToPx(textBox.bottom),
                                                )
                                                if (inRectInResizedPage(
                                                        boxRectInPixel,
                                                        resizedRectFInPixel
                                                    )
                                                ) {
                                                    containedBoxes.add(boxRectInPixel)
                                                }
                                            }
                                            if (containedBoxes.isNotEmpty()) {
                                                val highlightRects =
                                                    containedBoxes.map {
                                                        HighlightRect(
                                                            Matrix(),
                                                            it.toRect()
                                                        )
                                                    }
                                                pageAllHighlightRectList.addAll(highlightRects)

                                                searchResultsInPage.add(
                                                    DocSearchResult(
                                                        document.uuid,
                                                        page.uuid,
                                                        searchKeywords = keyWords,
                                                    ).apply {
                                                        docRenderInfo =
                                                            DocRenderInfo(
                                                                highlightRects,
                                                                pageAllHighlightRectList
                                                            )
                                                    })
                                            }
                                        }
                                    }
                                }
                        }
                    } else {
                        if (!isActive) break
                        val pdfContent = searchIndexEntity.pdfContent ?: continue
                        val matchRanges = TextViewLinesUtil.getMatchRanges(keyWords, pdfContent)
                        matchRanges.forEachIndexed { index, intRange ->
                            searchResultsInPage.add(
                                DocSearchResult(
                                    document.uuid,
                                    page.uuid,
                                    searchKeywords = keyWords,
                                    needInflate = true,
                                    inflateIndex = index,
                                )
                            )
                        }
                    }
                } else {
                    //影印版搜索，数据都处理好，直接拿来用
                    if (!isActive) break
                    val pdfContent = searchIndexEntity.pdfContent ?: continue
                    val matchRanges = TextViewLinesUtil.getMatchRanges(keyWords, pdfContent)
                    for (matchRange in matchRanges) {
                        if (!isActive) break
                        val pdfBounds =
                            pdfContentBounds.subList(matchRange.first, matchRange.last + 1)
                        val hasAnyBoundsInResizedPage =
                            hasAnyBoundsInPdfPtResizedPage(pdfBounds, resizedRectFInPixel)
                        if (!hasAnyBoundsInResizedPage) continue
                        val highlightRects =
                            pdfBounds.map {
                                HighlightRect(
                                    Matrix(),
                                    Rect(
                                        ptIntToPxInt(it.left),
                                        ptIntToPxInt(it.top),
                                        ptIntToPxInt(it.right),
                                        ptIntToPxInt(it.bottom)
                                    )
                                )
                            }

                        pageAllHighlightRectList.addAll(highlightRects)

                        searchResultsInPage.add(
                            DocSearchResult(
                                document.uuid,
                                page.uuid,
                                searchKeywords = keyWords,
                            ).apply {
                                docRenderInfo =
                                    DocRenderInfo(
                                        highlightRects,
                                        pageAllHighlightRectList
                                    )
                            })
                    }

                }

                searchDocList.addAll(searchResultsInPage)
            }

            searchDocList
        }

    private fun hasAnyBoundsInPdfPtResizedPage(
        pdfBoundsInPT: List<Rect>,
        resizedRectFInPixel: RectF
    ): Boolean {
        val tempRectF = RectF()
        for (pdfBoundInPT in pdfBoundsInPT) {
            tempRectF.set(
                ptIntToPx(pdfBoundInPT.left),
                ptIntToPx(pdfBoundInPT.top),
                ptIntToPx(pdfBoundInPT.right),
                ptIntToPx(pdfBoundInPT.bottom)
            )
            if (inRectInResizedPage(tempRectF, resizedRectFInPixel)) return true
        }
        return false
    }

    private fun hasAnyBoundsInPdfPxResizedPage(
        pdfBoundsInPx: List<Rect>,
        resizedRectFInPixel: RectF,
        transform: Matrix = Matrix()
    ): Boolean {
        val tempRectF = RectF()
        val needTransform = !transform.isIdentity
        for (pdfBoundInPx in pdfBoundsInPx) {
            tempRectF.set(pdfBoundInPx)
            if (needTransform) {
                transform.mapRect(tempRectF)
            }
            if (inRectInResizedPage(tempRectF, resizedRectFInPixel)) return true
        }
        return false
    }

    private fun isInsertableTextInResizedPage(
        resizedPageRectF: RectF,
        insertableObject: InsertableText
    ): Boolean {
        val transformedRectF = InsertableObject.getTransformedRectF(insertableObject)
        return inRectInResizedPage(transformedRectF, resizedPageRectF)
    }

    private fun inRectInResizedPage(rectF: RectF, resizedPageRectF: RectF): Boolean =
        resizedPageRectF.contains(rectF) ||
                RectF.intersects(resizedPageRectF, rectF)

    private fun isInsertableTextContainsKeyword(
        keyWords: String,
        insertableText: InsertableText
    ): Boolean {
        return insertableText.text.contains(keyWords, true)
    }

    /**
     * sort by documents insertableText pdfContent
     */
    private suspend fun searchInDocuments(
        keyWords: String,
        documents: List<Document>,
        filterSearchDocInfos: List<SearchDocInfo>? = null
    ): List<DocSearchResult> {
        val searchList = mutableListOf<DocSearchResult>()
        coroutineScope {
            for (document in documents) {
                val searchPageIds: List<UUID> = if (filterSearchDocInfos != null) {
                    filterSearchDocInfos.filter { searchDocInfo ->
                        searchDocInfo.docId == document.uuid
                    }.map { searchDocInfo ->
                        searchDocInfo.pageId
                    }
                } else {
                    document.pageIds
                }
                val searchContentJob = async {
                    searchPageContent(keyWords, document, searchPageIds)
                }
                val searchPdfJob = async {
                    searchPdfContent(keyWords, document, searchPageIds)
                }
                val docSearchResult = searchContentJob.await()
                val pdfSearchResult = searchPdfJob.await()
                if (docSearchResult.isNotEmpty()) {
                    searchList.addAll(docSearchResult)
                }
                if (pdfSearchResult.isNotEmpty()) {
                    searchList.addAll(pdfSearchResult)
                }
            }
        }
        return searchList
    }

    suspend fun getPageSearchIndexEntityState(searchDocInfo: SearchDocInfo): SearchIndexEntityState =
        withContext(Dispatchers.IO) {
            val currentSearchIndexEntity = SearchRepository.findCurrentSearchIndexEntity(
                searchDocInfo.docId,
                searchDocInfo.pageId
            )
            if (currentSearchIndexEntity == null) {
                SearchIndexNotCreated
            } else {
                if (currentSearchIndexEntity.isFinished) {
                    SearchIndexCreated
                } else {
                    SearchIndexMissingContent(
                        currentSearchIndexEntity.pageContent == null,
                        currentSearchIndexEntity.pdfContent == null
                    )
                }
            }
        }

    fun deleteSearchIndexEntityAsync(docId: UUID, pageId: UUID) {
        GlobalScope.launch(Dispatchers.IO) {
            deleteSearchIndexEntity(docId, pageId)
        }
    }

    suspend fun deleteSearchIndexEntity(docId: UUID, pageId: UUID) =
        withContext(searchWorkDispatcher) {
            SearchRepository.deleteSearchIndexEntity(docId, pageId)
        }

    fun deleteDocumentSearchIndexEntityAsync(docId: UUID) {
        GlobalScope.launch(Dispatchers.IO) {
            deleteDocumentSearchIndexEntity(docId)
        }
    }

    suspend fun deleteDocumentSearchIndexEntity(docId: UUID) =
        withContext(searchWorkDispatcher) {
            SearchRepository.deleteDocumentSearchIndexEntity(docId)
        }
}
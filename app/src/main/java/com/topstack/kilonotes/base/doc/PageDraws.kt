package com.topstack.kilonotes.base.doc

import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.topstack.kilonotes.base.doodle.model.InsertableObject
import com.topstack.kilonotes.base.util.concurrent.CopyOnIterateArrayList

@Keep
class PageDraws @JvmOverloads constructor(
    @Expose(serialize = false)
    @SerializedName("draws")
    var draws: CopyOnIterateArrayList<InsertableObject> = CopyOnIterateArrayList()
)
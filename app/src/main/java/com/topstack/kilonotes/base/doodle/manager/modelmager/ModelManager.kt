package com.topstack.kilonotes.base.doodle.manager.modelmager

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Region
import android.os.SystemClock
import android.util.Size
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.FileProvider
import com.topstack.kilonotes.BuildConfig
import com.topstack.kilonotes.KiloApp.Companion.deviceType
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.io.NeedResourceManager
import com.topstack.kilonotes.base.doc.io.ResourceManager
import com.topstack.kilonotes.base.doodle.listeners.GraphShapeInsertListener
import com.topstack.kilonotes.base.doodle.listeners.IBitmapGenerateFinishListener
import com.topstack.kilonotes.base.doodle.listeners.ICallReplaceImageListener
import com.topstack.kilonotes.base.doodle.listeners.ICallSwitchInputModeListener
import com.topstack.kilonotes.base.doodle.listeners.IImageModeTouch
import com.topstack.kilonotes.base.doodle.listeners.IInputModelChangeListener
import com.topstack.kilonotes.base.doodle.listeners.IInputTextShowListener
import com.topstack.kilonotes.base.doodle.listeners.IInsertableObjectListener
import com.topstack.kilonotes.base.doodle.listeners.IInterceptTouchEventAction
import com.topstack.kilonotes.base.doodle.listeners.ILassoModeTouch
import com.topstack.kilonotes.base.doodle.listeners.IObjectPastedListener
import com.topstack.kilonotes.base.doodle.listeners.IStrokeReadyListener
import com.topstack.kilonotes.base.doodle.listeners.ITouchEventListener
import com.topstack.kilonotes.base.doodle.listeners.IVerifyRecordStateAction
import com.topstack.kilonotes.base.doodle.listeners.OnRequestImagePermissionListener
import com.topstack.kilonotes.base.doodle.listeners.PickImageInsertListener
import com.topstack.kilonotes.base.doodle.model.InsertableObject
import com.topstack.kilonotes.base.doodle.model.InsertableObjectType
import com.topstack.kilonotes.base.doodle.model.InsertableTop
import com.topstack.kilonotes.base.doodle.model.LassoStyle
import com.topstack.kilonotes.base.doodle.model.LassoType
import com.topstack.kilonotes.base.doodle.model.Offset
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.doodle.model.PaperAware
import com.topstack.kilonotes.base.doodle.model.graph.InsertableGraph
import com.topstack.kilonotes.base.doodle.model.image.ImageEditor
import com.topstack.kilonotes.base.doodle.model.image.InsertableBitmap
import com.topstack.kilonotes.base.doodle.model.markdown.InsertableMarkdown
import com.topstack.kilonotes.base.doodle.model.markdown.MarkdownEditor
import com.topstack.kilonotes.base.doodle.model.pattern.InsertableObjectPattern
import com.topstack.kilonotes.base.doodle.model.record.InsertableRecord
import com.topstack.kilonotes.base.doodle.model.snippet.SnippetEditor
import com.topstack.kilonotes.base.doodle.model.stroke.InsertableObjectEffects
import com.topstack.kilonotes.base.doodle.model.stroke.InsertableObjectStroke
import com.topstack.kilonotes.base.doodle.model.stroke.InsertableObjectTexture
import com.topstack.kilonotes.base.doodle.model.stroke.StrokeType
import com.topstack.kilonotes.base.doodle.model.tape.InsertableTape
import com.topstack.kilonotes.base.doodle.model.text.InsertableText
import com.topstack.kilonotes.base.doodle.model.text.TextEditor
import com.topstack.kilonotes.base.doodle.utils.ThumbnailUtils.generateDrawsImage
import com.topstack.kilonotes.base.doodle.utils.ThumbnailUtils.generateObjectsThumbnailAsync
import com.topstack.kilonotes.base.doodle.utils.ThumbnailUtils.generateOcrBitmap
import com.topstack.kilonotes.base.doodle.utils.generateDefaultTextStyle
import com.topstack.kilonotes.base.doodle.views.doodleview.IInternalDoodle
import com.topstack.kilonotes.base.doodle.views.doodleview.InputMode
import com.topstack.kilonotes.base.doodle.views.doodleview.OnCallInsertDragObjectListener
import com.topstack.kilonotes.base.doodle.views.doodleview.OnCreateBitmapSuccessCallback
import com.topstack.kilonotes.base.doodle.views.doodleview.OnCreateOutlineClickListener
import com.topstack.kilonotes.base.doodle.views.doodleview.OnLassoToolTranslateClickListener
import com.topstack.kilonotes.base.doodle.views.doodleview.layer.IDoodleEditLayer
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.AddedOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.CropEndOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.DrawAllOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.GraphShapePropertyChangedOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.InsertableObjectsLayerChangeOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.MarkdownModifiedOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.RemovedOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.ReplaceOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.SelectedDrawAllOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.TextModifiedOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.TransformEndOperation
import com.topstack.kilonotes.base.doodle.views.doodleview.opereation.UnSelectedDrawAllOperation
import com.topstack.kilonotes.base.doodle.views.eraseview.IEraseBehavior
import com.topstack.kilonotes.base.doodle.views.eraseview.handler.SupportEraseType
import com.topstack.kilonotes.base.doodle.views.graphshape.GraphShapeToolType
import com.topstack.kilonotes.base.doodle.views.graphview.GraphViewTwoDrag
import com.topstack.kilonotes.base.doodle.views.lassoview.ILassoAiSolveProblemsListener
import com.topstack.kilonotes.base.doodle.views.lassoview.ILassoCreatedListener
import com.topstack.kilonotes.base.doodle.views.lassoview.ILassoToolClickListener
import com.topstack.kilonotes.base.doodle.views.lassoview.LassoToolType
import com.topstack.kilonotes.base.doodle.views.lassoview.OnGetLassoSelectedObjectsBitmapListener
import com.topstack.kilonotes.base.doodle.views.multiselectview.OnMultiSelectionOperationPerformListener
import com.topstack.kilonotes.base.doodle.views.multiselectview.Selection
import com.topstack.kilonotes.base.doodle.views.selectview.ChangeType
import com.topstack.kilonotes.base.doodle.views.selectview.ILayer
import com.topstack.kilonotes.base.doodle.views.selectview.ITransformChanged
import com.topstack.kilonotes.base.doodle.views.selectview.LayerParent
import com.topstack.kilonotes.base.doodle.views.selectview.SelectViewEnum
import com.topstack.kilonotes.base.doodle.views.textborderview.OnTextObjectOperateListener
import com.topstack.kilonotes.base.doodle.views.textborderview.TextInputListener
import com.topstack.kilonotes.base.doodle.views.textborderview.TextSelectedListener
import com.topstack.kilonotes.base.doodle.views.textborderview.TextToolType
import com.topstack.kilonotes.base.doodle.views.textborderview.TextTouchListener
import com.topstack.kilonotes.base.note.model.ColorWindowItem
import com.topstack.kilonotes.base.note.usage.TemplatePageUsageHelper
import com.topstack.kilonotes.base.search.SearchManager
import com.topstack.kilonotes.base.track.event.AiEvent
import com.topstack.kilonotes.base.track.event.EditEvent.sendEditLassoToolUsage
import com.topstack.kilonotes.base.track.event.EditEvent.sendEditTextBoxSelectAll
import com.topstack.kilonotes.base.track.event.EditEvent.sendLassoBottomLayerClick
import com.topstack.kilonotes.base.track.event.EditEvent.sendLassoTopLayerClick
import com.topstack.kilonotes.base.track.event.EditEvent.sendPictureMoveBack
import com.topstack.kilonotes.base.track.event.EditEvent.sendPictureMoveFront
import com.topstack.kilonotes.base.track.event.EditEvent.sendTextBottomLayerClick
import com.topstack.kilonotes.base.track.event.EditEvent.sendTextNextLayerClick
import com.topstack.kilonotes.base.track.event.EditEvent.sendTextPrevLayerClick
import com.topstack.kilonotes.base.track.event.EditEvent.sendTextTopLayerClick
import com.topstack.kilonotes.base.track.event.GraphEvent.sendGRAPHCOPY
import com.topstack.kilonotes.base.track.event.GraphEvent.sendGRAPHCUT
import com.topstack.kilonotes.base.track.event.GraphEvent.sendGRAPHDELETE
import com.topstack.kilonotes.base.track.event.RecordEvent.sendRecordAddToPageComplete
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.infra.device.DeviceUtils.isPhoneType
import com.topstack.kilonotes.infra.path.StylusPoint
import com.topstack.kilonotes.infra.size.PtSize
import com.topstack.kilonotes.infra.size.PxSize
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.AppUtils.appContext
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.notedata.NoteRepository
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.UUID
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.min

class ModelManager(
    val context: Context,
    override val internalDoodle: IInternalDoodle,
    private val doodleEditLayer: IDoodleEditLayer,
) : IModelManager {

    companion object {
        internal const val TAG = "ModelManager"

        fun getSelectedObjectListsByPath(
            objectList: List<InsertableObject>,
            path: Path,
            lassoContent: Set<LassoType>
        ): List<InsertableObject> {
            return getSelectedObjectListsByPath(objectList, path, lassoContent, false)
        }

        fun getSelectedObjectListsByPath(
            objectList: List<InsertableObject>,
            path: Path,
            lassoContent: Set<LassoType>,
            isContainLockedObject: Boolean
        ): List<InsertableObject> {
            val result: MutableList<InsertableObject> = java.util.ArrayList(objectList.size)
            val pathRect = RectF()
            path.computeBounds(pathRect, true)
            val pathRegion = Region()
            pathRegion.setPath(
                path,
                Region(
                    pathRect.left.toInt(),
                    pathRect.top.toInt(),
                    pathRect.right.toInt(),
                    pathRect.bottom.toInt()
                )
            )
            for (insertableObject in objectList) {
                if (!insertableObject.isSelectable) {
                    continue
                }
                var canSelect = false
                for (lassoType in lassoContent) {
                    canSelect = lassoType.canLassoSelect(insertableObject)
                    if (canSelect) {
                        break
                    }
                }
                if (!canSelect) {
                    continue
                }
                if (insertableObject is InsertableBitmap) {
                    if (!isContainLockedObject) {
                        if (insertableObject.isLocked) {
                            continue
                        }
                    }
                }
                if (IntersectDetector.isObjectIntersectWithPath(
                        insertableObject,
                        path,
                        pathRect,
                        pathRegion
                    )
                ) {
                    result.add(insertableObject)
                }
            }
            return result
        }
    }

    private val insertableObjectListeners: MutableList<IInsertableObjectListener> = mutableListOf()
    override var imageModeTouch: IImageModeTouch? = null
    override var lassoModeTouch: ILassoModeTouch? = null
    override var callReplaceImageListener: ICallReplaceImageListener? = null
    override var interceptTouchEventAction: IInterceptTouchEventAction? = null
    override var verifyRecordStateAction: IVerifyRecordStateAction? = null
    override var callSwitchInputModeListener: ICallSwitchInputModeListener? = null
    override var mPickImageInsertListener: PickImageInsertListener? = null
    override var mOnRequestImagePermissionListener: OnRequestImagePermissionListener? = null
    override var mEraseBehaviorListener: IEraseBehavior? = null
    override var mGraphShapeInsertListener: GraphShapeInsertListener? = null
    override var mObjectPastedListener: IObjectPastedListener? = null
    override var mInputModelChangeListener: IInputModelChangeListener? = null
    override var mOnCallInsertDragObjectListener: OnCallInsertDragObjectListener? = null

    override var mOnLassoToolTranslateClickListener: OnLassoToolTranslateClickListener? = null
    override var mOnCreateOutlineClickListener: OnCreateOutlineClickListener? = null
    override var mIBitmapGenerateFinishListener: IBitmapGenerateFinishListener? = null
    override var mInputTextShowListener: IInputTextShowListener? = null
    override var mTextTouchListener: TextTouchListener? = null
    override var mOuterTextSelectedListener: TextSelectedListener? = null
    override var mTouchEventListener: ITouchEventListener? = null
    override var mStrokeReadyListener: IStrokeReadyListener? = null
    override var mLassoToolAiSolveProblemsClick: ILassoAiSolveProblemsListener? = null

    override val mTextInputListener: TextInputListener<InsertableText> =
        object : TextInputListener<InsertableText> {
            override fun onTextCreated(textObject: InsertableText) {
                if (!textObject.isEmpty()) {
                    addInsertableObject(textObject, false)
                    select(textObject)
                    TemplatePageUsageHelper.useTool(page, InputMode.TEXT)
                    SearchManager.updatePageContentSearchIndexAsyncIfNeeded(document, page, true)
                }
            }

            override fun onTextModified(
                contentChanged: Boolean,
                oldText: InsertableText,
                newText: InsertableText,
                fromDismiss: Boolean
            ) {
                lastModifiedTime = SystemClock.uptimeMillis()
                val insertableObject = singleActingInsertableObject
                if (contentChanged && insertableObject != null) {
                    if (insertableObject is InsertableText) {
                        if (newText.isEmpty() && fromDismiss) {
                            removeInsertableObject(insertableObject, false)
                        } else {
                            insertableObject.set(newText, false)
                            if (fromDismiss) {
                                internalDoodle.insertOperation(
                                    TextModifiedOperation(
                                        insertableObject,
                                        oldText,
                                        newText
                                    )
                                )
                            }
                            NoteRepository.runInNoteOperationScopeAsync {
                                updateDrawingElementsWithFallback(listOf(insertableObject), page)
                            }
                        }
                        SearchManager.updatePageContentSearchIndexAsyncIfNeeded(
                            document,
                            page,
                            true
                        )
                    }
                }
                if (fromDismiss) {
                    unselect()
                }
            }

            override fun onTextDismiss() {
                unselect()
                mInputTextShowListener?.onDismiss()
            }

        }

    override val markdownInputListener: TextInputListener<InsertableMarkdown> =
        object : TextInputListener<InsertableMarkdown> {
            override fun onTextCreated(textObject: InsertableMarkdown) {

            }

            override fun onTextModified(
                contentChanged: Boolean,
                oldText: InsertableMarkdown,
                newText: InsertableMarkdown,
                fromDismiss: Boolean
            ) {
                lastModifiedTime = SystemClock.uptimeMillis()
                val insertableObject = singleActingInsertableObject
                if (contentChanged && insertableObject != null) {
                    if (insertableObject is InsertableMarkdown) {
                        if (newText.isEmpty() && fromDismiss) {
                            removeInsertableObject(insertableObject, false)
                        } else {
                            insertableObject.set(newText, false)
                            if (fromDismiss) {
                                internalDoodle.insertOperation(
                                    MarkdownModifiedOperation(
                                        insertableObject,
                                        oldText,
                                        newText
                                    )
                                )
                            }
                            NoteRepository.runInNoteOperationScopeAsync {
                                updateDrawingElementsWithFallback(listOf(insertableObject), page)
                            }
                        }
                        SearchManager.updatePageContentSearchIndexAsyncIfNeeded(
                            document,
                            page,
                            true
                        )
                    }
                }
                if (fromDismiss) {
                    unselect()
                }
            }

            override fun onTextDismiss() {
                unselect()
            }
        }

    override val mOnTextOperateListener: OnTextObjectOperateListener =
        OnTextObjectOperateListener { toolType, textObject ->
            if (textObject.initRectF.isEmpty) {
                internalDoodle.visualManager.getVisualElement(textObject).initVisualElement()
            }
            val objects: MutableList<InsertableObject> = ArrayList()
            objects.add(textObject)
            when (toolType) {
                TextToolType.COPY -> {
                    Clipboard.copy(objects, page.initialScale)
                    doodleEditLayer.mInputTextView?.dismissTextView(false)
                }

                TextToolType.CUT -> {
                    Clipboard.copy(objects, page.initialScale)
                    deleteSelectedObjects()
                    doodleEditLayer.mInputTextView?.dismissTextView(true)
                }

                TextToolType.DELETE -> {
                    deleteSelectedObjects()
                    doodleEditLayer.mInputTextView?.dismissTextView(true)
                }

                TextToolType.SELECT_ALL -> {
                    doodleEditLayer.mInputTextView?.selectAll()
                    sendEditTextBoxSelectAll()
                }

                TextToolType.OUTLINE_ADDITION -> {
                    mOnCreateOutlineClickListener?.onClick(
                        doodleEditLayer.mInputTextView?.getEditTextView()?.text?.toString()
                            .orEmpty()
                    )
                    doodleEditLayer.mInputTextView?.dismissTextView(false)
                }

                TextToolType.NEXT_LAYER -> {
                    val insertableObject = singleActingInsertableObject
                    if (insertableObject is InsertableText) {
                        onNextLayerClick(insertableObject, true)
                        sendTextNextLayerClick()
                    }
                    doodleEditLayer.mInputTextView?.dismissTextView(false)
                }

                TextToolType.PREV_LAYER -> {
                    val insertableObject = singleActingInsertableObject
                    if (insertableObject is InsertableText) {
                        onPrevLayerClick(insertableObject, true)
                        sendTextPrevLayerClick()
                    }
                    doodleEditLayer.mInputTextView?.dismissTextView(false)
                }

                TextToolType.TOP_LAYER -> {
                    val insertableObject = singleActingInsertableObject
                    if (insertableObject is InsertableText) {
                        onTopLayerClick(insertableObject, true)
                        sendTextTopLayerClick()
                    }
                    doodleEditLayer.mInputTextView?.dismissTextView(false)
                }

                TextToolType.BOTTOM_LAYER -> {
                    val insertableObject = singleActingInsertableObject
                    if (insertableObject is InsertableText) {
                        onBottomLayerClick(insertableObject, true)
                        sendTextBottomLayerClick()
                    }
                    doodleEditLayer.mInputTextView?.dismissTextView(false)
                }

                TextToolType.EDIT -> {
                    // 文本没有编辑选项
                }
            }
        }

    override val onMarkdownOperateListener: OnTextObjectOperateListener =
        OnTextObjectOperateListener { toolType, markdownObject ->
            if (markdownObject.initRectF.isEmpty) {
                internalDoodle.visualManager.getVisualElement(markdownObject).initVisualElement()
            }
            val objects: MutableList<InsertableObject> = ArrayList()
            objects.add(markdownObject)
            when (toolType) {
                TextToolType.COPY -> {
                    Clipboard.copy(objects, page.initialScale)
                    doodleEditLayer.markdownEditView?.dismissTextView(false)
                }

                TextToolType.CUT -> {
                    Clipboard.copy(objects, page.initialScale)
                    deleteSelectedObjects()
                    doodleEditLayer.markdownEditView?.dismissTextView(true)
                }

                TextToolType.DELETE -> {
                    deleteSelectedObjects()
                    doodleEditLayer.markdownEditView?.dismissTextView(true)
                }

                TextToolType.SELECT_ALL -> {
                    doodleEditLayer.markdownEditView?.selectAll()
                }

                TextToolType.OUTLINE_ADDITION -> {
                    mOnCreateOutlineClickListener?.onClick(
                        doodleEditLayer.markdownEditView?.getEditTextView()?.text?.toString()
                            .orEmpty()
                    )
                    doodleEditLayer.markdownEditView?.dismissTextView(false)
                }

                TextToolType.NEXT_LAYER -> {
                    // TODO markdown 元素层级调整
                }

                TextToolType.PREV_LAYER -> {
                    // TODO markdown 元素层级调整
                }

                TextToolType.TOP_LAYER -> {
                    // TODO markdown 元素层级调整
                }

                TextToolType.BOTTOM_LAYER -> {
                    // TODO markdown 元素层级调整
                }

                TextToolType.EDIT -> {
                    doodleEditLayer.markdownEditView?.enterEditMode()
                }
            }
        }

    override val mTransformChanged: ITransformChanged = object : ITransformChanged {
        private var mRecordMatrix: MutableList<Matrix>? = null
        private var mRecordFillingColor: MutableList<Int>? = null
        private var lastInsertableGraph: InsertableGraph? = null
        private var mRecordPoints: MutableList<List<StylusPoint>>? = null

        override fun onScaled(matrix: Matrix) {
            lastModifiedTime = SystemClock.uptimeMillis()
            actingInsertableObjects.forEach {
                it.applyScale(matrix)
            }
        }

        override fun onTranslate(matrix: Matrix) {
            lastModifiedTime = SystemClock.uptimeMillis()
            actingInsertableObjects.forEach {
                it.applyTranslate(matrix)
            }
        }

        override fun onRotate(matrix: Matrix) {
            lastModifiedTime = SystemClock.uptimeMillis()
            actingInsertableObjects.forEach {
                it.applyRotate(matrix)
            }
        }

        override fun onAction(actionType: SelectViewEnum.ActionType, layer: ILayer) {
            if (actingInsertableObjects.isEmpty()) return
            val currentInsertableObject = singleActingInsertableObject
            when (actionType) {
                SelectViewEnum.ActionType.begin -> {
                    if (currentInsertableObject is InsertableGraph) {
                        lastInsertableGraph = currentInsertableObject.cloneWithoutInitShape()
                        return
                    }
                    mRecordMatrix = mutableListOf()
                    mRecordPoints = mutableListOf()
                    if (mRecordFillingColor == null) {
                        mRecordFillingColor = mutableListOf()
                    }
                    for (insertableObject in actingInsertableObjects) {
                        mRecordMatrix?.add(Matrix(insertableObject.matrix))
                        if (insertableObject is InsertableObjectPattern) {
                            mRecordFillingColor?.add(insertableObject.mFillColor)
                        }
                        if (insertableObject is InsertableObjectStroke) {
                            mRecordPoints?.add(
                                insertableObject.points.map { point ->
                                    StylusPoint(
                                        point.x,
                                        point.y,
                                        point.pressure,
                                        point.discontinuity,
                                        point.eventTime
                                    )
                                }
                            )
                        }
                    }
                }

                SelectViewEnum.ActionType.end -> {
                    NoteRepository.runInNoteOperationScopeAsync {
                        updateDrawingElementsWithFallback(actingInsertableObjects, page)
                    }
                    if (currentInsertableObject is InsertableGraph && lastInsertableGraph != null) {
                        val operation = GraphShapePropertyChangedOperation(
                            this@ModelManager,
                            internalDoodle.visualManager,
                            lastInsertableGraph!!,
                            currentInsertableObject.cloneWithoutInitShape(),
                            currentInsertableObject
                        )
                        internalDoodle.insertOperation(operation)
                        return
                    }
                    if (actingInsertableObjects.size == mRecordMatrix?.size) {
                        if (currentInsertableObject is InsertableObjectStroke) {
                            mRecordPoints?.add(
                                currentInsertableObject.points.map { point ->
                                    StylusPoint(
                                        point.x,
                                        point.y,
                                        point.pressure,
                                        point.discontinuity,
                                        point.eventTime
                                    )
                                }
                            )
                        }
                        val operation = TransformEndOperation(
                            this@ModelManager,
                            internalDoodle.visualManager,
                            actingInsertableObjects,
                            mRecordMatrix,
                            mRecordFillingColor,
                            mRecordPoints
                        )
                        internalDoodle.insertOperation(operation)
                    }
                }
            }
        }

        override fun onAttributeChange(changeType: ChangeType, attribute: Any) {
            lastModifiedTime = SystemClock.uptimeMillis()
            when (changeType) {
                ChangeType.POINTS -> {
                    for (insertableObject in actingInsertableObjects) {
                        if (insertableObject is InsertableObjectStroke && attribute is List<*>) {
                            insertableObject.applyPointsChange(attribute as List<StylusPoint>)
                        }
                    }
                }

                ChangeType.FILL_COLOR -> {
                    val attr = attribute as ColorWindowItem
                    val color = attr.res
                    for (insertableObject in actingInsertableObjects) {
                        if (insertableObject is InsertableObjectPattern) {
                            if (mRecordFillingColor != null) {
                                mRecordFillingColor = mutableListOf()
                                mRecordFillingColor!!.add(insertableObject.mFillColor)
                            }
                            insertableObject.applyFillColorChange(color)
                        } else if (insertableObject is InsertableGraph) {
                            lastInsertableGraph = insertableObject.cloneWithoutInitShape()
                            insertableObject.fillColor = color
                            val operation = GraphShapePropertyChangedOperation(
                                this@ModelManager,
                                internalDoodle.visualManager,
                                lastInsertableGraph!!,
                                insertableObject.cloneWithoutInitShape(),
                                insertableObject
                            )
                            internalDoodle.insertOperation(operation)
                            lastInsertableGraph = insertableObject.cloneWithoutInitShape()
                        }
                    }
                }

                ChangeType.STORK_COLOR -> {
                    val attr = attribute as ColorWindowItem
                    val color = attr.res
                    for (insertableObject in actingInsertableObjects) {
                        if (insertableObject is InsertableObjectStroke && insertableObject !is InsertableObjectEffects) {
                            insertableObject.applyStrokeColorChange(color)
                        }
                        if (insertableObject is InsertableObjectPattern) {
                            if (insertableObject.mFillColor != Color.TRANSPARENT) {
                                insertableObject.applyFillColorChange(color)
                            }
                        }
                        if (insertableObject is InsertableText) {
                            insertableObject.applyTextColorChange(color)
                        }

                        if (insertableObject is InsertableGraph) {
                            lastInsertableGraph = insertableObject.cloneWithoutInitShape()
                            insertableObject.strokeColor = color
                            val operation = GraphShapePropertyChangedOperation(
                                this@ModelManager,
                                internalDoodle.visualManager,
                                lastInsertableGraph!!,
                                insertableObject.cloneWithoutInitShape(),
                                insertableObject
                            )
                            internalDoodle.insertOperation(operation)
                            lastInsertableGraph = insertableObject.cloneWithoutInitShape()
                        }
                    }
                }

                ChangeType.ALPHA -> {

                }
            }
            NoteRepository.runInNoteOperationScopeAsync {
                updateDrawingElementsWithFallback(actingInsertableObjects, page)
            }
        }
    }

    override val mMultiSelectionOperationPerformListener: OnMultiSelectionOperationPerformListener =
        object : OnMultiSelectionOperationPerformListener {
            private var mRecordMatrix: MutableList<Matrix>? = null
            private var mRecordFillingColor: MutableList<Int>? = null
            private var lastInsertableGraph: InsertableGraph? = null
            private var mRecordPoints: MutableList<List<StylusPoint>>? = null

            override fun onDelete(selection: Selection, layerParent: LayerParent) {
                val selectionObjects = selection.objects
                removeInsertableObject(selectionObjects, false)
                for (insertableObject in selectionObjects) {
                    checkOnDeleteOrRemove(insertableObject)
                }
                actingInsertableObjects.removeAll(selectionObjects)
                selectionObjects.clear()
            }

            override fun onScale(selection: Selection, transformation: Matrix) {
                lastModifiedTime = SystemClock.uptimeMillis()
                for (`object` in selection.objects) {
                    `object`.applyScale(transformation)
                }
            }

            override fun onRotate(selection: Selection, transformation: Matrix) {
                lastModifiedTime = SystemClock.uptimeMillis()
                for (`object` in selection.objects) {
                    `object`.applyRotate(transformation)
                }
            }

            override fun onTranslate(selection: Selection, transformation: Matrix) {
                lastModifiedTime = SystemClock.uptimeMillis()
                for (`object` in selection.objects) {
                    `object`.applyTranslate(transformation)
                }
            }

            override fun onAction(
                selection: Selection,
                type: SelectViewEnum.ActionType?,
                layer: ILayer?
            ) {
                val selectionObjects: List<InsertableObject> = selection.objects
                if (type == SelectViewEnum.ActionType.begin && selectionObjects.isNotEmpty()) {
                    if (selectionObjects.size == 1) {
                        val insertableObject = selectionObjects[0]
                        if (insertableObject is InsertableGraph) {
                            lastInsertableGraph = insertableObject.cloneWithoutInitShape()
                            return
                        }
                    }
                    mRecordMatrix = mutableListOf()
                    mRecordPoints = mutableListOf()
                    if (mRecordFillingColor == null) {
                        mRecordFillingColor = mutableListOf()
                    }
                    for (insertableObject in selectionObjects) {
                        mRecordMatrix?.add(Matrix(insertableObject.matrix))
                        if (insertableObject is InsertableObjectPattern) {
                            mRecordFillingColor!!.add(insertableObject.mFillColor)
                        }
                        if (insertableObject is InsertableObjectStroke) {
                            val list: MutableList<StylusPoint> = java.util.ArrayList()
                            for (point in insertableObject.points) {
                                val stylusPoint = StylusPoint(
                                    point.x,
                                    point.y,
                                    point.pressure,
                                    point.discontinuity,
                                    point.eventTime
                                )
                                list.add(stylusPoint)
                            }
                            mRecordPoints?.add(
                                insertableObject.points.map { point ->
                                    StylusPoint(
                                        point.x,
                                        point.y,
                                        point.pressure,
                                        point.discontinuity,
                                        point.eventTime
                                    )
                                }
                            )
                        }
                    }
                }
                if (type == SelectViewEnum.ActionType.end && selectionObjects.isNotEmpty()) {
                    if (selectionObjects.size == 1) {
                        val insertableObject = selectionObjects[0]
                        if (insertableObject is InsertableGraph && lastInsertableGraph != null) {
                            val operation = GraphShapePropertyChangedOperation(
                                this@ModelManager,
                                internalDoodle.visualManager,
                                lastInsertableGraph!!,
                                insertableObject.cloneWithoutInitShape(),
                                insertableObject
                            )
                            internalDoodle.insertOperation(operation)
                            return
                        }
                    }
                    if (mRecordMatrix != null && selectionObjects.size == mRecordMatrix!!.size) {
                        val insertableObject = selectionObjects[0]
                        if (insertableObject is InsertableObjectStroke) {
                            mRecordPoints?.add(
                                insertableObject.points.map { point ->
                                    StylusPoint(
                                        point.x,
                                        point.y,
                                        point.pressure,
                                        point.discontinuity,
                                        point.eventTime
                                    )
                                }
                            )
                        }
                        val operation = TransformEndOperation(
                            this@ModelManager,
                            internalDoodle.visualManager,
                            selectionObjects,
                            mRecordMatrix,
                            mRecordFillingColor,
                            mRecordPoints
                        )
                        internalDoodle.insertOperation(operation)
                    }
                }
            }

            override fun onAttributeChange(
                selection: Selection,
                changeType: ChangeType,
                attribute: Any
            ) {
                val selectionObjects: List<InsertableObject> = selection.objects
                lastModifiedTime = SystemClock.uptimeMillis()
                when (changeType) {
                    ChangeType.POINTS -> {
                        for (insertableObject in selectionObjects) {
                            if (insertableObject is InsertableObjectStroke && attribute is List<*>) {
                                insertableObject.applyPointsChange(attribute as List<StylusPoint>)
                            }
                        }
                    }

                    ChangeType.FILL_COLOR -> {
                        val attr = attribute as ColorWindowItem
                        val color = attr.res
                        for (insertableObject in selectionObjects) {
                            if (insertableObject is InsertableObjectPattern) {
                                if (mRecordFillingColor != null) {
                                    mRecordFillingColor = mutableListOf()
                                    mRecordFillingColor?.add(insertableObject.mFillColor)
                                }
                                insertableObject.applyFillColorChange(color)
                            } else if (insertableObject is InsertableGraph) {
                                insertableObject.fillColor = color
                                val operation = GraphShapePropertyChangedOperation(
                                    this@ModelManager,
                                    internalDoodle.visualManager,
                                    lastInsertableGraph!!,
                                    insertableObject.cloneWithoutInitShape(),
                                    insertableObject
                                )
                                internalDoodle.insertOperation(operation)
                                lastInsertableGraph = insertableObject.cloneWithoutInitShape()
                            }
                        }
                    }

                    ChangeType.STORK_COLOR -> {
                        val attr = attribute as ColorWindowItem
                        val color = attr.res
                        for (insertableObject in selectionObjects) {
                            if (insertableObject is InsertableObjectStroke && insertableObject !is InsertableObjectEffects) {
                                insertableObject.applyStrokeColorChange(color)
                            }
                            if (insertableObject is InsertableObjectPattern) {
                                if (insertableObject.mFillColor != Color.TRANSPARENT) {
                                    insertableObject.applyFillColorChange(color)
                                }
                            }
                            if (insertableObject is InsertableText) {
                                insertableObject.applyTextColorChange(color)
                            }

                            if (insertableObject is InsertableGraph) {
                                insertableObject.strokeColor = color
                                val operation = GraphShapePropertyChangedOperation(
                                    this@ModelManager,
                                    internalDoodle.visualManager,
                                    lastInsertableGraph!!,
                                    insertableObject.cloneWithoutInitShape(),
                                    insertableObject
                                )
                                internalDoodle.insertOperation(operation)
                                lastInsertableGraph = insertableObject.cloneWithoutInitShape()
                            }
                        }
                    }

                    ChangeType.ALPHA -> {

                    }
                }
            }
        }

    override val mLassoCreatedListener: ILassoCreatedListener = ILassoCreatedListener { lassoPath ->
        val selectedList = getLassoSelectedObjects(lassoPath)
        actingInsertableObjects.clear()
        if (selectedList.isNotEmpty()) {
            actingInsertableObjects.addAll(selectedList)
            val operation = SelectedDrawAllOperation(
                this,
                internalDoodle.visualManager,
                actingInsertableObjects
            )
            internalDoodle.insertOperation(operation)
            objectSelected = true
        } else {
            if (internalDoodle.doodleModeConfig.enableLassoLongPressDragToEditor) {
                ToastUtils.topCenter(
                    context,
                    R.string.lasso_toast_no_selected_contents
                )
            }
        }
        selectedList
    }

    override val mLassoToolClickListener: ILassoToolClickListener =
        ILassoToolClickListener { view, toolType ->
            when (toolType) {
                LassoToolType.CUT -> {
                    ToastUtils.windowTopCenter(
                        context as Activity,
                        R.string.cut_tips
                    )
                    copyActingObjects()
                    deleteSelectedObjects()
                    lassoModeTouch?.onQuitLasso()
                }

                LassoToolType.DELETE -> deleteSelectedObjects()
                LassoToolType.COPY -> {
                    ToastUtils.windowTopCenter(
                        context as Activity,
                        R.string.copy_tips
                    )
                    copyActingObjects()
                    unselect()
                    lassoModeTouch?.onQuitLasso()
                }

                LassoToolType.TRANSFORM -> enterSelectionMode(lassoTypes)
                LassoToolType.SCREEN_CAPTURE -> {
                    createBitmapByScreen { bitmap ->
                        mIBitmapGenerateFinishListener?.generateFinish(bitmap)
                    }
                    unselect()
                }

                LassoToolType.TRANSLATE -> {
                    createOcrBitmap { bitmap ->
                        mOnLassoToolTranslateClickListener?.onLassoTranslateClick(bitmap)
                    }
                    unselect()
                }

                LassoToolType.FETCH_TEXT -> {
                    createOcrBitmap { bitmap ->
                        mOnLassoToolTranslateClickListener?.onLassoFetchTextClick(bitmap)
                    }
                    unselect()
                }

                LassoToolType.OUTLINE_ADDITION -> {
                    createOcrBitmap { bitmap ->
                        mOnLassoToolTranslateClickListener?.onLassoOutlineAdditionCLick(bitmap)
                    }
                    unselect()
                }

                LassoToolType.TOP_LAYER -> {
                    onTopLayerClick(actingInsertableObjects, false)
                    sendLassoTopLayerClick()
                    unselect()
                }

                LassoToolType.BOTTOM_LAYER -> {
                    onBottomLayerClick(actingInsertableObjects, false)
                    sendLassoBottomLayerClick()
                    unselect()
                }

                LassoToolType.SEARCH -> {
                    createOcrBitmap { bitmap ->
                        mOnLassoToolTranslateClickListener?.onLassoSearchClick(bitmap)
                    }
                    unselect()
                }

                LassoToolType.DRAG_TO_EDITOR -> {
                    Clipboard.copy(
                        actingInsertableObjects,
                        page.initialScale,
                        Clipboard.ClipboardTarget.SECONDARY
                    )
                    doodleEditLayer.mLassoView?.startDragToEditor(view)
                }

                LassoToolType.DRAG_TO_SOLVE_PROBLEMS -> {
                    AiEvent.sendAiLassoSolveProblemClick()
                    val content = doodleEditLayer.mLassoView?.aiContent
                    if (content == null) {
                        createBitmapByScreen { bitmap ->
                            try {
                                val time = System.currentTimeMillis()
                                val dir = File(appContext.externalCacheDir, "pictures")
                                if (!dir.exists()) {
                                    dir.mkdirs()
                                }
                                val file = File(dir, "pictureFromLassoCutToAi_$time.png")
                                if (file.exists()) {
                                    file.delete()
                                }

                                val fos = FileOutputStream(file)
                                bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                                fos.flush()
                                val imageUri = FileProvider.getUriForFile(
                                    appContext,
                                    BuildConfig.APPLICATION_ID + ".provider",
                                    file
                                )
                                mLassoToolAiSolveProblemsClick?.onSolveProblems(null, imageUri)
                            } catch (e: Exception) {
                                LogHelper.i(
                                    TAG, "createBitmapByScreen error ${e.message}"
                                )
                            }
                        }
                    } else {
                        mLassoToolAiSolveProblemsClick?.onSolveProblems(content, null)
                    }
                    unselect()
                }

                else -> {}
            }
            sendEditLassoToolUsage(toolType.name)
        }

    override val mOnGetLassoSelectedObjectsBitmapListener: OnGetLassoSelectedObjectsBitmapListener =
        OnGetLassoSelectedObjectsBitmapListener { _, bitmapSender ->
            createDraftPaperSelectedObjectsBitmap(bitmapSender::sendBitmap)
        }

    override val mOnGetLassoAiBitmapListener: OnGetLassoSelectedObjectsBitmapListener =
        OnGetLassoSelectedObjectsBitmapListener { _, bitmapSender ->
            createAiBitmap(bitmapSender::sendBitmap)
        }

    override var lastModifiedTime: Long = 0

    private var objectSelected = false

    override var lassoTypes: Set<LassoType> = setOf(LassoType.LassoStrokeNormal)
        set(value) {
            if (field != value) {
                field = value
                unselect()
                doodleEditLayer.mLassoView?.reset(renderMatrix)
            }
        }

    override var lassoStyle: LassoStyle = LassoStyle.FREE
        set(value) {
            if (field != value) {
                field = value
                if (inputMode == InputMode.LASSO) {
                    doodleEditLayer.mLassoView?.lassoStyle = value
                }
            }
        }

    override var supportEraseType: Set<SupportEraseType> = emptySet()

    override var currentDocument: Document? = null

    override var currentPage: Page? = null

    override val document: Document
        get() = currentDocument!!

    override val page: Page
        get() = currentPage!!

    override val currentPageScale: Float
        get() {
            return if (currentPage == null) {
                1F
            } else {
                renderMatrix.mapRadius(1f) / page.initialScale
            }
        }

    private var allInsertableObjects: MutableList<InsertableObject> = mutableListOf()

    private var actingInsertableObjects: MutableList<InsertableObject> = mutableListOf()

    override var mSelectionContent: Set<LassoType> = emptySet()

    override val singleActingInsertableObject: InsertableObject?
        get() {
            if (actingInsertableObjects.size == 1) {
                return actingInsertableObjects[0]
            }
            return null
        }

    override val renderMatrix = Matrix()
    override val inverseRenderMatrix = Matrix()

    override val insertableObjectLayerEditor = InsertableObjectLayerEditor(this)
    override val snippetEditor = SnippetEditor(internalDoodle, this, doodleEditLayer)
    override val textEditor = TextEditor(internalDoodle, this, doodleEditLayer)
    override val markdownEditor = MarkdownEditor(internalDoodle, this, doodleEditLayer)
    override val imageEditor = ImageEditor(this, doodleEditLayer)

    override fun setDocumentPage(document: Document, page: Page) {
        currentDocument = document
        currentPage = page
        allInsertableObjects = page.draws
    }

    override fun setRenderMatrix(matrix: Matrix) {
        renderMatrix.set(matrix)
        matrix.invert(inverseRenderMatrix)
        internalDoodle.recognition.setRenderMatrix(matrix)
        if (inputMode != InputMode.IMAGE && inputMode != InputMode.GRAPH) {
            resetToolViews(false)
            doodleEditLayer.dismissSelectView()
        }
    }

    override fun addInsertableObject(insertableObject: InsertableObject, fromUndoRedo: Boolean) {
        val page = currentPage ?: return

        addInsertableObjectInternal(listOf(insertableObject), fromUndoRedo)

        if (!fromUndoRedo) {
            if (insertableObject is InsertableBitmap && inputMode == InputMode.IMAGE) {
                select(insertableObject)
                TemplatePageUsageHelper.useTool(page, InputMode.IMAGE)
            } else if (insertableObject is InsertableGraph && inputMode == InputMode.GRAPH) {
                if (inConsoleState) {
                    doodleEditLayer.mGraphShapeView?.notShowToolWindow = true
                }
                select(insertableObject)
                doodleEditLayer.mGraphShapeView?.showEditView(insertableObject)
            } else if (insertableObject is InsertableObjectPattern) {
                select(insertableObject)
                doodleEditLayer.showPatternEditView(insertableObject)
            }
        }
    }

    override fun addInsertableObject(
        insertableObjects: List<InsertableObject>,
        fromUndoRedo: Boolean
    ) {
        addInsertableObjectInternal(insertableObjects, fromUndoRedo)
    }

    private fun addInsertableObjectInternal(
        insertableObjects: List<InsertableObject>,
        fromUndoRedo: Boolean
    ) {
        unselect()

        val page = currentPage ?: return
        for (insertableObject in insertableObjects) {
            if (insertableObject is PaperAware) {
                insertableObject.onBlackPaper = page.isBlack()
            }
        }
        lastModifiedTime = SystemClock.uptimeMillis()
        if (!fromUndoRedo) {
            actingInsertableObjects.clear()
            actingInsertableObjects.addAll(insertableObjects)
        }
        allInsertableObjects.addAll(insertableObjects)

        val operation = AddedOperation(internalDoodle.visualManager, insertableObjects)
        operation.isCreatingCommand = !fromUndoRedo
        if (insertableObjects.firstOrNull() is InsertableTop) {
            internalDoodle.insertThumbnailOperation(operation)
        } else {
            internalDoodle.insertOperation(operation)
        }

        NoteRepository.runInNoteOperationScopeAsync {
            saveDrawingElementsAdditionOnPageWithFallback(page, insertableObjects)
        }

        for (listener in insertableObjectListeners) {
            listener.onAdded(insertableObjects, fromUndoRedo)
        }
    }

    override fun select(insertableObject: InsertableObject) {
        unselect()

        actingInsertableObjects.clear()
        actingInsertableObjects.add(insertableObject)

        var showSelectView = false
        if (insertableObject is InsertableBitmap) {
            showSelectView = true
            mSelectionContent = setOf(LassoType.LassoImage)
            imageModeTouch?.onImageSelect(insertableObject)
        }

        onSelectObjects(actingInsertableObjects, showSelectView)
    }

    override fun unselect() {
        if (!objectSelected || actingInsertableObjects.isEmpty()) return

        val operation = UnSelectedDrawAllOperation(
            this,
            internalDoodle.visualManager,
            actingInsertableObjects
        )
        internalDoodle.insertOperation(operation)

        objectSelected = false
        actingInsertableObjects.clear()
    }

    override fun removeInsertableObject(insertableObject: InsertableObject, fromUndoRedo: Boolean) {
        removeInsertableObject(listOf(insertableObject), fromUndoRedo)
    }

    override fun removeInsertableObject(
        insertableObjects: List<InsertableObject>,
        fromUndoRedo: Boolean
    ) {
        if (allInsertableObjects.removeAll(insertableObjects)) {
            lastModifiedTime = SystemClock.uptimeMillis()

            val operation = RemovedOperation(this, internalDoodle.visualManager, insertableObjects)
            operation.isCreatingCommand = !fromUndoRedo
            internalDoodle.insertOperation(operation)

            val elementsCopy = ArrayList(insertableObjects)
            NoteRepository.runInNoteOperationScopeAsync {
                saveDrawingElementsDeletionOnPageWithFallback(page, elementsCopy)
            }

            for (listener in insertableObjectListeners) {
                listener.onRemoved(insertableObjects, fromUndoRedo)
            }
        }
    }

    override fun eraserRemove(removed: List<InsertableObject>) {
        lastModifiedTime = SystemClock.uptimeMillis()
        allInsertableObjects.removeAll(removed)

        val removedCopy = ArrayList(removed)
        NoteRepository.runInNoteOperationScopeAsync {
            saveDrawingElementsDeletionOnPageWithFallback(page, removedCopy)
        }

        for (listener in insertableObjectListeners) {
            listener.onEraserRemoved(removed)
        }
    }

    override fun replaceInsertableObject(
        oldData: InsertableObject,
        newData: InsertableObject,
        fromUndoRedo: Boolean
    ) {
        lastModifiedTime = SystemClock.uptimeMillis()
        val replaceIndex: Int = allInsertableObjects.indexOf(oldData)
        if (replaceIndex == -1) {
            LogHelper.d(
                TAG,
                "replaceInsertableObject old data not exits",
                IllegalStateException(),
                true
            )
            return
        }
        actingInsertableObjects.clear()
        actingInsertableObjects.add(newData)
        allInsertableObjects[replaceIndex] = newData

        NoteRepository.runInNoteOperationScopeAsync {
            saveDrawingElementsAdditionOnPageWithFallback(page, listOf(newData))
        }

        val operation = ReplaceOperation(
            this,
            internalDoodle.visualManager,
            oldData,
            newData
        )
        operation.isCreatingCommand = !fromUndoRedo
        internalDoodle.insertOperation(operation)
    }

    override fun changeInsertableObjectIndex(
        insertableObject: InsertableObject,
        toIndex: Int,
        fromUndoRedo: Boolean
    ) {
        changeInsertableObjectIndex(listOf(insertableObject), toIndex, fromUndoRedo)
    }

    override fun changeInsertableObjectIndex(
        insertableObjects: List<InsertableObject>,
        toIndex: Int,
        fromUndoRedo: Boolean
    ) {
        val objectOldPositionMap = mutableMapOf<InsertableObject, Int>()
        val objectNewPositionMap = mutableMapOf<InsertableObject, Int>()
        for (insertableObject in insertableObjects) {
            val oldIndex: Int = allInsertableObjects.indexOf(insertableObject)
            if (oldIndex != -1) {
                objectOldPositionMap[insertableObject] = oldIndex
                objectNewPositionMap[insertableObject] = toIndex
            }
        }
        changeInsertableObjectIndex(objectOldPositionMap, objectNewPositionMap, fromUndoRedo)
    }

    override fun changeInsertableObjectIndex(
        oldObjectPositionMap: Map<InsertableObject, Int>,
        newObjectPositionMap: Map<InsertableObject, Int>,
        fromUndoRedo: Boolean
    ) {
        for ((insertableObject) in oldObjectPositionMap) {
            val newPosition = newObjectPositionMap[insertableObject]
                ?: throw IllegalStateException("map values is different")
            if (allInsertableObjects.remove(insertableObject)) {
                allInsertableObjects.add(newPosition, insertableObject)
            }
        }

        val operation = InsertableObjectsLayerChangeOperation(
            this,
            internalDoodle.visualManager,
            oldObjectPositionMap,
            newObjectPositionMap,
            fromUndoRedo
        )
        operation.isCreatingCommand = !fromUndoRedo
        internalDoodle.insertOperation(operation)

        for (listener in insertableObjectListeners) {
            listener.onIndexChange(oldObjectPositionMap, newObjectPositionMap, fromUndoRedo)
        }
        NoteRepository.runInNoteOperationScopeAsync {
            savePageDrawingElementsOrder(page, page.draws.map { it.elementId })
        }
    }

    override fun applyUndo(removed: List<InsertableObject>, added: List<InsertableObject>) {
        lastModifiedTime = SystemClock.uptimeMillis()
        allInsertableObjects.removeAll(removed)
        allInsertableObjects.addAll(added)
        val removedCopy = ArrayList(removed)
        val addedCopy = ArrayList(added)
        NoteRepository.runInNoteOperationScopeAsync {
            saveDrawingElementsDeletionOnPageWithFallback(page, removedCopy)
            saveDrawingElementsAdditionOnPageWithFallback(page, addedCopy)
        }
    }

    override fun checkIntersect(source: InsertableObject, target: InsertableObject): Boolean {
        if (target is InsertableTape) return false
        val sourceGraph: InsertableGraph?
        val sourcePath: Path?
        var targetPath = Path()

        val sourceRegion = Region()
        val targetRegion = Region()

        val insertObjectRectF = InsertableObject.getTransformedRectF(source)
        var isIntersect = InsertableObject.isObjectIntersect(insertObjectRectF, target)
        if (isIntersect && source is InsertableGraph) {
            sourceGraph = source
            sourcePath = sourceGraph.getTransformedStrokeFillPath()
            sourceRegion.setPath(
                sourcePath, Region(
                    Rect(
                        InsertableObject.getTransformedRectF(source).left.toInt(),
                        InsertableObject.getTransformedRectF(source).top.toInt(),
                        InsertableObject.getTransformedRectF(source).right.toInt(),
                        InsertableObject.getTransformedRectF(source).bottom.toInt()
                    )
                )
            )
            when (target) {
                is InsertableGraph -> {
                    targetPath = target.getTransformedStrokeFillPath()
                    targetRegion.setPath(
                        targetPath, Region(
                            Rect(
                                InsertableObject.getTransformedRectF(source).left.toInt(),
                                InsertableObject.getTransformedRectF(source).top.toInt(),
                                InsertableObject.getTransformedRectF(source).right.toInt(),
                                InsertableObject.getTransformedRectF(source).bottom.toInt()
                            )
                        )
                    )
                }

                is InsertableObjectStroke -> {
                    targetPath = target.fillPath
                    targetRegion.setPath(
                        targetPath, Region(
                            Rect(
                                InsertableObject.getTransformedRectF(source).left.toInt(),
                                InsertableObject.getTransformedRectF(source).top.toInt(),
                                InsertableObject.getTransformedRectF(source).right.toInt(),
                                InsertableObject.getTransformedRectF(source).bottom.toInt()
                            )
                        )
                    )
                }

                else -> {
                    targetPath.addRect(
                        InsertableObject.getTransformedRectF(target),
                        Path.Direction.CCW
                    )
                    targetRegion.setPath(
                        targetPath, Region(
                            Rect(
                                InsertableObject.getTransformedRectF(source).left.toInt(),
                                InsertableObject.getTransformedRectF(source).top.toInt(),
                                InsertableObject.getTransformedRectF(source).right.toInt(),
                                InsertableObject.getTransformedRectF(source).bottom.toInt()
                            )
                        )
                    )
                }
            }
            isIntersect = sourceRegion.op(targetRegion, Region.Op.INTERSECT) && !sourcePath.isEmpty
        }
        return isIntersect
    }

    override fun findInsertableRecord(recordObjectId: UUID): InsertableRecord? {
        for (insertableObject in allInsertableObjects) {
            if (insertableObject is InsertableRecord) {
                if (recordObjectId == insertableObject.elementId) {
                    return insertableObject
                }
            }
        }
        return null
    }

    override fun getInsertableObjects(): List<InsertableObject> {
        return allInsertableObjects
    }

    override fun getSelectedObjects(): List<InsertableObject> {
        if (objectSelected) return actingInsertableObjects
        return emptyList()
    }

    override fun clearActingInsertableObjects() {
        actingInsertableObjects.clear()
    }

    override fun doOnActingInsertableObjects(action: (InsertableObject) -> Unit) {
        actingInsertableObjects.forEach {
            action(it)
        }
    }

    override fun getTouchedObject(
        x: Float,
        y: Float,
        lassoTypes: Set<LassoType>,
        tolerance: Int
    ): InsertableObject? {
        val list = allInsertableObjects.toList()
        val touchP = FloatArray(2)
        val invertMatrix = Matrix()
        for (i in list.indices.reversed()) {
            val insertableObject = list[i]
            if (!insertableObject.isSelectable) {
                continue
            }
            var canSelect = false
            for (lassoType in lassoTypes) {
                canSelect = lassoType.canLassoSelect(insertableObject)
                if (canSelect) {
                    break
                }
            }
            if (!canSelect) {
                continue
            }
            touchP[0] = x
            touchP[1] = y
            if (insertableObject.matrix != null && !insertableObject.matrix.isIdentity) {
                insertableObject.matrix.invert(invertMatrix)
                invertMatrix.mapPoints(touchP)
            }
            if (insertableObject.type == InsertableObjectType.STROKE) {
                val pathRect = RectF()
                val objPath = (insertableObject as InsertableObjectStroke).path
                objPath.computeBounds(pathRect, false)
                val pathRegion = Region()
                pathRegion.setPath(
                    objPath,
                    Region(
                        pathRect.left.toInt(),
                        pathRect.top.toInt(),
                        pathRect.right.toInt(),
                        pathRect.bottom.toInt()
                    )
                )
                if (pathRegion.contains(touchP[0].toInt(), touchP[1].toInt())) {
                    return insertableObject
                }
            } else if (insertableObject.type == InsertableObjectType.GRAPH) {
                val insertableGraph = insertableObject as InsertableGraph
                val shape = insertableGraph.shape
                val matrix = Matrix()
                insertableGraph.shapeTransform.invert(matrix)
                matrix.mapPoints(touchP)
                if (shape.pointIsInGraph(touchP[0], touchP[1])) {
                    return insertableObject
                }
            } else if (insertableObject.type == InsertableObjectType.TEXT) {
                val transformedRect = RectF()
                insertableObject.matrix.mapRect(transformedRect, insertableObject.initRectF)
                if (transformedRect.contains(x, y)) {
                    return insertableObject
                }
            } else {
                val rectF = insertableObject.initRectF ?: continue
                rectF.inset(tolerance.toFloat(), tolerance.toFloat())
                if (rectF.contains(touchP[0], touchP[1])) {
                    return insertableObject
                }
            }
        }
        return null
    }

    override fun clear() {
        currentPage?.clearDraws()
        lastModifiedTime = SystemClock.uptimeMillis()
        internalDoodle.insertOperation(DrawAllOperation(this, internalDoodle.visualManager))
        for (listener in insertableObjectListeners) {
            listener.onClear()
        }
    }

    override fun empty(): Boolean {
        return allInsertableObjects.isEmpty()
    }

    override fun enterSelectionMode(selectionContentType: Set<LassoType>): Boolean {
        mSelectionContent = selectionContentType
        if (actingInsertableObjects.isEmpty()) return false
        actingInsertableObjects.removeIf { insertableObject ->
            !selectionContentType.any {
                it.canLassoSelect(insertableObject)
            }
        }
        onSelectObjects(actingInsertableObjects, true)
        return true
    }

    override fun resetToolViewsStylusWithFinger() {
        doodleEditLayer.resetToolViewsStylusWithFinger()
    }

    override fun exitSelectionMode() {
        if (doodleEditLayer.mSelectView?.isSelectViewShowing() == true) {
            doodleEditLayer.dismissSelectView()
        }
        if (inputMode == InputMode.SNIPPET) {
            unselect()
        }
    }

    private fun onSelectObjects(
        insertableObjects: List<InsertableObject>,
        showSelectView: Boolean
    ) {
        val operation = SelectedDrawAllOperation(
            this,
            internalDoodle.visualManager,
            actingInsertableObjects
        )
        internalDoodle.insertOperation(operation)
        objectSelected = true
        if (showSelectView) {
            doodleEditLayer.showSelectView(insertableObjects)
        }
    }

    override fun interceptTouchEvent(event: MotionEvent, globalRect: Rect): Boolean {
        return (doodleEditLayer.isMultiSelectViewShowing
                && doodleEditLayer.mMultiSelectView?.isHitInMultiSelectView(event) == true)
                || doodleEditLayer.mSelectView?.isHitInSelectView(event) == true
                || doodleEditLayer.mInputTextView?.handleTouchEvent(event) == true
                || (doodleEditLayer.mGraphShapeView?.isEditViewShowing() == true
                && doodleEditLayer.mGraphShapeView?.isHitInView(event) == true)
    }

    override fun exitCommandMode() {
        unselect()
        doodleEditLayer.mInputTextView?.dismissTextView(false)
        doodleEditLayer.mInputTextView?.dismissToolWindow()
        doodleEditLayer.mGraphShapeView?.dismissEditView()
        doodleEditLayer.dismissMultiSelectView()
        doodleEditLayer.dismissSelectView()
        doodleEditLayer.mDrawerView?.clearCopiedObjectsRect()
    }

    override var inConsoleState = false

    override fun onConsoleStateChanged(inConsoleState: Boolean) {
        this.inConsoleState = inConsoleState
        doodleEditLayer.mInputTextView?.let {
            it.notShowToolWindow = inConsoleState
            it.editable = !inConsoleState
        }
        doodleEditLayer.mSelectView?.setNotShowToolWindow(inConsoleState)
    }

    override fun handleNoneMode(event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                val strokeType = when (inputMode) {
                    InputMode.DRAW -> StrokeType.NORMAL
                    InputMode.ERASER -> StrokeType.ERASER
                    InputMode.HIGHLIGHTER -> StrokeType.HIGHLIGHTER
                    InputMode.GRAFFITI -> StrokeType.GRAFFITI
                    InputMode.OUTLINEPEN -> StrokeType.OUTLINEPEN
                    InputMode.LINEDRAW -> StrokeType.LINEDRAW
                    InputMode.PEN -> StrokeType.PEN
                    InputMode.PAINTBRUSH -> StrokeType.BRUSHPAINT
                    else -> StrokeType.NORMAL
                }
                TemplatePageUsageHelper.useTool(page, inputMode)
                val configStroke = internalDoodle.propertyConfigStrokeContainer.get(strokeType)
                val stroke = InsertableObjectStroke.create(
                    strokeType,
                    configStroke,
                    page.initialScale,
                    document.resources,
                    page
                )
                actingInsertableObjects.clear()
                actingInsertableObjects.add(stroke)
            }

            else -> {}
        }

        internalDoodle.recognition.detect(event)

        var handled = false
        val touchListener = mTouchEventListener
        if (touchListener != null) {
            for (insertableObject in actingInsertableObjects) {
                if (touchListener.onTouchEvent(event, insertableObject)) {
                    handled = true
                    break
                }
            }
        }

        when (event.actionMasked) {
            MotionEvent.ACTION_UP -> if (actingInsertableObjects.isNotEmpty()) {
                val activeObject = singleActingInsertableObject
                if (activeObject is InsertableObjectStroke) {
                    val strokeType = activeObject.strokeType
                    // 橡皮擦不参与图形识别
                    // PATTERN不参与添加，因为他在识别成功的时候就已经替换了
                    if (strokeType != StrokeType.ERASER && strokeType != StrokeType.PATTERN) {
                        addInsertableObjectInternal(listOf(activeObject), false)
                    }
                    if (strokeType == StrokeType.PATTERN) {

                    } else {
                        actingInsertableObjects.clear()
                    }
                }
            }

            MotionEvent.ACTION_CANCEL -> actingInsertableObjects.clear()
            else -> {}
        }
        return handled
    }

    override fun handleGraphShapeToolTypeClick(toolType: GraphShapeToolType) {
        when (toolType) {
            GraphShapeToolType.COPY -> {
                sendGRAPHCOPY()
                copyActingObjects()
                unselect()
                doodleEditLayer.mGraphShapeView?.dismissEditView()
            }

            GraphShapeToolType.CUT -> {
                sendGRAPHCUT()
                ToastUtils.windowTopCenter(
                    context as Activity,
                    R.string.cut_tips
                )
                copyActingObjects()
                deleteSelectedObjects()
                doodleEditLayer.mGraphShapeView?.dismissEditView()
            }

            GraphShapeToolType.DELETE -> {
                sendGRAPHDELETE()
                deleteSelectedObjects()
                doodleEditLayer.mGraphShapeView?.dismissEditView()
            }

            GraphShapeToolType.NEXT_LAYER -> {
                val insertableObject = singleActingInsertableObject
                if (insertableObject is InsertableGraph) {
                    onNextLayerClick(insertableObject, false)
                    unselect()
                    doodleEditLayer.mGraphShapeView?.dismissEditView()
                }
            }

            GraphShapeToolType.PREV_LAYER -> {
                val insertableObject = singleActingInsertableObject
                if (insertableObject is InsertableGraph) {
                    onPrevLayerClick(insertableObject, false)
                    unselect()
                    doodleEditLayer.mGraphShapeView?.dismissEditView()
                }
            }

            GraphShapeToolType.TOP_LAYER -> {
                val insertableObject = singleActingInsertableObject
                if (insertableObject is InsertableGraph) {
                    onTopLayerClick(insertableObject, false)
                    unselect()
                    doodleEditLayer.mGraphShapeView?.dismissEditView()
                }
            }

            GraphShapeToolType.BOTTOM_LAYER -> {
                val insertableObject = singleActingInsertableObject
                if (insertableObject is InsertableGraph) {
                    onBottomLayerClick(insertableObject, false)
                    unselect()
                    doodleEditLayer.mGraphShapeView?.dismissEditView()
                }
            }

            else -> {}
        }
    }

    override fun pasteClipboardObjects(
        fromClipboard: Clipboard.ClipboardTarget,
        centerX: Float,
        centerY: Float
    ): List<InsertableObject> {
        val pasteData = Clipboard.paste(
            page.initialScale,
            PtSize(page.paper.widthInPoint.toFloat()).getPxValue(),
            generateDefaultTextStyle(),
            fromClipboard
        )
        pasteObjects(Point(centerX.toInt(), centerY.toInt()), pasteData)
        return pasteData
    }

    override fun copyAndPasteObjects(
        objects: List<InsertableObject>,
        initialScale: Float,
        centerX: Float,
        centerY: Float
    ) {
        if (objects.isEmpty()) return
        val pasteData =
            Clipboard.copyAndPasteInsertableObjects(objects, initialScale, page.initialScale)
        if (pasteData.isEmpty()) return
        val unionRect = RectF()
        var type = 0
        for (insertableObject in pasteData) {
            type = type or insertableObject.type
            val rect = InsertableObject.getTransformedRectF(insertableObject)
            unionRect.union(rect)

            if (insertableObject is NeedResourceManager) {
                val oldManager = (insertableObject as NeedResourceManager).getResManager()
                if (oldManager != null) {
                    try {
                        if (insertableObject is InsertableObjectTexture) {
                            val graffitiTiles = insertableObject.graffiti
                            for (i in graffitiTiles.indices) {
                                val graffitiTile = graffitiTiles[i]
                                document.resources.storeAttachment(
                                    oldManager.openInputStream(
                                        graffitiTile.path
                                    ), ResourceManager.GRAFFITI
                                )
                            }
                        } else {
                            document.resources.storeAttachment(
                                oldManager.openInputStream(
                                    insertableObject.attachFilePath
                                )
                            )
                        }
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
        }
        val anchorArr = floatArrayOf(centerX, centerY)
        inverseRenderMatrix.mapPoints(anchorArr)

        val currentRenderRect: RectF = internalDoodle.frameTransform.getVisibleRectToActualContent()

        val targetWidth = (min(
            (anchorArr[0] - currentRenderRect.left).toDouble(),
            (currentRenderRect.right - anchorArr[0]).toDouble()
        ) * 2).toFloat()

        val targetHeight = (min(
            (anchorArr[1] - currentRenderRect.top).toDouble(),
            (currentRenderRect.bottom - anchorArr[1]).toDouble()
        ) * 2).toFloat()
        val targetRect = RectF()
        val scale = min(
            (targetHeight / unionRect.height()).toDouble(),
            (targetWidth / unionRect.width()).toDouble()
        ).toFloat()
        val width = scale * unionRect.width()
        val height = scale * unionRect.height()
        targetRect[anchorArr[0] - width / 2f, anchorArr[1] - height / 2f, anchorArr[0] + width / 2f] =
            anchorArr[1] + height / 2f

        val targetMatrix = Matrix()
        targetMatrix.setRectToRect(unionRect, targetRect, Matrix.ScaleToFit.CENTER)
        for (`object` in pasteData) {
            `object`.matrix.postConcat(targetMatrix)
        }

        unselect()
        addInsertableObject(pasteData, false)
        val types = HashSet<LassoType>()
        types.add(LassoType.LassoStrokeNormal)
        types.add(LassoType.LassoGraffiti)
        types.add(LassoType.LassoHighlighter)
        types.add(LassoType.LassoImage)
        types.add(LassoType.LassoText)
        types.add(LassoType.LassoGraph)
        enterSelectionMode(types)
    }

    override fun copyActingObjects() {
        val page = currentPage ?: return
        Clipboard.copy(actingInsertableObjects, page.initialScale)
    }

    override fun copyAllObjects() {
        val page = currentPage ?: return
        Clipboard.copy(allInsertableObjects, page.initialScale)
    }

    override fun notifyInsertableBitmapCropEnd(
        oldDate: InsertableBitmap,
        newData: InsertableBitmap
    ) {
        internalDoodle.insertOperation(
            CropEndOperation(
                this,
                internalDoodle.visualManager,
                oldDate,
                newData
            )
        )
        NoteRepository.runInNoteOperationScopeAsync {
            saveDrawingElementsAdditionOnPageWithFallback(page, listOf(newData))
        }
    }

    override fun deleteSelectedObjects() {
        if (actingInsertableObjects.isNotEmpty()) {
            removeInsertableObject(actingInsertableObjects, false)
            for (insertableObject in actingInsertableObjects) {
                checkOnDeleteOrRemove(insertableObject)
            }
        }
        objectSelected = false
        actingInsertableObjects.clear()
    }

    override fun addInsertableObjectListener(listener: IInsertableObjectListener) {
        if (insertableObjectListeners.contains(listener)) return
        insertableObjectListeners.add(listener)
    }

    override fun removeInsertableObjectListener(listener: IInsertableObjectListener) {
        insertableObjectListeners.remove(listener)
    }

    override fun clearInsertableObjectListener() {
        insertableObjectListeners.clear()
    }

    override fun redrawAll() {
        val drawAllOperation = DrawAllOperation(this, internalDoodle.visualManager)
        internalDoodle.insertOperation(drawAllOperation)
    }

    override fun select(drawPath: Path, pathBounds: RectF): List<Selection> {
        unselect()

        actingInsertableObjects.clear()
        val selections = getObjectSelectionsCircleOperation(
            allInsertableObjects,
            drawPath,
            inverseRenderMatrix,
            actingInsertableObjects
        )
        internalSelect(selections)

        return selections
    }

    override fun selectAll(): List<Selection> {
        unselect()

        actingInsertableObjects.clear()
        val selections =
            getObjectSelectionsBySelectAll(allInsertableObjects, actingInsertableObjects)
        internalSelect(selections)

        return selections
    }

    private fun internalSelect(selections: List<Selection>) {
        doodleEditLayer.dismissSelectView()
        doodleEditLayer.dismissMultiSelectView()
        doodleEditLayer.showMultiSelectView(selections)
        onSelectObjects(actingInsertableObjects, false)
    }

    override fun delete(drawPath: Path, pathBounds: RectF): List<Selection> {
        doodleEditLayer.dismissSelectView()
        val selections =
            getObjectDeletionByXOperation(allInsertableObjects, pathBounds, inverseRenderMatrix)
        val deletedObjects = selections.flatMap { it.objects }
        if (deletedObjects.isNotEmpty()) {
            removeInsertableObject(deletedObjects, false)
            for (insertableObject in deletedObjects) {
                checkOnDeleteOrRemove(insertableObject)
            }
        }
        return selections
    }

    override fun remove(drawPath: Path, pathBounds: RectF): List<Selection> {
        doodleEditLayer.dismissSelectView()
        val selections =
            getObjectDeletionByRemoveOperation(allInsertableObjects, drawPath, inverseRenderMatrix)
        val deletedObjects = selections.flatMap { it.objects }
        if (deletedObjects.isNotEmpty()) {
            removeInsertableObject(deletedObjects, false)
            for (insertableObject in deletedObjects) {
                checkOnDeleteOrRemove(insertableObject)
            }
        }
        return selections
    }

    override fun deleteAll(): List<Selection> {
        doodleEditLayer.dismissSelectView()
        val allObjects = allInsertableObjects.filterNot {
            val result =
                it.type == InsertableObjectType.SNIPPET || it.type == InsertableObjectType.RECORD
            if (!result) {
                checkOnDeleteOrRemove(it)
            }
            result
        }
        if (allObjects.isNotEmpty()) {
            removeInsertableObject(allObjects, false)
            // 返回一个非空列表表示有删除数据
            return listOf(Selection())
        }
        return emptyList()
    }

    private fun checkOnDeleteOrRemove(insertableObject: InsertableObject) {
        if (insertableObject is InsertableBitmap) {
            Preferences.pictureDeleteCount++
        }
        if (insertableObject is InsertableText) {
            SearchManager.updatePageContentSearchIndexAsyncIfNeeded(document, page, true)
        }
    }

    override fun copy(drawPath: Path, pathBounds: RectF): List<Selection> {
        doodleEditLayer.dismissSelectView()
        val objects = mutableListOf<InsertableObject>()
        val selections = getObjectSelectionsByCopyOperation(
            allInsertableObjects,
            pathBounds,
            inverseRenderMatrix,
            objects
        )
        if (selections.isNotEmpty() && objects.isNotEmpty()) {
            Clipboard.copy(objects, page.initialScale)
            doodleEditLayer.drawCopiedObjectsRect(objects, renderMatrix)
        }

        return selections
    }

    override fun paste(drawPath: Path, pathBounds: RectF): List<Selection> {
        doodleEditLayer.dismissSelectView()
        if (Clipboard.hasAvailablePaste()) {
            val pasteData: List<InsertableObject> = pasteClipboardObjects(
                Clipboard.ClipboardTarget.PRIMARY,
                pathBounds.centerX(),
                pathBounds.centerY()
            )
            if (pasteData.isNotEmpty()) {
                // 返回一个非空列表表示有粘贴数据
                val pasteSelection = Selection()
                val selections: MutableList<Selection> = ArrayList()
                selections.add(pasteSelection)
                if (doodleEditLayer.mInputTextView != null) {
                    doodleEditLayer.mInputTextView!!.notShowToolWindow = true
                    doodleEditLayer.mInputTextView!!.editable = false
                }
                if (doodleEditLayer.mSelectView != null) {
                    doodleEditLayer.mSelectView!!.setNotShowToolWindow(true)
                }
                return selections
            }
        }
        return ArrayList()
    }

    override fun pasteObjects(anchor: Point, pasteData: List<InsertableObject>) {
        if (pasteData.isEmpty()) return
        val unionRect = RectF()
        for (insertableObject in pasteData) {
            if (insertableObject is InsertableMarkdown && insertableObject.initRectF.isEmpty) {
                // 直接从文本复制的 Markdown 对象没有大小
                val newTextView = AppCompatTextView(AppUtils.appContext)
                newTextView.layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                newTextView.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    insertableObject.textSize.getPxValue()
                )

                val pageViewPort: Offset = page.pageViewPortOffsetToPaperInPoint ?: Offset()
                val paperWidthInPoint: Int = page.paper.widthInPoint
                val paperHeightInPoint: Int = page.paper.heightInPoint
                val pageWidthInPoint = paperWidthInPoint + pageViewPort.widthDiff
                val pageHeightInPoint = paperHeightInPoint + pageViewPort.heightDiff

                newTextView.text = insertableObject.originalText
                val widthSpec = View.MeasureSpec.makeMeasureSpec(
                    (PtSize(pageWidthInPoint).getPxValue() * 2 / 3F + 0.5f).toInt(),
                    View.MeasureSpec.AT_MOST
                )
                val heightSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                newTextView.measure(widthSpec, heightSpec)
                newTextView.layout(
                    0,
                    0,
                    newTextView.measuredWidth,
                    newTextView.measuredHeight
                )
                insertableObject.borderWidth =
                    PxSize(newTextView.measuredWidth.toFloat()).toPtSize()
                insertableObject.borderMaxHeight = PtSize(pageHeightInPoint)
                insertableObject.updateVisualRect(
                    newTextView.measuredWidth.toFloat(),
                    newTextView.measuredHeight.toFloat()
                )
            }


            val rect = InsertableObject.getTransformedRectF(insertableObject)
            unionRect.union(rect)
            if (insertableObject is InsertableText) {
                SearchManager.updatePageContentSearchIndexAsyncIfNeeded(document, page, true)
            }

            if (insertableObject is NeedResourceManager) {
                val oldManager = (insertableObject as NeedResourceManager).getResManager()
                if (oldManager != null) {
                    try {
                        if (insertableObject is InsertableObjectTexture) {
                            val graffitiTiles = insertableObject.graffiti
                            for (i in graffitiTiles.indices) {
                                val graffitiTile = graffitiTiles[i]
                                document.resources.storeAttachment(
                                    oldManager.openInputStream(
                                        graffitiTile.path
                                    ), ResourceManager.GRAFFITI
                                )
                            }
                        } else {
                            document.resources.storeAttachment(
                                oldManager.openInputStream(
                                    insertableObject.attachFilePath
                                )
                            )
                        }
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
        }
        val anchorArr = floatArrayOf(anchor.x.toFloat(), anchor.y.toFloat())
        inverseRenderMatrix.mapPoints(anchorArr)
        val transX = anchorArr[0] - unionRect.centerX()
        val transY = anchorArr[1] - unionRect.centerY()
        for (insertableObject in pasteData) {
            insertableObject.matrix.postTranslate(transX, transY)
        }


        if (isPhoneType(deviceType)) {
            if (pasteData.size == 1) {
                val insertableObject = pasteData[0]
                if (insertableObject is InsertableBitmap) {
                    if (imageModeTouch != null) {
                        addInsertableObject(pasteData, false)
                        imageModeTouch?.onImageSelect(insertableObject)
                    }
                } else if (insertableObject is InsertableText) {
                    addInsertableObject(pasteData, false)
                    val insertableText = pasteData[0] as InsertableText
                    inputMode = InputMode.TEXT
                    textEditor.modifyText(insertableText)
                    mInputTextShowListener?.onShow()
                } else if (insertableObject is InsertableMarkdown) {
                    addInsertableObject(pasteData, false)
                    val insertableText = pasteData[0] as InsertableMarkdown
                    inputMode = InputMode.TEXT
                    markdownEditor.modifyMarkdown(insertableText)
                } else if (insertableObject is InsertableGraph) {
                    callSwitchInputModeListener?.switchInputModeImmediately(InputMode.GRAPH)
                    val insertableGraph = pasteData[0] as InsertableGraph
                    addInsertableObject(insertableGraph, false)
                } else {
                    if (mObjectPastedListener != null) {
                        mObjectPastedListener?.objectPasted(pasteData)
                        actingInsertableObjects.clear()
                    }
                }
            } else {
                if (mObjectPastedListener != null) {
                    mObjectPastedListener?.objectPasted(pasteData)
                    actingInsertableObjects.clear()
                }
            }
        } else {
            if (pasteData.size == 1 && pasteData[0] is InsertableText) {
                callSwitchInputModeListener?.switchInputModeImmediately(InputMode.TEXT)
                addInsertableObject(pasteData, false)
                val insertableText = pasteData[0] as InsertableText
                textEditor.modifyText(insertableText)
                mInputTextShowListener?.onShow()
            } else if (pasteData.size == 1 && pasteData[0] is InsertableMarkdown) {
                callSwitchInputModeListener?.switchInputModeImmediately(InputMode.TEXT)
                addInsertableObject(pasteData, false)
                val insertableText = pasteData[0] as InsertableMarkdown
                markdownEditor.modifyMarkdown(insertableText)
            } else if (pasteData.size == 1 && pasteData[0] is InsertableGraph) {
                callSwitchInputModeListener?.switchInputModeImmediately(InputMode.GRAPH)
                val insertableGraph = pasteData[0] as InsertableGraph
                addInsertableObject(insertableGraph, false)
            } else {
                resetToolViews(false)
                mObjectPastedListener?.objectPasted(pasteData)
                actingInsertableObjects.clear()
            }
        }
    }

    override fun resetToolViews(fromSurfaceSizeChange: Boolean) {
        if (doodleEditLayer.mGraphView != null) {
            val graphViewTwoDrag = doodleEditLayer.mGraphView as GraphViewTwoDrag
            graphViewTwoDrag.reset()
        }
        if (doodleEditLayer.mInputTextView != null && !fromSurfaceSizeChange) {
            doodleEditLayer.mInputTextView?.dismissTextView(false)
        }
        doodleEditLayer.mSelectView?.resetSelectView()
        if (doodleEditLayer.mLassoView?.isLassoViewShowing == true) {
            doodleEditLayer.mLassoView!!.reset(renderMatrix)
        }

        if (doodleEditLayer.mEraseView?.isEraseViewShowing == true) {
            doodleEditLayer.mEraseView!!.reset(renderMatrix)
        }

        if (doodleEditLayer.mGraphShapeView != null && !fromSurfaceSizeChange) {
            doodleEditLayer.mGraphShapeView!!.dismissEditView()
        }

        doodleEditLayer.mTapeView?.reset()

        if (doodleEditLayer.mRecordView != null) {
            confirmRecordAdding()
            doodleEditLayer.mRecordView!!.reset()
        }

        if (doodleEditLayer.mMultiSelectView != null) {
            doodleEditLayer.dismissSelectView()
        }

        if (!fromSurfaceSizeChange) {
            unselectSnippet()
        }
        doodleEditLayer.dismissLongPressToolsView()
    }

    private fun confirmRecordAdding() {
        val recordView = doodleEditLayer.mRecordView
        if (recordView?.isRecordViewShowing() == true && recordView.getRecordId() != null) {
            page.isEditing = true
            val insertableRecord = InsertableRecord(
                UUID.randomUUID(),
                recordView.getRecordId()!!,
                recordView.getRecordTagId()
            )
            insertableRecord.initRectF = recordView.getDrawRect()
            insertableRecord.matrix = recordView.getMatrix()
            addInsertableObject(insertableRecord, true)
            sendRecordAddToPageComplete()
        }
    }

    private fun unselectSnippet() {
        if (doodleEditLayer.mSnippetView != null) {
            doodleEditLayer.mSnippetView!!.reset(currentPageScale)
        }
        unselect()
    }

    override fun undo(step: Int) {
    }

    override fun redo(step: Int) {
    }


    /**
     * 仅文本和图片现在支持上一层下一层
     *
     * @param insertableObject
     */
    override fun onPrevLayerClick(insertableObject: InsertableObject, withBitmapOrText: Boolean) {
        val prevLayerIndex =
            insertableObjectLayerEditor.getPrevLayerIndex(insertableObject, withBitmapOrText)
        if (prevLayerIndex == -1) {
            LogHelper.d(TAG, "InsertableObject dose not have prev layer")
            unselect()
            return
        }
        sendPictureMoveFront()
        changeInsertableObjectIndex(insertableObject, prevLayerIndex, false)
    }

    /**
     * 仅文本和图片现在支持上一层下一层
     *
     * @param insertableObject
     */
    override fun onNextLayerClick(insertableObject: InsertableObject, withBitmapOrText: Boolean) {
        val nextLayerIndex: Int =
            insertableObjectLayerEditor.getNextLayerIndex(insertableObject, withBitmapOrText)
        if (nextLayerIndex == -1) {
            LogHelper.d(TAG, "InsertableObject dose not have next layer")
            unselect()
            return
        }
        sendPictureMoveBack()
        changeInsertableObjectIndex(insertableObject, nextLayerIndex, false)
    }

    override fun onTopLayerClick(insertableObject: InsertableObject, withBitmapOrText: Boolean) {
        val topIndex: Int =
            insertableObjectLayerEditor.getTopLayerIndex(insertableObject, withBitmapOrText)
        if (topIndex == -1) {
            unselect()
            return
        }
        changeInsertableObjectIndex(insertableObject, topIndex, false)
    }

    override fun onTopLayerClick(
        insertableObjects: List<InsertableObject>,
        withBitmapOrText: Boolean
    ) {
        val topIndex: Int =
            insertableObjectLayerEditor.getTopLayerIndex(insertableObjects, withBitmapOrText)
        if (topIndex == -1) {
            unselect()
            return
        }
        changeInsertableObjectIndex(insertableObjects, topIndex, false)
    }

    override fun onBottomLayerClick(insertableObject: InsertableObject, withBitmapOrText: Boolean) {
        val bottomIndex =
            insertableObjectLayerEditor.getBottomLayerIndex(insertableObject, withBitmapOrText)
        if (bottomIndex == -1) {
            unselect()
            return
        }
        changeInsertableObjectIndex(insertableObject, bottomIndex, false)
    }

    override fun onBottomLayerClick(
        insertableObjects: List<InsertableObject>,
        withBitmapOrText: Boolean
    ) {
        val bottomIndex =
            insertableObjectLayerEditor.getBottomLayerIndex(insertableObjects, withBitmapOrText)
        if (bottomIndex == -1) {
            unselect()
            return
        }
        changeInsertableObjectIndex(insertableObjects.reversed(), bottomIndex, false)
    }

    private fun getLassoSelectedObjects(lassoPath: Path): List<InsertableObject> {
        val path = Path(lassoPath)
        path.transform(inverseRenderMatrix)
        return getSelectedObjectListsByPath(allInsertableObjects, path, lassoTypes)
    }

    private fun createBitmapByScreen(onCreateBitmapSuccess: OnCreateBitmapSuccessCallback) {
        if (doodleEditLayer.isLassoViewShowing) {
            val lassoPath = doodleEditLayer.mLassoView?.lassoPath
            if (lassoPath != null) {
                val doodleViewWidth = internalDoodle.doodleWidth
                val doodleViewHeight = internalDoodle.doodleHeight
                if (doodleViewWidth < 0 || doodleViewHeight < 0) {
                    LogHelper.d(TAG, "doodleView 没有准备好，不响应裁剪")
                    return
                }
                val renderLassoPath = Path(lassoPath)
                renderLassoPath.transform(inverseRenderMatrix)
                val types = setOf(
                    LassoType.LassoStrokeNormal,
                    LassoType.LassoGraffiti,
                    LassoType.LassoHighlighter,
                    LassoType.LassoImage,
                    LassoType.LassoText,
                    LassoType.LassoSnippet,
                    LassoType.LassoGraph,
                    LassoType.LassoTape
                )
                val draws = getSelectedObjectListsByPath(page.draws, renderLassoPath, types, true)

                generateDrawsImage(
                    document, page, draws, internalDoodle.frameTransform, true
                ) { bitmap: Bitmap? ->
                    val path = Path(lassoPath)
                    val bitmapSrcRect = RectF()
                    path.computeBounds(bitmapSrcRect, true)
                    val revert = Matrix()
                    revert.postTranslate(-bitmapSrcRect.left, -bitmapSrcRect.top)
                    path.transform(revert)
                    val paint = Paint()
                    paint.isAntiAlias = true
                    paint.style = Paint.Style.FILL
                    val targetBitmap =
                        Bitmap.createBitmap(
                            ceil(bitmapSrcRect.width().toDouble())
                                .toInt(),
                            ceil(bitmapSrcRect.height().toDouble()).toInt(),
                            Bitmap.Config.ARGB_8888
                        )
                    val canvas = Canvas(targetBitmap)
                    canvas.drawPath(path, paint)
                    paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
                    canvas.drawBitmap(
                        bitmap!!,
                        Rect(
                            floor(bitmapSrcRect.left.toDouble())
                                .toInt(),
                            floor(bitmapSrcRect.top.toDouble()).toInt(),
                            ceil(bitmapSrcRect.right.toDouble()).toInt(),
                            ceil(bitmapSrcRect.bottom.toDouble()).toInt()
                        ),
                        Rect(
                            0,
                            0,
                            targetBitmap.width,
                            targetBitmap.height
                        ),
                        paint
                    )
                    onCreateBitmapSuccess.onCreateSuccess(targetBitmap)
                }
            }
        }
    }

    private fun createOcrBitmap(onCreateBitmapSuccess: OnCreateBitmapSuccessCallback) {
        if (doodleEditLayer.isLassoViewShowing) {
            val lassoPath = doodleEditLayer.mLassoView?.lassoPath
            if (lassoPath != null) {
                val lassoPathInPdf = Path(lassoPath)
                lassoPathInPdf.transform(internalDoodle.doodleTouchLayer.touchMatrix)
                generateOcrBitmap(
                    document,
                    page,
                    page.initialPageWidthInPixel,
                    page.initialPageHeightInPixel,
                    lassoPathInPdf
                ) { bitmap: Bitmap ->
                    onCreateBitmapSuccess.onCreateSuccess(
                        bitmap
                    )
                }
            }
        }
    }

    private fun createDraftPaperSelectedObjectsBitmap(onCreateBitmapSuccess: OnCreateBitmapSuccessCallback) {
        if (doodleEditLayer.isLassoViewShowing) {
            val lassoPath = doodleEditLayer.mLassoView?.lassoPath
            if (lassoPath != null) {
                val doodleViewWidth = internalDoodle.doodleWidth
                val doodleViewHeight = internalDoodle.doodleHeight
                if (doodleViewWidth < 0 || doodleViewHeight < 0) {
                    LogHelper.d(TAG, "doodleView 没有准备好，不响应裁剪")
                    return
                }
                val renderLassoPath = Path(lassoPath)
                renderLassoPath.transform(inverseRenderMatrix)
                val draws = actingInsertableObjects.toList()
                val screenDimensions = DimensionUtil.getScreenDimensions(context)
                val scale = min(
                    1f,
                    min(
                        (screenDimensions.widthPixels * 1f / doodleViewWidth),
                        (screenDimensions.heightPixels * 1f / doodleViewHeight)
                    )
                )

                generateObjectsThumbnailAsync(
                    document,
                    page,
                    Size((doodleViewWidth * scale).toInt(), (doodleViewHeight * scale).toInt()),
                    draws
                ) { bitmap: Bitmap ->
                    onCreateBitmapSuccess.onCreateSuccess(bitmap)
                }
            }
        }
    }

    private fun createAiBitmap(onCreateBitmapSuccess: OnCreateBitmapSuccessCallback) {
        if (doodleEditLayer.isLassoViewShowing) {
            createBitmapByScreen { bitmap ->
                onCreateBitmapSuccess.onCreateSuccess(bitmap)
            }
        }
    }

    override var inputMode: InputMode = InputMode.PEN
        set(value) {
            if (field != value) {
                if (!internalDoodle.doodleModeConfig.isSupportMode(value)) return
                if (field != InputMode.UNSELECT) {
                    exitInputMode(field)
                }
                field = value
                enterInputMode(field)
            }
        }

    private fun enterInputMode(mode: InputMode) {
        doodleEditLayer.enterInputMode(inputMode)
        when (mode) {
            InputMode.DRAW -> {}
            InputMode.ERASER -> {

            }

            InputMode.LASSO -> {

            }

            InputMode.IMAGE -> {}
            InputMode.HIGHLIGHTER -> {}
            InputMode.GRAFFITI -> {}
            InputMode.OUTLINEPEN -> {}
            InputMode.LINEDRAW -> {}
            InputMode.VIEW -> {}
            InputMode.TEXT -> {
            }

            InputMode.INSTANT_ALPHA -> {}
            InputMode.MATERIAL -> {}
            InputMode.UNSELECT -> {}
            InputMode.SNIPPET -> {}
            InputMode.PRESENTATION -> {}
            InputMode.GRAPH -> {}
            InputMode.TAPE -> {}
            InputMode.PEN -> {}
            InputMode.PAINTBRUSH -> {}
        }
    }

    private fun exitInputMode(mode: InputMode) {
        unselect()
        doodleEditLayer.exitInputMode(mode)
        when (mode) {
            InputMode.DRAW -> {}
            InputMode.ERASER -> {}
            InputMode.LASSO -> {
                unselect()
            }

            InputMode.IMAGE -> {}
            InputMode.HIGHLIGHTER -> {}
            InputMode.GRAFFITI -> {}
            InputMode.OUTLINEPEN -> {}
            InputMode.LINEDRAW -> {}
            InputMode.VIEW -> {}
            InputMode.TEXT -> {
                mInputTextShowListener?.onDismiss()
            }

            InputMode.INSTANT_ALPHA -> {}
            InputMode.MATERIAL -> {}
            InputMode.UNSELECT -> {}
            InputMode.SNIPPET -> {}
            InputMode.PRESENTATION -> {}
            InputMode.GRAPH -> {}
            InputMode.TAPE -> {
                internalDoodle.doodleStickyOnTopLayer.addObjectBeforeRenderAction = null
            }

            InputMode.PEN -> {}
            InputMode.PAINTBRUSH -> {}
        }
    }
}
package com.topstack.kilonotes.base.imagemagnifier

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapRegionDecoder
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PathEffect
import android.graphics.Rect
import android.os.Build
import android.util.AttributeSet
import android.view.View
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.withSave
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.infra.util.LogHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import java.io.InputStream
import kotlin.math.min

/**
 *
 */
class ImageMagnifierView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private val logTag = "ImageMagnifierView"
    private val viewScope = MainScope()

    var shadowWidth: Float = context.resources.getDimension(R.dimen.dp_6)
    var borderWidth: Float = resources.getDimension(R.dimen.dp_6)
    var pathWidth: Float = 0f
    var pathColor: Int = Color.BLUE
    var zoom: Float = 2f

    var imageAlpha: Int = 0xFF
        set(value) {
            field = value.coerceIn(0, 0xFF)
            bitmapPaint.alpha = field
            invalidate()
        }

    var initialScale: Float = 1f
        set(value) {
            field = value
            if (pathPaint.pathEffect is DashPathEffect) {
                pathPaint.pathEffect =
                    DashPathEffect(
                        floatArrayOf(
                            KiloApp.app.resources.getDimension(R.dimen.crop_view_border_dash_length) / initialScale,
                            KiloApp.app.resources.getDimension(R.dimen.crop_view_border_dash_interval) / initialScale
                        ), 0f
                    )
            }
        }

    private var sourceBitmap: Bitmap? = null
    private var subsetBitmap: Bitmap? = null
    private var bitmapWidth = 0
    private var bitmapHeight = 0
    private var decoder: BitmapRegionDecoder? = null

    suspend fun setSourceStream(bitmapStream: InputStream) {
        bitmapWidth = 0
        bitmapHeight = 0
        decoder = null
        withContext(Dispatchers.IO) {
            try {
                decoder = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                    BitmapRegionDecoder.newInstance(bitmapStream, false)
                } else {
                    BitmapRegionDecoder.newInstance(bitmapStream)
                }
            } catch (e: IOException) {
                LogHelper.w(logTag, "BitmapRegionDecoder cannot created.", e, report2Bugly = true)
            }

            bitmapWidth = decoder?.width ?: 0
            bitmapHeight = decoder?.height ?: 0
        }
    }

    fun setSourceBitmap(bitmap: Bitmap) {
        sourceBitmap = bitmap
        bitmapWidth = bitmap.width
        bitmapHeight = bitmap.height
    }

    private var focusX: Float = 0f
    private var focusY: Float = 0f
    private var path = Path()

    private var decoding = false
    private val decodedImageRegion = Rect()
    private var focusXInDecodedRegion = 0f
    private var focusYInDecodedRegion = 0f
    fun updateFocusPointAndPath(touchX: Float, touchY: Float, path: Path) {
        val halfWidth = width / 2 / (zoom * initialScale)
        val halfHeight = height / 2 / (zoom * initialScale)
        val left = (touchX - halfWidth).toInt().coerceIn(0, bitmapWidth)
        val right = (touchX + halfWidth).toInt().coerceIn(0, bitmapWidth)
        val top = (touchY - halfHeight).toInt().coerceIn(0, bitmapHeight)
        val bottom = (touchY + halfHeight).toInt().coerceIn(0, bitmapHeight)

        viewScope.launch {
            withContext(Dispatchers.IO) {
                decodedImageRegion.set(left, top, right, bottom)
                if (!decoding) {
                    decoding = true
                    try {
                        subsetBitmap = if (sourceBitmap == null) {
                            decoder?.decodeRegion(
                                decodedImageRegion,
                                null
                            )
                        } else {
                            Bitmap.createBitmap(
                                sourceBitmap!!,
                                decodedImageRegion.left,
                                decodedImageRegion.top,
                                decodedImageRegion.width(),
                                decodedImageRegion.height()
                            )
                        }
                        if (subsetBitmap != null) {
                            <EMAIL> = path
                            focusX = touchX
                            focusY = touchY
                            focusXInDecodedRegion = focusX - left
                            focusYInDecodedRegion = focusY - top
                            postInvalidate()
                        }
                    } catch (e: IllegalArgumentException) {
                        class DecodeImageException(message: String, cause: Exception) :
                            Exception(message, cause)

                        val msg =
                            "decode image failed. region: $decodedImageRegion, focus: ($focusX, $focusY), bitmapWH: ${bitmapWidth}x$bitmapHeight, bitmapWH from decoder: ${decoder?.width}x${decoder?.height}"
                        LogHelper.e(logTag, msg, DecodeImageException(msg, e), report2Bugly = true)
                    } finally {
                        decoding = false
                    }
                }
            }
        }
    }

    private var radius = 0f
    private val imageArea = Path()

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        radius = min(w, h) / 2f - shadowWidth - borderWidth / 2f
        imageArea.reset()
        imageArea.addCircle(w / 2f, h / 2f, radius - borderWidth / 2f, Path.Direction.CW)
    }

    override fun onDraw(canvas: Canvas) {
        drawBorderAndShadow(canvas)
        drawBitmap(canvas)
        drawCrossHair(canvas)
    }

    private val borderPaint = Paint().apply {
        color = Color.WHITE
        isAntiAlias = true
        style = Paint.Style.FILL_AND_STROKE
        strokeWidth = borderWidth
        setShadowLayer(
            shadowWidth,
            0f,
            0f,
            ResourcesCompat.getColor(resources, R.color.black_15, null)
        )
    }

    private fun drawBorderAndShadow(canvas: Canvas) {
        canvas.drawCircle(width / 2f, height / 2f, radius, borderPaint)
    }


    private val bitmapPaint = Paint()
    private val bitmapMatrix = Matrix()

    private val pathPaint = Paint().apply {
        style = Paint.Style.STROKE
        isAntiAlias = true
        pathEffect =
            DashPathEffect(
                floatArrayOf(
                    KiloApp.app.resources.getDimension(R.dimen.crop_view_border_dash_length) / initialScale,
                    KiloApp.app.resources.getDimension(R.dimen.crop_view_border_dash_interval) / initialScale
                ), 0f
            )
    }

    fun setPathEffect(pathEffect: PathEffect?) {
        pathPaint.pathEffect = pathEffect
    }

    private fun drawBitmap(canvas: Canvas) {
        val bitmap = subsetBitmap
        if (bitmap != null) {
            canvas.withSave {
                clipPath(imageArea)
                val matrix = Matrix()
                matrix.postTranslate(
                    <EMAIL> / 2F - focusXInDecodedRegion,
                    <EMAIL> / 2F - focusYInDecodedRegion
                )
                matrix.postScale(zoom * initialScale, zoom * initialScale, width / 2F, height / 2F)
                concat(matrix)
                drawBitmap(bitmap, 0f, 0f, bitmapPaint)
            }
            canvas.withSave {
                clipPath(imageArea)
                translate(width / 2f, height / 2f)
                bitmapMatrix.reset()
                bitmapMatrix.postScale(zoom * initialScale, zoom * initialScale, focusX, focusY)
                bitmapMatrix.postTranslate(-focusX, -focusY)
                concat(bitmapMatrix)
                pathPaint.color = pathColor
                pathPaint.strokeWidth = pathWidth / initialScale
                canvas.drawPath(path, pathPaint)
            }
        }
    }

    private val crossHairPaint = Paint().apply {
        color = resources.getColor(R.color.white, null)
        strokeWidth = borderWidth / 2
        style = Paint.Style.STROKE
        strokeCap = Paint.Cap.ROUND
        setShadowLayer(
            strokeWidth,
            0f,
            0f,
            ResourcesCompat.getColor(resources, R.color.black_15, null)
        )
    }

    private fun drawCrossHair(canvas: Canvas) {
        canvas.withSave {
            val halfLength = radius * 0.3f
            val centerX = width / 2f
            val centerY = height / 2f
            drawLine(centerX - halfLength, centerY, centerX + halfLength, centerY, crossHairPaint)
            drawLine(centerX, centerY - halfLength, centerX, centerY + halfLength, crossHairPaint)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        viewScope.cancel("magnifier view detached")
    }
}
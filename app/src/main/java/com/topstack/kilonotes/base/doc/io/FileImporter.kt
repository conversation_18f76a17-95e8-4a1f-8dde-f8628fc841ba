package com.topstack.kilonotes.base.doc.io

import android.content.Context
import android.graphics.Color
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import androidx.annotation.AnyThread
import androidx.annotation.Keep
import androidx.core.net.toFile
import com.google.gson.reflect.TypeToken
import com.shockwave.pdfium.PdfDocument
import com.shockwave.pdfium.PdfDocument.Bookmark
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.base.backup.RestoreHandler
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.db.HandbookDatabase
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.DocumentManager
import com.topstack.kilonotes.base.doc.DocumentManager.isValidDocumentFileName
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.doodle.model.text.InsertableText
import com.topstack.kilonotes.base.fonts.FontInfo
import com.topstack.kilonotes.base.fonts.FontManager
import com.topstack.kilonotes.base.limit.CreateNoteLimit
import com.topstack.kilonotes.base.limit.HiddenNoteLimit
import com.topstack.kilonotes.base.note.snippet.SnippetManager
import com.topstack.kilonotes.base.note.snippet.SnippetType
import com.topstack.kilonotes.base.note.snippet.data.CommonSnippet
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippet
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippetTagCrossRef
import com.topstack.kilonotes.base.note.snippet.data.SnippetTag
import com.topstack.kilonotes.base.track.event.FontEvent
import com.topstack.kilonotes.base.util.PDFManager
import com.topstack.kilonotes.base.util.ZipUtil
import com.topstack.kilonotes.base.util.appreciableCopyTo
import com.topstack.kilonotes.base.util.ensureDocumentTitle
import com.topstack.kilonotes.base.util.getStringWithDefault
import com.topstack.kilonotes.infra.gson.commonGsonClient
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.infra.util.Md5Utils
import com.topstack.kilonotes.mlkit.recognition.text.model.TextRecognitionResult
import com.topstack.kilonotes.notedata.NoteRepository
import com.topstack.kilonotes.notedata.database.backup.DatabaseNoteRestore
import com.topstack.kilonotes.pad.note.NoteSnippetCreateFragment
import com.topstack.kilonotes.pad.note.outline.OutlineEntity
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.InputStreamReader
import java.util.UUID
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicInteger
import java.util.zip.ZipFile
import kotlin.coroutines.resume

object FileImporter {
    private const val TAG = "FileImporter"
    private const val COMPLETE_PROGRESS = 1F

    private val ioScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val uiHandler = Handler(Looper.getMainLooper())
    private val analyseSnippetDispatcher = Executors.newFixedThreadPool(10).asCoroutineDispatcher()
    private var runningImportJob: ImportJob? = null
    private val fontFileMd5List = mutableSetOf<String>()

    fun isImporting(): Boolean = runningImportJob != null

    val cacheZipsDir = "${AppUtils.appContext.externalCacheDir}/zips/"
        get() {
            File(field).also { dir ->
                if (!dir.exists()) {
                    dir.mkdirs()
                }
            }
            return field
        }

    /**
     * error code
     */
    const val ERROR_DEFAULT_CODE = -1
    const val ERROR_SPACE_NOT_ENOUGH = -2
    const val ERROR_UNKNOWN_ERROR = -3
    const val ERROR_FILE_EXCEPTION = -4
    const val ERROR_RESTORE_ZIP_ILLEGAL_VERSION_CODE = 201
    const val ERROR_RESTORE_ZIP_FILE_EXCEPTION = 202
    const val ERROR_RESTORE_ZIP_FILE_DAMAGE_CODE = 203
    const val ERROR_RESTORE_ZIP_FILE_CREATE_LIMIT = 204
    const val ERROR_RESTORE_ZIP_FILE_NO_FONT = 205
    const val ERROR_RESTORE_SYNC_FILE_ILLEGAL_VERSION_CODE = 301
    const val ERROR_RESTORE_SYNC_FILE_DAMAGE_CODE = 302
    const val ERROR_AUDIO_CONFIG_FILE_NOT_FOUND_CODE = 401
    const val ERROR_AUDIO_CONFIG_FILE_PARSE_CODE = 402
    private val OUTLINE_LEVEL_RESTRICTION = 2

    private val SEGMENTED_IMPORT_PAGES_COUNT = 30

    /**
     * 老版本网盘备份恢复（文件夹形式备份）
     */
    @Deprecated("新代码不要使用")
    fun importFromSyncDirectory(
        syncDir: File,
        presentDocuments: List<Document>?,
        needReplace: Boolean = false,
        syncBackupTime: Long = System.currentTimeMillis(),
    ) {
        if (!syncDir.isDirectory) {
            throw IllegalArgumentException("syncDir is not directory, path = ${syncDir.path}")
        }
        if (isImporting()) {
            return
        }
        val importStartTime = System.currentTimeMillis()

        val totalImportStage = 2
        val importJob = ImportJob().apply {
            totalStage = totalImportStage
        }
        val currentState = CurrentState()
        runningImportJob = importJob
        importJob.parseJob = ioScope.launch {
            val originFileName = syncDir.nameWithoutExtension
            try {
                importJob.importFileName = originFileName
                LogHelper.i(
                    TAG,
                    "start import task: ${runningImportJob?.importFileName}",
                    report2Bugly = true
                )

                notifyProgress(originFileName, 0f, currentState)
                notifyProgress(originFileName, importJob.currentProgress, currentState)
                // 检查协程状态
                ensureActive()
                val currentExistsDocument =
                    presentDocuments ?: NoteRepository.allGeneralDocuments

                /**
                 * phase two: parse and serialize file to Document
                 */
                val parsedDocument =
                    parseAndSaveDocument(
                        importJob,
                        syncDir,
                        currentExistsDocument,
                        needReplace,
                        currentState,
                        syncBackupTime
                    )

                /**
                 * phase three: notify success
                 */
                notifyProgress(originFileName, COMPLETE_PROGRESS, currentState)

                ensureActive()

                if (isActive) {
                    runOnUiThread {
                        LogHelper.i(
                            TAG,
                            "import task succeed: ${runningImportJob?.importFileName}",
                            report2Bugly = true
                        )
                        if (currentState.errorCode == ERROR_RESTORE_SYNC_FILE_ILLEGAL_VERSION_CODE) {
                            onImportFailedAction?.invoke(
                                importJob.importFileName,
                                ERROR_RESTORE_SYNC_FILE_ILLEGAL_VERSION_CODE,
                                emptyList(),
                                emptyList()
                            )
                        } else if (currentState.errorCode == ERROR_RESTORE_SYNC_FILE_DAMAGE_CODE) {
                            onImportFailedAction?.invoke(
                                importJob.importFileName,
                                ERROR_RESTORE_SYNC_FILE_DAMAGE_CODE,
                                emptyList(),
                                emptyList()
                            )
                        } else {
                            if (parsedDocument == null) {
                                onImportFailedAction?.invoke(
                                    importJob.importFileName,
                                    ERROR_RESTORE_SYNC_FILE_DAMAGE_CODE,
                                    emptyList(),
                                    emptyList()
                                )
                            } else {
                                val importEndTime = System.currentTimeMillis()
                                LogHelper.d(
                                    TAG,
                                    "import task succeed: ${runningImportJob?.importFileName}  total time: ${importEndTime - importStartTime}"
                                )
                                onImportSucceededAction?.invoke(listOf(parsedDocument))
                            }
                        }
                        // clear restore dir
                        RestoreHandler.clear()
                        clearCallbacks()
                        runningImportJob = null
                    }
                }

            } catch (e: Exception) {
                if (e is CancellationException) {
                    // 如果是协程取消，清理Document资源
                    importJob.deleteDocument()
                    // clear restore dir
                    RestoreHandler.clear()
                } else if (e is IOException) {
                    LogHelper.d(
                        TAG,
                        "import from sync directory io exception, reason: ${e.message}"
                    )
                    currentState.errorCode = ERROR_SPACE_NOT_ENOUGH
                }

                if (isActive) {
                    LogHelper.w(
                        TAG,
                        "import task failed: ${runningImportJob?.importFileName}",
                        throwable = e,
                        report2Bugly = true
                    )
                    runOnUiThread {
                        if (currentState.errorCode == ERROR_SPACE_NOT_ENOUGH) {
                            onImportFailedAction?.invoke(
                                originFileName,
                                ERROR_SPACE_NOT_ENOUGH,
                                emptyList(),
                                emptyList()
                            )
                        } else {
                            onImportFailedAction?.invoke(
                                originFileName,
                                ERROR_DEFAULT_CODE,
                                emptyList(),
                                emptyList()
                            )
                        }
                        clearCallbacks()
                        runningImportJob?.cancel()
                        runningImportJob = null
                    }
                }
            }
        }
    }

    fun isDocumentsZip(file: File): Boolean {
        if (!file.exists() || file.isDirectory) throw Exception("illegal zip file")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            dalvik.system.ZipPathValidator.clearCallback()
        }
        try {
            ZipFile(file).use { zipFile ->
                val entries = zipFile.entries()
                val documentInfoRegex = "^/?[^/]*/?[^/]*/info\$".toRegex()
                val documentManifestRegex = "^/?[^/]*/?[^/]*/manifest\$".toRegex()
                val ttfRegex = "^/?[^/]*/?[^/]*\\.(ttf|TTF)\$".toRegex()
                while (entries.hasMoreElements()) {
                    val element = entries.nextElement()
                    if (!element.isDirectory) {
                        if (documentInfoRegex.matches(element.name)
                            || documentManifestRegex.matches(element.name)
                        ) {
                            return true
                        } else if (ttfRegex.matches(element.name)) {
                            return false
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LogHelper.i(TAG, "isDocumentsZip Error message: ${e.message}")
        }
        return false
    }

    fun isSnippetCardZip(file: File): Boolean {
        if (!file.exists() || file.isDirectory) throw Exception("illegal zip file")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            dalvik.system.ZipPathValidator.clearCallback()
        }
        val cardSuffix = ".zdcommoncard"
        try {
            ZipFile(file).use { zipFile ->
                val entries = zipFile.entries()
                while (entries.hasMoreElements()) {
                    val element = entries.nextElement()
                    if (!element.isDirectory) {
                        if (element.name.endsWith(cardSuffix))
                            return true
                    }
                }
            }
        } catch (e: Exception) {
            LogHelper.i(TAG, "isSnippetCardZip Error message: ${e.message}")
        }
        return false
    }

    fun isFontZipByFile(file: File): Boolean {
        if (!file.exists() || file.isDirectory) return false
        var isFontZip = false
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            dalvik.system.ZipPathValidator.clearCallback()
        }
        try {
            ZipFile(file).use { zipFile ->
                val entries = zipFile.entries()
                while (entries.hasMoreElements()) {
                    val element = entries.nextElement()
                    if (!element.isDirectory) {
                        val ttfRegex = "^/?[^/]*/?[^/]*\\.(ttf|TTF)\$".toRegex()
                        if (ttfRegex.matches(element.name)) {
                            isFontZip = true
                            break
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LogHelper.i(TAG, "isFontZipByFile Error message: ${e.message}")
        }
        return isFontZip
    }

    private fun isSnippetCard(file: File): Boolean {
        if (!file.exists() || file.isDirectory) return false
        val cardSuffix = ".zdcommoncard"
        return file.name.endsWith(cardSuffix)
    }

    /**
     *  restore backup zip file
     */
    fun importFromBackupZip(
        context: Context,
        zipUri: Uri? = null,
        zipInputStream: InputStream?,
        presentDocuments: List<Document>?,
        importFileName: String = "",
        ignoreLimit: Boolean = false,
        forceImportToHiddenSpace: Boolean? = null,
        needReplace: Boolean = false,
        syncBackupTime: Long = System.currentTimeMillis(),
        decompressDirectory: File? = null,
    ) {
        if (isImporting()) {
            return
        }
        val totalImportStage = 2
        val importJob = ImportJob().apply {
            totalStage = totalImportStage
        }
        val currentState = CurrentState()
        runningImportJob = importJob
        importJob.parseJob = ioScope.launch {
            var originFileName = importFileName
            try {
                if (originFileName.isBlank() && zipUri != null) {
                    originFileName = getFileNameFromUri(context, zipUri)
                }

                importJob.importFileName = originFileName
                LogHelper.i(
                    TAG,
                    "start import task: ${runningImportJob?.importFileName}",
                    report2Bugly = true
                )

                notifyProgress(originFileName, 0f, currentState)
                notifyProgress(originFileName, importJob.currentProgress, currentState)

                // validate input stream
                if (null == zipInputStream && decompressDirectory == null) {
                    LogHelper.w(
                        TAG,
                        "import task failed: ${runningImportJob?.importFileName}, zipInputStream and decompressDirectory is null",
                        report2Bugly = true
                    )
                    runOnUiThread {
                        onImportFailedAction?.invoke(
                            originFileName,
                            ERROR_RESTORE_ZIP_FILE_EXCEPTION,
                            emptyList(),
                            emptyList()
                        )
                        clearCallbacks()
                        runningImportJob?.cancel()
                        runningImportJob = null
                    }
                    return@launch
                }

                // 检查协程状态
                ensureActive()
                val currentExistsDocument =
                    presentDocuments ?: NoteRepository.allGeneralDocuments

                /**
                 * phase one: decompression and move file to kilo file dir
                 */
                val decompressFile = if (zipInputStream != null) {
                    decompressZipFile(
                        importJob,
                        zipInputStream,
                        currentState
                    )
                } else {
                    decompressDirectory
                }

                /**
                 * phase two: parse and serialize file to Document
                 */
                val parsedDocuments = if (null == decompressFile) {
                    emptyList()
                } else {
                    parseAndDeserializeZipFile(
                        importJob,
                        decompressFile,
                        currentExistsDocument,
                        ignoreLimit,
                        forceImportToHiddenSpace,
                        currentState,
                        needReplace,
                        syncBackupTime
                    )
                }

                /**
                 * phase three: notify success
                 */
                notifyProgress(originFileName, COMPLETE_PROGRESS, currentState)

                ensureActive()

                if (isActive) {
                    runOnUiThread {
                        LogHelper.i(
                            TAG,
                            "import task succeed: ${runningImportJob?.importFileName}",
                            report2Bugly = true
                        )
                        if (currentState.errorCode == ERROR_RESTORE_ZIP_ILLEGAL_VERSION_CODE) {
                            onImportFailedAction?.invoke(
                                importJob.importFileName,
                                ERROR_RESTORE_ZIP_ILLEGAL_VERSION_CODE,
                                emptyList(),
                                parsedDocuments
                            )
                        } else if (currentState.errorCode == ERROR_RESTORE_ZIP_FILE_DAMAGE_CODE) {
                            onImportFailedAction?.invoke(
                                importJob.importFileName,
                                ERROR_RESTORE_ZIP_FILE_DAMAGE_CODE,
                                emptyList(),
                                parsedDocuments
                            )
                        } else if (currentState.errorCode == ERROR_SPACE_NOT_ENOUGH) {
                            onImportFailedAction?.invoke(
                                importJob.importFileName,
                                ERROR_SPACE_NOT_ENOUGH,
                                emptyList(),
                                emptyList()
                            )
                        } else {
                            if (importJob.errorFileNames.isNotEmpty()) {
                                onImportFailedAction?.invoke(
                                    importJob.importFileName,
                                    ERROR_RESTORE_ZIP_FILE_EXCEPTION,
                                    importJob.errorFileNames,
                                    parsedDocuments
                                )
                            } else {
                                onImportSucceededAction?.invoke(parsedDocuments)
                            }
                        }
                        // clear restore dir
                        RestoreHandler.clear()
                        clearCallbacks()
                        runningImportJob = null
                    }
                }

            } catch (e: Exception) {
                if (e is CancellationException) {
                    // 如果是协程取消，清理Document资源
                    importJob.deleteDocument()
                    // clear restore dir
                    RestoreHandler.clear()
                } else if (e is IOException) {
                    currentState.errorCode = ERROR_SPACE_NOT_ENOUGH
                }

                if (isActive) {
                    LogHelper.w(
                        TAG,
                        "import task failed: ${runningImportJob?.importFileName}",
                        throwable = e,
                        report2Bugly = true
                    )
                    runOnUiThread {
                        if (currentState.errorCode == ERROR_SPACE_NOT_ENOUGH) {
                            onImportFailedAction?.invoke(
                                originFileName,
                                ERROR_SPACE_NOT_ENOUGH,
                                emptyList(),
                                emptyList()
                            )
                        } else {
                            onImportFailedAction?.invoke(
                                originFileName,
                                ERROR_DEFAULT_CODE,
                                emptyList(),
                                emptyList()
                            )
                        }
                        clearCallbacks()
                        runningImportJob?.cancel()
                        runningImportJob = null
                    }
                }
            }
        }
    }

    fun importSnippetFromZip(
        context: Context,
        zipUri: Uri? = null,
        zipInputStream: InputStream?,
        importFileName: String = "",
    ) {
        if (isImporting()) {
            return
        }
        val totalImportStage = 3
        val importJob = ImportJob().apply {
            totalStage = totalImportStage
        }
        val currentState = CurrentState()
        runningImportJob = importJob

        importJob.parseJob = ioScope.launch {
            var originFileName = importFileName
            try {
                if (originFileName.isBlank() && zipUri != null) {
                    originFileName = getFileNameFromUri(context, zipUri)
                }

                importJob.importFileName = originFileName
                LogHelper.i(
                    TAG,
                    "start import task: ${runningImportJob?.importFileName}",
                    report2Bugly = true
                )

                notifyProgress(originFileName, 0f, currentState)
                notifyProgress(originFileName, importJob.currentProgress, currentState)

                // validate input stream
                if (null == zipInputStream) {
                    LogHelper.w(
                        TAG,
                        "import task failed: ${runningImportJob?.importFileName}, zipInputStream is null",
                        report2Bugly = true
                    )
                    runOnUiThread {
                        onImportFailedAction?.invoke(
                            originFileName,
                            ERROR_RESTORE_ZIP_FILE_EXCEPTION,
                            emptyList(),
                            emptyList()
                        )
                        clearCallbacks()
                        runningImportJob?.cancel()
                        runningImportJob = null
                    }
                    return@launch
                }

                // 检查协程状态
                ensureActive()
                importJob.moveToNextStage()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    dalvik.system.ZipPathValidator.clearCallback()
                }

                val target =
                    File(RestoreHandler.restoreDir, importJob.importFileName)
                if (target.exists()) {
                    target.deleteRecursively()
                }

                zipInputStream.appreciableCopyTo(target.outputStream()) { progress, copiedBytes, totalBytes ->
                    ensureActive()
                    importJob.setStageProgress(progress)
                    notifyProgress(
                        importJob.importFileName,
                        importJob.currentProgress,
                        currentState
                    )
                }

                if (target.exists()) {
                    importJob.moveToNextStage()
                    val zipFile = ZipFile(target)
                    val cardSuffix = ".zdcommoncard"
                    var filesCount = zipFile.size()
                    val zipEntries = zipFile.entries()
                    val parseCardJobs = mutableListOf<Deferred<Unit>>()
                    val processedCount = AtomicInteger(0)
                    val allSnippets = CopyOnWriteArrayList<NoteSnippet>()
                    val snippetTagRefs = CopyOnWriteArrayList<NoteSnippetTagCrossRef>()
                    val allSnippetTags = CopyOnWriteArrayList<SnippetTag>()
                    while (zipEntries.hasMoreElements()) {
                        val zipEntry = zipEntries.nextElement()
                        if (!zipEntry.isDirectory && zipEntry.name.endsWith(cardSuffix)) {
                            val job = async(analyseSnippetDispatcher) {
                                ensureActive()
                                val jsonContent = zipFile.getInputStream(zipEntry) // 读取文件内容
                                val commonSnippet = commonGsonClient.fromJson(
                                    InputStreamReader(jsonContent),
                                    CommonSnippet::class.java
                                )


                                ensureActive()

                                val content = commonSnippet.decryptContent()
                                val hasSameTitleAndContentSnippet =
                                    SnippetManager.hasMatchedSnippet(commonSnippet.title, content)

                                if (!hasSameTitleAndContentSnippet) {
                                    var snippetId = UUID.fromString(commonSnippet.uuid)
                                    if (SnippetManager.hasMatchedSnippet(snippetId)) {
                                        snippetId = UUID.randomUUID()
                                    }

                                    val snippet = NoteSnippet(
                                        snippetId = snippetId,
                                        snippetType = SnippetType.TEXT,
                                        color = Color.parseColor(commonSnippet.hexString),
                                        text = content,
                                        title = commonSnippet.title,
                                        textRecognitionResult = TextRecognitionResult(
                                            content,
                                            emptyList()
                                        )
                                    )

                                    allSnippets.add(snippet)
                                    val snippetTags = commonSnippet.labelNames.map { snippetTag ->
                                        LogHelper.d(
                                            NoteSnippetCreateFragment.TAG,
                                            "snippetTag = $snippetTag"
                                        )
                                        val snippetTagDB = SnippetManager.queryTagByName(snippetTag)
                                        if (snippetTagDB != null) {
                                            snippetTagDB
                                        } else {
                                            val newSnippetTag =
                                                allSnippetTags.find { it.name == snippetTag }
                                            if (newSnippetTag != null) {
                                                newSnippetTag
                                            } else {
                                                synchronized(allSnippetTags) {
                                                    allSnippetTags.find { it.name == snippetTag }
                                                        ?: SnippetTag(name = snippetTag).apply {
                                                            allSnippetTags.add(this)
                                                        }
                                                }
                                            }
                                        }
                                    }

                                    val refs = snippetTags.map { tag ->
                                        NoteSnippetTagCrossRef(
                                            snippet.snippetId,
                                            tag.tagId
                                        )
                                    }
                                    snippetTagRefs.addAll(refs)
                                }

                                importJob.setStageProgress((processedCount.incrementAndGet()) / filesCount.toFloat())
                                notifyProgress(
                                    commonSnippet.title,
                                    importJob.currentProgress,
                                    currentState
                                )
                            }

                            parseCardJobs.add(job)
                        } else {
                            filesCount--
                        }
                    }

                    parseCardJobs.awaitAll()
                    importJob.moveToNextStage()
                    val saveSnippetTagsJob = async {
                        SnippetManager.insertCommonTagsWithoutSort(allSnippetTags)
                    }
                    val saveTagsRefsJob = async {
                        SnippetManager.saveSnippetTagRefs(snippetTagRefs)
                    }
                    val saveSnippetsJob = async {
                        SnippetManager.addSnippetsIgnore(allSnippets)
                    }
                    saveSnippetTagsJob.await()
                    importJob.setStageProgress(0.33F)
                    notifyProgress(
                        "",
                        importJob.currentProgress,
                        currentState
                    )
                    saveTagsRefsJob.await()
                    importJob.setStageProgress(0.66F)
                    notifyProgress(
                        "",
                        importJob.currentProgress,
                        currentState
                    )
                    saveSnippetsJob.await()
                    importJob.setStageProgress(1F)
                    notifyProgress(
                        "",
                        importJob.currentProgress,
                        currentState
                    )
                }
                ensureActive()
                notifyProgress(originFileName, COMPLETE_PROGRESS, currentState)
                runningImportJob = null
                runOnUiThread {
                    onImportSucceededAction?.invoke(listOf())
                }
            } catch (e: Exception) {
                if (e is CancellationException) {
                    // 如果是协程取消，清理Document资源
                    importJob.deleteDocument()
                    // clear restore dir
                    RestoreHandler.clear()
                } else if (e is IOException) {
                    currentState.errorCode = ERROR_SPACE_NOT_ENOUGH
                }

                if (isActive) {
                    LogHelper.w(
                        TAG,
                        "import task failed: ${runningImportJob?.importFileName}",
                        throwable = e,
                        report2Bugly = true
                    )
                    runOnUiThread {
                        if (currentState.errorCode == ERROR_SPACE_NOT_ENOUGH) {
                            onImportFailedAction?.invoke(
                                originFileName,
                                ERROR_SPACE_NOT_ENOUGH,
                                emptyList(),
                                emptyList()
                            )
                        } else {
                            onImportFailedAction?.invoke(
                                originFileName,
                                ERROR_DEFAULT_CODE,
                                emptyList(),
                                emptyList()
                            )
                        }
                        clearCallbacks()
                        runningImportJob?.cancel()
                        runningImportJob = null
                    }
                }
            }
        }
    }

    private fun searchAllCardFile(file: File): List<File> {
        val cardFiles = mutableListOf<File>()
        if (file.isDirectory) {
            file.listFiles()?.forEach {
                if (it.isFile && isSnippetCard(it)) {
                    cardFiles.add(it)
                } else if (it.isDirectory) {
                    cardFiles.addAll(searchAllCardFile(it))
                }
            }
        } else if (file.isFile && isSnippetCard(file)) {
            cardFiles.add(file)
        }
        return cardFiles
    }

    fun importFromFontZip(targetFile: File) {
        ioScope.launch {
            initFontMD5List()
            try {
                val cacheFontsDir = File(cacheZipsDir, targetFile.nameWithoutExtension).apply {
                    if (!exists()) {
                        mkdirs()
                    }
                }
                runOnUiThread {
                    onProgressUpdatedAction?.invoke(
                        cacheFontsDir.nameWithoutExtension,
                        0F
                    )
                }
                ZipFile(targetFile).use { zipFile ->
                    val entries = zipFile.entries()
                    while (entries.hasMoreElements()) {
                        val element = entries.nextElement()
                        if (!element.isDirectory) {
                            if (element.name.endsWith(".ttf", true)) {
                                zipFile.getInputStream(element).use { inputStream ->
                                    val fontFile = File(
                                        cacheFontsDir,
                                        element.name.substringAfterLast(File.separator)
                                    )
                                    FileOutputStream(fontFile).use { outputStream ->
                                        inputStream.copyTo(outputStream)
                                    }
                                }
                            }
                        }
                    }
                }
                val ttfFiles = cacheFontsDir.listFiles()
                if (ttfFiles.isNullOrEmpty()) {
                    FontEvent.sendFontImport(FontEvent.FAIL)
                    if (targetFile.exists()) {
                        targetFile.deleteRecursively()
                    }
                    if (cacheFontsDir.exists()) {
                        cacheFontsDir.deleteRecursively()
                    }
                    runOnUiThread {
                        onImportFailedAction?.invoke(
                            targetFile.name,
                            ERROR_RESTORE_ZIP_FILE_NO_FONT,
                            emptyList(),
                            emptyList()
                        )
                    }
                    return@launch
                }
                val unImportedTtfFiles =
                    ttfFiles.filter { Md5Utils.getMd5String(it) !in fontFileMd5List }
                val md52FileMap = mutableMapOf<String, File>()
                unImportedTtfFiles.forEach {
                    md52FileMap[Md5Utils.getMd5String(it)] = it
                }
                val needImportTtfFiles = md52FileMap.values
                val needImportTtfFileSize = needImportTtfFiles.count()
                var importedTtfFile = 0
                if (needImportTtfFileSize == 0) {
                    if (targetFile.exists()) {
                        targetFile.deleteRecursively()
                    }
                    if (cacheFontsDir.exists()) {
                        cacheFontsDir.deleteRecursively()
                    }
                } else {
                    needImportTtfFiles.forEach { ttfFile ->
                        var currentFontName = ""
                        importFontFile(ttfFile.inputStream(), ttfFile.name) { fontName ->
                            currentFontName = fontName
                            runOnUiThread {
                                onProgressUpdatedAction?.invoke(
                                    fontName,
                                    importedTtfFile.toFloat() / needImportTtfFileSize
                                )
                            }
                        }
                        importedTtfFile++
                        runOnUiThread {
                            onProgressUpdatedAction?.invoke(
                                currentFontName,
                                importedTtfFile.toFloat() / needImportTtfFileSize
                            )
                        }
                        if (needImportTtfFileSize == importedTtfFile) {
                            if (targetFile.exists()) {
                                targetFile.deleteRecursively()
                            }
                        }
                    }
                }
                if (targetFile.exists()) {
                    targetFile.deleteRecursively()
                }
                if (cacheFontsDir.exists()) {
                    cacheFontsDir.deleteRecursively()
                }
                runOnUiThread {
                    onImportSucceededAction?.invoke(listOf())
                }
            } catch (exception: Exception) {
                FontEvent.sendFontImport(FontEvent.FAIL)
                if (targetFile.exists()) {
                    targetFile.deleteRecursively()
                }
                runOnUiThread {
                    onImportFailedAction?.invoke(
                        targetFile.name,
                        ERROR_RESTORE_ZIP_FILE_EXCEPTION,
                        emptyList(),
                        emptyList()
                    )
                }
            }
        }
    }

    private fun initFontMD5List() {
        File(FontManager.customFontsDir).listFiles()?.forEach { file ->
            fontFileMd5List.add(Md5Utils.getMd5String(file))
        }
    }

    private suspend fun importFontFile(
        fileStream: InputStream,
        originFileName: String,
        importFontNameCallback: (String) -> Unit,
    ) {
        val targetFile = File(FontManager.customFontsDir, originFileName)
        if (targetFile.exists()) {
            targetFile.deleteRecursively()
        }
        targetFile.createNewFile()
        fileStream.use { inputStream ->
            FileOutputStream(targetFile).use { outputStream ->
                inputStream.copyTo(outputStream)
                val fontName = FontManager.getFontNameByFile(targetFile)
                if (fontName == null) {
                    targetFile.delete()
                    FontEvent.sendFontImport(FontEvent.FAIL)
                } else {
                    importFontNameCallback.invoke(fontName)
                    val fontInfo = FontInfo(
                        fontType = InsertableText.BasicFontInfo.FONT_TYPE_CUSTOM_IMPORT,
                        name = fontName,
                        subPath = originFileName
                    )
                    HandbookDatabase.getDatabase().fontDao().insertFont(fontInfo)
                    FontEvent.sendFontImport(FontEvent.SUCCESS)
                    fontFileMd5List.add(Md5Utils.getMd5String(targetFile.inputStream()))
                }
            }
        }
    }


    /**
     * decompress and move
     */
    private suspend fun decompressZipFile(
        importJob: ImportJob,
        zipInputStream: InputStream,
        currentState: CurrentState,
        targetDir: File = RestoreHandler.restoreDir,
        zipDirName: String = importJob.importFileName.removeSuffix(".zip"),
    ): File? = withContext(Dispatchers.IO) {
        importJob.moveToNextStage()
        val target = File(targetDir, zipDirName)
        if (target.exists()) {
            target.deleteRecursively()
        }
        try {
            val unzipFile = ZipUtil.unzip(
                targetDir,
                zipDirName,
                zipInputStream
            ) { progress ->
                ensureActive()
                importJob.setStageProgress(progress)
                notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
            }
            if (unzipFile == null) {
                currentState.errorCode = ERROR_RESTORE_ZIP_FILE_EXCEPTION
            } else {
                importJob.addCacheFile(unzipFile)
            }
            unzipFile
        } catch (exception: IOException) {
            currentState.errorCode = ERROR_SPACE_NOT_ENOUGH
            null
        }
    }

    /**
     *  parse and deserialize
     *
     *  file structure overview
     *
     *  zipName-dir
     *  d|-- documentUuid
     *     d|-- attachments
     *     d|-- thumbnail
     *     d|-- pages
     *        d|-- pageUuid
     *          f|-- info
     *     f|-- info
     *  d|-- documentUuid
     *     d|-- database
     *       f|-- backup_{documentUuid}.db
     *     d|-- resources
     *       d|-- attachments
     *     f|-- info
     *  d|++ documentUuid
     *  -------N--------
     *  d|++ documentUuid
     *
     */
    private suspend fun parseAndDeserializeZipFile(
        importJob: ImportJob,
        deCompressFile: File,
        currentExistDocuments: List<Document>,
        ignoreLimit: Boolean,
        forceImportToHiddenSpace: Boolean?,
        currentState: CurrentState,
        needReplace: Boolean,
        syncBackupTime: Long = System.currentTimeMillis(),
    ): ArrayList<Document> = withContext(Dispatchers.IO) {
        importJob.moveToNextStage()
        // reset error code
        currentState.errorCode = ERROR_DEFAULT_CODE

        //  file type flag
        var isFileValidate = true
        var existDocumentFile = false
        var existExceptionFile = false

        //  file type name
        var documentFileNames = ArrayList<String>()
        var exceptionFileNames = ArrayList<String>()

        val addedDocuments = ArrayList<Document>()
        // validate file
        // validate file structure
        if (!deCompressFile.isDirectory) {
            isFileValidate = false
        }

        if (!isFileValidate) {
            runOnUiThread {
                onImportFailedAction?.invoke(
                    importJob.importFileName,
                    ERROR_RESTORE_ZIP_FILE_EXCEPTION,
                    emptyList(),
                    emptyList()
                )
                clearCallbacks()
                runningImportJob?.cancel()
                runningImportJob = null
            }
            return@withContext addedDocuments
        }


        val subFiles = deCompressFile.listFiles()
        if (subFiles == null) {
            runOnUiThread {
                onImportFailedAction?.invoke(
                    importJob.importFileName,
                    ERROR_RESTORE_ZIP_FILE_EXCEPTION,
                    emptyList(),
                    emptyList()
                )
                clearCallbacks()
                runningImportJob?.cancel()
                runningImportJob = null
            }
            return@withContext addedDocuments
        }
        val fileTotalCount = subFiles.size

        // validate Sub UUId Dir  (name match uuid naming regex)
        subFiles.forEach { checkFile ->
            if (!isValidDocumentFileName(checkFile)) {
                exceptionFileNames.add(checkFile.name)
                existExceptionFile = true
            } else {
                documentFileNames.add(checkFile.name)
                existDocumentFile = true
            }
        }

        if (existExceptionFile && exceptionFileNames.size == fileTotalCount) {
            runOnUiThread {
                onImportFailedAction?.invoke(
                    importJob.importFileName,
                    ERROR_RESTORE_ZIP_FILE_EXCEPTION,
                    exceptionFileNames,
                    emptyList()
                )
                clearCallbacks()
                runningImportJob?.cancel()
                runningImportJob = null
            }
            return@withContext addedDocuments
        }

        val importFileCount = fileTotalCount - exceptionFileNames.size
        if (!ignoreLimit && !CreateNoteLimit.isAllowToCreateNote(importFileCount)
            || (forceImportToHiddenSpace == true && !HiddenNoteLimit.isAllowToIncreaseHiddenNote(
                importFileCount
            ))
        ) {
            runOnUiThread {
                onImportFailedAction?.invoke(
                    importJob.importFileName,
                    ERROR_RESTORE_ZIP_FILE_CREATE_LIMIT,
                    emptyList(),
                    emptyList()
                )
                clearCallbacks()
                runningImportJob?.cancel()
                runningImportJob = null
            }
            return@withContext addedDocuments
        }

        // file structure is error
        var currentFileCount = 0

        // copy and deserialize kilo file
        subFiles.forEach { subFile ->
            ensureActive()
            // 数据库版本备份中也包含了一个 info 文件来提供笔记基本信息，可以用 DocumentManager.parseDocumentInfo
            val subDocument = DocumentManager.parseDocumentInfo(
                subFile,
                resourceManager = ResourceManager.from(subFile.name, deCompressFile)
            )
            // unavailable file
            if (null == subDocument) {
                importJob.setStageProgress((currentFileCount++).toFloat() / fileTotalCount.toFloat())
                notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
                if (exceptionFileNames.contains(subFile.name)) {
                    importJob.addErrorFileName(subFile.name)
                } else {
                    currentState.errorCode = ERROR_RESTORE_ZIP_FILE_DAMAGE_CODE
                }
                return@forEach
            }

            // validate document uuid and dir name
            if (subDocument.uuid.toString() != subFile.name) {
                importJob.setStageProgress((currentFileCount++).toFloat() / fileTotalCount.toFloat())
                notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
                currentState.errorCode = ERROR_RESTORE_ZIP_FILE_DAMAGE_CODE
                return@forEach
            }

            // validate file version
            if (subDocument.versionCode > Document.DOCUMENT_VERSION_CODE) {
                currentState.errorCode = ERROR_RESTORE_ZIP_ILLEGAL_VERSION_CODE
                importJob.setStageProgress((currentFileCount++).toFloat() / fileTotalCount.toFloat())
                notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
                return@forEach
            }

            // available file
            importJob.addCacheFile(subFile)

            if (subDocument.isStoredInObsoleteKiloNotesRoom()) {

                // validate identical document (document uuid)
                val updateUUIdDocument = if (needReplace) {
                    subDocument
                } else {
                    validateIdenticalDocument(subDocument, currentExistDocuments)
                }

                // parse page exception
                if (updateUUIdDocument == Document.EMPTY) {
                    currentState.errorCode = ERROR_RESTORE_ZIP_FILE_DAMAGE_CODE
                    return@forEach
                }

                // validate pages
                try {
                    DocumentManager.parsePagesInfo(subDocument)
                } catch (e: Exception) {
                    currentState.errorCode = ERROR_RESTORE_ZIP_FILE_DAMAGE_CODE
                    return@forEach
                }

                // identical document exists
                if (updateUUIdDocument.uuid != subDocument.uuid || !updateUUIdDocument.imported) {
                    updateUUIdDocument.imported = true
                    val originalDocDir = File(deCompressFile, "${subDocument.uuid}")
                    val updateDocDir = File(deCompressFile, "${updateUUIdDocument.uuid}")
                    originalDocDir.renameTo(updateDocDir)

                    updateUUIdDocument.resources = ResourceManager.from(
                        "${updateUUIdDocument.uuid}",
                        deCompressFile
                    )

                    DocumentManager.storeDocumentInfo(updateUUIdDocument)
                }

                // validate identical document name (document title) and view page index
                validateIdenticalDocumentTitleAndPageIndex(
                    updateUUIdDocument,
                    currentExistDocuments,
                    addedDocuments,
                    needReplace
                )

                // validate modified time
                validateModifiedTime(updateUUIdDocument, syncBackupTime)

                if (needReplace) {
                    val needDeleteDoc = currentExistDocuments.firstOrNull { existDoc ->
                        existDoc.uuid == updateUUIdDocument.uuid
                    }

                    if (needDeleteDoc != null) {
                        suspendCancellableCoroutine { cont ->
                            DocumentManager.waitStoreFinished(needDeleteDoc) {
                                cont.resume(Unit)
                            }
                        }

                        NoteRepository.deleteDocumentPermanently(needDeleteDoc).join()
                    }
                }

                // move updated restore file
                val dstFile =
                    File(ResourceManager.OBSOLETE_KILONOTES_ROOM, "${updateUUIdDocument.uuid}")
                if (!dstFile.exists()) {
                    dstFile.mkdirs()
                }

                // success intermediate file
                importJob.addCacheFile(dstFile)

                val currentUUIdFile = File(deCompressFile, "${updateUUIdDocument.uuid}")
                currentUUIdFile.copyRecursively(
                    dstFile,
                    onError = { _, exception ->
                        currentState.errorCode = ERROR_SPACE_NOT_ENOUGH
                        dstFile.deleteRecursively()
                        throw exception
                    }
                )

                val restoreDocument: Document? = DocumentManager.parseDocumentInfo(dstFile)
                if (restoreDocument != null) {
                    if (restoreDocument.isHid) {
                        restoreDocument.isHid = false
                    }
                    addedDocuments.add(restoreDocument)
                    DocumentManager.storeDocumentInfo(restoreDocument)
                    NoteRepository.addDocumentToCache(restoreDocument)
                }

                // clear intermediate file
                currentUUIdFile.deleteRecursively()
            } else {
                // 处理数据库格式备份
                val documentDir = File(deCompressFile, "${subDocument.uuid}")
                if (documentDir.exists()) {
                    val restoredUuid = DatabaseNoteRestore().restore(
                        documentDir,
                        replaceExisting = needReplace,
                        tempAddedDocuments = addedDocuments
                    )
                    if (restoredUuid != null) {
                        val restoredDocument =
                            NoteRepository.retrieveGeneralDocumentById(restoredUuid.toString())
                        if (restoredDocument != null) {
                            addedDocuments.add(restoredDocument)
                            NoteRepository.addDocumentToCache(restoredDocument)
                        }
                    }
                }
                documentDir.deleteRecursively()
            }
            // 检查协程状态
            ensureActive()

            importJob.setStageProgress((currentFileCount++).toFloat() / fileTotalCount.toFloat())
            notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
        }

        addedDocuments
    }

    private suspend fun parseAndSaveDocument(
        importJob: ImportJob,
        subFile: File,
        currentExistDocuments: List<Document>,
        needReplace: Boolean,
        currentState: CurrentState,
        syncBackupTime: Long = System.currentTimeMillis(),
    ): Document? =
        withContext(Dispatchers.IO) {
            val deCompressFile = subFile.parentFile!!
            ensureActive()
            val subDocument = DocumentManager.parseDocumentInfo(
                subFile,
                resourceManager = ResourceManager.from(subFile.name, deCompressFile)
            )
            // unavailable file
            if (null == subDocument) {
                currentState.errorCode = ERROR_RESTORE_SYNC_FILE_DAMAGE_CODE
                return@withContext null
            }

            // validate document uuid and dir name
            if (subDocument.uuid.toString() != subFile.name) {
                currentState.errorCode = ERROR_RESTORE_SYNC_FILE_DAMAGE_CODE
                return@withContext null
            }

            // validate file version
            if (subDocument.versionCode > Document.DOCUMENT_VERSION_CODE) {
                notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
                return@withContext null
            }

            // available file
            importJob.addCacheFile(subFile)

            val updateUUIdDocument = if (needReplace) {
                subDocument
            } else {
                validateIdenticalDocument(subDocument, currentExistDocuments)
            }
            updateUUIdDocument.isHid = subDocument.isHid
            // validate identical document (document uuid)

            // parse page exception
            if (updateUUIdDocument == Document.EMPTY) {
                currentState.errorCode = ERROR_RESTORE_SYNC_FILE_ILLEGAL_VERSION_CODE
                return@withContext null
            }

            // identical document exists
            if (updateUUIdDocument.uuid != subDocument.uuid || !updateUUIdDocument.imported) {
                updateUUIdDocument.imported = true
                val originalDocDir = File(deCompressFile, "${subDocument.uuid}")
                val updateDocDir = File(deCompressFile, "${updateUUIdDocument.uuid}")
                originalDocDir.renameTo(updateDocDir)

                updateUUIdDocument.resources = ResourceManager.from(
                    "${updateUUIdDocument.uuid}",
                    deCompressFile
                )

                DocumentManager.storeDocumentInfo(updateUUIdDocument)
            }

            // validate identical document name (document title) and view page index
            validateIdenticalDocumentTitleAndPageIndex(
                updateUUIdDocument,
                currentExistDocuments,
                emptyList(),
                needReplace
            )

            // validate modified time
            validateModifiedTime(updateUUIdDocument, syncBackupTime)
            if (needReplace) {
                val needDeleteDoc = currentExistDocuments.firstOrNull { existDoc ->
                    existDoc.uuid == updateUUIdDocument.uuid
                }

                if (needDeleteDoc != null) {
                    suspendCancellableCoroutine { cont ->
                        DocumentManager.waitStoreFinished(needDeleteDoc) {
                            cont.resume(Unit)
                        }
                    }
                    NoteRepository.deleteDocumentPermanently(needDeleteDoc).join()
                }
            }
            // move updated restore file
            val dstFile =
                File(ResourceManager.OBSOLETE_KILONOTES_ROOM, "${updateUUIdDocument.uuid}")

            if (!dstFile.exists()) {
                dstFile.mkdirs()
            }

            // success intermediate file
            importJob.addCacheFile(dstFile)

            val currentUUIdFile = File(deCompressFile, "${updateUUIdDocument.uuid}")
            currentUUIdFile.copyRecursively(
                dstFile,
                true,
                onError = { _, exception ->
                    currentState.errorCode = ERROR_SPACE_NOT_ENOUGH
                    dstFile.deleteRecursively()
                    throw exception
                }
            )

            val restoreDocument: Document? = DocumentManager.parseDocumentInfo(dstFile)
            if (restoreDocument != null) {
                DocumentManager.storeDocumentInfo(restoreDocument)
            }

            // clear intermediate file
            currentUUIdFile.deleteRecursively()
            // 检查协程状态
            ensureActive()

            notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
            restoreDocument
        }


    private fun validateIdenticalDocument(
        checkDocument: Document,
        currentExistDocuments: List<Document>,
    ): Document {
        if (checkDocument.isStoredInObsoleteKiloNotesRoom()) {
            currentExistDocuments.forEach { existDocument ->
                if (checkDocument.uuid == existDocument.uuid) {
                    //  document exist, update document uuid 、 pages uuid
                    val updateUUIdDocument = Document(checkDocument, true).apply {
                        versionCode = checkDocument.versionCode
                        versionName = checkDocument.versionName
                    }

                    try {
                        DocumentManager.parsePagesInfo(checkDocument)
                    } catch (exception: IOException) {
                        return Document.EMPTY
                    }
                    updateUUIdDocument.pages.clear()
                    updateUUIdDocument.pages.addAll(checkDocument.pages)
                    updateUUIdDocument.pageIds.clear()
                    updateUUIdDocument.pageIds.addAll(checkDocument.pageIds)

                    return updateUUIdDocument
                }
            }
        }
        return checkDocument
    }

    private fun validateIdenticalDocumentTitleAndPageIndex(
        checkDocument: Document,
        currentExistDocuments: List<Document>,
        addedDocument: List<Document>,
        needReplace: Boolean = false,
    ) {
        val checkDocumentTitle = checkDocument.title
        val currentDocument = ArrayList<Document>()
        currentDocument.addAll(currentExistDocuments)
        currentDocument.addAll(addedDocument)
        val needIgnoreDocument = if (needReplace) checkDocument else null
        val revisedTitle = ensureDocumentTitle(
            checkDocumentTitle,
            currentDocument,
            needIgnoreDocument
        )
        if (revisedTitle != checkDocumentTitle || checkDocument.viewingPageIndex != 0) {
            checkDocument.title = revisedTitle
            checkDocument.viewingPageIndex = 0
        }
    }

    private suspend fun validateModifiedTime(
        checkDocument: Document,
        syncBackupTime: Long = System.currentTimeMillis(),
    ) {
        checkDocument.specifyModifiedTime(syncBackupTime)
        DocumentManager.storeDocumentInfo(checkDocument)
    }


    /**
     * 从PDF文件创建笔记文件
     *
     * @param pdfUri pdf文件
     * @param presentDocuments 当前已存在的文档，用于文档重名检测。
     */
    @AnyThread
    fun importFromPdf(
        context: Context,
        pdfUri: Uri,
        presentDocuments: List<Document>?,
        importFileName: String = "",
        attachedAudioFile: File? = null,
        needShowResizingTip: Boolean = false,
    ) {
        if (isImporting()) {
            return
        }
        // 导入阶段：缓存原始PDF+解析页面, 保存文档现在基本不耗时
        val totalImportStage = if (attachedAudioFile == null) {
            2
        } else {
            3
        }
        val importJob = ImportJob().apply {
            totalStage = totalImportStage
        }
        val currentState = CurrentState()
        runningImportJob = importJob

        importJob.parseJob = ioScope.launch {
            var originFileName = importFileName
            try {
                if (originFileName.isBlank()) {
                    originFileName = getFileNameFromUri(context, pdfUri)
                }
                importJob.importFileName = originFileName
                LogHelper.i(
                    TAG,
                    "start import task: ${runningImportJob?.importFileName}",
                    report2Bugly = true
                )
                notifyProgress(originFileName, 0f, currentState) // 更新UI

                notifyProgress(originFileName, importJob.currentProgress, currentState)

                val pdfInputStream: InputStream? = try {
                    KiloApp.app.contentResolver.openInputStream(pdfUri)
                } catch (_: Exception) {
                    null
                }

                if (pdfInputStream == null) {
                    LogHelper.w(
                        TAG,
                        "import task failed: ${runningImportJob?.importFileName}, pdfInputStream is null",
                        report2Bugly = true
                    )
                    runOnUiThread {
                        onImportFailedAction?.invoke(
                            originFileName,
                            ERROR_DEFAULT_CODE,
                            emptyList(),
                            emptyList()
                        )
                        clearCallbacks()
                        runningImportJob?.cancel()
                        runningImportJob = null
                    }
                    return@launch
                }

                val documents = presentDocuments ?: NoteRepository.allGeneralDocuments

                // 检查协程状态
                ensureActive()

                val documentTitle =
                    ensureDocumentTitle(
                        originFileName.removeSuffix(".pdf"),
                        documents
                    )

                val kiloDocument = Document(documentTitle, true)
                importJob.importDocuments.add(kiloDocument)

                if (attachedAudioFile != null) {
                    val zipInputStream = FileInputStream(attachedAudioFile)
                    val file = decompressZipFile(
                        importJob,
                        zipInputStream,
                        currentState,
                        attachedAudioFile.parentFile!!,
                        attachedAudioFile.name + ".unzip"
                    )
                    if (file != null) {
                        addAudiosToDocument(kiloDocument, file, currentState)
                    }
                }

                if (currentState.errorCode == ERROR_DEFAULT_CODE) {
                    // 第一阶段：缓存原始PDF 开始 +++++++++++++
                    val cachedPdfUri =
                        cacheOriginalPdf(importJob, pdfInputStream, kiloDocument, currentState)
                    // 第一阶段：缓存原始PDF 完成 -------------
                    if (null != cachedPdfUri) {
                        try {
                            // 第二阶段：解析页面 开始 +++++++++++++
                            segmentedParsePdfPage(
                                importJob,
                                kiloDocument,
                                cachedPdfUri,
                                currentState
                            )
                            // 第二阶段：解析页面 完成 ----------------
                            // 保存文档 开始 +++++++++++++
                            NoteRepository.savePagesInfo(kiloDocument.pages)
                            NoteRepository.saveDocumentInfo(kiloDocument)
                            if (needShowResizingTip) {
                                UserUsageConfig.setNeedShowPageResizingTip(kiloDocument, true)
                            }
                        } catch (pdfException: PdfFileException) {
                            currentState.errorCode = ERROR_FILE_EXCEPTION
                            pdfException.printStackTrace()
                        } catch (exception: IOException) {
                            currentState.errorCode = ERROR_SPACE_NOT_ENOUGH
                        } catch (e: Exception) {
                            currentState.errorCode = ERROR_UNKNOWN_ERROR
                        }
                    }

                    ensureActive()

                    notifyProgress(originFileName, COMPLETE_PROGRESS, currentState)
                    // 保存文档 完成 -------------

                    ensureActive()
                }

                if (isActive) {
                    runOnUiThread {
                        LogHelper.i(
                            TAG,
                            "import task succeed: ${runningImportJob?.importFileName}",
                            report2Bugly = true
                        )
                        if (currentState.errorCode == ERROR_DEFAULT_CODE) {
                            onImportSucceededAction?.invoke(importJob.importDocuments)
                        } else {
                            onImportFailedAction?.invoke(
                                originFileName,
                                currentState.errorCode,
                                emptyList(),
                                emptyList()
                            )
                        }
                        clearCallbacks()
                        runningImportJob = null
                    }
                }
            } catch (e: Exception) {
                if (e is CancellationException) {
                    // 如果是协程取消，清理Document资源
                    importJob.deleteDocument()
                }

                if (isActive) {
                    LogHelper.w(
                        TAG,
                        "import task failed: ${runningImportJob?.importFileName}",
                        throwable = e,
                        report2Bugly = true
                    )
                    runOnUiThread {
                        onImportFailedAction?.invoke(
                            originFileName,
                            ERROR_DEFAULT_CODE,
                            emptyList(),
                            emptyList()
                        )
                        clearCallbacks()
                        runningImportJob?.cancel()
                        runningImportJob = null
                    }
                }
            }
        }
    }


    /**
     * handle origin pdf file -> move file to kilo destination
     */
    private suspend fun cacheOriginalPdf(
        importJob: ImportJob,
        pdfInputStream: InputStream,
        document: Document,
        currentState: CurrentState,
    ): String? = withContext(Dispatchers.IO) {
        importJob.moveToNextStage()
        try {
            val cachedPdfUri = pdfInputStream.use {
                document.resources.storeAttachment(pdfInputStream) { cacheProgress ->
                    ensureActive()
                    importJob.setStageProgress(cacheProgress)
                    notifyProgress(
                        importJob.importFileName,
                        importJob.currentProgress,
                        currentState
                    )
                }
            }
            cachedPdfUri.toString()
        } catch (exception: IOException) {
            currentState.errorCode = ERROR_SPACE_NOT_ENOUGH
            null
        }
    }

    @Keep
    private class AudioInfo(
        val name: String,
        val sort: Int,
    )

    private suspend fun addAudiosToDocument(
        document: Document,
        audioRootFile: File,
        currentState: CurrentState,
    ) {
        val configFile = audioRootFile.walk().find { it.isFile && it.name == "config.json" }
        if (configFile == null) {
            currentState.errorCode = ERROR_AUDIO_CONFIG_FILE_NOT_FOUND_CODE
            return
        }
        val audioInfoList = try {
            InputStreamReader(FileInputStream(configFile)).use {
                commonGsonClient.fromJson<List<AudioInfo>>(
                    it,
                    object : TypeToken<List<AudioInfo>>() {}.type
                )
            }
        } catch (e: Exception) {
            null
        }
        if (audioInfoList.isNullOrEmpty()) {
            currentState.errorCode = ERROR_AUDIO_CONFIG_FILE_PARSE_CODE
            return
        }
        val mediaMetadataRetriever = MediaMetadataRetriever()
        audioInfoList.sortedBy {
            it.sort
        }.forEach {
            try {
                val file = File(configFile.parent, it.name)
                mediaMetadataRetriever.setDataSource(file.absolutePath)
                val durationString =
                    mediaMetadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                if (durationString != null) {
                    val duration = durationString.toInt()
                    if (duration > 0) {
                        document.recordManager.newRecord(file, duration, it.name, 0)
                    }
                }
            } catch (_: Exception) {
            }
        }
        try {
            mediaMetadataRetriever.release()
            audioRootFile.deleteRecursively()
        } catch (_: Exception) {
        }
    }

    private suspend fun segmentedParsePdfPage(
        importJob: ImportJob,
        document: Document,
        cachedPdfUri: String,
        currentState: CurrentState,
    ) = withContext(Dispatchers.IO) {
        importJob.moveToNextStage()
        val resourceManager = document.resources

        val cachedPdfFile = resourceManager.openFile(cachedPdfUri)
        importJob.addCacheFile(cachedPdfFile)

        val pdfPageCount = try {
            PDFManager.getPdfPageCount(cachedPdfFile)
        } catch (e: IOException) {
            throw PdfFileException(e.message ?: "", e)
        }
        var pdfDocument: PdfDocument? = null
        for (pageIndex in 0 until pdfPageCount) {
            if (pdfDocument == null) {
                pdfDocument = PDFManager.getPdfDocument(cachedPdfFile)
            }
            PDFManager.openPdfPage(pdfDocument, pageIndex)

            val pageSizePoint = PDFManager.getPDFPagePointSize(pdfDocument, pageIndex)
            document.newPage(
                pageIndex,
                Paper.custom(cachedPdfUri, pageIndex, pageSizePoint.width, pageSizePoint.height),
                pdfLinks = PDFManager.getPdfPageLinks(pdfDocument, pageIndex)
            )
            if (pageIndex == 0) {
                ThumbnailManager.saveDefaultThumbnail(document, pageIndex)
            }
            // 检查协程状态
            ensureActive()
            importJob.setStageProgress(pageIndex.toFloat() / pdfPageCount)
            notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
            if (pageIndex + 1 % SEGMENTED_IMPORT_PAGES_COUNT == 0) {
                PDFManager.closePdfDocument(pdfDocument)
                pdfDocument = null
            }
        }
        if (pdfDocument != null) {
            PDFManager.closePdfDocument(pdfDocument)
            pdfDocument = null
        }
        //需要page uuid，必须等待document页面全部解析完，再进行大纲解析
        analysisPDFOutline(document, cachedPdfFile)
    }

    private fun analysisPDFOutline(
        document: Document,
        pdfFile: File,
    ) {
        val level = 0
        val tableOfContents = PDFManager.getPdfTableOfContents(pdfFile)
        tableOfContents.forEach { bookmark ->
            if (bookmark.pageIdx < 0L) {
                return@forEach
            }
            val pageUUID = document.pageIds[bookmark.pageIdx.toInt()]
            document.addOutline(
                OutlineEntity(
                    bookmark.title,
                    OutlineEntity.TitleLevel.ZERO,
                    pageUUID,
                    System.currentTimeMillis()
                )
            )
            recursiveGetOutlineEntry(document, bookmark, level)
        }
    }

    private fun recursiveGetOutlineEntry(
        document: Document,
        bookmark: Bookmark,
        level: Int,
    ) {
        if (bookmark.hasChildren() && level < OUTLINE_LEVEL_RESTRICTION) {
            bookmark.children.forEach { childBookmark ->
                if (childBookmark.pageIdx < 0L) {
                    return@forEach
                }
                val pageUUID = document.pageIds[childBookmark.pageIdx.toInt()]
                document.addOutline(
                    OutlineEntity(
                        childBookmark.title,
                        when (level + 1) {
                            1 -> OutlineEntity.TitleLevel.ONE
                            2 -> OutlineEntity.TitleLevel.TWO
                            else -> throw Exception("pdf recursion error")
                        },
                        pageUUID,
                        System.currentTimeMillis()
                    )
                )
                recursiveGetOutlineEntry(document, childBookmark, level + 1)
            }
        }
    }

    fun cancel() {
        LogHelper.i(
            TAG,
            "cancel import task: ${runningImportJob?.importFileName}",
            report2Bugly = true
        )
        val jobToCancel = runningImportJob
        runningImportJob = null
        clearCallbacks()
        ioScope.launch {
            jobToCancel?.cancel()
        }
    }

    private var onProgressUpdatedAction: ((fileName: String, progress: Float) -> Unit)? = null

    /**
     * 导入进度回调，在主线程
     */
    fun doOnProgressUpdated(progressUpdateAction: (fileName: String, progress: Float) -> Unit): FileImporter {
        if (!isImporting()) {
            onProgressUpdatedAction = progressUpdateAction
        }
        return this
    }

    private var onImportSucceededAction: ((importDocuments: List<Document>) -> Unit)? = null

    /**
     * 导入成功回调，在主线程
     */
    fun doOnImportSucceeded(successAction: (importDocuments: List<Document>) -> Unit): FileImporter {
        if (!isImporting()) {
            onImportSucceededAction = successAction
        }
        return this
    }

    private var onImportFailedAction: ((fileName: String, errorCode: Int, errorFileNames: List<String>, parsedDocuments: List<Document>) -> Unit)? =
        null

    /**
     * 导入失败回调，在主线程
     */
    fun doOnImportFailed(failureAction: (fileName: String, errorCode: Int, errorFileNames: List<String>, parsedDocuments: List<Document>) -> Unit): FileImporter {
        if (!isImporting()) {
            onImportFailedAction = failureAction
        }
        return this
    }

    /**
     * 恢复到当前正在导入任务的环境。
     */
    fun restore(
        progressUpdateAction: (fileName: String, progress: Float) -> Unit,
        failureAction: (fileName: String, errorCode: Int, errorFileNames: List<String>, parsedDocuments: List<Document>) -> Unit,
        successAction: (importDocuments: List<Document>) -> Unit,
        currentState: CurrentState,
    ) {
        if (isImporting()) {
            onProgressUpdatedAction = progressUpdateAction
            onImportFailedAction = failureAction
            onImportSucceededAction = successAction
            runningImportJob?.let { importJob ->
                notifyProgress(importJob.importFileName, importJob.currentProgress, currentState)
            }
        }
    }

    fun clearCallbacks() {
        onProgressUpdatedAction = null
        onImportSucceededAction = null
        onImportFailedAction = null
    }

    fun getFileNameFromUri(context: Context, uri: Uri): String {
        var name = ""

        try {
            if (uri.scheme == "file") {
                name = uri.toFile().name
            } else {
                val projection = arrayOf(
                    MediaStore.Images.Media.DISPLAY_NAME,
                )

                val result = context.contentResolver.query(uri, projection, null, null, null)
                result?.use { cursor ->
                    val nameColumn =
                        cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)

                    if (cursor.moveToFirst()) {
                        name = cursor.getStringWithDefault(nameColumn, "未命名")
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            name = "未命名"
            LogHelper.e(TAG, "getFileNameFromUri($uri)", e, true)
        }
        return name
    }

    private const val MIN_NOTIFY_TIME_INTERVAL = 100L
    private const val MIN_NOTIFY_PROGRESS_INTERVAL = 0.01F

    private fun notifyProgress(fileName: String, progress: Float, currentState: CurrentState) {
        val currentTime = System.currentTimeMillis()
        val timeDiff = currentTime - currentState.lastNotifyProgressTime
        val progressDiff = progress - currentState.lastNotifyProgress
        if (timeDiff >= MIN_NOTIFY_TIME_INTERVAL || progressDiff >= MIN_NOTIFY_PROGRESS_INTERVAL) {
            currentState.lastNotifyProgress = progress
            currentState.lastNotifyProgressTime = currentTime
            runOnUiThread {
                onProgressUpdatedAction?.invoke(fileName, progress)
            }
        }
    }

    private fun runOnUiThread(block: () -> Unit) {
        uiHandler.post(block)
    }


    private class ImportJob {
        private val cachedFiles = mutableListOf<File>()
        val errorFileNames = mutableListOf<String>()

        var parseJob: Job? = null
        var importFileName: String = ""

        var totalStage: Int = 0 // 导入过程要经历的阶段数，用来计算进度，由外部传入
        private var currentStage = 0
        var currentProgress: Float = 0f
            private set

        fun moveToNextStage() {
            if (currentStage < totalStage) {
                currentStage++
            }
        }

        fun setStageProgress(stageProgress: Float) {
            if (totalStage > 0) {
                val progress = stageProgress.coerceIn(0f, 1f)
                val completedStage = if (currentStage > 0) currentStage - 1f else 0f
                currentProgress = (completedStage + progress) / totalStage
            }
        }

        fun addCacheFile(cache: File) {
            if (!canceled) {
                cachedFiles.add(cache)
            }
        }

        fun addErrorFileName(fileName: String) {
            if (!canceled) {
                errorFileNames.add(fileName)
            }
        }

        var importDocuments: ArrayList<Document> = ArrayList<Document>()

        private var canceled = false

        fun cancel() {
            canceled = true
            parseJob?.cancel()
            cachedFiles.forEach { cachedFile ->
                try {
                    if (cachedFile.isFile) {
                        cachedFile.delete()
                    }
                    if (cachedFile.isDirectory && !cachedFile.absolutePath.startsWith(RestoreHandler.restoreDir.absolutePath)) {
                        cachedFile.deleteRecursively()
                    }
                } catch (e: Exception) {
                    val message =
                        "FileImporter cancel exception, file = ${cachedFile.absolutePath}\n" + (e.message
                            ?: "")
                    LogHelper.e(
                        TAG,
                        message,
                        LogHelper.KiloLogException(message),
                        true
                    )
                }
            }
            reset()
        }

        fun deleteDocument() {
            importDocuments.forEach { document ->
                NoteRepository.deleteDocumentPermanently(document)
                document.resources.delete()
            }
            importDocuments.clear()
        }

        private fun reset() {
            parseJob = null
            cachedFiles.clear()
            errorFileNames.clear()
        }
    }

    class CurrentState {
        var errorCode = ERROR_DEFAULT_CODE
        var lastNotifyProgress = 0F
        var lastNotifyProgressTime = 0L
    }
}
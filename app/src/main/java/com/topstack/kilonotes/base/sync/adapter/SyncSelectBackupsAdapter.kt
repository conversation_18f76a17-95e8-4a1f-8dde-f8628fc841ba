package com.topstack.kilonotes.base.sync.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.sync.mode.SyncBackupsInfo
import com.topstack.kilonotes.infra.util.TimeUtil

class SyncSelectBackupsAdapter(
    private val syncBackupsInfoList: List<SyncBackupsInfo>,
    private val selection: Set<Int>,
    private val onSelect: (Int) -> Unit
) : RecyclerView.Adapter<SyncSelectBackupsAdapter.ViewHolder>() {

    companion object {
        private const val DASHED_LINE = 0
        private const val BACKUPS_DOCUMENT = 1
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): ViewHolder {
        return if (viewType == DASHED_LINE) {
            DashedLineViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.sync_recycle_item_select_dashed_line, parent, false)
            )
        } else {
            BackupsViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.sync_recycle_item_select_note, parent, false)
            )
        }
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (holder is BackupsViewHolder) {
            holder.backupTime.text = TimeUtil.format(syncBackupsInfoList[position].syncTime)
            holder.title.text = syncBackupsInfoList[position].noteName
            holder.checkbox.isSelected = selection.contains(position)
            holder.root.setOnClickListener {
                val isSelected = !holder.checkbox.isSelected
                onSelect(holder.bindingAdapterPosition)
                holder.checkbox.isSelected = isSelected
            }
        }
    }

    override fun getItemCount(): Int {
        return syncBackupsInfoList.size
    }

    override fun getItemViewType(position: Int): Int {
        return if (syncBackupsInfoList[position].showHalvingLine) {
            DASHED_LINE
        } else {
            BACKUPS_DOCUMENT
        }
    }

    abstract class ViewHolder(view: View) : RecyclerView.ViewHolder(view)

    class BackupsViewHolder(view: View) : ViewHolder(view) {
        val root: View = view.rootView
        val backupTime: TextView = view.findViewById(R.id.time)
        val title: TextView = view.findViewById(R.id.title)
        val checkbox: ImageView = view.findViewById(R.id.checkbox)
    }

    class DashedLineViewHolder(view: View) : ViewHolder(view)

}
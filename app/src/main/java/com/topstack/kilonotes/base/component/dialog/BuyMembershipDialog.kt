package com.topstack.kilonotes.base.component.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.i18n.isChineseLanguage
import com.topstack.kilonotes.base.ktx.safeNavigate
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.infra.device.DeviceType
import com.topstack.kilonotes.infra.device.DeviceUtils
import com.topstack.kilonotes.pad.note.NoteListFragmentDirections
import com.topstack.kilonotes.phone.note.PhoneNoteListFragmentDirections
import kotlin.math.max

class BuyMembershipDialog : BaseHomeDialog() {

    companion object {
        const val TAG = "BuyMembershipDialog"
    }

    private val close: ImageView by lazy { requireView().findViewById(R.id.close) }
    private val confirm: TextView by lazy { requireView().findViewById(R.id.confirm) }
    private val image: ImageView by lazy { requireView().findViewById(R.id.image) }
    private lateinit var displayMetrics: DisplayMetrics
    private var contentViewMeasuredWidth = 0
    private var contentViewMeasuredHeight = 0
    private val isWidthLayout
        get() = displayMetrics.widthPixels > displayMetrics.heightPixels
    private val isPhoneLayout
        get() = KiloApp.deviceType == DeviceType.Phone

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
        displayMetrics = DimensionUtil.getScreenDimensions(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return if (isPhoneLayout) {
            inflater.inflate(R.layout.phone_dialog_buy_membership, container, false)
        } else {
            if (isWidthLayout) {
                inflater.inflate(R.layout.dialog_buy_membership, container, false)
            } else {
                inflater.inflate(R.layout.dialog_buy_membership_one_third, container, false)
            }
        }.apply {
            measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            contentViewMeasuredWidth = measuredWidth
            contentViewMeasuredHeight = measuredHeight
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        image.setImageResource(
            if (isChineseLanguage()) {
                R.drawable.dialog_buy_membership_image_zh
            } else {
                R.drawable.dialog_buy_membership_image_en
            }
        )
        close.setOnClickListener {
            dismiss()
        }
        confirm.setOnClickListener {
            setIsNeedRefreshNextDialog(false)
            if (DeviceUtils.isPhoneType(KiloApp.deviceType)) {
                val action = PhoneNoteListFragmentDirections.actionBuyMembershipStore()
                safeNavigate(R.id.note_list, action)
            } else {
                val action = NoteListFragmentDirections.actionBuyMembershipStore()
                safeNavigate(R.id.note_list, action)
            }
            dismiss()
        }
        image.setOnClickListener {
            setIsNeedRefreshNextDialog(false)
            if (DeviceUtils.isPhoneType(KiloApp.deviceType)) {
                val action = PhoneNoteListFragmentDirections.actionBuyMembershipStore()
                safeNavigate(R.id.note_list, action)
            } else {
                val action = NoteListFragmentDirections.actionBuyMembershipStore()
                safeNavigate(R.id.note_list, action)
            }
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setLayout(displayMetrics.widthPixels, displayMetrics.heightPixels)
            setGravity(Gravity.CENTER)
        }
        val padding = requireContext().resources.getDimensionPixelSize(R.dimen.dp_32)
        val widthRatio =
            (contentViewMeasuredWidth.toFloat() + padding * 2) / displayMetrics.widthPixels
        val heightRatio =
            (contentViewMeasuredHeight.toFloat() + padding * 2) / displayMetrics.heightPixels
        val maxRatio = max(widthRatio, heightRatio)
        if (maxRatio > 1) {
            image.layoutParams = image.layoutParams.apply {
                width = (width / maxRatio).toInt()
            }
            confirm.apply {
                val lp = confirm.layoutParams as ConstraintLayout.LayoutParams
                layoutParams = lp.apply {
                    width = (width / maxRatio).toInt()
                    height = (height / maxRatio).toInt()
                    topMargin = (topMargin / maxRatio).toInt()
                }
                setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize / maxRatio)
            }
        }
    }

    override fun dismiss() {
        Preferences.needShowBuyMembershipDialog = false
        super.dismiss()
    }

}
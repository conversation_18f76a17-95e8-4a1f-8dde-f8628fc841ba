package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.marginTop
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadHiddenSpaceVipAndSecurityTipsLayoutBinding

class PadHiddenSpaceVipAndSecurityTipsLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val binding: PadHiddenSpaceVipAndSecurityTipsLayoutBinding
    private var onAskLaterBtnClick: (() -> Unit)? = null
    private var onBuyVipBtnClick: (() -> Unit)? = null
    private var onSecurityQuestionTipsClick: (() -> Unit)? = null
    private var hasSetSecurityQuestion: Boolean = true

    init {
        binding =
            PadHiddenSpaceVipAndSecurityTipsLayoutBinding.inflate(
                LayoutInflater.from(context),
                this,
                true
            )
        transitLayout()
    }

    fun setAskLaterBtnClickListener(action: () -> Unit) {
        onAskLaterBtnClick = action
    }

    fun setBuyVipBtnClickListener(action: () -> Unit) {
        onBuyVipBtnClick = action
    }

    fun setHasSetSecurityQuestion(hasSet: Boolean) {
        hasSetSecurityQuestion = hasSet
        transitLayout()
    }

    fun setSecurityQuestionTipsClickListener(action: () -> Unit) {
        onSecurityQuestionTipsClick = action
    }

    private fun transitLayout() {
        binding.askLater.isVisible = hasSetSecurityQuestion
        binding.buyVip.isVisible = hasSetSecurityQuestion
        binding.hiddenSpaceVipTipsTitle.isVisible = hasSetSecurityQuestion
        binding.goToSetSecurityQuestionText.isVisible = !hasSetSecurityQuestion
        binding.goToSetSecurityQuestionIcon.isVisible = !hasSetSecurityQuestion
        if (hasSetSecurityQuestion) {
            val params = binding.containerShadow.layoutParams as ConstraintLayout.LayoutParams
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.setMargins(
                resources.getDimension(R.dimen.dp_20).toInt(),
                binding.containerShadow.marginTop,
                resources.getDimension(R.dimen.dp_20).toInt(),
                binding.containerShadow.marginBottom
            )
            binding.containerShadow.layoutParams = params
            binding.containerShadow.setShadowColor(
                context.resources.getColor(
                    R.color.hidden_space_vip_and_security_tips_shadow_color,
                    null
                )
            )
            if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context) || DimensionUtil.isLandAndOneThirdScreen(
                    context
                )
            ) {
                val set = ConstraintSet()
                set.clone(
                    binding.container
                )
                set.connect(
                    binding.hiddenSpaceVipTipsTitle.id,
                    ConstraintSet.START,
                    binding.container.id,
                    ConstraintSet.START
                )
                set.connect(
                    binding.hiddenSpaceVipTipsTitle.id,
                    ConstraintSet.END,
                    binding.container.id,
                    ConstraintSet.END
                )

                set.connect(
                    binding.hiddenSpaceVipTipsTitle.id,
                    ConstraintSet.TOP,
                    binding.container.id,
                    ConstraintSet.TOP
                )

                set.connect(
                    binding.hiddenSpaceVipTipsTitle.id,
                    ConstraintSet.BOTTOM,
                    binding.buyVip.id,
                    ConstraintSet.TOP
                )

                set.connect(
                    binding.buyVip.id,
                    ConstraintSet.START,
                    binding.container.id,
                    ConstraintSet.START
                )
                set.connect(
                    binding.buyVip.id,
                    ConstraintSet.END,
                    binding.container.id,
                    ConstraintSet.END
                )

                set.connect(
                    binding.buyVip.id,
                    ConstraintSet.TOP,
                    binding.hiddenSpaceVipTipsTitle.id,
                    ConstraintSet.BOTTOM
                )

                set.connect(
                    binding.buyVip.id,
                    ConstraintSet.BOTTOM,
                    binding.askLater.id,
                    ConstraintSet.TOP
                )
                set.connect(
                    binding.askLater.id,
                    ConstraintSet.START,
                    binding.container.id,
                    ConstraintSet.START
                )
                set.connect(
                    binding.askLater.id,
                    ConstraintSet.END,
                    binding.container.id,
                    ConstraintSet.END
                )
                set.connect(
                    binding.askLater.id,
                    ConstraintSet.TOP,
                    binding.buyVip.id,
                    ConstraintSet.BOTTOM
                )

                set.connect(
                    binding.askLater.id,
                    ConstraintSet.BOTTOM,
                    binding.container.id,
                    ConstraintSet.BOTTOM
                )
                set.applyTo(binding.container)

                binding.hiddenSpaceVipTipsTitle.setMargins(
                    0,
                    resources.getDimension(R.dimen.dp_30).toInt(),
                    0,
                    0
                )

                binding.hiddenSpaceVipTipsTitle.setPadding(
                    resources.getDimension(R.dimen.dp_30).toInt(),
                    0,
                    resources.getDimension(R.dimen.dp_30).toInt(),
                    0
                )


                binding.buyVip.setMargins(
                    resources.getDimension(R.dimen.dp_30).toInt(),
                    resources.getDimension(R.dimen.dp_36).toInt(),
                    resources.getDimension(R.dimen.dp_30).toInt(),
                    0
                )

                binding.askLater.setMargins(
                    resources.getDimension(R.dimen.dp_30).toInt(),
                    resources.getDimension(R.dimen.dp_24).toInt(),
                    resources.getDimension(R.dimen.dp_30).toInt(),
                    resources.getDimension(R.dimen.dp_29).toInt()
                )

                val askLaterLayoutParams = binding.askLater.layoutParams
                askLaterLayoutParams.width = resources.getDimension(R.dimen.dp_0).toInt()
                binding.askLater.layoutParams = askLaterLayoutParams

                val buyVipLayoutParams = binding.buyVip.layoutParams
                buyVipLayoutParams.width = resources.getDimension(R.dimen.dp_0).toInt()
                binding.buyVip.layoutParams = buyVipLayoutParams



                binding.containerShadow.setShadowHiddenBottom(true)
                binding.containerShadow.setShadowHiddenTop(true)
                binding.containerShadow.setShadowHiddenLeft(true)
                binding.containerShadow.setShadowHiddenRight(true)
                binding.containerShadow.setCornerRadius(0)
                binding.containerShadow.setMargins(0, 0, 0, 0)
                binding.containerShadow.requestLayout()
                binding.topShadow.isVisible = true
                binding.root.setPadding(
                    0, 0, 0, 0
                )

            }

            binding.container.background = ColorDrawable(Color.WHITE)
            binding.askLater.setOnClickListener(AntiShakeClickListener {
                onAskLaterBtnClick?.invoke()
            })
            binding.buyVip.setOnClickListener(AntiShakeClickListener {
                onBuyVipBtnClick?.invoke()
            })
        } else {
            binding.topShadow.isVisible = false
            val params = binding.containerShadow.layoutParams as ConstraintLayout.LayoutParams
            if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context) || DimensionUtil.isLandAndOneThirdScreen(
                    context
                )
            ) {
                params.width = ViewGroup.LayoutParams.MATCH_PARENT
                params.setMargins(
                    context.resources.getDimension(R.dimen.dp_12).toInt(),
                    binding.containerShadow.marginTop,
                    context.resources.getDimension(R.dimen.dp_12).toInt(),
                    binding.containerShadow.marginBottom
                )
            } else {
                params.width = resources.getDimension(R.dimen.dp_680).toInt()
                params.setMargins(
                    0,
                    binding.containerShadow.marginTop,
                    0,
                    binding.containerShadow.marginBottom
                )
            }
            binding.containerShadow.layoutParams = params


            if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context) || DimensionUtil.isLandAndOneThirdScreen(
                    context
                )
            ) {
                binding.containerShadow.setShadowHiddenBottom(false)
                binding.containerShadow.setShadowHiddenTop(false)
                binding.containerShadow.setShadowHiddenLeft(false)
                binding.containerShadow.setShadowHiddenRight(false)
                binding.containerShadow.setCornerRadius(
                    resources.getDimension(R.dimen.dp_12).toInt()
                )
                binding.containerShadow.setShadowLimit(
                    resources.getDimension(R.dimen.dp_10).toInt()
                )
                binding.containerShadow.requestLayout()
                binding.root.setPadding(
                    context.resources.getDimension(R.dimen.dp_12).toInt(),
                    0,
                    context.resources.getDimension(R.dimen.dp_12).toInt(),
                    resources.getDimension(R.dimen.dp_20).toInt()
                )
            }


            binding.container.background =
                ColorDrawable(resources.getColor(R.color.skip_text, null))
            binding.goToSetSecurityQuestionText.setOnClickListener(AntiShakeClickListener {
                onSecurityQuestionTipsClick?.invoke()
            })
            binding.goToSetSecurityQuestionIcon.setOnClickListener(AntiShakeClickListener {
                onSecurityQuestionTipsClick?.invoke()
            })
        }
    }
}
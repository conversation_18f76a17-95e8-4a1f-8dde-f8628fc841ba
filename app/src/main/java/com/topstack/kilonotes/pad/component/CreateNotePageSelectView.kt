package com.topstack.kilonotes.pad.component

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.graphics.Typeface
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.signature.ObjectKey
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.datareporter.DataType
import com.topstack.kilonotes.base.datareporter.datacollection.TemplateDataCollection
import com.topstack.kilonotes.base.doc.io.ThumbnailManager
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.handbook.model.Template
import com.topstack.kilonotes.base.handbook.model.TemplateCategory
import com.topstack.kilonotes.base.handbook.model.TemplateCategoryWithList
import com.topstack.kilonotes.base.note.viewmodel.BaseTemplateViewModel
import com.topstack.kilonotes.base.util.TextViewLinesUtil
import com.topstack.kilonotes.base.util.getRandomLoadColor
import com.topstack.kilonotes.databinding.ItemAddPageTemplateBinding
import com.topstack.kilonotes.databinding.ItemTemplatePageBinding
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.pay.order.PayHelper
import com.topstack.kilonotes.pay.order.PayType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class CreateNotePageSelectView(context: Context, attrs: AttributeSet) :
    OverScrollCoordinatorRecyclerView(context, attrs) {

    companion object {
        private const val INVALID_POSITION = -1
        private const val PAPER_TITLE_POSITION = 0
        private const val PAPER_COLOR_LIST_POSITION = 1
        private const val PAPER_LIST_POSITION = 2
        private const val SPLIT_LINE_POSITION = 3
        private const val TEMPLATE_TITLE_POSITION = 4

        private const val ITEM_TYPE_TITLE = 0
        private const val ITEM_TYPE_PAPER_COLOR_LIST = 1
        private const val ITEM_TYPE_PAPER_LIST = 2
        private const val ITEM_TYPE_SPLIT_LINE = 3
        private const val ITEM_TYPE_TEMPLATE_LIST = 4

        const val DEFAULT_CATEGORY_SELECTED = INVALID_POSITION
        const val DEFAULT_TEMPLATE_SELECTED = INVALID_POSITION
        private const val UPDATE_CHECK_STATE = 1
        private const val PAD_DEVICE = "pad"
    }

    private val createNotePageSelectAdapter = CreateNotePageSelectAdapter()
    private val templateRecyclerViewPool = RecyclerView.RecycledViewPool()

    private var currentPaperTitlePosition: Int = PAPER_TITLE_POSITION
    private var currentPaperColorListPosition: Int = PAPER_COLOR_LIST_POSITION
    private var currentPaperListPosition: Int = PAPER_LIST_POSITION
    private var currentSplitLinePosition: Int = INVALID_POSITION
    private var currentTemplateTitlePosition: Int = INVALID_POSITION
    private val itemList = mutableListOf<Any>().apply {
        add(ITEM_TYPE_TITLE)
        add(ITEM_TYPE_PAPER_COLOR_LIST)
        add(ITEM_TYPE_PAPER_LIST)
    }

    var selectedTemplatePosition: Pair<Int, Int> =
        Pair(DEFAULT_CATEGORY_SELECTED, DEFAULT_TEMPLATE_SELECTED)
        set(value) {
            if (field != value) {
                val oldPosition = field
                field = value
                if (isValidPosition(oldPosition)) {
                    getNoteTemplateListAdapter(oldPosition.first)?.let {
                        it.selectedPosition = INVALID_POSITION
                    }
                    createNotePageSelectAdapter.notifyItemChanged(oldPosition.first)
                }
                if (isValidPosition(value)) {
                    getNoteTemplateListAdapter(value.first)?.let {
                        it.selectedPosition = value.second
                    }
                    createNotePageSelectAdapter.notifyItemChanged(currentPaperListPosition)
                    createNotePageSelectAdapter.notifyItemChanged(value.first)
                }
            }
        }

    var selectedPaperPosition: Int = INVALID_POSITION
        set(value) {
            if (field != value) {
                field = value
                val holder =
                    binding.overscrollView.findViewHolderForAdapterPosition(currentPaperListPosition)
                if (holder is CreateNotePageSelectPaperListViewHolder) {
                    holder.paperSelectView.updateSelectedPosition(value)
                }
            }
        }

    private fun isValidPosition(pair: Pair<Int, Int>): Boolean {
        return pair.first != DEFAULT_CATEGORY_SELECTED && pair.second != DEFAULT_TEMPLATE_SELECTED
    }

    private fun getNoteTemplateListAdapter(position: Int): NoteTemplateListAdapter? {
        val holder = binding.overscrollView.findViewHolderForAdapterPosition(position)
        if (holder is CreateNotePageSelectTemplateListViewHolder) {
            val adapter = holder.binding.templateListCoordinator.overScrollRecyclerView.adapter
            if (adapter is NoteTemplateListAdapter) {
                return adapter
            }
        }
        return null
    }

    private var initialColorPosition: Int = 0
    private val currentColorList = mutableListOf<String>()
    private var onColorSelectedAction: (String) -> Unit = {}

    private var initialPaperPosition: Int = 0
    private val currentPaperList = mutableListOf<Paper>()
    private var paperListIsHorizontal = false
    private var onPaperSelectedAction: (Paper?, Int) -> Unit = { _, _ -> }
    private var onImportPaperAction: () -> Unit = {}
    private var onPaperInitSelectedAction: (Paper?, Int) -> Unit = { _, _ -> }

    var downloadHandbookCallback: ((String) -> Boolean)? = null
    var buyTemplateCallback: ((TemplateCategory) -> Unit)? = null
    var createTemplateBuyCallback: ((TemplateCategory) -> Unit)? = null
    var createTemplateCallback: ((Template, Pair<Int, Int>) -> Unit)? = null


    private val dp165 = context.resources.getDimension(R.dimen.dp_165).toInt()
    private val dp220 = context.resources.getDimension(R.dimen.dp_220).toInt()
    private val dp48 = resources.getDimensionPixelSize(R.dimen.dp_48)
    private val dp36 = resources.getDimensionPixelSize(R.dimen.dp_36)
    private val dp30 = resources.getDimensionPixelSize(R.dimen.dp_30)
    private val dp24 = resources.getDimensionPixelSize(R.dimen.dp_24)
    private val sp24 = resources.getDimension(R.dimen.sp_24)
    private var horizontalMargins = dp48

    init {
        overScrollRecyclerView.apply {
            itemAnimator = null
            adapter = createNotePageSelectAdapter
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State,
                ) {
                    val position = parent.getChildAdapterPosition(view)
                    outRect.top = when (position) {
                        currentPaperTitlePosition, currentPaperListPosition -> dp30
                        currentPaperColorListPosition -> dp24
                        else -> dp36
                    }
                    val horizontal = when (position) {
                        currentPaperColorListPosition, currentPaperListPosition, currentSplitLinePosition -> 0
                        else -> horizontalMargins
                    }
                    outRect.left = horizontal
                    outRect.right = horizontal
                }
            })
        }
    }

    private fun getTemplateCategoryWithList(position: Int): TemplateCategoryWithList? {
        return itemList[position] as? TemplateCategoryWithList
    }

    fun setupColorList(
        initialPosition: Int, colorList: List<String>, onColorSelected: (color: String) -> Unit,
    ) {
        initialColorPosition = initialPosition
        onColorSelectedAction = onColorSelected
        currentColorList.clear()
        currentColorList.addAll(colorList)
        createNotePageSelectAdapter.notifyItemChanged(currentPaperColorListPosition)
    }

    fun setupPaperList(
        initialPosition: Int,
        paperList: List<Paper>,
        isHorizontalPaper: Boolean,
        onPaperSelected: (Paper?, Int) -> Unit,
        onImportPaper: () -> Unit,
        onPaperInitSelected: (Paper?, Int) -> Unit,
    ) {
        initialPaperPosition = initialPosition
        paperListIsHorizontal = isHorizontalPaper
        onPaperSelectedAction = onPaperSelected
        onImportPaperAction = onImportPaper
        onPaperInitSelectedAction = onPaperInitSelected
        currentPaperList.clear()
        currentPaperList.addAll(paperList)
        createNotePageSelectAdapter.notifyItemChanged(currentPaperListPosition)
    }

    fun setTemplateCategoryWithLists(allList: List<TemplateCategoryWithList>) {
        val list = allList.filter { it.templateCategory.device == PAD_DEVICE }
        val newItemList = mutableListOf<Any>().apply {
            add(ITEM_TYPE_TITLE)
            add(ITEM_TYPE_PAPER_COLOR_LIST)
            add(ITEM_TYPE_PAPER_LIST)
            if (list.isNotEmpty()) {
                add(ITEM_TYPE_SPLIT_LINE)
                add(ITEM_TYPE_TITLE)
                addAll(list)
                currentSplitLinePosition = SPLIT_LINE_POSITION
                currentTemplateTitlePosition = TEMPLATE_TITLE_POSITION
            } else {
                currentSplitLinePosition = INVALID_POSITION
                currentTemplateTitlePosition = INVALID_POSITION
            }
        }
        val diffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun getOldListSize(): Int {
                return itemList.size
            }

            override fun getNewListSize(): Int {
                return newItemList.size
            }

            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldValue = itemList[oldItemPosition]
                val newValue = newItemList[newItemPosition]
                return if (oldValue is TemplateCategoryWithList && newValue is TemplateCategoryWithList) {
                    oldValue.templateCategory.categoryId == newValue.templateCategory.categoryId
                } else {
                    oldValue == newValue
                }
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldValue = itemList[oldItemPosition]
                val newValue = newItemList[newItemPosition]
                return if (oldValue is TemplateCategoryWithList && newValue is TemplateCategoryWithList) {
                    oldValue.templateCategory == newValue.templateCategory
                            && oldValue.templateList === newValue.templateList
                } else {
                    oldValue == newValue
                }
            }

        })
        itemList.clear()
        itemList.addAll(newItemList)
        diffResult.dispatchUpdatesTo(createNotePageSelectAdapter)
    }

    fun setUseSmallerHorizontalMargins() {
        horizontalMargins = dp30
    }

    inner class CreateNotePageSelectAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        override fun getItemViewType(position: Int): Int {
            return when (position) {
                currentPaperTitlePosition, currentTemplateTitlePosition -> ITEM_TYPE_TITLE
                currentPaperColorListPosition -> ITEM_TYPE_PAPER_COLOR_LIST
                currentPaperListPosition -> ITEM_TYPE_PAPER_LIST
                currentSplitLinePosition -> ITEM_TYPE_SPLIT_LINE
                else -> ITEM_TYPE_TEMPLATE_LIST
            }
        }

        override fun getItemCount(): Int {
            return itemList.size
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            val context = parent.context
            return when (viewType) {
                ITEM_TYPE_TITLE -> {
                    CreateNotePageSelectTextViewHolder(TextView(context).apply {
                        setTextColor(AppUtils.getColor(R.color.black))
                        setTextSize(TypedValue.COMPLEX_UNIT_PX, sp24)
                        setTypeface(null, Typeface.BOLD)
                    })
                }

                ITEM_TYPE_PAPER_COLOR_LIST -> {
                    CreateNotePageSelectColorListViewHolder(ColorSelectView(context))
                }

                ITEM_TYPE_PAPER_LIST -> {
                    CreateNotePageSelectPaperListViewHolder(PaperSelectView(context))
                }

                ITEM_TYPE_SPLIT_LINE -> {
                    CreateNotePageSelectSplitViewHolder(View(context))
                }

                ITEM_TYPE_TEMPLATE_LIST -> {
                    CreateNotePageSelectTemplateListViewHolder(
                        ItemAddPageTemplateBinding.inflate(
                            LayoutInflater.from(context), parent, false
                        )
                    ).apply {
                        binding.templateListCoordinator.overScrollRecyclerView.apply {
//                            setRecycledViewPool(templateRecyclerViewPool)
                            layoutManager =
                                LinearLayoutManager(context, RecyclerView.HORIZONTAL, false).apply {
//                                    recycleChildrenOnDetach = true
                                }
                            addItemDecoration(object : RecyclerView.ItemDecoration() {
                                override fun getItemOffsets(
                                    outRect: Rect,
                                    view: View,
                                    parent: RecyclerView,
                                    state: RecyclerView.State,
                                ) {
                                    val position = parent.getChildAdapterPosition(view)
                                    if (position == 0) {
                                        outRect.left = dp30
                                    }
                                    outRect.right = dp30
                                }
                            })
                        }
                        binding.root.background =
                            AppUtils.getDrawable(R.drawable.template_list_create_background)
                    }
                }

                else -> throw IllegalArgumentException("CreateNotePageSelectAdapter unknown view type: $viewType")
            }
        }


        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            when (position) {
                currentPaperTitlePosition -> {
                    (holder as CreateNotePageSelectTextViewHolder).bind(R.string.page_name)
                }

                currentPaperColorListPosition -> {
                    (holder as CreateNotePageSelectColorListViewHolder).colorSelectView.setup(
                        initialColorPosition, currentColorList, onColorSelectedAction
                    )
                }

                currentPaperListPosition -> {
                    val paperSelectView =
                        (holder as CreateNotePageSelectPaperListViewHolder).paperSelectView
                    if (paperSelectView.isSetup() && paperSelectView.isHorizontalPaper() == paperListIsHorizontal) {
                        val selectedPosition =
                            if (selectedTemplatePosition.first != INVALID_POSITION) {
                                INVALID_POSITION
                            } else {
                                selectedPaperPosition
                            }
                        paperSelectView.updatePaperList(
                            currentPaperList,
                            selectedPosition,
                            onPaperInitSelectedAction
                        )
                    } else {
                        paperSelectView.setupHorizontal(
                            if (selectedPaperPosition != INVALID_POSITION) {
                                selectedPaperPosition
                            } else if (selectedTemplatePosition.first != INVALID_POSITION) {
                                INVALID_POSITION
                            } else {
                                initialPaperPosition
                            },
                            currentPaperList,
                            paperListIsHorizontal,
                            onPaperSelectedAction,
                            onImportPaperAction,
                            onPaperInitSelectedAction
                        )
                    }
                }

                currentSplitLinePosition -> {

                }

                currentTemplateTitlePosition -> {
                    (holder as CreateNotePageSelectTextViewHolder).bind(R.string.template_name)
                }

                else -> {
                    val binding = (holder as CreateNotePageSelectTemplateListViewHolder).binding
                    val recyclerView = binding.templateListCoordinator.overScrollRecyclerView
                    val adapter = recyclerView.adapter
                    val category = getTemplateCategoryWithList(position) ?: return
                    if (adapter is NoteTemplateListAdapter) {
                        adapter
                    } else {
                        NoteTemplateListAdapter().also {
                            it.createTemplateCallback = { template, templateIndex ->
                                createTemplateCallback?.invoke(
                                    template,
                                    Pair(holder.bindingAdapterPosition, templateIndex)
                                )
                            }
                            recyclerView.adapter = it
                        }
                    }.apply {
                        selectedPosition = if (selectedTemplatePosition.first == position) {
                            selectedTemplatePosition.second
                        } else {
                            INVALID_POSITION
                        }
                        setTemplateCategoryWithList(category)
                    }

                    binding.title.text = category.templateCategory.categoryName
                    if (category.templateList.firstOrNull { it.isVip } != null) {
                        val hasProduct = if (PayType.GOOGLE in PayHelper.getSupportedPayTypes()) {
                            UserManager.checkProductByGoogleProductId(category.templateCategory.googleProductId)
                        } else {
                            UserManager.checkProductByNotebookId(category.templateCategory.notebookId)
                        }
                        if (!UserManager.isVip()
                            && !hasProduct
                            && downloadHandbookCallback?.invoke(category.templateCategory.notebookId) != true
                        ) {
                            binding.templateBuyBg.visibility = View.VISIBLE
                            binding.templateBuyBg.setOnClickListener {
                                createTemplateBuyCallback?.invoke(category.templateCategory)
                            }
                        } else {
                            binding.templateBuyBg.visibility = View.GONE
                        }
                    } else {
                        binding.templateBuyBg.visibility = View.GONE
                    }

                    recyclerView.post {
                        if (!binding.templateListCoordinator.overScrollRecyclerView.isAttachedToWindow) return@post
                        val layoutManager =
                            binding.templateListCoordinator.overScrollRecyclerView.layoutManager as? LinearLayoutManager
                        val recyclerViewAdapter =
                            binding.templateListCoordinator.overScrollRecyclerView.adapter
                        if (recyclerViewAdapter is NoteTemplateListAdapter) {
                            layoutManager?.let {
                                TemplateDataCollection.templateListDataOfCategory(
                                    recyclerViewAdapter.templateList,
                                    category.templateCategory.categoryId,
                                    it.findFirstVisibleItemPosition(),
                                    it.findLastVisibleItemPosition(),
                                    DataType.NOTEBOOKS_TEMPLATE_VIEW
                                )
                            }
                        }
                    }
                    recyclerView.clearOnScrollListeners()
                    val materialListScrollListener: RecyclerView.OnScrollListener = object :
                        RecyclerView.OnScrollListener() {
                        override fun onScrollStateChanged(
                            recyclerView: RecyclerView,
                            newState: Int,
                        ) {
                            super.onScrollStateChanged(recyclerView, newState)
                            val hasEnd = newState == RecyclerView.SCROLL_STATE_IDLE
                            if (hasEnd && recyclerView.layoutManager != null) {
                                val layoutManager =
                                    recyclerView.layoutManager!! as? LinearLayoutManager
                                val recyclerViewAdapter = recyclerView.adapter
                                if (recyclerViewAdapter is NoteTemplateListAdapter) {
                                    layoutManager?.let {
                                        TemplateDataCollection.templateListDataOfCategory(
                                            recyclerViewAdapter.templateList,
                                            category.templateCategory.categoryId,
                                            it.findFirstVisibleItemPosition(),
                                            it.findLastVisibleItemPosition(),
                                            DataType.NOTEBOOKS_TEMPLATE_VIEW
                                        )
                                    }
                                }
                            }
                        }
                    }
                    recyclerView.addOnScrollListener(materialListScrollListener)
                }
            }
        }

    }

    class CreateNotePageSelectTextViewHolder(val textView: TextView) :
        RecyclerView.ViewHolder(textView) {

        fun bind(stringResId: Int) {
            textView.text = textView.context.getText(stringResId)
        }
    }

    class CreateNotePageSelectColorListViewHolder(val colorSelectView: ColorSelectView) :
        RecyclerView.ViewHolder(colorSelectView)

    class CreateNotePageSelectPaperListViewHolder(val paperSelectView: PaperSelectView) :
        RecyclerView.ViewHolder(paperSelectView)

    class CreateNotePageSelectSplitViewHolder(splitView: View) :
        RecyclerView.ViewHolder(splitView) {
        init {
            splitView.apply {
                setBackgroundColor(AppUtils.getColor(R.color.template_list_stroke_color))
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    resources.getDimensionPixelSize(R.dimen.dp_1)
                )
            }
        }
    }

    class CreateNotePageSelectTemplateListViewHolder(val binding: ItemAddPageTemplateBinding) :
        RecyclerView.ViewHolder(binding.root)

    inner class NoteTemplateListAdapter :
        RecyclerView.Adapter<NoteTemplateListAdapter.ViewHolder>() {
        private var mainScope: CoroutineScope? = null

        private lateinit var category: TemplateCategory

        val templateList = mutableListOf<Template>()

        private var defWidth: Int = 0
        private var defHeight: Int = 0

        //这一行每个模版名称的最大行数
        private var maxLines = 1

        //这一行最高模板高度
        private var maxTemplateHeight = defHeight

        var createTemplateCallback: ((Template, Int) -> Unit)? = null

        var selectedPosition: Int = INVALID_POSITION
            set(value) {
                if (field != value) {
                    val oldPosition = field
                    field = value
                    notifyItemChanged(oldPosition)
                    notifyItemChanged(value)
                }
            }

        @SuppressLint("NotifyDataSetChanged")
        fun setTemplateCategoryWithList(templateCategoryWithList: TemplateCategoryWithList) {
            category = templateCategoryWithList.templateCategory
            templateList.clear()
            templateList.addAll(templateCategoryWithList.templateList)
            defWidth = if (category.format == BaseTemplateViewModel.FORMAT_VERTICAL) {
                dp165
            } else {
                dp220
            }
            defHeight = if (category.format == BaseTemplateViewModel.FORMAT_VERTICAL) {
                dp220
            } else {
                dp165
            }
            maxTemplateHeight = templateList.maxOfOrNull { it.thumbnailHeight } ?: defHeight
            computeTemplateNameMaxLines()
            notifyDataSetChanged()
        }

        private fun computeTemplateNameMaxLines() {
            var maxLengthTemplateName = ""
            for (template in templateList) {
                if (template.name.length > maxLengthTemplateName.length) {
                    maxLengthTemplateName = template.name
                }
            }
            val computeTextView = ItemTemplatePageBinding.inflate(
                LayoutInflater.from(context),
                null,
                false
            ).templatePageName
            computeTextView.text = maxLengthTemplateName
            val lines = TextViewLinesUtil.getTextViewLines(
                computeTextView,
                defWidth
            )
            maxLines = lines.coerceIn(1, 2)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            return ViewHolder(
                ItemTemplatePageBinding.inflate(LayoutInflater.from(context), parent, false)
            )
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            if (position < templateList.size) {
                val template = templateList[position]
                holder.binding.selected.visibility =
                    if (selectedPosition == position) View.VISIBLE else View.INVISIBLE
                holder.binding.imageOutline.visibility =
                    if (selectedPosition == position) View.VISIBLE else View.INVISIBLE
                mainScope?.launch {
                    holder.bind(category, template, position)
                }
            }
        }

        override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
            mainScope = MainScope()
            super.onAttachedToRecyclerView(recyclerView)
        }

        override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
            super.onDetachedFromRecyclerView(recyclerView)
            mainScope?.cancel()
            mainScope = null
        }

        override fun getItemCount(): Int {
            return templateList.size
        }

        override fun onViewRecycled(holder: ViewHolder) {
            super.onViewRecycled(holder)
            Glide.with(holder.itemView).clear(holder.binding.templatePageCover)
        }

        inner class ViewHolder(val binding: ItemTemplatePageBinding) :
            RecyclerView.ViewHolder(binding.root) {
            @SuppressLint("ResourceAsColor")
            suspend fun bind(category: TemplateCategory, template: Template, position: Int) {
                val document = BaseTemplateViewModel.getDocument(template.file)
                val page = if (document?.pages?.isNotEmpty() == true) {
                    document.pages[0]
                } else {
                    null
                }
                binding.templatePageCover.apply {
                    layoutParams.width = defWidth
                    layoutParams.height = if (template.thumbnailWidth <= 0) {
                        defHeight
                    } else {
                        (maxTemplateHeight * (defWidth.toFloat() / template.thumbnailWidth)).toInt()
                    }
                }
                if (document == null || page == null) {
                    Glide.with(itemView)
                        .load(template.thumbnailUrl)
                        .placeholder(ColorDrawable().apply {
                            color = getRandomLoadColor()
                        })
                        .into(binding.templatePageCover)
                } else {
                    Glide.with(itemView)
                        .load(ThumbnailManager.getThumbnailFile(document, page))
                        .signature(ObjectKey(document.modifiedTime))
                        .into(binding.templatePageCover)
                }
                binding.vipTag.isVisible = template.isVip
                binding.templatePageName.setLines(maxLines)
                binding.templatePageName.text = template.name
                binding.maker.isVisible = false
                binding.selected.visibility =
                    if (selectedPosition == position) View.VISIBLE else View.INVISIBLE
                binding.imageOutline.visibility =
                    if (selectedPosition == position) View.VISIBLE else View.INVISIBLE
                itemView.setOnClickListener(
                    AntiShakeClickListener {
                        TemplateDataCollection.templateData(
                            template,
                            DataType.NOTEBOOKS_TEMPLATE_CLICK
                        )
                        val hasProduct = if (PayType.GOOGLE in PayHelper.getSupportedPayTypes()) {
                            UserManager.checkProductByGoogleProductId(category.googleProductId)
                        } else {
                            UserManager.checkProductByNotebookId(category.notebookId)
                        }
                        if (template.isVip
                            && !UserManager.isVip()
                            && !hasProduct
                            && downloadHandbookCallback?.invoke(category.notebookId) != true
                        ) {
                            buyTemplateCallback?.invoke(category)
                        } else {
                            createTemplateCallback?.invoke(template, position)
                        }
                    }
                )
            }
        }
    }
}
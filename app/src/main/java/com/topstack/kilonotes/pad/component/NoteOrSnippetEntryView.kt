package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.databinding.ViewNoteOrSnippetEntryBinding
import com.topstack.kilonotes.infra.util.AppUtils

class NoteOrSnippetEntryView : ConstraintLayout {

    lateinit var binding: ViewNoteOrSnippetEntryBinding

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        isClickable = true
        binding = ViewNoteOrSnippetEntryBinding.inflate(LayoutInflater.from(context), this, true)
        initView()
        refreshSelectedBtn()
    }

    var isNoteMode = true
    var isNoteSelectModeClickCallback: ((Boolean) -> Unit)? = null

    private fun initView() {
        binding.noteSelectMode.setOnClickListener(AntiShakeClickListener {
            isNoteSelectModeClickCallback?.invoke(true)
        })
        binding.snippetSelectMode.setOnClickListener(AntiShakeClickListener {
            isNoteSelectModeClickCallback?.invoke(false)
        })
    }

    private fun refreshSelectedBtn() {
        binding.noteBtnIcon.isSelected = isNoteMode
        val noteBtnTipColor = if (isNoteMode) {
            AppUtils.getColor(R.color.note_text_tip_selected_color)
        } else {
            Color.BLACK
        }
        binding.noteBtnTip.setTextColor(noteBtnTipColor)

        binding.snippetBtnIcon.isSelected = !isNoteMode
        val snippetBtnTipColor = if (isNoteMode) {
            Color.BLACK
        } else {
            AppUtils.getColor(R.color.note_text_tip_selected_color)
        }
        binding.snippetBtnTip.setTextColor(snippetBtnTipColor)
    }

    fun setCurrentIsNoteMode(isNoteMode: Boolean) {
        this.isNoteMode = isNoteMode
        refreshSelectedBtn()
    }

    fun setupBlurView(rootView: ViewGroup) {
        binding.root.setupWith(rootView)
            .setBlurRadius(23F)
    }

}


package com.topstack.kilonotes.pad.select

import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.BubbleLayout
import com.topstack.kilonotes.databinding.NoteSnippetTagOptionWindowBinding

class SnippetTagOptionWindow(val context: Context) : PopupWindow() {
    private val binding: NoteSnippetTagOptionWindowBinding by lazy {
        NoteSnippetTagOptionWindowBinding.inflate(LayoutInflater.from(context))
    }

    private var onEditClickListener: (() -> Unit)? = null
    private var onDeleteClickListener: (() -> Unit)? = null

    init {
        height = context.resources.getDimensionPixelSize(R.dimen.dp_170)
        width = context.resources.getDimensionPixelSize(R.dimen.dp_300)
        isFocusable = true
        isOutsideTouchable = true
        binding.snippetTagEdit.setOnClickListener {
            onEditClickListener?.invoke()
        }
        binding.snippetTagDelete.setOnClickListener {
            onDeleteClickListener?.invoke()
        }
    }

    fun setOnEditClickListener(action: () -> Unit) {
        onEditClickListener = action
    }

    fun setOnDeleteClickListener(action: () -> Unit) {
        onDeleteClickListener = action
    }

    fun showAsBubble(view: View) {
        val context = view.context
        val viewLocation = IntArray(2)
        view.getLocationOnScreen(viewLocation)
        val bubbleLegWidth = context.resources.getDimension(R.dimen.dp_15)
        val bubbleLegHeight = context.resources.getDimension(R.dimen.dp_30)
        val shadowRadius = context.resources.getDimension(R.dimen.dp_10)
        val bubbleLegOffset =
            viewLocation[1] + view.height.toFloat() / 2 - bubbleLegHeight / 2 + shadowRadius
        val bubbleLayout =
            BubbleLayout(
                view.context,
                bubbleLegOffset,
                bubbleLegWidth,
                bubbleLegHeight,
                BubbleLayout.BubbleOrientation.LEFT,
                context.resources.getDimension(R.dimen.dp_20),
                Color.WHITE,
                shadowRadius,
                BubbleLayout.BubbleType.TRIANGLE
            )
        bubbleLayout.addView(binding.root)
        view.getLocationInWindow(viewLocation)
        measureSnippetTagTitleView(binding.snippetTagDelete)
        measureSnippetTagTitleView(binding.snippetTagEdit)
        height = binding.snippetTagDelete.measuredHeight + binding.snippetTagDelete.measuredHeight
        contentView = bubbleLayout
        showAtLocation(
            view,
            Gravity.NO_GRAVITY,
            viewLocation[0] + view.width,
            viewLocation[1] + (view.height - height) / 2
        )
    }

    private val maximumMeasureSpec = (1 shl 30) - 1

    private fun measureSnippetTagTitleView(view: View) {
        val widthMeasureSpec =
            View.MeasureSpec.makeMeasureSpec(
                context.resources.getDimensionPixelSize(R.dimen.dp_268),
                View.MeasureSpec.AT_MOST
            )
        val heightMeasureSpec =
            View.MeasureSpec.makeMeasureSpec(maximumMeasureSpec, View.MeasureSpec.AT_MOST)
        view.measure(widthMeasureSpec, heightMeasureSpec)
    }
}
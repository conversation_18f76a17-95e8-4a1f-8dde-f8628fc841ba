package com.topstack.kilonotes.pad.note.adapter

import android.animation.ValueAnimator
import android.app.Activity
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.BitmapFactory.Options
import android.graphics.Matrix
import android.graphics.Rect
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.BackgroundColorSpan
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import androidx.core.animation.doOnEnd
import androidx.core.content.FileProvider
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.bumptech.glide.Glide
import com.bumptech.glide.signature.ObjectKey
import com.topstack.kilonotes.BuildConfig
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.doodle.views.snippetview.SnippetView
import com.topstack.kilonotes.base.drag.startDragShareHandWrite
import com.topstack.kilonotes.base.drag.startDragShareImage
import com.topstack.kilonotes.base.drag.startDragShareText
import com.topstack.kilonotes.base.note.snippet.SnippetManager
import com.topstack.kilonotes.base.note.snippet.SnippetType
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippetItem
import com.topstack.kilonotes.base.track.event.SnippetEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.WindowInsetsUtils
import com.topstack.kilonotes.databinding.ItemSnippetBinding
import com.topstack.kilonotes.debug.DebugManager
import com.topstack.kilonotes.infra.util.AppUtils
import java.io.File

class NoteSnippetAdapter(val context: Context) :
    PagingDataAdapter<NoteSnippetItem, NoteSnippetAdapter.SnippetViewHolder>(
        object : DiffUtil.ItemCallback<NoteSnippetItem>() {
            override fun areItemsTheSame(
                oldItem: NoteSnippetItem,
                newItem: NoteSnippetItem,
            ): Boolean {
                return oldItem.snippet.snippetId == newItem.snippet.snippetId
            }

            override fun areContentsTheSame(
                oldItem: NoteSnippetItem,
                newItem: NoteSnippetItem,
            ): Boolean {
                return oldItem == newItem
            }

        }
    ) {

    private val TAG = "NoteSnippetAdapter"

    private var snippetMinHeight = context.resources.getDimensionPixelSize(R.dimen.dp_50)
    private var snippetCollapseHeight = context.resources.getDimensionPixelSize(R.dimen.dp_150)
    private var snippetImgDefaultWidth = context.resources.getDimensionPixelSize(R.dimen.dp_380)
    private var snippetDefaultWidth = context.resources.getDimensionPixelSize(R.dimen.dp_420)
    private var snippetTitleHeight = context.resources.getDimensionPixelOffset(R.dimen.dp_86)
    private val ZOOM_ANIMATOR_DURATION = 300L

    var snippetClickCallback: ((Int, View) -> Unit)? = null
    var snippetTitleChangedCallback: ((NoteSnippetItem) -> Unit)? = null
    var snippetListIsExpendCallback: (() -> Boolean)? = null
    var currentSelectedPosition = -1
    private var recyclerView: RecyclerView? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewEType: Int): SnippetViewHolder {
        val snippetViewHolder =
            SnippetViewHolder(ItemSnippetBinding.inflate(LayoutInflater.from(context)))
        snippetViewHolder.title.setOnDragListener { v, event ->
            true
        }
        return snippetViewHolder
    }

    override fun onBindViewHolder(holder: SnippetViewHolder, position: Int) {
        val snippetItem = getItem(position) ?: return
        bindDrag(holder, position)
        val isClicked = isClicked(position)
        holder.snippetBg.background = if (isClicked) {
            if (snippetListIsExpendCallback?.invoke() == true) {
                AppUtils.getDrawable(R.drawable.note_snippet_expand_click_bg)
            } else {
                AppUtils.getDrawable(R.drawable.note_snippet_collapse_click_bg)
            }
        } else {
            null
        }
        holder.snippetStroke.isSelected = snippetItem.isSelected
        holder.snippetTitleBg.setBackgroundColor(snippetItem.snippet.color)
        val titleTextColor = when (snippetItem.snippet.color) {
            AppUtils.getColor(R.color.snippet_color_yellow) -> AppUtils.getColor(R.color.note_snippet_title_with_yellow_background_color)
            AppUtils.getColor(R.color.snippet_color_pink) -> AppUtils.getColor(R.color.note_snippet_title_with_pink_background_color)
            AppUtils.getColor(R.color.snippet_color_green) -> AppUtils.getColor(R.color.note_snippet_title_with_green_background_color)
            else -> AppUtils.getColor(R.color.note_snippet_title_with_blue_background_color)
        }

        if (position != currentSelectedPosition) {
            holder.title.apply {
                isCursorVisible = false
                isFocusable = false
                isFocusableInTouchMode = false
            }
            holder.snippetBg.background = null
        } else {
            holder.title.apply {
                isCursorVisible = true
                isFocusable = true
                isFocusableInTouchMode = true
            }
            holder.snippetBg.background = if (snippetListIsExpendCallback?.invoke() == true) {
                AppUtils.getDrawable(R.drawable.note_snippet_expand_click_bg)
            } else {
                AppUtils.getDrawable(R.drawable.note_snippet_collapse_click_bg)
            }
        }
        fun getHighlightedTitle(): CharSequence {
            val snippetTitle = SpannableString(snippetItem.snippet.title)
            if (snippetTitle.isNotEmpty() && snippetItem.searchKeyword.isNotEmpty()) {
                val startIndex = snippetTitle.indexOf(snippetItem.searchKeyword, ignoreCase = true)
                if (startIndex >= 0) {
                    val highlight =
                        BackgroundColorSpan(AppUtils.getColor(R.color.snippet_search_highlight_background))
                    snippetTitle.setSpan(
                        highlight,
                        startIndex,
                        startIndex + snippetItem.searchKeyword.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
            return snippetTitle
        }

        fun getHighlightedContent(): CharSequence {
            val snippetContent = SpannableString(snippetItem.snippet.text)
            val searchKeyword = snippetItem.searchKeyword
            if (snippetContent.isNotEmpty() && searchKeyword.isNotEmpty()) {
                var startIndex = snippetContent.indexOf(searchKeyword, ignoreCase = true)
                while (startIndex >= 0) {
                    val highlight = BackgroundColorSpan(
                        AppUtils.getColor(R.color.snippet_search_highlight_background)
                    )
                    snippetContent.setSpan(
                        highlight,
                        startIndex,
                        startIndex + searchKeyword.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    startIndex = snippetContent.indexOf(
                        searchKeyword,
                        startIndex + searchKeyword.length,
                        ignoreCase = true
                    )
                }
            }
            return snippetContent
        }

        holder.title.apply {
            isCursorVisible = isClicked
            isFocusable = isClicked
            isFocusableInTouchMode = isClicked
            hint = getHighlightedTitle()
            setPadding(
                resources.getDimension(R.dimen.dp_0).toInt(),
                paddingTop,
                resources.getDimension(R.dimen.dp_0).toInt(),
                paddingBottom
            )
            setHintTextColor(titleTextColor)
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    hint = null
                    holder.snippetEmptyTitle.isVisible = false
                    setPadding(
                        resources.getDimension(R.dimen.dp_12).toInt(),
                        paddingTop,
                        resources.getDimension(R.dimen.dp_12).toInt(),
                        paddingBottom
                    )
                    text = Editable.Factory.getInstance().newEditable(snippetItem.snippet.title)
                    WindowInsetsUtils.showSoftKeyBoard(this)
                } else {
                    finishEdit(snippetItem, holder)
                    text = null
                    hint = getHighlightedTitle()
                    holder.snippetEmptyTitle.isVisible = snippetItem.snippet.title.isEmpty()
                    setPadding(
                        resources.getDimension(R.dimen.dp_0).toInt(),
                        paddingTop,
                        resources.getDimension(R.dimen.dp_0).toInt(),
                        paddingBottom
                    )
                }
            }
            setOnKeyListener { _, keyCode, _ ->
                if (keyCode == KeyEvent.KEYCODE_ENTER) {
                    finishEdit(snippetItem, holder)
                    true
                } else {
                    false
                }
            }
        }
        holder.snippetEmptyTitle.apply {
            isVisible = snippetItem.snippet.title.isEmpty() && !holder.snippetEmptyTitle.hasFocus()
            setOnClickListener {
                if (isClicked(holder.bindingAdapterPosition) && !holder.title.hasFocus()) {
                    holder.snippetEmptyTitle.isVisible = false
                    holder.title.requestFocus()
                }
            }
        }
        if (DebugManager.isDebugMode()) {
            holder.recognitionContent.visibility = View.VISIBLE
            holder.recognitionContent.text = snippetItem.snippet.textRecognitionResult?.symbols
        }

        if (snippetItem.snippet.snippetType == SnippetType.TEXT) {
            holder.snippetTextContent.text = getHighlightedContent()
            holder.snippetTextContent.visibility = View.VISIBLE
            holder.snippetImg.visibility = View.GONE

            holder.snippetTextContent.measure(
                View.MeasureSpec.makeMeasureSpec(snippetImgDefaultWidth, View.MeasureSpec.EXACTLY),
                View.MeasureSpec.UNSPECIFIED
            )
            val textHeight = holder.snippetTextContent.measuredHeight
            val snippetContentHeight = textHeight

            val lp = holder.snippetTextContent.layoutParams
            if (snippetContentHeight > snippetCollapseHeight) {
                holder.zoomBtn.visibility = View.VISIBLE
                if (snippetItem.isExpand) {
                    lp.height = snippetContentHeight
                } else {
                    lp.height = snippetCollapseHeight
                }
            } else {
                holder.zoomBtn.visibility = View.GONE
                lp.height = maxOf(snippetContentHeight, snippetMinHeight)
            }
            holder.snippetTextContent.layoutParams = lp

            holder.zoomBtn.isSelected = snippetItem.isExpand
            holder.zoomBtn.setOnClickListener(AntiShakeClickListener {
                val currentPosition = holder.bindingAdapterPosition
                if (currentPosition < 0 || currentPosition >= itemCount) return@AntiShakeClickListener
                val currentSnippet =
                    getItem(currentPosition) ?: return@AntiShakeClickListener
                holder.itemView.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
                SnippetEvent.sendSnippetCardSpreadBtnClick()
                currentSnippet.isExpand = !currentSnippet.isExpand
                val targetHeight = if (currentSnippet.isExpand) {
                    snippetContentHeight
                } else {
                    snippetCollapseHeight
                }
                zoomAnimation(currentPosition, targetHeight, holder) {
                    holder.zoomBtn.isSelected = currentSnippet.isExpand
                    notifyItemChanged(currentPosition)
                }
            })

            if (snippetItem.isExpand) {
                holder.itemView.layoutParams = ViewGroup.LayoutParams(
                    snippetDefaultWidth,
                    snippetTitleHeight + snippetContentHeight
                )
            } else {
                holder.itemView.layoutParams = ViewGroup.LayoutParams(
                    snippetDefaultWidth,
                    if (snippetContentHeight > snippetCollapseHeight) {
                        snippetTitleHeight + snippetCollapseHeight
                    } else {
                        snippetTitleHeight + maxOf(snippetContentHeight, snippetMinHeight)
                    }
                )
            }

            holder.snippetTextContent.setOnClickListener {
                if (isClicked(holder.bindingAdapterPosition) && holder.title.hasFocus()) {
                    finishEdit(snippetItem, holder)
                } else {
                    snippetClickCallback?.invoke(
                        holder.bindingAdapterPosition,
                        holder.snippetTextContent
                    )
                    if (position >= itemCount) return@setOnClickListener
                }
            }
        } else {
            holder.snippetTextContent.text = null
            holder.snippetTextContent.visibility = View.GONE
            holder.snippetImg.visibility = View.VISIBLE

            val lp = holder.snippetImg.layoutParams
            val snippetImgFile =
                File(SnippetManager.getSnippetImageAbsoluteFilePath(snippetItem.snippet))
            var snippetImgHeight = 0
            var scale = 1F
            if (snippetImgFile.exists()) {
                val options = Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeFile(snippetImgFile.absolutePath, options)
                holder.snippetImg.setImageOriginalSize(options.outWidth, options.outHeight)
                if (options.outWidth != 0) {
                    snippetImgHeight = snippetImgDefaultWidth * options.outHeight / options.outWidth
                    val screenDimensions = DimensionUtil.getScreenDimensions(context)
                    val currentAppHeight = screenDimensions.heightPixels
                    if (snippetImgHeight > currentAppHeight) {
                        scale = snippetImgHeight.toFloat() / currentAppHeight
                    }
                }
            }
            val matrix = Matrix()
            matrix.postScale(scale, scale)
            holder.snippetImg.imageMatrix = matrix

            val contentHighlightRanges = snippetItem.searchResultContentMatchedRanges
            val symbolsRectList = snippetItem.snippet.textRecognitionResult?.symbolsRectList
            val contentHighlights = mutableListOf<Rect>()
            if (symbolsRectList != null && contentHighlightRanges.isNotEmpty()) {
                contentHighlightRanges.forEach {
                    val contentHighlightStart = it.first.coerceIn(symbolsRectList.indices)
                    val contentHighlightEnd = it.last.coerceIn(symbolsRectList.indices)
                    for (i in contentHighlightStart..contentHighlightEnd) {
                        contentHighlights.add(Rect(symbolsRectList[i]))
                    }
                }
            }
            holder.snippetImg.highlights = contentHighlights

            if (snippetImgHeight > snippetCollapseHeight) {
                holder.zoomBtn.visibility = View.VISIBLE
                if (snippetItem.isExpand) {
                    lp.height = snippetImgHeight
                } else {
                    lp.height = snippetCollapseHeight
                }
            } else {
                holder.zoomBtn.visibility = View.GONE
                lp.height = maxOf(snippetImgHeight, snippetMinHeight)
            }
            holder.snippetImg.layoutParams = lp

            holder.zoomBtn.isSelected = snippetItem.isExpand
            holder.zoomBtn.setOnClickListener(AntiShakeClickListener {
                val currentPosition = holder.bindingAdapterPosition
                if (currentPosition < 0 || currentPosition >= itemCount) return@AntiShakeClickListener
                val currentSnippet = getItem(currentPosition) ?: return@AntiShakeClickListener
                holder.itemView.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
                SnippetEvent.sendSnippetCardSpreadBtnClick()
                currentSnippet.isExpand = !currentSnippet.isExpand
                val targetHeight = if (currentSnippet.isExpand) {
                    snippetImgHeight
                } else {
                    snippetCollapseHeight
                }
                zoomAnimation(currentPosition, targetHeight, holder) {
                    holder.zoomBtn.isSelected = currentSnippet.isExpand
                    notifyItemChanged(currentPosition)
                }
            })

            val snippetImagePath =
                SnippetManager.getSnippetImageAbsoluteFilePath(snippetItem.snippet)

            if (isValidContextForGlide(context)) {
                Glide.with(context)
                    .asBitmap()
                    .load(snippetImagePath)
                    .override(
                        (snippetImgDefaultWidth / scale).toInt(),
                        (snippetImgHeight / scale).toInt()
                    )
                    .signature(ObjectKey(File(snippetImagePath).lastModified()))
                    .into(holder.snippetImg)
            }

            //viewHolder复用导致itemView高度展示有误，重新设置其高度
            if (snippetItem.isExpand) {
                holder.itemView.layoutParams = ViewGroup.LayoutParams(
                    snippetDefaultWidth,
                    snippetTitleHeight + snippetImgHeight
                )
            } else {
                holder.itemView.layoutParams = ViewGroup.LayoutParams(
                    snippetDefaultWidth,
                    if (snippetImgHeight > snippetCollapseHeight) {
                        snippetTitleHeight + snippetCollapseHeight
                    } else {
                        snippetTitleHeight + maxOf(snippetImgHeight, snippetMinHeight)
                    }
                )
            }

            holder.snippetImg.setOnClickListener {
                if (isClicked(holder.bindingAdapterPosition) && holder.title.hasFocus()) {
                    finishEdit(snippetItem, holder)
                } else {
                    snippetClickCallback?.invoke(holder.bindingAdapterPosition, holder.snippetImg)
                    if (position >= itemCount) return@setOnClickListener
                }
            }
        }


        holder.snippet.setOnClickListener {
            if (isClicked(holder.bindingAdapterPosition) && holder.title.hasFocus()) {
                finishEdit(snippetItem, holder)
            } else {
                snippetClickCallback?.invoke(holder.bindingAdapterPosition, holder.snippetImg)
                if (position >= itemCount) return@setOnClickListener
            }
        }

        if (KiloApp.isLayoutRtl) {
            holder.snippetEmptyTitle.scaleType = ImageView.ScaleType.FIT_END
        } else {
            holder.snippetEmptyTitle.scaleType = ImageView.ScaleType.FIT_START
        }

    }

    private fun bindDrag(holder: SnippetViewHolder, position: Int) {
        val snippetItem = getItem(position) ?: return
        when (snippetItem.snippet.snippetType) {
            SnippetType.IMAGE, SnippetType.CAMERA -> {
                holder.snippetImg.startDragShareImage(SnippetView.ADD_IMAGE_LABEL) {
                    if (position < 0 && position > itemCount - 1) return@startDragShareImage null
                    val imagePath =
                        SnippetManager.getSnippetImageAbsoluteFilePath(snippetItem.snippet)
                    val imageFile = File(imagePath)
                    if (!imageFile.exists()) return@startDragShareImage null
                    val uri = FileProvider.getUriForFile(
                        context,
                        BuildConfig.APPLICATION_ID + ".provider",
                        imageFile
                    )
                    uri
                }
            }

            SnippetType.TEXT -> {
                holder.snippetTextContent.startDragShareText(SnippetView.ADD_IMAGE_LABEL) {
                    snippetItem.snippet.text
                }
            }

            SnippetType.DOODLE -> {
                holder.snippetImg.startDragShareHandWrite(SnippetView.ADD_IMAGE_LABEL) {
                    snippetItem.snippet.snippetId
                }
            }
        }

    }

    private fun isClicked(position: Int): Boolean {
        return position == currentSelectedPosition
    }

    private fun isValidContextForGlide(context: Context?): Boolean {
        if (context == null) {
            return false
        }
        if (context is Activity) {
            val activity = context
            if (activity.isDestroyed || activity.isFinishing) {
                return false
            }
        }
        return true
    }

    private fun finishEdit(snippetItem: NoteSnippetItem, holder: SnippetViewHolder) {
        snippetItem.snippet.title = holder.title.text.toString()
        snippetTitleChangedCallback?.invoke(snippetItem)
        holder.title.hint = snippetItem.snippet.title
        removeFocus(holder.title)
        holder.snippetEmptyTitle.isVisible = snippetItem.snippet.title.isEmpty()
    }

    private fun removeFocus(editText: EditText) {
        val inputManager =
            context.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
        inputManager?.hideSoftInputFromWindow(
            editText.windowToken,
            0
        )
        editText.clearFocus()
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        this.recyclerView = null
    }

    fun refreshItemByPosition(position: Int) {
        if (position in 0 until itemCount) {
            notifyItemChanged(position)
        }
    }

    fun refreshCurrentSelectedSnippet(position: Int) {
        val oldPosition = currentSelectedPosition
        currentSelectedPosition = position
        if (oldPosition != -1) {
            notifyItemChanged(oldPosition)
        }
        if (currentSelectedPosition != -1) {
            notifyItemChanged(currentSelectedPosition)
        }
    }

    fun zoomAnimation(
        position: Int,
        targetHeight: Int,
        holder: SnippetViewHolder,
        animatorFinished: () -> Unit,
    ) {
        val currentItemHolder =
            recyclerView?.findViewHolderForAdapterPosition(position) as? SnippetViewHolder
        val currentItemView = currentItemHolder?.itemView ?: return
        val snippetImg = currentItemHolder.snippetImg
        val peerItemView = if (position % 2 == 0) {
            if (position + 1 in 0 until itemCount) {
                val peerItemHolder =
                    recyclerView?.findViewHolderForAdapterPosition(position + 1) as? SnippetViewHolder
                peerItemHolder?.itemView
            } else {
                null
            }
        } else {
            if (position - 1 in 0 until itemCount) {
                val peerItemHolder =
                    recyclerView?.findViewHolderForAdapterPosition(position - 1) as? SnippetViewHolder
                peerItemHolder?.itemView
            } else {
                null
            }
        }
        val snippetImgHeight = snippetImg.height
        val animatorArgs = intArrayOf(
            snippetImgHeight,
            targetHeight
        )
        ValueAnimator.ofInt(
            *animatorArgs
        ).apply {
            duration = ZOOM_ANIMATOR_DURATION
            addUpdateListener {
                val snippetImgLp = snippetImg.layoutParams
                snippetImgLp.height = it.animatedValue as Int
                snippetImg.layoutParams = snippetImgLp
                currentItemView.requestLayout()
                peerItemView?.requestLayout()
            }
            doOnEnd {
                animatorFinished.invoke()
            }
        }.start()
    }

    fun removeEditTextFocus() {
        recyclerView?.layoutManager?.focusedChild?.clearFocus()
    }

    fun getAllItems(): List<NoteSnippetItem> {
        return snapshot().items
    }

    inner class SnippetViewHolder(binding: ItemSnippetBinding) : ViewHolder(binding.root) {
        val snippetBg = binding.snippetBg
        val snippet = binding.snippet
        val snippetStroke = binding.snippetStroke
        val snippetTitleBg = binding.snippetTitleBg
        val title = binding.snippetTitle
        val snippetEmptyTitle = binding.snippetEmptyTitle
        val snippetImg = binding.snippetImg
        val snippetTextContent = binding.snippetTextContent
        val zoomBtn = binding.zoomBtn
        val recognitionContent = binding.recognitionContent

        init {
            itemView.layoutParams = ViewGroup.LayoutParams(
                snippetDefaultWidth,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        }
    }
}
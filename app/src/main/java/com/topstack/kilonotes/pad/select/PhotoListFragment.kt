package com.topstack.kilonotes.pad.select

import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.imagefetch.ImageFetcher
import com.topstack.kilonotes.base.note.decoration.NoteGridSpacingItemDecoration
import com.topstack.kilonotes.base.select.BUNDLE_ALBUM_AID_KEY
import com.topstack.kilonotes.base.select.BUNDLE_ALPHA_KEY
import com.topstack.kilonotes.base.select.BUNDLE_URI_KEY
import com.topstack.kilonotes.base.select.BasePhotoCropDialogFragment
import com.topstack.kilonotes.base.select.PICK_PIC_RESULT_CODE
import com.topstack.kilonotes.databinding.FragmentPhotoListBinding
import com.topstack.kilonotes.pad.select.adapter.AlbumListAdapter
import com.topstack.kilonotes.pad.select.adapter.PhotoListAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class PhotoListFragment : BaseFragment(R.layout.fragment_photo_list),
    BasePhotoCropDialogFragment.IOnCropCompleteListener {
    private val PHOTO_SPAN_COUNT = 5
    private val ALBUM_SPAN_COUNT = 3
    private val CHECK_STATUE_KEY = "checkStatue"

    private var checkStatue = true

    private lateinit var binding: FragmentPhotoListBinding

    private val selectPhotoViewModel: PadSelectPhotoViewModel by activityViewModels()

    private val imageFetcher: ImageFetcher by lazy {
        ImageFetcher(requireContext())
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        savedInstanceState?.getBoolean(CHECK_STATUE_KEY)?.let {
            checkStatue = it
        }
        binding = FragmentPhotoListBinding.bind(view)
        if (savedInstanceState != null) {
            val cropDialog =
                parentFragmentManager.findFragmentByTag(BasePhotoCropDialogFragment.TAG)
            if (cropDialog is PhotoCropDialogFragment) {
                cropDialog.setOnCropCompleteListener(this)
            }
        }
        initView()
        initListener()

    }

    private fun initView() {
        refreshRecycleView()
        val decoration =
            NoteGridSpacingItemDecoration(
                resources.getDimension(R.dimen.dp_2).toInt()
            )
        binding.photoRv.addItemDecoration(decoration)
    }

    private fun refreshRecycleView() {
        val gridLayoutManager = GridLayoutManager(requireContext(), getSpanCount())
        binding.photoRv.stopScroll()
        coroutineScope.launch(Dispatchers.IO) {
            if (checkStatue) {
                val allImages = imageFetcher.getAllImages()
                withContext(Dispatchers.Main) {
                    //https://stackoverflow.com/questions/30220771/recyclerview-inconsistency-detected-invalid-item-position
                    //这里本身就需要通过切换adapter来达到切换列表的目的
                    //所以recycledViewPool.clear() 在此业务下，并不会导致缓存失效
                    binding.photoRv.recycledViewPool.clear()
                    binding.photoRv.adapter = PhotoListAdapter(allImages, requireContext()) {
                        if (selectPhotoViewModel.ignoreDetailImage) {
                            val activity = requireActivity()
                            val intent = activity.intent
                            intent.putExtra(BUNDLE_URI_KEY, it.uri)
                            activity.setResult(PICK_PIC_RESULT_CODE, intent)
                            activity.finish()
                            return@PhotoListAdapter
                        }
                        val bundle = Bundle()
                        bundle.putParcelable(BUNDLE_URI_KEY, it.uri)
                        if (selectPhotoViewModel.needCropImage) {
                            if (selectPhotoViewModel.needSelectRatio) {
                                findNavController().navigate(
                                    R.id.pick_and_selectable_fixed_crop_photo_fragment,
                                    bundle
                                )
                            } else {
                                findNavController().navigate(
                                    R.id.pick_and_crop_photo_fragment,
                                    bundle
                                )
                            }
                        } else if (selectPhotoViewModel.needCropAndChangeAlpha) {
                            val photoCropDialogFragment = PhotoCropDialogFragment().apply {
                                photoUri = it.uri
                                setOnCropCompleteListener(this@PhotoListFragment)
                            }
                            photoCropDialogFragment.show(
                                parentFragmentManager,
                                BasePhotoCropDialogFragment.TAG
                            )
                        } else {
                            findNavController().navigate(R.id.pickPhotoFragment, bundle)
                        }
                    }
                }
            } else {
                val albums = imageFetcher.getAlbums()
                withContext(Dispatchers.Main) {
                    binding.photoRv.recycledViewPool.clear()
                    binding.photoRv.adapter = AlbumListAdapter(albums, requireContext()) {
                        val bundle = Bundle()
                        bundle.putString(BUNDLE_ALBUM_AID_KEY, it.aid)
                        findNavController().navigate(R.id.albumPhotoListFragment, bundle)
                    }
                }
            }
        }

        binding.photoRv.layoutManager = gridLayoutManager
        binding.photoTv.isSelected = checkStatue
        binding.albumTv.isSelected = !checkStatue
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(CHECK_STATUE_KEY, checkStatue)
    }

    private fun initListener() {
        binding.cancelTv.setOnClickListener {
            requireActivity().finish()
        }
        binding.photoTv.setOnClickListener {
            if (!checkStatue) {
                checkStatue = true
            }
            refreshRecycleView()
        }
        binding.albumTv.setOnClickListener {
            if (checkStatue) {
                checkStatue = false
            }
            refreshRecycleView()
        }
    }

    private fun getSpanCount(): Int {
        return if (checkStatue) PHOTO_SPAN_COUNT
        else ALBUM_SPAN_COUNT
    }

    override fun onCropSuccess(uri: Uri, imageAlpha: Int) {
        val requireActivity = requireActivity()
        val intent = requireActivity.intent
        intent.putExtra(BUNDLE_URI_KEY, uri)
        intent.putExtra(BUNDLE_ALPHA_KEY, imageAlpha)
        requireActivity.setResult(PICK_PIC_RESULT_CODE, intent)
        requireActivity.finish()
    }

    override fun onCropFailed() {
    }

    override fun onCancel() {
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (requireActivity().isChangingConfigurations) {
            binding.photoRv.stopScroll()
        }
    }

}
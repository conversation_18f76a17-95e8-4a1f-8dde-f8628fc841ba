package com.topstack.kilonotes.pad.component

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.constraintlayout.motion.widget.TransitionAdapter
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.paging.LoadState
import androidx.paging.PagingData
import androidx.recyclerview.widget.GridLayoutManager
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippetItem
import com.topstack.kilonotes.base.note.snippet.data.SnippetTag
import com.topstack.kilonotes.base.track.event.SearchEvent
import com.topstack.kilonotes.base.track.event.SnippetEvent
import com.topstack.kilonotes.base.util.WindowInsetsUtils
import com.topstack.kilonotes.databinding.NotePageSnippetLayoutBinding
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.pad.note.adapter.NoteSnippetAdapter

class SnippetLayout : ConstraintLayout {

    companion object {
        const val COLLAPSE_SPAN_COUNT = 1
        const val EXPAND_SPAN_COUNT = 2
        const val NO_TRANSLATION_Y = 0F
        const val NO_SELECTED_SNIPPET = 0
        const val DELAY_FINISH_TIME = 500
    }

    lateinit var binding: NotePageSnippetLayoutBinding

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        binding = NotePageSnippetLayoutBinding.inflate(LayoutInflater.from(context), this, true)
    }

    var optionRoomClick: ((Boolean) -> Unit)? = null
    var optionSelectClick: ((Boolean) -> Unit)? = null
    var optionAddLabelClick: (() -> Unit)? = null
    var snippetClickCallback: ((Int, View) -> Unit)? = null
    var snippetTitleChangedCallback: ((NoteSnippetItem) -> Unit)? = null
    var noteSnippetAdapter: NoteSnippetAdapter? = null
    var onSearchModeSwitch: ((inSearchMode: Boolean) -> Unit)? = null
    var onSearchTextChanged: ((searchKeyword: String) -> Unit)? = null
    var onSearchBoxFocusChanged: ((focused: Boolean) -> Unit)? = null
    var refreshSnippetListCallback: (() -> Unit)? = null

    private var searchBoxFocused = false

    private var isColorSelectorAndLabelSelectorItemClickable = true
        set(value) {
            if (field != value) {
                field = value
                binding.labelSelector.setIsItemClickable(value)
                binding.colorSelector.setIsItemClickable(value)
            }
        }

    override fun onFinishInflate() {
        super.onFinishInflate()
        initView()
    }

    private fun initView() {
        binding.optionZoom.setOnClickListener(AntiShakeClickListener {
            if (binding.optionZoom.isSelected) {
                SnippetEvent.sendSnippetLayoutPackUpBtnClick()
            } else {
                SnippetEvent.sendSnippetLayoutSpreadBtnClick()
            }
            binding.optionZoom.isSelected = !binding.optionZoom.isSelected
            optionRoomClick?.invoke(binding.optionZoom.isSelected)
        })
        binding.optionSelect.setOnClickListener(AntiShakeClickListener {
            SnippetEvent.sendSnippetOptionSelectBtnClick()
            binding.optionSelect.isSelected = !binding.optionSelect.isSelected
            binding.optionZoom.isSelected = true
            optionSelectClick?.invoke(binding.optionSelect.isSelected)
        })
        binding.selectFinishBtn.setOnClickListener(AntiShakeClickListener {
            binding.optionSelect.isSelected = !binding.optionSelect.isSelected
            binding.optionZoom.isSelected = false
            optionSelectClick?.invoke(binding.optionSelect.isSelected)
        })
        binding.optionAddLabel.setOnClickListener(AntiShakeClickListener {
            if (it?.isSelected == true) {
                optionAddLabelClick?.invoke()
            }
        })

        binding.searchBox.run {
            setClearIconVisibility(binding.searchBox.text.isNotEmpty())

            setOnEditTextFocusChangeListener { hasFocus ->
                if (hasFocus != searchBoxFocused) {
                    searchBoxFocused = hasFocus
                    onSearchBoxFocusChanged?.invoke(hasFocus)
                }

                if (hasFocus) {
                    enterSearchMode(searchBoxFocused)
                    SearchEvent.sendSearchButtonClickEvent(SearchEvent.LOCATION_EDIT_PAGE)
                }
            }

            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                }

                override fun afterTextChanged(s: Editable?) {
                    val text = s?.toString() ?: ""
                    binding.searchBox.setClearIconVisibility(text.isNotEmpty())

                    onSearchTextChanged?.invoke(text)
                }
            })

            setEditListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    onSearchTextChanged?.invoke(binding.searchBox.text)
                    binding.searchBox.setEditTextFocusable(false)
                    binding.searchBox.hideSoftKeyboard()
                    binding.searchBox.setEditTextFocusable(true)
                    return@setEditListener true
                }
                return@setEditListener false
            }
        }

        binding.cancelSearch.setOnClickListener {
            exitSearchMode()
        }

        binding.root.addTransitionListener(object : TransitionAdapter() {
            override fun onTransitionStarted(
                motionLayout: MotionLayout?,
                startId: Int,
                endId: Int,
            ) {
                binding.labelSelector.isAnimationRunning = true
                when (startId) {
                    R.id.search_mode -> {
                        binding.searchBox.text = ""
                    }
                }
            }

            override fun onTransitionCompleted(motionLayout: MotionLayout?, currentId: Int) {
                binding.labelSelector.isAnimationRunning = false
                binding.searchBox.setEditTextFocusable(true)
                when (currentId) {
                    R.id.non_search_mode -> {
                        binding.searchBox.setHint("")
                    }

                    R.id.search_mode -> {
                        binding.searchBox.setHint(resources.getString(R.string.snippet_search_hint))
                        if (searchBoxFocused) {
                            binding.searchBox.showSoftKeyboard()
                        } else {
                            clearSearchBoxCursor()
                        }
                    }
                }
            }
        })

        //阻止drag事件向下传递
        setOnDragListener { _, _ -> true }

        if (noteSnippetAdapter == null) {
            noteSnippetAdapter = NoteSnippetAdapter(context).apply {
                snippetClickCallback = { position, view ->
                    removeFocus(binding.snippetList.overScrollRecyclerView)
                    <EMAIL>?.invoke(position, view)
                }
                snippetTitleChangedCallback = {
                    <EMAIL>?.invoke(it)
                }
                snippetListIsExpendCallback = {
                    binding.optionZoom.isSelected
                }
            }
            binding.snippetList.overScrollRecyclerView.adapter = noteSnippetAdapter
        }

        noteSnippetAdapter?.addLoadStateListener {
            when (it.refresh) {
                is LoadState.NotLoading -> {
                    binding.snippetList.visibility = View.VISIBLE
                    refreshSnippetList()
                    refreshSnippetListCallback?.invoke()
                }

                is LoadState.Loading -> {

                }

                is LoadState.Error -> {
                    val state = it.refresh as LoadState.Error
                    binding.snippetList.visibility = View.INVISIBLE
                    binding.emptyDataView.visibility = View.VISIBLE
                }
            }
        }
    }

    suspend fun submitData(pagingData: PagingData<NoteSnippetItem>) {
        noteSnippetAdapter?.submitData(pagingData)
    }

    fun refreshPagingSource() {
        noteSnippetAdapter?.refresh()
    }

    private var inSearchMode = false

    fun enterSearchMode(searchBoxFocused: Boolean) {
        this.searchBoxFocused = searchBoxFocused
        if (!inSearchMode) {
            inSearchMode = true
            binding.root.transitionToEnd()
            onSearchModeSwitch?.invoke(true)
        }
    }

    fun exitSearchMode() {
        if (inSearchMode) {
            inSearchMode = false
            onSearchModeSwitch?.invoke(false)
            binding.searchBox.hideSoftKeyboard()
            binding.searchBox.clearFocus()
            binding.searchBox.setEditTextFocusable(false)
            binding.root.transitionToStart()
        }
        binding.searchBox.text = ""
        binding.searchBox.setClearIconVisibility(false)
    }

    fun setSearchText(text: String) {
        if (text.isEmpty() && inSearchMode) {
            binding.searchBox.setHint(resources.getString(R.string.snippet_search_hint))
        }
        if (binding.searchBox.text != text) {
            binding.searchBox.text = text
        }
    }

    fun clearSearchBoxCursor() {
        binding.searchBox.setEditTextFocusable(false)
        binding.searchBox.clearFocus()
        binding.searchBox.setEditTextFocusable(true)
    }

    fun editSnippetItemTitle(position: Int) {
        val viewHolder =
            binding.snippetList.overScrollRecyclerView.findViewHolderForAdapterPosition(position) as? NoteSnippetAdapter.SnippetViewHolder
        viewHolder?.title?.run {
            requestFocus()
            setSelection(text.length)
            post {
                WindowInsetsUtils.showSoftKeyBoard(this)
            }
        }
    }

    fun refreshSnippetList() {
        if (inSearchMode && binding.searchBox.text.isNotEmpty()) {
            binding.emptyDataView.text = resources.getString(
                R.string.snippet_have_no_search_result,
                binding.searchBox.text
            )
        } else {
            binding.emptyDataView.setText(R.string.note_snippet_empty_data_tips)
        }

        val isEmpty = noteSnippetAdapter?.itemCount == 0
        if (inSearchMode && binding.searchBox.text.isEmpty()) {
            binding.snippetList.visibility = View.VISIBLE
            binding.emptyDataView.isVisible = false
        } else {
            if (isEmpty) {
                binding.snippetList.visibility = View.INVISIBLE
            } else {
                binding.snippetList.visibility = View.VISIBLE
            }
            binding.emptyDataView.isVisible = isEmpty
        }
        if (binding.snippetList.overScrollRecyclerView.layoutManager == null) {
            refreshLayoutManager()
        }
        if (!inSearchMode) {
            removeFocus(binding.snippetList.overScrollRecyclerView)
        }
    }

    fun snippetListIsEmpty(): Boolean {
        return noteSnippetAdapter?.itemCount == 0
    }

    fun getAllSnippets(): List<NoteSnippetItem> {
        return noteSnippetAdapter?.getAllItems() ?: emptyList()
    }

    fun getItem(position: Int): NoteSnippetItem? = if (position < 0) null else noteSnippetAdapter?.peek(position)

    private fun removeFocus(view: View) {
        val inputManager =
            context.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
        inputManager?.hideSoftInputFromWindow(
            view.windowToken,
            0
        )
        view.clearFocus()
    }

    fun getTopSnippetView(): View? {
        return binding.snippetList.overScrollRecyclerView.layoutManager?.findViewByPosition(0)
    }

    fun refreshItemByPosition(position: Int) {
        noteSnippetAdapter?.refreshItemByPosition(position)
    }

    fun refreshCurrentSelectedSnippet(position: Int) {
        noteSnippetAdapter?.refreshCurrentSelectedSnippet(position)
    }

    fun refreshCurrentMode(isEdit: Boolean) {
        if (isEdit) {
            binding.snippetReadMode.visibility = View.GONE
            binding.snippetEditMode.visibility = View.VISIBLE
        } else {
            binding.snippetEditMode.visibility = View.GONE
            binding.snippetReadMode.visibility = View.VISIBLE
        }
    }

    fun refreshSelectedNumber(size: Int) {
        binding.selectedNumber.text = AppUtils.getString(R.string.note_snippet_selected_num, size)
        refreshAddLabel(size > NO_SELECTED_SNIPPET)
    }

    fun smoothScrollToTop() {
        binding.snippetList.overScrollRecyclerView.smoothScrollToPosition(0)
    }

    fun refreshRoomState(isExpand: Boolean) {
        binding.optionZoom.isSelected = isExpand
        refreshLayoutManager()
    }

    fun refreshAddLabel(canAddLabel: Boolean) {
        binding.optionAddLabel.isSelected = canAddLabel
    }

    fun softKeyboardHeightUpdate(softKeyboardHeight: Int) {
        val currentNoteSnippetPosition = noteSnippetAdapter?.currentSelectedPosition ?: -1
        if (currentNoteSnippetPosition == -1) {
            binding.snippetList.translationY = NO_TRANSLATION_Y
        } else {
            (binding.snippetList.overScrollRecyclerView.findViewHolderForAdapterPosition(
                currentNoteSnippetPosition
            ) as? NoteSnippetAdapter.SnippetViewHolder)?.let {
                if (it.title.hasFocus()) {
                    val snippetLayoutLocation = IntArray(2)
                    <EMAIL>(snippetLayoutLocation)
                    val titleLocation = IntArray(2)
                    it.snippetTitleBg.getLocationOnScreen(titleLocation)
                    val titleBottom =
                        height - (titleLocation[1] - snippetLayoutLocation[1]) - it.snippetTitleBg.height
                    if (titleBottom < softKeyboardHeight) {
                        binding.snippetList.translationY =
                            (titleBottom - softKeyboardHeight).toFloat()
                    }
                } else {
                    binding.snippetList.translationY = NO_TRANSLATION_Y
                }
            }
        }
    }

    fun removeEditTextFocus() {
        binding.snippetList.translationY = NO_TRANSLATION_Y
        noteSnippetAdapter?.removeEditTextFocus()
    }

    fun getSnippetListTranslationY(): Float {
        return binding.snippetList.translationY
    }

    private fun refreshLayoutManager() {
        val spanCount = if (binding.optionZoom.isSelected) {
            EXPAND_SPAN_COUNT
        } else {
            COLLAPSE_SPAN_COUNT
        }
        removeFocus(binding.snippetList.overScrollRecyclerView)
        binding.snippetList.overScrollRecyclerView.layoutManager =
            GridLayoutManager(context, spanCount)
    }

    fun setUpLabelSelector(
        isFold: Boolean,
        isExtended: Boolean,
        isItemClickable: Boolean,
        labelList: List<SnippetTag>,
        initialSelectedLabel: SnippetTag,
    ) {
        binding.labelSelector.setUpRecyclerView(
            isFold,
            isExtended,
            isItemClickable,
            labelList,
            initialSelectedLabel
        )
    }

    fun setUpColorSelector(
        colorList: List<Int>,
        initialSelectedColor: Int?,
        isItemClickable: Boolean,
    ) {
        binding.colorSelector.setUpRecyclerView(colorList, initialSelectedColor, isItemClickable)
    }

    fun changeLabelSelectorIsFold(isFold: Boolean) {
        binding.labelSelector.setIsFold(isFold)
    }

    fun setOnLabelSelectorIsFoldIconClickListener(action: () -> Unit) {
        binding.labelSelector.setOnFoldIconClickListener(action)
    }

    fun changeLabelSelectorIsExtended(isExtended: Boolean) {
        binding.labelSelector.setIsExtended(isExtended)
    }

    fun setOnLabelSelectorItemClickListener(action: (label: SnippetTag) -> Unit) {
        binding.labelSelector.setOnLabelClickListener(action)
    }

    fun setOnColorSelectorItemClickListener(action: (color: Int?) -> Unit) {
        binding.colorSelector.setOnItemClickListener(action)
    }

    fun changeColorSelectorIsExtended(isExtended: Boolean) {
        binding.colorSelector.setIsExtended(isExtended)
    }

    fun setOnSnippetLabelRecyclerViewLayoutChangeListener(listener: OnLayoutChangeListener) {
        binding.labelSelector.setOnSnippetLabelRecyclerViewLayoutChangeListener(listener)
    }

    fun setColorSelectorAndLabelSelectorIsClickable(isClickable: Boolean) {
        this.isColorSelectorAndLabelSelectorItemClickable = isClickable
    }

    fun setColorSelectorAndLabelSelectorSelectedPosition(label: SnippetTag, color: Int?) {
        setCurrentSelectedLabel(label)
        setCurrentSelectedColor(color)
    }

    fun setCurrentSelectedLabel(label: SnippetTag) {
        binding.labelSelector.setCurrentSelectedLabel(label)
    }

    fun setCurrentSelectedColor(color: Int?) {
        binding.colorSelector.setCurrentSelectedColor(color)
    }


    fun removeFirstRowLastPositionDecoration() {
        binding.labelSelector.removeFirstRowLastPositionDecoration()
    }

    fun setOptionSelectBtnSelectState(isSelect: Boolean) {
        binding.optionSelect.isSelected = isSelect
    }
}
package com.topstack.kilonotes.pad.select.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.bumptech.glide.Glide
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.imagefetch.model.Album
import com.topstack.kilonotes.base.util.findActivity
import com.topstack.kilonotes.databinding.PadImageBubbleAlbumItemBinding

/**
 *
 */
class BubbleAlbumListAdapter :
    ListAdapter<Album, BubbleAlbumItemViewHolder>(object : DiffUtil.ItemCallback<Album>() {
        override fun areItemsTheSame(oldItem: Album, newItem: Album): Boolean =
            oldItem.aid == newItem.aid

        override fun areContentsTheSame(oldItem: Album, newItem: Album): Boolean =
            oldItem == newItem

    }) {

    private var recyclerView: RecyclerView? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        this.recyclerView = null
    }

    var selectedAlbum: Album? = null
        set(value) {
            field = value
            if (lastSelectedPosition >= 0) {
                notifyItemChanged(lastSelectedPosition)
            }
        }

    private var lastSelectedPosition = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BubbleAlbumItemViewHolder {
        val viewHeight = parent.context.resources.getDimensionPixelSize(R.dimen.dp_100)
        val binding = PadImageBubbleAlbumItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        binding.root.layoutParams = RecyclerView.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            viewHeight
        )
        return BubbleAlbumItemViewHolder(binding, viewHeight)
    }

    override fun onBindViewHolder(holder: BubbleAlbumItemViewHolder, position: Int) {
        val album = getItem(position)
        val selected: Boolean = selectedAlbum?.let { it.aid == album.aid } ?: false
        if (selected) {
            lastSelectedPosition = holder.bindingAdapterPosition
        }
        holder.bindData(album, selected)
        holder.itemView.setOnClickListener(viewClickListener)
    }

    private val viewClickListener = View.OnClickListener { v ->
        recyclerView?.getChildViewHolder(v)?.let { viewHolder ->
            val position = viewHolder.bindingAdapterPosition
            if (position >= itemCount || position < 0) return@OnClickListener
            val album = getItem(position)
            selectedAlbum = album
            notifyItemChanged(position)

            actionOnAlbumSelected?.invoke(album)
        }
    }

    private var actionOnAlbumSelected: ((album: Album) -> Unit)? = null

    fun doOnAlbumSelected(
        action: ((album: Album) -> Unit)?
    ) {
        actionOnAlbumSelected = action
    }
}

class BubbleAlbumItemViewHolder(
    private val binding: PadImageBubbleAlbumItemBinding,
    private val viewLength: Int
) :
    ViewHolder(binding.root) {

    fun bindData(album: Album, isSelected: Boolean) {
        val activity = findActivity(binding.image.context)
        if (activity?.isDestroyed == false) {
            Glide.with(binding.image)
                .load(album.images.firstOrNull()?.uri)
                .override(viewLength, viewLength)
                .into(binding.image)

            binding.albumName.text = "${album.name}（${album.count}）"

            binding.albumSelectedIcon.isVisible = isSelected
        }
    }
}
package com.topstack.kilonotes.pad.component

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.DocumentManager
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.doodle.views.doodleview.InputMode
import com.topstack.kilonotes.base.doodle.views.doodleview.OnPageChangeListener
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.SnippetDocPreviewFragmentBinding
import com.topstack.kilonotes.notedata.NoteRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.UUID
import kotlin.math.min

/**
 *
 */
class SnippetDocPreviewFragment : BaseFragment() {
    companion object {
        const val snippetDocPreviewFragmentTag = "SnippetDocPreviewFragmentTag"
    }

    private lateinit var binding: SnippetDocPreviewFragmentBinding

    private var document: Document? = null
    private var viewingIndex = 0

    private val viewModel: SnippetDocPreviewViewModel by viewModels()

    var blurRootView: ViewGroup? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = SnippetDocPreviewFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (savedInstanceState != null) {
            document = viewModel.document
            viewingIndex = viewModel.viewingIndex
        }

        if (document == null) {
            dismiss()
            return
        }
        initView()
        binding.doodle.doodleModeConfig.enterSnippetDocPreviewMode()
        binding.doodle.doodleTouchLayer.onPageChangeListener =
            OnPageChangeListener { direction, _ ->
                when (direction) {
                    OnPageChangeListener.Direction.PREVIOUS -> {
                        if (viewingIndex > 0) {
                            viewingIndex--
                            setPage()
                            updateNextAndPrePageButtonState()
                        }
                    }

                    OnPageChangeListener.Direction.NEXT -> {
                        if (viewingIndex < document!!.pageCount - 1) {
                            viewingIndex++
                            setPage()
                            updateNextAndPrePageButtonState()
                        }
                    }

                    else -> {}
                }
            }
        setPage()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        viewModel.document = document
        viewModel.viewingIndex = viewingIndex
    }

    private fun initView() {
        blurRootView?.let {
            binding.root.setupWith(it)
                .setBlurRadius(8F)
        }
        val buttonMargin = if (useSmallButtonMargin()) {
            resources.getDimensionPixelSize(R.dimen.dp_22)
        } else {
            resources.getDimensionPixelSize(R.dimen.dp_40)
        }

        binding.close.run {
            (layoutParams as? MarginLayoutParams)?.let { params ->
                params.marginEnd = buttonMargin
                layoutParams = params
            }
        }

        binding.previousPage.run {
            (layoutParams as? MarginLayoutParams)?.let { params ->
                params.marginStart = buttonMargin
                layoutParams = params
            }
        }

        binding.nextPage.run {
            (layoutParams as? MarginLayoutParams)?.let { params ->
                params.marginEnd = buttonMargin
                layoutParams = params
            }
        }

        updateNextAndPrePageButtonState()
        binding.close.setOnClickListener(AntiShakeClickListener {
            dismiss()
        })

        binding.nextPage.setOnClickListener(AntiShakeClickListener {
            if (viewingIndex < document!!.pageCount - 1) {
                viewingIndex++
                setPage()
                updateNextAndPrePageButtonState()
            }
        })

        binding.previousPage.setOnClickListener(AntiShakeClickListener {
            if (viewingIndex > 0) {
                viewingIndex--
                setPage()
                updateNextAndPrePageButtonState()
            }
        })
    }

    private fun updateNextAndPrePageButtonState() {
        val document = document ?: return
        binding.nextPage.isVisible = viewingIndex < document.pageIds.size - 1
        binding.previousPage.isVisible = viewingIndex > 0
    }

    fun dismiss() {
        if (!isAdded) return
        parentFragmentManager.beginTransaction()
            .remove(this)
            .commit()
    }

    fun show(fragmentManager: FragmentManager) {
        fragmentManager.beginTransaction()
            .replace(
                R.id.snippet_document_preview,
                this,
                snippetDocPreviewFragmentTag
            )
            .show(this)
            .commit()
    }

    fun initDoc(
        document: Document,
        pageUUID: UUID,
    ) {
        this.document = document
        viewingIndex = document.pageIds.indexOf(pageUUID)
    }

    fun updateShowPageInfo(
        document: Document,
        pageUUID: UUID,
    ) {
        val isDifferentDoc = document != this.document
        if (isDifferentDoc) {
            initDoc(document, pageUUID)
        } else {
            viewingIndex = document.pageIds.indexOf(pageUUID)
        }

        if (isAdded) {
            setPage()
            updateNextAndPrePageButtonState()
        }
    }

    private fun setPage() {
        binding.doodle.inputMode = InputMode.VIEW
        binding.doodle.doOnSizeConfirmed {
            val currentDoc = document ?: return@doOnSizeConfirmed

            lifecycleScope.launch(Dispatchers.Main.immediate) {
                if (currentDoc.pageIds.isNotEmpty() && currentDoc.pages.isEmpty()) {
                    withContext(Dispatchers.IO) {
                        if (currentDoc.isStoredInObsoleteKiloNotesRoom()) {
                            DocumentManager.parsePagesInfo(currentDoc)
                        } else {
                            NoteRepository.inflateDocumentPages(currentDoc)
                        }
                    }
                }
                val page = currentDoc[viewingIndex]

                val maxMinScale = getMaxMinScale(page)
                val initialDoodleScale = getInitialDoodleScale(page)
                binding.doodle.doodleModeConfig.apply {
                    minScale = if (maxMinScale <= 1F) {
                        1F
                    } else {
                        initialDoodleScale.coerceIn(1F, maxMinScale)
                    }
                    maxScale = minScale + 2F
                }

                binding.doodle.doodlePageLoader.loadPage(
                    currentDoc,
                    page,
                    viewingIndex,
                    loadSidePage = true
                )
            }
        }
    }

    private fun getMaxMinScale(page: Page): Float {
        val initialPageWidthInPixel = page.initialPageWidthInPixel
        val initialPageHeightInPixel = page.initialPageHeightInPixel
        val doodleWidth = binding.doodle.width
        val doodleHeight = binding.doodle.height
        return min(
            doodleWidth / initialPageWidthInPixel.toFloat(),
            doodleHeight / initialPageHeightInPixel.toFloat()
        )
    }

    private fun getInitialDoodleScale(page: Page): Float {
        val doodleWidth = binding.doodle.width
        val doodleHeight = binding.doodle.height
        val initialPageWidthInPixel = page.initialPageWidthInPixel
        val initialPageHeightInPixel = page.initialPageHeightInPixel
        val pageRadio = initialPageWidthInPixel.toFloat() / initialPageHeightInPixel
        val doodleRadio = doodleWidth.toFloat() / doodleHeight
        if (pageRadio > doodleRadio) return 1F
        return doodleWidth * 0.8F / initialPageWidthInPixel
    }

    private fun useSmallButtonMargin(): Boolean {
        return DimensionUtil.isLandAndOneThirdScreen(context)
                || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)
                || DimensionUtil.isLandAndHalfScreen(context)
    }
}
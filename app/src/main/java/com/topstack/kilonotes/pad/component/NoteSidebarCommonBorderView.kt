package com.topstack.kilonotes.pad.component

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.ktx.isLayoutRtl
import kotlin.math.max
import kotlin.math.min

class NoteSidebarCommonBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val DEFAULT_BACKGROUND_COLOR_STRING = "#FFF4F4F4"
        private const val DEFAULT_SHADOW_COLOR_STRING = "#26000000"
        private const val DEFAULT_SHADOW_RADIUS = 14F
        private const val DEFAULT_BUTTON_WIDTH = 30F
        private const val DEFAULT_BUTTON_HEIGHT = 64F
        private const val DEFAULT_BUTTON_RADIUS = 20F
        private const val DEFAULT_ICON_COLOR_STRING = "#FF131415"
        private const val DEFAULT_ICON_WIDTH = 7F
        private const val DEFAULT_ICON_HEIGHT = 14F
        private const val DEFAULT_ICON_STROKE_WIDTH = 2F
        private const val BUTTON_ORIENTATION_AUTO = 0
        private const val BUTTON_ORIENTATION_LTR = 1
        private const val BUTTON_ORIENTATION_RTL = 2
        private const val DEFAULT_BUTTON_ORIENTATION = BUTTON_ORIENTATION_AUTO
    }

    private val backgroundColor: Int
    private val shadowColor: Int
    private val shadowRadius: Float
    private val buttonOrientation: Int
    private val buttonWidth: Float
    private val buttonHeight: Float
    private val buttonRadius: Float
    private val iconColor: Int
    private val iconWidth: Float
    private val iconHeight: Float
    private val iconStrokeWidth: Float

    init {
        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.NoteMaterialBorderView,
            defStyleAttr, 0
        ).apply {
            try {
                backgroundColor = getColor(
                    R.styleable.NoteMaterialBorderView_background_color,
                    Color.parseColor(DEFAULT_BACKGROUND_COLOR_STRING)
                )
                shadowColor = getColor(
                    R.styleable.NoteMaterialBorderView_shadow_color,
                    Color.parseColor(DEFAULT_SHADOW_COLOR_STRING)
                )
                shadowRadius = getDimension(
                    R.styleable.NoteMaterialBorderView_shadow_radius,
                    DEFAULT_SHADOW_RADIUS
                )
                buttonOrientation = getInt(
                    R.styleable.NoteMaterialBorderView_button_orientation,
                    DEFAULT_BUTTON_ORIENTATION
                )
                buttonWidth = getDimension(
                    R.styleable.NoteMaterialBorderView_button_width,
                    DEFAULT_BUTTON_WIDTH
                )
                buttonHeight = getDimension(
                    R.styleable.NoteMaterialBorderView_button_height,
                    DEFAULT_BUTTON_HEIGHT
                ).coerceAtLeast(buttonWidth)
                buttonRadius = getDimension(
                    R.styleable.NoteMaterialBorderView_button_radius,
                    DEFAULT_BUTTON_RADIUS
                ).coerceAtMost(buttonWidth)
                iconColor = getColor(
                    R.styleable.NoteMaterialBorderView_icon_color,
                    Color.parseColor(DEFAULT_ICON_COLOR_STRING)
                )
                iconWidth = getDimension(
                    R.styleable.NoteMaterialBorderView_icon_width,
                    DEFAULT_ICON_WIDTH
                ).coerceAtMost(buttonWidth)
                iconHeight = getDimension(
                    R.styleable.NoteMaterialBorderView_icon_height,
                    DEFAULT_ICON_HEIGHT
                ).coerceAtMost(buttonHeight)
                iconStrokeWidth = getDimension(
                    R.styleable.NoteMaterialBorderView_icon_stroke_width,
                    DEFAULT_ICON_STROKE_WIDTH
                )
            } finally {
                recycle()
            }
        }
        setLayerType(LAYER_TYPE_SOFTWARE, null)
    }

    private val path = Path()
    private val pathPaint = Paint().apply {
        isAntiAlias = true
        color = backgroundColor
        style = Paint.Style.FILL
        setShadowLayer(shadowRadius, 0F, 0F, shadowColor)
    }
    private val iconPoints = FloatArray(8)
    private val iconPaint = Paint().apply {
        isAntiAlias = true
        color = iconColor
        style = Paint.Style.FILL
        strokeWidth = iconStrokeWidth
        strokeCap = Paint.Cap.ROUND
        strokeJoin = Paint.Join.ROUND
    }
    private val buttonTouchRect = RectF()
    private val transform = Matrix()

    var onButtonClickedAction: (() -> Unit)? = null

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        if (h < buttonHeight) {
            return
        }
        buttonTouchRect.set(
            shadowRadius,
            (h - buttonHeight) / 2,
            shadowRadius + buttonWidth,
            (h + buttonHeight) / 2
        )
        path.apply {
            reset()
            moveTo(buttonWidth + shadowRadius, 0F)
            lineTo(buttonWidth + shadowRadius, (h - buttonHeight) / 2)
            lineTo(shadowRadius + buttonRadius, (h - buttonHeight) / 2)
            arcTo(
                RectF(
                    shadowRadius,
                    (h - buttonHeight) / 2,
                    shadowRadius + buttonRadius * 2,
                    (h - buttonHeight) / 2 + buttonRadius * 2
                ),
                270F,
                -90F
            )
            lineTo(shadowRadius, (h + buttonHeight) / 2 - buttonRadius)
            arcTo(
                RectF(
                    shadowRadius,
                    (h + buttonHeight) / 2 - buttonRadius * 2,
                    shadowRadius + buttonRadius * 2,
                    (h + buttonHeight) / 2
                ),
                180F,
                -90F
            )
            lineTo(buttonWidth + shadowRadius, (h + buttonHeight) / 2)
            lineTo(buttonWidth + shadowRadius, h.toFloat())
            val x = max((buttonWidth + shadowRadius) * 2, w.toFloat())
            lineTo(x, h.toFloat())
            lineTo(x, 0F)
            close()
        }
        iconPoints[0] = shadowRadius + (buttonWidth - iconWidth) / 2
        iconPoints[1] = (h - iconHeight) / 2
        iconPoints[2] = shadowRadius + (buttonWidth + iconWidth) / 2
        iconPoints[3] = h.toFloat() / 2
        iconPoints[4] = shadowRadius + (buttonWidth + iconWidth) / 2
        iconPoints[5] = h.toFloat() / 2
        iconPoints[6] = shadowRadius + (buttonWidth - iconWidth) / 2
        iconPoints[7] = (h + iconHeight) / 2
        val needTransform = when(buttonOrientation) {
            BUTTON_ORIENTATION_LTR -> true
            BUTTON_ORIENTATION_RTL -> false
            BUTTON_ORIENTATION_AUTO -> isLayoutRtl()
            else -> false
        }
        if (needTransform) {
            transform.setScale(-1F, 1F, w.toFloat() / 2, 0F)
            path.transform(transform)
            transform.mapPoints(iconPoints)
            transform.mapRect(buttonTouchRect)
        }
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        canvas.drawPath(path, pathPaint)
        canvas.drawLines(iconPoints, iconPaint)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val w = MeasureSpec.getSize(widthMeasureSpec)
        val h = MeasureSpec.getSize(heightMeasureSpec)
        setMeasuredDimension(
            min((buttonWidth + shadowRadius).toInt(), w),
            h
        )
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return if (event.action == MotionEvent.ACTION_DOWN && buttonTouchRect.contains(
                event.x,
                event.y
            )
        ) {
            onButtonClickedAction?.invoke()
            true
        } else {
            super.onTouchEvent(event)
        }
    }
}
package com.topstack.kilonotes.pad.customtool.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.databinding.ItemCustomLineBinding

class CustomLineAdapter(val context: Context) :
    RecyclerView.Adapter<CustomLineAdapter.CustomLineViewHolder>() {
    inner class CustomLineViewHolder(val binding: ItemCustomLineBinding) :
        RecyclerView.ViewHolder(binding.root) {

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CustomLineViewHolder {
        return CustomLineViewHolder(
            ItemCustomLineBinding.inflate(
                LayoutInflater.from(context),
                parent,
                false
            )
        )
    }

    override fun getItemCount(): Int {
        return 1
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON>iewHolder, position: Int) {

    }
}
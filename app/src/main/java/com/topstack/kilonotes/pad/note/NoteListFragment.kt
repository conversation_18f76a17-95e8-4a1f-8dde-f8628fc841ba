package com.topstack.kilonotes.pad.note

import android.Manifest
import android.animation.ValueAnimator
import android.app.Activity
import android.app.Activity.RESULT_OK
import android.content.ActivityNotFoundException
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Rect
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.ContextMenu
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.content.FileProvider
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.core.view.marginLeft
import androidx.core.view.marginRight
import androidx.core.view.marginTop
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.BuildConfig
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.NavGraphDirections
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.ad.AdHelper
import com.topstack.kilonotes.ad.AdUnit
import com.topstack.kilonotes.base.account.UnifiedLogin
import com.topstack.kilonotes.base.backup.dialog.BackupDialog
import com.topstack.kilonotes.base.combine.dialog.DocumentCombineSelectionDialog
import com.topstack.kilonotes.base.combine.model.CombineCancelled
import com.topstack.kilonotes.base.combine.model.CombineFailed
import com.topstack.kilonotes.base.combine.model.CombineSuccess
import com.topstack.kilonotes.base.combine.viewmodel.DocumentCombineViewModel
import com.topstack.kilonotes.base.component.dialog.AddNoteDialog
import com.topstack.kilonotes.base.component.dialog.AlertDialog
import com.topstack.kilonotes.base.component.dialog.CheckInNoteUnUseDialog
import com.topstack.kilonotes.base.component.dialog.FileManagerMoveDialog
import com.topstack.kilonotes.base.component.fragment.NaviEnum
import com.topstack.kilonotes.base.component.popup.BubbleTipWindow
import com.topstack.kilonotes.base.component.requester.VipExclusiveType
import com.topstack.kilonotes.base.component.view.InterceptTouchEventLayout
import com.topstack.kilonotes.base.component.view.OnInterceptTouchEventListener
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.component.view.impl.MultipleClicksListener
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.constant.AppConfig
import com.topstack.kilonotes.base.constant.MimeType
import com.topstack.kilonotes.base.creatorCommunity.dialog.JoinCreatorClubDialog
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.DocumentManager
import com.topstack.kilonotes.base.doc.Folder
import com.topstack.kilonotes.base.doc.MetaDocument
import com.topstack.kilonotes.base.hiddenspace.BasePasswordKeyboardDialog
import com.topstack.kilonotes.base.hiddenspace.PasswordState
import com.topstack.kilonotes.base.importfile.BaseHandleImportActivity
import com.topstack.kilonotes.base.ktx.adjustBackgroundRtlOrLtr
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.ktx.getCommonPromptDialog
import com.topstack.kilonotes.base.ktx.outsideArea
import com.topstack.kilonotes.base.ktx.safeNavigate
import com.topstack.kilonotes.base.ktx.safeShow
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.ktx.showStorageNotEnoughToOpenDocumentDialog
import com.topstack.kilonotes.base.limit.CreateNoteLimit
import com.topstack.kilonotes.base.limit.CreateNoteLimit.isAllowToCreateNote
import com.topstack.kilonotes.base.limit.HiddenNoteLimit
import com.topstack.kilonotes.base.netcover.model.NoteCover
import com.topstack.kilonotes.base.note.BaseNoteListFragment
import com.topstack.kilonotes.base.note.BaseVerifyRandomCodeDialog
import com.topstack.kilonotes.base.note.decoration.FileManagerNoteTreeItemDecoration
import com.topstack.kilonotes.base.note.decoration.GridCategoryItemDecoration
import com.topstack.kilonotes.base.note.drag.DocumentDragHelper
import com.topstack.kilonotes.base.note.drag.FileManagerDragHelper
import com.topstack.kilonotes.base.note.model.NoteOrganizeAndSortType
import com.topstack.kilonotes.base.note.model.NoteTreeDocument
import com.topstack.kilonotes.base.note.model.NoteTreeFolder
import com.topstack.kilonotes.base.note.model.NoteTreeItem
import com.topstack.kilonotes.base.note.model.NoteTreeMetaDocument
import com.topstack.kilonotes.base.note.model.NoteTreeTag
import com.topstack.kilonotes.base.note.model.TitleErrorType
import com.topstack.kilonotes.base.note.snippet.SnippetManager
import com.topstack.kilonotes.base.note.viewmodel.ImportFileViewModel
import com.topstack.kilonotes.base.note.viewmodel.SnippetEditViewModel
import com.topstack.kilonotes.base.select.BUNDLE_URI_KEY
import com.topstack.kilonotes.base.select.PICK_PIC_RESULT_CODE
import com.topstack.kilonotes.base.sync.SyncCore
import com.topstack.kilonotes.base.sync.cloud.core.SyncManager
import com.topstack.kilonotes.base.sync.cloud.dropbox.DropboxOauthUtil
import com.topstack.kilonotes.base.sync.cloud.googledrive.GoogleDriveOauthUtil
import com.topstack.kilonotes.base.sync.dialog.SyncLoginDialog
import com.topstack.kilonotes.base.sync.viewmodel.SyncViewModel
import com.topstack.kilonotes.base.track.event.AppAdEvent
import com.topstack.kilonotes.base.track.event.CheckInEvent
import com.topstack.kilonotes.base.track.event.CloudBackupEvent
import com.topstack.kilonotes.base.track.event.CreateNoteLimitEvent
import com.topstack.kilonotes.base.track.event.DataBackUpEvent.sendDataBackUpShow
import com.topstack.kilonotes.base.track.event.DocumentCombineEvent
import com.topstack.kilonotes.base.track.event.FolderManagerEvent
import com.topstack.kilonotes.base.track.event.HiddenSpaceEvent
import com.topstack.kilonotes.base.track.event.HomeEvent
import com.topstack.kilonotes.base.track.event.HomeEvent.sendDefaultDocumentStart
import com.topstack.kilonotes.base.track.event.HomeEvent.sendExceptionalContactShow
import com.topstack.kilonotes.base.track.event.HomeEvent.sendImportClickEvent
import com.topstack.kilonotes.base.track.event.HomeEvent.sendNoteBookTypeEvent
import com.topstack.kilonotes.base.track.event.HomeEvent.sendNoteBookUnusualEvent
import com.topstack.kilonotes.base.track.event.HomeEvent.sendStoreClickEvent
import com.topstack.kilonotes.base.track.event.LoginLocation
import com.topstack.kilonotes.base.track.event.PageViewportResizingEvent
import com.topstack.kilonotes.base.track.event.SnippetEvent
import com.topstack.kilonotes.base.track.event.Sort
import com.topstack.kilonotes.base.track.event.StoreAndLoginEvent
import com.topstack.kilonotes.base.track.event.SyncBaiduEvent
import com.topstack.kilonotes.base.track.event.UserEvent
import com.topstack.kilonotes.base.track.event.CommunityEvent
import com.topstack.kilonotes.base.util.ClipboardUtil.newPlain
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.PermissionRequester
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.base.util.isWebViewAvailable
import com.topstack.kilonotes.base.util.showWebViewNotAvailableDialog
import com.topstack.kilonotes.cloudbackup.upload.BackupUploadTaskManager
import com.topstack.kilonotes.databinding.NoteListFragmentBinding
import com.topstack.kilonotes.infra.config.BUILD_TYPE_TEMPLATE
import com.topstack.kilonotes.infra.config.PersonalInfoCollectionCounter
import com.topstack.kilonotes.infra.foundation.thread.ThreadUtils
import com.topstack.kilonotes.infra.gson.commonGsonClient
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.notedata.NoteRepository
import com.topstack.kilonotes.pad.MainActivity
import com.topstack.kilonotes.pad.about.AboutActivity
import com.topstack.kilonotes.pad.account.AccountCancellationActivity
import com.topstack.kilonotes.pad.component.dialog.CreateFolderGuideDialog
import com.topstack.kilonotes.pad.component.dialog.PadHiddenSpaceCreateOrAddNoteSelectWindow
import com.topstack.kilonotes.pad.component.dialog.PadHiddenSpaceNoticeDialog
import com.topstack.kilonotes.pad.component.dialog.PadRedeemCodeDialog
import com.topstack.kilonotes.pad.component.dialog.PasswordKeyboardDialog
import com.topstack.kilonotes.pad.guide.FileManagerDesktopOutlineGuideWindow
import com.topstack.kilonotes.pad.note.adapter.FileManagerAdapter
import com.topstack.kilonotes.pad.note.adapter.FileManagerPathAdapter
import com.topstack.kilonotes.pad.note.adapter.NoteCoverViewHolder
import com.topstack.kilonotes.pad.note.adapter.NoteFolderViewHolder
import com.topstack.kilonotes.pad.note.adapter.NoteListAdapter
import com.topstack.kilonotes.pad.note.adapter.NoteListAddAdapter
import com.topstack.kilonotes.pad.note.adapter.NoteTreeDocumentViewHolder
import com.topstack.kilonotes.pad.note.adapter.NoteTreeFolderViewHolder
import com.topstack.kilonotes.pad.note.dialog.FileManagerFileCreateDialog
import com.topstack.kilonotes.pad.note.dialog.ShowGetBackPasswordDialog
import com.topstack.kilonotes.pad.note.dialog.VerifyRandomCodeDialog
import com.topstack.kilonotes.pad.note.popupwindow.EditFolderInfoWindow
import com.topstack.kilonotes.pad.note.popupwindow.EditNoteInfoWindow
import com.topstack.kilonotes.pad.note.popupwindow.NoteCreateWindow
import com.topstack.kilonotes.pad.promotion.checkin.GainNoteLimitsPromotionManager
import com.topstack.kilonotes.pad.security.PadSecurityQuestionFragment
import com.topstack.kilonotes.pad.select.SelectPhotoDialogActivity
import com.topstack.kilonotes.pad.setting.HiddenSpaceGuideWindow
import com.topstack.kilonotes.pad.setting.HiddenSpaceGuideWindow.Companion.DEFAULT_TIPS_LINE
import com.topstack.kilonotes.pad.setting.SettingWindow
import com.topstack.kilonotes.pad.sort.NoteSortWindow
import com.topstack.kilonotes.pad.sync.BaiduLoginActivity
import com.topstack.kilonotes.phone.note.info.EditFolderAction
import com.topstack.kilonotes.phone.note.info.EditNoteAction
import com.topstack.kilonotes.xuanhu.XuanhuHelper
import com.topstack.sdk.baidu.pan.oauth.BaiduErrorInfo
import com.topstack.sdk.baidu.pan.oauth.BaiduInfo
import com.topstack.sdk.baidu.pan.oauth.BaiduOAuth
import com.topstack.sdk.baidu.pan.oauth.BaiduOauthManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.abs

/**
 * <AUTHOR>
 */
class NoteListFragment : BaseNoteListFragment(R.layout.note_list_fragment),
    NoteListAdapter.NoteListener {
    private val TAG = NoteListFragment::class.java.simpleName
    private val FOLDER_TITLE_ERROR_DIALOG_TAG = "FolderTitleErrorDialog"
    private var ration: Float? = null
    private var binding: NoteListFragmentBinding? = null
    private var noteListAdapter: NoteListAdapter? = null
    private var addNoteAdapter: NoteListAddAdapter? = null
    private var fileManagerAdapter: FileManagerAdapter? = null
    private var fileManagerPathAdapter: FileManagerPathAdapter? = null
    private var fileManagerDragHelper: FileManagerDragHelper? = null
    private var documentDragHelper: DocumentDragHelper? = null
    private var mEditNoteInfoWindow: EditNoteInfoWindow? = null
    private var mEditFolderInfoWindow: EditFolderInfoWindow? = null
    private var settingWindow: SettingWindow? = null
    private var fileManagerdesktopOutlineGuideWindow: FileManagerDesktopOutlineGuideWindow? = null
    private var hiddenSpaceGuideWindow: HiddenSpaceGuideWindow? = null
    private var hiddenSpaceCreateOrAddNoteSelectWindow: PadHiddenSpaceCreateOrAddNoteSelectWindow? =
        null
    private var noteSortWindow: NoteSortWindow? = null
    private var lastClickDocument: Document? = null
    private var snippetPageFragment: SnippetPageFragment? = null
    private val computeShotViewTempRect = Rect()
    private val computeShotViewTempIntArray = IntArray(2)
    private val computeFileManagerShotViewTempRect = Rect()
    private val computeFileManagerShotViewTempIntArray = IntArray(2)
    private var passwordKeyboardDialog: PasswordKeyboardDialog? = null
    private var addNoteDialog: AddNoteDialog? = null
    private val getBackPasswordDialog: ShowGetBackPasswordDialog by lazy { ShowGetBackPasswordDialog() }
    private val verifyRandomCodeDialog: VerifyRandomCodeDialog by lazy {
        VerifyRandomCodeDialog().apply {
            onVerifySuccess = <EMAIL>
        }
    }
    private var fileManagerFileCreateDialog: FileManagerFileCreateDialog? = null
    private var padSecurityQuestionFragment: PadSecurityQuestionFragment? = null
    private val snippetEditViewModel: SnippetEditViewModel by activityViewModels()
    private val syncViewModel: SyncViewModel by activityViewModels()
    private val combineViewModel: DocumentCombineViewModel by viewModels()
    private val aboutActivityLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == AboutActivity.OPEN_HIDDEN_SPACE_RESULT_CODE) {
                securityViewModel.isHiddenSpacePasswordSet { isSet ->
                    if (isSet) {
                        securityViewModel.passwordList.clear()
                        noteListViewModel.changePasswordKeyboardState(PasswordState.INPUT_PASSWORD)
                    } else {
                        noteViewModel.changeIsHiddenSpaceMode(true)
                    }
                }
            }
        }
    private val loginBaiduLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) GetContent@{
        val data = it.data
        val resultCode = it.resultCode
        if (BaiduLoginActivity.BAIDU_LOGIN_RESULT_CODE == resultCode) {
            val code = data?.extras?.get(BaiduLoginActivity.BAIDU_CODE_KEY) ?: return@GetContent
            if (code is String) {
                loginBaiduByCode(code)
            }
        }
    }
    private var isOSAction: Boolean = true
    private var deleteAlertDialog: AlertDialog? = null
    private var fileManagerAnimator: ValueAnimator? = null
    private val DELETE_SNIPPET_DIALOG = "SaveDialog"

    private val globalLayoutListener = OnGlobalLayoutListener {
        var adaptNoteRv = false
        binding?.freeCountLayout?.let { freeCountLayout ->
            val newVisibility = freeCountLayout.visibility
            if (freeCountLayout.tag != newVisibility) {
                freeCountLayout.tag = newVisibility
                adaptNoteRv = true
            }
        }

        if (adaptNoteRv) {
            val adapter = binding?.noteRv?.overScrollRecyclerView?.adapter
            val itemDecoration = gridCategoryItemDecoration
            if (adapter != null && itemDecoration != null) {
                val start = itemDecoration.getLastRowFirstColumnPositionWithoutOffset()
                val count =
                    adapter.itemCount - itemDecoration.getLastRowFirstColumnPositionWithoutOffset()
                adapter.notifyItemRangeChanged(start, count)
            }
        }
    }

    private var gridCategoryItemDecoration: GridCategoryItemDecoration? = null
    private var fileManagerNoteTreeItemDecoration: FileManagerNoteTreeItemDecoration? = null
    private var gridLayoutManager: GridLayoutManager? = null

    private val RV_SCROLL_HORIZONAL_DIRECTION_RIGHT = 1

    private fun checkViewBindingValidity() = binding != null

    private val initialLayoutComplete = AtomicBoolean(false)
    private val bannerAdUnit: AdUnit by lazy {
        if (AdHelper.isDomestic()) {
            AdUnit.DOMESTIC_HOME_BANNER
        } else {
            AdUnit.ADMOB_HOME_BANNER
        }
    }

    private var createdNoteInCurrentViewLifecycle = false

    private val takePhotoLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        createNotebookViewModel.photoUri?.let { uri ->
            if (success) {
                val defaultTitle = createNotebookViewModel.getNewDocumentTitle(
                    getString(R.string.notebook_default_name),
                    noteViewModel::verifyDocumentTitle
                )
                createNotebookViewModel.createDocumentFromImageUri(
                    requireContext(),
                    uri,
                    defaultTitle,
                    noteViewModel.isHiddenSpaceMode.value ?: false
                )
                createdNoteInCurrentViewLifecycle = true
            }
        }
        createNotebookViewModel.photoUri = null
    }

    private val PERMISSION_ERROR_TAG = "permissionError"
    private val permissionRequester = PermissionRequester(this)

    private val selectImageToCreateNoteLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) GetContent@{
        val data = it.data
        val resultCode = it.resultCode
        if (PICK_PIC_RESULT_CODE == resultCode) {
            val imageUri = data?.extras?.getParcelable(BUNDLE_URI_KEY) as? Uri ?: return@GetContent

            val defaultTitle = createNotebookViewModel.getNewDocumentTitle(
                getString(R.string.notebook_default_name),
                noteViewModel::verifyDocumentTitle
            )
            createNotebookViewModel.createDocumentFromImageUri(
                requireContext(),
                imageUri,
                defaultTitle,
                noteViewModel.isHiddenSpaceMode.value ?: false
            )
            createdNoteInCurrentViewLifecycle = true
        }
    }

    private val startActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val syncOauthUtil = syncViewModel.getCurrentSyncUserUtil()
                if (syncOauthUtil is GoogleDriveOauthUtil) {
                    PersonalInfoCollectionCounter.cloudBackupLoginCount++
                    syncOauthUtil.googleDriveOauthManager.storeAuthInfo(
                        result
                    ) {
                        SyncCore.syncRemoteChannel = SyncManager.REMOTE_CHANNEL_GOOGLE_DRIVE
                        syncViewModel.changeSyncRemoteChannel(SyncManager.REMOTE_CHANNEL_GOOGLE_DRIVE)
                        showSyncLogInDialog()
                    }
                }
            }
        }

    private fun generatePictureUri(): Uri? {
        return try {
            val dir = File(AppUtils.appContext.externalCacheDir, "pictures")
            if (!dir.exists()) {
                dir.mkdirs()
            }
            val file = File(dir, "pictureFromCamera.jpg")
            if (file.exists()) {
                file.delete()
            }
            FileProvider.getUriForFile(
                AppUtils.appContext,
                BuildConfig.APPLICATION_ID + ".provider",
                file
            )
        } catch (e: Exception) {
            null
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        initialLayoutComplete.set(false)
        super.onViewCreated(view, savedInstanceState)
        initSnippetPage()
        if (savedInstanceState != null) {
            createdNoteInCurrentViewLifecycle = false
        }
        ration = DimensionUtil.getPadNoteListMultiWindowRatio(requireContext())
        val activity: Activity = requireActivity()
        val spanCount = getNoteSpanCountByWith(0)
        gridLayoutManager = GridLayoutManager(activity, spanCount).apply {
            spanSizeLookup = object : SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    val list = noteViewModel.selectedNoteTreeItemList.value ?: return 1
                    return if (position == 0) {
                        if (noteViewModel.getCurrentDocumentSortType() == NoteOrganizeAndSortType.COLOR_TAG) {
                            spanCount
                        } else {
                            1
                        }
                    } else if (position > 0) {
                        val item = list[position - 1]
                        if (item is NoteTreeFolder || item is NoteTreeDocument) {
                            1
                        } else {
                            spanCount
                        }
                    } else {
                        1
                    }
                }

            }
            isMeasurementCacheEnabled = false
        }
        noteListAdapter = NoteListAdapter(activity, ration!!)
        noteListAdapter!!.setListener(this)
        addNoteAdapter = NoteListAddAdapter(activity, ration) { view ->
            noteViewModel.isHiddenSpaceMode.value?.let { isHiddenSpaceMode ->
                if (isHiddenSpaceMode) {
                    //展示隐藏模式创建/添加本子选择弹窗
                    if (!HiddenNoteLimit.isAllowToIncreaseHiddenNote()) {
                        showVipExclusiveDialog(
                            VipExclusiveType.HIDE_SPACE,
                            NaviEnum.HIDDEN_SPACE_NOTE_ADD_OR_CREATE
                        )
                    } else {
                        showCreateOrAddNotePopupWindow(view)
                    }
                    HiddenSpaceEvent.sendHidenSpaceCreateOrAddNoteBtnClick()
                } else {
                    if (isAllowToCreateNote()) {
                        NoteCreateWindow(requireContext()).apply {
                            createAction = object : NoteCreateWindow.NoteCreateAction {
                                override fun onCreateNote() {
                                    createNotebookViewModel.getCurrentCoverCategoryList {
                                        if (!isAdded) return@getCurrentCoverCategoryList
                                        createNotebookViewModel.resetCreateProperty()
                                        createNotebookViewModel.currentCoverPosition.value =
                                            Pair(
                                                NoteCover.DEFAULT_COVER_CATEGORY_INDEX,
                                                NoteCover.DEFAULT_COVER_INDEX
                                            )
                                        <EMAIL>(
                                            R.id.note_list,
                                            R.id.create
                                        )
                                    }
                                }

                                override fun onImportNote() {
                                    launchImport(showTipsAsToast = true)
                                }

                                override fun onCreateNoteFromImage() {
                                    val needRequestPermission =
                                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                                            Manifest.permission.READ_EXTERNAL_STORAGE
                                        } else {
                                            Manifest.permission.READ_MEDIA_IMAGES
                                        }
                                    PermissionRequester.showPermissionRationaleDialogThenRequest(
                                        title = AppUtils.getString(R.string.permission_rationale_title_for_storage),
                                        message = AppUtils.getString(R.string.permission_rationale_content_for_storage),
                                        permissionState = PermissionRequester.checkPermissionState(
                                            requireActivity(),
                                            needRequestPermission
                                        ),
                                        fragmentManager = parentFragmentManager
                                    ) {
                                        permissionRequester.request(needRequestPermission) { isGranted ->
                                            if (isGranted) {
                                                selectImageToCreateNoteLauncher.launch(
                                                    Intent(
                                                        requireContext(),
                                                        SelectPhotoDialogActivity::class.java
                                                    ).apply {
                                                        putExtra(
                                                            SelectPhotoDialogActivity.BUNDLE_KEY_NEED_CROP_IMAGE,
                                                            false
                                                        )
                                                        putExtra(
                                                            SelectPhotoDialogActivity.BUNDLE_KEY_IGNORE_DETAIL_IMAGE,
                                                            true
                                                        )
                                                    }
                                                )
                                            } else {
                                                if (PermissionRequester.checkPermissionState(
                                                        this@NoteListFragment,
                                                        needRequestPermission
                                                    ) == PermissionRequester.PermissionState.PERMISSION_NOT_ASK_AGAIN
                                                ) {
                                                    val alertDialog = AlertDialog.Builder()
                                                        .setMsg(resources.getString(R.string.never_aks_read_external_storage))
                                                        .setPositiveBtn(resources.getString(R.string.go_to_set)) {
                                                            PermissionRequester.jumpToSetting(this@NoteListFragment)
                                                        }
                                                        .setNegativeBtn(resources.getString(R.string.ok)) {

                                                        }
                                                        .build()
                                                    alertDialog.show(
                                                        parentFragmentManager,
                                                        PERMISSION_ERROR_TAG
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }

                                override fun onCreateNoteFromTakingPhoto() {
                                    createNotebookViewModel.photoUri = generatePictureUri()
                                    takePhotoLauncher.launch(createNotebookViewModel.photoUri)
                                    KiloApp.app.provisionallyJumpToOtherApp = true
                                }

                            }
                        }.also {
                            it.show(view)
                        }

                    } else {
                        showCreateNoteLimitDialog("创建笔记按钮")
                    }
                }
            }
        }
        if (savedInstanceState != null) {
            hideLoadingDialog()
            isOSAction = true
        }
        binding = NoteListFragmentBinding.bind(view)
        if (!isSatisfyConditionOfFileManagerSplitScreen()) {
            adjustFileManagerPathContain()
            binding?.fileManagerEnter?.visibility = View.GONE
        }
        gridCategoryItemDecoration = GridCategoryItemDecoration(
            offset = 1,
            itemWidth = getItemWidth(),
            rowSpace = getItemSpace(),
            spanCount = spanCount,
            bottomPadding = {
                if (noteViewModel.isHiddenSpaceMode.value == true) {
                    -1
                } else {
                    val defaultPadding =
                        context?.resources?.getDimensionPixelSize(R.dimen.dp_84)
                    if (defaultPadding == null) {
                        -1
                    } else {
                        var extraPadding = 0
                        if (binding?.freeCountLayout?.isVisible == true) {
                            extraPadding += binding?.freeCountLayout?.height ?: 0
                        }
                        defaultPadding + extraPadding
                    }
                }
            }
        )
        (view as InterceptTouchEventLayout).onInterceptTouchEventListener =
            object : OnInterceptTouchEventListener {
                override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
                    if (ev?.actionMasked == MotionEvent.ACTION_POINTER_DOWN) {
                        if (fileManagerDragHelper?.onDragging() == true || documentDragHelper?.onDragging() == true) return true
                    }
                    return false
                }
            }
        binding!!.noteAndSnippetSelectView.setupBlurView(binding!!.root)
        binding!!.noteRv.overScrollRecyclerView.addItemDecoration(gridCategoryItemDecoration!!)
        binding!!.noteRv.overScrollRecyclerView.layoutManager = gridLayoutManager
        binding!!.noteRv.overScrollRecyclerView.adapter =
            ConcatAdapter(addNoteAdapter, noteListAdapter)
        binding!!.noteRv.overScrollRecyclerView.itemAnimator = null
        binding!!.freeCountLayout.tag = binding!!.freeCountLayout.visibility
        val viewTreeObserver = binding!!.freeCountLayout.viewTreeObserver
        if (viewTreeObserver.isAlive) {
            viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)
        }
        if (homeDialogViewModel.isNeedCheckShowCreateFolderGuideDialog() || homeDialogViewModel.isNeedShowSupportAppDialog() || homeDialogViewModel.isNeedShowCreateNoteRightsDialog()) {
            noteViewModel.currentMetaDocumentList.observe(
                viewLifecycleOwner,
                object : Observer<List<MetaDocument>> {
                    override fun onChanged(list: List<MetaDocument>) {
                        if (importFileViewModel.importResult.value?.importStatus == ImportFileViewModel.ImportStatus.JUMP) return
                        if (!homeDialogViewModel.isNeedCheckShowCreateFolderGuideDialog() && !homeDialogViewModel.isNeedShowSupportAppDialog() && !homeDialogViewModel.isNeedShowCreateNoteRightsDialog()) {
                            noteViewModel.currentMetaDocumentList.removeObserver(this)
                        }
                        if (CreateNoteLimit.currentFreeCreatedNoteNumber == MIN_NOTE_NUMBER_WHEN_SHOW_SUPPORT_APP && noteViewModel.isHiddenSpaceMode.value == false) {
                            homeDialogViewModel.markShouldShowSupportAppDialog()
                            var isJump =
                                importFileViewModel.importResult.value?.needOpenFile ?: false
                            if (findNavController().currentDestination?.id == getCurrentNavigationId() && !isJump) {
                                homeDialogViewModel.refreshFirstNeedShowDialogTag()
                            }
                        } else if (CreateNoteLimit.currentFreeCreatedNoteNumber == MIN_NOTE_NUMBER_WHEN_SHOW_CREATE_NOTE_RIGHTS && noteViewModel.isHiddenSpaceMode.value == false) {
                            homeDialogViewModel.markShouldShowCreateNoteRightsDialog()
                            var isJump =
                                importFileViewModel.importResult.value?.needOpenFile ?: false
                            if (findNavController().currentDestination?.id == getCurrentNavigationId() && !isJump) {
                                homeDialogViewModel.refreshFirstNeedShowDialogTag()
                            }
                        }
                        if (noteViewModel.allUnhiddenDocuments.size >= MIN_NOTE_NUMBER_WHEN_SHOW_CREATE_FOLDER_GUIDE && noteViewModel.isHiddenSpaceMode.value == false) {
                            if (findNavController().currentDestination?.id == getCurrentNavigationId()) {
                                homeDialogViewModel.markShouldShowCreateFolderGuideDialog()
                                homeDialogViewModel.refreshFirstNeedShowDialogTag()
                            }
                        }
                    }
                })
        }
        noteViewModel.selectedNoteTreeItemList.observe(viewLifecycleOwner) {
            LogHelper.d(TAG, "selectedNoteTreeItem callback")
            if (isOSAction) return@observe
            refreshNoteRvItemDecoration(it)
        }

        noteViewModel.isHiddenSpaceMode.observe(viewLifecycleOwner) { isHiddenSpaceMode ->
            addNoteAdapter?.setIsHiddenSpaceMode(isHiddenSpaceMode)
            checkInViewModel.isCheckInPromotionFinished.value?.let { setCheckInEnterVisible(it) }
            if (isHiddenSpaceMode) {
                HiddenSpaceEvent.sendHidenSpaceShow()
                val parentActivity = activity as? BaseHandleImportActivity
                parentActivity?.registerOnBackPressedListener {
                    val dialog =
                        childFragmentManager.findFragmentByTag(PadSecurityQuestionFragment.TAG)
                    if (dialog is PadSecurityQuestionFragment) {
                        dialog.closeDialog(false)
                    } else {
                        securityViewModel.isHiddenSpacePasswordSet { isSet ->
                            if (!isSet && noteViewModel.allHiddenDocuments.isNotEmpty()) {
                                noteListViewModel.changePasswordKeyboardState(PasswordState.SET_PASSWORD)
                            } else {
                                noteViewModel.changeIsHiddenSpaceMode(false)
                            }
                        }
                    }
                }
                binding!!.title.text = resources.getString(R.string.hidden_space_title)
                binding!!.root.background = ResourcesCompat.getDrawable(
                    resources,
                    R.drawable.pad_note_list_fragment_hidden_space_background,
                    null
                )
                binding!!.freeCountLayout.visibility = View.GONE
                binding!!.hiddenSpaceNoticeTips.isVisible =
                    noteListViewModel.isNeedShowHiddenSpaceNoticeTips
                binding!!.noteAndSnippetSelectView.isVisible = false
                binding!!.noteAndSnippetSelectViewDividingLine.isVisible = false
                binding!!.settingBtn.isVisible = false
                binding!!.storeBtn.isVisible = false
                binding!!.importBtn.isVisible = false
                binding!!.hiddenSpaceHomeBtn.isVisible = true
                binding!!.hiddenSpaceDataBackupBtn.isVisible = true
                securityViewModel.isQASet { isSetQA ->
                    if (!checkViewBindingValidity()) return@isQASet
                    binding!!.hiddenSpaceVipAndSecurityTips.setHasSetSecurityQuestion(isSetQA)
                    securityViewModel.isHiddenSpacePasswordSet { isSetPwd ->
                        if (!isAdded) return@isHiddenSpacePasswordSet
                        binding!!.hiddenSpaceVipAndSecurityTips.isVisible =
                            if (isSetQA) {
                                noteListViewModel.isNeedShowHiddenSpaceVipAndSecurityTips && !UserManager.isVip()
                            } else {
                                noteListViewModel.isNeedShowHiddenSpaceVipAndSecurityTips && isSetPwd
                            }
                        if (isSetQA && binding!!.hiddenSpaceVipAndSecurityTips.isVisible) {
                            HiddenSpaceEvent.sendHidenSpaceVipTipsShow()
                        }
                        if (binding!!.hiddenSpaceVipAndSecurityTips.isVisible) {
                            binding!!.hiddenSpaceNoticeTips.setMargins(
                                binding!!.hiddenSpaceNoticeTips.marginLeft,
                                binding!!.hiddenSpaceNoticeTips.marginTop,
                                binding!!.hiddenSpaceNoticeTips.marginRight,
                                0
                            )
                        } else {
                            binding!!.hiddenSpaceNoticeTips.setMargins(
                                binding!!.hiddenSpaceNoticeTips.marginLeft,
                                binding!!.hiddenSpaceNoticeTips.marginTop,
                                binding!!.hiddenSpaceNoticeTips.marginRight,
                                resources.getDimension(R.dimen.dp_20).toInt()
                            )
                        }
                    }
                }
            } else {
                val parentActivity = activity as? BaseHandleImportActivity
                parentActivity?.unRegisterOnBackPressedListener()
                binding!!.title.text = resources.getString(R.string.app_name)
                binding!!.root.setBackgroundColor(Color.parseColor("#F2F2F7"))
                noteListViewModel.showFreeNoteCountLayout.value?.let { isShow ->
                    if (isShow && !UserManager.isVip()) {
                        binding!!.freeCountLayout.visibility = View.VISIBLE
                    }
                }
                binding!!.noteAndSnippetSelectView.isVisible = true
                binding!!.noteAndSnippetSelectViewDividingLine.isVisible = true
                binding!!.hiddenSpaceVipAndSecurityTips.isVisible = false
                binding!!.hiddenSpaceNoticeTips.isVisible = false
                binding!!.settingBtn.isVisible = !isLandAndOneThirdScreen()
                binding!!.storeBtn.isVisible = true
                binding!!.importBtn.isVisible = true
                binding!!.hiddenSpaceHomeBtn.isVisible = false
                binding!!.hiddenSpaceDataBackupBtn.isVisible = false
                noteListViewModel.resetHiddenSpaceTipsVisibility()
            }

            /**
             * 此处处理多级文件夹大纲状态恢复，isOSAction为系统行为用于重建
             */
            if (isOSAction) return@observe
            if (isHiddenSpaceMode) {
                noteListViewModel.openOrCloseHiddenOutline.value?.let { isOpen ->
                    translateNoteOutline(
                        isOpen && isSatisfyConditionOfFileManagerSplitScreen(),
                        true
                    )
                }
            } else {
                noteListViewModel.openOrCloseOutline.value?.let { isOpen ->
                    translateNoteOutline(
                        isOpen && isSatisfyConditionOfFileManagerSplitScreen(),
                        true
                    )
                }
            }
        }

        noteListViewModel.openOrCloseOutline.observe(viewLifecycleOwner) { isOpen ->
            if (noteViewModel.isHiddenSpaceMode.value != false) return@observe
            FolderManagerEvent.sendFolderManagerContainerClickEvent(isOpen)
            LogHelper.d(TAG, "openOrCloseOutline callback")
            if (isOSAction) {
                if (isOpen && isSatisfyConditionOfFileManagerSplitScreen()) {
                    translateNoteOutline(true) {
                        isOSAction = false
                    }
                } else {
                    isOSAction = false
                }
            } else {
                if (isOpen && isSatisfyConditionOfFileManagerSplitScreen()) {
                    translateNoteOutline(true, callback = null)
                } else {
                    translateNoteOutline(false, callback = null)
                }
            }
        }

        noteListViewModel.openOrCloseHiddenOutline.observe(viewLifecycleOwner) { isOpen ->
            if (noteViewModel.isHiddenSpaceMode.value != true) return@observe
            if (isOSAction) {
                if (isOpen && isSatisfyConditionOfFileManagerSplitScreen()) {
                    translateNoteOutline(true) {
                        isOSAction = false
                    }
                } else {
                    isOSAction = false
                }
            } else {
                if (isOpen && isSatisfyConditionOfFileManagerSplitScreen()) {
                    translateNoteOutline(true)
                } else {
                    translateNoteOutline(false)
                }
            }
        }

        noteViewModel.isNeedShowAddNoteDialog.observe(viewLifecycleOwner) { isNeedShow ->
            if (isNeedShow) {
                val dialog = parentFragmentManager.findFragmentByTag(AddNoteDialog.TAG)
                if (dialog is AddNoteDialog) {
                    dialog.vipWindowAction = {
                        showVipExclusiveDialog(
                            VipExclusiveType.HIDE_SPACE,
                            NaviEnum.HIDDEN_SPACE_NOTE_ADD_OR_CREATE,
                            resources.getString(R.string.hidden_space_subscribe_vip_title),
                            onVipClickedAction = {
                                dialog.dismiss()
                            }
                        )
                    }
                    dialog.closeAction = {
                        noteViewModel.isNeedShowAddNoteDialog.value = false
                    }
                    addNoteDialog = dialog
                    return@observe
                }
                addNoteDialog = AddNoteDialog().apply {
                    vipWindowAction = {
                        showVipExclusiveDialog(
                            VipExclusiveType.HIDE_SPACE,
                            NaviEnum.HIDDEN_SPACE_NOTE_ADD_OR_CREATE,
                            resources.getString(R.string.hidden_space_subscribe_vip_title),
                            onVipClickedAction = {
                                this.dismiss()
                            }
                        )
                    }
                    closeAction = {
                        noteViewModel.isNeedShowAddNoteDialog.value = false
                    }
                }
                addNoteDialog?.show(parentFragmentManager, AddNoteDialog.TAG)
            } else {
                addNoteDialog?.dismiss()
            }
        }

        noteListViewModel.addUserInfoChangeListener { userInfo, isSubscriber ->
            showMemberEnterIcon()
            if (UserManager.isVip()) {
                binding!!.hiddenSpaceVipAndSecurityTips.isVisible = false
                noteListViewModel.hideFreeNoteCountLayout(false)
            }
        }

        binding!!.hiddenSpaceNoticeTips.apply {
            setOnCloseClickListener {
                this.isVisible = false
                noteListViewModel.isNeedShowHiddenSpaceNoticeTips = false
            }
            setOnRootViewClickListener {
                showHiddenSpaceNoticeDialog()
            }
        }

        binding!!.hiddenSpaceVipAndSecurityTips.apply {
            setAskLaterBtnClickListener {
                this.isVisible = false
                noteListViewModel.isNeedShowHiddenSpaceVipAndSecurityTips = false
                binding!!.hiddenSpaceNoticeTips.setMargins(
                    binding!!.hiddenSpaceNoticeTips.marginLeft,
                    binding!!.hiddenSpaceNoticeTips.marginTop,
                    binding!!.hiddenSpaceNoticeTips.marginRight,
                    resources.getDimension(R.dimen.dp_20).toInt()
                )
            }
            setBuyVipBtnClickListener {
                val action = NavGraphDirections.actionVipExclusiveDialogToVipStore()
                action.source = NaviEnum.HIDDEN_SPACE_SUBSCRIBE
                safeNavigate(action)
            }
            setSecurityQuestionTipsClickListener {
                noteListViewModel.isSetQuestionByQADialog.value = true
            }
        }


        if (isLandAndOneThirdScreen()) {
            binding!!.settingBtn.visibility = View.GONE
            noteViewModel.isShowSettingWindow.setValue(false)
        } else {
            binding!!.settingBtn.setOnClickListener {
                noteViewModel.isShowSettingWindow.setValue(
                    true
                )
            }
        }

        val targetViews = listOf(
            binding!!.memberEnterIcon
        )

        targetViews.forEach { view ->
            view.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
            view.setOnClickListener(AntiShakeClickListener {
                if (activity is MainActivity) {
                    XuanhuHelper.sendEvent("home_member_entry_click")
                    safeNavigate(R.id.vip_store_new_edition)
                }
            })
        }

        binding!!.sortBtn.setOnClickListener(AntiShakeClickListener { v: View? ->
            Sort.sendSortInClickSortBtn()
            noteViewModel.changeSortWindowStatues(true)
        })

        binding!!.importBtn.setOnClickListener(AntiShakeClickListener { v: View? ->
            sendImportClickEvent()
            launchImport()
        })

        binding!!.storeBtn.setOnClickListener {
            sendStoreClickEvent()
            safeNavigate(R.id.action_note_list_to_vip_store)
        }

        binding!!.hiddenSpaceHomeBtn.setOnClickListener {
            securityViewModel.isHiddenSpacePasswordSet { isSet ->
                if (!isSet && noteViewModel.allHiddenDocuments.isNotEmpty()) {
                    securityViewModel.passwordList.clear()
                    noteListViewModel.changePasswordKeyboardState(PasswordState.SET_PASSWORD)
                } else {
                    noteViewModel.changeIsHiddenSpaceMode(false)
                }
            }
        }

        binding!!.hiddenSpaceDataBackupBtn.setOnClickListener {
            noteViewModel.isShowSettingWindow.setValue(true)
        }

        binding!!.freeCountLayout.setOnAskLaterClick {
            CreateNoteLimitEvent.sendCreateLimitLayoutCloseClick()
            noteListViewModel.hideFreeNoteCountLayout(!isAllowToCreateNote())
        }
        binding!!.freeCountLayout.setOnUnlockClick {
            showRemoveCreateNoteRestrictionsDialogFragment("首页底部弹窗")
        }
        noteListViewModel.currentFreeCreatedNoteNumber.observe(viewLifecycleOwner) { value: Int ->
            if (!isAdded) return@observe
            if (value == CreateNoteLimit.currentMaxFreeCreatedNoteNumber) {
                binding?.freeCountLayout?.changeFreeNoteCountTitle(getString(R.string.free_note_title_used_up))
            } else {
                val bonusLimit =
                    CreateNoteLimit.currentMaxFreeCreatedNoteNumber - CreateNoteLimit.originalMaxFreeCreatedNoteNumber
                lifecycleScope.launch(Dispatchers.IO) {
                    val validDays = GainNoteLimitsPromotionManager.getNoteLimitsValidDays()
                    withContext(Dispatchers.Main) {
                        binding?.freeCountLayout?.changeFreeNoteCountTitle(
                            getString(
                                R.string.free_note_title_not_used_up,
                                value,
                                CreateNoteLimit.currentMaxFreeCreatedNoteNumber
                            ) + if (bonusLimit > 0 && validDays > 0) {
                                getString(
                                    R.string.free_note_title_check_in_quota,
                                    bonusLimit
                                )
                            } else {
                                ""
                            }
                        )
                    }
                }
            }
        }
        noteListViewModel.currentFileManagerGuideWindowStates.observe(viewLifecycleOwner) { isShow: Boolean ->
            if (!isAdded) return@observe
            if (isShow) {
                if (fileManagerdesktopOutlineGuideWindow != null && fileManagerdesktopOutlineGuideWindow!!.isShowing) {
                    fileManagerdesktopOutlineGuideWindow!!.setOnDismissListener { }
                    fileManagerdesktopOutlineGuideWindow!!.dismiss()
                }

                if (binding!!.fileManagerEnter.visibility != View.VISIBLE) {
                    noteListViewModel.changeCurrentFileManagerGuideWindowStates(false)
                    Preferences.isNeedShowFileManagerEnterGuideWindow = false
                    return@observe
                }
                binding!!.fileManagerEnter.post {
                    if (checkViewBindingValidity()) {
                        fileManagerdesktopOutlineGuideWindow =
                            FileManagerDesktopOutlineGuideWindow(
                                requireContext(),
                                binding!!.fileManagerEnter
                            )
                        fileManagerdesktopOutlineGuideWindow?.setOnDismissListener {
                            noteListViewModel.changeCurrentFileManagerGuideWindowStates(false)
                            Preferences.isNeedShowFileManagerEnterGuideWindow = false
                        }
                        fileManagerdesktopOutlineGuideWindow?.isClippingEnabled = false
                        fileManagerdesktopOutlineGuideWindow?.showAtLocation(
                            binding!!.fileManagerEnter,
                            Gravity.NO_GRAVITY,
                            0,
                            0
                        )
                    }
                }
            }
        }
        noteListViewModel.showFreeNoteCountLayout.observe(viewLifecycleOwner) { value: Boolean ->
            if (value && noteViewModel.isHiddenSpaceMode.value == false && !UserManager.isVip()) {
                binding!!.freeCountLayout.visibility = View.VISIBLE
            } else {
                binding!!.freeCountLayout.visibility = View.GONE
            }
        }

        noteListViewModel.notAllowImport.observe(viewLifecycleOwner) { notAllowImport ->
            binding!!.importCornerMark.isVisible = false
        }

        noteViewModel.documentUpdate.observe(viewLifecycleOwner) { document: Document? ->
            val indices = noteListAdapter!!.getMetaDocumentIndices((document)!!)
            if (indices.isNotEmpty()) {
                for (index in indices) {
                    noteListAdapter!!.notifyItemChanged(index, NoteListAdapter.DOC_UPDATE)
                }
            }
        }

        noteListViewModel.passwordState.observe(viewLifecycleOwner) { passwordState ->
            showPasswordKeyboardDialog(passwordState)
        }

        noteListViewModel.isSetQuestionByQADialog.observe(viewLifecycleOwner) { isSetQuestion ->
            if (isSetQuestion == null) {
                hideSecurityQuestionDialog()
            } else {
                showSecurityQuestionDialog(isSetQuestion)
            }
        }
        noteViewModel.isShowSnippetPage.observe(viewLifecycleOwner) { isShowSnippetPage ->
            binding!!.noteAndSnippetSelectView.setCurrentIsNoteMode(!isShowSnippetPage)
            if (isShowSnippetPage) {
                showSnippetPage()
            } else {
                hideSnippetPage()
            }
        }

        noteListViewModel.isShowFileManagerFolderCreateDialog.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                showFileManagerFolderCreateDialog()
            }
        }

        noteViewModel.selectedFolderList.observe(viewLifecycleOwner) { selectedFolderList ->
            val isRootFolder = noteViewModel.checkSelectedFolderListIsRootFolder()
            binding!!.fileManagerPathAll.text =
                getString(if (isRootFolder) R.string.folder_root_folder_title else R.string.file_manager_path_title)
            fileManagerPathAdapter?.updateFileList(selectedFolderList)
            if (isRootFolder && noteListViewModel.openOrCloseOutline.value == false) {
                binding!!.title.visibility = View.VISIBLE
                binding!!.fileManagerPathContainer.visibility = View.GONE
            } else {
                binding!!.title.visibility = View.INVISIBLE
                binding!!.fileManagerPathContainer.visibility = View.VISIBLE
                binding!!.fileManagerPath.overScrollRecyclerView.apply {
                    post {
                        if (canScrollHorizontally(RV_SCROLL_HORIZONAL_DIRECTION_RIGHT)) {
                            scrollBy(Int.MAX_VALUE, 0)
                        }
                    }
                }
            }
        }
        fileManagerPathAdapter = FileManagerPathAdapter(activity).apply {
            noteViewModel.selectedFolderList.value?.let {
                fileList = it
            }
            setOnItemClickListener { noteTreeFolder ->
                noteViewModel.selectFolder(noteTreeFolder)
                FolderManagerEvent.sendFolderManagerPathClickEvent()
            }
        }
        binding!!.fileManagerPath.overScrollRecyclerView.apply {
            layoutManager =
                LinearLayoutManager(activity, RecyclerView.HORIZONTAL, KiloApp.isLayoutRtl)
            adapter = fileManagerPathAdapter
        }

        if (savedInstanceState != null) {
            try {
                val lastClickDocumentIndex = savedInstanceState.getInt(
                    LAST_CLICK_DOCUMENT_INDEX_KEY
                )
                val documents = noteViewModel.allDocuments
                if (lastClickDocumentIndex >= 0 && lastClickDocumentIndex < documents.size) {
                    lastClickDocument = documents[lastClickDocumentIndex]
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            val verifyRandomCodeDialog =
                parentFragmentManager.findFragmentByTag(BaseVerifyRandomCodeDialog.TAG) as? VerifyRandomCodeDialog
            verifyRandomCodeDialog?.onVerifySuccess = this.onVerifySuccess

            val permissionErrorAlertDialog =
                parentFragmentManager.findFragmentByTag(PERMISSION_ERROR_TAG) as? AlertDialog
            permissionErrorAlertDialog?.setPositiveBtnListener {
                PermissionRequester.jumpToSetting(this@NoteListFragment)
            }
        }
        val dialog = this.getCommonPromptDialog()
        if (dialog != null) {
            dialog.onConfirmed = {
                openDocument()
            }
        }

        documentDragHelper = DocumentDragHelper(requireContext()) { adapterPosition, holder ->
            val adapter = noteListAdapter!!
            val viewType = adapter.getItemViewType(adapterPosition)
            var bitmap: Bitmap? = null
            if (adapter.isDocument(viewType)) {
                val noteCoverViewHolder = holder as NoteCoverViewHolder
                bitmap = Bitmap.createBitmap(
                    noteCoverViewHolder.noteCoverContainer.width,
                    noteCoverViewHolder.noteCoverContainer.height,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = Canvas(bitmap)
                noteCoverViewHolder.noteCoverContainer.draw(canvas)

            } else if (adapter.isFolder(viewType)) {
                val folderViewHolder = holder as NoteFolderViewHolder
                bitmap = Bitmap.createBitmap(
                    folderViewHolder.noteFolderContainer.width,
                    folderViewHolder.noteFolderContainer.height,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = Canvas(bitmap)
                folderViewHolder.noteFolderContainer.draw(canvas)
            }
            bitmap
        }
        documentDragHelper?.apply {
            setOnSelectListener { position ->
                val adapter = noteListAdapter ?: return@setOnSelectListener false
                return@setOnSelectListener adapter.isDocument(adapter.getItemViewType(position)) || adapter.isFolder(
                    adapter.getItemViewType(position)
                )
            }
            setOnDragAvailableListener { selectorPosition, pressedPosition ->
                if (pressedPosition < 0) return@setOnDragAvailableListener true
                if (selectorPosition == pressedPosition) return@setOnDragAvailableListener true
                val adapter = noteListAdapter ?: return@setOnDragAvailableListener true
                val sourceFile = adapter.getNoteTreeItem(selectorPosition)
                val targetFile = adapter.getNoteTreeItem(pressedPosition)
                if (sourceFile == null || targetFile == null) return@setOnDragAvailableListener true
                if (sourceFile !is NoteTreeMetaDocument || targetFile !is NoteTreeMetaDocument) return@setOnDragAvailableListener true
                return@setOnDragAvailableListener !(sourceFile is NoteTreeFolder && targetFile is NoteTreeDocument)
            }
            setOnDragListener { selectorPosition, pressedPosition, _, _ ->
                if (pressedPosition < 0) return@setOnDragListener false
                val adapter = noteListAdapter ?: return@setOnDragListener false
                val viewType = adapter.getItemViewType(pressedPosition)
                return@setOnDragListener if (adapter.isDocument(viewType) || adapter.isFolder(
                        viewType
                    )
                ) {
                    !adapter.isSameMetaDocument(selectorPosition, pressedPosition)
                } else {
                    false
                }
            }
            setOnDropListener { selectorPosition, pressedPosition, x, y ->
                if (pressedPosition >= 0) {
                    val adapter = noteListAdapter ?: return@setOnDropListener
                    val pressedViewType = adapter.getItemViewType(pressedPosition)
                    val selectNoteTreeItem = adapter.getNoteTreeItem(selectorPosition)

                    if (adapter.isDocument(pressedViewType)) {
                        when (selectNoteTreeItem) {
                            is NoteTreeDocument -> {
                                val documents = mutableListOf<Document>()
                                documents.add(selectNoteTreeItem.document)
                                adapter.getDocument(pressedPosition)?.let {
                                    documents.add(it)
                                }
                                val title = noteViewModel.getNewFolderTitle(
                                    resources.getString(R.string.folder_default_name),
                                    noteViewModel::verifyFolderTitle
                                )
                                documents.sortBy {
                                    it.modifiedTime
                                }
                                val newFolder = noteViewModel.createFolder(title, documents)
                                HomeEvent.sendHomeCreateFolder()
                                FolderManagerEvent.sendFolderManagerNewFolderSuccess(
                                    FolderManagerEvent.NEW_FOLDER_LOCATION_NOTE_LIST_DRAG,
                                    newFolder.level
                                )
                                FolderManagerEvent.sendFolderManagerItemDragPositionChangeEvent(
                                    FolderManagerEvent.DRAG_NOTE_LIST_LOCATION
                                )
                            }

                            is NoteTreeFolder -> {

                            }

                            else -> {

                            }

                        }

                    } else if (adapter.isFolder(pressedViewType)) {
                        if (selectNoteTreeItem == null) return@setOnDropListener
                        val pressedItem = adapter.getFolder(pressedPosition)
                        when (selectNoteTreeItem) {
                            is NoteTreeDocument -> {
                                noteViewModel.moveMeteDocuments(
                                    selectNoteTreeItem.document,
                                    targetFolder = pressedItem
                                )
                                HomeEvent.sendFolderInsertFile()
                                FolderManagerEvent.sendFolderManagerItemDragPositionChangeEvent(
                                    FolderManagerEvent.DRAG_NOTE_LIST_LOCATION
                                )
                            }

                            is NoteTreeFolder -> {
                                noteViewModel.moveMeteDocuments(
                                    selectNoteTreeItem.folder,
                                    targetFolder = pressedItem
                                )
                                FolderManagerEvent.sendFolderManagerItemDragPositionChangeEvent(
                                    FolderManagerEvent.DRAG_NOTE_LIST_LOCATION
                                )
                            }

                            else -> {

                            }

                        }

                    }
                }
            }
            setIsShotOnTargetViewListener { viewHolder, x, y ->
                when (viewHolder) {
                    is NoteCoverViewHolder -> {
                        viewHolder.noteCoverContainer.getLocationOnScreen(
                            computeShotViewTempIntArray
                        )
                        computeShotViewTempRect.set(
                            computeShotViewTempIntArray[0],
                            computeShotViewTempIntArray[1],
                            computeShotViewTempIntArray[0] + viewHolder.noteCoverContainer.width,
                            computeShotViewTempIntArray[1] + viewHolder.noteCoverContainer.height
                        )
                        computeShotViewTempRect.contains(x.toInt(), y.toInt())
                    }

                    is NoteFolderViewHolder -> {
                        viewHolder.noteFolderContainer.getLocationOnScreen(
                            computeShotViewTempIntArray
                        )
                        computeShotViewTempRect.set(
                            computeShotViewTempIntArray[0],
                            computeShotViewTempIntArray[1],
                            computeShotViewTempIntArray[0] + viewHolder.noteFolderContainer.width,
                            computeShotViewTempIntArray[1] + viewHolder.noteFolderContainer.height
                        )
                        computeShotViewTempRect.contains(x.toInt(), y.toInt())
                    }

                    else -> {
                        false
                    }
                }
            }
        }?.attachToRecyclerView(binding!!.noteRv.overScrollRecyclerView)

        binding!!.title.setOnClickListener(
            MultipleClicksListener { v: View?, currentTimes: Int, totalCounts: Int ->
                if (noteViewModel.isHiddenSpaceMode.value == true) return@MultipleClicksListener
                if (currentTimes != totalCounts) return@MultipleClicksListener
                if (getBackPasswordDialog.isAdded) return@MultipleClicksListener
                getBackPasswordDialog.safeShow(parentFragmentManager, null)
            })

        binding!!.fileManagerEnter.setOnClickListener {
            Preferences.isNeedShowFileManagerEnterGuideWindow = false
            if (noteViewModel.isHiddenSpaceMode.value == true) {
                noteListViewModel.changeHiddenOutlineStates(true)
            } else {
                noteListViewModel.changeOutlineStates(true)
            }
        }

        binding!!.fileManagerClose.setOnClickListener {
            if (noteViewModel.isHiddenSpaceMode.value == true) {
                noteListViewModel.changeHiddenOutlineStates(false)
            } else {
                noteListViewModel.changeOutlineStates(false)
            }
        }

        binding!!.fileManagerFolderCreate.setOnClickListener {
            noteViewModel.selectedDocumentsToBeCombined.clear()
            noteListViewModel.createFolderLocation =
                FolderManagerEvent.NEW_FOLDER_LOCATION_FOLDER_MANAGER_ICON
            noteListViewModel.changesShowFileManagerFolderCreateDialog(true)
        }

        binding!!.title.setOnLongClickListener {
            if (noteViewModel.isHiddenSpaceMode.value == true) return@setOnLongClickListener false
            verifyRandomCodeDialog.show(parentFragmentManager, BaseVerifyRandomCodeDialog.TAG)
            true
        }
        binding!!.noteAndSnippetSelectView.apply {
            isNoteSelectModeClickCallback = { isNoteSelectMode ->
                noteViewModel.isShowSnippetPage.value = !isNoteSelectMode
                if (!isNoteSelectMode) {
                    SnippetEvent.sendSnippetPageEntranceClick()
                } else {
                    snippetEditViewModel.changeSnippetPageEditMode(false)
                    snippetEditViewModel.clearCurrentSelectedSnippet()
                }
            }
        }

        fileManagerAdapter = FileManagerAdapter(
            activity,
            onItemClickListener = { noteTreeItem, _ ->
                when (noteTreeItem) {
                    is NoteTreeFolder -> {
                        noteViewModel.selectFolder(noteTreeItem)
                    }

                    is NoteTreeDocument -> {
                        val parentNoteTreeItem =
                            noteViewModel.allNoteTreeItemList.value?.get(noteTreeItem.parentIndex)
                        if (parentNoteTreeItem is NoteTreeFolder) {
                            noteViewModel.selectFolder(parentNoteTreeItem)
                        }
                        handleDocumentClick(noteTreeItem.document)
                    }

                    else -> {

                    }
                }
            },
            onFolderOpenClickListener = { noteTreeFolder, _ ->
                if (noteTreeFolder.opened) {
                    noteViewModel.closeFolder(noteTreeFolder)
                } else {
                    noteViewModel.openFolder(noteTreeFolder)
                }
            })

        binding?.noteTreeOutline?.overScrollRecyclerView?.apply {
            adapter = fileManagerAdapter
            layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
            fileManagerNoteTreeItemDecoration = FileManagerNoteTreeItemDecoration(
                dotRadius = resources.getDimension(R.dimen.dp_3),
                horizontalPadding = resources.getDimensionPixelSize(R.dimen.dp_12),
                noteViewModel.getCurrentDocumentSortType()
            )
            fileManagerNoteTreeItemDecoration?.let {
                addItemDecoration(it)
            }
        }

        noteViewModel.allNoteTreeItemList.observe(viewLifecycleOwner) { allNoteTreeItemList ->
            fileManagerAdapter?.submitList(allNoteTreeItemList)
            fileManagerNoteTreeItemDecoration?.noteOrganizeAndSortType =
                noteViewModel.getCurrentDocumentSortType()
        }

        fileManagerDragHelper = FileManagerDragHelper(requireContext()) { adapterPosition, holder ->
            /**
             * 生成拖动窗口所显示的bitmap，即条目内容
             */
            val adapter = fileManagerAdapter!!
            var bitmapDragged: Bitmap? = null
            when (holder) {
                is NoteTreeDocumentViewHolder -> {
                    bitmapDragged = Bitmap.createBitmap(
                        holder.content.width,
                        holder.content.height,
                        Bitmap.Config.ARGB_8888
                    )
                    val canvas = Canvas(bitmapDragged)
                    holder.content.draw(canvas)
                }

                is NoteTreeFolderViewHolder -> {
                    bitmapDragged = Bitmap.createBitmap(
                        holder.content.width,
                        holder.content.height,
                        Bitmap.Config.ARGB_8888
                    )
                    val canvas = Canvas(bitmapDragged)
                    holder.content.draw(canvas)
                }

                else -> {}
            }
            bitmapDragged
        }

        fileManagerDragHelper?.apply {
            setupBlurView(binding!!.root)
            setOnSelectListener { position ->
                /**
                 * 定义能够触发长按拖动的条目类型：笔记和文件夹
                 */
                val adapter = fileManagerAdapter ?: return@setOnSelectListener false
                if (position == 0) return@setOnSelectListener false
                return@setOnSelectListener adapter.isDocument(
                    adapter.getItemViewType(
                        position
                    )
                ) || adapter.isFolder(
                    adapter.getItemViewType(position)
                )
            }
            setOnDragListener { selectorPosition, pressedPosition, x, y ->
                /**
                 * pressedPosition != -1时 为有效悬浮处的位置，如长按选中了第1个元素，拖动到了第3个位置并悬浮着
                 * 结果如下：
                 *  （1）第3位置是有效位置，可接纳被拖动的1号元素，返回true
                 *  （2）第3位置不是有效位置，是1号元素的子目录，返回false
                 */
                LogHelper.d(
                    TAG,
                    "selectorPosition = $selectorPosition, pressedPosition = $pressedPosition"
                )
                if (pressedPosition < 0) return@setOnDragListener false
                val adapter = fileManagerAdapter ?: return@setOnDragListener false
                val sourceFile = adapter.getNoteTreeMetaDocumentByPosition(selectorPosition)
                val targetFile = adapter.getNoteTreeMetaDocumentByPosition(pressedPosition)
                if (sourceFile == null || targetFile == null) {
                    return@setOnDragListener false
                }
                return@setOnDragListener noteViewModel.isCombineOperationValid(
                    sourceFile.metaDocument,
                    targetFile.metaDocument
                )
            }
            setOnDragAvailableListener { selectorPosition, pressedPosition ->
                if (pressedPosition < 0) return@setOnDragAvailableListener true
                if (selectorPosition == pressedPosition) return@setOnDragAvailableListener true
                val adapter = fileManagerAdapter ?: return@setOnDragAvailableListener true
                val sourceFile = adapter.getNoteTreeMetaDocumentByPosition(selectorPosition)
                val targetFile = adapter.getNoteTreeMetaDocumentByPosition(pressedPosition)
                if (sourceFile == null || targetFile == null) {
                    return@setOnDragAvailableListener false
                }
                return@setOnDragAvailableListener noteViewModel.isCombineOperationValid(
                    sourceFile.metaDocument,
                    targetFile.metaDocument
                )
            }
            setIsShotOnTargetViewListener { viewHolder, x, y ->
                //todo 判断是否命中目标View
                when (viewHolder) {
                    is NoteTreeDocumentViewHolder -> {
                        viewHolder.rootView.getLocationOnScreen(
                            computeFileManagerShotViewTempIntArray
                        )
                        computeFileManagerShotViewTempRect.set(
                            computeFileManagerShotViewTempIntArray[0],
                            computeFileManagerShotViewTempIntArray[1],
                            computeFileManagerShotViewTempIntArray[0] + viewHolder.rootView.width,
                            computeFileManagerShotViewTempIntArray[1] + viewHolder.rootView.height
                        )
                        computeFileManagerShotViewTempRect.contains(x.toInt(), y.toInt())

                    }

                    is NoteTreeFolderViewHolder -> {
                        viewHolder.rootView.getLocationOnScreen(
                            computeFileManagerShotViewTempIntArray
                        )
                        computeFileManagerShotViewTempRect.set(
                            computeFileManagerShotViewTempIntArray[0],
                            computeFileManagerShotViewTempIntArray[1],
                            computeFileManagerShotViewTempIntArray[0] + viewHolder.rootView.width,
                            computeFileManagerShotViewTempIntArray[1] + viewHolder.rootView.height
                        )
                        computeFileManagerShotViewTempRect.contains(x.toInt(), y.toInt())
                    }

                    else -> {
                        false
                    }
                }
            }
            setOnDropListener { selectorPosition, enabledPosition, x, y ->
                if (enabledPosition < 0) return@setOnDropListener
                val adapter = fileManagerAdapter ?: return@setOnDropListener
                val sourceFile = adapter.getNoteTreeMetaDocumentByPosition(selectorPosition)
                val targetFile = adapter.getNoteTreeMetaDocumentByPosition(enabledPosition)
                if (sourceFile == null || targetFile == null) {
                    return@setOnDropListener
                }
                val isBothDocument =
                    sourceFile is NoteTreeDocument && targetFile is NoteTreeDocument
                if (isBothDocument) {
                    sourceFile as NoteTreeDocument
                    targetFile as NoteTreeDocument
                    noteViewModel.selectedDocumentsToBeCombined.clear()
                    noteViewModel.selectedDocumentsToBeCombined.add(sourceFile.document)
                    noteViewModel.selectedDocumentsToBeCombined.add(targetFile.document)
                    noteListViewModel.createFolderLocation =
                        FolderManagerEvent.NEW_FOLDER_LOCATION_FOLDER_MANAGER_DRAG
                    noteListViewModel.changesShowFileManagerFolderCreateDialog(true)
                } else {
                    val isCombineValid = noteViewModel.isCombineOperationValid(
                        sourceFile.metaDocument,
                        targetFile.metaDocument
                    )
                    if (isCombineValid) {
                        noteViewModel.handleFileManagerDragToCombine(sourceFile, targetFile)
                    }
                }

            }
        }?.attachToRecyclerView(binding!!.noteTreeOutline.overScrollRecyclerView)

        syncViewModel.toLogin.observe(viewLifecycleOwner) { toLogin ->
            if (toLogin) {
                if (NetworkUtils.isNetworkAvailable()) {
                    when (syncViewModel.syncRemoteChannel.value) {
                        SyncManager.REMOTE_CHANNEL_BAIDU -> {
                            if (isWebViewAvailable()) {
                                val intent = Intent(
                                    activity, BaiduLoginActivity::class.java
                                )
                                loginBaiduLauncher.launch(intent)
                            } else {
                                showWebViewNotAvailableDialog()
                            }
                        }

                        SyncManager.REMOTE_CHANNEL_DROPBOX -> {
                            val syncUserUtil = syncViewModel.getCurrentSyncUserUtil()
                            if (syncUserUtil is DropboxOauthUtil) {
                                syncUserUtil.dropboxOauthManager.startDropboxAuthorization2PKCE(
                                    requireContext()
                                )
                            }
                        }

                        SyncManager.REMOTE_CHANNEL_GOOGLE_DRIVE -> {
                            val syncUserUtil = syncViewModel.getCurrentSyncUserUtil()
                            if (syncUserUtil is GoogleDriveOauthUtil) {
                                startActivityForResult.launch(
                                    syncUserUtil.googleDriveOauthManager.doOnAuth(
                                        requireContext()
                                    )
                                )
                            }
                        }
                    }
                } else {
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            AppUtils.getString(R.string.network_not_connected)
                        )
                    }
                }
                syncViewModel.changeToLogin(false)
            }
        }
        if (noteViewModel.isHiddenSpaceMode.value == false) {
            combineViewModel.combineResult.observe(viewLifecycleOwner) { state ->
                if (state == null) return@observe
                when (state) {
                    is CombineFailed -> {
                        ToastUtils.windowTopCenter(
                            requireActivity(),
                            R.string.combine_documents_failed_prompt
                        )
                        DocumentCombineEvent.sendDocumentCombineFailedEvent(state.message)
                    }

                    is CombineSuccess -> {
                        lifecycleScope.launch {
                            if (combineViewModel.deleteOriginDocuments.value == true) {
                                val documents = combineViewModel.combineDocumentList.value
                                if (documents != null) {
                                    noteViewModel.deleteDocuments(documents)
                                }
                            }
                            noteViewModel.addDocument(state.document)
                            CreateNoteLimit.increaseCreatedNoteNumber()
                            DocumentCombineEvent.sendDocumentCombineSuccessEvent(
                                combineViewModel.deleteOriginDocuments.value == true
                            )
                        }
                    }

                    CombineCancelled -> {}
                }
                combineViewModel.markCombineResultReceived()
            }
        }
        noteListViewModel.isUserIdentityValid.observe(viewLifecycleOwner) { isValid ->
            if (isValid) {
                //初始时非会员展示，后再开通会员仍然要展示
                checkInViewModel.isCheckInPromotionFinished.value?.let { isCheckInPromotionFinished ->
                    setCheckInEnterVisible(isCheckInPromotionFinished)
                }
            }
        }
        checkInViewModel.isCheckInPromotionFinished.observe(viewLifecycleOwner) { isCheckInPromotionFinished ->
            setCheckInEnterVisible(isCheckInPromotionFinished)
        }
        checkInViewModel.invalidDays.observe(viewLifecycleOwner) { invalidDays ->
            checkInViewModel.remainedLimits.value?.let { remainedLimits ->
                if (Preferences.needShowCheckInRemainedNoteUnUseDialog && invalidDays in 1..7 && remainedLimits > 0) {
                    showCheckInRemainedNoteUnUseDialog(remainedLimits, invalidDays)
                }
            }
        }
        binding!!.checkInEnter.setOnClickListener {
            CheckInEvent.sendCheckInEnterClick()
            noteListViewModel.changeIsShowCheckInPromotionDialog(true)
        }

        binding!!.fileManagerPathAll.setOnClickListener {
            noteViewModel.getFileManagerRootFolder()?.let { rootFolder ->
                noteViewModel.selectFolder(rootFolder)
                FolderManagerEvent.sendFolderManagerPathClickEvent()
            }
        }

        createNotebookViewModel.createdDocumentByImageUri.observe(viewLifecycleOwner) { document ->
            if (document != null) {
                noteViewModel.addDocument(document)
                noteViewModel.currentDoc = document
                noteViewModel.clearPreviousNoteParams()
                if (createdNoteInCurrentViewLifecycle) {
                    safeNavigate(R.id.edit)
                }
                createNotebookViewModel.markCreatedDocumentByImageUriProcessed()
            }
        }
    }

    private fun launchImport(showTipsAsToast: Boolean = false) {
        try {
            KiloApp.app.provisionallyJumpToOtherApp = true
            mGetDocument.launch(arrayOf(MimeType.PDF.mime, MimeType.ZIP.mime))
        } catch (e: ActivityNotFoundException) {
            if (showTipsAsToast) {
                ToastUtils.topCenter(
                    requireContext(),
                    R.string.note_list_open_document_manager_failed_tip
                )
            } else {
                val tipWindow = BubbleTipWindow(requireContext()).apply {
                    setTipsTextSize(
                        resources.getDimensionPixelSize(R.dimen.sp_23).toFloat()
                    )
                    setKnowTextSize(
                        resources.getDimensionPixelSize(R.dimen.sp_23).toFloat()
                    )
                    setTips(getString(R.string.note_list_open_document_manager_failed_tip))
                }
                tipWindow.show(binding!!.importBtn)
            }
        }
    }

    private fun isSatisfyConditionOfFileManagerSplitScreen(): Boolean {
        return isLandAndFullScreen() || isPortraitAndFullScreen() || isLandTwoThirdScreenOrLargerButNotFullScreen()
    }

    private fun translateNoteOutline(
        isShow: Boolean,
        isRestore: Boolean = false,
        callback: (() -> Unit)? = null,
    ) {
        binding?.fileManagerContainer?.post {
            val animatorArgs =
                if (KiloApp.isLayoutRtl) {
                    if (isShow) floatArrayOf(
                        0f,
                        -resources.getDimensionPixelSize(R.dimen.dp_430).toFloat()
                    ) else {
                        floatArrayOf(resources.getDimensionPixelSize(R.dimen.dp_430).toFloat())
                    }
                } else {
                    if (isShow) floatArrayOf(
                        resources.getDimensionPixelSize(R.dimen.dp_430).toFloat()
                    ) else {
                        floatArrayOf(0F, -resources.getDimensionPixelSize(R.dimen.dp_430).toFloat())
                    }
                }
            fileManagerAnimator = ValueAnimator.ofFloat(*animatorArgs).apply {
                duration =
                    if (isOSAction || isRestore) FILE_MANAGER_NO_ANIMATION_DURATION else FILE_MANAGER_ANIMATION_DURATION_TIME
                addUpdateListener {
                    if (checkViewBindingValidity()) {
                        binding!!.fileManagerAnimGroup.translationX = it.animatedValue as Float
                        val params =
                            binding!!.fileManagerPathContainer.layoutParams as ViewGroup.MarginLayoutParams
                        params.marginStart =
                            requireContext().resources.getDimensionPixelSize(R.dimen.dp_30) +
                                    if (KiloApp.isLayoutRtl) {
                                        if (!isShow) resources.getDimensionPixelSize(
                                            R.dimen.dp_430
                                        ) - ((it.animatedValue as Float)).toInt() else abs((it.animatedValue as Float).toInt())
                                    } else {
                                        if (!isShow) ((it.animatedValue as Float) + resources.getDimensionPixelSize(
                                            R.dimen.dp_430
                                        )).toInt() else (it.animatedValue as Float).toInt()
                                    }
                        binding!!.fileManagerPathContainer.layoutParams = params
                    }
                }
                doOnStart {
                    if (checkViewBindingValidity()) {
                        if (isShow) {
                            binding!!.fileManagerEnter.visibility = View.GONE
                            binding!!.title.visibility = View.INVISIBLE
                        }
                    }
                }
                doOnEnd {
                    if (checkViewBindingValidity()) {
                        if (!isShow) {
                            binding!!.fileManagerEnter.visibility =
                                if (isSatisfyConditionOfFileManagerSplitScreen()) View.VISIBLE else View.GONE
                        }

                        if (noteViewModel.checkSelectedFolderListIsRootFolder()) {
                            if (isShow) {
                                // layer and layer's child view
                                // layer's child view  don't work
                                binding!!.title.visibility = View.INVISIBLE
                                binding!!.fileManagerPathContainer.visibility = View.VISIBLE
                            } else {
                                binding!!.title.visibility = View.VISIBLE
                                binding!!.fileManagerPathContainer.visibility = View.GONE
                            }
                        } else {
                            binding!!.title.visibility = View.INVISIBLE
                            binding!!.fileManagerPathContainer.visibility = View.VISIBLE
                            binding!!.fileManagerPath.overScrollRecyclerView.apply {

                                post {
                                    if (canScrollHorizontally(RV_SCROLL_HORIZONAL_DIRECTION_RIGHT)) {
                                        scrollBy(Int.MAX_VALUE, 0)
                                    }
                                }
                            }
                        }
                        val spanCount =
                            if (isShow)
                                getNoteSpanCountByWith(binding!!.fileManagerContainer.width)
                            else
                                getNoteSpanCountByWith(0)
                        refreshSpanSizeLookup(spanCount)
                        callback?.invoke()
                        val list =
                            noteViewModel.selectedNoteTreeItemList.value ?: return@doOnEnd
                        gridCategoryItemDecoration?.resetItemEnd()
                        refreshNoteRvItemDecoration(list)
                        refreshFileManagerContainerAndNoteRv(isShow)
                    }
                }
            }
            fileManagerAnimator?.start()
        }
    }

    private fun refreshFileManagerContainerAndNoteRv(isFileManagerContainer: Boolean) {
        // update constraint set
        val constraintSet = ConstraintSet()
        constraintSet.clone(binding?.root)
        constraintSet.clear(binding!!.fileManagerContainer.id, ConstraintSet.START)
        constraintSet.clear(binding!!.fileManagerContainer.id, ConstraintSet.END)
        if (isFileManagerContainer) {
            constraintSet.connect(
                binding!!.fileManagerContainer.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START
            )
            constraintSet.connect(
                binding!!.fileManagerContainer.id,
                ConstraintSet.END,
                binding!!.noteRv.id,
                ConstraintSet.START
            )
        } else {
            constraintSet.connect(
                binding!!.fileManagerContainer.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START
            )
        }
        constraintSet.applyTo(binding!!.root)

        // update note list
        val adapter = binding?.noteRv?.overScrollRecyclerView?.adapter
        adapter?.notifyDataSetChanged()
    }

    private fun showCheckInRemainedNoteUnUseDialog(remainedLimits: Int, invalidDays: Int) {
        var dialog = parentFragmentManager.findFragmentByTag(CheckInNoteUnUseDialog.TAG)
        if (dialog != null && dialog is CheckInNoteUnUseDialog) return
        dialog = CheckInNoteUnUseDialog().apply {
            setTitle(
                AppUtils.getString(
                    R.string.check_in_notice_remained_note_un_use,
                    "$remainedLimits",
                    "$invalidDays"
                )
            )
        }
        dialog.safeShow(parentFragmentManager, CheckInNoteUnUseDialog.TAG)
    }

    private fun showFileManagerDialog(targetMetaDocument: MetaDocument) {
        if (isAdded) {
            val dialog = parentFragmentManager.findFragmentByTag(FileManagerMoveDialog.TAG)
            val fileManagerMoveDialog =
                if (dialog is FileManagerMoveDialog) dialog else FileManagerMoveDialog()
            fileManagerMoveDialog.apply {
                noteViewModel.setTargetSourceMetaDocument(targetMetaDocument)
            }.safeShow(parentFragmentManager, FileManagerMoveDialog.TAG)
            FolderManagerEvent.sendFolderManagerMoveDialogSelectPositionEvent()
        }
    }


    override fun onStart() {
        super.onStart()
        // 修复vivo、联想机型生命周期不规范，在部分分屏操作下不走完整生命周期导致弹窗位置错误
        if (settingWindow != null) {
            settingWindow!!.setOnDismissListener { }
            settingWindow!!.dismiss()
        }
    }

    override fun onPause() {
        super.onPause()
    }

    override fun onResume() {
        super.onResume()
        showMemberEnterIcon()
        val syncOauthUtil = syncViewModel.getCurrentSyncUserUtil()
        if (syncOauthUtil is DropboxOauthUtil) {
            syncOauthUtil.apply {
                dropboxOauthManager.storeLoginInfo {
                    SyncCore.syncRemoteChannel = SyncManager.REMOTE_CHANNEL_DROPBOX
                    syncViewModel.changeSyncRemoteChannel(SyncManager.REMOTE_CHANNEL_DROPBOX)
                    showSyncLogInDialog()
                }
            }
        }
        noteViewModel.isShowSettingWindow.observe(viewLifecycleOwner) { isNeedShow ->
            if (isNeedShow) {
                if (settingWindow == null || (settingWindow != null && !settingWindow!!.isShowing)) {
                    showSettingsWindow()
                }
            } else {
                if (settingWindow != null && settingWindow!!.isShowing()) {
                    settingWindow!!.dismiss()
                }
                if (hiddenSpaceGuideWindow != null && hiddenSpaceGuideWindow!!.isShowing) {
                    hiddenSpaceGuideWindow!!.dismiss()
                }
            }
        }

        noteViewModel.isShowSortWindow.observe(viewLifecycleOwner) { isNeedShow ->
            if (isNeedShow) {
                if (noteSortWindow == null || !noteSortWindow!!.isShowing) {
                    showNoteSortWindow()
                }
            } else {
                if (noteSortWindow != null && noteSortWindow!!.isShowing) {
                    noteSortWindow!!.dismiss()
                }
            }
        }
        checkInViewModel.isCheckInPromotionFinished()
        checkInViewModel.fetchCheckInNoteLimits()
        checkInViewModel.fetchCheckInInvalidDays()
    }

    private fun setCheckInEnterVisible(prerequisite: Boolean) {
        val isVisible = if (Preferences.needShowCheckInGuideDialog) {
            !DimensionUtil.isLikeLandAndOneThirdScreen(
                context
            ) && !prerequisite && noteViewModel.isHiddenSpaceMode.value == false && UserUsageConfig.userHasCheckInParticipationRight
        } else {
            !DimensionUtil.isLikeLandAndOneThirdScreen(
                context
            ) && !prerequisite && noteViewModel.isHiddenSpaceMode.value == false
        }
        if (isVisible) {
            CheckInEvent.sendCheckInEnterShow()
        }
        binding!!.checkInEnter.isVisible = isVisible
        binding!!.checkInTimeLimit.isVisible = isVisible
    }

    private fun showMemberEnterIcon() {
        val isVisible = !UserManager.isVip()
        val topBarLp = binding!!.topBar.layoutParams as ConstraintLayout.LayoutParams
        if (isLandAndOneThirdScreen() || isPortraitAndOneThirdScreenOrLikeXiaoMiPad5PortraitHalfScreen()) {
            binding!!.memberEnterIcon.isVisible = isVisible

            topBarLp.topToTop = ConstraintLayout.LayoutParams.UNSET
            topBarLp.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
            topBarLp.topToBottom = binding!!.title.id
        } else {

            topBarLp.topToTop = binding!!.title.id
            topBarLp.bottomToBottom = binding!!.title.id
            topBarLp.topToBottom = ConstraintLayout.LayoutParams.UNSET
        }

        binding!!.topBar.layoutParams = topBarLp
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (lastClickDocument != null) {
            outState.putInt(
                LAST_CLICK_DOCUMENT_INDEX_KEY,
                noteViewModel.allDocuments.indexOf(lastClickDocument)
            )
        }
    }

    private fun showCreateNoteLimitDialog(source: String) {
        val activity = activity
        if (activity is MainActivity) {
            activity.showCreateNoteLimitDialog(source)
        }
    }

    private fun showRemoveCreateNoteRestrictionsDialogFragment(source: String) {
        val activity = activity
        if (activity is MainActivity) {
            activity.showRemoveCreateNoteRestrictionsDialogFragment(source)
        }
    }

    private fun openDocument() {
        if (lastClickDocument == null) {
            noteViewModel.isOpenDocument.value = false
            if (isAdded) {
                hideLoadingDialog()
            }
            return
        }
        // parse document pages
        noteViewModel.clearPreviousNoteParams()
        noteViewModel.loadDocumentPages(lastClickDocument!!) { status: DocumentManager.DocumentStatus ->
            noteViewModel.isOpenDocument.value = false
            if (!isAdded) {
                return@loadDocumentPages
            }
            hideLoadingDialog()
            if (status === DocumentManager.DocumentStatus.OK) {
                if (lastClickDocument?.pageViewPortOffsetToPaperInPercent?.isValid() == true) {
                    PageViewportResizingEvent.sendNoteOpenWithPageViewPort()
                }

                editorAdViewModel.showInterstitialAdIfNeeded(
                    requireActivity(),
                    AppAdEvent.InterstitialAdPosition.ENTER_EDITOR
                )
                    .observe(viewLifecycleOwner) { hasDone ->
                        if (hasDone) {
                            val action = NoteListFragmentDirections.edit()
                            safeNavigate(action)
                        }
                    }
            } else if (status === DocumentManager.DocumentStatus.PAGE_MISSING) {
                showDocumentPageMissingDialog()
                sendExceptionalContactShow()
            } else {
                sendNoteBookUnusualEvent("three")
                sendExceptionalContactShow()
                showFileCorruptedDialog(lastClickDocument!!)
            }
        }
    }

    override fun getCurrentNavigationId(): Int {
        return R.id.note_list
    }

    private fun showSettingsWindow() {
        settingWindow =
            SettingWindow(requireActivity(), noteViewModel.isHiddenSpaceMode.value ?: false)
        settingWindow!!.setMenuClickAction { menuType: SettingWindow.MenuType? ->
            settingWindow?.dismiss()
            when (menuType) {
                SettingWindow.MenuType.BACKUP_SPACE -> {
                    safeNavigate(R.id.backup_space_fragment)
                    BackupUploadTaskManager.resetUploadStatusIfNeeded()
                    CloudBackupEvent.sendSettingsClickMyBackupSpace()
                }

                SettingWindow.MenuType.FEEDBACK -> {
                    val feedback = AppConfig.EMAIL
                    val success = newPlain(requireContext(), feedback)
                    if (success) {
                        ToastUtils.windowTopCenter(requireActivity(), R.string.copy_success)
                    } else {
                        newPlain(requireContext(), feedback)
                    }
                }

                SettingWindow.MenuType.BACKUP -> {
                    if (noteViewModel.isHiddenSpaceMode.value == true) {
                        //隐藏空间数据备份
                        backupViewModel.setDocuments(noteViewModel.allHiddenDocuments)
                        BackupDialog().show(parentFragmentManager, BackupDialog.TAG)
                    } else {
                        val documents = noteViewModel.allUnhiddenDocuments
                        backupViewModel.setDocuments(documents)
                        showBackupDialog()
                    }
                }

                SettingWindow.MenuType.ABOUT -> {
                    val intent = Intent(
                        activity, AboutActivity::class.java
                    )
                    aboutActivityLauncher.launch(intent)
                }

                SettingWindow.MenuType.AI_POINTS -> {
                    val loggedInUser = UserManager.loggedInUser
                    if (loggedInUser != null) {
                        showAiPointsDialog()
                    } else {
                        callLogin()
                    }
                }

                SettingWindow.MenuType.CREATOR_COMMUNITY -> {
                    showCreatorClubDialog()
                }

                SettingWindow.MenuType.CANCEL -> startActivity(
                    Intent(
                        activity, AccountCancellationActivity::class.java
                    )
                )

                SettingWindow.MenuType.TEMPLATE_TOOL -> if (BuildConfig.BUILD_TYPE.equals(
                        BUILD_TYPE_TEMPLATE,
                        ignoreCase = true
                    )
                ) {
                    safeNavigate(R.id.template_list)
                }

                SettingWindow.MenuType.REDEEM_CODE -> {
                    showRedeemCodeDialog()
                }

                SettingWindow.MenuType.SYNC -> {
                    showSyncLogInDialog()
                }

                SettingWindow.MenuType.COMBINE_DOCUMENTS -> {
                    DocumentCombineEvent.sendCombineDocumentClickEvent()
                    combineViewModel.setDocuments(
                        if (noteViewModel.isHiddenSpaceMode.value == true) {
                            noteViewModel.allHiddenDocuments
                        } else {
                            noteViewModel.allUnhiddenDocuments
                        }
                    )
                    showCombineDocumentsSelectionDialog()
                }

                else -> {}
            }
            null
        }
        settingWindow!!.setOnDismissListener {
            noteViewModel.isShowSettingWindow.setValue(
                false
            )
        }

        val parentView = if (noteViewModel.isHiddenSpaceMode.value == true) {
            binding!!.hiddenSpaceDataBackupBtn
        } else {
            binding!!.settingBtn
        }
        parentView.post {
            if (activity == null || requireActivity().isFinishing) return@post
            if (parentView.windowToken == null) return@post
            val settingBtnLocation = IntArray(2)
            parentView.getLocationInWindow(settingBtnLocation)
            settingWindow!!.contentView.measure(
                View.MeasureSpec.UNSPECIFIED,
                View.MeasureSpec.UNSPECIFIED
            )
            val settingMeasuredWidth: Int = settingWindow!!.contentView.measuredWidth
            val settingXOffset: Int =
                parentView.width / 2 + settingBtnLocation[0] - (settingMeasuredWidth - resources.getDimensionPixelSize(
                    R.dimen.dp_55
                ))
            val settingYOffset: Int = parentView.height + settingBtnLocation[1]

            //构建PopupWindow
            settingWindow!!.showAtLocation(
                parentView,
                Gravity.NO_GRAVITY,
                settingXOffset,
                settingYOffset
            )
            settingWindow!!.binding.root.adjustBackgroundRtlOrLtr(KiloApp.isLayoutRtl)
            if (UserUsageConfig.isNeedShowHiddenSpaceGuide) {
                changeWindowAlpha(ALPHA_TRANSLUCENT, false)
                if (hiddenSpaceGuideWindow != null && hiddenSpaceGuideWindow!!.isShowing) {
                    hiddenSpaceGuideWindow!!.setOnDismissListener { }
                    hiddenSpaceGuideWindow!!.dismiss()
                }
                hiddenSpaceGuideWindow =
                    HiddenSpaceGuideWindow(requireActivity(), isLandAndHalfScreen())
                hiddenSpaceGuideWindow!!.setOnDismissListener {
                    changeWindowAlpha(ALPHA_TRANSPARENT, true)
                    UserUsageConfig.isNeedShowHiddenSpaceGuide = false
                    settingWindow!!.updateAboutBgView()
                }
                hiddenSpaceGuideWindow!!.contentView.measure(
                    View.MeasureSpec.UNSPECIFIED,
                    View.MeasureSpec.UNSPECIFIED
                )
                hiddenSpaceGuideWindow!!.setTouchInterceptor { v, event ->
                    val isOuterClick =
                        hiddenSpaceGuideWindow!!.contentView.outsideArea(event)
                    if (isOuterClick) {
                        hiddenSpaceGuideWindow!!.dismiss()
                        return@setTouchInterceptor true
                    }
                    false
                }
                if (isLandAndHalfScreen()) {
                    val offsetY =
                        settingYOffset + hiddenSpaceGuideWindow!!.contentView.measuredHeight / 2 + resources.getDimensionPixelSize(
                            R.dimen.dp_270
                        )
                    hiddenSpaceGuideWindow!!.showAtLocation(
                        binding!!.settingBtn,
                        Gravity.NO_GRAVITY,
                        if (KiloApp.isLayoutRtl) settingMeasuredWidth - resources.getDimensionPixelSize(
                            R.dimen.dp_20
                        ) else
                            settingXOffset - hiddenSpaceGuideWindow!!.contentView.measuredWidth + resources.getDimensionPixelSize(
                                R.dimen.dp_20
                            ),
                        offsetY
                    )
                    return@post
                }
                val distance =
                    (hiddenSpaceGuideWindow!!.getTitleLines() - DEFAULT_TIPS_LINE) * resources.getDimensionPixelSize(
                        R.dimen.dp_30
                    )
                val offsetY = if (isPortraitAndOneThirdScreen()) {
                    settingYOffset - distance
                } else if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext())) {
                    settingYOffset + resources.getDimensionPixelSize(R.dimen.dp_470) - distance
                } else {
                    settingYOffset + resources.getDimensionPixelSize(R.dimen.dp_240) - distance
                }
                hiddenSpaceGuideWindow!!.showAtLocation(
                    binding!!.settingBtn,
                    Gravity.NO_GRAVITY,
                    if (KiloApp.isLayoutRtl) settingMeasuredWidth - resources.getDimensionPixelSize(
                        R.dimen.dp_20
                    ) else
                        settingXOffset - hiddenSpaceGuideWindow!!.contentView.measuredWidth + resources.getDimensionPixelSize(
                            R.dimen.dp_20
                        ),
                    offsetY
                )
            }
        }
    }

    private fun showNoteSortWindow() {
        if (!isAdded) return
        noteSortWindow =
            NoteSortWindow(
                requireContext(),
                noteViewModel.getCurrentDocumentSortType()
            ).apply {
                setMenuClickAction { type ->
                    noteViewModel.changeDocumentListSortType(type)
                }
                setOnDismissListener { noteViewModel.changeSortWindowStatues(false) }
            }
        val parentView = binding!!.sortBtn
        parentView.post {
            if (activity == null || requireActivity().isFinishing) return@post
            if (parentView.windowToken == null) return@post
            noteSortWindow?.let {
                val noteSortBtnLocation = IntArray(2)
                parentView.getLocationInWindow(noteSortBtnLocation)
                it.contentView.measure(
                    View.MeasureSpec.UNSPECIFIED,
                    View.MeasureSpec.UNSPECIFIED
                )
                val sortMeasuredWidth: Int = it.contentView.measuredWidth
                val sortXOffset: Int =
                    parentView.width / 2 + noteSortBtnLocation[0] -
                            (sortMeasuredWidth - resources.getDimensionPixelSize(R.dimen.dp_55))
                val sortYOffset: Int = parentView.height + noteSortBtnLocation[1]
                it.showAtLocation(
                    parentView, Gravity.NO_GRAVITY, sortXOffset, sortYOffset
                )
            }
        }
    }

    private fun callLogin() {
        if (checkNetwork()) {
            val fragment = parentFragmentManager.findFragmentByTag(UnifiedLogin.TAG)
            if (fragment != null && fragment is UnifiedLogin) {
                return
            }
            StoreAndLoginEvent.sendLoginClickEvent()
            UnifiedLogin().apply {
                loginSuccessCallback = {
                    ThreadUtils.postMainThread {
                        showAiPointsDialog()
                    }
                }
                setLoginLocation(LoginLocation.SETTING_PAGE_AI_POINTS)
                safeShow(
                    <EMAIL>,
                    UnifiedLogin.TAG
                )
            }
            UserEvent.sendLoginPopoverShow(LoginLocation.SETTING_PAGE_AI_POINTS)
        }
    }

    private fun checkNetwork(): Boolean {
        return if (NetworkUtils.isNetworkAvailable()) {
            true
        } else {
            ToastUtils.topCenter(requireContext(), R.string.toast_no_internet)
            false
        }
    }

    private fun showAiPointsDialog() {
        safeNavigate(R.id.unified_integral_dialog)
    }

    private fun showCreatorClubDialog() {
        val dialog = parentFragmentManager.findFragmentByTag(JoinCreatorClubDialog.TAG)
        if (dialog is JoinCreatorClubDialog) {
            return
        }
        JoinCreatorClubDialog().show(parentFragmentManager, JoinCreatorClubDialog.TAG)
        CommunityEvent.sendCommunityPageShow()
    }

    private fun showBackupDialog() {
        sendDataBackUpShow()
        val dialog = parentFragmentManager.findFragmentByTag(BackupDialog.TAG)
        if (dialog is BackupDialog) {
            return
        }
        BackupDialog().show(parentFragmentManager, BackupDialog.TAG)
    }

    private fun showRedeemCodeDialog() {
        val dialog = parentFragmentManager.findFragmentByTag(PadRedeemCodeDialog.TAG)
        if (dialog is PadRedeemCodeDialog) {
            return
        }
        PadRedeemCodeDialog().show(parentFragmentManager, PadRedeemCodeDialog.TAG)
    }

    private fun showSyncLogInDialog() {
        val dialog = parentFragmentManager.findFragmentByTag(SyncLoginDialog.TAG)
        if (dialog is SyncLoginDialog) {
            return
        }
        SyncLoginDialog().show(parentFragmentManager, SyncLoginDialog.TAG)
    }

    private fun showCombineDocumentsSelectionDialog() {
        val dialog =
            childFragmentManager.findFragmentByTag(DocumentCombineSelectionDialog.TAG)
        if (dialog is DocumentCombineSelectionDialog) {
            return
        }
        DocumentCombineSelectionDialog().show(
            childFragmentManager,
            DocumentCombineSelectionDialog.TAG
        )
    }

    private fun showSecurityQuestionDialog(isSetQuestion: Boolean) {
        val dialog =
            childFragmentManager.findFragmentByTag(PadSecurityQuestionFragment.TAG) as? PadSecurityQuestionFragment
        if (dialog == null) {
            padSecurityQuestionFragment = PadSecurityQuestionFragment()
            padSecurityQuestionFragment?.let { padSecurityQuestionFragment ->
                padSecurityQuestionFragment.isSetSecurityQuestion(isSetQuestion)
                padSecurityQuestionFragment.setDoOnSecurityDialogCloseAction { showPasswordKeyboardDialog ->
                    noteListViewModel.isSetQuestionByQADialog.value = null
                    if (showPasswordKeyboardDialog) {
                        securityViewModel.passwordList.clear()
                        noteListViewModel.changePasswordKeyboardState(PasswordState.INPUT_PASSWORD)
                    }
                }
                if (isSetQuestion) {
                    padSecurityQuestionFragment.setAfterSaveSecurityQAAction {
                        if (!isAdded) return@setAfterSaveSecurityQAAction
                        binding?.apply {
                            hiddenSpaceVipAndSecurityTips.setHasSetSecurityQuestion(true)
                            hiddenSpaceVipAndSecurityTips.isVisible =
                                noteListViewModel.isNeedShowHiddenSpaceVipAndSecurityTips && !UserManager.isVip()
                        }
                        binding?.let { notNullBinding ->
                            if (notNullBinding.hiddenSpaceVipAndSecurityTips.isVisible) {
                                HiddenSpaceEvent.sendHidenSpaceVipTipsShow()
                            }
                        }
                    }
                } else {
                    padSecurityQuestionFragment.setVerifyQASuccessAction {
                        actionQASuccess()
                    }
                }
                childFragmentManager.beginTransaction()
                    .add(
                        R.id.security_question_dialog,
                        padSecurityQuestionFragment,
                        PadSecurityQuestionFragment.TAG
                    )
                    .addToBackStack(null).commit()
            }
        } else {
            padSecurityQuestionFragment = dialog
            padSecurityQuestionFragment?.let { padSecurityQuestionFragment ->
                padSecurityQuestionFragment.isSetSecurityQuestion(isSetQuestion)
                padSecurityQuestionFragment.setDoOnSecurityDialogCloseAction { showPasswordKeyboardDialog ->
                    noteListViewModel.isSetQuestionByQADialog.value = null
                    if (showPasswordKeyboardDialog) {
                        securityViewModel.passwordList.clear()
                        noteListViewModel.changePasswordKeyboardState(PasswordState.INPUT_PASSWORD)
                    }
                }
                if (isSetQuestion) {
                    padSecurityQuestionFragment.setAfterSaveSecurityQAAction {
                        if (!isAdded) return@setAfterSaveSecurityQAAction
                        binding?.apply {
                            hiddenSpaceVipAndSecurityTips.setHasSetSecurityQuestion(true)
                            hiddenSpaceVipAndSecurityTips.isVisible =
                                noteListViewModel.isNeedShowHiddenSpaceVipAndSecurityTips && !UserManager.isVip()
                        }
                    }
                } else {
                    padSecurityQuestionFragment.setVerifyQASuccessAction {
                        actionQASuccess()
                    }
                }
                childFragmentManager.beginTransaction().show(padSecurityQuestionFragment)
                    .commit()
            }
        }
    }


    override fun onDestroyView() {
        val viewTreeObserver = binding!!.freeCountLayout.viewTreeObserver
        if (viewTreeObserver.isAlive) {
            viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
        }
        if (mEditNoteInfoWindow != null && mEditNoteInfoWindow!!.isShowing) {
            mEditNoteInfoWindow!!.action = null
            mEditNoteInfoWindow!!.dismiss()
        }
        if (mEditFolderInfoWindow != null && mEditFolderInfoWindow!!.isShowing) {
            mEditFolderInfoWindow!!.action = null
            mEditFolderInfoWindow!!.dismiss()
        }
        if (settingWindow != null && settingWindow!!.isShowing) {
            settingWindow!!.setOnDismissListener { }
            settingWindow!!.dismiss()
        }
        if (fileManagerdesktopOutlineGuideWindow != null && fileManagerdesktopOutlineGuideWindow!!.isShowing) {
            fileManagerdesktopOutlineGuideWindow!!.setOnDismissListener { }
            fileManagerdesktopOutlineGuideWindow!!.dismiss()
        }
        if (hiddenSpaceGuideWindow != null && hiddenSpaceGuideWindow!!.isShowing) {
            hiddenSpaceGuideWindow!!.setOnDismissListener {
                changeWindowAlpha(ALPHA_TRANSPARENT, true)
            }
            hiddenSpaceGuideWindow!!.dismiss()
        }
        if (hiddenSpaceCreateOrAddNoteSelectWindow != null && hiddenSpaceCreateOrAddNoteSelectWindow!!.isShowing) {
            hiddenSpaceCreateOrAddNoteSelectWindow!!.dismiss()
        }
        noteListAdapter!!.setListener(null)
        noteListAdapter = null
        binding!!.noteRv.overScrollRecyclerView.adapter = null
        super.onDestroyView()
        binding = null
        noteListViewModel.removeUserInfoChangeListener()
        noteSortWindow?.setOnDismissListener { }
        noteSortWindow?.dismiss()
        noteSortWindow = null
        fileManagerAnimator?.cancel()
        isOSAction = true
    }

    override fun handleItemClick(noteTreeItem: NoteTreeItem, view: View) {
        when (noteTreeItem) {
            is NoteTreeDocument -> {
                noteViewModel.currentFolder = null
                handleDocumentClick(noteTreeItem.document)
            }

            is NoteTreeFolder -> {
                noteViewModel.updateFolderOpenedTime(noteTreeItem.folder)
                noteViewModel.selectFolder(noteTreeItem)
            }

            else -> {
                throw IllegalArgumentException("Unknown metaDocument type")
            }
        }
    }

    private fun handleDocumentClick(document: Document) {
        if ((true == noteViewModel.isOpenDocument.value)) {
            return
        }
        noteViewModel.isOpenDocument.value = true
        if (document.imported) {
            sendNoteBookTypeEvent("pdf")
        } else {
            sendNoteBookTypeEvent("notebook")
        }
        if (!document.isSupported) {
            ToastUtils.topEnd(requireContext(), R.string.upgrade_up_first)
            return
        }
        showLoadingDialog()
        lastClickDocument = document
        if (DocumentManager.isWelcomeDocument(document.uuid)) {
            sendDefaultDocumentStart()
        }
        if (noteViewModel.isStorageNotEnoughToOpenDocument()) {
            this.showStorageNotEnoughToOpenDocumentDialog {
                openDocument()
            }
        } else {
            openDocument()
        }
    }

    override fun handleMoreClick(viewAnchor: View, metaDocument: MetaDocument, index: Int) {
        when (metaDocument) {
            is Document -> {
                showNoteCoverPopupWindow(
                    viewAnchor, metaDocument,
                    refreshItem = { payload ->
                        val adapter = noteListAdapter ?: return@showNoteCoverPopupWindow
                        val indices = adapter.getMetaDocumentIndices(metaDocument)
                        indices.forEach {
                            adapter.notifyItemChanged(it, NoteListAdapter.DOC_UPDATE)
                        }
                        fileManagerAdapter?.apply {
                            getMetaDocumentIndices(metaDocument).forEach { index ->
                                notifyItemChanged(
                                    index,
                                    payload
                                )
                            }
                        }
                    })
            }

            is Folder -> {
                showEditFolderPopupWindow(
                    viewAnchor, metaDocument,
                    refreshItem = { payload ->
                        fileManagerAdapter?.apply {
                            getMetaDocumentIndices(metaDocument).forEach { index ->
                                notifyItemChanged(
                                    index,
                                    payload
                                )
                            }
                        }
                    })
            }

            else -> {
                throw IllegalArgumentException("Unknown metaDocument type")
            }
        }
    }

    override fun getNoteSpanCountByWith(interceptiveWidth: Int): Int {
        val displayMetrics = DimensionUtil.getScreenDimensions(requireActivity())
        val widthPixels = displayMetrics.widthPixels - interceptiveWidth
        val count = (widthPixels - getItemSpace()) / (getItemWidth() + getItemSpace())
        if (count < 1) {
            LogHelper.d(
                TAG,
                "pad getNoteSpanCountByWith() <1 : widthPixels is" + widthPixels + " ,ItemSpace is " + getItemSpace() + " ,ItemWidth is " + getItemWidth(),
                null,
                true
            )
        }
        return Math.max(count, 1)
    }

    override fun getItemSpace(): Int {
        return (resources.getDimension(R.dimen.dp_25).toInt() * ration!!).toInt()
    }

    override fun getItemHeight(): Int {
        return getItemSpace()
    }

    override fun getItemWidth(): Int {
        return (resources.getDimension(R.dimen.dp_260).toInt() * ration!!).toInt()
    }

    override fun showCreateFolderGuideWindow() {
        if (parentFragmentManager.findFragmentByTag(CreateFolderGuideDialog.TAG) != null) return
        val dialog = CreateFolderGuideDialog()
        dialog.safeShow(parentFragmentManager, CreateFolderGuideDialog.TAG)
    }

    /**
     * note cover popup window
     */
    private fun showNoteCoverPopupWindow(
        viewAnchor: View,
        document: Document,
        refreshItem: (payload: Int) -> Unit,
    ) {
        val context = requireContext()
        if (!document.isSupported) {
            ToastUtils.topEnd(context, R.string.upgrade_up_first)
            return
        }
        if (mEditNoteInfoWindow != null && mEditNoteInfoWindow!!.isShowing) {
            mEditNoteInfoWindow!!.dismiss()
        }
        val isInHiddenSpace = noteViewModel.isHiddenSpaceMode.value ?: false
        mEditNoteInfoWindow = EditNoteInfoWindow(document, context, isInHiddenSpace).apply {
            showFileManagerMoveDialog = {
                showFileManagerDialog(document)
            }
            action = object : EditNoteAction {
                override fun onNoteEditTitle(document: Document, title: String) {
                    noteViewModel.currentDoc = document
                    if (document.title != title && commitChanges(title, document)) {
                        document.title = title
                        document.updateAndStoreModifiedTime()
                        refreshItem.invoke(FileManagerAdapter.UPDATE_TITLE_STATE)
                        noteViewModel.editDocument(document)
                        NoteRepository.runInNoteOperationScopeAsync {
                            saveDocumentInfo(document)
                        }
                    }
                }

                override fun onNoteEdit(document: Document) {
                    noteViewModel.upgradeDocumentAsync(document) { upgradeSuccess: Boolean ->
                        if (upgradeSuccess && isAdded) {
                            createNotebookViewModel.getCurrentCoverCategoryList {
                                noteViewModel.currentDoc = document
                                createNotebookViewModel.resetCreateProperty()
                                createNotebookViewModel.findCoverPosition(document)
                                safeNavigate(R.id.edit_cover)
                            }
                        }
                    }
                }

                override fun onNoteDelete(document: Document) {
                    showDeleteNotebookDialog(document) {
                        noteViewModel.currentFolder?.let { folder ->
                            if (folder.children.isEmpty()) {
                                noteViewModel.deleteFolder(folder)
                                noteViewModel.currentFolder = null
                            }
                        }
                    }
                }

                override fun onNoteRemove(document: Document) {
                    if (isAllowToCreateNote()) {
                        CreateNoteLimit.increaseCreatedNoteNumber()
                        val documents = listOf(document)
                        HiddenNoteLimit.decreaseHiddenNoteNumber()
                        ToastUtils.topCenter(
                            context,
                            R.string.hidden_space_note_move_to_home
                        )
                        noteViewModel.setDocumentsIsHid(documents, false)
                    } else {
                        showCreateNoteLimitDialog("隐藏空间移出")
                    }
                }

                override fun onNoteAddColorTag(document: Document, color: Int) {
                    noteViewModel.addMetaDocumentColorTag(document, color)
                    refreshItem.invoke(FileManagerAdapter.UPDATE_COLOR_TAGS_STATE)
                }

                override fun onNoteRemoveColorTag(document: Document, color: Int) {
                    noteViewModel.removeMetaDocumentColorTag(document, color)
                    refreshItem.invoke(FileManagerAdapter.UPDATE_COLOR_TAGS_STATE)
                }
            }
            setOnDismissListener {
                changeWindowAlpha(ALPHA_TRANSPARENT, true)
                noteViewModel.storeMetaDocumentInfo(document)
            }
        }
        val location = IntArray(2)
        viewAnchor.getLocationInWindow(location)
        mEditNoteInfoWindow!!.show(
            viewAnchor,
        )
        changeWindowAlpha(ALPHA_TRANSLUCENT, false)
    }

    private fun showEditFolderPopupWindow(
        anchor: View,
        folder: Folder,
        refreshItem: (payload: Int) -> Unit,
    ) {
        if (mEditFolderInfoWindow != null && mEditFolderInfoWindow!!.isShowing) {
            mEditFolderInfoWindow!!.dismiss()
        }
        mEditFolderInfoWindow =
            EditFolderInfoWindow(requireContext(), folder.title, folder.colorTags).apply {
                showFileManagerMoveDialog = {
                    showFileManagerDialog(folder)
                }
                action = object : EditFolderAction {
                    override fun onFolderEditTitle(title: String) {
                        when (noteViewModel.verifyFolderTitle(title, folder)) {
                            TitleErrorType.NONE -> {
                                noteViewModel.renameFolder(title, folder)
                                refreshItem.invoke(FileManagerAdapter.UPDATE_TITLE_STATE)
                            }

                            TitleErrorType.REPEAT -> {
                                showTitleErrorDialog(getString(R.string.folder_title_repeat_tips))
                            }

                            TitleErrorType.SPECIAL -> {
                                showTitleErrorToast(R.string.folder_title_special_tips)
                            }

                            TitleErrorType.BLANK -> {
                                showTitleErrorToast(R.string.folder_title_empty_tips)
                            }
                        }
                    }

                    override fun onFolderDelete() {
                        showDeleteFolderDialog(folder)
                    }

                    override fun onFolderAddColorTag(color: Int) {
                        noteViewModel.addMetaDocumentColorTag(folder, color)
                        refreshItem.invoke(FileManagerAdapter.UPDATE_COLOR_TAGS_STATE)
                    }

                    override fun onFolderRemoveColorTag(color: Int) {
                        noteViewModel.removeMetaDocumentColorTag(folder, color)
                        refreshItem.invoke(FileManagerAdapter.UPDATE_COLOR_TAGS_STATE)
                    }
                }
            }
        mEditFolderInfoWindow!!.setOnDismissListener {
            changeWindowAlpha(ALPHA_TRANSPARENT, true)
            noteViewModel.storeMetaDocumentInfo(folder)
        }
        mEditFolderInfoWindow!!.show(anchor)
        changeWindowAlpha(ALPHA_TRANSLUCENT, false)
    }

    private fun showTitleErrorDialog(title: String) {
        if (parentFragmentManager.findFragmentByTag(FOLDER_TITLE_ERROR_DIALOG_TAG) is AlertDialog) return
        AlertDialog.Builder()
            .setTitle(title)
            .setPositiveBtn(getString(R.string.ok)) {

            }
            .build()
            .show(parentFragmentManager, FOLDER_TITLE_ERROR_DIALOG_TAG)
    }

    private fun showFileManagerFolderCreateDialog() {
        val dialog =
            parentFragmentManager.findFragmentByTag(FileManagerFileCreateDialog.TAG)
        if (dialog is FileManagerFileCreateDialog) return
        fileManagerFileCreateDialog = FileManagerFileCreateDialog()
        fileManagerFileCreateDialog?.show(
            parentFragmentManager,
            FileManagerFileCreateDialog.TAG
        )
    }

    override fun onCreateContextMenu(
        menu: ContextMenu,
        v: View,
        menuInfo: ContextMenu.ContextMenuInfo?,
    ) {
        super.onCreateContextMenu(menu, v, menuInfo)
    }

    private fun initSnippetPage() {
        val snippetPage =
            childFragmentManager.findFragmentByTag(SnippetPageFragment.TAG) as? SnippetPageFragment
        if (snippetPage == null) {
            snippetPageFragment = SnippetPageFragment()
            snippetPageFragment?.let { snippetListFragment ->
                childFragmentManager.beginTransaction()
                    .replace(
                        R.id.snippet_list_page,
                        snippetListFragment,
                        SnippetPageFragment.TAG
                    )
                    .commit()
            }
        } else {
            snippetPageFragment = snippetPage
            snippetPageFragment?.let { snippetListFragment ->
                childFragmentManager.beginTransaction().show(snippetListFragment).commit()
            }
        }
        snippetPageFragment?.apply {
            restoreSnippetPreviewCallback = { noteSnippet ->
                <EMAIL>?.expandedImageContent?.restorePreview(
                    parentFragmentManager,
                    getPreviewSnippetWidth(),
                    getPreviewSnippetHeight(),
                    noteSnippet,
                    snippetEditCallback = {
                        SnippetEvent.sendSnippetPreviewEditBtnClick()
                        safeNavigate(
                            NoteListFragmentDirections.actionNoteListToCreateSnippet()
                                .apply {
                                    isEditMode = true
                                })
                    }
                )

            }
            initSnippetPreviewCallback = { view, noteSnippet ->
                lifecycleScope.launch(Dispatchers.IO) {
                    val snippetTags =
                        SnippetManager.getSnippetAndTags(noteSnippet.snippetId.toString())?.tags
                            ?: listOf()
                    withContext(Dispatchers.Main) {
                        snippetEditViewModel.setCurrentEditSnippetAndTags(
                            noteSnippet,
                            snippetTags
                        )
                        <EMAIL>?.expandedImageContent?.initPreview(
                            view,
                            dismissCallback = {
                                snippetEditViewModel.resetCurrentEditSnippetAndTags()
                            },
                            showDeleteDialogAction = {
                                showDeleteSnippetDialog()
                            }
                        )
                    }
                }
                lifecycleScope.launch(Dispatchers.IO) {
                    val snippetTags =
                        SnippetManager.getSnippetAndTags(noteSnippet.snippetId.toString())?.tags
                            ?: listOf()
                    withContext(Dispatchers.Main) {
                        snippetEditViewModel.setCurrentEditSnippetAndTags(
                            noteSnippet,
                            snippetTags
                        )
                    }
                }
            }
            snippetPreviewCallback = { view, noteSnippetItem, showDuration ->
                lifecycleScope.launch(Dispatchers.IO) {
                    val noteSnippet = noteSnippetItem.snippet
                    val snippetTags =
                        SnippetManager.getSnippetAndTags(noteSnippet.snippetId.toString())?.tags
                            ?: listOf()
                    withContext(Dispatchers.Main) {
                        snippetEditViewModel.setCurrentEditSnippetAndTags(
                            noteSnippet,
                            snippetTags
                        )
                        <EMAIL>?.expandedImageContent?.showPreview(
                            parentFragmentManager,
                            getPreviewSnippetWidth(),
                            getPreviewSnippetHeight(),
                            view,
                            noteSnippetItem,
                            showDuration,
                            dismissCallback = {
                                snippetEditViewModel.resetCurrentEditSnippetAndTags()
                            },
                            snippetEditCallback = {
                                SnippetEvent.sendSnippetPreviewEditBtnClick()
                                safeNavigate(
                                    NoteListFragmentDirections.actionNoteListToCreateSnippet()
                                        .apply {
                                            isEditMode = true
                                        })
                            },
                            showDeleteDialogAction = {
                                showDeleteSnippetDialog()
                            }
                        )
                    }
                }

            }
            onSnippetTagMoreBtnClickListener = {
                <EMAIL>!!.noteAndSnippetSelectViewDividingLine
            }
            onCreateSnippetBtnClickListener = {
                safeNavigate(
                    NoteListFragmentDirections.actionNoteListToCreateSnippet().apply {
                        isEditMode = false
                    })
            }
        }
    }

    private fun showDeleteSnippetDialog() {
        val dialog = parentFragmentManager.findFragmentByTag(DELETE_SNIPPET_DIALOG)
        if (dialog != null) return
        if (deleteAlertDialog == null) {
            deleteAlertDialog = AlertDialog.Builder()
                .setIsCancelable(false)
                .setNegativeBtnColor(AppUtils.getColor(R.color.sign_red))
                .setPositiveBtnColor(AppUtils.getColor(R.color.text_secondary))
                .setTitle(getString(R.string.note_snippet_preview_delete_window_title))
                .setMsg(getString(R.string.note_snippet_preview_delete_window_msg))
                .setPositiveBtn(
                    getString(R.string.cancel),
                    AntiShakeClickListener {

                    })
                .setNegativeBtn(
                    getString(R.string.delete),
                    AntiShakeClickListener {
                        snippetEditViewModel.currentEditSnippet?.let { currentEditSnippet ->
                            binding?.expandedImageContent?.hidePreview()
                            snippetPageFragment?.deleteSnippet(currentEditSnippet)
                            snippetEditViewModel.resetCurrentEditSnippetAndTags()
                        }
                    })
                .build()
        }
        deleteAlertDialog!!.show(parentFragmentManager, DELETE_SNIPPET_DIALOG)
    }

    private fun showSnippetPage() {
        binding?.snippetListPage?.visibility = View.VISIBLE
    }

    private fun getPreviewSnippetWidth(): Int {
        val widthPixels = DimensionUtil.getScreenDimensions(context).widthPixels
        val previewSnippetWidth =
            if (DimensionUtil.isLandAndOneThirdScreen(context) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                    context
                )
            ) {
                widthPixels * (600F / 640F)
            } else if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
                widthPixels * (700F / 1080F)
            } else if (DimensionUtil.isPortraitScreen(context)) {
                widthPixels * (800F / 1080F)
            } else if (DimensionUtil.isLandAndHalfScreen(context)) {
                widthPixels * (900F / 960F)
            } else if (DimensionUtil.isLandTwoThirdScreen(context)) {
                widthPixels * (1160F / 1280F)
            } else {
                widthPixels * (1260F / 1920F)
            }
        return previewSnippetWidth.toInt()
    }

    private fun getPreviewSnippetHeight(): Int {
        return (getPreviewSnippetWidth() * (3F / 5F)).toInt()
    }

    private fun hideSnippetPage() {
        binding?.snippetListPage?.visibility = View.GONE
    }

    private fun hideSecurityQuestionDialog() {
        val dialog =
            childFragmentManager.findFragmentByTag(PadSecurityQuestionFragment.TAG) as? PadSecurityQuestionFragment
        dialog?.let {
            childFragmentManager.popBackStack()
        }
    }

    private fun showHiddenSpaceNoticeDialog() {
        if (parentFragmentManager.findFragmentByTag(PadHiddenSpaceNoticeDialog.TAG) != null) return
        val dialog = PadHiddenSpaceNoticeDialog()
        dialog.safeShow(parentFragmentManager, PadHiddenSpaceNoticeDialog.TAG)
    }

    private fun showCreateOrAddNotePopupWindow(anchor: View) {
        hiddenSpaceCreateOrAddNoteSelectWindow =
            PadHiddenSpaceCreateOrAddNoteSelectWindow(requireContext()).apply {
                setOnCreateBtnClickListener {
                    HiddenSpaceEvent.sendHidenSpaceCreateNoteBtnClick()
                    if (HiddenNoteLimit.isAllowToIncreaseHiddenNote()) {
                        createNotebookViewModel.getCurrentCoverCategoryList {
                            if (!isAdded) return@getCurrentCoverCategoryList
                            createNotebookViewModel.resetCreateProperty()
                            createNotebookViewModel.currentCoverPosition.value = Pair(
                                NoteCover.DEFAULT_COVER_CATEGORY_INDEX,
                                NoteCover.DEFAULT_COVER_INDEX
                            )
                            <EMAIL>(R.id.note_list, R.id.create)
                        }
                    } else {
                        showVipExclusiveDialog(
                            VipExclusiveType.HIDE_SPACE,
                            NaviEnum.HIDDEN_SPACE_NOTE_ADD_OR_CREATE
                        )
                    }
                }

                setOnImportNoteBtnClickListener {
                    launchImport(showTipsAsToast = true)
                }

                setOnCreateNoteFromImageBtnClickListener {
                    val needRequestPermission =
                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                            Manifest.permission.READ_EXTERNAL_STORAGE
                        } else {
                            Manifest.permission.READ_MEDIA_IMAGES
                        }
                    PermissionRequester.showPermissionRationaleDialogThenRequest(
                        title = AppUtils.getString(R.string.permission_rationale_title_for_storage),
                        message = AppUtils.getString(R.string.permission_rationale_content_for_storage),
                        permissionState = PermissionRequester.checkPermissionState(
                            requireActivity(),
                            needRequestPermission
                        ),
                        fragmentManager = parentFragmentManager
                    ) {
                        permissionRequester.request(needRequestPermission) { isGranted ->
                            if (isGranted) {
                                selectImageToCreateNoteLauncher.launch(
                                    Intent(
                                        requireContext(),
                                        SelectPhotoDialogActivity::class.java
                                    ).apply {
                                        putExtra(
                                            SelectPhotoDialogActivity.BUNDLE_KEY_NEED_CROP_IMAGE,
                                            false
                                        )
                                        putExtra(
                                            SelectPhotoDialogActivity.BUNDLE_KEY_IGNORE_DETAIL_IMAGE,
                                            true
                                        )
                                    }
                                )
                            } else {
                                if (PermissionRequester.checkPermissionState(
                                        this@NoteListFragment,
                                        needRequestPermission
                                    ) == PermissionRequester.PermissionState.PERMISSION_NOT_ASK_AGAIN
                                ) {
                                    val alertDialog = AlertDialog.Builder()
                                        .setMsg(resources.getString(R.string.never_aks_read_external_storage))
                                        .setPositiveBtn(resources.getString(R.string.go_to_set)) {
                                            PermissionRequester.jumpToSetting(this@NoteListFragment)
                                        }
                                        .setNegativeBtn(resources.getString(R.string.ok)) {

                                        }
                                        .build()
                                    alertDialog.show(
                                        parentFragmentManager,
                                        PERMISSION_ERROR_TAG
                                    )
                                }
                            }
                        }
                    }
                }

                setOnCreateNoteFromTakingPhotoBtnClickListener {
                    createNotebookViewModel.photoUri = generatePictureUri()
                    takePhotoLauncher.launch(createNotebookViewModel.photoUri)
                    KiloApp.app.provisionallyJumpToOtherApp = true
                }

                setOnAddBtnClickListener {
                    HiddenSpaceEvent.sendHiddenSpaceAddNoteBtnClick()
                    if (!HiddenNoteLimit.isAllowToIncreaseHiddenNote()) {
                        showVipExclusiveDialog(
                            VipExclusiveType.HIDE_SPACE,
                            NaviEnum.HIDDEN_SPACE_NOTE_ADD_OR_CREATE
                        )
                        return@setOnAddBtnClickListener
                    }
                    noteViewModel.selectedNoteSet.clear()
                    noteViewModel.isNeedShowAddNoteDialog.value = true
                }
            }
        hiddenSpaceCreateOrAddNoteSelectWindow?.show(anchor)
    }

    override fun onWindowInsetsChange(
        isSoftKeyboardShowing: Boolean,
        softKeyboardHeight: Int,
        isStatusBarShowing: Boolean,
        statusBarHeight: Int,
        isNavigationBarShowing: Boolean,
        navigationBarHeight: Int,
    ) {
        super.onWindowInsetsChange(
            isSoftKeyboardShowing,
            softKeyboardHeight,
            isStatusBarShowing,
            statusBarHeight,
            isNavigationBarShowing,
            navigationBarHeight
        )
        binding!!.root.setPadding(
            binding!!.root.paddingLeft,
            binding!!.root.paddingTop,
            binding!!.root.paddingRight,
            navigationBarHeight
        )
    }

    fun showTitleErrorToast(resInt: Int) {
        val tip = AppUtils.getString(resInt)
        ToastUtils.windowTopCenter(requireActivity(), tip)
    }

    private fun showPasswordKeyboardDialog(passwordState: PasswordState?) {
        val dialog =
            parentFragmentManager.findFragmentByTag(BasePasswordKeyboardDialog.TAG) as? PasswordKeyboardDialog
        if (passwordState == null) {
            noteListViewModel.isResetPassword = false
            dialog?.dismiss()
            return
        }
        passwordKeyboardDialog = dialog ?: PasswordKeyboardDialog()
        when (passwordState) {
            PasswordState.SET_PASSWORD, PasswordState.RESET_PASSWORD -> {
                passwordKeyboardDialog?.apply {
                    isCancelable = true
                    setTitle(passwordState)
                    inputCompletedListener =
                        { input: String, _: (isPass: Boolean) -> Unit ->
                            noteListViewModel.password = input
                            securityViewModel.passwordList.clear()
                            noteListViewModel.isResetPassword =
                                passwordState == PasswordState.RESET_PASSWORD
                            noteListViewModel.changePasswordKeyboardState(PasswordState.CONFIRM_PASSWORD)
                        }
                }
            }

            PasswordState.CONFIRM_PASSWORD -> {
                passwordKeyboardDialog?.apply {
                    isCancelable = false
                    if (dialog != null) {
                        refreshPasswordContent()
                    }
                    setTitle(passwordState)
                    inputCompletedListener =
                        { input: String, function: (isPass: Boolean) -> Unit ->
                            if (noteListViewModel.password == input) {
                                securityViewModel.saveHiddenSpacePassword(input)
                                if (noteListViewModel.isResetPassword) {
                                    context?.let {
                                        ToastUtils.topCenter(
                                            it,
                                            R.string.hidden_space_reset_password_success
                                        )
                                    }
                                }
                                function.invoke(true)
                                noteListViewModel.changePasswordKeyboardState(null)
                                noteListViewModel.changeIsSetPassword(true)
                                if (noteListViewModel.needQuitHiddenSpaceMode) {
                                    noteViewModel.changeIsHiddenSpaceMode(false)
                                } else {
                                    noteListViewModel.needQuitHiddenSpaceMode = true
                                }
                            } else {
                                function.invoke(false)
                            }
                        }
                }
            }

            PasswordState.INPUT_PASSWORD -> {
                passwordKeyboardDialog?.apply {
                    isCancelable = true
                    setTitle(passwordState)
                    inputCompletedListener =
                        { password: String, function: (isPass: Boolean) -> Unit ->
                            securityViewModel.verifyHiddenSpacePassword(password) { pass ->
                                if (pass) {
                                    noteListViewModel.changePasswordKeyboardState(null)
                                    noteViewModel.changeIsHiddenSpaceMode(true)
                                }
                                function.invoke(pass)
                            }
                        }
                    forgetPasswordListener = {
                        noteListViewModel.changePasswordKeyboardState(null)
                        noteListViewModel.isSetQuestionByQADialog.value = false
                    }
                }
            }

            else -> {
                return
            }
        }
        if (dialog == null) {
            passwordKeyboardDialog?.show(
                parentFragmentManager,
                BasePasswordKeyboardDialog.TAG
            )
        }
    }

    private fun refreshNoteRvItemDecoration(list: List<NoteTreeItem>) {
        if (!isAdded) return
        val itemDecoration = gridCategoryItemDecoration ?: return
        val lastRowFirstColumnPosition =
            itemDecoration.getLastRowFirstColumnPositionWithoutOffset()
        itemDecoration.setCategoryPositionList(
            if (noteViewModel.getCurrentDocumentSortType() == NoteOrganizeAndSortType.COLOR_TAG) {
                list.mapIndexedNotNull { index, any ->
                    if (any is NoteTreeTag) index + 1 else null
                }
            } else {
                emptyList()
            },
            list.size
        )

        fun calculateFirstRowLastColumnPosition(): Int {
            if (list.isEmpty()) return -1
            val gridLayoutManager = gridLayoutManager ?: return -1
            var count = 0
            if (itemDecoration.offset > 0) {
                for (position in 0 until itemDecoration.offset) {
                    count += gridLayoutManager.spanSizeLookup.getSpanSize(position)
                    if (count >= gridLayoutManager.spanCount) {
                        return -1
                    }
                }
            }
            for (position in list.indices) {
                count += gridLayoutManager.spanSizeLookup.getSpanSize(position + itemDecoration.offset)
                if (count >= gridLayoutManager.spanCount) {
                    return position
                }
            }
            return list.size - 1
        }

        noteListAdapter?.apply {
            firstRowLastColumnPosition = calculateFirstRowLastColumnPosition()
            setNoteTreeItemList(list)
            val newLastRowFirstColumnPosition =
                itemDecoration.getLastRowFirstColumnPositionWithoutOffset()
            if (lastRowFirstColumnPosition > newLastRowFirstColumnPosition) {
                notifyItemRangeChanged(
                    newLastRowFirstColumnPosition,
                    lastRowFirstColumnPosition - newLastRowFirstColumnPosition
                )
            } else if (lastRowFirstColumnPosition < newLastRowFirstColumnPosition) {
                notifyItemRangeChanged(
                    lastRowFirstColumnPosition,
                    newLastRowFirstColumnPosition - lastRowFirstColumnPosition
                )
            }
        }
    }

    private fun adjustFileManagerPathContain() {
        val constraintSet = ConstraintSet()
        constraintSet.clone(binding?.root)
        constraintSet.clear(binding!!.fileManagerPathContainer.id, ConstraintSet.END)
        constraintSet.clear(binding!!.fileManagerPathContainer.id, ConstraintSet.TOP)
        constraintSet.clear(binding!!.fileManagerPathContainer.id, ConstraintSet.BOTTOM)
        constraintSet.connect(
            binding!!.fileManagerPathContainer.id,
            ConstraintSet.END,
            ConstraintSet.PARENT_ID,
            ConstraintSet.END
        )
        constraintSet.connect(
            binding!!.fileManagerPathContainer.id,
            ConstraintSet.BOTTOM,
            binding!!.sortBtn.id,
            ConstraintSet.TOP
        )
        constraintSet.applyTo(binding!!.root)
        binding!!.fileManagerPathContainer.setMargins(
            requireContext().resources.getDimensionPixelSize(R.dimen.dp_30),
            binding!!.fileManagerPathContainer.top,
            requireContext().resources.getDimensionPixelSize(R.dimen.dp_24),
            binding!!.fileManagerPathContainer.bottom
        )
    }

    private fun refreshSpanSizeLookup(spanCount: Int) {
        gridCategoryItemDecoration?.spanCount = spanCount
        gridLayoutManager?.spanCount = spanCount
        gridLayoutManager?.spanSizeLookup = object : SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                val list = noteViewModel.selectedNoteTreeItemList.value ?: return 1
                return if (position == 0) {
                    if (noteViewModel.getCurrentDocumentSortType() == NoteOrganizeAndSortType.COLOR_TAG) {
                        spanCount
                    } else {
                        1
                    }
                } else if (position > 0) {
                    val item = list[position - 1]
                    if (item is NoteTreeFolder || item is NoteTreeDocument) {
                        1
                    } else {
                        spanCount
                    }
                } else {
                    1
                }
            }

        }
    }

    fun loginBaiduByCode(code: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            if (!NetworkUtils.isNetworkAvailable()) {
                withContext(Dispatchers.Main) {
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            AppUtils.getString(R.string.network_not_connected)
                        )
                    }
                }
                return@launch
            }
            val response = BaiduOauthManager().code2token(code)
            withContext(Dispatchers.Main) {
                response.use { usingResponse ->
                    LogHelper.d(
                        SyncLoginDialog.TAG,
                        "usingResponse.isSuccessful:${usingResponse.isSuccessful}"
                    )
                    val responseData = usingResponse.body?.string()
                    if (usingResponse.isSuccessful) {
                        PersonalInfoCollectionCounter.cloudBackupLoginCount++
                        val baiduInfo = commonGsonClient.fromJson(
                            responseData,
                            BaiduInfo::class.java
                        )
                        if (baiduInfo.scope.contains(BaiduOAuth.SCOPE_NETDISK)) {
                            SyncBaiduEvent.sendSyncBaiduLoginEvent(true)
                            baiduInfo.oauthTime = System.currentTimeMillis()
                            BaiduOAuth.baiduInfo = baiduInfo
                            SyncCore.syncRemoteChannel = SyncManager.REMOTE_CHANNEL_BAIDU
                            syncViewModel.changeSyncRemoteChannel(SyncManager.REMOTE_CHANNEL_BAIDU)
                        } else {
                            context?.let { context ->
                                ToastUtils.topCenter(
                                    context,
                                    resources.getString(R.string.sync_login_no_file_read_and_write_permission)
                                )
                                SyncBaiduEvent.sendSyncBaiduLoginEvent(
                                    false,
                                    "sync_login_no_file_read_and_write_permission"
                                )
                            }
                        }
                    } else {
                        LogHelper.d(
                            SyncLoginDialog.TAG,
                            "usingResponse.code = ${usingResponse.code} , usingResponse.body.string =  ${usingResponse.body?.string()}"
                        )
                        val baiduErrorInfo = commonGsonClient.fromJson(
                            responseData,
                            BaiduErrorInfo::class.java
                        )
                        SyncBaiduEvent.sendSyncBaiduLoginEvent(
                            false,
                            baiduErrorInfo.errorDescription
                        )
                    }
                }
                showSyncLogInDialog()
            }
        }
    }


    override fun isListenerWindowInsets(): Boolean {
        return true
    }

    companion object {
        private const val LAST_CLICK_DOCUMENT_INDEX_KEY = "LAST_CLICK_DOCUMENT_INDEX_KEY"

        private const val MIN_NOTE_NUMBER_WHEN_SHOW_CREATE_FOLDER_GUIDE = 3
        private const val MIN_NOTE_NUMBER_WHEN_SHOW_SUPPORT_APP = 2
        private const val MIN_NOTE_NUMBER_WHEN_SHOW_CREATE_NOTE_RIGHTS = 4

        /**
         * 动画时长为250ms，用作正常情况下做平滑的入场或退场动画
         */
        private const val FILE_MANAGER_ANIMATION_DURATION_TIME = 250L

        /**
         * 动画时长为0，用作转屏分屏状态下，瞬间展示恢复
         */
        private const val FILE_MANAGER_NO_ANIMATION_DURATION = 0L
    }
}
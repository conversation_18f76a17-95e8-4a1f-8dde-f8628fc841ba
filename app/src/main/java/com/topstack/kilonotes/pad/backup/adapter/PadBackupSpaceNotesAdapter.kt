package com.topstack.kilonotes.pad.backup.adapter

import android.content.Context
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.backup.adapter.BaseBackupSpaceNotesAdapter
import com.topstack.kilonotes.base.backup.viewmodel.BackupDownloadViewModel
import com.topstack.kilonotes.base.util.DimensionUtil

class PadBackupSpaceNotesAdapter(context: Context, viewModel: BackupDownloadViewModel) :
    BaseBackupSpaceNotesAdapter(context, viewModel) {

    override fun getLayoutResId(): Int {
        return if (DimensionUtil.isLandAndOneThirdScreen(context) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                context
            )
        ) {
            R.layout.pad_backup_space_note_item_one_third
        } else {
            R.layout.pad_backup_space_note_item
        }

    }

}
package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.ktx.isLayoutRtl
import com.topstack.kilonotes.infra.util.AppUtils

class NoteRecordProgressView : androidx.appcompat.widget.AppCompatSeekBar {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    companion object {
        private val SEGMENT_PAINT_COLOR =
            AppUtils.appContext.getColor(R.color.note_record_progress_view_segment_color)
        private val SEGMENT_PAINT_WIDTH = AppUtils.appContext.resources.getDimension(R.dimen.dp_1)
        private val SEGMENT_PAINT_HEIGHT = AppUtils.appContext.resources.getDimension(R.dimen.dp_9)

        private val TAG_PAINT_COLOR =
            AppUtils.appContext.getColor(R.color.note_record_progress_view_tag_color)
        private val TAG_PAINT_WIDTH = AppUtils.appContext.resources.getDimension(R.dimen.dp_6)

        private val SEGMENT_RECT_RADIUS = AppUtils.appContext.resources.getDimension(R.dimen.dp_2)

        private val PADDING_HORIZONTAL = AppUtils.appContext.resources.getDimension(R.dimen.dp_10)
    }

    private val segmentPaint = Paint()
    private val tagPaint = Paint()
    private val segmentRectF = RectF()

    private var tagList = emptyList<Int>()
        @JvmName("tagListInt")
        set(value) {
            field = value
            invalidate()
        }

    private var segmentList = emptyList<Int>()
        @JvmName("segmentListInt")
        set(value) {
            field = value
            invalidate()
        }


    init {
        segmentPaint.apply {
            color = SEGMENT_PAINT_COLOR
        }
        segmentPaint.strokeWidth = SEGMENT_PAINT_WIDTH
        segmentPaint.style = Paint.Style.FILL
        segmentPaint.isAntiAlias = true

        tagPaint.color = TAG_PAINT_COLOR
        tagPaint.strokeWidth = TAG_PAINT_WIDTH
        tagPaint.strokeCap = Paint.Cap.ROUND
        tagPaint.isAntiAlias = true
        setPadding(PADDING_HORIZONTAL.toInt(), 0, PADDING_HORIZONTAL.toInt(), 0)
        initProgress()
    }


    fun setSegmentList(list: List<Int>) {
        segmentList = list
    }

    fun setTagList(list: List<Int>) {
        tagList = list
    }

    fun setMaxProgress(max: Int) {
        this.max = max
    }

    //修复vivo、步步高设备自动恢复上一次的进度，导致初始化时进度为0进度指示器与进度条分离
    private fun initProgress() {
        progress = 1
        progress = 0
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val isLayoutRtl = isLayoutRtl()
        segmentList.forEach {
            val ratio = if (isLayoutRtl) {
                (max - it) / max.toFloat()
            } else {
                it / max.toFloat()
            }
            segmentRectF.set(
                paddingStart + (width - paddingStart - paddingEnd) * ratio - SEGMENT_RECT_RADIUS,
                height.toFloat() / 2 - SEGMENT_PAINT_HEIGHT / 2,
                paddingStart + (width - paddingStart - paddingEnd) * ratio + SEGMENT_RECT_RADIUS,
                height.toFloat() / 2 + SEGMENT_PAINT_HEIGHT / 2,
            )
            canvas.drawRoundRect(
                segmentRectF,
                SEGMENT_RECT_RADIUS,
                SEGMENT_RECT_RADIUS,
                segmentPaint
            )
        }
        tagList.forEach {
            val ratio = if (isLayoutRtl) {
                (max - it) / max.toFloat()
            } else {
                it / max.toFloat()
            }
            canvas.drawPoint(
                paddingStart + (width - paddingStart - paddingEnd) * ratio,
                height.toFloat() / 2,
                tagPaint
            )
        }

    }


}
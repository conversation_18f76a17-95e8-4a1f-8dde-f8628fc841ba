package com.topstack.kilonotes.pad.guide

import android.animation.Animator
import android.animation.ValueAnimator
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.topstack.kilonotes.KiloApp.Companion.app
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.config.Preferences.needShowFirstLaunchGuide
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.ktx.setPolicyAndAgreement
import com.topstack.kilonotes.base.note.snippet.SnippetManager
import com.topstack.kilonotes.base.third.initAdvertising
import com.topstack.kilonotes.base.third.initBugly
import com.topstack.kilonotes.base.third.initFunReport
import com.topstack.kilonotes.base.third.initPush
import com.topstack.kilonotes.base.third.initTrack
import com.topstack.kilonotes.base.toggle.FeatureToggle
import com.topstack.kilonotes.base.track.event.GuideEvent.sendGuideStartEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadFragmentThirdGuidePageBinding
import com.topstack.kilonotes.pad.MainActivity
import com.topstack.kilonotes.pad.agreement.UserAgreementActivity
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class PadThirdGuidePageFragment : Fragment() {
    private lateinit var binding: PadFragmentThirdGuidePageBinding

    private var viewCreated = false
    private var isAnimationStarted = false
    private val mViewModel: GuideViewModel by activityViewModels()
    private val sliceTextMarginTop = 150


    companion object {
        private const val LOTTIE_ROOT_PATH = "lottie_animation/guide_third/"
        private const val LOTTIE_FIRST_X = "guide_third_x.json"
        private const val LOTTIE_SECOND_X = "guide_third_x.json"
        private const val LOTTIE_THIRD_X = "guide_third_x.json"
        private const val LOTTIE_UNDO_REDO = "guide_undo_redo.json"
        private const val LOTTIE_UNDO_REDO_IMAGES = "images/"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = PadFragmentThirdGuidePageBinding.inflate(inflater, container, false)
        handleLastFragment()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewCreated = true
        binding.sliceText.text = binding.sliceText.text.replace(Regex("\n"), " ")

        if (DimensionUtil.isLandAndOneThirdScreen(requireContext()) ||
            DimensionUtil.isLandAndHalfScreen(requireContext()) ||
            DimensionUtil.isLandTwoThirdScreen(requireContext()) ||
            DimensionUtil.isLandAndFullScreen(requireContext())
        ) {
            binding.sliceText.setMargins(0, sliceTextMarginTop, 0, 0)
        }
    }

    override fun onResume() {
        super.onResume()
        startAnimation()
    }

    private fun handleLastFragment() {
        val policy = getString(R.string.guide_terms_hint_part_2)
        val agreement = getString(R.string.guide_terms_hint_part_4)
        val content = (getString(R.string.guide_terms_hint_part_1) + policy
                + getString(R.string.guide_terms_hint_part_3)
                + agreement)
        val color = ContextCompat.getColor(requireContext(), R.color.guide_terms_text_link)
        binding.guideTermsAndPolicy.setPolicyAndAgreement(content, policy, color, {
            UserAgreementActivity.showUsersTerms(requireContext())
        }, agreement, color, {
            UserAgreementActivity.showPolicyAgreement(requireContext())
        })
        binding.guideStartUse.setOnClickListener(AntiShakeClickListener {
            openMainActivity()
            sendGuideStartEvent()
            /**
             * 跳转至MainActivity之前，如果多次点击，会在KiloApp中多次回调onActivityResumed，干扰广告的展示逻辑
             * 因此在点击后，禁用按钮
             */
            binding.guideStartUse.isEnabled = false
        })
    }

    private fun openMainActivity() {
        needShowFirstLaunchGuide = false
        if (FeatureToggle.FEAT_TRACK_FLAG) {
            app.initTrack()
        }
        if (FeatureToggle.FEAT_PUSH_FLAG) {
            app.initPush()
        }
        if (FeatureToggle.FEAT_BUGLY_FLAG) {
            app.initBugly()
        }
        if (FeatureToggle.FEAT_FUNREPORT_FLAG) {
            app.initFunReport()
        }
        if (FeatureToggle.FEAT_ADVERTISING_FLAG) {
            app.initAdvertising(null)
        }
        GlobalScope.launch {
            SnippetManager.recognizeOldSnippets()
        }
        app.processHttpRequest()
        app.preLoadPurchasedInfo()
        app.initTurnOnPolling()
        val activity: Activity = requireActivity()
        startActivity(Intent(activity, MainActivity::class.java).apply {
            action = activity.intent.action
            data = activity.intent.data
        })
        activity.finish()
    }

    private fun playAnimation() {
        isAnimationStarted = true
        binding.xFirst.setAnimation(LOTTIE_ROOT_PATH + LOTTIE_FIRST_X)
        binding.xSecond.setAnimation(LOTTIE_ROOT_PATH + LOTTIE_SECOND_X)
        binding.xThird.setAnimation(LOTTIE_ROOT_PATH + LOTTIE_THIRD_X)
        binding.undoRedo.imageAssetsFolder = (LOTTIE_ROOT_PATH + LOTTIE_UNDO_REDO_IMAGES)
        binding.undoRedo.setAnimation(LOTTIE_ROOT_PATH + LOTTIE_UNDO_REDO)

        val animatorListener = object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {

            }

            override fun onAnimationEnd(animation: Animator) {
                binding.xFirst.visibility = View.INVISIBLE
                binding.xSecond.visibility = View.INVISIBLE
                binding.xThird.visibility = View.INVISIBLE

            }

            override fun onAnimationCancel(animation: Animator) {

            }

            override fun onAnimationRepeat(animation: Animator) {

            }
        }

        binding.xFirst.postDelayed(
            {
                binding.xFirst.apply {
                    visibility = View.VISIBLE
                    playAnimation()
                    addAnimatorListener(animatorListener)
                }

            }, 2300
        )
        binding.xSecond.postDelayed(
            {
                binding.xSecond.apply {
                    visibility = View.VISIBLE
                    playAnimation()
                    addAnimatorListener(animatorListener)
                }
            }, 3500
        )
        binding.xThird.postDelayed(
            {
                binding.xThird.apply {
                    visibility = View.VISIBLE
                    playAnimation()
                    addAnimatorListener(animatorListener)
                }
            }, 4700
        )
        binding.undoRedo.postDelayed(
            {
                binding.undoRedo.apply {
                    visibility = View.VISIBLE
                    playAnimation()
                    addAnimatorListener(animatorListener)
                }
            }, 5400
        )
    }


    fun startAnimation() {
        if (viewCreated) {
            if (!isAnimationStarted) {
                playAnimation()
            }
            binding.root.transitionToEnd()
            mViewModel.isAnimationExecuted = true
        }
    }

    fun sliceBackAnimation(valueAnimator: ValueAnimator, values: Float) {
        valueAnimator.addUpdateListener {
            if (viewCreated) {
                binding.sliceText.apply {
                    scaleX = 0.2f + values * 0.8f
                    scaleY = 0.2f + values * 0.8f
                    alpha = 0.2f + values * 0.8f
                }
                binding.guideStartUse.apply {
                    scaleX = 0.2f + values * 0.8f
                    scaleY = 0.2f + values * 0.8f
                    alpha = 0.2f + values * 0.8f
                }
                binding.guideTermsAndPolicy.apply {
                    scaleX = 0.2f + values * 0.8f
                    scaleY = 0.2f + values * 0.8f
                    alpha = 0.2f + values * 0.8f
                }
            }
        }
    }

    fun setStartState() {
        if (viewCreated) {
            binding.root.setState(R.id.page_three_start, 0, 0)
        }
    }


    private fun setProgress(progress: Float) {
        if (viewCreated) {
            binding.root.progress = progress
        }
    }
}
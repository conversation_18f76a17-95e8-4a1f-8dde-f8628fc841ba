package com.topstack.kilonotes.pad.component.dialog

import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.BaseDialogFragment
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.i18n.isSimplifiedChineseLanguage
import com.topstack.kilonotes.base.track.event.PageViewportResizingEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.infra.util.AppUtils

class PageResizingGuideDialog : BaseDialogFragment() {

    companion object {
        const val TAG = "PageResizingGuideDialog"
        const val StepOne = 1
        const val StepTwo = 2
        const val StepThree = 3
        var currentStep = StepOne
    }

    private val guideImg: ImageView by lazy { requireView().findViewById(R.id.guide_img) }
    private val guideBtn: TextView by lazy { requireView().findViewById(R.id.guide_btn) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return if (isLandOneThirdScreen()) {
            inflater.inflate(
                R.layout.dialog_page_resizing_guide_one_three_horizontal,
                container,
                false
            )
        } else if (isPortraitOneThirdScreen()) {
            inflater.inflate(
                R.layout.dialog_page_resizing_guide_one_three_vertical,
                container,
                false
            )
        } else if (isLandHalfOrLandTwoThirdsOrPortraitFullOrPortraitTwoThirdsOrPortraitHalf()) {
            inflater.inflate(
                R.layout.dialog_page_resizing_guide_one_half_horizontal,
                container,
                false
            )
        } else {
            inflater.inflate(R.layout.dialog_page_resizing_guide, container, false)
        }.apply {
            measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        switchViewContentByCurrentStep()
        guideBtn.setOnClickListener {
            currentStep++
            switchViewContentByCurrentStep()
        }
        PageViewportResizingEvent.sendPageResizingGuideShow()
        Preferences.needShowPageResizingGuideDialog = false
    }

    private fun switchViewContentByCurrentStep() {
        val guideImgId: Int
        val guideBtnText: String
        when (currentStep) {
            StepOne -> {
                guideImgId =
                    if (isSimplifiedChineseLanguage(requireContext())) R.drawable.page_resizing_guide_img_step_one else R.drawable.page_resizing_guide_img_step_one_english
                guideBtnText = getString(R.string.next_step)
            }

            StepTwo -> {
                guideImgId =
                    if (isSimplifiedChineseLanguage(requireContext())) R.drawable.page_resizing_guide_img_step_two else R.drawable.page_resizing_guide_img_step_two_english
                guideBtnText = getString(R.string.next_step)
            }

            StepThree -> {
                guideImgId = R.drawable.page_resizing_guide_img_step_three
                guideBtnText = getString(R.string.finish_text_edit)
            }

            else -> {
                dismiss()
                return
            }
        }
        guideImg.setImageDrawable(AppUtils.getDrawable(guideImgId))
        guideBtn.text = guideBtnText
    }

    private fun isLandOneThirdScreen(): Boolean {
        return DimensionUtil.isLandAndOneThirdScreen(requireContext()) ||
                DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext())
    }

    private fun isPortraitOneThirdScreen(): Boolean {
        return DimensionUtil.isPortraitAndOneThirdScreen(context)
    }

    private fun isLandHalfOrLandTwoThirdsOrPortraitFullOrPortraitTwoThirdsOrPortraitHalf(): Boolean {
        return DimensionUtil.isLandAndHalfScreen(context) ||
                DimensionUtil.isLandTwoThirdScreen(context) ||
                DimensionUtil.isPortraitAndFullScreen(context) ||
                DimensionUtil.isPortraitAndTwoThirdsScreen(context) ||
                DimensionUtil.isPortraitAndHalfScreen(context)
    }

    override fun onStart() {
        super.onStart()
        dialog?.apply {
            setOnKeyListener { _, keyCode, _ -> keyCode == KeyEvent.KEYCODE_BACK }
            setCanceledOnTouchOutside(false)
        }
        dialog?.window?.apply {
            val params = attributes
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.MATCH_PARENT
            attributes = params
            setBackgroundDrawable(
                ColorDrawable(
                    resources.getColor(
                        R.color.page_resizing_guide_background_color,
                        null
                    )
                )
            )
            setGravity(Gravity.CENTER)
        }
    }
}
package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.InputFilter
import android.text.InputType
import android.text.TextUtils
import android.text.TextWatcher
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.databinding.PadCreateNoteNameInputBinding
import com.topstack.kilonotes.infra.util.LogHelper

class PadCreateNoteInputLayout(
    context: Context,
    attrs: AttributeSet
) : ConstraintLayout(context, attrs) {

    companion object {
        private const val DEFAULT_TEXT_COLOR = Color.GRAY
        private const val TAG = "PadCreateNoteInputLayout"
    }

    private var textSize: Float = 0f
    private var defaultText: String? = null
    private var textColor: Int = Color.GRAY
    private var hint: String? = null
    private var editTextPaddingBottom: Float = 0f

    private var binding: PadCreateNoteNameInputBinding

    var text: String?
        set(value) {
            binding.commonInputLayoutEdit.setText(value)
        }
        get() {
            return binding.commonInputLayoutEdit.text.toString()
        }

    init {
        val DEFAULT_TEXT_SIZE = context.resources.getDimensionPixelSize(R.dimen.sp_28)
        val array = context.obtainStyledAttributes(attrs, R.styleable.CommonInputLayout)
        try {
            textSize = array.getDimension(
                R.styleable.CommonInputLayout_textSize,
                DEFAULT_TEXT_SIZE.toFloat()
            )
            defaultText = array.getString(R.styleable.CommonInputLayout_defaultText)
            textColor = array.getColor(
                R.styleable.CommonInputLayout_textColor, DEFAULT_TEXT_COLOR
            )
            hint = array.getString(R.styleable.CommonInputLayout_text_hint)
            editTextPaddingBottom = array.getDimension(
                R.styleable.CommonInputLayout_editTextPaddingBottom,
                0f
            )
        } catch (e: Exception) {
            e.printStackTrace()
            LogHelper.d(TAG, e.message.toString())
        } finally {
            array.recycle()
        }
        binding = PadCreateNoteNameInputBinding.inflate(LayoutInflater.from(context), this, true)
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        binding.commonInputLayoutEdit.setText(defaultText)
        binding.commonInputLayoutEdit.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
        binding.commonInputLayoutEdit.setTextColor(textColor)
        binding.commonInputLayoutClear.visibility = View.INVISIBLE
        binding.commonInputLayoutClear.setOnClickListener { v ->
            binding.commonInputLayoutEdit.setText(
                ""
            )
        }
        binding.commonInputLayoutEdit.setHint(hint)
        binding.commonInputLayoutEdit.setPadding(
            binding.commonInputLayoutEdit.paddingStart,
            binding.commonInputLayoutEdit.paddingTop,
            binding.commonInputLayoutEdit.paddingRight,
            editTextPaddingBottom.toInt()
        )
        setClearIconMargin()
        resetInputAttrWhenNotHasFocus()
    }

    private fun setClearIconMargin() {
        val layoutParams = binding.commonInputLayoutClear.layoutParams as MarginLayoutParams
        layoutParams.rightMargin = layoutParams.height / 2
    }

    fun setHint(content: String) {
        binding.commonInputLayoutEdit.hint = content
    }

    fun setMaxLines(maxLine: Int) {
        binding.commonInputLayoutEdit.maxLines = maxLine
    }

    fun needFocus() {
        binding.commonInputLayoutEdit.requestFocus()
    }

    override fun hasFocus(): Boolean {
        return binding.commonInputLayoutEdit.hasFocus()
    }

    override fun clearFocus() {
        binding.commonInputLayoutEdit.clearFocus()
    }

    fun setEditListener(listener: TextView.OnEditorActionListener?) {
        binding.commonInputLayoutEdit.setOnEditorActionListener(listener)
    }

    fun setOnEditTextInputFilter(filter: InputFilter) {
        val inputFilters = arrayOf(filter)
        binding.commonInputLayoutEdit.filters = inputFilters
    }

    fun addTextChangedListener(textWatcher: TextWatcher?) {
        binding.commonInputLayoutEdit.addTextChangedListener(textWatcher)
    }

    fun removeTextChangedListener(textWatcher: TextWatcher?) {
        binding.commonInputLayoutEdit.removeTextChangedListener(textWatcher)
    }

    fun showSoftKeyboard() {
        val editText = binding.commonInputLayoutEdit
        editText.requestFocus()
        editText.setSelection(editText.text.length)
        editText.post {
            val controller =
                ViewCompat.getWindowInsetsController(editText)
            controller?.show(WindowInsetsCompat.Type.ime())
        }
    }

    fun hideSoftKeyboard() {
        val controller = ViewCompat.getWindowInsetsController(binding.commonInputLayoutEdit)
        controller?.hide(WindowInsetsCompat.Type.ime())
    }

    fun setInputRadio(radio: Float) {
        val background = binding.commonInputLayoutEdit.background
        if (background is GradientDrawable) {
            background.cornerRadius = radio
        }
    }

    fun setClearIconSize(width: Int, height: Int) {
        val layoutParams = binding.commonInputLayoutClear.layoutParams
        layoutParams.width = width
        layoutParams.height = height
        binding.commonInputLayoutClear.layoutParams = layoutParams
    }

    fun setClearIconMargin(left: Int, top: Int, right: Int, bottom: Int) {
        binding.commonInputLayoutClear.setMargins(left, top, right, bottom)
    }

    fun setClearIconVisibility(isVisible: Boolean) {
        var visible = VISIBLE
        if (!isVisible) {
            visible = INVISIBLE
        }
        binding.commonInputLayoutClear.visibility = visible
    }

    fun setInputLayoutOnFocusChangeListener(onFocusChangeListener: OnFocusChangeListener) {
        binding.commonInputLayoutEdit.setOnFocusChangeListener(onFocusChangeListener)
    }

    fun setTextColor(color: Int) {
        binding.commonInputLayoutEdit.setTextColor(color)
    }

    fun resetInputAttrWhenNotHasFocus() {
        binding.commonInputLayoutEdit.isFocusableInTouchMode = false
        binding.commonInputLayoutEdit.isActivated = false
        binding.commonInputLayoutEdit.isSingleLine = true
        binding.commonInputLayoutEdit.keyListener = null
        binding.commonInputLayoutEdit.ellipsize = TextUtils.TruncateAt.END
    }

    fun resetInputAttrWhenHasFocus() {
        binding.commonInputLayoutEdit.isFocusableInTouchMode = true
        binding.commonInputLayoutEdit.isActivated = true
        binding.commonInputLayoutEdit.inputType = InputType.TYPE_CLASS_TEXT
        binding.commonInputLayoutEdit.isSingleLine = true
        binding.commonInputLayoutEdit.ellipsize = null
        showSoftKeyboard()
    }

    fun setOnInputClickListener(listener: OnClickListener) {
        binding.commonInputLayoutEdit.setOnClickListener(listener)
    }
}
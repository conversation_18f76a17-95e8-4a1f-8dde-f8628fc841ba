package com.topstack.kilonotes.pad.select

import android.os.Bundle
import androidx.activity.viewModels
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.activity.BaseDialogActivity

class SelectPhotoDialogActivity : BaseDialogActivity() {
    companion object {
        const val BUNDLE_KEY_NEED_CROP_IMAGE = "needCropImage"
        const val BUNDLE_KEY_NEED_FIX_RATIO = "needFixRatio"
        const val BUNDLE_KEY_NEED_SELECT_RATIO = "needSelectRatio"
        const val BUNDLE_KEY_FORBID_ZOOM = "forbidZoom"
        const val BUNDLE_KEY_IGNORE_DETAIL_IMAGE = "ignoreDetailImage"
        const val CREATE_IS_VERTICAL = "isVertical"
        const val BUNDLE_KEY_NEED_CROP_AND_CHANGE_ALPHA = "needCropAndChangeAlpha"
    }

    private val selectPhotoViewModel: PadSelectPhotoViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectPhotoViewModel.needCropImage =
            intent.extras?.getBoolean(BUNDLE_KEY_NEED_CROP_IMAGE, false) ?: false
        selectPhotoViewModel.needFixRatio =
            intent.extras?.getBoolean(BUNDLE_KEY_NEED_FIX_RATIO, false) ?: false
        selectPhotoViewModel.needSelectRatio =
            intent.extras?.getBoolean(BUNDLE_KEY_NEED_SELECT_RATIO, false) ?: false
        selectPhotoViewModel.forbidZoom =
            intent.extras?.getBoolean(BUNDLE_KEY_FORBID_ZOOM, false) ?: false
        selectPhotoViewModel.ignoreDetailImage =
            intent.extras?.getBoolean(BUNDLE_KEY_IGNORE_DETAIL_IMAGE, false) ?: false
        selectPhotoViewModel.isVertical =
            intent.extras?.getBoolean(CREATE_IS_VERTICAL, true) ?: true
        selectPhotoViewModel.needCropAndChangeAlpha =
            intent.extras?.getBoolean(BUNDLE_KEY_NEED_CROP_AND_CHANGE_ALPHA, false) ?: false
        setContentView(R.layout.dialog_select_photo_container)
    }

}
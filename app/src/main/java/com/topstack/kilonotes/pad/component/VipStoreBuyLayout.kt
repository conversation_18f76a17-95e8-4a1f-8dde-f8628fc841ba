package com.topstack.kilonotes.pad.component

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.TextViewCompat
import androidx.core.widget.TextViewCompat.AUTO_SIZE_TEXT_TYPE_UNIFORM
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.databinding.PadVipStoreUserBenefitsBinding
import com.topstack.kilonotes.databinding.PhoneUserBenefitsLayoutBinding
import com.topstack.kilonotes.infra.device.DeviceUtils


/**
 * 此布局为一个view
 */
class VipStoreBuyLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    init {
        if (DeviceUtils.isPhoneType(KiloApp.deviceType)) {
            val binding =
                PhoneUserBenefitsLayoutBinding.inflate(LayoutInflater.from(context), this, true)
            TextViewCompat.setAutoSizeTextTypeWithDefaults(
                binding.userBenefitLayoutTitle,
                AUTO_SIZE_TEXT_TYPE_UNIFORM
            )
            TextViewCompat.setAutoSizeTextTypeUniformWithConfiguration(
                binding.userBenefitLayoutTitle, resources.getDimensionPixelSize(
                    R.dimen.sp_30
                ), resources.getDimensionPixelSize(
                    R.dimen.sp_48
                ), resources.getDimensionPixelSize(
                    R.dimen.sp_1
                ) , TypedValue.COMPLEX_UNIT_PX
            )
        } else {
            PadVipStoreUserBenefitsBinding.inflate(LayoutInflater.from(context), this, true)
        }
    }

}
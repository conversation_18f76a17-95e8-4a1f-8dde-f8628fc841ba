package com.topstack.kilonotes.pad.component.dialog

import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.EditText
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.fragment.app.viewModels
import androidx.navigation.navGraphViewModels
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.BaseDialogFragment
import com.topstack.kilonotes.base.component.view.FormatVerificationInputLayout
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.note.viewmodel.OutlineCreateViewModel
import com.topstack.kilonotes.base.note.viewmodel.ThumbnailAndOutlineViewStatusViewModel
import com.topstack.kilonotes.base.track.event.OutlineEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.SoftInputUtil
import com.topstack.kilonotes.databinding.DialogOutlineCreateBinding
import com.topstack.kilonotes.databinding.DialogOutlineCreateOneThreeHorizontalBinding
import com.topstack.kilonotes.databinding.DialogOutlineCreateOneThreeVerticalBinding
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.pad.note.outline.OutlineEntity
import java.util.UUID

class OutlineCreateDialog : BaseDialogFragment() {

    companion object {
        const val TAG = "OutlineCreateDialog"
    }

    private val outlineCreateViewModel: OutlineCreateViewModel by viewModels()
    private val thumbnailAndOutlineViewStatusViewModel: ThumbnailAndOutlineViewStatusViewModel by navGraphViewModels(
        R.id.note_editor
    )

    private lateinit var title: TextView
    private lateinit var close: View
    private lateinit var outlineTitleLevelZero: TextView
    private lateinit var outlineTitleLevelOne: TextView
    private lateinit var outlineTitleLevelTwo: TextView
    private lateinit var outlineTitleAssociateCurrentPage: TextView
    private lateinit var titleErrorTip: TextView
    private lateinit var outlineAssociatePageErrorTip: TextView
    private lateinit var delete: TextView
    private lateinit var confirm: TextView
    private lateinit var outlineTitleInput: FormatVerificationInputLayout
    private lateinit var deleteBtnGroup: Group
    private lateinit var outlineTitleAssociatePageIndex: EditText

    var getPageSize: (() -> Int)? = null
    var getCurrentPageUUID: (() -> UUID)? = null
    var getUUIDByPageIndex: ((Int) -> UUID)? = null
    var deleteOutline: ((OutlineEntity) -> Unit)? = null
    var addOutline: ((OutlineEntity) -> Unit)? = null
    var updateOutline: ((OutlineEntity) -> Unit)? = null
    var outlinePageIsCurrent: ((UUID) -> Boolean)? = null
    var getIndexByPageUUID: ((UUID) -> Int?)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = DimensionUtil.isLandAndOneThirdScreen(context)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
            DialogOutlineCreateOneThreeVerticalBinding.inflate(inflater).root
        } else if (DimensionUtil.isLandAndOneThirdScreen(context)) {
            DialogOutlineCreateOneThreeHorizontalBinding.inflate(inflater).root
        } else {
            DialogOutlineCreateBinding.inflate(inflater).root
        }

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        title = view.findViewById(R.id.title)
        close = view.findViewById(R.id.close)
        outlineTitleLevelZero = view.findViewById(R.id.outline_title_level_zero)
        outlineTitleLevelOne = view.findViewById(R.id.outline_title_level_one)
        outlineTitleLevelTwo = view.findViewById(R.id.outline_title_level_two)
        outlineTitleAssociateCurrentPage =
            view.findViewById(R.id.outline_title_associate_current_page)
        titleErrorTip = view.findViewById(R.id.title_error_tip)
        outlineAssociatePageErrorTip = view.findViewById(R.id.outline_associate_page_error_tip)
        confirm = view.findViewById(R.id.confirm)
        outlineTitleInput = view.findViewById(R.id.outline_title_input)
        outlineTitleAssociatePageIndex = view.findViewById(R.id.outline_title_associate_page_index)
        delete = view.findViewById(R.id.delete)
        deleteBtnGroup = view.findViewById(R.id.delete_btn_group)
        initView()
        initObserve()
    }

    private fun initView() {
        outlineTitleInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(editable: Editable?) {
                if (editable.isNullOrEmpty()) {
                    confirm.setTextColor(AppUtils.getColor(R.color.outline_cannot_confirm_color))
                } else {
                    if (editable.isNotEmpty()) {
                        titleErrorTip.visibility = View.GONE
                        confirm.setTextColor(AppUtils.getColor(R.color.text_secondary))
                    }
                }
            }

        })

        outlineTitleAssociatePageIndex.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(editable: Editable?) {
                editable?.let { pageIndex ->
                    if (pageIndex.isNotEmpty()) {
                        outlineAssociatePageErrorTip.visibility = View.GONE
                    }
                }
            }

        })
        val outlineEntity = thumbnailAndOutlineViewStatusViewModel.outlineEntity
        if (outlineEntity == null) {
            deleteBtnGroup.visibility = View.GONE
            title.text = AppUtils.getString(R.string.outline_title_create)
            if (!thumbnailAndOutlineViewStatusViewModel.outlineTitleDefault.isNullOrEmpty()) {
                outlineTitleInput.text = thumbnailAndOutlineViewStatusViewModel.outlineTitleDefault
                thumbnailAndOutlineViewStatusViewModel.outlineTitleDefault = null
            }
        } else {
            deleteBtnGroup.visibility = View.VISIBLE
            title.text = AppUtils.getString(R.string.outline_title_edit)
            outlineTitleInput.text = outlineEntity.title
            outlineCreateViewModel.changeCurrentOutlineLevel(outlineEntity.titleLevel)
            outlineCreateViewModel.changeIsCurrentPage(false)
            getIndexByPageUUID?.invoke(outlineEntity.linkedPageUUID)?.let { pageIndex ->
                outlineTitleAssociatePageIndex.setText("${pageIndex + 1}")
            }
            delete.setOnClickListener(AntiShakeClickListener {
                deleteOutline?.invoke(outlineEntity)
            })
        }
        outlineTitleInput.setClearIconVisibilityListener { visible ->
            if (isAdded) {
                val textPaddingEnd = if (visible) {
                    resources.getDimensionPixelSize(R.dimen.dp_66)
                } else {
                    resources.getDimensionPixelSize(R.dimen.dp_24)
                }
                outlineTitleInput.setTextPadding(
                    outlineTitleInput.getTextPaddingStart(),
                    outlineTitleInput.getTextPaddingTop(),
                    textPaddingEnd,
                    outlineTitleInput.getTextPaddingBottom()
                )
            }
        }
        outlineTitleInput.setClearIconVisibility(false)
        outlineTitleInput.setCommonInputLayoutFocusChange { _, hasFocus ->
            if (hasFocus) {
                if (outlineTitleInput.text?.isNotEmpty() == true) {
                    outlineTitleInput.setClearIconVisibility(true)
                }
                SoftInputUtil.showSoftInput(outlineTitleInput.getEditText())
            }
        }
        close.setOnClickListener(AntiShakeClickListener {
            thumbnailAndOutlineViewStatusViewModel.hideOutlineEditDialog()
        })
        outlineTitleLevelZero.setOnClickListener(AntiShakeClickListener {
            outlineCreateViewModel.changeCurrentOutlineLevel(OutlineEntity.TitleLevel.ZERO)
        })
        outlineTitleLevelOne.setOnClickListener(AntiShakeClickListener {
            outlineCreateViewModel.changeCurrentOutlineLevel(OutlineEntity.TitleLevel.ONE)
        })
        outlineTitleLevelTwo.setOnClickListener(AntiShakeClickListener {
            outlineCreateViewModel.changeCurrentOutlineLevel(OutlineEntity.TitleLevel.TWO)
        })
        outlineTitleAssociatePageIndex.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                outlineCreateViewModel.changeIsCurrentPage(outlineTitleAssociatePageIndex.text.isEmpty())
            }

        })
        confirm.setOnClickListener(AntiShakeClickListener {
            val isLegalOutline = outlineLegalityVerification()
            if (isLegalOutline) {
                val outlineTitle = outlineCreateViewModel.currentOutlineTitle
                val outlineTitleLevel = outlineCreateViewModel.currentOutlineLevel.value
                    ?: OutlineEntity.TitleLevel.ZERO
                val pageUUID = outlineCreateViewModel.associatePagePageUUID
                pageUUID?.let { UUID ->
                    if (outlineEntity == null) {
                        val confirmOutlineEntity = OutlineEntity(
                            outlineTitle,
                            outlineTitleLevel,
                            UUID,
                            System.currentTimeMillis()
                        )
                        addOutline?.invoke(confirmOutlineEntity)
                    } else {
                        val confirmOutlineEntity = outlineEntity.apply {
                            title = outlineTitle
                            titleLevel = outlineTitleLevel
                            linkedPageUUID = pageUUID
                        }
                        updateOutline?.invoke(confirmOutlineEntity)
                    }
                }
            }
        })

        outlineTitleAssociateCurrentPage.setOnClickListener(AntiShakeClickListener {
            outlineCreateViewModel.changeIsCurrentPage(true)
        })
    }

    private fun initObserve() {
        outlineCreateViewModel.currentOutlineLevel.observe(viewLifecycleOwner) { currentOutlineLevel ->
            changeTextViewSelectState(outlineTitleLevelZero, false)
            changeTextViewSelectState(outlineTitleLevelOne, false)
            changeTextViewSelectState(outlineTitleLevelTwo, false)
            when (currentOutlineLevel) {
                OutlineEntity.TitleLevel.ZERO -> changeTextViewSelectState(
                    outlineTitleLevelZero,
                    true
                )

                OutlineEntity.TitleLevel.ONE -> changeTextViewSelectState(
                    outlineTitleLevelOne,
                    true
                )

                OutlineEntity.TitleLevel.TWO -> changeTextViewSelectState(
                    outlineTitleLevelTwo,
                    true
                )

                else -> {}
            }
        }
        outlineCreateViewModel.isCurrentPage.observe(viewLifecycleOwner) { isCurrentPage ->
            if (isCurrentPage) {
                changeTextViewSelectState(outlineTitleAssociateCurrentPage, true)
                if (!outlineTitleAssociatePageIndex.text.isNullOrEmpty()) {
                    outlineTitleAssociatePageIndex.text = null
                }
            } else {
                changeTextViewSelectState(outlineTitleAssociateCurrentPage, false)
            }
        }
    }

    private fun outlineLegalityVerification(): Boolean {
        val associatePageIndexLegality = associatePageIndexLegalityVerification()
        val outlineEntity = thumbnailAndOutlineViewStatusViewModel.outlineEntity
        if (outlineEntity == null) {
            if (outlineTitleInput.text.isNullOrEmpty()) {
                OutlineEvent.sendOutlineCreateFail(OutlineEvent.ERROR_TITLE)
            } else {
                if (outlineTitleAssociateCurrentPage.isSelected || associatePageIndexLegality) {
                    OutlineEvent.sendOutlineCreateSuccess(OutlineEvent.outlineDialogCreateSource)
                } else {
                    OutlineEvent.sendOutlineCreateFail(OutlineEvent.ERROR_PAGE)
                }
            }
        } else {
            if (outlineTitleInput.text.isNullOrEmpty()) {
                OutlineEvent.sendOutlineEditFail(OutlineEvent.ERROR_TITLE)
            } else {
                if (outlineTitleAssociateCurrentPage.isSelected || associatePageIndexLegality) {
                    OutlineEvent.sendOutlineEditSuccess()
                } else {
                    OutlineEvent.sendOutlineEditFail(OutlineEvent.ERROR_PAGE)
                }
            }
        }
        if (outlineTitleAssociateCurrentPage.isSelected || associatePageIndexLegality) {
            outlineAssociatePageErrorTip.visibility = View.GONE
        } else {
            outlineAssociatePageErrorTip.text =
                AppUtils.getString(R.string.outline_associate_page_tip)
            outlineAssociatePageErrorTip.visibility = View.VISIBLE
        }
        if (outlineTitleInput.text.isNullOrEmpty()) {
            titleErrorTip.text = AppUtils.getString(R.string.outline_title_error_tip)
            titleErrorTip.visibility = View.VISIBLE
            return false
        } else {
            titleErrorTip.visibility = View.GONE
            outlineCreateViewModel.currentOutlineTitle = outlineTitleInput.text.toString()
        }
        return if (outlineTitleAssociateCurrentPage.isSelected) {
            outlineCreateViewModel.associatePagePageUUID = getCurrentPageUUID?.invoke()
            true
        } else {
            associatePageIndexLegality
        }
    }

    private fun associatePageIndexLegalityVerification(): Boolean {
        val pageIndex = outlineTitleAssociatePageIndex.text.toString()
        if (pageIndex.isEmpty()) {
            return false
        } else {
            val pageSize = getPageSize?.invoke() ?: return false
            return try {
                val index = pageIndex.toInt()
                if (index - 1 in 0 until pageSize) {
                    outlineCreateViewModel.associatePagePageUUID =
                        getUUIDByPageIndex?.invoke(index - 1)
                    true
                } else {
                    false
                }
            } catch (numberFormatException: NumberFormatException) {
                LogHelper.e(TAG, "input number too long,format to int failed")
                false
            }
        }
    }

    private fun changeTextViewSelectState(textView: TextView, isSelect: Boolean) {
        if (isSelect) {
            textView.isSelected = true
            textView.setTextColor(AppUtils.getColor(R.color.white))
        } else {
            textView.isSelected = false
            textView.setTextColor(AppUtils.getColor(R.color.text_secondary))
        }
    }

    fun clearFocus() {
        outlineTitleInput.clearFocus()
        outlineTitleAssociatePageIndex.clearFocus()
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setLayout(
                if (DimensionUtil.isLandAndOneThirdScreen(context)) {
                    LayoutParams.MATCH_PARENT
                } else {
                    resources.getDimensionPixelSize(R.dimen.dp_560)
                },
                if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
                    LayoutParams.MATCH_PARENT
                } else {
                    LayoutParams.WRAP_CONTENT
                }
            )
            if (DimensionUtil.isLandAndOneThirdScreen(context)) {
                setGravity(Gravity.BOTTOM)
            } else {
                setGravity(Gravity.CENTER)
            }
        }
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        thumbnailAndOutlineViewStatusViewModel.hideOutlineEditDialog()
    }
}
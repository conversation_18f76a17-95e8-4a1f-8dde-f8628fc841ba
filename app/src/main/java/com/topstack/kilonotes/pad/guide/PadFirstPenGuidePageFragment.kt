package com.topstack.kilonotes.pad.guide

import android.animation.ValueAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadFragmentFirstPenGuidePageBinding

class PadFirstPenGuidePageFragment : Fragment() {

    private lateinit var binding: PadFragmentFirstPenGuidePageBinding
    private var viewCreated = false
    private val sliceTextMarginTop = 150

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = PadFragmentFirstPenGuidePageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewCreated = true
        binding.sliceText.text = binding.sliceText.text.replace(Regex("\n"), " ")

        if (DimensionUtil.isLandAndOneThirdScreen(requireContext()) ||
            DimensionUtil.isLandAndHalfScreen(requireContext()) ||
            DimensionUtil.isLandTwoThirdScreen(requireContext()) ||
            DimensionUtil.isLandAndFullScreen(requireContext())
        ) {
            binding.sliceText.setMargins(0, sliceTextMarginTop, 0, 0)
        }
        if (DimensionUtil.isPortraitAndFullScreen(requireContext()) || DimensionUtil.isLandAndHalfScreen(
                requireContext()
            ) || DimensionUtil.isPortraitAndTwoThirdsScreen(requireContext())
        ) {
            binding.penColors.setMargins(
                0,
                binding.penColors.top,
                requireContext().resources.getDimensionPixelSize(R.dimen.dp_455),
                0
            )
            binding.penSize.setMargins(
                requireContext().resources.getDimensionPixelSize(R.dimen.dp_465),
                binding.penSize.top,
                0,
                0
            )
        }
    }

    override fun onResume() {
        super.onResume()
        startAnimation()
    }

    fun sliceAnimation(valueAnimator: ValueAnimator, values: Float, offset: Float) {
        valueAnimator.addUpdateListener {
            binding.apply {
                sliceText.apply {
                    alpha = 1 - values * 2.4f
                    translationX = -offset * 1.1f
                }
                group.apply {
                    translationX = -offset * 1.1f
                    alpha = 1 - values * 2f
                }
                pen.apply {
                    translationX = -offset * 1.2f
                    alpha = 1 - values * 1.6f
                }
                penSize.apply {
                    translationX = -offset * 2.5f
                    alpha = 1 - values * 1.8f
                }
                penColors.apply {
                    translationX = -offset * 2.0f
                    alpha = 1 - values * 2f
                }
                otherSettings.apply {
                    translationX = -offset * 3.0f
                    alpha = 1 - values * 6.0f
                }
                penStyleSelect.apply {
                    alpha = 1 - values * 6f
                    translationX = offset
                    scaleX = 1 - values * 6f
                    scaleY = 1 - values * 6f
                }
            }
        }
    }

    fun startAnimation() {
        if (viewCreated) {
            binding.root.postDelayed({ binding.root.transitionToEnd() }, 300)
        }
    }

    fun setProgress(progress: Float) {
        if (viewCreated) {
            binding.root.progress = progress
        }
    }
}
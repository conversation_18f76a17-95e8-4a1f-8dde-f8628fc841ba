package com.topstack.kilonotes.pad.component.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.BaseLogoLoadingDialog
import com.topstack.kilonotes.databinding.DialogLogoLoadingBinding

class PadLogoLoadingDialog : BaseLogoLoadingDialog() {

    companion object {
        const val TAG = "PhoneLogoLoadingDialog"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        return DialogLogoLoadingBinding.inflate(inflater, container, false).root
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setLayout(
            resources.getDimensionPixelSize(R.dimen.dp_340),
            resources.getDimensionPixelSize(R.dimen.dp_322)
        )
        dialog?.window?.setGravity(Gravity.CENTER)
    }

}
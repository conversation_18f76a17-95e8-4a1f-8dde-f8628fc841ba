package com.topstack.kilonotes.pad.note

import android.Manifest
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Path
import android.graphics.Rect
import android.graphics.RectF
import android.hardware.input.InputManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.text.SpannableString
import android.text.Spanned
import android.text.style.UnderlineSpan
import android.view.Gravity
import android.view.InputDevice
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.PopupWindow
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.toRectF
import androidx.core.view.doOnNextLayout
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import androidx.core.view.marginTop
import androidx.core.view.updateMargins
import androidx.core.widget.PopupWindowCompat
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.withStateAtLeast
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import androidx.transition.AutoTransition
import androidx.transition.Transition
import androidx.transition.TransitionListenerAdapter
import androidx.transition.TransitionManager
import com.bumptech.glide.Glide
import com.topstack.kilonotes.BuildConfig
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.base.KeyEventViewModel
import com.topstack.kilonotes.base.account.UnifiedLogin
import com.topstack.kilonotes.base.ai.manager.AiAccessManager
import com.topstack.kilonotes.base.ai.viewmodel.AIViewModel
import com.topstack.kilonotes.base.audio.AudioPlayer
import com.topstack.kilonotes.base.component.dialog.AlertDialog
import com.topstack.kilonotes.base.component.dialog.VipExclusiveDialog
import com.topstack.kilonotes.base.component.fragment.NaviEnum
import com.topstack.kilonotes.base.component.view.AdsorptionEdgeLayout
import com.topstack.kilonotes.base.component.view.BubbleLayout
import com.topstack.kilonotes.base.component.view.VerticalTextView
import com.topstack.kilonotes.base.component.view.WindowColorPickView
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.component.viewmodel.GlobalDialogViewModel
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.constant.MimeType
import com.topstack.kilonotes.base.customtool.model.CustomTool
import com.topstack.kilonotes.base.datareporter.DataType
import com.topstack.kilonotes.base.datareporter.datacollection.StickerDataCollection
import com.topstack.kilonotes.base.db.HandbookDatabase
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.DocumentManager
import com.topstack.kilonotes.base.doc.io.FileImporter
import com.topstack.kilonotes.base.doc.io.ThumbnailManager
import com.topstack.kilonotes.base.doc.record.RecordTag
import com.topstack.kilonotes.base.doc.record.RecordsInfo
import com.topstack.kilonotes.base.doodle.listeners.IGraffitiSwitchListener
import com.topstack.kilonotes.base.doodle.listeners.IHighlighterSwitchListener
import com.topstack.kilonotes.base.doodle.listeners.IPatternSwitchListener
import com.topstack.kilonotes.base.doodle.listeners.ISnippetListener
import com.topstack.kilonotes.base.doodle.listeners.IVerifyRecordStateAction
import com.topstack.kilonotes.base.doodle.manager.modelmager.Clipboard
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.doodle.model.TransformType
import com.topstack.kilonotes.base.doodle.model.image.InsertableBitmap
import com.topstack.kilonotes.base.doodle.views.doodleview.DoodlePresentationLayer
import com.topstack.kilonotes.base.doodle.views.doodleview.DoodleView
import com.topstack.kilonotes.base.doodle.views.doodleview.GraffitiToolType
import com.topstack.kilonotes.base.doodle.views.doodleview.InputMode
import com.topstack.kilonotes.base.doodle.views.doodleview.OnPageChangeListener
import com.topstack.kilonotes.base.doodle.views.doodleview.OnTransformChangedListener
import com.topstack.kilonotes.base.doodle.views.doodleview.OnTurnPageListener
import com.topstack.kilonotes.base.doodle.views.doodleview.loader.DoodlePageLoader
import com.topstack.kilonotes.base.doodle.views.guideline.GuideLineConfig
import com.topstack.kilonotes.base.doodle.views.lassoview.ILassoAiSolveProblemsListener
import com.topstack.kilonotes.base.doodle.views.multiselectview.Selection
import com.topstack.kilonotes.base.doodle.views.pagepreviewview.PagePreviewGearControlView
import com.topstack.kilonotes.base.doodle.views.recordtoolsview.RecordToolsClickListener
import com.topstack.kilonotes.base.doodle.views.recordview.OnRecordViewDismiss
import com.topstack.kilonotes.base.doodle.visual.brush.VisualStrokeErase
import com.topstack.kilonotes.base.flavor.Channel
import com.topstack.kilonotes.base.fonts.FontDownloadProgressDialog
import com.topstack.kilonotes.base.fonts.FontImportError
import com.topstack.kilonotes.base.fonts.FontImportFail
import com.topstack.kilonotes.base.fonts.FontImportSuccess
import com.topstack.kilonotes.base.fonts.FontManager
import com.topstack.kilonotes.base.handbook.model.Template
import com.topstack.kilonotes.base.imagecrop.BaseImageCropDialogFragment
import com.topstack.kilonotes.base.imagecrop.CropType
import com.topstack.kilonotes.base.importfile.BaseHandleImportActivity
import com.topstack.kilonotes.base.ktx.isOverlap
import com.topstack.kilonotes.base.ktx.outsideArea
import com.topstack.kilonotes.base.ktx.safeDismiss
import com.topstack.kilonotes.base.ktx.safeNavigate
import com.topstack.kilonotes.base.ktx.safeShow
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.ktx.showStorageNotEnoughToDownloadResourceDialog
import com.topstack.kilonotes.base.ktx.showStorageNotEnoughToEnableFunctionDialog
import com.topstack.kilonotes.base.ktx.showTemplateFileErrorDialog
import com.topstack.kilonotes.base.material.repository.NoteMaterialRepository
import com.topstack.kilonotes.base.note.BaseNoteEditorFragment
import com.topstack.kilonotes.base.note.BaseSelectColorWindow
import com.topstack.kilonotes.base.note.FontAttributes
import com.topstack.kilonotes.base.note.GraffitiAttributes
import com.topstack.kilonotes.base.note.HighlighterAttributes
import com.topstack.kilonotes.base.note.PenAttributes
import com.topstack.kilonotes.base.note.TextAttributes
import com.topstack.kilonotes.base.note.adapter.NoteToolEraserAdapter
import com.topstack.kilonotes.base.note.adapter.NoteToolLassoAdapter
import com.topstack.kilonotes.base.note.adapter.NoteToolLassoStyleAdapter
import com.topstack.kilonotes.base.note.adapter.ToolColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolDividerAdapter
import com.topstack.kilonotes.base.note.adapter.ToolHighlighterColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolHighlighterSizeAdapter
import com.topstack.kilonotes.base.note.adapter.ToolLaserColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolPenColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolPenSizeAdapter
import com.topstack.kilonotes.base.note.adapter.ToolTextColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolTextFontAdapter
import com.topstack.kilonotes.base.note.adapter.ToolTextParagraphAdapter
import com.topstack.kilonotes.base.note.adapter.ToolTextSizeAdapter
import com.topstack.kilonotes.base.note.listener.ThumbnailTouchAction
import com.topstack.kilonotes.base.note.model.ColorWindowItem
import com.topstack.kilonotes.base.note.model.ColorWindowItemType
import com.topstack.kilonotes.base.note.model.EraseToolItem
import com.topstack.kilonotes.base.note.model.ExportTypeMode
import com.topstack.kilonotes.base.note.model.InsertPosition
import com.topstack.kilonotes.base.note.model.NoteEditorBackStatus
import com.topstack.kilonotes.base.note.model.NotebookCoverType
import com.topstack.kilonotes.base.note.model.TitleErrorType
import com.topstack.kilonotes.base.note.recognition.text.TextRecognitionManager
import com.topstack.kilonotes.base.note.record.NoteRecordItem
import com.topstack.kilonotes.base.note.snippet.SnippetManager
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippet
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippetItem
import com.topstack.kilonotes.base.note.snippet.data.SnippetTag
import com.topstack.kilonotes.base.note.usage.TemplatePageUsageHelper
import com.topstack.kilonotes.base.note.viewmodel.AddPageSource
import com.topstack.kilonotes.base.note.viewmodel.AdsorptionEdgeViewModel
import com.topstack.kilonotes.base.note.viewmodel.BaseTemplateViewModel
import com.topstack.kilonotes.base.note.viewmodel.CreateNotebookViewModel
import com.topstack.kilonotes.base.note.viewmodel.EditorSwitchDocumentViewModel
import com.topstack.kilonotes.base.note.viewmodel.FontDownloadViewModel
import com.topstack.kilonotes.base.note.viewmodel.ImportFontViewModel
import com.topstack.kilonotes.base.note.viewmodel.NoteEditorBackStatusViewModel
import com.topstack.kilonotes.base.note.viewmodel.NoteMaterialViewModel
import com.topstack.kilonotes.base.note.viewmodel.NoteViewModel
import com.topstack.kilonotes.base.note.viewmodel.PagePreviewViewModel
import com.topstack.kilonotes.base.note.viewmodel.RecordViewModel
import com.topstack.kilonotes.base.note.viewmodel.SideBarViewModel
import com.topstack.kilonotes.base.note.viewmodel.SnippetManagerViewModel
import com.topstack.kilonotes.base.note.viewmodel.SnippetViewModel
import com.topstack.kilonotes.base.note.viewmodel.SnippetViewModel.Companion.SEARCH_DELAY_INTERVAL
import com.topstack.kilonotes.base.note.viewmodel.TemplateViewModel
import com.topstack.kilonotes.base.note.viewmodel.ThumbnailAndOutlineViewStatusViewModel
import com.topstack.kilonotes.base.pageresizing.viewmodel.PageViewPortAdjustmentViewModel
import com.topstack.kilonotes.base.pickimage.PickImageResult
import com.topstack.kilonotes.base.pickimage.PickImageResultParser
import com.topstack.kilonotes.base.push.PushManager
import com.topstack.kilonotes.base.search.SearchManager
import com.topstack.kilonotes.base.search.model.DocRenderInfo
import com.topstack.kilonotes.base.search.model.DocSearchResult
import com.topstack.kilonotes.base.search.model.SearchStart
import com.topstack.kilonotes.base.search.model.SearchSuccess
import com.topstack.kilonotes.base.search.model.SearchType
import com.topstack.kilonotes.base.search.view.DocumentSearchGroup
import com.topstack.kilonotes.base.search.view.NoteSearchFrameLayout
import com.topstack.kilonotes.base.search.viewmodel.SearchViewModel
import com.topstack.kilonotes.base.select.BasePhotoCropDialogFragment
import com.topstack.kilonotes.base.track.event.AddTemplateEvent
import com.topstack.kilonotes.base.track.event.AiEvent
import com.topstack.kilonotes.base.track.event.AppAdEvent
import com.topstack.kilonotes.base.track.event.ConsoleEvent
import com.topstack.kilonotes.base.track.event.DraftPaperEvent
import com.topstack.kilonotes.base.track.event.EditEvent
import com.topstack.kilonotes.base.track.event.GraphEvent
import com.topstack.kilonotes.base.track.event.HomeEvent
import com.topstack.kilonotes.base.track.event.ImageCallEvent
import com.topstack.kilonotes.base.track.event.InputDeviceEvent
import com.topstack.kilonotes.base.track.event.InstantAlphaEvent
import com.topstack.kilonotes.base.track.event.LoginLocation
import com.topstack.kilonotes.base.track.event.OutlineEvent
import com.topstack.kilonotes.base.track.event.PageViewportResizingEvent
import com.topstack.kilonotes.base.track.event.RecordEvent
import com.topstack.kilonotes.base.track.event.SearchEvent
import com.topstack.kilonotes.base.track.event.SettingEvent
import com.topstack.kilonotes.base.track.event.SnippetEvent
import com.topstack.kilonotes.base.track.event.TapeEvent
import com.topstack.kilonotes.base.track.event.UserEvent
import com.topstack.kilonotes.base.upgrade.DataUpgradeDialog
import com.topstack.kilonotes.base.util.ActivityResultRequester
import com.topstack.kilonotes.base.util.BRAND_SAMSUNG
import com.topstack.kilonotes.base.util.CharacterUtils
import com.topstack.kilonotes.base.util.ClipboardUtil
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.PercentFormatUtil
import com.topstack.kilonotes.base.util.PermissionRequester
import com.topstack.kilonotes.base.util.ReadingStyleUtils
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.base.util.WindowInsetsUtils
import com.topstack.kilonotes.base.util.activityIsFinishing
import com.topstack.kilonotes.base.util.getDefaultGraffitiLineDrawingPenPreferenceStyleList
import com.topstack.kilonotes.base.util.getDefaultGraffitiOutlinePenPreferenceStyleList
import com.topstack.kilonotes.base.util.getDefaultGraffitiPatternPreferenceStyleList
import com.topstack.kilonotes.base.util.getDefaultHighlighterPreferenceColorList
import com.topstack.kilonotes.base.util.getDefaultPenPreferenceColorList
import com.topstack.kilonotes.base.util.isExternal
import com.topstack.kilonotes.base.util.isPenConnected
import com.topstack.kilonotes.base.util.isWebViewAvailable
import com.topstack.kilonotes.base.util.showWebViewNotAvailableDialog
import com.topstack.kilonotes.databinding.FragmentNoteEditorBinding
import com.topstack.kilonotes.infra.foundation.thread.ThreadUtils
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.infra.size.MmSize
import com.topstack.kilonotes.infra.size.PtSize
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.AppUtils.getColor
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.infra.util.TimeUtil
import com.topstack.kilonotes.infra.util.Timer
import com.topstack.kilonotes.mlkit.recognition.digitalink.DigitalInkRecognitionManager
import com.topstack.kilonotes.mlkit.recognition.gesture.QuickRecognizer
import com.topstack.kilonotes.mlkit.recognition.text.model.TextRecognitionResult
import com.topstack.kilonotes.notedata.NoteRepository
import com.topstack.kilonotes.pad.MainActivity
import com.topstack.kilonotes.pad.component.dialog.GenerateLongPictureProgressDialog
import com.topstack.kilonotes.pad.component.dialog.OutlineCreateDialog
import com.topstack.kilonotes.pad.component.dialog.PadLogoLoadingDialog
import com.topstack.kilonotes.pad.component.popupwindow.PagePreviewPaginationWindow
import com.topstack.kilonotes.pad.console.ConsoleCommand
import com.topstack.kilonotes.pad.console.ConsoleCommandManager
import com.topstack.kilonotes.pad.console.ConsoleCommandType
import com.topstack.kilonotes.pad.console.ICommandExecuteCallback
import com.topstack.kilonotes.pad.console.IConsoleCommandReceiver
import com.topstack.kilonotes.pad.console.IConsoleSwitchToolCommandReceiver
import com.topstack.kilonotes.pad.console.adapter.ConsoleLastDocumentsAdapter
import com.topstack.kilonotes.pad.console.getConsoleToastString
import com.topstack.kilonotes.pad.customtool.dialog.CustomToolbarDialog
import com.topstack.kilonotes.pad.customtool.popuwindow.CustomSelectToolsWindow
import com.topstack.kilonotes.pad.customtool.viewmodel.CustomToolsViewModel
import com.topstack.kilonotes.pad.draftpaper.DraftPaperFragment
import com.topstack.kilonotes.pad.draftpaper.DraftPaperSizeAndLocationHelper
import com.topstack.kilonotes.pad.draftpaper.DraftPaperViewModel
import com.topstack.kilonotes.pad.guide.ConsoleGuideWindow
import com.topstack.kilonotes.pad.guide.DraftPaperFunctionIntroductionGuidePopupWindow
import com.topstack.kilonotes.pad.guide.DraftPaperMorePopupGuideWindow
import com.topstack.kilonotes.pad.guide.NoteToolPenGraphGuideWindow
import com.topstack.kilonotes.pad.imagecrop.ImageCropDialogFragment
import com.topstack.kilonotes.pad.note.adapter.NoteToolPictureAdapter
import com.topstack.kilonotes.pad.note.adapter.SelectParagraphStyleWindow
import com.topstack.kilonotes.pad.note.adapter.TapeStyleShowControlWindow
import com.topstack.kilonotes.pad.note.adapter.ToolGraffitiSizeAdapter
import com.topstack.kilonotes.pad.note.adapter.ToolGraffitiStyleAdapter
import com.topstack.kilonotes.pad.note.adapter.ToolTapeControlAdapter
import com.topstack.kilonotes.pad.note.adapter.ToolTapeStyleAdapter
import com.topstack.kilonotes.pad.note.dialog.NoteSnippetManagerDialog
import com.topstack.kilonotes.pad.note.model.EraseToolType
import com.topstack.kilonotes.pad.note.model.PaperCutTool
import com.topstack.kilonotes.pad.note.model.PaperCutToolType
import com.topstack.kilonotes.pad.note.model.Sidebar
import com.topstack.kilonotes.pad.note.model.ThumbnailActionWindowStatus
import com.topstack.kilonotes.pad.note.model.ThumbnailListScrollPosition
import com.topstack.kilonotes.pad.note.popupwindow.ColorGuideWindow
import com.topstack.kilonotes.pad.note.popupwindow.ConsoleSettingWindow
import com.topstack.kilonotes.pad.note.popupwindow.ExportNoteOneThirdWindow
import com.topstack.kilonotes.pad.note.popupwindow.ExportNoteWindow
import com.topstack.kilonotes.pad.note.popupwindow.MoreToolWindow
import com.topstack.kilonotes.pad.note.popupwindow.NoteImgShareOneThirdWindow
import com.topstack.kilonotes.pad.note.popupwindow.NoteImgShareWindow
import com.topstack.kilonotes.pad.note.popupwindow.NoteMaterialWindow
import com.topstack.kilonotes.pad.note.popupwindow.NoteRecordItemSettingWindow
import com.topstack.kilonotes.pad.note.popupwindow.NoteSnippetGuideWindow
import com.topstack.kilonotes.pad.note.popupwindow.OverviewActionWindow
import com.topstack.kilonotes.pad.note.popupwindow.RecordListWindow
import com.topstack.kilonotes.pad.note.popupwindow.SelectColorWindow
import com.topstack.kilonotes.pad.note.popupwindow.SelectPenSizeWindow
import com.topstack.kilonotes.pad.note.popupwindow.SelectTextSizeWindow
import com.topstack.kilonotes.pad.note.preview.otherdoc.OtherDocPreviewFragment
import com.topstack.kilonotes.pad.select.FontListWindow
import com.topstack.kilonotes.pad.select.PhotoCropDialogFragment
import com.topstack.kilonotes.pad.select.SelectPhotoDialogActivity
import com.topstack.kilonotes.pad.select.SnippetOption
import com.topstack.kilonotes.pad.select.SnippetOptionWindow
import com.topstack.kilonotes.phone.note.PhoneNoteImgShareBottomSheet
import com.topstack.kilonotes.phone.note.state.LongPictureCreateFail
import com.topstack.kilonotes.phone.note.state.LongPictureCreateNone
import com.topstack.kilonotes.phone.note.state.LongPictureCreateProgress
import com.topstack.kilonotes.phone.note.state.LongPictureCreateStart
import com.topstack.kilonotes.phone.note.state.LongPictureCreateSuccess
import com.topstack.kilonotes.push.PushTag
import com.topstack.kilonotes.stylus.FunctionSwitchType
import com.topstack.kilonotes.stylus.StylusToolKit
import com.topstack.kilonotes.xuanhu.XuanhuHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.io.File
import java.util.Locale
import java.util.UUID
import kotlin.math.min

class NoteEditorFragment : BaseNoteEditorFragment<FragmentNoteEditorBinding>(),
    ThumbnailTouchAction, IConsoleSwitchToolCommandReceiver {
    private lateinit var majorToolViews: List<View>

    companion object {
        const val XUANHU_TAG = "xuanhuTag"
        const val REQUEST_WRITE_EXTERNAL_STORAGE_CODE = 1000
        private const val SNIPPET_MORE_SHOW_FLAG = 0
        private const val SNIPPET_MANAGER_SHOW_FLAG = 1
        private const val REVERSE_ROTATION = 180f
        private const val DEFAULT_ROTATION = 0f
        private const val FILE_CORRUPTED_DIALOG_TAG = "fileCorrupted"
        private const val DOCUMENT_PAGE_MISSING_DIALOG_TAG = "pageMissing"
        private const val SWITCH_LAST_DOCUMENTS_NUM = 7
        private const val CONSOLE_TOAST_DISMISS_DELAY_TIME_MS = 2000L
        private const val CONSOLE_DISMISS_PAGE_PREVIEW_RATE_DELAY_TIME_MS = 2000L

        /**
         * 动画时长为250ms，用作正常情况下做平滑的入场或退场动画
         */
        private const val ANIMATION_DURATION_TIME = 250L

        /**
         * 动画时长为0，用作转屏分屏状态下，瞬间展示恢复
         */
        private const val NO_ANIMATION_DURATION = 0L

        private val PERMISSION_ERROR_TAG = "permissionError"
        private const val DELETETHUMBNAIL_WITH_OUTLINE_ALERT_DIALOG =
            "deleteThumbnailWithOutlineAlertDialog"
        private const val TRANSLATE_LOADING_DIALOG_TAG = "TranslateLoadingDialog"
        private const val TRANSLATE_LOADING_DIALOG_DELAY_SHOW_TIME_MS = 500L
        private const val TEXT_RECOGNIZE_TIME_OUT_MS = 5000L
        private const val OTHER_DOC_PREVIEW_BOTTOM_SHEET_TAG = "OtherDocPreviewFragment"
        private const val DRAFT_PAPER_FRAGMENT_TAG = "DraftPaperFragment"
        const val OUTLINE_DELETE_ALERT_DIALOG_TAG = "OutlineDeleteAlertDialog"
        private const val SCALE_LOCKED_HIDDEN_TIME = 5000L
        private const val UNDO_REDO_TOAST_HIDDEN_TIME = 1500L
        private const val TAPE_CONTROL_VIEW_INDEX = 7

        val toolBarShortLength: Int =
            AppUtils.appContext.resources.getDimensionPixelSize(R.dimen.dp_54)
    }

    override val pickImageRequester = ActivityResultRequester(this, PickImageResultParser()) {
        if (it?.uri != null) {
            EditEvent.sendEditPictureUsage("toolbar")
            editorModeViewModel.switchMode(InputMode.IMAGE)
            insertImageElement(it.uri, it.alpha, editorViewModel.selectedInsertableBitmap != null) {
                editorViewModel.selectedInsertableBitmap = null
            }
        }
    }

    private val pickImageForCreatePageRequester =
        ActivityResultRequester(this, PickImageResultParser()) {
            val imageUri = it?.uri
            if (imageUri != null) {
                if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE) {
                    AlertDialog.Builder()
                        .setTitle(resources.getString(R.string.page_replace_title))
                        .setMsg(resources.getString(R.string.page_replace_tip))
                        .setPositiveBtn(resources.getString(R.string.add_page_replace)) {
                            addPageViewModel.addPageFromImageUri(
                                InsertPosition.REPLACE,
                                imageUri,
                                currentDoc
                            )
                            changeCurrentSidebarStatus(Sidebar.NONE)
                        }
                        .setNegativeBtnColor(getColor(R.color.text_secondary))
                        .setNegativeBtn(resources.getString(R.string.cancel)) { }
                        .build().show(childFragmentManager, "")
                } else {
                    addPageViewModel.addPageFromImageUri(
                        addPageViewModel.insertPosition.value!!,
                        imageUri,
                        currentDoc
                    )
                    changeCurrentSidebarStatus(Sidebar.NONE)
                }
            }
        }

    private var lastScale = 100

    private var hideScaleIndicatorJob: Job? = null
        set(value) {
            field?.cancel()
            field = value
        }

    private var photoCropDialogFragment: PhotoCropDialogFragment? = null

    private val permissionRequester = PermissionRequester(this)

    private val aiPickImageRequester: ActivityResultRequester<PickImageResult?> =
        ActivityResultRequester(this, PickImageResultParser()) {
            val uri = it?.uri
            if (uri != null) {
                aiViewModel.setAddPictureUri(uri)
                AiEvent.sendAiAlbumAddPhotoSuccess()
            }
        }

    private var aiTakePhotoLauncher: ActivityResultLauncher<Uri?> =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { success ->
            if (success) {
                photoCropDialogFragment = PhotoCropDialogFragment().apply {
                    cropFileContainAlpha = true
                    photoUri = aiViewModel.cameraPhotoUri
                    setOnCropCompleteListener(object :
                        BasePhotoCropDialogFragment.IOnCropCompleteListener {
                        override fun onCropSuccess(uri: Uri, imageAlpha: Int) {
                            photoCropDialogFragment?.dismiss()
                            aiViewModel.setAddPictureUri(uri)
                            AiEvent.sendAiAlbumTakePictureSuccess()
                        }

                        override fun onCropFailed() {

                        }

                        override fun onCancel() {

                        }
                    })
                }
                photoCropDialogFragment?.show(
                    parentFragmentManager,
                    BasePhotoCropDialogFragment.TAG
                )
            } else {

            }
        }

    private var popWindow: OverviewActionWindow? = null

    private var exportNoteWindow: ExportNoteWindow? = null
    private var exportNoteOneThirdWindow: ExportNoteOneThirdWindow? = null

    private var shareImgWindow: NoteImgShareWindow? = null
    private var shareImgOneThirdWindow: NoteImgShareOneThirdWindow? = null

    private var selectPenSizeWindow: SelectPenSizeWindow? = null

    var vipExclusiveDialog: VipExclusiveDialog? = null

    private var selectTextSizeWindow: SelectTextSizeWindow? = null

    private var draftPaperMorePopupGuideWindow: DraftPaperMorePopupGuideWindow? = null
    private var consoleGuideWindow: ConsoleGuideWindow? = null

    private var draftPaperFunctionIntroductionGuideWindow: DraftPaperFunctionIntroductionGuidePopupWindow? =
        null

    private var selectParagraphStyleWindow: SelectParagraphStyleWindow? = null

    private var noteSnippetGuideWindow: NoteSnippetGuideWindow? = null
    private var noteSnippetDragGuideWindow: NoteSnippetGuideWindow? = null
    private var isSnippetsExpandAnimationEnd: Boolean = false

    private var noteSnippetManagerDialog: NoteSnippetManagerDialog? = null

    private var moreToolWindow: MoreToolWindow? = null

    private var customSelectToolsWindow: CustomSelectToolsWindow? = null

    private var penToolsGraphGuideWindow: NoteToolPenGraphGuideWindow? = null

    private var tapeStyleShowControlWindow: TapeStyleShowControlWindow? = null

    private var noteMaterialWindow: NoteMaterialWindow? = null

    private var recordListWindow: RecordListWindow? = null
    private var recordSettingWindow: NoteRecordItemSettingWindow? = null
    private var consoleSettingWindow: ConsoleSettingWindow? = null

    private var pagePreviewPaginationWindow: PagePreviewPaginationWindow? = null

    private var noteAIFragment: NoteAIFragment? = null

    private var noteSearchFrameLayout: NoteSearchFrameLayout? = null

    private lateinit var inputManager: InputManager

    private var generateLongPictureProgressDialog: GenerateLongPictureProgressDialog? = null

    private var customToolsDialog: CustomToolbarDialog? = null

    private var aiContainer: FrameLayout? = null

    private var thumbnailAnimator: ValueAnimator? = null
    private var snippetAnimator: ValueAnimator? = null
    private var addPageAnimator: ValueAnimator? = null
    private var materialAnimator: ValueAnimator? = null
    private var searchAnimator: ValueAnimator? = null
    private var aiAnimator: ValueAnimator? = null
    private var draftAnimator: Animator? = null
    private var snippetZoomAnimator: ValueAnimator? = null
    private var materialOrTemplateAnimator: ValueAnimator? = null
    private var webSidebarViewAnimator: ValueAnimator? = null
    private var isCreateSnippet = false
    private var isConsoleState = false

    private val mainHandler: Handler = Handler(Looper.getMainLooper())

    private val inputDeviceListener = object : InputManager.InputDeviceListener {

        override fun onInputDeviceAdded(deviceId: Int) {
            //埋点统计外接设备信息
            val inputDevice = inputManager.getInputDevice(deviceId)
            sendPadAndInputDevice(inputDevice)
            updateDoodleDeviceMode()
        }

        override fun onInputDeviceRemoved(deviceId: Int) {
            updateDoodleDeviceMode()
        }

        override fun onInputDeviceChanged(deviceId: Int) {
            updateDoodleDeviceMode()
        }

    }

    private var oldStickerCategoryId: Long? = null
    private val noteMaterialViewModel: NoteMaterialViewModel by activityViewModels()
    private val fontDownloadViewModel: FontDownloadViewModel by activityViewModels()
    private val templateViewModel: TemplateViewModel by activityViewModels()
    private val noteEditorBackStatusViewModel: NoteEditorBackStatusViewModel by activityViewModels()
    private val adsorptionEdgeViewModel: AdsorptionEdgeViewModel by viewModels()
    private val snippetViewModel: SnippetViewModel by viewModels()
    private val snippetManagerViewModel: SnippetManagerViewModel by activityViewModels()
    private val recordViewModel: RecordViewModel by viewModels()
    private val importFontViewModel: ImportFontViewModel by viewModels()
    private val draftPaperViewModel: DraftPaperViewModel by viewModels()
    private val globalDialogViewModel: GlobalDialogViewModel by viewModels()
    private val editorSwitchDocumentViewModel: EditorSwitchDocumentViewModel by viewModels {

        object : ViewModelProvider.Factory {

            @Suppress("UNCHECKED_CAST")
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                return EditorSwitchDocumentViewModel(currentDoc) as T
            }

        }
    }
    private val customToolsViewModel: CustomToolsViewModel by viewModels()

    private val pageViewPortAdjustmentViewModel: PageViewPortAdjustmentViewModel by navGraphViewModels(
        R.id.note_editor
    )
    private val thumbnailAndOutlineViewStatusViewModel: ThumbnailAndOutlineViewStatusViewModel by navGraphViewModels(
        R.id.note_editor
    )
    private val sideBarViewModel: SideBarViewModel by navGraphViewModels(
        R.id.note_editor
    )
    private val searchViewModel: SearchViewModel by viewModels()
    private val aiViewModel: AIViewModel by viewModels()
    private val pagePreviewViewModel: PagePreviewViewModel by viewModels()

    private var thumbnailListViewState = NoteViewModel.ThumbnailListViewState.HIDDEN
    private var snippetListViewState = SnippetViewModel.SnippetListViewState.HIDDEN

    private var sidebarAnimationTime = NO_ANIMATION_DURATION
    private var snippetAnimationTime = NO_ANIMATION_DURATION
    private var draftPaperAnimationTime = NO_ANIMATION_DURATION

    private val getFont =
        registerForActivityResult(ActivityResultContracts.OpenDocument()) { result ->
            if (result != null) {
                importFontFile(result)
            }
        }

    private var fontListWindow: FontListWindow? = null

    private var supportChangeRecognitionLanguage: List<String> = listOf(
        Locale.CHINESE.language, Locale.JAPANESE.language, Locale.KOREAN.language
    )
    private var outlineCreateDialog: OutlineCreateDialog? = null

    private var deleteThumbnailWithOutlineAlertDialog: AlertDialog? = null

    private val draftPaperSizeAndLocationHelper = DraftPaperSizeAndLocationHelper()

    private var draftPaperFragment: DraftPaperFragment? = null

    private val keyEventViewModel: KeyEventViewModel by activityViewModels()

    private val scaleLockedHiddenDelayTimer = Timer()
    private val undoAndRedoDelayTimer = Timer()
    private val onTransformChanged = object : OnTransformChangedListener {
        override fun onTransformChanged(
            matrix: Matrix,
            transformType: TransformType,
            currentScale: Float,
        ) {
            if (UserUsageConfig.isNoteScalePercentageShow) {
                if (transformType != TransformType.SCALE_TRANSLATE) {
                    noteScaleViewModel.renderRectInPdf =
                        doodleView.doodleTouchLayer.getOriginalRectInPdf()
                }
                if (transformType != TransformType.TRANSLATE || noteScaleViewModel.enableScale.value == false) {
                    binding.scaleLockAndRatioContainer.visibility = View.VISIBLE
                    noteScaleViewModel.changeCurrentScaleRatio(currentScale)
                    scaleLockedHiddenDelayTimer.cancel()
                    scaleLockedHiddenDelayTimer.start(delayedMs = SCALE_LOCKED_HIDDEN_TIME) {
                        if (isAdded && checkViewBindingValidity()) {
                            binding.scaleLockAndRatioContainer.visibility = View.INVISIBLE
                        }
                    }
                }
            }
        }

        override fun onTransformFinished(
            matrix: Matrix,
            transformType: TransformType,
            isReDraw: Boolean,
        ) {

        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleScope.launchWhenCreated {
            AiAccessManager.updateUserAiAccess()
            if (invalidCurrentDoc()) return@launchWhenCreated
            currentDoc.recordManager.loadRecordsInfo()
        }
        RecordViewModel.currentEditRecordTagUUID = null
        RecordViewModel.currentSelectedRecordTagUUID = null
        RecordViewModel.currentEditRecordUUID = null
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        if (noteScaleViewModel.enableScale.value == false && savedInstanceState != null) {
            noteScaleViewModel.changeEnableScale(true)
            ToastUtils.topCenter(
                requireContext(),
                R.string.note_tool_scale_change_prompt
            )
        }
        resetSideBarViewState()
        inputManager = requireContext().getSystemService(Context.INPUT_SERVICE) as InputManager
        inputManager.registerInputDeviceListener(inputDeviceListener, null)
        when (editorModeViewModel.editorMode.value) {
            InputMode.VIEW -> {
                EditEvent.sendEditShow("view")
            }

            else -> {
                EditEvent.sendEditShow("edit")
            }
        }
        TextRecognitionManager.addHighPriorityTaskFinishCallback(onFinishAction)
        TextRecognitionManager.setOnRecognitionTimeOut {
            if (isAdded) {
                ToastUtils.topCenter(
                    requireContext(),
                    AppUtils.getString(R.string.recognition_service_exception)
                )
            }
        }
        lifecycleScope.launch(Dispatchers.IO) {
            QuickRecognizer.init(requireContext())
        }
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun initBinding(inflater: LayoutInflater): FragmentNoteEditorBinding {
        val binding = FragmentNoteEditorBinding.inflate(inflater)
        aiContainer = if (DimensionUtil.isLikeLandAndOneThirdScreen(requireContext())) {
            binding.aiOneThirdContainer
        } else {
            binding.aiContainer
        }
        noteSearchFrameLayout = NoteSearchFrameLayout(requireContext()).apply {
            snippetScrollCallback = { scrollPosition ->
                searchViewModel.snippetScrollPosition = scrollPosition
            }
            currentDocScrollCallback = { scrollPosition ->
                searchViewModel.currentDocScrollPosition = scrollPosition
            }
            otherDocScrollCallback = { scrollPosition ->
                searchViewModel.otherDocScrollPosition = scrollPosition
            }
            id = R.id.note_search_frameLayout
        }
        val params = if (isLandOneThirdScreen()) {
            ConstraintLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                getNoteSearchFrameLayoutHeight()
            ).apply {
                topToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                startToStart = ConstraintLayout.LayoutParams.PARENT_ID
            }
        } else {
            ConstraintLayout.LayoutParams(
                getNoteSearchFrameLayoutWidth(),
                0
            ).apply {
                bottomToBottom = R.id.side_list_bottom_boundary
                startToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                topToBottom = R.id.side_list_top_boundary
            }
        }
        noteSearchFrameLayout?.layoutParams = params
        binding.rootConstraintLayout.addView(
            noteSearchFrameLayout,
            locateNoteSearchFrameLayoutIndexInParentViewGroup(binding)
        )
        return binding
    }

    private fun getNoteSearchFrameLayoutHeight() = resources.getDimensionPixelSize(R.dimen.dp_700)
    private fun getNoteSearchFrameLayoutWidth() = resources.getDimensionPixelSize(R.dimen.dp_479)

    private fun getWebSidebarViewWidth() =
        if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext()) || DimensionUtil.isLandAndOneThirdScreen(
                requireContext()
            )
        ) DimensionUtil.getScreenDimensions(requireContext()).widthPixels.toFloat() else resources.getDimensionPixelSize(
            R.dimen.dp_479
        ).toFloat()

    private fun locateNoteSearchFrameLayoutIndexInParentViewGroup(binding: FragmentNoteEditorBinding): Int {
        val indexOfMaterial = binding.rootConstraintLayout.indexOfChild(binding.noteMaterialView)
        val indexOfAddPage = binding.rootConstraintLayout.indexOfChild(binding.noteAddPageLayout)
        val indexOfWeb = binding.rootConstraintLayout.indexOfChild(binding.webSidebarView)
        if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext()) || DimensionUtil.isLandAndOneThirdScreen(
                requireContext()
            )
        ) {
            val indexOfOtherDocPreviewContainer =
                binding.rootConstraintLayout.indexOfChild(binding.otherDocPreviewContainer)
            return minOf(
                indexOfMaterial,
                indexOfAddPage,
                indexOfWeb,
                indexOfOtherDocPreviewContainer
            )

        } else {
            return minOf(indexOfMaterial, indexOfAddPage, indexOfWeb)
        }
    }

    override fun onRetrieveDoodleView(binding: FragmentNoteEditorBinding): DoodleView =
        binding.doodle

    private fun showPenToolsWindow() {
        val targetView = binding.blurView
        targetView.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (targetView.windowToken == null) return@post
                if (customSelectToolsWindow == null) {
                    customSelectToolsWindow = CustomSelectToolsWindow(
                        requireContext()
                    )
                }
                customSelectToolsWindow?.apply {
                    removeAllChildrenView()
                    customToolsWindowAddLayout(this, binding.doodle.inputMode)
                }
                val showGuide = Preferences.needShowFirstGraphDrawGuide
                var offsetX =
                    binding.noteMajorToolLayout.getToolViewOffsetInWindow(binding.doodle.inputMode)
                val offsetXOnScreen =
                    binding.noteMajorToolLayout.getToolViewOffsetOnScreen(binding.doodle.inputMode)
                customSelectToolsWindow?.show(targetView, binding.root, offsetX, offsetXOnScreen)
                if (showGuide) {
                    val anchorWindow = customSelectToolsWindow ?: return@post

                    if (isPortraitAndOneThirdScreenOrLikeXiaoMiPad5PortraitHalfScreen()) {
                        anchorWindow.updateScrollView()
                    }
                    penToolsGraphGuideWindow =
                        NoteToolPenGraphGuideWindow(
                            requireContext(),
                            isPortraitAndOneThirdScreenOrLikeXiaoMiPad5PortraitHalfScreen()
                        )
                    penToolsGraphGuideWindow?.contentView?.addOnAttachStateChangeListener(
                        object : View.OnAttachStateChangeListener {
                            override fun onViewAttachedToWindow(v: View) {
                                changeWindowAlpha(ALPHA_TRANSLUCENT, false)
                            }

                            override fun onViewDetachedFromWindow(v: View) {
                                changeWindowAlpha(ALPHA_TRANSPARENT, true)
                                penToolsGraphGuideWindow?.contentView?.removeOnAttachStateChangeListener(
                                    this
                                )
                            }
                        }
                    )
                    penToolsGraphGuideWindow?.setOnDismissListener {
                        Preferences.needShowFirstGraphDrawGuide = false
                    }
                    val needChangeLayout = DimensionUtil.isLandAndOneThirdScreen(
                        requireContext()
                    ) || DimensionUtil.isLandAndHalfScreen(requireContext())
                    val yOffset =
                        if (isPortraitAndOneThirdScreenOrLikeXiaoMiPad5PortraitHalfScreen()) requireContext().resources.getDimension(
                            R.dimen.dp_237
                        ).toInt() else requireContext().resources.getDimension(R.dimen.dp_433)
                            .toInt()
                    penToolsGraphGuideWindow?.showGuideWindow(
                        targetView, needChangeLayout, offsetX + anchorWindow.width / 2,
                        yOffset
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 展示套索语言弹窗
     */
    private fun showLassoLanguageWindow() {
        if (customSelectToolsWindow == null) {
            customSelectToolsWindow = CustomSelectToolsWindow(requireContext())
        }
        customSelectToolsWindow?.apply {
            removeAllChildrenView()
            customToolsWindowAddLayout(this, binding.doodle.inputMode)
        }
        val targetView = binding.blurView
        targetView.post {
            try {
                if (activity?.isFinishing == true || !isAdded) return@post
                val offsetX =
                    binding.noteMajorToolLayout.getToolViewOffsetInWindow(binding.doodle.inputMode)
                val offsetXOnScreen =
                    binding.noteMajorToolLayout.getToolViewOffsetOnScreen(binding.doodle.inputMode)
                customSelectToolsWindow?.show(targetView, binding.root, offsetX, offsetXOnScreen)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun showReadingStyleSelectWindow() {
        if (customSelectToolsWindow == null) {
            customSelectToolsWindow = CustomSelectToolsWindow(requireContext())
        }

        customSelectToolsWindow?.apply {
            removeAllChildrenView()
            customToolsWindowAddLayout(this, binding.doodle.inputMode)
        }
        val targetView = binding.blurView
        targetView.post {
            if (activity?.isFinishing == true || !isAdded) return@post
            val offsetX =
                binding.noteMajorToolLayout.getToolViewOffsetInWindow(binding.doodle.inputMode)
            val offsetXOnScreen =
                binding.noteMajorToolLayout.getToolViewOffsetOnScreen(binding.doodle.inputMode)
            customSelectToolsWindow?.show(targetView, binding.root, offsetX, offsetXOnScreen)
        }
    }

    private fun showNoteSnippetGuideWindow(view: View) {
        view.post {
            if (!isAdded) return@post
            if (noteSnippetGuideWindow == null) {
                noteSnippetGuideWindow = NoteSnippetGuideWindow(requireContext())
            }
            noteSnippetGuideWindow?.show(view)
        }
    }

    private fun showGraphToolsWindow() {
        val targetView = binding.blurView
        targetView.post {
            if (activity?.isFinishing == true || !isAdded) return@post
            if (targetView.windowToken == null) return@post
            if (noteViewModel.isShowMoreWindow.value == true) return@post
            if (customSelectToolsWindow == null) {
                customSelectToolsWindow = CustomSelectToolsWindow(requireContext())
            }
            customSelectToolsWindow?.apply {
                removeAllChildrenView()
                customToolsWindowAddLayout(this, binding.doodle.inputMode)
            }
            val offsetX =
                binding.noteMajorToolLayout.getToolViewOffsetInWindow(binding.doodle.inputMode)
            val offsetXOnScreen =
                binding.noteMajorToolLayout.getToolViewOffsetOnScreen(binding.doodle.inputMode)
            customSelectToolsWindow?.show(targetView, binding.root, offsetX, offsetXOnScreen)
        }
    }

    private fun showGraffitiToolsWindow() {
        val targetView = binding.blurView
        targetView.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (targetView.windowToken == null) return@post
                if (customSelectToolsWindow == null) {
                    customSelectToolsWindow = CustomSelectToolsWindow(requireContext())
                }

                customSelectToolsWindow?.apply {
                    removeAllChildrenView()
                    customToolsWindowAddLayout(this, InputMode.GRAFFITI)
                }
                val offsetX =
                    binding.noteMajorToolLayout.getToolViewOffsetInWindow(InputMode.GRAFFITI)
                val offsetXOnScreen =
                    binding.noteMajorToolLayout.getToolViewOffsetOnScreen(InputMode.GRAFFITI)
                customSelectToolsWindow?.show(targetView, binding.root, offsetX, offsetXOnScreen)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun showHighlighterToolsWindow() {
        val targetView = binding.blurView
        targetView.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (targetView.windowToken == null) return@post
                if (customSelectToolsWindow == null) {
                    customSelectToolsWindow =
                        CustomSelectToolsWindow(requireContext())
                }
                customSelectToolsWindow?.apply {
                    removeAllChildrenView()
                    customToolsWindowAddLayout(this, binding.doodle.inputMode)
                }
                val offsetX =
                    binding.noteMajorToolLayout.getToolViewOffsetInWindow(binding.doodle.inputMode)
                val offsetXOnScreen =
                    binding.noteMajorToolLayout.getToolViewOffsetOnScreen(binding.doodle.inputMode)
                customSelectToolsWindow?.show(targetView, binding.root, offsetX, offsetXOnScreen)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun showEraseToolsWindow() {
        val targetView = binding.blurView
        targetView.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (targetView.windowToken == null) return@post
                if (customSelectToolsWindow == null && checkViewBindingValidity()) {
                    customSelectToolsWindow = CustomSelectToolsWindow(requireContext())
                }
                customSelectToolsWindow?.apply {
                    removeAllChildrenView()
                    customToolsWindowAddLayout(this, binding.doodle.inputMode)
                }

                customSelectToolsWindow?.contentView?.measure(
                    View.MeasureSpec.UNSPECIFIED,
                    View.MeasureSpec.UNSPECIFIED
                )
                val offsetX =
                    binding.noteMajorToolLayout.getToolViewOffsetInWindow(binding.doodle.inputMode)
                val offsetXOnScreen =
                    binding.noteMajorToolLayout.getToolViewOffsetOnScreen(binding.doodle.inputMode)
                customSelectToolsWindow?.show(targetView, binding.root, offsetX, offsetXOnScreen)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun dismissEraseToolsWindow() {
        if (customSelectToolsWindow != null && customSelectToolsWindow!!.isShowing) {
            customSelectToolsWindow!!.dismiss()
        }
    }

    fun showHideDoodleToolsWindow() {
        if (customSelectToolsWindow == null) {
            customSelectToolsWindow = CustomSelectToolsWindow(
                requireContext(),
            )
        }
        val customTools = customToolsViewModel.targetCustomTools.value
        if (customTools != null) {
            customSelectToolsWindow?.apply {
                removeAllChildrenView()
                binding.noteMajorToolLayout.showToolsTouchLayout.post {
                    val snippetViewIsShow =
                        snippetViewModel.snippetListViewState.value ?: return@post
                    val materialViewIsShow =
                        sideBarViewModel.currentSidebarState.value ?: return@post
                    showCustomHideToolsWindow(
                        binding.noteMajorToolLayout.showToolsTouchLayout,
                        binding.root,
                        binding.doodle.inputMode,
                        snippetViewIsShow == SnippetViewModel.SnippetListViewState.SHOWN,
                        materialViewIsShow == Sidebar.MATERIAL,
                        customToolsViewModel.getHideDoodleTools(customTools),
                        doOnShowCustomToolsItemClick = {
                            dismiss()
                            customToolsViewModel.showCustomToolDialog()
                        },
                        doOnHideToolItemClick = { customTool ->
                            if (customTool.inputMode != null) {
                                customTool.inputMode?.let {
                                    if (customTool.isDrawMode(it)) {
                                        changeToolbar(UserUsageConfig.lastSelectedPenMode)
                                    } else if (it == InputMode.IMAGE) {
                                        if (editorModeViewModel.editorMode.value == InputMode.IMAGE) {
                                            editorViewModel.selectedInsertableBitmap = null
                                            checkAndPickImage()
                                        } else {
                                            checkImagePermission { state ->
                                                if (state == PermissionRequester.PermissionState.PERMISSION_GRANTED) {
                                                    imageFetchViewModel.loadSidebarPictures()
                                                }
                                            }
                                            changeToolbar(InputMode.IMAGE)
                                        }
                                    } else {
                                        changeToolbar(it)
                                    }
                                }
                            } else {
                                if (customTool.isToolSnippet()) {
                                    if (isLandOneThirdScreen()) {
                                        ToastUtils.topCenter(
                                            requireContext(),
                                            AppUtils.getString(R.string.custom_tool_feature_not_support)
                                        )
                                        return@showCustomHideToolsWindow
                                    }
                                    snippetAnimationTime = ANIMATION_DURATION_TIME
                                    if (thumbnailListViewState.isStill() && snippetListViewState.isStill()) {
                                        when (snippetListViewState) {
                                            SnippetViewModel.SnippetListViewState.HIDDEN -> {
                                                SnippetEvent.sendSnippetLayoutShow()
                                                noteViewModel.changeThumbnailListViewState(
                                                    NoteViewModel.ThumbnailListViewState.HIDDEN
                                                )
                                                snippetViewModel.changeSnippetListViewState(
                                                    SnippetViewModel.SnippetListViewState.SHOWN
                                                )
                                            }

                                            SnippetViewModel.SnippetListViewState.SHOWN, SnippetViewModel.SnippetListViewState.EXPANDED -> {
                                                SnippetEvent.sendSnippetLayoutHide()
                                                snippetViewModel.changeSnippetListViewState(
                                                    SnippetViewModel.SnippetListViewState.HIDDEN
                                                )
                                            }

                                            else -> {}
                                        }
                                    }
                                }
                                if (customTool.isToolSticker()) {
                                    EditEvent.sendEditMaterialClick()
                                    if (sideBarViewModel.currentSidebarState.value == Sidebar.MATERIAL) {
                                        changeCurrentSidebarStatus(Sidebar.NONE)
                                    } else {
                                        changeCurrentSidebarStatus(Sidebar.MATERIAL)
                                    }
                                }
                            }
                        },
                        doOnNextIconClick = { customTool ->
                            customToolsWindowAddLayout(this, binding.doodle.inputMode)
                        },
                        doOnDismiss = {
                            clearCache()
                            customToolsViewModel.hideCustomSelectToolWindow()
                        }
                    )
                }
            }
        }
    }

    private fun updateMajorToolLayout(inputMode: InputMode) {
        val customTools = customToolsViewModel.targetCustomTools.value ?: return
        val snippetViewIsShow =
            snippetViewModel.snippetListViewState.value ?: return
        val materialViewIsShow =
            sideBarViewModel.currentSidebarState.value ?: return
        binding.noteMajorToolLayout.updateToolsView(
            inputMode,
            snippetViewIsShow == SnippetViewModel.SnippetListViewState.SHOWN,
            materialViewIsShow == Sidebar.MATERIAL,
            customToolsViewModel.getDisplayDoodleTools(
                customTools
            ),
            customToolsViewModel.getHideDoodleTools(
                customTools
            )
        )
    }

    private fun updateHideToolsShowIcon(inputMode: InputMode, isShowHideToolsIcon: Boolean) {
        val customTools = customToolsViewModel.targetCustomTools.value ?: return
        val snippetViewIsShow =
            snippetViewModel.snippetListViewState.value ?: return
        val materialViewIsShow =
            sideBarViewModel.currentSidebarState.value ?: return
        binding.noteMajorToolLayout.updateShowTools(
            inputMode,
            customToolsViewModel.getDisplayDoodleTools(customTools)
        )
        binding.noteMajorToolLayout.updateHideToolsShowIcon(
            inputMode,
            customToolsViewModel.getHideDoodleTools(
                customTools
            ),
            snippetViewIsShow == SnippetViewModel.SnippetListViewState.SHOWN,
            materialViewIsShow == Sidebar.MATERIAL,
            isShowHideToolsIcon
        )
    }

    private fun customToolsWindowAddLayout(
        customSelectToolsWindow: CustomSelectToolsWindow,
        inputMode: InputMode?,
    ) {
        customSelectToolsWindow.apply {
            when (inputMode) {
                InputMode.DRAW, InputMode.PEN, InputMode.PAINTBRUSH -> {
                    val listener = object : IPatternSwitchListener {
                        override fun onGraphDrawSwitch(isChecked: Boolean) {
                            toolAttributesViewModel.isUsePenPatternRecognition = isChecked
                        }

                        override fun onStraightLineSwitch(isChecked: Boolean) {
                            toolAttributesViewModel.isUsePenStraightLine = isChecked
                        }

                        override fun onSwitchMode(mode: InputMode) {
                            editorModeViewModel.switchMode(mode)
                            EditEvent.sendEditDrawPaintType(mode.toString())
                        }
                    }
                    addPenToolsLayout(
                        inputMode,
                        penToolWindowIsShowOtherSettingsWindow,
                        onPenHandwritingParamsChanged = { params ->
                            toolAttributesViewModel.setPenHandwritingParams(params)
                        },
                        onBrushPaintHandwritingParamsChanged = { params ->
                            toolAttributesViewModel.setBrushPaintHandwritingParams(params)
                        },
                        otherSettingsWindowListener = { isShowOtherSettingsWindow ->
                            penToolWindowIsShowOtherSettingsWindow = isShowOtherSettingsWindow
                        },
                        listener = listener,
                        doOnDismiss = {
                            penToolWindowIsShowOtherSettingsWindow = false
                            noteViewModel.isShowRecognizeWindow.postValue(false)
                            Preferences.needShowFirstGraphDrawGuide = false
                        }
                    )
                }

                InputMode.ERASER -> {
                    addEraseToolsLayout(
                        isFromHandWriteSnippet = false,
                        eraseTools = listOf(
                            EraseToolItem(EraseToolType.ERASE_PEN, UserUsageConfig.canErasePen),
                            EraseToolItem(
                                EraseToolType.ERASE_HIGHLIGHT,
                                UserUsageConfig.canEraseHighlight
                            ),
                            EraseToolItem(
                                EraseToolType.ERASE_GRAFFITI,
                                UserUsageConfig.canEraseGraffiti
                            ),
                            EraseToolItem(
                                EraseToolType.ERASE_TAPE,
                                UserUsageConfig.canEraseTape
                            ),
                            EraseToolItem(
                                EraseToolType.AUTO_ERASE,
                                UserUsageConfig.isUseEraseAuto
                            )
                        ),
                        onSwitchChangeCallback = { eraseToolItem, isLastSwitch ->
                            if (!isLastSwitch) {
                                when (eraseToolItem.eraseToolType) {
                                    EraseToolType.ERASE_PEN -> {
                                        toolAttributesViewModel.setEraserSupportTypePen(
                                            eraseToolItem.isCheck
                                        )
                                    }

                                    EraseToolType.ERASE_HIGHLIGHT -> {
                                        UserUsageConfig.canEraseHighlight = eraseToolItem.isCheck
                                    }

                                    EraseToolType.ERASE_GRAFFITI -> {
                                        UserUsageConfig.canEraseGraffiti = eraseToolItem.isCheck
                                    }

                                    EraseToolType.ERASE_TAPE -> {
                                        UserUsageConfig.canEraseTape = eraseToolItem.isCheck
                                    }

                                    EraseToolType.AUTO_ERASE -> {
                                        UserUsageConfig.isUseEraseAuto = eraseToolItem.isCheck
                                    }
                                }
                                setSupportEraseType()
                            } else {
                                ToastUtils.topCenter(
                                    requireContext(),
                                    R.string.erase_style_no_close_all
                                )
                            }
                        },
                        doOnDismiss = {
                            noteViewModel.isShowEraseToolWindow.postValue(false)
                        }
                    )
                }

                InputMode.LASSO -> {
                    addNoteLassoLanguageLayout(
                        doOnDismiss = {
                            noteViewModel.isShowLassoLanguageWindow.postValue(false)
                        },
                        onLanguageSelect = { supportLanguage ->
                            TextRecognitionManager.changeRecognitionLanguage(supportLanguage.id)
                        }
                    )
                }

                InputMode.HIGHLIGHTER -> {
                    addHighlighterToolsLayout(
                        object : IHighlighterSwitchListener {
                            override fun onStraightLineSwitch(isChecked: Boolean) {
                                toolAttributesViewModel.isUseHighlighterStraightLine = isChecked
                            }
                        },
                        dismissListener = {
                            noteViewModel.isShowHighlighterWindow.postValue(false)
                        }
                    )
                }

                InputMode.GRAFFITI, InputMode.OUTLINEPEN, InputMode.LINEDRAW -> {
                    addGraffitiToolsLayout(
                        object : IGraffitiSwitchListener {
                            override fun onStraightLineSwitch(isChecked: Boolean) {
                                toolAttributesViewModel.isUseGraffitiStraightLine = isChecked
                            }
                        },
                        doOnDismiss = {
                            noteViewModel.isShowGraffitiWindow.postValue(false)
                        })
                }

                InputMode.VIEW -> {
                    toolAttributesViewModel.currentReadingStyle.value?.let { readingStyle ->
                        toolAttributesViewModel.changeCurrentReadingStyle(readingStyle)
                        addNoteReadingStyleSelectLayout(
                            doOnDismiss = {
                                noteViewModel.isShowReadingStyleSelectWindow.postValue(false)
                            },
                            currentReadingStyle = readingStyle,
                            onReadingStyleSelectCallback = { readingStyle ->
                                toolAttributesViewModel.changeCurrentReadingStyle(readingStyle)
                            }
                        )
                    }
                }

                InputMode.GRAPH -> {
                    addGraphToolsLayout(
                        onGraphClickListener = {
                            customSelectToolsWindow.dismiss()
                            checkViewBindingValidity()
                            binding.doodle.insertGraph(it)

                        },
                        doOnDismiss = {
                            noteViewModel.changeGraphWindowStatues(false)
                        }
                    )
                }

                else -> {

                }
            }
        }
    }

    private fun storeNoteTitle(it: MoreToolWindow) {
        val newTitle = it.binding.input.text
        noteViewModel.currentDoc = it.document
        if (it.document.title != newTitle && <EMAIL>(
                newTitle,
                it.document
            )
        ) {
            <EMAIL> = newTitle
            it.document.title = newTitle
            it.document.updateAndStoreModifiedTime()
            noteViewModel.editDocument(it.document)
            NoteRepository.runInNoteOperationScopeAsync {
                saveDocumentInfo(it.document)
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun showMoreToolWindow() {
        binding.noteMajorToolLayout.more.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (binding.noteMajorToolLayout.more.windowToken == null) return@post
                if (moreToolWindow == null) {
                    moreToolWindow = MoreToolWindow(
                        requireContext(),
                        currentDoc,
                        if (isPortraitAndOneThirdScreen()) {
                            resources.getDimensionPixelSize(R.dimen.dp_0)
                        } else {
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        }
                    ).apply {
                        setNoteTitleChangeListener {
                            storeNoteTitle(this)
                        }
                        setOnMenuClickListener {
                            when (it) {
                                MoreToolWindow.MenuType.EXPORT -> {
                                    dismiss()
                                    EditEvent.sendEditExportDialog()
                                    exportViewModel.resetExportDocument(currentDoc)
                                    exportViewModel.showExportNoteUI()
                                }

                                MoreToolWindow.MenuType.GUIDE_LINE -> {
                                    refreshGuideLineConfig()
                                    SettingEvent.sendReferenceLinesClick(GuideLineConfig.enableGuideLine)
                                }

                                MoreToolWindow.MenuType.SHARE -> {
                                    dismiss()
                                    shareImgViewModel.shareImgSelectedIndexList.clear()
                                    EditEvent.sendShareClick()
                                    shareImgViewModel.showShareImgUI()
                                }

                                MoreToolWindow.MenuType.BLANK -> {
                                    dismiss()
                                    PageViewportResizingEvent.sendPageViewportResizingEntranceClickEvent()
                                    jumpToPageSizingFragment()
                                }

                                MoreToolWindow.MenuType.SCALE -> {
                                    SettingEvent.sendDoodleQuickScaleClickEvent(UserUsageConfig.isOpenDoodleQuickScale)

                                }

                                MoreToolWindow.MenuType.DRAFT_PAPER -> {
                                    resetDraftSizeAndLocation()
                                    if (UserUsageConfig.isShowDraftPaper) {
                                        dismiss()
                                        draftPaperViewModel.switchDraftPaperVisibility(true)
                                        DraftPaperEvent.sendDraftPaperSwitchClick(true)

                                    } else {
                                        draftPaperViewModel.switchDraftPaperVisibility(false)
                                        DraftPaperEvent.sendDraftPaperSwitchClick(false)
                                        removeAndHideDraftPaper()
                                    }
                                }

                                MoreToolWindow.MenuType.BLUETOOTH_PEN -> {
                                    updateDoodleDeviceMode()
                                }

                                MoreToolWindow.MenuType.SCALE_LOCKED -> {
                                    if (!UserUsageConfig.isNoteScalePercentageShow) {
                                        scaleLockedHiddenDelayTimer.cancel()
                                        if (isAdded && checkViewBindingValidity()) {
                                            <EMAIL> =
                                                View.INVISIBLE
                                        }
                                    }
                                    noteScaleViewModel.changeEnableScale(true)
                                }

                                MoreToolWindow.MenuType.CONSOLE -> {
                                    ConsoleEvent.sendConsoleMoreToolClick()
                                    dismiss()
                                    consoleViewModel.changeIsShowConsoleSettingWindow(true)
                                }

                                MoreToolWindow.MenuType.CUSTOM_TOOLS -> {
                                    dismiss()
                                    customToolsViewModel.showCustomToolDialog()
                                }
                            }
                        }
                        setOnDismissListener {
                            storeNoteTitle(this)
                            draftPaperMorePopupGuideWindow?.let { checkChildPopupWindow(it) }
                            noteViewModel.isShowMoreWindow.value = false
                            moreToolWindow = null
                        }
                    }
                } else {
                    moreToolWindow?.changeInputShow(false)
                }
                moreToolWindow?.let {
                    showMoreToolWindowAtTopRight(it)
                    checkShowDraftPaperMorePopupGuideWindow()
                    it.setTouchInterceptor { v, event ->
                        val isOuterClick =
                            moreToolWindow!!.contentView.outsideArea(event)
                        if (isOuterClick) {
                            moreToolWindow!!.dismiss()
                            if (noteViewModel.isShowGraphWindow.value == true) {
                                noteViewModel.changeGraphWindowStatues(true)
                            }
                            return@setTouchInterceptor true
                        } else {
                            noteViewModel.changeGraphWindowStatues(false)
                        }
                        false
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun showConsoleGuideWindow() {
        binding.console.post {
            if (!isAdded) return@post
            if (consoleGuideWindow != null && consoleGuideWindow!!.isShowing) {
                consoleGuideWindow!!.setOnDismissListener { }
                consoleGuideWindow!!.dismiss()
            }
            consoleGuideWindow = ConsoleGuideWindow(requireContext(), binding.consoleIcon)
            consoleGuideWindow?.setOnDismissListener {
                noteViewModel.isShowConsoleGuideWindow.value = false
                UserUsageConfig.isNeedShowConsoleGuide = false
            }
            consoleGuideWindow?.isClippingEnabled = false
            consoleGuideWindow?.showAtLocation(
                binding.console,
                Gravity.NO_GRAVITY,
                0,
                0
            )
            ConsoleEvent.sendConsoleGuideWindowShow()
        }
    }

    private fun showDraftPaperMorePopupGuideWindow() {
        if (draftPaperMorePopupGuideWindow != null && draftPaperMorePopupGuideWindow!!.isShowing) {
            draftPaperMorePopupGuideWindow!!.setOnDismissListener { }
            draftPaperMorePopupGuideWindow!!.dismiss()
        }
        val anchorWindow = moreToolWindow ?: return

        if (isPortraitAndOneThirdScreen()) {
            anchorWindow.updateScrollView()
        }
        draftPaperMorePopupGuideWindow =
            DraftPaperMorePopupGuideWindow(requireActivity(), isLandOneThirdScreen())
        draftPaperMorePopupGuideWindow?.contentView?.addOnAttachStateChangeListener(
            object : View.OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(v: View) {
                    changeWindowAlpha(ALPHA_TRANSLUCENT, false)
                }

                override fun onViewDetachedFromWindow(v: View) {
                    changeWindowAlpha(ALPHA_TRANSPARENT, true)
                    anchorWindow.updateDraftPaperViewVisible()
                    draftPaperMorePopupGuideWindow?.contentView?.removeOnAttachStateChangeListener(
                        this
                    )
                }
            }
        )
        draftPaperMorePopupGuideWindow?.setOnDismissListener {
            UserUsageConfig.isNeedShowDraftPaperMorePopupGuide = false
        }
        val xOffset: Int =
            if (KiloApp.isLayoutRtl) anchorWindow.width else binding.blurView.width - anchorWindow.width
        val yOffset: Int = binding.blurView.height + anchorWindow.getDraftPaperInletLocation()[1] -
                (draftPaperMorePopupGuideWindow!!.getTitleLines() - DraftPaperMorePopupGuideWindow.DEFAULT_TIPS_LINE) * resources.getDimensionPixelSize(
            if (isLandOneThirdScreen()) R.dimen.dp_0 else R.dimen.dp_23
        )
        draftPaperMorePopupGuideWindow!!.show(
            binding.blurView,
            xOffset,
            yOffset
        )
    }

    private fun showRecordListWindow() {
        binding.recordControlView.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (binding.recordControlView.windowToken == null) return@post
                recordListWindow = RecordListWindow(requireContext()).apply {
                    setCurrentNoteTitle(noteViewModel.currentDoc?.title)
                    playListener = { noteRecordItem, recordTag ->
                        val isPlaying: Boolean = recordViewModel.isPlaying.value ?: false
                        if (recordTag != null) {
                            RecordEvent.sendRecordTagPlayClick()
                            if (isPlaying && recordViewModel.currentPlayingRecordTagId.value == recordTag.uuid) {
                                recordViewModel.pausePlay()
                            } else {
                                recordViewModel.stopPlay()
                                recordViewModel.preparePlay(recordTag.uuid)
                                recordViewModel.startPlay(mainHandler)
                            }
                        } else {
                            RecordEvent.sendRecordListPlayClick()
                            val playingRecordId = recordViewModel.currentPlayingRecordId.value
                            if (isPlaying && playingRecordId != null && playingRecordId == noteRecordItem.record.uuid) {
                                recordViewModel.pausePlay()
                            } else {
                                recordViewModel.stopPlay()
                                recordViewModel.preparePlay(noteRecordItem.record.uuid)
                                recordViewModel.startPlay(mainHandler)
                            }
                        }
                    }
                    recordPlayAllClickListener = {
                        RecordEvent.sendRecordListPlayAllClick()
                        recordViewModel.resetPlayedDuration()
                        recordViewModel.stopPlay()
                        recordViewModel.preparePlay(RecordsInfo.ALL_RECORDS_UUID)
                        recordViewModel.startPlay(mainHandler)
                    }
                    tagLocateListener = { noteRecordItem, recordTag ->
                        RecordEvent.sendRecordAddToPageClick("label")
                        showRecordView(
                            noteRecordItem.record.uuid,
                            recordTag.uuid
                        )
                    }
                    tagDeleteListener = { noteRecordItem, recordTag ->
                        showTagDeleteDialog(noteRecordItem, recordTag)
                    }

                    setOnDismissListener {
                        RecordViewModel.currentSelectedRecordUUID = null
                        RecordViewModel.currentSelectedRecordTagUUID = null
                        RecordViewModel.currentShowRecordSettingPosition = null
                        recordViewModel.showRecordListScrollPosition = null
                        recordViewModel.isShowRecordWindow.value = false
                    }
                    noteRecordModifyNameAction = { recordUUID: UUID, name: String ->
                        when (recordViewModel.verifyRecordOrTagName(name, recordUUID)) {
                            TitleErrorType.BLANK -> {
                            }

                            TitleErrorType.REPEAT -> {
                                AlertDialog.Builder()
                                    .setTitle(getString(R.string.document_title_repeat))
                                    .setPositiveBtn(getString(R.string.ok)) {
                                    }
                                    .build()
                                    .show(parentFragmentManager, null)
                            }

                            TitleErrorType.NONE -> {
                                recordViewModel.setRecordName(recordUUID, name)
                                recordListWindow?.refreshRecordListByRecordItemUUID(recordUUID)
                            }

                            else -> {}
                        }
                    }
                    noteRecordTagModifyNameAction =
                        { recordUUID, recordTagUUID: UUID, name: String ->
                            when (recordViewModel.verifyRecordOrTagName(
                                name,
                                recordUUID,
                                recordTagUUID
                            )) {
                                TitleErrorType.BLANK -> {
                                }

                                TitleErrorType.REPEAT -> {
                                    AlertDialog.Builder()
                                        .setTitle(getString(R.string.document_title_repeat))
                                        .setPositiveBtn(getString(R.string.ok)) {
                                        }
                                        .build()
                                        .show(parentFragmentManager, null)
                                }

                                TitleErrorType.NONE -> {
                                    recordViewModel.setTagName(recordTagUUID, name)
                                    recordListWindow?.refreshRecordListByRecordItemUUID(recordUUID)
                                }

                                else -> {}
                            }
                        }
                    noteRecordSettingClickAction = { noteRecord, view ->
                        showNoteRecordSettingWindow(this, noteRecord, view)
                    }
                }
                recordListWindow?.initRecordList(recordViewModel.currentRecordItemList)
                PopupWindowCompat.setWindowLayoutType(
                    recordListWindow!!,
                    WindowManager.LayoutParams.TYPE_APPLICATION_SUB_PANEL + 500
                )
                val scrollPosition = recordViewModel.showRecordListScrollPosition ?: Pair(0, 0)
                recordListWindow?.showAsBubble(binding.recordControlView.getShowListButtonView())
                recordListWindow?.contentView?.post {
                    recordListWindow?.scrollToPositionWithOffset(
                        scrollPosition,
                        scrollPositionCallback = { scrollPosition ->
                            recordViewModel.showRecordListScrollPosition = scrollPosition
                        },
                        scrollEnd = {
                            val settingPosition = RecordViewModel.currentShowRecordSettingPosition
                            settingPosition?.let {
                                val anchor =
                                    recordListWindow?.getSettingWindowShowAnchor(settingPosition)
                                val noteRecordItem =
                                    recordListWindow?.getNoteRecordItemByPosition(settingPosition)
                                if (isAdded && anchor != null && noteRecordItem != null) {
                                    recordListWindow?.let {
                                        showNoteRecordSettingWindow(it, noteRecordItem, anchor)
                                    }
                                }
                            }
                        })
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun showNoteRecordSettingWindow(
        parentWindow: RecordListWindow,
        noteRecord: NoteRecordItem,
        anchor: View,
    ) {
        recordSettingWindow =
            NoteRecordItemSettingWindow(requireContext(), binding.recordControlView)
        recordSettingWindow?.editNameAction = {
            recordSettingWindow?.dismiss()
            parentWindow.notifyItemToEditStyle(noteRecord)
        }
        recordSettingWindow?.addToPageAction = {
            RecordEvent.sendRecordAddToPageClick("audio")
            showRecordView(noteRecord.record.uuid, null)
        }
        recordSettingWindow?.setOnDismissListener {
            RecordViewModel.currentShowRecordSettingPosition = null
        }
        recordSettingWindow?.delAction = {
            recordSettingWindow?.dismiss()
            val alertDialog = AlertDialog.Builder()
                .setMsg(resources.getString(R.string.note_record_delete_msg))
                .setTitle(resources.getString(R.string.note_record_delete_title))
                .setPositiveBtn(resources.getString(R.string.cancel)) {

                }
                .setPositiveBtnColor(
                    resources.getColor(
                        R.color.text_secondary,
                        null
                    )
                )
                .setNegativeBtn(resources.getString(R.string.confirm)) {
                    parentWindow.deleteRecord(noteRecord)
                    recordViewModel.deleteRecord(noteRecord.record.uuid, mainHandler)
                }
                .setIsCancelable(false)
                .setNegativeBtnColor(
                    resources.getColor(
                        R.color.note_record_delete_confirm_color,
                        null
                    )
                )
                .build()
            alertDialog.show(parentFragmentManager, null)
        }
        recordSettingWindow?.show(anchor, binding.root)
    }

    private fun showRecordView(noteRecordUUID: UUID, recordTagId: UUID?) {
        var pageThumbnailListIsShow = false
        var pageSnippetListIsShow = false
        var draftPaperIsShow = false
        binding.highLightView.mClipRect = doodleView.clipRectOnScreen
        binding.highLightView.visibility = View.VISIBLE
        binding.recordControlView.recordEditState = true
        if (noteViewModel.thumbnailListViewState.value?.isStillShown() == true) {
            pageThumbnailListIsShow = true
            noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
        }

        if (snippetViewModel.snippetListViewState.value?.isStillShown() == true) {
            pageSnippetListIsShow = true
            snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
        }

        if (draftPaperViewModel.isShowDraftPaper.value == true) {
            draftPaperIsShow = true
            draftPaperViewModel.switchDraftPaperVisibility(false)
        }
        binding.console.ignoreTouchEvent = true
        doodleView.doodleEditLayer.showRecordView(noteRecordUUID, recordTagId)
        doodleView.doodleEditLayer.setOnRecordViewDismissListener(object : OnRecordViewDismiss {
            override fun onDismiss() {
                binding.console.ignoreTouchEvent = false
                binding.highLightView.visibility = View.GONE
                binding.recordControlView.recordEditState = false
                if (pageThumbnailListIsShow) {
                    noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.SHOWN)
                    pageThumbnailListIsShow = false
                }
                if (pageSnippetListIsShow) {
                    snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.SHOWN)
                    pageSnippetListIsShow = false
                }
                if (draftPaperIsShow) {
                    draftPaperViewModel.switchDraftPaperVisibility(true)
                    draftPaperIsShow = false
                }
            }
        })
        recordSettingWindow?.dismiss()
        recordListWindow?.dismiss()
        recordViewModel.isShowRecordWindow.value = false
    }

    private fun adaptToolBar(view: View, margin: Int, edge: AdsorptionEdgeLayout.EDGE) {
        if (adsorptionEdgeViewModel.toolBarState.value == AdsorptionEdgeViewModel.ToolBarState.SHOW) {
            when (edge) {
                AdsorptionEdgeLayout.EDGE.LEFT, AdsorptionEdgeLayout.EDGE.RIGHT -> {
                    (binding.sideListTopBoundary.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.topToBottom = R.id.record_control_view

                        layoutParams.topToTop = ConstraintLayout.LayoutParams.UNSET
                        layoutParams.topMargin = 0

                        binding.sideListTopBoundary.layoutParams = layoutParams
                    }

                    (binding.sideListBottomBoundary.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        layoutParams.bottomMargin = 0
                        binding.sideListBottomBoundary.layoutParams = layoutParams
                    }


                    (binding.pageIndicator.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        layoutParams.bottomToTop = ConstraintLayout.LayoutParams.UNSET

                        binding.pageIndicator.layoutParams = layoutParams
                    }
                }

                AdsorptionEdgeLayout.EDGE.TOP -> {
                    (binding.sideListTopBoundary.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.topToBottom = ConstraintLayout.LayoutParams.UNSET

                        layoutParams.topToTop = view.id
                        layoutParams.topMargin = margin

                        binding.sideListTopBoundary.layoutParams = layoutParams
                    }

                    (binding.sideListBottomBoundary.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        layoutParams.bottomMargin = 0
                        binding.sideListBottomBoundary.layoutParams = layoutParams
                    }

                    (binding.pageIndicator.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        layoutParams.bottomToTop = ConstraintLayout.LayoutParams.UNSET

                        binding.pageIndicator.layoutParams = layoutParams
                    }
                }

                AdsorptionEdgeLayout.EDGE.BOTTOM -> {
                    (binding.sideListTopBoundary.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.topToBottom = R.id.record_control_view

                        layoutParams.topToTop = ConstraintLayout.LayoutParams.UNSET
                        layoutParams.topMargin = 0

                        binding.sideListTopBoundary.layoutParams = layoutParams
                    }

                    (binding.sideListBottomBoundary.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.bottomToBottom = view.id
                        layoutParams.bottomMargin = margin
                        binding.sideListBottomBoundary.layoutParams = layoutParams
                    }

                    (binding.pageIndicator.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                        layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
                        layoutParams.bottomToTop = view.id

                        binding.pageIndicator.layoutParams = layoutParams
                    }
                }
            }
        } else {
            (binding.sideListTopBoundary.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.topToBottom = R.id.record_control_view

                layoutParams.topToTop = ConstraintLayout.LayoutParams.UNSET
                layoutParams.topMargin = 0

                binding.sideListTopBoundary.layoutParams = layoutParams
            }

            (binding.sideListBottomBoundary.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                layoutParams.bottomMargin = 0
                binding.sideListBottomBoundary.layoutParams = layoutParams
            }

            (binding.pageIndicator.layoutParams as? ConstraintLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                layoutParams.bottomToTop = ConstraintLayout.LayoutParams.UNSET

                binding.pageIndicator.layoutParams = layoutParams
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initViews() {
        binding.blurView
            .setupWith(binding.root)
            .setFrameClearDrawable(activity?.window?.decorView?.background)
            .setBlurRadius(16F)

        binding.toolBarBlurView
            .setupWith(binding.root)
            .setFrameClearDrawable(activity?.window?.decorView?.background)
            .setBlurRadius(16F)

        binding.ratioBlurView
            .setupWith(binding.root)
            .setFrameClearDrawable(activity?.window?.decorView?.background)
            .setBlurRadius(21F)

        binding.noteAddPageLayout.setupBlurView(binding.root)

        binding.noteMaterialView.setupBlurView(binding.root)

        binding.recordControlView.setupBlurView(binding.root)

        initThumbnailAndOutlineView()
        binding.templateDownloadDialog.cancel.setOnClickListener {
            AddTemplateEvent.sendAddtemplateSlightlythumbnailCancel(AddTemplateEvent.EDIT_PAGE)
            templateViewModel.cancelCurrentDownload()
        }

        binding.toolBarHide.setOnClickListener(AntiShakeClickListener {
            binding.toolBar.hide {
                adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.HIDE)
            }
        })

        binding.toolBarShow.setOnClickListener(AntiShakeClickListener {
            binding.toolBar.visibility = View.VISIBLE
            binding.toolBar.show {
                adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
            }
        })

        Glide.with(binding.root)
            .asGif()
            .load(R.drawable.template_download)
            .into(binding.templateDownloadDialog.downloadImg)

        binding.noteMajorToolLayout.back.setOnClickListener(AntiShakeClickListener {
            if (it != null) {
                WindowInsetsUtils.hideSoftKeyBoard(it)
            }
            noteViewModel.clearDocumentPages(currentDoc)
            editorAdViewModel.showInterstitialAdIfNeeded(
                requireActivity(),
                AppAdEvent.InterstitialAdPosition.BACK_TO_HOME
            ).observe(viewLifecycleOwner) { hasDone ->
                if (hasDone) {
                    findNavController().popBackStack()
                }
            }
        }
        )

        binding.searchPageBack.setOnClickListener(AntiShakeClickListener {
            SearchEvent.sendNoteSearchPageBackClickEvent()
            searchViewModel.getBackPageUUID()?.let { backPageUUID ->
                currentDoc.getPageByUUID(backPageUUID)?.let { page ->
                    searchViewModel.resetBackPageUUID()
                    currentDoc.viewingPageIndex = currentDoc.pages.indexOf(page)
                    setPage(page, false)
                }
            }
            noteSearchFrameLayout?.resetCurrentDocSearchSelect()
        }
        )

        initPagesSnippet()

        //缩略图列表的展示和隐藏
        binding.noteMajorToolLayout.showThumbnail.setOnClickListener(AntiShakeClickListener {
            snippetAnimationTime = ANIMATION_DURATION_TIME
            EditEvent.sendEditSidebarClick(!binding.noteMajorToolLayout.showThumbnail.isSelected)
            if (thumbnailListViewState.isStill() && snippetListViewState.isStill()) {
                when (thumbnailListViewState) {
                    NoteViewModel.ThumbnailListViewState.HIDDEN -> {
                        snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
                        noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.SHOWN)
                    }

                    NoteViewModel.ThumbnailListViewState.SHOWN -> {
                        noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
                    }

                    else -> {}
                }
            }
        })
        if (DimensionUtil.isLandAndOneThirdScreen(requireContext()) ||
            DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext())
        ) {
            snippetViewModel.changeSnippetListSelectStatus(false)
            snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
        }

        //卡片列表的展示和隐藏
        binding.noteMajorToolLayout.showSnippet.setOnClickListener(AntiShakeClickListener {
            snippetAnimationTime = ANIMATION_DURATION_TIME
            if (thumbnailListViewState.isStill() && snippetListViewState.isStill()) {
                when (snippetListViewState) {
                    SnippetViewModel.SnippetListViewState.HIDDEN -> {
                        SnippetEvent.sendSnippetLayoutShow()
                        noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
                        snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.SHOWN)
                    }

                    SnippetViewModel.SnippetListViewState.SHOWN, SnippetViewModel.SnippetListViewState.EXPANDED -> {
                        SnippetEvent.sendSnippetLayoutHide()
                        snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
                    }

                    else -> {}
                }
            }
        })

        binding.noteMajorToolLayout.newPage.setOnClickListener(AntiShakeClickListener {
            EditEvent.sendEditAddPageClick()
            AddTemplateEvent.sendAddTemplateShow()
            resetAllToolViews()
            if (sideBarViewModel.currentSidebarState.value == Sidebar.ADD_PAGE) {
                changeCurrentSidebarStatus(Sidebar.NONE)
            } else {
                changeCurrentSidebarStatus(Sidebar.ADD_PAGE)
            }
            EditEvent.sendEditSidebarClick(!binding.noteMajorToolLayout.showThumbnail.isSelected)
        })

        binding.noteMajorToolLayout.more.setOnClickListener(AntiShakeClickListener {
            resetAllToolViews()
            noteViewModel.isShowMoreWindow.value = true
        })

        doodleView.doodleTouchLayer.onTurnPageListener = object : OnTurnPageListener {
            override fun turnPreviousPage() {
                if (currentDoc.viewingPageIndex > 0) {
                    EditEvent.sendTurnPageClick()
                    doodleView.doodleTouchLayer
                        .clickToTurnPage(OnPageChangeListener.Direction.PREVIOUS)
                }
            }

            override fun turnNextPage() {
                if (currentDoc.viewingPageIndex < currentDoc.pageCount - 1) {
                    EditEvent.sendTurnPageClick()
                    doodleView.doodleTouchLayer
                        .clickToTurnPage(OnPageChangeListener.Direction.NEXT)
                }
            }
        }

        binding.undo.setOnClickListener {
            if (DigitalInkRecognitionManager.isRecognizedJustNow) {
                ConsoleEvent.sendConsoleUndoLocation(ConsoleEvent.CONSOLE_LOCATION_EDIT)
            }
            onUndoClick(it)
            binding.undoRedoToast.text = AppUtils.getString(R.string.instant_alpha_tool_undo)
            binding.undoRedoToastContainer.visibility = View.VISIBLE
            undoAndRedoDelayTimer.cancel()
            undoAndRedoDelayTimer.start(delayedMs = UNDO_REDO_TOAST_HIDDEN_TIME) {
                if (isAdded && checkViewBindingValidity()) {
                    binding.undoRedoToastContainer.visibility = View.INVISIBLE
                }
            }
        }

        binding.redo.setOnClickListener {
            ConsoleEvent.sendConsoleRedoClick()
            onRedoClick(it)
            binding.undoRedoToast.text = AppUtils.getString(R.string.instant_alpha_tool_redo)
            binding.undoRedoToastContainer.visibility = View.VISIBLE
            undoAndRedoDelayTimer.cancel()
            undoAndRedoDelayTimer.start(delayedMs = UNDO_REDO_TOAST_HIDDEN_TIME) {
                if (isAdded && checkViewBindingValidity()) {
                    binding.undoRedoToastContainer.visibility = View.INVISIBLE
                }
            }
        }
        binding.textStrikethrough.setOnClickListener {
            toolAttributesViewModel.setTextStrikethrough(!it.isSelected)
            EditEvent.sendEditTextBoxStyleClick("deleteline")
        }
        binding.textBold.setOnClickListener {
            toolAttributesViewModel.setTextBold(!it.isSelected)
            EditEvent.sendEditTextBoxStyleClick("bold")
        }
        binding.textUnderline.setOnClickListener {
            toolAttributesViewModel.setTextUnderLine(!it.isSelected)
            EditEvent.sendEditTextBoxStyleClick("underline")
        }
        binding.toolBar.onNewTargetEdgeAvailable =
            onNewTargetEdgeAvailable@{ newEdge, contentWidth, contentHeight ->
                if (!checkViewBindingValidity()) return@onNewTargetEdgeAvailable
                binding.toolBar.adaptViewConstraintToEdge(
                    binding.toolBarSelectionSubstitute,
                    newEdge,
                    toolBarShortLength
                )
                binding.toolBarSelectionSubstitute.isVisible = true

                adaptToolBar(binding.toolBarSelectionSubstitute, toolBarShortLength, newEdge)
            }
        val minorToolVerticalConstraints = ConstraintSet()
        minorToolVerticalConstraints.clone(binding.minorToolContent)
        val minorToolHorizontalConstraints = ConstraintSet()
        minorToolHorizontalConstraints.clone(
            requireContext(),
            R.layout.minor_tool_content_horizontal_layout
        )
        binding.toolBar.onNewEdgeSelected =
            onNewEdgeSelected@{ newEdge, contentWidth, contentHeight ->
                if (!checkViewBindingValidity()) return@onNewEdgeSelected
                binding.toolBarSelectionSubstitute.isVisible = false
                val recyclerViewOrientation =
                    if (newEdge.isLeftOrRigh()) RecyclerView.VERTICAL else RecyclerView.HORIZONTAL
                (binding.minorToolRecyclerView.layoutManager as? LinearLayoutManager)?.orientation =
                    recyclerViewOrientation

                (binding.suppressibleToolRecyclerView.layoutManager as? LinearLayoutManager)?.orientation =
                    recyclerViewOrientation

                changeShadow(newEdge)

                binding.toolBar.layoutParams?.let { layoutParams ->
                    layoutParams.width =
                        if (newEdge.isLeftOrRigh()) LayoutParams.WRAP_CONTENT else LayoutParams.MATCH_PARENT
                    layoutParams.height =
                        if (newEdge.isLeftOrRigh()) LayoutParams.MATCH_PARENT else LayoutParams.WRAP_CONTENT

                    binding.toolBar.layoutParams = layoutParams
                }
                binding.toolBarBlurView.layoutParams?.let { layoutParams ->
                    layoutParams.width =
                        if (newEdge.isLeftOrRigh()) LayoutParams.WRAP_CONTENT else LayoutParams.MATCH_PARENT
                    layoutParams.height =
                        if (newEdge.isLeftOrRigh()) LayoutParams.MATCH_PARENT else LayoutParams.WRAP_CONTENT

                    binding.toolBarBlurView.layoutParams = layoutParams
                }

                binding.minorToolContent.layoutParams?.let { layoutParams ->
                    layoutParams.width =
                        if (newEdge.isLeftOrRigh()) LayoutParams.WRAP_CONTENT else LayoutParams.MATCH_PARENT
                    layoutParams.height =
                        if (newEdge.isLeftOrRigh()) LayoutParams.MATCH_PARENT else LayoutParams.WRAP_CONTENT

                    binding.minorToolContent.layoutParams = layoutParams
                }

                TransitionManager.beginDelayedTransition(
                    binding.minorToolContent,
                    AutoTransition().apply {
                        duration = 0
                        addListener(object : TransitionListenerAdapter() {
                            override fun onTransitionEnd(transition: Transition) {
                                if (checkViewBindingValidity()) {
                                    binding.minorToolContainer.requestLayout()
                                }
                            }
                        })
                    })
                val suppressibleToolRecyclerViewVisibility =
                    binding.suppressibleToolRecyclerView.visibility
                if (newEdge.isLeftOrRigh()) {
                    minorToolVerticalConstraints.applyTo(binding.minorToolContent)
                } else {
                    minorToolHorizontalConstraints.applyTo(binding.minorToolContent)
                }
                binding.suppressibleToolRecyclerView.visibility =
                    suppressibleToolRecyclerViewVisibility

                binding.minorToolContainer.layoutParams?.let { layoutParams ->
                    layoutParams.width =
                        if (newEdge.isLeftOrRigh()) LayoutParams.WRAP_CONTENT else LayoutParams.MATCH_PARENT
                    layoutParams.height =
                        if (newEdge.isLeftOrRigh()) LayoutParams.MATCH_PARENT else LayoutParams.WRAP_CONTENT

                    binding.minorToolContainer.layoutParams = layoutParams
                }

                binding.toolBar.run {
                    if (selectEdge == AdsorptionEdgeLayout.EDGE.BOTTOM) {
                        if (binding.textOperationContainer.isVisible) {
                            binding.toolBar.changeBoundaryView(
                                binding.textOperationContainer.id,
                                AdsorptionEdgeLayout.EDGE.BOTTOM
                            )
                            binding.toolBar.updatePositionWithAnimation(duration = 0L)
                        }
                    } else {
                        if (binding.toolBar.bottomViewId == binding.textOperationContainer.id) {
                            binding.toolBar.changeBoundaryView(
                                ResourcesCompat.ID_NULL,
                                AdsorptionEdgeLayout.EDGE.BOTTOM
                            )
                            binding.toolBar.updatePositionWithAnimation(duration = 0L)
                        }
                    }
                    adsorptionEdgeViewModel.edge = selectEdge
                    adsorptionEdgeViewModel.leftViewId = leftViewId
                    adsorptionEdgeViewModel.topViewId = topViewId
                    adsorptionEdgeViewModel.rightViewId = rightViewId
                    adsorptionEdgeViewModel.bottomViewId = bottomViewId
                }

                binding.minorToolRecyclerView.adapter?.notifyDataSetChanged()
                adaptToolBar(binding.toolBar, min(contentWidth, contentHeight), newEdge)
            }
        binding.toolBar.contentView = binding.minorToolContent
        binding.toolBar.setOnIgnoreTouchEventListener {
            doodleView.doodleEditLayer.isRecordViewEditing
        }
        binding.noteMajorToolLayout.startRecord.setOnClickListener {
            val notificationManager = NotificationManagerCompat.from(requireContext())
            if (!notificationManager.areNotificationsEnabled() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (PermissionRequester.checkPermissionState(
                        this@NoteEditorFragment,
                        Manifest.permission.POST_NOTIFICATIONS
                    ) == PermissionRequester.PermissionState.PERMISSION_GRANTED
                ) {
                    checkPermissionAndStartRecord()
                } else {
                    permissionRequester.request(Manifest.permission.POST_NOTIFICATIONS) { isGranted ->
                        checkPermissionAndStartRecord()
                    }
                }
            } else {
                checkPermissionAndStartRecord()
            }
        }
        binding.noteMajorToolLayout.stopRecord.setOnClickListener {
            recordViewModel.stopRecord()
            activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
        binding.noteMajorToolLayout.showRecordControlView.setOnClickListener {
            RecordEvent.sendRecordExpandBtnClick()
            recordViewModel.switchIsShowRecordControlView()
        }
        binding.recordControlView.apply {
            setOnPlayButtonClickListener {
                RecordEvent.sendRecordPlayBtnClick()
                recordViewModel.isPlaying.value?.let { isPlaying ->
                    if (isPlaying) {
                        recordViewModel.pausePlay()
                    } else {
                        recordViewModel.startPlay(mainHandler)
                    }
                }
            }
            setOnShowListButtonClickListener {
                RecordEvent.sendRecordListClick()
                recordViewModel.refreshRecordItemList()
                recordViewModel.isShowRecordWindow.value =
                    recordViewModel.isShowRecordWindow.value != true
            }
            setOnSpeedClickListener {
                recordViewModel.changePlayBackSpeed()
            }
            setOnForwardButtonClickListener {
                RecordEvent.sendRecordTenSecondsClick("advance")
                recordViewModel.fastForward()
            }
            setOnBackButtonClickListener {
                RecordEvent.sendRecordTenSecondsClick("retreat")
                recordViewModel.fastBackward()
            }
            setOnAddTagButtonClickListener {
                RecordEvent.sendRecordAddTagClick()
                recordViewModel.newTag()
            }
            setOnProgressStartTrackingListener {
                recordViewModel.pausePlay()
            }
            setOnProgressStopTrackingListener {
                recordViewModel.resumePlay(it, mainHandler)
            }
        }
        recordViewModel.setOnPlayErrorCallback { fileName, error ->
            ToastUtils.topCenter(
                requireContext(),
                resources.getString(
                    when (error) {
                        AudioPlayer.ERROR_FILE_NOT_EXIST -> R.string.note_record_tip_file_not_found_exception
                        AudioPlayer.ERROR_FILE_CORRUPTED -> R.string.note_record_tip_file_damage_exception
                        else -> R.string.note_record_tip_play_exception
                    }, fileName
                )
            )
        }

        binding.webSidebarView.onCloseListener = {
            if (sideBarViewModel.currentSidebarState.value == Sidebar.WEB_SEARCH) {
                changeCurrentSidebarStatus(Sidebar.DOC_SEARCH, false)
            } else {
                changeCurrentSidebarStatus(Sidebar.NONE)
            }
        }

        if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext()) || DimensionUtil.isLandAndOneThirdScreen(
                requireContext()
            )
        ) {
            val param = binding.webSidebarView.layoutParams
            val metrics = DimensionUtil.getScreenDimensions(requireContext())
            param.width = metrics.widthPixels
            binding.webSidebarView.layoutParams = param
        }

        if (KiloApp.isLayoutRtl) {
            binding.draftPaperShowBtn.setShadowHiddenRight(false)
            binding.draftPaperShowBtn.setShadowHiddenLeft(true)
            binding.draftPaperShowBtn.setSpecialCorner(
                0,
                DimensionUtil.getDimensionPixelSize(R.dimen.dp_12),
                0,
                DimensionUtil.getDimensionPixelSize(R.dimen.dp_12)
            )
        }

        binding.draftPaperShowBtn.setOnClickListener(AntiShakeClickListener {
            resetDraftSizeAndLocation()
            draftPaperViewModel.switchDraftPaperVisibility(true)
            DraftPaperEvent.sendDraftPaperShowBtnClickEvent()
        })
        binding.draftPaperFragmentContainer.post {
            if (!checkViewBindingValidity()) return@post
            binding.draftPaperFragmentContainer.apply {
                val lp = layoutParams
                lp.apply {
                    width =
                        draftPaperSizeAndLocationHelper.getDraftPaperInitWidth(
                            context,
                            draftPaperViewModel.draftPaperViewWidth
                        )
                    height =
                        draftPaperSizeAndLocationHelper.getDraftPaperInitHeight(
                            context,
                            binding.doodle,
                            draftPaperViewModel.draftPaperViewHeight
                        )
                }
                layoutParams = lp
                binding.draftPaperFragmentContainer.doOnNextLayout {
                    x = draftPaperSizeAndLocationHelper.getDraftPaperInitX(
                        context,
                        draftPaperViewModel.draftPaperViewXRelativeScreenRatio,
                        draftPaperViewModel.draftPaperViewWidth
                    )
                    y = draftPaperSizeAndLocationHelper.getDraftPaperInitY(
                        context,
                        doodleView,
                        draftPaperViewModel.draftPaperViewYRelativeScreenRatio,
                        draftPaperViewModel.draftPaperViewHeight
                    )
                }
                setMinWidth(draftPaperSizeAndLocationHelper.getDraftPaperMinWidth(context))
                setMinHeight(draftPaperSizeAndLocationHelper.getDraftPaperMinHeight(context))
                setMoveMargins(
                    draftPaperSizeAndLocationHelper.getDraftPaperHorizontalMinMargin(
                        context
                    ).toFloat() - draftPaperSizeAndLocationHelper.getDraftPaperShadowWidth(context)
                )
                pivotX = 0f
                pivotY = 0f
            }
        }

        doodleView.doodleTouchLayer.addOnTransformChangeListeners(onTransformChanged)
        binding.doodle.doodleModeConfig.initScale = noteScaleViewModel.currentScaleRadio.value ?: 1F
        binding.doodle.doodleModeConfig.lastPageInitialScale =
            noteScaleViewModel.lastPageInitialScale
        binding.doodle.doodleModeConfig.renderRectInPdf = noteScaleViewModel.renderRectInPdf
        binding.scaleLockAndRatioToolContent.setOnClickListener(AntiShakeClickListener {
            noteScaleViewModel.enableScale.value?.let { enable ->
                noteScaleViewModel.changeEnableScale(!enable)
            }
        })


        binding.consoleGuide.setOnClickListener {
            binding.consoleGuide.visibility = View.INVISIBLE
            consoleViewModel.changeIsShowConsoleInstructionsView(true)
        }

        binding.consoleInstruction.closeView = {
            consoleViewModel.changeIsShowConsoleInstructionsView(false)
            if (isConsoleState) {
                binding.consoleGuide.visibility = View.VISIBLE
            } else {
                binding.consoleGuide.visibility = View.INVISIBLE
            }
        }
        binding.consoleInstruction.onVisibilityChangedAction = { visibility ->
            if (visibility == View.VISIBLE) {
                binding.consoleInstruction.post {
                    if (checkViewBindingValidity()) {
                        if (binding.consoleInstruction.isOverlap(binding.consoleLastDocumentRv)) {
                            binding.consoleLastDocumentRvContainer.visibility = View.INVISIBLE
                            binding.consoleNote.apply {
                                isSelected = false
                                changeConsoleNoteStates(isSelected)
                            }
                        }
                    }
                }
            }
        }

        binding.console.apply {
            if (isPortraitAndHalfScreen()) {
                setMargins(
                    context.resources.getDimensionPixelSize(R.dimen.dp_105),
                    marginTop,
                    marginEnd,
                    context.resources.getDimensionPixelSize(R.dimen.dp_140)
                )
            } else if (isPortraitAndOneThirdScreen()) {
                setMargins(
                    context.resources.getDimensionPixelSize(R.dimen.dp_105),
                    marginTop,
                    marginEnd,
                    context.resources.getDimensionPixelSize(R.dimen.dp_80)
                )
            } else if (isPortraitAndFullScreen()) {
                setMargins(
                    context.resources.getDimensionPixelSize(R.dimen.dp_105),
                    marginTop,
                    marginEnd,
                    marginBottom
                )
            }

            onSingleTapUp = {
                if (consoleViewModel.isConsoleState.value != true) {
                    ConsoleEvent.sendConsoleClick(true)
                    UserUsageConfig.isNeedShowConsoleGuide = false
                    consoleViewModel.changeIsShowConsoleInstructionsView(true)
                    consoleViewModel.changeConsoleState(true)
                } else {
                    ConsoleEvent.sendConsoleClick(false)
                    consoleViewModel.changeConsoleState(false)
                    binding.consoleNote.apply {
                        isSelected = false
                        changeConsoleNoteStates(isSelected)
                    }
                    binding.console.isSelected = false
                    binding.consoleIcon.isSelected = false
                }

            }

        }

        binding.consoleNote.setOnClickListener {
            ConsoleEvent.sendConsoleSwitchDocumentClick()
            val adapter = binding.consoleLastDocumentRv.overScrollRecyclerView.adapter
            if (!binding.consoleNote.isSelected) {
                if (adapter is ConsoleLastDocumentsAdapter) {
                    val (documents, index) = getConsoleLastDocumentParam()
                    adapter.update(documents, index)
                    ensureConsoleLastDocumentVisible(index)
                } else {
                    initConsoleLastDocuments()
                }
                binding.consoleNote.apply {
                    isSelected = true
                    changeConsoleNoteStates(isSelected)
                }
                binding.consoleLastDocumentRvContainer.visibility = View.VISIBLE
            } else {
                if (adapter is ConsoleLastDocumentsAdapter) {
                    val newPosition = adapter.switchToNext()
                    if (newPosition != -1) {
                        ensureConsoleLastDocumentVisible(newPosition)
                    }
                }
            }
        }

        binding.consoleToastUndo.apply {
            val spannableString =
                SpannableString(AppUtils.getString(R.string.instant_alpha_tool_undo))
            val length = spannableString.length
            spannableString.setSpan(UnderlineSpan(), 0, length, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
            text = spannableString
            setOnClickListener {
                if (DigitalInkRecognitionManager.isRecognizedJustNow) {
                    ConsoleEvent.sendConsoleUndoLocation(ConsoleEvent.CONSOLE_LOCATION_TOAST)
                }
                onUndoClick(it)
            }
        }

        initPagePreviewList()
    }

    private fun checkPermissionAndStartRecord() {
        if (PermissionRequester.checkPermissionState(
                this@NoteEditorFragment,
                Manifest.permission.RECORD_AUDIO
            ) == PermissionRequester.PermissionState.PERMISSION_GRANTED
        ) {
            activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            recordViewModel.startRecord()
            RecordEvent.sendRecordStartBtnClick()
        } else {
            permissionRequester.request(Manifest.permission.RECORD_AUDIO) { isGranted ->
                if (isGranted) {
                    activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    recordViewModel.startRecord()
                    RecordEvent.sendRecordStartBtnClick()
                    RecordEvent.sendRecordMicrophonePermissionStatus("permit")
                } else {
                    if (PermissionRequester.checkPermissionState(
                            this@NoteEditorFragment,
                            Manifest.permission.RECORD_AUDIO
                        ) == PermissionRequester.PermissionState.PERMISSION_DENIED
                    ) {
                        RecordEvent.sendRecordMicrophonePermissionStatus("disallow")
                    }
                    if (PermissionRequester.checkPermissionState(
                            this@NoteEditorFragment,
                            Manifest.permission.RECORD_AUDIO
                        ) == PermissionRequester.PermissionState.PERMISSION_NOT_ASK_AGAIN
                    ) {
                        val alertDialog = AlertDialog.Builder()
                            .setMsg(resources.getString(R.string.never_aks_read_external_storage))
                            .setPositiveBtn(resources.getString(R.string.go_to_set)) {
                                PermissionRequester.jumpToSetting(this@NoteEditorFragment)
                                RecordEvent.sendRecordMicrophonePermissionStatus("setting")
                            }
                            .setNegativeBtn(resources.getString(R.string.ok)) {
                                RecordEvent.sendRecordMicrophonePermissionStatus("cancel")
                            }
                            .build()
                        alertDialog.show(
                            parentFragmentManager,
                            PERMISSION_ERROR_TAG
                        )
                    }
                }
            }
        }
    }

    private fun ensureConsoleLastDocumentVisible(newPosition: Int) {
        val layoutManager =
            binding.consoleLastDocumentRv.overScrollRecyclerView.layoutManager as LinearLayoutManager
        val firstVisiblePosition =
            layoutManager.findFirstCompletelyVisibleItemPosition()
        val lastVisiblePosition = layoutManager.findLastCompletelyVisibleItemPosition()
        if (newPosition !in firstVisiblePosition..lastVisiblePosition) {
            layoutManager.scrollToPosition(newPosition)
        }
    }

    private fun showPagePreviewList() {
        pagePreviewViewModel.showSelectedPageBitmap(currentDoc) { bitmap ->
            if (!bitmap.isRecycled && checkViewBindingValidity()) {
                binding.pagePreviewList.updateSelectPageBitmap(bitmap)
            }
        }
        binding.pagePreviewList.setCurrentDocument(currentDoc)
        binding.pagePreviewList.visibility = View.VISIBLE
    }

    private fun removePagePreviewList() {
        binding.pagePreviewList.visibility = View.GONE
        doodleView.removePagePreviewView()
    }

    private fun initPagePreviewList() {
        pagePreviewViewModel.currentPreviewPageBitmap.observe(viewLifecycleOwner) { result ->
            if (result != null) {
                doodleView.updatePagePreviewView(result.first, result.second)
            }
        }

        binding.pagePreviewList.setOnUpdatePagePreview { index ->
            if (index < 0 || index >= currentDoc.pageCount) return@setOnUpdatePagePreview
            val selectedPageRectF = binding.pagePreviewList.getSelectedPageRect()
            showPreviewPagePaginationWindow(
                index,
                binding.pagePreviewList,
                (if (KiloApp.isLayoutRtl) (selectedPageRectF.left + binding.pagePreviewList.context.resources.getDimensionPixelSize(
                    R.dimen.dp_156
                )) else selectedPageRectF.left) + selectedPageRectF.width() / 2,
                selectedPageRectF.width()
            )
            doodleView.doodleEditLayer.dismissDrawerView()
            doodleView.exitCommandMode()
            doodleView.doodleTouchLayer.visibility = View.INVISIBLE
            doodleView.doodleLayer.visibility = View.INVISIBLE
            doodleView.showPagePreviewView(context)
            pagePreviewViewModel.previewPageThumbnail(
                currentDoc,
                index,
                doodleView.doodleTouchLayer.actualDoodleScale(),
                doodleView.pagePreviewViewport
            ) { bitmap ->
                if (!bitmap.isRecycled && checkViewBindingValidity()) {
                    binding.pagePreviewList.updateSelectPageBitmap(bitmap)
                }
            }
        }

        binding.pagePreviewList.setOnPagePreviewRateChange { pagePreviewRate ->
            showConsolePageTurningRate(pagePreviewRate)
        }

        binding.pagePreviewList.setOnPagePreviewChangeFinish { index ->
            if (index >= 0 && index < currentDoc.pages.size) {
                if (index != currentDoc.viewingPageIndex) {
                    currentDoc.viewingPageIndex = index
                    setPage(currentDoc[currentDoc.viewingPageIndex])
                } else {
                    if (isConsoleState) {
                        doodleView.hidePagePreviewView()
                        doodleView.doodleTouchLayer.visibility = View.VISIBLE
                        doodleView.doodleLayer.visibility = View.VISIBLE
                    }
                }
            }
        }

        binding.pagePreviewList.setOnIsFirstPageAction {
            showConsoleToast(AppUtils.getString(R.string.console_toast_first_page))
        }

        binding.pagePreviewList.setOnIsLastPageAction {
            showConsoleToast(AppUtils.getString(R.string.console_toast_last_page))
        }
    }

    private val dismissPreviewPaginationWindowRunnable = Runnable {
        if (pagePreviewPaginationWindow?.isShowing == true) {
            pagePreviewPaginationWindow?.dismiss()
            pagePreviewPaginationWindow = null
        }
    }

    private fun showPreviewPagePaginationWindow(
        index: Int,
        anchor: View?,
        offsetX: Float,
        viewWidth: Float,
    ) {
        val pagination = getString(
            R.string.page_indicator,
            index + 1,
            currentDoc.pageCount
        )
        if (pagePreviewPaginationWindow == null) {
            pagePreviewPaginationWindow = PagePreviewPaginationWindow(requireContext())
            anchor?.let {
                pagePreviewPaginationWindow?.show(anchor, offsetX, viewWidth, pagination)
            }
        } else if (pagePreviewPaginationWindow?.isShowing == true) {
            anchor?.let {
                pagePreviewPaginationWindow?.updateLocation(anchor, offsetX, viewWidth, pagination)
            }
        }
        mainHandler.removeCallbacks(dismissPreviewPaginationWindowRunnable)
        mainHandler.postDelayed(
            dismissPreviewPaginationWindowRunnable,
            CONSOLE_DISMISS_PAGE_PREVIEW_RATE_DELAY_TIME_MS
        )
    }

    override fun doOnPageThumbnailUpdated(page: Page, newThumbnail: Bitmap) {
        super.doOnPageThumbnailUpdated(page, newThumbnail)
        ThreadUtils.postMainThread {
            if (checkViewBindingValidity()) {
                binding.noteAddPageLayout.refreshTemplate()
            }
        }
    }

    override fun onThumbnailUpdateFinish(page: Page) {
        super.onThumbnailUpdateFinish(page)
        if (checkViewBindingValidity()) {
            binding.pagePreviewList.updatePagesList(page)
        }
    }

    private fun getConsoleLastDocumentParam(): Pair<List<Document>, Int> {
        val documents = if (noteViewModel.isHiddenSpaceMode.value == true) {
            noteViewModel.allHiddenDocuments
        } else {
            noteViewModel.allUnhiddenDocuments
        }.sortedByDescending {
            it.openedTime
        }.take(SWITCH_LAST_DOCUMENTS_NUM)
        var index = documents.indexOf(currentDoc)
        if (index == -1) {
            index = 0
        }
        return documents to index
    }

    private fun initConsoleLastDocuments() {
        val context = requireContext()
        if (DimensionUtil.isLandAndOneThirdScreen(context) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                context
            )
        ) {
            val layoutParams =
                binding.consoleLastDocumentRvContainer.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.updateMargins(
                context.resources.getDimensionPixelSize(R.dimen.dp_27),
                0,
                context.resources.getDimensionPixelSize(R.dimen.dp_27),
                context.resources.getDimensionPixelSize(R.dimen.dp_42)
            )
            ConstraintSet().apply {
                clone(binding.rootConstraintLayout)
                clear(binding.consoleLastDocumentRvContainer.id, ConstraintSet.TOP)
                connect(
                    binding.consoleLastDocumentRvContainer.id,
                    ConstraintSet.BOTTOM,
                    binding.consoleNote.id,
                    ConstraintSet.TOP
                )
                applyToLayoutParams(binding.consoleLastDocumentRvContainer.id, layoutParams)
                applyTo(binding.rootConstraintLayout)
            }

            binding.toolBarShow.visibility = View.INVISIBLE
        } else if (DimensionUtil.isLandAndHalfScreen(context)) {
            val layoutParams =
                binding.consoleLastDocumentRvContainer.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.matchConstraintMaxWidth =
                context.resources.getDimensionPixelSize(R.dimen.dp_507)
            binding.consoleLastDocumentRvContainer.layoutParams = layoutParams
        } else if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
            val layoutParams =
                binding.consoleLastDocumentRvContainer.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.matchConstraintMaxWidth =
                context.resources.getDimensionPixelSize(R.dimen.dp_561)
            binding.consoleLastDocumentRvContainer.layoutParams = layoutParams
        }
        binding.consoleLastDocumentRv.overScrollRecyclerView.apply {
            layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            addItemDecoration(
                object : ItemDecoration() {

                    private val space = context.resources.getDimensionPixelSize(R.dimen.dp_18)
                    private val edge = context.resources.getDimensionPixelSize(R.dimen.dp_15)

                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State,
                    ) {
                        val position = parent.getChildAdapterPosition(view)
                        if (position == 0) {
                            outRect.left = edge
                        }
                        outRect.right = if (position + 1 == parent.adapter?.itemCount) {
                            edge
                        } else {
                            space
                        }
                    }
                }
            )
            val (documents, index) = getConsoleLastDocumentParam()
            adapter = ConsoleLastDocumentsAdapter(
                documents,
                index,
                resources.getDimensionPixelSize(R.dimen.dp_4)
            ) { document, fromClick ->
                editorSwitchDocumentViewModel.setTargetSwitchDocument(document)
                if (fromClick) {
                    consoleViewModel.changeConsoleState(false)
                    binding.consoleNote.apply {
                        isSelected = false
                        changeConsoleNoteStates(isSelected)
                    }
                    binding.console.isSelected = false
                    binding.consoleIcon.isSelected = false
                }
                true
            }
        }
        binding.consoleLastDocumentRvContainer.onVisibilityChangedAction = { visibility ->
            if (visibility == View.VISIBLE) {
                binding.consoleLastDocumentRv.post {
                    if (checkViewBindingValidity()) {
                        if (binding.consoleLastDocumentRv.isOverlap(binding.consoleInstruction)) {
                            consoleViewModel.changeIsShowConsoleInstructionsView(false)
                            if (isConsoleState) {
                                binding.consoleGuide.visibility = View.VISIBLE
                            } else {
                                binding.consoleGuide.visibility = View.INVISIBLE
                            }
                        }
                    }
                }
            }
            binding.consoleGestureLayer.swallowTouchEvent = visibility == View.VISIBLE
        }
    }

    private fun showConsoleToast(command: ConsoleCommand, selections: List<Selection>) {
        if (checkViewBindingValidity() && isAdded) {
            var toast = getConsoleToastString(command, selections)
            if (BuildConfig.DEBUG_MODE && toast.isEmpty()) {
                toast = command.toString()
            }
            showConsoleToast(
                toast,
                needUndoTip = (command.commandType == ConsoleCommandType.DELETE
                        || command.commandType == ConsoleCommandType.DELETE_ALL
                        || command.commandType == ConsoleCommandType.REMOVE
                        || command.commandType == ConsoleCommandType.PASTE)
                        && selections.isNotEmpty()
            )
        }
    }

    private val dismissConsoleToastRunnable = Runnable {
        if (checkViewBindingValidity()) {
            binding.consoleToastContainer.visibility = View.GONE
        }
    }

    private val dismissConsolePageTurningRateRunnable = Runnable {
        if (checkViewBindingValidity()) {
            binding.pagePreviewArrow.visibility = View.GONE
            binding.pagePreviewPageTurnRate.visibility = View.GONE
        }
    }

    private fun showConsolePageTurningRate(pagePreviewRate: PagePreviewGearControlView.PagePreviewRate?) {
        if (pagePreviewRate == null || pagePreviewRate == PagePreviewGearControlView.PagePreviewRate.Unset) return
        binding.pagePreviewArrow.visibility = View.VISIBLE
        binding.pagePreviewPageTurnRate.visibility = View.VISIBLE
        binding.pagePreviewArrow.apply {
            setMargins(
                marginStart,
                marginTop,
                marginEnd,
                -DimensionUtil.getDimensionPixelSize(R.dimen.dp_14)
            )
        }
        when (pagePreviewRate) {
            PagePreviewGearControlView.PagePreviewRate.LeftOneSpeed -> {
                binding.pagePreviewArrow.scaleX = 1F
                binding.pagePreviewPageTurnRate.setImageDrawable(AppUtils.getDrawable(R.drawable.page_preview_page_turn_rate_x1))
            }

            PagePreviewGearControlView.PagePreviewRate.LeftDoubleSpeed -> {
                binding.pagePreviewArrow.scaleX = 1F
                binding.pagePreviewPageTurnRate.setImageDrawable(AppUtils.getDrawable(R.drawable.page_preview_page_turn_rate_x2))
            }

            PagePreviewGearControlView.PagePreviewRate.LeftTripleSpeed -> {
                binding.pagePreviewArrow.scaleX = 1F
                binding.pagePreviewPageTurnRate.setImageDrawable(AppUtils.getDrawable(R.drawable.page_preview_page_turn_rate_x3))
            }

            PagePreviewGearControlView.PagePreviewRate.RightOneSpeed -> {
                binding.pagePreviewPageTurnRate.setImageDrawable(AppUtils.getDrawable(R.drawable.page_preview_page_turn_rate_x1))
                binding.pagePreviewArrow.scaleX = -1F
            }

            PagePreviewGearControlView.PagePreviewRate.RightDoubleSpeed -> {
                binding.pagePreviewPageTurnRate.setImageDrawable(AppUtils.getDrawable(R.drawable.page_preview_page_turn_rate_x2))
                binding.pagePreviewArrow.scaleX = -1F
            }

            PagePreviewGearControlView.PagePreviewRate.RightTripleSpeed -> {
                binding.pagePreviewPageTurnRate.setImageDrawable(AppUtils.getDrawable(R.drawable.page_preview_page_turn_rate_x3))
                binding.pagePreviewArrow.scaleX = -1F
            }

            else -> {

            }
        }
        mainHandler.removeCallbacks(dismissConsolePageTurningRateRunnable)
        mainHandler.postDelayed(
            dismissConsolePageTurningRateRunnable,
            CONSOLE_DISMISS_PAGE_PREVIEW_RATE_DELAY_TIME_MS
        )
    }

    private fun showConsoleToast(toast: String, needUndoTip: Boolean = false) {
        if (checkViewBindingValidity() && isAdded) {
            if (toast.isNotEmpty()) {
                binding.consoleToastContainer.apply {
                    visibility = View.VISIBLE
                    binding.consoleToast.text = toast
                    binding.consoleToastUndo.visibility =
                        if (needUndoTip) View.VISIBLE else View.GONE
                    mainHandler.removeCallbacks(dismissConsoleToastRunnable)
                    mainHandler.postDelayed(
                        dismissConsoleToastRunnable,
                        CONSOLE_TOAST_DISMISS_DELAY_TIME_MS
                    )
                }
            }
        }
    }

    private fun enterGestureRecognizeMode() {
        isConsoleState = true
        doodleView.doodleTouchLayer.interruptTransform = true
        (doodleView.modelManager as? IConsoleCommandReceiver)?.onConsoleStateChanged(true)
        adsorptionEdgeViewModel.changeToolBarVisibility(
            AdsorptionEdgeViewModel.ToolBarState.NONE,
            persistent = false
        )
        binding.consoleGestureLayer.reset()
        binding.consoleGestureLayer.visibility = View.VISIBLE
        binding.consoleNote.apply {
            visibility = View.VISIBLE
            if (isLandOneThirdScreen()) {
                setMargins(
                    context.resources.getDimensionPixelSize(R.dimen.dp_4),
                    marginTop,
                    marginEnd,
                    marginBottom
                )
            } else if (isPortraitAndOneThirdScreen()) {
                setMargins(
                    context.resources.getDimensionPixelSize(R.dimen.dp_97),
                    marginTop,
                    marginEnd,
                    context.resources.getDimensionPixelSize(R.dimen.dp_347)
                )
            } else if (isPortraitAndHalfScreen()) {
                setMargins(
                    context.resources.getDimensionPixelSize(R.dimen.dp_97),
                    marginTop,
                    marginEnd,
                    context.resources.getDimensionPixelSize(R.dimen.dp_296)
                )
            }
        }
        binding.consoleGuide.visibility =
            if (isPortraitAndOneThirdScreen() || consoleViewModel.isShowConsoleInstructionsView.value == true) View.INVISIBLE else View.VISIBLE
        binding.consoleNoteIcon.visibility = View.VISIBLE
        binding.consoleUndoRedo.visibility =
            if (isLandOneThirdScreen()) View.INVISIBLE else View.VISIBLE
        doodleView.doodleEditLayer.dismissSelectView()
        doodleView.modelManager.resetToolViews(false)
        doodleView.showPagePreviewView(context)
        hideAllSidebarView()
        showPagePreviewList()
        setMajorToolAndDoodleViewBackgroundInConsoleMode()
    }

    private fun exitGestureRecognizeMode() {
        isConsoleState = false
        doodleView.doodleTouchLayer.interruptTransform = false
        (doodleView.modelManager as? IConsoleCommandReceiver)?.onConsoleStateChanged(false)
        binding.consoleGestureLayer.reset()
        binding.consoleGestureLayer.visibility = View.INVISIBLE
        binding.consoleNote.visibility = View.INVISIBLE
        binding.consoleNoteIcon.visibility = View.INVISIBLE
        binding.consoleGuide.visibility = View.INVISIBLE
        binding.consoleLastDocumentRvContainer.visibility = View.INVISIBLE
        binding.consoleUndoRedo.visibility = View.INVISIBLE
        doodleView.doodleTouchLayer.visibility = View.VISIBLE
        doodleView.doodleLayer.visibility = View.VISIBLE
        ConsoleCommandManager.reset()
        if (editorSwitchDocumentViewModel.switchDocument()) {
            showLoadingDialog()
            consoleViewModel.resetSideBarState()
        } else {
            recoverAllSidebarViewState()
        }
        removePagePreviewList()
        resetMajorToolAndDoodleViewBackground()
    }

    private fun hideAllSidebarView() {
        if (consoleViewModel.currentSidebarState == null) {
            consoleViewModel.currentSidebarState = sideBarViewModel.currentSidebarState.value
            sidebarAnimationTime = ANIMATION_DURATION_TIME
            changeCurrentSidebarStatus(Sidebar.NONE, false)
        }
        if (consoleViewModel.thumbnailListViewState == null) {
            consoleViewModel.thumbnailListViewState = noteViewModel.thumbnailListViewState.value
            snippetAnimationTime = ANIMATION_DURATION_TIME
            noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
        }
        if (consoleViewModel.snippetListViewState == null) {
            consoleViewModel.snippetListViewState = snippetViewModel.snippetListViewState.value
            snippetAnimationTime = ANIMATION_DURATION_TIME
            snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
        }
        if (consoleViewModel.isShowRecordControlView == null) {
            consoleViewModel.isShowRecordControlView = recordViewModel.isShowRecordControlView.value
            recordViewModel.setIsShowRecordControlView(false)
        }
        if (consoleViewModel.isShowDraftPaper == null) {
            consoleViewModel.isShowDraftPaper = draftPaperViewModel.isShowDraftPaper.value
            draftPaperAnimationTime = ANIMATION_DURATION_TIME
            draftPaperViewModel.switchDraftPaperVisibility(false)
        }
    }

    private fun recoverAllSidebarViewState() {
        consoleViewModel.isShowRecordControlView?.let {
            consoleViewModel.isShowRecordControlView = null
            recordViewModel.setIsShowRecordControlView(it)
        }
        consoleViewModel.currentSidebarState?.let {
            consoleViewModel.currentSidebarState = null
            sidebarAnimationTime = ANIMATION_DURATION_TIME
            changeCurrentSidebarStatus(it, false)
        }
        consoleViewModel.thumbnailListViewState?.let {
            consoleViewModel.thumbnailListViewState = null
            snippetAnimationTime = ANIMATION_DURATION_TIME
            noteViewModel.changeThumbnailListViewState(it)
        }
        consoleViewModel.snippetListViewState?.let {
            consoleViewModel.snippetListViewState = null
            snippetAnimationTime = ANIMATION_DURATION_TIME
            snippetViewModel.changeSnippetListViewState(it)
        }
        consoleViewModel.isShowDraftPaper?.let {
            consoleViewModel.isShowDraftPaper = null
            draftPaperAnimationTime = ANIMATION_DURATION_TIME
            draftPaperViewModel.switchDraftPaperVisibility(it)
        }
    }

    private fun setMajorToolAndDoodleViewBackgroundInConsoleMode() {
        binding.doodle.setBackgroundResource(R.color.console_doodle_background_color)
        binding.noteMajorToolLayout.maskView.visibility = View.INVISIBLE
        binding.noteMajorToolLayout.majorToolBackgroundInConsole.visibility = View.VISIBLE
    }

    private fun resetMajorToolAndDoodleViewBackground() {
        if (currentDoc.getCurrentPage().paper.isBuiltinBlack()) {
            doodleView.setBackgroundResource(R.color.black_40)
        } else {
            doodleView.setBackgroundResource(if (editorModeViewModel.editorMode.value == InputMode.VIEW && toolAttributesViewModel.currentReadingStyle.value == ReadingStyleUtils.ReadingStyle.DARK) R.color.black_30 else R.color.transparent)
        }
        binding.noteMajorToolLayout.maskView.visibility = View.VISIBLE
        binding.noteMajorToolLayout.majorToolBackgroundInConsole.visibility = View.INVISIBLE
    }

    private fun showPageResizingTipIfNeed() {
        if (UserUsageConfig.needShowPageResizingTip(currentDoc) && currentDoc.viewingPageIndex != 0) {
            EditEvent.sendShowPageSizeTip()
            binding.tipsResizingContainer.isVisible = true
            binding.tipsResizingToSet.setOnClickListener(AntiShakeClickListener {
                EditEvent.sendClickSetPageSizeTip()
                jumpToPageSizingFragment()
                hidePageResizingTip()
            })
            binding.tipsResizingClose.setOnClickListener {
                hidePageResizingTip()
            }
            lifecycleScope.launch {
                // 10s 后自动关闭
                delay(10_000)
                if (checkViewBindingValidity()) {
                    hidePageResizingTip()
                }
            }
        }
    }

    private fun hidePageResizingTip() {
        UserUsageConfig.setNeedShowPageResizingTip(currentDoc, false)
        ObjectAnimator.ofFloat(
            binding.tipsResizingContainer,
            "translationX",
            binding.tipsResizingContainer.translationX,
            binding.tipsResizingContainer.width.toFloat()
        ).apply {
            duration = 200
            doOnEnd {
                if (checkViewBindingValidity()) {
                    binding.tipsResizingContainer.isVisible = false
                }
            }
        }.start()
    }

    private fun updateReadingStyle(style: ReadingStyleUtils.ReadingStyle) {
        if (binding.doodle.currentReadStyle == style) return
        binding.thumbnailAndOutlineView.getNoteThumbnailAdapter()
            ?.changePageThumbnailReadStyle(style)
        binding.doodle.changeDoodleViewReadStyle(style)
        val isValidIndex =
            currentDoc.viewingPageIndex >= 0 && currentDoc.viewingPageIndex < currentDoc.pageCount
        /**
         * 1.内置黑色纸张，直接设置其对应的黑色背景
         * 2.阅读模式，根据是否为黑色护眼模式来设置黑色或白色背景
         */
        if (style == ReadingStyleUtils.ReadingStyle.DARK) {
            if (isValidIndex) {
                if (currentDoc[currentDoc.viewingPageIndex].paper.isBuiltinBlack()) {
                    binding.doodle.setBackgroundResource(R.color.black_40)
                } else {
                    binding.doodle.setBackgroundResource(R.color.black_30)
                }
            } else {
                binding.doodle.setBackgroundResource(R.color.black_30)
            }
        } else {
            if (isValidIndex) {
                if (currentDoc[currentDoc.viewingPageIndex].paper.isBuiltinBlack()) {
                    binding.doodle.setBackgroundResource(R.color.black_40)
                } else {
                    binding.doodle.setBackgroundResource(R.color.transparent)
                }
            } else {
                binding.doodle.setBackgroundResource(R.color.transparent)
            }
        }
    }

    private fun initConsoleModule() {
        binding.consoleContainer.visibility =
            if (UserUsageConfig.configConsoleComponentsAvailable) View.VISIBLE else View.GONE
        DigitalInkRecognitionManager.setOnRecognizeResultCallback { digitalInkRawRecognize ->
            if (checkViewBindingValidity()) {
                if (digitalInkRawRecognize.recognizeRet != "noMatch") {
                    ConsoleEvent.sendConsoleRecognizeResult(digitalInkRawRecognize.recognizeRet)
                } else {
                    ConsoleEvent.sendConsoleRecognizeNoMatch()
                }
                ConsoleCommandManager.sendCommand(digitalInkRawRecognize, doodleView)
            }
        }
        binding.consoleUndoRedo.setOnNoUndoAction {
            showConsoleToast(AppUtils.getString(R.string.console_toast_no_undo))
        }
        binding.consoleUndoRedo.setOnNoRedoAction {
            showConsoleToast(AppUtils.getString(R.string.console_toast_no_redo))
        }

        binding.consoleGestureLayer.doOnThreeFingerSpreading = {
            if (UserUsageConfig.configConsoleComponentsAvailable) {
                if (consoleViewModel.isConsoleState.value == true) {
                    consoleViewModel.changeConsoleState(false)
                    binding.consoleNote.apply {
                        isSelected = false
                        changeConsoleNoteStates(isSelected)
                    }
                    binding.console.isSelected = false
                    binding.consoleIcon.isSelected = false
                }
            }
        }

        ConsoleCommandManager.commandExecuteCallback = object : ICommandExecuteCallback {

            override fun onExecute(command: ConsoleCommand) {
                showConsoleToast(command, emptyList())
            }

            override fun onExecute(command: ConsoleCommand, selections: List<Selection>) {
                showConsoleToast(command, selections)
            }

        }
        ConsoleCommandManager.registerCommandReceiver(this)
        ConsoleCommandManager.registerCommandReceiver(binding.doodle)

        consoleViewModel.isConsoleState.observe(viewLifecycleOwner) { isConsoleState ->
            if (isConsoleState) {
                binding.console.isSelected = false
                binding.consoleIcon.isSelected = true
                enterGestureRecognizeMode()
            } else {
                consoleViewModel.changeIsShowConsoleInstructionsView(false)
                adsorptionEdgeViewModel.changeToolBarVisibilityToPersistentState()
                exitGestureRecognizeMode()
            }
        }
        consoleViewModel.isShowConsoleInstructionsView.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                binding.consoleGuide.visibility = View.INVISIBLE
                binding.consoleInstruction.showConsoleInstructionsView()
            } else {
                binding.consoleInstruction.closeConsoleInstructionsView()
            }
        }
        consoleViewModel.isShowConsoleSettingWindow.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                if (consoleSettingWindow == null) {
                    consoleSettingWindow = ConsoleSettingWindow(requireContext())
                }
                binding.noteMajorToolLayout.more.post {
                    if (activity != null && !activity?.isFinishing!!) {
                        if (binding.noteMajorToolLayout.more.windowToken != null) {
                            consoleSettingWindow!!.apply {
                                onMenuClickAction = {
                                    binding.consoleContainer.visibility =
                                        if (UserUsageConfig.configConsoleComponentsAvailable) View.VISIBLE else View.GONE
                                }
                                setOnDismissListener {
                                    consoleViewModel.changeIsShowConsoleSettingWindow(false)
                                }
                                showMoreToolWindowAtTopRight(this)
                            }
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged", "ClickableViewAccessibility")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (invalidCurrentDoc()) return
        resetPhotoCropDialog()
        loadAndShowBannerAd(binding.bannerAdContainer) {
            resetDraftSizeAndLocation()
        }
        initConsoleModule()
        val isShowGraphToolWindow = arguments?.getBoolean("isShowGraphToolWindow")
        val isShowDraftPaperMorePopupGuide = arguments?.getBoolean("isShowDraftPaperMorePopupGuide")
        if (isShowDraftPaperMorePopupGuide == true && noteViewModel.checkIsShowMoreToolWindow() && UserUsageConfig.isNeedShowDraftPaperMorePopupGuide) {
            noteViewModel.isShowMoreWindow.value = true
        }
        arguments?.clear()

        sidebarAnimationTime = if (sideBarViewModel.currentSidebarState.value != Sidebar.NONE) {
            NO_ANIMATION_DURATION
        } else {
            ANIMATION_DURATION_TIME
        }
        snippetAnimationTime = NO_ANIMATION_DURATION
        draftPaperAnimationTime = NO_ANIMATION_DURATION
        fillStatusBarPadding(binding.root)
        customToolsViewModel.getCustomTools { customTools ->
            customToolsViewModel.updateTargetCustomTools(customTools)
        }

        val permissionErrorAlertDialog =
            parentFragmentManager.findFragmentByTag(PERMISSION_ERROR_TAG)
        if (permissionErrorAlertDialog != null && permissionErrorAlertDialog is AlertDialog) {
            permissionErrorAlertDialog.setPositiveBtnListener {
                PermissionRequester.jumpToSetting(this@NoteEditorFragment)
                RecordEvent.sendRecordMicrophonePermissionStatus("setting")
            }
            permissionErrorAlertDialog.setNegativeBtnListener {
                RecordEvent.sendRecordMicrophonePermissionStatus("cancel")
            }
        }

        val deleteThumbnailTipDialog =
            parentFragmentManager.findFragmentByTag(DELETETHUMBNAIL_WITH_OUTLINE_ALERT_DIALOG)
        if (deleteThumbnailTipDialog != null && deleteThumbnailTipDialog is AlertDialog) {
            deleteThumbnailTipDialog.setNegativeBtnListener {
                thumbnailAndOutlineViewStatusViewModel.deleteThumbnailPosition?.let { position ->
                    deleteThumbnailAction(position)
                }
                thumbnailAndOutlineViewStatusViewModel.deleteThumbnailPosition = null
            }
            deleteThumbnailTipDialog.setPositiveBtnListener {
                thumbnailAndOutlineViewStatusViewModel.deleteThumbnailPosition = null
            }
        }

        initNoteMaterial()
        snippetViewModel.setCurrentDoc(currentDoc)
        searchViewModel.setCurrentDoc(currentDoc)
        initNoteSearch()
        initNoteAi()

        initMajorToolLayout()

        if (savedInstanceState == null) {
            recordViewModel.setCurrentDoc(currentDoc)
            recordViewModel.checkShowRecordControlView(currentDoc)
        } else {
            showDeleteOutlineInBatchesAlertDialog(true)
        }
        imageFetchViewModel.sidebarPictures.observe(viewLifecycleOwner) {
            val adapter = binding.minorToolRecyclerView.adapter
            if (adapter is NoteToolPictureAdapter) {
                adapter.setImageList(it)
            }
        }
        sideBarViewModel.currentSidebarState.observe(viewLifecycleOwner) { currentSidebarStatus ->
            updateHideToolsShowIcon(
                binding.doodle.inputMode,
                UserUsageConfig.showHideToolsEnterIcon
            )
            when (currentSidebarStatus) {
                Sidebar.ADD_PAGE -> {
                    binding.noteAddPageLayout.post {
                        if (checkViewBindingValidity()) {
                            changeAddPageViewPosition(true, sidebarAnimationTime) {
                                changeAddPageState(true)
                            }
                        }
                    }
                }

                Sidebar.MATERIAL -> {
                    binding.noteMaterialView.post {
                        if (checkViewBindingValidity()) {
                            changeMaterialViewPosition(true, sidebarAnimationTime) {
                                changeMaterialState(true)
                            }
                        }
                    }
                }

                Sidebar.DOC_SEARCH -> {
                    noteSearchFrameLayout?.post {
                        if (checkViewBindingValidity()) {
                            changeSearchViewPosition(true, sidebarAnimationTime)
                        }
                    }
                }

                Sidebar.WEB_SEARCH -> {
                    binding.webSidebarView.post {
                        if (checkViewBindingValidity()) {
                            changeWebCommonSidebarViewPosition(true, sidebarAnimationTime)
                        }
                    }
                }

                Sidebar.TRANSLATE -> {
                    binding.webSidebarView.post {
                        if (checkViewBindingValidity()) {
                            changeWebCommonSidebarViewPosition(true, sidebarAnimationTime)
                        }
                    }
                }

                Sidebar.AI -> {
                    binding.aiContainer.post {
                        if (checkViewBindingValidity()) {
                            showAIFragment()
                            changeAiViewPosition(true, sidebarAnimationTime) {
                                changeAIState(true)
                            }
                        }
                    }
                }

                else -> {}
            }
        }
        noteViewModel.thumbnailListViewState.observe(viewLifecycleOwner) { state ->
            if (!state.isStill()) throw Exception("Unexpected thumbnail list view state: $state")
            if (!thumbnailListViewState.isStill() || !snippetListViewState.isStill()) return@observe
            val multiWindowCondition =
                DimensionUtil.getOrientation(requireContext()) == Configuration.ORIENTATION_LANDSCAPE
                        && DimensionUtil.getMultiWindowRatio(requireContext()) <= DimensionUtil.ONE_THIRD_SCREEN_LIMIT
            if (multiWindowCondition) {
                if (state == NoteViewModel.ThumbnailListViewState.SHOWN) {
                    binding.doodle.post {
                        safeNavigate(R.id.show_thumbnail)
                    }
                }
            } else {
                binding.noteMajorToolLayout.showThumbnail.isSelected =
                    state == NoteViewModel.ThumbnailListViewState.SHOWN
                binding.thumbnailAndOutlineView.post {
                    if (!checkViewBindingValidity()) return@post
                    processThumbnailAndSnippetAnimation()
                }
            }
        }

        snippetViewModel.snippetListViewState.observe(viewLifecycleOwner) { state ->
            if (!state.isFinal()) throw Exception("Unexpected thumbnail list view state: $state")
            if (!thumbnailListViewState.isStill() || !snippetListViewState.isStill()) return@observe
            val multiWindowCondition = DimensionUtil.isLandAndOneThirdScreen(context)
                    || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)
            if (multiWindowCondition) return@observe
            binding.noteMajorToolLayout.showSnippet.isSelected =
                state != SnippetViewModel.SnippetListViewState.HIDDEN
            binding.pageSnippetList.post {
                if (!checkViewBindingValidity()) return@post
                processThumbnailAndSnippetAnimation()
            }
            if (state == SnippetViewModel.SnippetListViewState.HIDDEN) {
                snippetViewModel.exitSearchMode()
                updateHideToolsShowIcon(
                    binding.doodle.inputMode,
                    UserUsageConfig.showHideToolsEnterIcon
                )
            }
            if (state == SnippetViewModel.SnippetListViewState.SHOWN) {
                updateHideToolsShowIcon(
                    binding.doodle.inputMode,
                    UserUsageConfig.showHideToolsEnterIcon
                )
            }
        }

        snippetViewModel.needTranslateSnippet.observe(viewLifecycleOwner) { noteSnippet ->
            translateSnippet(noteSnippet)
        }

        snippetViewModel.allCommonLabelList.observe(viewLifecycleOwner) {
            LogHelper.d(defaultTag, "allCommonLabels: $it")
        }

        snippetViewModel.currentSelectSnippetPosition.observe(viewLifecycleOwner) {
            binding.pageSnippetList.refreshCurrentSelectedSnippet(it)
            if (it == null) {
                doodleView.tryClosePopUpWindows()
            }
        }

        snippetManagerViewModel.isShowManagerSnippetTag.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                val fragmentManager = activity?.supportFragmentManager ?: return@observe
                val dialog = fragmentManager.findFragmentByTag(NoteSnippetManagerDialog.TAG)
                if (dialog is NoteSnippetManagerDialog && dialog.isVisible) return@observe
                noteSnippetManagerDialog = NoteSnippetManagerDialog().apply {
                    setOnLabelDeleteAction { label ->
                        snippetViewModel.resetCurrentSelectedLabelWithCurrentSelectedLabelDeleted(
                            label
                        )
                    }
                    setOnDismissListener {
                        snippetManagerViewModel.changeManagerSnippetTagVisible(false)
                        if (<EMAIL>) {
                            val snippetId =
                                doodleView.doodleEditLayer.mSnippetView?.noteSnippet?.snippetId?.toString()
                            if (snippetId != null) {
                                snippetViewModel.requestNoteSnippetTags(snippetId) { snippet, allTags, addedTags ->
                                    doodleView.doodleEditLayer.mSnippetView?.apply {
                                        setSnippetAndTagsInfo(snippet, allTags, addedTags);
                                        showToolWindow();
                                    }
                                }
                            }
                        }
                    }
                    onTagSelectChanged = {
                        if (snippetViewModel.inSearchMode) {
                            search(snippetViewModel.searchingKeyword)
                        } else {
                            refreshCurrentSnippetItemList()
                        }
                    }
                    show(fragmentManager, NoteSnippetManagerDialog.TAG)
                }
            } else {
                noteSnippetManagerDialog?.dismiss()
            }
        }

        snippetViewModel.snippetListIsSelect.observe(viewLifecycleOwner) { isSelect ->
            if (isSelect) {
                snippetViewModel.changeCurrentSelectSnippetPosition(-1)
                binding.pageSnippetList.refreshSelectedNumber(snippetViewModel.selectedSnippetIndex.size)
            } else {
                binding.pageSnippetList.getAllSnippets().forEach {
                    it.isSelected = false
                }
                snippetViewModel.selectedSnippetIndex.clear()
            }
            binding.pageSnippetList.setOptionSelectBtnSelectState(isSelect)
            binding.pageSnippetList.refreshCurrentMode(isSelect)
            binding.pageSnippetList.setColorSelectorAndLabelSelectorIsClickable(!isSelect)
        }

        recordViewModel.isShowRecordWindow.observe(viewLifecycleOwner) { isShowRecordWindow: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (isShowRecordWindow) {
                    showRecordListWindow()
                } else {
                    if (recordListWindow != null && recordListWindow!!.isShowing) {
                        recordListWindow!!.dismiss()
                    }
                }
            }
        }

        editorSwitchDocumentViewModel.documentUpgradeDialogShowing.observe(viewLifecycleOwner) { showing ->
            val dialog = childFragmentManager.findFragmentByTag(DataUpgradeDialog.TAG)
            if (showing && dialog == null) {
                DataUpgradeDialog().show(childFragmentManager, DataUpgradeDialog.TAG)
            } else if (!showing && dialog is DataUpgradeDialog) {
                dialog.dismiss()
            }
        }

        editorSwitchDocumentViewModel.switchDocumentAndStatus.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            val document = it.first
            if (document == currentDoc) return@observe
            editorSwitchDocumentViewModel.resetSwitchDocumentAndStatus()
            hideLoadingDialog()
            when (it.second) {
                DocumentManager.DocumentStatus.OK -> {
                    lifecycleScope.launch(Dispatchers.Main) {
                        commandViewModel.clearAllStack()

                        RecordViewModel.currentEditRecordTagUUID = null
                        RecordViewModel.currentSelectedRecordTagUUID = null
                        RecordViewModel.currentEditRecordUUID = null
                        recordViewModel.stopRecord()
                        recordViewModel.stopPlay()
                        if (recordViewModel.isShowRecordControlView.value == true) {
                            recordViewModel.switchIsShowRecordControlView()
                        }

                        DoodlePageLoader.ensureCurrentLoadPages(
                            currentDoc,
                            setOf(currentDoc[currentDoc.viewingPageIndex])
                        )

                        currentDoc = document

                        currentDoc.recordManager.loadRecordsInfo()

                        noteViewModel.currentDoc = currentDoc
                        recordViewModel.setCurrentDoc(currentDoc)

                        searchViewModel.resetSearchJob()
                        searchViewModel.resetSearchState()
                        searchViewModel.resetSearchType()
                        searchViewModel.resetCurrentDocSearchResult()
                        searchViewModel.setCurrentDoc(currentDoc)
                        refreshSearchBackPageIcon(null)

                        snippetViewModel.setCurrentDoc(currentDoc)

                        initThumbnailAndOutlineView()
                        binding.noteTitle.text = currentDoc.title

                        setPage(currentDoc[currentDoc.viewingPageIndex])

                        AddPageSource.document = currentDoc
                        AddPageSource.paper = editorViewModel.page.paper
                    }
                }

                DocumentManager.DocumentStatus.ERROR -> {
                    HomeEvent.sendNoteBookUnusualEvent("three")
                    HomeEvent.sendExceptionalContactShow()
                    showFileCorruptedDialog(document)
                }

                DocumentManager.DocumentStatus.PAGE_MISSING -> {
                    showDocumentPageMissingDialog()
                    HomeEvent.sendExceptionalContactShow()
                }
            }
        }

        //更多弹窗监听
        noteViewModel.isShowMoreWindow.observe(viewLifecycleOwner) { value: Boolean ->
            binding.noteMajorToolLayout.more.isSelected = value
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showMoreToolWindow()
                } else {
                    if (moreToolWindow != null && moreToolWindow!!.isShowing) {
                        moreToolWindow!!.dismiss()
                    }
                }
            }
        }

        noteViewModel.isShowRecognizeWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showPenToolsWindow()
                } else {
                    if (customSelectToolsWindow != null && customSelectToolsWindow!!.isShowing) {
                        customSelectToolsWindow!!.dismiss()
                    }
                }
            }
        }

        noteViewModel.isShowLassoLanguageWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (customSelectToolsWindow != null && customSelectToolsWindow!!.isShowing) {
                    customSelectToolsWindow!!.dismiss()
                }
                if (value) {
                    showLassoLanguageWindow()
                }
            }
        }

        noteViewModel.isShowReadingStyleSelectWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (customSelectToolsWindow != null && customSelectToolsWindow!!.isShowing) {
                    customSelectToolsWindow!!.dismiss()
                }
                if (value) {
                    showReadingStyleSelectWindow()
                }
            }
        }

        toolAttributesViewModel.currentReadingStyle.observe(viewLifecycleOwner) { value: ReadingStyleUtils.ReadingStyle ->
            updateReadingStyle(value)
        }

        toolAttributesViewModel.highlighterAttributes.observe(viewLifecycleOwner) { attributes ->
            binding.noteMajorToolLayout.updateIconColor(binding.doodle.inputMode)
        }

        toolAttributesViewModel.penAttributes.observe(viewLifecycleOwner) { attrs ->
            binding.noteMajorToolLayout.updateIconColor(binding.doodle.inputMode)
        }

        noteViewModel.isShowGraffitiWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showGraffitiToolsWindow()
                } else {
                    if (customSelectToolsWindow != null && customSelectToolsWindow!!.isShowing) {
                        customSelectToolsWindow!!.dismiss()
                    }
                }
            }
        }

        noteViewModel.isShowGraphWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showGraphToolsWindow()
                } else {
                    customSelectToolsWindow?.let {
                        if (it.isShowing) {
                            it.dismiss()
                        }
                    }
                }
            }
        }

        noteViewModel.isShowHighlighterWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showHighlighterToolsWindow()
                } else {
                    if (customSelectToolsWindow != null && customSelectToolsWindow!!.isShowing) {
                        customSelectToolsWindow!!.dismiss()
                    }
                }
            }
        }

        customToolsViewModel.customSelectToolWindowIsShow.observe(viewLifecycleOwner) { isShow ->
            lifecycleScope.launchWhenResumed {
                if (isShow) {
                    showHideDoodleToolsWindow()
                } else {
                    if (customSelectToolsWindow != null && customSelectToolsWindow!!.isShowing) {
                        customSelectToolsWindow!!.dismiss()
                    }
                }
            }
        }

        noteViewModel.isShowConsoleGuideWindow.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                showConsoleGuideWindow()
            }
        }

        //缩略图列表的更多弹窗的展示和隐藏
        noteViewModel.pageThumbnailActionWindowStatus.observe(viewLifecycleOwner) { status ->
            if (status.needShow) {
                val position =
                    noteViewModel.pageThumbnailActionWindowStatus.value!!.listPosition
                popWindow =
                    OverviewActionWindow(requireContext(), position, this, noteViewModel)
                binding.thumbnailAndOutlineView.post {
                    if (!checkViewBindingValidity()) {
                        return@post
                    }
                    val item =
                        binding.thumbnailAndOutlineView.findViewInThumbnailByPosition(position)
                    if (item == null) {
                        //有时可能会没找到相对应的item
                        //需要清除掉侧边栏信息 否则分屏会再次显示更多弹窗
                        noteViewModel.pageThumbnailActionWindowStatus.postValue(
                            ThumbnailActionWindowStatus(
                                false,
                                0
                            )
                        )
                        return@post
                    }
                    val anchor = item.findViewById<ImageView>(R.id.more)
                    val location = IntArray(2)
                    anchor.getLocationInWindow(location)
                    val anchorToBottom =
                        binding.thumbnailAndOutlineView.getThumbnailListViewHeight() - item.top - anchor.bottom
                    val yoff = resources.getDimension(R.dimen.dp_180).toInt()
                    val xoff = if (KiloApp.isLayoutRtl) location[0] - popWindow!!.width
                    else location[0] + resources.getDimension(R.dimen.dp_60).toInt()
                    val popWindowAfterAnchorBottom = popWindow!!.height - yoff
                    //如果下方的距离小于popWindow高 - yoff，会将popWindow向上移动
                    if (anchorToBottom < popWindowAfterAnchorBottom) {
                        popWindow!!.showAtLocation(
                            anchor,
                            Gravity.NO_GRAVITY,
                            xoff,
                            location[1] - (popWindow!!.height / 2) + anchor.height / 2 + popWindowAfterAnchorBottom - anchorToBottom
                        )
                    } else {
                        popWindow!!.showAtLocation(
                            anchor,
                            Gravity.NO_GRAVITY,
                            xoff,
                            location[1] - (popWindow!!.height / 2) + anchor.height / 2
                        )
                    }
                }
                popWindow!!.setOnDismissListener {
                    noteViewModel.pageThumbnailActionWindowStatus.postValue(
                        ThumbnailActionWindowStatus(false, 0)
                    )
                }
            }
        }

        binding.minorToolRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.minorToolRecyclerView.itemAnimator = null

        binding.suppressibleToolRecyclerView.layoutManager =
            LinearLayoutManager(requireContext())
        binding.suppressibleToolRecyclerView.itemAnimator = null

        editorViewModel.apply {
            fullScreen.observe(viewLifecycleOwner) { fullScreen ->
                EditEvent.sendEditReadUsage(fullScreen)
                binding.blurView.isVisible = !fullScreen
                if (BuildConfig.DEBUG_MODE) {
                    binding.noteTitle.isVisible = !fullScreen
                }
                binding.pageIndicator.isVisible = !fullScreen

                if (fullScreen) {
                    noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
                    snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
                    changeCurrentSidebarStatus(Sidebar.NONE)
                }

                if (fullScreen) {
                    //隐藏音频播放器
                    if (recordViewModel.isShowRecordControlView.value == true && recordViewModel.isPlaying.value != true && recordViewModel.isShowRecordWindow.value != true) {
                        recordViewModel.switchIsShowRecordControlView()
                    }
                }
            }
        }


        editorModeViewModel.editorMode.observe(viewLifecycleOwner) { mode ->
            if (!checkViewBindingValidity()) return@observe
            changeToolbar(mode)
            binding.noteMajorToolLayout.updateSelectedInputIcon(mode)
            updateHideToolsShowIcon(mode, UserUsageConfig.showHideToolsEnterIcon)
            if (mode != InputMode.VIEW && editorViewModel.fullScreen.value == true) {
                editorViewModel.switchFullScreen()
            }
            binding.suppressibleToolRecyclerView.isVisible =
                mode == InputMode.GRAFFITI || mode == InputMode.OUTLINEPEN || mode == InputMode.LINEDRAW
            changeTurnPageEnable(mode == InputMode.VIEW)
            if (mode == InputMode.VIEW) {
                toolAttributesViewModel.currentReadingStyle.value?.let { currentReadingStyle ->
                    toolAttributesViewModel.changeCurrentReadingStyle(currentReadingStyle)
                }
            } else {
                if (binding.doodle.currentReadStyle != ReadingStyleUtils.ReadingStyle.DEFAULT) {
                    updateReadingStyle(ReadingStyleUtils.ReadingStyle.DEFAULT)
                }
            }
            binding.toolBar.setEdge(
                adsorptionEdgeViewModel.edge,
            )
            UserUsageConfig.inputMode = mode
            binding.minorToolRecyclerView.stopScroll()
            when (mode) {
                InputMode.DRAW -> {
                    EditEvent.sendEditPenClick()
                    UserUsageConfig.lastSelectedPenMode = InputMode.DRAW
                    binding.doodle.inputMode = InputMode.DRAW
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolPenColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                            ).apply {
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showPenSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultPenPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setPenColor(color)
                                }
                            },
                            ToolPenSizeAdapter(
                                UserUsageConfig.penCurrentSizePosition,
                                UserUsageConfig.penPreferenceSizes,
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                                itemPadding = 0
                            ).apply {
                                doOnSizeChanged {
                                    toolAttributesViewModel.setPenSize(it)
                                }

                                doOnSizePositionChanged {
                                    UserUsageConfig.penCurrentSizePosition = it
                                }

                                doOnPreferredSizesChanged {
                                    UserUsageConfig.penPreferenceSizes = it
                                }

                                doOnCustomSize {
                                    val penAttributes =
                                        toolAttributesViewModel.penAttributes.value
                                            ?: PenAttributes.DEFAULT
                                    selectPenSizeWindow =
                                        SelectPenSizeWindow(
                                            requireContext(),
                                            penAttributes.width.getMmValue(),
                                            PenAttributes.MAX_WIDTH.getMmValue(),
                                            PenAttributes.MIN_WIDTH.getMmValue(),
                                            penAttributes.color
                                        ) {
                                            val currentSize = MmSize(it)
                                            toolAttributesViewModel.setPenSize(currentSize)
                                            this.updateSize(size = currentSize)
                                        }.apply {
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.PEN -> {
                    binding.doodle.inputMode = InputMode.PEN
                    UserUsageConfig.lastSelectedPenMode = InputMode.PEN
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolPenColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                            ).apply {
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showPenSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultPenPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setPenColor(color)
                                }
                            },
                            ToolPenSizeAdapter(
                                UserUsageConfig.penCurrentSizePosition,
                                UserUsageConfig.penPreferenceSizes,
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                                itemPadding = 0
                            ).apply {
                                doOnSizeChanged {
                                    toolAttributesViewModel.setPenSize(it)
                                }

                                doOnSizePositionChanged {
                                    UserUsageConfig.penCurrentSizePosition = it
                                }

                                doOnPreferredSizesChanged {
                                    UserUsageConfig.penPreferenceSizes = it
                                }

                                doOnCustomSize {
                                    val penAttributes =
                                        toolAttributesViewModel.penAttributes.value
                                            ?: PenAttributes.DEFAULT
                                    selectPenSizeWindow =
                                        SelectPenSizeWindow(
                                            requireContext(),
                                            penAttributes.width.getMmValue(),
                                            PenAttributes.MAX_WIDTH.getMmValue(),
                                            PenAttributes.MIN_WIDTH.getMmValue(),
                                            penAttributes.color
                                        ) {
                                            val currentSize = MmSize(it)
                                            toolAttributesViewModel.setPenSize(currentSize)
                                            this.updateSize(size = currentSize)
                                        }.apply {
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.PAINTBRUSH -> {
                    binding.doodle.inputMode = InputMode.PAINTBRUSH
                    UserUsageConfig.lastSelectedPenMode = InputMode.PAINTBRUSH
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolPenColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                            ).apply {
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showPenSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultPenPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setPenColor(color)
                                }
                            },
                            ToolPenSizeAdapter(
                                UserUsageConfig.penCurrentSizePosition,
                                UserUsageConfig.penPreferenceSizes,
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                                itemPadding = 0
                            ).apply {
                                doOnSizeChanged {
                                    toolAttributesViewModel.setPenSize(it)
                                }

                                doOnSizePositionChanged {
                                    UserUsageConfig.penCurrentSizePosition = it
                                }

                                doOnPreferredSizesChanged {
                                    UserUsageConfig.penPreferenceSizes = it
                                }

                                doOnCustomSize {
                                    val penAttributes =
                                        toolAttributesViewModel.penAttributes.value
                                            ?: PenAttributes.DEFAULT
                                    selectPenSizeWindow =
                                        SelectPenSizeWindow(
                                            requireContext(),
                                            penAttributes.width.getMmValue(),
                                            PenAttributes.MAX_WIDTH.getMmValue(),
                                            PenAttributes.MIN_WIDTH.getMmValue(),
                                            penAttributes.color
                                        ) {
                                            val currentSize = MmSize(it)
                                            toolAttributesViewModel.setPenSize(currentSize)
                                            this.updateSize(size = currentSize)
                                        }.apply {
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.HIGHLIGHTER -> {
                    EditEvent.sendEditHighlighterClick()
                    binding.doodle.inputMode = InputMode.HIGHLIGHTER
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolHighlighterColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                            ).apply {
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showHighlighterSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultHighlighterPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setHighlighterColor(color)
                                }
                            },
                            ToolHighlighterSizeAdapter(
                                requireContext(),
                                UserUsageConfig.highlighterCurrentSizePosition,
                                UserUsageConfig.highlighterPreferenceSizes,
                                resources.getDimensionPixelSize(R.dimen.dp_54)
                            ).apply {
                                doOnSizeChanged {
                                    toolAttributesViewModel.setHighlighterSize(it)
                                }

                                doOnSizePositionChanged {
                                    UserUsageConfig.highlighterCurrentSizePosition = it
                                }

                                doOnPreferredSizesChanged {
                                    UserUsageConfig.highlighterPreferenceSizes = it
                                }

                                doOnCustomSize {
                                    val highlighterAttributes =
                                        toolAttributesViewModel.highlighterAttributes.value
                                            ?: HighlighterAttributes.DEFAULT
                                    selectPenSizeWindow =
                                        SelectPenSizeWindow(
                                            requireContext(),
                                            highlighterAttributes.width.getMmValue(),
                                            HighlighterAttributes.MAX_WIDTH.getMmValue(),
                                            HighlighterAttributes.MIN_WIDTH.getMmValue(),
                                            highlighterAttributes.color
                                        ) {
                                            val currentSize = MmSize(it)
                                            toolAttributesViewModel.setHighlighterSize(
                                                currentSize
                                            )
                                            this.updateSize(size = currentSize)
                                        }.apply {
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.ERASER -> {
                    EditEvent.sendEditEraserClick()
                    binding.doodle.eraseType = VisualStrokeErase.EraseType.Erase_Whole
                    binding.doodle.inputMode = InputMode.ERASER

                    val firstItem = NoteToolEraserAdapter.EraserItem(
                        resources.getDimensionPixelSize(R.dimen.dp_54),
                        resources.getDimensionPixelSize(R.dimen.dp_4)
                    )
                    val secondItem = NoteToolEraserAdapter.EraserItem(
                        resources.getDimensionPixelSize(R.dimen.dp_54),
                        resources.getDimensionPixelSize(R.dimen.dp_9)
                    )
                    val thirdItem = NoteToolEraserAdapter.EraserItem(
                        resources.getDimensionPixelSize(R.dimen.dp_54),
                        resources.getDimensionPixelSize(R.dimen.dp_21)
                    )
                    binding.minorToolRecyclerView.adapter =
                        NoteToolEraserAdapter(
                            requireContext(),
                            firstItem,
                            secondItem,
                            thirdItem,
                            false,
                            toolAttributesViewModel.eraserAttributes
                        )
                }

                InputMode.LASSO -> {
                    EditEvent.sendEditLassoToolMode()
                    binding.doodle.inputMode = InputMode.LASSO
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            NoteToolLassoStyleAdapter(
                                resources.getDimensionPixelSize(R.dimen.dp_54),
                                UserUsageConfig.lassoStyle
                            ) { newStyle ->
                                toolAttributesViewModel.setLassoStyle(newStyle)
                            },
                            ToolDividerAdapter(),
                            NoteToolLassoAdapter(
                                requireContext(),
                                resources.getDimensionPixelSize(R.dimen.dp_54),
                                toolAttributesViewModel.lassoAttributes,
                                listOf(
                                    R.drawable.note_main_sidebar_lasso_stroke_normal,
                                    R.drawable.note_main_sidebar_lasso_pic,
                                    R.drawable.note_main_sidebar_lasso_text,
                                    R.drawable.note_main_sidebar_lasso_graffiti,
                                    R.drawable.note_main_sidebar_lasso_high_lighter,
                                    R.drawable.note_main_sidebar_lasso_graph
                                )
                            )
                        )
                }

                InputMode.IMAGE -> {
                    binding.doodle.inputMode = InputMode.IMAGE

                    val noteToolPictureAdapter = NoteToolPictureAdapter(
                        requireContext(),
                        resources.getDimensionPixelSize(R.dimen.dp_54)
                    ) { path ->
                        insertImageElement(path)
                        EditEvent.sendEditPictureUsage("sidebar")
                    }
                    binding.minorToolRecyclerView.adapter = noteToolPictureAdapter
                    EditEvent.sendEditPictureClick()
                    val needRequestPermission =
                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                            Manifest.permission.READ_EXTERNAL_STORAGE
                        } else {
                            Manifest.permission.READ_MEDIA_IMAGES
                        }

                    if (PermissionRequester.checkPermissionState(
                            this,
                            needRequestPermission
                        ) == PermissionRequester.PermissionState.PERMISSION_GRANTED
                    ) {
                        imageFetchViewModel.loadSidebarPictures()
                    }
                }

                InputMode.TEXT -> {
                    EditEvent.sendEditTextInputClick()
                    binding.doodle.inputMode = InputMode.TEXT
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolTextColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                            ).apply {
                                colorClickAction = { _, _, _ ->
                                    EditEvent.sendEditTextColorClick()
                                }
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showTextSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultPenPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setTextColor(color)
                                }
                            },
                            ToolTextSizeAdapter(
                                requireContext(),
                                toolAttributesViewModel.textAttributes.value!!.size.getPtValue()
                                    .toInt(),
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                                textSize = resources.getDimensionPixelSize(R.dimen.dp_33),
                                itemPadding = 0
                            ).apply {
                                onSizeChangeAction = {
                                    toolAttributesViewModel.setTextSize(PtSize(it.toFloat()))
                                }
                                customSizeAction = {
                                    selectTextSizeWindow = SelectTextSizeWindow(
                                        requireContext(),
                                        toolAttributesViewModel.textAttributes.value!!.size.getPtValue()
                                            .toInt()
                                    ) {
                                        this.updateTextSize(it)
                                    }.apply {
                                        showAsBubble(
                                            it,
                                            adsorptionEdgeViewModel.getBubbleOrientation()
                                        )
                                    }
                                }
                            },
                            ToolTextFontAdapter(
                                toolAttributesViewModel.fontAttributes.value?.fontInfo
                                    ?: UserUsageConfig.fontAttributes.fontInfo
                            ) { adapter, view ->
                                showFontListWindow(adapter, view)
                            },
                            ToolTextParagraphAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_54)
                            ).apply {
                                doOnCustomParagraphAction {
                                    EditEvent.sendEditTextParagraphClick()
                                    selectParagraphStyleWindow =
                                        SelectParagraphStyleWindow(
                                            requireContext(),
                                            toolAttributesViewModel.textAttributes.value!!.textGravity
                                        ) {
                                            toolAttributesViewModel.setTextGravity(it)
                                            this.updateTextParagraphIcon(it)
                                        }.apply {
                                            EditEvent.sendEditTextParagraphShow()
                                            inputMethodMode = PopupWindow.INPUT_METHOD_NOT_NEEDED
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.GRAFFITI, InputMode.OUTLINEPEN, InputMode.LINEDRAW -> {
                    val graffitiPatternStyleList =
                        getDefaultGraffitiPatternPreferenceStyleList()
                    val graffitiOutlinePenStyleList =
                        getDefaultGraffitiOutlinePenPreferenceStyleList()
                    val graffitiLineDrawPenStyleList =
                        getDefaultGraffitiLineDrawingPenPreferenceStyleList()
                    when (UserUsageConfig.graffitiCurrentToolTypePosition) {
                        GraffitiToolType.PATTERN.ordinal -> binding.doodle.inputMode =
                            InputMode.GRAFFITI

                        GraffitiToolType.OUTLINEPEN.ordinal -> binding.doodle.inputMode =
                            InputMode.OUTLINEPEN

                        GraffitiToolType.LINEDRAWPEN.ordinal -> binding.doodle.inputMode =
                            InputMode.LINEDRAW
                    }
                    binding.minorToolRecyclerView.adapter = ConcatAdapter(
                        ToolGraffitiStyleAdapter(
                            UserUsageConfig.graffitiCurrentStylePosition,
                            graffitiPatternStyleList,
                            outlinePenStyleList = null,
                            lineDrawPenStyleList = null,
                            itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                            GraffitiToolType.PATTERN.ordinal
                        ).apply {
                            setStyleChangedActionAndGetInitialStyle { styleIndex ->
                                if (UserUsageConfig.graffitiCurrentToolTypePosition == GraffitiToolType.PATTERN.ordinal) {
                                    binding.doodle.inputMode = InputMode.GRAFFITI
                                    toolAttributesViewModel.setGraffitiPatternStyle(styleIndex)
                                    (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.forEach {
                                        if (it is ToolGraffitiStyleAdapter) {
                                            it.clearCurrentSelectedState()
                                        }
                                    }
                                }
                            }
                        },
                        ToolGraffitiStyleAdapter(
                            UserUsageConfig.graffitiCurrentStylePosition,
                            patternStyleList = null,
                            graffitiOutlinePenStyleList,
                            lineDrawPenStyleList = null,
                            itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                            GraffitiToolType.OUTLINEPEN.ordinal
                        ).apply {
                            setStyleChangedActionAndGetInitialStyle { styleIndex ->
                                if (UserUsageConfig.graffitiCurrentToolTypePosition == GraffitiToolType.OUTLINEPEN.ordinal) {
                                    binding.doodle.inputMode = InputMode.OUTLINEPEN
                                    toolAttributesViewModel.setGraffitiOutlineStyle(styleIndex)
                                    (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.forEach {
                                        if (it is ToolGraffitiStyleAdapter) {
                                            it.clearCurrentSelectedState()
                                        }
                                    }
                                }
                            }
                        },
                        ToolGraffitiStyleAdapter(
                            UserUsageConfig.graffitiCurrentStylePosition,
                            patternStyleList = null,
                            outlinePenStyleList = null,
                            graffitiLineDrawPenStyleList,
                            itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                            GraffitiToolType.LINEDRAWPEN.ordinal
                        ).apply {
                            setStyleChangedActionAndGetInitialStyle { styleIndex ->
                                if (UserUsageConfig.graffitiCurrentToolTypePosition == GraffitiToolType.LINEDRAWPEN.ordinal) {
                                    binding.doodle.inputMode = InputMode.LINEDRAW
                                    toolAttributesViewModel.setLineDrawPenStyle(styleIndex)
                                    (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.forEach {
                                        if (it is ToolGraffitiStyleAdapter) {
                                            it.clearCurrentSelectedState()
                                        }
                                    }
                                }
                            }
                        }
                    )
                    binding.suppressibleToolRecyclerView.adapter = ToolGraffitiSizeAdapter(
                        requireContext(),
                        UserUsageConfig.graffitiCurrentSizePosition,
                        UserUsageConfig.graffitiPreferenceSizes,
                        resources.getDimensionPixelSize(R.dimen.dp_54)
                    ).apply {
                        doOnSizeChanged {
                            EditEvent.sendGraffitipenThickness(it.toString())
                            toolAttributesViewModel.setGraffitiSize(it)
                        }

                        doOnSizePositionChanged {
                            UserUsageConfig.graffitiCurrentSizePosition = it
                        }

                        doOnPreferredSizesChanged {
                            UserUsageConfig.graffitiPreferenceSizes = it
                        }

                        doOnCustomSize {
                            val graffitiAttributes =
                                toolAttributesViewModel.graffitiPatternAttributes.value
                                    ?: GraffitiAttributes.DEFAULT
                            selectPenSizeWindow =
                                SelectPenSizeWindow(
                                    requireContext(),
                                    graffitiAttributes.width.getMmValue(),
                                    GraffitiAttributes.MAX_WIDTH.getMmValue(),
                                    GraffitiAttributes.MIN_WIDTH.getMmValue(),
                                    Color.BLACK
                                ) {
                                    val currentSize = MmSize(it)
                                    toolAttributesViewModel.setGraffitiSize(currentSize)
                                    this.updateSize(size = currentSize)
                                }.apply {
                                    showAsBubble(
                                        it,
                                        adsorptionEdgeViewModel.getBubbleOrientation()
                                    )
                                    setOnDismissListener {
                                        it.isSelected = false
                                    }
                                }
                        }
                    }
                    when (UserUsageConfig.graffitiCurrentToolTypePosition) {
                        GraffitiToolType.PATTERN.ordinal -> binding.minorToolRecyclerView.scrollToPosition(
                            UserUsageConfig.graffitiCurrentStylePosition
                        )

                        GraffitiToolType.OUTLINEPEN.ordinal -> binding.minorToolRecyclerView.scrollToPosition(
                            UserUsageConfig.graffitiCurrentStylePosition + graffitiPatternStyleList.size
                        )

                        GraffitiToolType.LINEDRAWPEN.ordinal -> binding.minorToolRecyclerView.scrollToPosition(
                            UserUsageConfig.graffitiCurrentStylePosition + graffitiPatternStyleList.size + graffitiOutlinePenStyleList.size
                        )
                    }

                }

                InputMode.GRAPH -> {
                    binding.doodle.inputMode = InputMode.GRAPH
                    binding.minorToolRecyclerView.adapter = null
                }

                InputMode.SNIPPET -> {
                    binding.doodle.inputMode = InputMode.SNIPPET
                    binding.minorToolRecyclerView.adapter = null
                }

                InputMode.VIEW -> {
                    EditEvent.sendEditReadClick()
                    adsorptionEdgeViewModel.changeToolBarVisibility(
                        AdsorptionEdgeViewModel.ToolBarState.NONE
                    )
                    binding.doodle.inputMode = InputMode.VIEW
                }

                InputMode.PRESENTATION -> {
                    binding.doodle.inputMode = InputMode.PRESENTATION
                    //后续添加侧边工具栏选项（激光笔的样式选项）
                    when (UserUsageConfig.laserCurrentColorPosition) {
                        DoodlePresentationLayer.LaserColor.GREEN.ordinal -> {
                            doodleView.configPresentationLaserMode(DoodlePresentationLayer.LaserColor.GREEN)
                        }

                        DoodlePresentationLayer.LaserColor.BLUE.ordinal -> {
                            doodleView.configPresentationLaserMode(DoodlePresentationLayer.LaserColor.BLUE)
                        }

                        else -> {
                            doodleView.configPresentationLaserMode(DoodlePresentationLayer.LaserColor.RED)
                        }
                    }
                    binding.minorToolRecyclerView.adapter = ToolLaserColorAdapter(
                        UserUsageConfig.laserCurrentColorPosition,
                        itemSize = resources.getDimensionPixelSize(R.dimen.dp_54),
                        colors = mutableListOf(
                            DoodlePresentationLayer.LaserColor.RED,
                            DoodlePresentationLayer.LaserColor.GREEN,
                            DoodlePresentationLayer.LaserColor.BLUE
                        )
                    ).apply {
                        positionChangedAction = { position ->
                            UserUsageConfig.laserCurrentColorPosition = position
                        }
                        colorChangedAction = { color ->
                            EditEvent.sendLaserColorSelected(color)
                            doodleView.configPresentationLaserMode(color)
                        }
                    }
                }

                InputMode.TAPE -> {
                    binding.doodle.inputMode = InputMode.TAPE
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolTapeStyleAdapter(
                                itemSizeSpecial = resources.getDimensionPixelSize(R.dimen.dp_54),
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_38),
                                context = requireContext(),
                                UserUsageConfig.tapeStyle
                            ).apply {
                                onTapeStyleSelectAction = { position ->
                                    UserUsageConfig.tapeStyle = position
                                }
                            },
                            ToolTapeControlAdapter(
                                requireContext()
                            ).apply {
                                showTapeVipControlWindow { view, isVisible, position ->
                                    if (position <= 1) {
                                        if (isVisible) {
                                            TapeEvent.sendTapeShowAllClick()
                                        } else {
                                            TapeEvent.sendTapeHideAllClick()
                                        }
                                        doodleView.doodleStickyOnTopLayer.setTapeShowMode(
                                            null,
                                            isVisible
                                        )
                                    } else {
                                        TapeEvent.sendTapeControlBtnClick()
                                        noteViewModel.changeTapeStyleControlWindowStates(true)
                                    }
                                }
                            }
                        )
                }

                else -> {}
            }
            draftPaperFragment?.onEditorInputModeChanged(mode)
        }

        if (BuildConfig.DEBUG_MODE) {
            binding.noteTitle.visibility = View.VISIBLE
            binding.noteTitle.text = currentDoc.title
        }

        setToolContainerHeightWhenMultiWindow()

        noteViewModel.isShowUserBenefitDialogFromTapeStyleControlWindow.observe(
            viewLifecycleOwner
        ) { isShow ->
            if (isShow) {
                showUserBenefitDialog(isShow)
            } else {
                vipExclusiveDialog?.dismiss()
            }
        }

        noteViewModel.isShowTapeStyleControlWindow.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                binding.minorToolRecyclerView.post {
                    if (checkViewBindingValidity()) {
                        tapeStyleShowControlWindow =
                            TapeStyleShowControlWindow(
                                requireContext(),
                                UserManager.isVip()
                            ).apply {
                                tapeShowStatesChange { position, tape, isVisible ->
                                    TapeEvent.sendTapeStyleStatesControl(position, isVisible)
                                    doodleView.doodleStickyOnTopLayer.setTapeShowMode(
                                        tape, isVisible
                                    )
                                }
                                setTouchInterceptor { v, event ->
                                    if (event.actionMasked == MotionEvent.ACTION_DOWN) {
                                        val isOuterClick =
                                            contentView.outsideArea(event)
                                        if (isOuterClick) {
                                            noteViewModel.changeTapeStyleControlWindowStates(
                                                false
                                            )
                                            dismiss()
                                            return@setTouchInterceptor true
                                        } else {
                                            if (!UserManager.isVip()) {
                                                noteViewModel.changeUserBenefitDialogState(true)
                                            }
                                        }
                                    }
                                    false
                                }
                                getToolTapeControlView()?.let {
                                    showAsBubble(
                                        it,
                                        adsorptionEdgeViewModel.getBubbleOrientation()
                                    )
                                }
                            }
                    }
                }
            }
        }

        noteViewModel.copyResult.observe(viewLifecycleOwner) {
            if (it.copyStatus == NoteViewModel.CopyStatus.SUCCESS &&
                noteViewModel.pasteResult.value?.pasteStatus == NoteViewModel.PasteStatus.PENDING
            ) {
                if (it.page == null) return@observe
                val position = noteViewModel.pasteResult.value!!.position
                noteViewModel.pastePage(position, currentDoc, it.page!!) {
                    pasteFinish(position)
                }
            }
        }

        thumbnailAndOutlineViewStatusViewModel.thumbnailAndOutlineViewSelectedMode.observe(
            viewLifecycleOwner
        ) { mode ->
            binding.thumbnailAndOutlineView.setCurrentSelectedMode(mode)
        }

        thumbnailAndOutlineViewStatusViewModel.outlineViewIsEditMode.observe(viewLifecycleOwner) { isEditMode ->
            binding.thumbnailAndOutlineView.setOutlineIsEditMode(isEditMode)
        }

        thumbnailAndOutlineViewStatusViewModel.outlineCreateShow.observe(viewLifecycleOwner) { outlineCreateShow ->
            if (outlineCreateShow) {
                showOutlineCreateDialog()
            } else {
                outlineCreateDialog?.dismiss()
            }
        }

        thumbnailAndOutlineViewStatusViewModel.currentSelectedOutlineEntities.observe(
            viewLifecycleOwner
        ) { currentSelectedOutlineEntities ->
            currentSelectedOutlineEntities?.let {
                binding.thumbnailAndOutlineView.setCurrentSelectedOutlineList(
                    currentSelectedOutlineEntities
                )
            }
        }

        toolAttributesViewModel.textAttributes.observe(viewLifecycleOwner) {
            binding.textStrikethrough.isSelected = it.isStrikethrough
            binding.textBold.isSelected = it.isBold
            binding.textUnderline.isSelected = it.isUnderLine
        }

        exportViewModel.exportNoteUIState.observe(viewLifecycleOwner) {
            if (it) {
                noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
                snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
                val multiWindowCondition =
                    DimensionUtil.getOrientation(requireContext()) == Configuration.ORIENTATION_LANDSCAPE
                            && DimensionUtil.getMultiWindowRatio(requireContext()) <= DimensionUtil.ONE_THIRD_SCREEN_LIMIT
                if (multiWindowCondition) {
                    if (exportNoteOneThirdWindow == null) {
                        exportNoteOneThirdWindow =
                            ExportNoteOneThirdWindow(requireContext(), currentDoc).apply {
                                setOnExportListener {
                                    EditEvent.sendEditExportClick()
                                    exportViewModel.export(requireContext())
                                }
                                setOnExportNameChangeListener { name ->
                                    exportViewModel.setExportName(name)
                                }
                                setOnExportPagesChangeListener { list ->
                                    exportViewModel.setExportPageIndexList(list)
                                }
                                setOnShowModeChangeListener { mode ->
                                    exportViewModel.setExportNoteShowMode(mode)
                                }
                                setOnDismissListener {
                                    exportViewModel.closeExportNoteUI()
                                }
                            }
                    }
                    binding.root.post {
                        if (activity != null && !activity?.isFinishing!!) {
                            if (binding.root.windowToken != null) {
                                exportNoteOneThirdWindow!!.apply {
                                    exportViewModel.exportName.value?.let { exportName ->
                                        updateExportName(exportName)
                                    }
                                    exportViewModel.exportPageIndexList.value?.let { exportPageIndexList ->
                                        updateExportPages(exportPageIndexList)
                                    }
                                    exportViewModel.exportNoteShowMode.value?.let { mode ->
                                        updateShowMode(mode)
                                    }
                                }.showAtLocation(
                                    binding.root,
                                    Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL,
                                    0,
                                    0
                                )
                            }
                        }
                    }
                } else {
                    if (exportNoteWindow == null) {
                        exportNoteWindow =
                            ExportNoteWindow(requireContext(), currentDoc).apply {
                                setOnExportListener {
                                    EditEvent.sendEditExportClick()
                                    exportViewModel.export(requireContext())
                                }
                                setOnExportNameChangeListener { name ->
                                    exportViewModel.setExportName(name)
                                }
                                setOnExportPagesChangeListener { list ->
                                    exportViewModel.setExportPageIndexList(list)
                                }
                                setOnShowModeChangeListener { mode ->
                                    exportViewModel.setExportNoteShowMode(mode)
                                }
                                setOnDismissListener {
                                    exportViewModel.closeExportNoteUI()
                                }
                                setOnExportTypeChangeListener { exportType ->
                                    exportViewModel.exportTypeMode = exportType
                                }
                                setShowExportType(exportViewModel.exportTypeMode)
                            }
                    }
                    binding.noteMajorToolLayout.more.post {
                        if (activity != null && !activity?.isFinishing!!) {
                            if (binding.noteMajorToolLayout.more.windowToken != null) {
                                exportNoteWindow!!.apply {
                                    exportViewModel.exportName.value?.let { exportName ->
                                        updateExportName(exportName)
                                    }
                                    exportViewModel.exportPageIndexList.value?.let { exportPageIndexList ->
                                        updateExportPages(exportPageIndexList)
                                    }
                                    exportViewModel.exportNoteShowMode.value?.let { mode ->
                                        updateShowMode(mode)
                                    }
                                    showMoreToolWindowAtTopRight(exportNoteWindow!!)
                                }
                            }
                        }
                    }
                }
            }
        }

        shareImgViewModel.shareImgUIState.observe(viewLifecycleOwner) { show ->
            if (show) {
                noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
                snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
                val multiWindowCondition =
                    DimensionUtil.getOrientation(requireContext()) == Configuration.ORIENTATION_LANDSCAPE
                            && DimensionUtil.getMultiWindowRatio(requireContext()) <= DimensionUtil.ONE_THIRD_SCREEN_LIMIT
                if (multiWindowCondition) {
                    if (shareImgOneThirdWindow == null) {
                        shareImgOneThirdWindow = NoteImgShareOneThirdWindow(
                            requireContext(),
                            currentDoc,
                            shareImgViewModel.shareImgSelectedIndexList
                        )
                    }
                    binding.root.post {
                        if (activity != null && !activity?.isFinishing!!) {
                            if (binding.root.windowToken != null) {
                                shareImgOneThirdWindow!!.apply {
                                    shareLongPictureClickListener = {
                                        shareLongPicture(it)

                                    }
                                    setOnDismissListener {
                                        shareImgViewModel.closeShareImgUI()
                                    }
                                    refreshSelected()
                                }.showAtLocation(
                                    binding.root,
                                    Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL,
                                    0,
                                    0
                                )
                            }
                        }
                    }
                } else {
                    if (shareImgWindow == null) {
                        shareImgWindow = NoteImgShareWindow(
                            requireContext(),
                            currentDoc,
                            shareImgViewModel.shareImgSelectedIndexList
                        )
                    }
                    binding.noteMajorToolLayout.more.post {
                        if (activity != null && !activity?.isFinishing!!) {
                            if (binding.noteMajorToolLayout.more.windowToken != null) {
                                shareImgWindow!!.apply {
                                    shareLongPictureClickListener = {
                                        shareLongPicture(it)
                                    }
                                    setOnDismissListener {
                                        shareImgViewModel.closeShareImgUI()
                                    }
                                    refreshSelected()
                                    showMoreToolWindowAtTopRight(this)
                                }
                            }
                        }
                    }
                }
            }
        }

        inputManager.inputDeviceIds.forEach { inputDeviceId ->
            val inputDevice = inputManager.getInputDevice(inputDeviceId)
            sendPadAndInputDevice(inputDevice)
        }
        // init
        updateDoodleDeviceMode()
        initBluetoothConnectStatus()

        templateViewModel.currentDownloadState.observe(viewLifecycleOwner) { downloadState ->
            when (downloadState) {
                BaseTemplateViewModel.DownloadState.DOWNLOAD_INITIAL -> {
                    binding.templateDownloadDialog.root.visibility = View.GONE
                }

                BaseTemplateViewModel.DownloadState.DOWNLOADING -> {
                    binding.templateDownloadDialog.root.visibility = View.VISIBLE
                }

                BaseTemplateViewModel.DownloadState.DOWNLOADED -> {
                    lifecycleScope.launch(Dispatchers.Main) {
                        val template = templateViewModel.currentDownloadTemplate
                        template?.let {
                            binding.noteAddPageLayout.updateAdapter(
                                it
                            )
                        }
                        templateViewModel.resetDownloadState()
                    }
                }

                BaseTemplateViewModel.DownloadState.DOWNLOAD_ERROR -> {
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            getString(R.string.network_connection_timed_out)
                        )
                    }
                    templateViewModel.resetDownloadState()
                }

                BaseTemplateViewModel.DownloadState.DOWNLOAD_CANCELLED -> {
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            getString(R.string.template_download_cancel)
                        )
                    }
                    templateViewModel.resetDownloadState()
                }

                else -> {}
            }
        }

        fontDownloadViewModel.currentDownloadState.observe(viewLifecycleOwner) { downloadState ->
            when (downloadState) {
                is FontDownloadViewModel.DownloadCompleted -> {
                    getFontDownloadDialog()?.dismiss()
                    fontDownloadViewModel.removeNetworkChangeListener()
                    fontDownloadViewModel.resetDownloadState()
                    fontDownloadViewModel.document?.let { document ->
                        fontDownloadViewModel.template?.let { template ->
                            addTemplate(template, document)
                        }
                    }
                }

                is FontDownloadViewModel.DownloadNoNet -> {
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            R.string.toast_no_internet
                        )
                    }
                }

                is FontDownloadViewModel.DownloadLostNet -> {
                    getFontDownloadDialog()?.dismiss()
                    fontDownloadViewModel.removeNetworkChangeListener()
                    fontDownloadViewModel.resetDownloadState()
                    fontDownloadViewModel.cancelAllDownload()
                    context?.let {
                        ToastUtils.topCenter(
                            it,
                            R.string.template_download_fail
                        )
                    }
                }

                is FontDownloadViewModel.DownloadError -> {
                    getFontDownloadDialog()?.dismiss()
                    fontDownloadViewModel.cancelAllDownload()
                    fontDownloadViewModel.removeNetworkChangeListener()
                    fontDownloadViewModel.resetDownloadState()
                    val alertDialog = AlertDialog.Builder()
                        .setMsg(resources.getString(R.string.template_resource_download_fail))
                        .setNegativeBtn(resources.getString(R.string.cancel)) {
                        }
                        .setPositiveBtn(resources.getString(R.string.retry)) {
                            fontDownloadViewModel.document?.let { document ->
                                lifecycleScope.launch(Dispatchers.IO) {
                                    fontDownloadViewModel.checkFontResources(document)
                                }
                            }
                        }
                        .build()
                    alertDialog.show(parentFragmentManager, null)
                }

                is FontDownloadViewModel.DownloadInitial -> {

                }

                is FontDownloadViewModel.DownloadProgress -> {
                    getFontDownloadDialog()?.updateDialogContent(
                        downloadState.progress
                    )
                }

                is FontDownloadViewModel.DownloadStart -> {
                    fontDownloadViewModel.addNetworkChangeListener()
                    showFontDownloadDialog()
                }
            }
        }

        adsorptionEdgeViewModel.toolBarState.observe(viewLifecycleOwner) {
            if (consoleViewModel.isConsoleState.value == true && it != AdsorptionEdgeViewModel.ToolBarState.NONE) return@observe
            binding.toolBar.post {
                if (!checkViewBindingValidity()) return@post
                when (it) {
                    AdsorptionEdgeViewModel.ToolBarState.SHOW -> {
                        binding.toolBar.visibility = View.VISIBLE
                        binding.toolBarShow.visibility = View.INVISIBLE
                    }

                    AdsorptionEdgeViewModel.ToolBarState.HIDE -> {
                        binding.toolBar.visibility = View.INVISIBLE
                        binding.toolBarShow.visibility = View.VISIBLE
                    }

                    AdsorptionEdgeViewModel.ToolBarState.NONE -> {
                        binding.toolBar.visibility = View.INVISIBLE
                        binding.toolBarShow.visibility = View.INVISIBLE
                    }

                    null -> {}
                }
            }
            adaptToolBar(
                binding.toolBar,
                min(
                    binding.toolBar.contentView?.width ?: 0,
                    binding.toolBar.contentView?.height ?: 0
                ),
                binding.toolBar.selectEdge
            )
        }

        noteViewModel.longPictureCreateState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is LongPictureCreateNone -> {
                    hideGenerateLongPictureProgressDialog()
                }

                is LongPictureCreateStart -> {
                    showGenerateLongPictureProgressDialog()
                }

                is LongPictureCreateProgress -> {
                    updateGenerateLongPictureProgressDialogProgress(state.process)
                }

                is LongPictureCreateSuccess -> {
                    noteViewModel.resetLongPictureCreateState()
                }

                is LongPictureCreateFail -> {
                    context?.let {
                        ToastUtils.topCenter(
                            it,
                            AppUtils.getString(R.string.generate_long_picture_fail_tip)
                        )
                    }
                    noteViewModel.resetLongPictureCreateState()
                }

                else -> {}
            }
        }

        snippetViewModel.labelSelectorIsFold.observe(viewLifecycleOwner) { isFold ->
            binding.pageSnippetList.changeLabelSelectorIsFold(isFold)
        }

        snippetViewModel.allLabelList.observe(viewLifecycleOwner) { list ->
            val isFold = snippetViewModel.labelSelectorIsFold.value ?: true
            val isExtended =
                snippetViewModel.snippetListViewState.value == SnippetViewModel.SnippetListViewState.EXPANDED
            //选中状态下不可点击
            val isItemClickable = !(snippetViewModel.snippetListIsSelect.value ?: false)
            if (snippetViewModel.currentSelectedLabel.value == null) {
                snippetViewModel.setCurrentSelectedLabel(list[0])
            }
            snippetViewModel.currentSelectedLabel.value?.let { label ->
                binding.pageSnippetList.setUpLabelSelector(
                    isFold,
                    isExtended,
                    isItemClickable,
                    list,
                    label
                )
            }
        }

        snippetViewModel.currentSelectedColor.observe(viewLifecycleOwner) { color ->
            binding.pageSnippetList.setCurrentSelectedColor(color)
            if (snippetViewModel.inSearchMode) {
                search(snippetViewModel.searchingKeyword)
            } else {
                refreshCurrentSnippetItemList()
            }
        }

        snippetViewModel.currentSelectedLabel.observe(viewLifecycleOwner) { label ->
            if (label == null) return@observe
            binding.pageSnippetList.setCurrentSelectedLabel(label)
            if (snippetViewModel.inSearchMode) {
                search(snippetViewModel.searchingKeyword)
            } else {
                refreshCurrentSnippetItemList()
            }
        }

        recordViewModel.isRecording.observe(viewLifecycleOwner) { isRecording ->
            if (isRecording) {
                binding.noteMajorToolLayout.stopRecord.start()
            } else {
                binding.noteMajorToolLayout.stopRecord.stop()
                binding.noteMajorToolLayout.stopRecord.reset()
            }
            binding.noteMajorToolLayout.startRecord.visibility = if (isRecording) {
                View.INVISIBLE
            } else {
                View.VISIBLE
            }
            binding.noteMajorToolLayout.stopRecord.visibility = if (isRecording) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }
            binding.noteMajorToolLayout.showRecordControlView.visibility =
                if (isRecording || currentDoc.recordManager.getRecordsCount() == 0) {
                    View.INVISIBLE
                } else {
                    View.VISIBLE
                }
            binding.noteMajorToolLayout.recordedTime.isVisible = isRecording
        }

        recordViewModel.isShowRecordControlView.observe(viewLifecycleOwner) { isShow ->
            binding.noteMajorToolLayout.showRecordControlView.rotation =
                if (isShow) REVERSE_ROTATION else DEFAULT_ROTATION
            binding.recordControlView.visibility = if (isShow) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
        recordViewModel.currentSelectedRecordList.observe(viewLifecycleOwner) { list ->
            binding.noteMajorToolLayout.showRecordControlView.visibility =
                if (recordViewModel.isRecording.value == true) {
                    View.INVISIBLE
                } else {
                    if (list.isNotEmpty()) {
                        View.VISIBLE
                    } else {
                        View.INVISIBLE
                    }
                }
            binding.recordControlView.setCurrentSelectedRecordList(list)
            binding.recordControlView.setCurrentTime(recordViewModel.playedDuration.value ?: 0)
        }
        recordViewModel.isPlaying.observe(viewLifecycleOwner) { isPlaying ->
            binding.recordControlView.setIsPlaying(isPlaying)
        }
        recordViewModel.playedDuration.observe(viewLifecycleOwner) { duration ->
            binding.recordControlView.setCurrentTime(duration)
            currentDoc.recordManager.playedDuration = duration
        }
        recordViewModel.playbackSpeed.observe(viewLifecycleOwner) { speed ->
            binding.recordControlView.setSpeed(speed)
            currentDoc.recordManager.playbackSpeed = speed
        }
        recordViewModel.currentSelectedRecordFileAbsolutePathList.observe(viewLifecycleOwner) {

        }
        recordViewModel.currentSelectedRecordUUID.observe(viewLifecycleOwner) {

        }
        recordViewModel.currentAmplitude.observe(viewLifecycleOwner) { amplitude ->
            binding.noteMajorToolLayout.stopRecord.setCurrentPercent(amplitude)
        }
        recordViewModel.currentRecordedTime.observe(viewLifecycleOwner) { time ->
            binding.noteMajorToolLayout.recordedTime.text = TimeUtil.getDurationByMillisecond(time)
        }
        recordViewModel.currentPlayingRecordId.observe(viewLifecycleOwner) { playingRecordId ->
            if (playingRecordId == null) {
                // 暂停/停止播放
                recordViewModel.currentPlayObjectId = null
            } else {
                // 开始播放
            }
            RecordViewModel.currentSelectedRecordUUID = null
            RecordViewModel.currentPlayingRecordUUID = playingRecordId
            recordListWindow?.refreshAllRecordList()
            doodleView.setCurrentPlayObjectId(recordViewModel.currentPlayObjectId)
        }

        recordViewModel.currentPlayingRecordTagId.observe(viewLifecycleOwner) { playingRecordTagId ->
            if (playingRecordTagId == null) {
                // 停止播放标记
                recordViewModel.currentPlayObjectId = null
            } else {
                // 开始播放标记
            }
            RecordViewModel.currentPlayingRecordTagUUID = playingRecordTagId
            recordListWindow?.refreshAllRecordList()
            doodleView.setCurrentPlayObjectId(recordViewModel.currentPlayObjectId)
        }
        recordViewModel.recordDelete.observe(viewLifecycleOwner) { record ->
            val deletedTagId = record.second
            if (deletedTagId != null) {
                binding.recordControlView.deleteTag(record.first, deletedTagId)
            }
            if (recordViewModel.currentRecordItemList.isEmpty()) {
                recordViewModel.isShowRecordWindow.value = false
                if (recordViewModel.isShowRecordControlView.value == true) {
                    recordViewModel.switchIsShowRecordControlView()
                }
            } else {
                recordListWindow?.refreshAllRecordList()
            }
            val currentPage = currentDoc.getCurrentPage()
            if (currentPage.isContainRecord(record.first, record.second)) {
                doodleView.doodleStickyOnTopLayer.parseRenderAll()
            }
        }

        snippetViewModel.liveInSearchMode.observe(viewLifecycleOwner) { inSearchMode ->
            if (inSearchMode) {
                binding.pageSnippetList.enterSearchMode(snippetViewModel.searchBoxFocused)
            } else {
                binding.pageSnippetList.exitSearchMode()
                refreshCurrentSnippetItemList()
            }
        }

        snippetViewModel.liveSearchingKeyword.observe(viewLifecycleOwner) { keyword ->
            if (snippetViewModel.inSearchMode) {
                binding.pageSnippetList.setSearchText(keyword)
            }
        }
        draftPaperViewModel.isShowDraftPaper.observe(viewLifecycleOwner) { isShowDraftPaper ->
            if (!checkViewBindingValidity()) return@observe
            if (!UserUsageConfig.isShowDraftPaper) return@observe
            if (isShowDraftPaper) {
                binding.draftPaperFragmentContainer.post {
                    showDraftPaper()
                }
            } else {
                binding.draftPaperFragmentContainer.post {
                    closeDraftPaper()
                }
            }
        }
        noteScaleViewModel.currentScaleRadio.observe(viewLifecycleOwner) { scale ->
            binding.scaleRatio.text = if (scale >= 1.0) {
                PercentFormatUtil().getThreeDigitPercent(scale)
            } else {
                PercentFormatUtil().getTwoDigitPercent(scale)
            }
        }
        noteScaleViewModel.enableScale.observe(viewLifecycleOwner) { enable ->
            EditEvent.sendDoodleScreenLockRatioClick(enable)
            binding.doodle.doodleModeConfig.enableScale = enable
            binding.scaleLock.isSelected = enable
        }

        customToolsViewModel.targetCustomTools.observe(viewLifecycleOwner) { customTools ->
            if (customTools.isNotEmpty()) {
                updateMajorToolLayout(binding.doodle.inputMode)
                if (Preferences.needShowFirstGraphDrawGuide) {
                    noteViewModel.isShowRecognizeWindow.postValue(true)
                }
                if (editorModeViewModel.editorMode.value == InputMode.GRAPH && isShowGraphToolWindow == true) {
                    noteViewModel.changeGraphWindowStatues(true)
                }
                noteMaterialViewModel.refreshIsNeedShowMaterialSign()
            }
        }

        customToolsViewModel.customToolDialogIsShow.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                showCustomToolsDialog()
            } else {
                hideCustomToolsDialog()
            }
        }

        keyEventViewModel.keyEvent.observe(viewLifecycleOwner) { keyEvent ->
            StylusToolKit.obtainStylusFunctionSwitch()?.onKeyEvent(keyEvent)
        }

        viewLifecycleOwner.lifecycleScope.launch {
            snippetViewModel.snippetsPagingDataFlow.collectLatest { pagingData ->
                if (checkViewBindingValidity()) {
                    binding.pageSnippetList.submitData(pagingData)
                }
            }
        }

        editorAdViewModel.loadInterstitialAdIfNeeded(requireActivity())
        editorAdViewModel.setShouldAutoLoadInterstitialAdWhenOverTime(true)
    }

    private fun initMajorToolLayout() {
        binding.noteMajorToolLayout.doOnSelectTool = { customTool, view ->
            selectMajorTool(customTool)
        }

        binding.noteMajorToolLayout.doOnShowHideToolIconClick = {
            customToolsViewModel.showCustomSelectToolWindow()
        }
    }

    private fun changeTurnPageEnable(isEnable: Boolean) {
        if (!checkViewBindingValidity()) return
    }

    private fun refreshSnippetList() {
        binding.pageSnippetList.getAllSnippets().forEachIndexed { index, noteSnippetItem ->
            if (snippetViewModel.selectedSnippetIndex.contains(index)) {
                noteSnippetItem.isSelected = !noteSnippetItem.isSelected
            }
        }
        binding.pageSnippetList.refreshSnippetList()
        binding.pageSnippetList.refreshSelectedNumber(snippetViewModel.selectedSnippetIndex.size)
        val pageSnippetListIsShow = snippetListViewState.isStillShown()
        if (UserUsageConfig.isNeedShowSnippetDragGuide && !binding.pageSnippetList.snippetListIsEmpty()) {
            if (pageSnippetListIsShow && isSnippetsExpandAnimationEnd) {
                binding.pageSnippetList.post {
                    if (!isAdded) return@post
                    showNoteSnippetDragGuideWindow()
                }
            }
        }
    }

    private fun showNoteSnippetDragGuideWindow() {
        noteSnippetDragGuideWindow?.setOnDismissListener {

        }
        noteSnippetDragGuideWindow?.dismiss()
        val topSnippetView =
            binding.pageSnippetList.getTopSnippetView() ?: return
        noteSnippetDragGuideWindow = NoteSnippetGuideWindow(requireContext())
        noteSnippetDragGuideWindow?.showDragWindow(topSnippetView)
        noteSnippetDragGuideWindow?.setOnDismissListener {
            UserUsageConfig.isNeedShowSnippetDragGuide = false
            noteSnippetDragGuideWindow = null
        }
    }

    private fun importFontFile(uri: Uri) {
        importFontViewModel.importFile(uri) { fontImportResult ->
            lifecycleScope.launch(Dispatchers.IO) {
                val fontGroupInfo = FontManager.getFontGroupInfo()
                withContext(Dispatchers.Main) {
                    when (fontImportResult) {
                        is FontImportSuccess -> {
                            ToastUtils.topCenter(
                                requireContext(),
                                R.string.font_file_import_success_tip
                            )
                        }

                        is FontImportFail -> {
                            ToastUtils.topCenter(
                                requireContext(),
                                R.string.font_file_import_fail_tip
                            )
                        }

                        is FontImportError -> {
                            if (fontImportResult.errorCode == FileImporter.ERROR_RESTORE_ZIP_FILE_NO_FONT) {
                                ToastUtils.topCenter(
                                    requireContext(),
                                    R.string.font_file_import_fail_no_ttf
                                )
                            } else {
                                ToastUtils.topCenter(
                                    requireContext(),
                                    R.string.font_import_font_error_tip
                                )
                            }
                        }
                    }
                    fontListWindow?.refreshFontList(fontGroupInfo)
                }
            }
        }
    }

    private fun showFontListWindow(adapter: ToolTextFontAdapter, view: View) {
        lifecycleScope.launch(Dispatchers.IO) {
            val fontGroupInfo = FontManager.getFontGroupInfo()
            withContext(Dispatchers.Main) {
                fontListWindow = FontListWindow(
                    requireContext(),
                    fontGroupInfo
                ).apply {
                    inputMethodMode = PopupWindow.INPUT_METHOD_NOT_NEEDED
                    onFontChangeAction = {
                        adapter.setFontInfo(it)
                        adapter.notifyDataSetChanged()
                        dismiss()
                        toolAttributesViewModel.setFontInfo(it)
                        lifecycleScope.launch(Dispatchers.IO) {
                            it.lastUseTime = SystemClock.elapsedRealtime()
                            HandbookDatabase.getDatabase().fontDao()
                                .insertFont(it)
                        }
                    }
                    fontDown = { url, listener ->
                        lifecycleScope.launch(Dispatchers.IO) {
                            FontManager.downloadFont(url, listener)
                        }
                    }
                    fontAdd = {
                        try {
                            getFont.launch(arrayOf(MimeType.TTF.mime, MimeType.ZIP.mime))
                        } catch (e: ActivityNotFoundException) {

                        }
                    }
                }
                fontListWindow?.showAsBubble(
                    view,
                    view is VerticalTextView,
                    adsorptionEdgeViewModel.getBubbleOrientation()
                )
            }
        }
    }

    override fun getCurrentNavigationId(): Int {
        return R.id.note_editor
    }

    var onSnippetListenerSetFinishedAction: (() -> Unit)? = null

    override fun onSetSnippetListener() {
        super.onSetSnippetListener()
        doodleView.modelManager.snippetEditor.setOnSnippetListener(object : ISnippetListener {
            override fun onGetDefaultSnippetColor(): Int {
                return snippetViewModel.currentDefaultColor
            }

            override fun onGetDefaultSnippetColors(): List<Int> {
                return snippetViewModel.defaultColorList
            }

            override fun onRequestGetSnippetTags(snippetId: String) {
                snippetViewModel.requestNoteSnippetTags(snippetId) { snippet, allTags, addedTags ->
                    doodleView.doodleEditLayer.mSnippetView?.run {
                        setSnippetAndTagsInfo(snippet, allTags, addedTags)
                        showToolWindow()
                    }
                }
            }

            override fun onRequestGetDefaultSnippetTags(snippet: NoteSnippet) {
                snippetViewModel.requestAllTags {
                    doodleView.doodleEditLayer.mSnippetView?.run {
                        setSnippetAndTagsInfo(snippet, it, emptyList())
                        showToolWindow()
                    }
                }
            }

            override fun onColorSelect(color: Int) {
                snippetViewModel.currentDefaultColor = color
            }

            override fun onSnippetCreate(noteSnippet: NoteSnippet) {
                isCreateSnippet = true
                if (UserUsageConfig.isNeedShowSnippetGuide) {
                    if (binding.noteMajorToolLayout.showSnippet.isVisible) {
                        showNoteSnippetGuideWindow(binding.noteMajorToolLayout.showSnippet)
                    }
                    UserUsageConfig.isNeedShowSnippetGuide = false
                }
                lifecycleScope.launchWhenCreated {
                    snippetViewModel.createSnippet(noteSnippet, snippetViewModel.inSearchMode)
                    if (snippetViewModel.inSearchMode) {
                        withTimeoutOrNull(TEXT_RECOGNIZE_TIME_OUT_MS) {
                            SnippetManager.waitRecognizeFinished(noteSnippet)
                            search(snippetViewModel.searchingKeyword)
                        }
                    }
                }
            }

            override fun onSnippetUpdate(
                oldSnippet: NoteSnippet,
                newSnippet: NoteSnippet,
                snippetNeedTranslate: Boolean,
            ) {
                lifecycleScope.launchWhenCreated {
                    snippetViewModel.updateSnippet(newSnippet)
                    if (snippetNeedTranslate) {
                        snippetViewModel.addNeedTranslateSnippet(newSnippet.snippetId)
                    }
                    if (snippetViewModel.inSearchMode) {
                        withTimeoutOrNull(TEXT_RECOGNIZE_TIME_OUT_MS) {
                            SnippetManager.waitRecognizeFinished(newSnippet)
                            search(snippetViewModel.searchingKeyword)
                        }
                    }
                }
            }

            override fun onSnippetDelete(noteSnippet: NoteSnippet) {
                snippetViewModel.deleteSnippet(noteSnippet)
                snippetOptionWindow?.dismiss()
            }

            override fun onSnippetAddTag(
                noteSnippet: NoteSnippet,
                tag: SnippetTag,
                tags: List<SnippetTag>,
            ) {
                snippetViewModel.bindSnippetAndTag(noteSnippet, tag)
            }

            override fun onSnippetRemoveTag(
                noteSnippet: NoteSnippet,
                tag: SnippetTag,
                tags: List<SnippetTag>,
            ) {
                snippetViewModel.unbindSnippetAndTag(noteSnippet, tag, tags)
            }

            override fun onSnippetTagCreate(snippetTag: SnippetTag) {

            }

            override fun onSnippetTagDelete(snippetTag: SnippetTag) {

            }

            override fun onSnippetTagMoreClick(
                snippet: NoteSnippet,
                addedTags: List<SnippetTag>,
            ) {
                snippetManagerViewModel.currentSelectedSnippetList = listOf(snippet)
                snippetManagerViewModel.showManagerOrMoreDialogTag = SNIPPET_MORE_SHOW_FLAG
                snippetManagerViewModel.isManageSnippetTagEntrance = false
                snippetManagerViewModel.currentAddedTags = addedTags
                snippetManagerViewModel.changeManagerSnippetTagVisible(true)
            }

            override fun onSnippetTagManagerClick(
                snippet: NoteSnippet,
                addedTags: List<SnippetTag>,
            ) {
                snippetManagerViewModel.currentSelectedSnippetList = listOf(snippet)
                snippetManagerViewModel.showManagerOrMoreDialogTag = SNIPPET_MANAGER_SHOW_FLAG
                snippetManagerViewModel.isManageSnippetTagEntrance = true
                snippetManagerViewModel.currentAddedTags = addedTags
                snippetManagerViewModel.changeManagerSnippetTagVisible(true)
            }

            override fun onSnippetTranslate(snippet: NoteSnippet) {
                translateSnippet(snippet)
            }
        })
        onSnippetListenerSetFinishedAction?.invoke()
    }

    private fun translateSnippet(snippet: NoteSnippet) {
        lifecycleScope.launch(Dispatchers.Main) {
            val recognizeJob = async {
                withTimeoutOrNull(TEXT_RECOGNIZE_TIME_OUT_MS) {
                    SnippetManager.waitRecognizeFinished(snippet)
                }
            }
            launch {
                delay(TRANSLATE_LOADING_DIALOG_DELAY_SHOW_TIME_MS)
                if (!recognizeJob.isCompleted) {
                    showTranslateLoadingDialog()
                }
            }
            translateJob = launch {
                val symbols = recognizeJob.await().orEmpty()
                hideTranslateLoadingDialog()
                ensureActive()
                showCommonWebSideBar(symbols)
            }
        }
    }

    private fun showCommonWebSideBar(content: String, isFromWebSearch: Boolean = false) {
        if (isWebViewAvailable()) {
            if (isFromWebSearch) {
                binding.webSidebarView.search(content)
                changeCurrentSidebarStatus(Sidebar.WEB_SEARCH, false)
            } else {
                binding.webSidebarView.translate(content)
                changeCurrentSidebarStatus(Sidebar.TRANSLATE)
            }

        } else {
            showWebViewNotAvailableDialog()
        }
    }

    fun resetPhotoCropDialog() {
        val dialog = parentFragmentManager.findFragmentByTag(BasePhotoCropDialogFragment.TAG)
        if (dialog != null && dialog is BasePhotoCropDialogFragment) {
            photoCropDialogFragment = dialog as PhotoCropDialogFragment
            dialog.apply {
                photoUri = aiViewModel.cameraPhotoUri
                setOnCropCompleteListener(object :
                    BasePhotoCropDialogFragment.IOnCropCompleteListener {
                    override fun onCropSuccess(uri: Uri, imageAlpha: Int) {
                        photoCropDialogFragment?.dismiss()
                        aiViewModel.setAddPictureUri(uri)
                    }

                    override fun onCropFailed() {

                    }

                    override fun onCancel() {

                    }
                })
            }
        }
    }

    override fun setDoodleConfig() {
        super.setDoodleConfig()
        doodleView.modelManager.mLassoToolAiSolveProblemsClick = object :
            ILassoAiSolveProblemsListener {
            override fun onSolveProblems(content: String?, imageUri: Uri?) {
                if (sideBarViewModel.currentSidebarState.value != Sidebar.AI) {
                    aiViewModel.startSession()
                    aiViewModel.acquireAIIntegral()
                    changeCurrentSidebarStatus(Sidebar.AI)
                }
                if (noteAIFragment?.isAdded == true) {
                    aiContainer?.post {
                        noteAIFragment?.setQuestionFromDoodle(content, imageUri)
                    }
                } else {
                    aiContainer?.doOnNextLayout {
                        noteAIFragment?.setQuestionFromDoodle(content, imageUri)
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (invalidCurrentDoc()) return
        noteMaterialViewModel.refreshNoteMaterialCategoryState()
        StylusToolKit.obtainStylusFunctionSwitch()
            ?.registerFunctionSwitchCallback(requireContext(), this) { switchType ->
                val handled = when (switchType) {
                    FunctionSwitchType.CURRENT_TO_ERASE -> {
                        editorModeViewModel.switchBetweenCurrentAndEraserMode()
                    }

                    FunctionSwitchType.CURRENT_TO_LAST -> {
                        editorModeViewModel.switchModeToLast()
                    }

                    else -> false
                }

                if (handled) {
                    if (!binding.toolBar.isVisible) {
                        binding.toolBar.post {
                            val inputMode = editorModeViewModel.editorMode.value
                            if (inputMode == InputMode.VIEW) {
                                adsorptionEdgeViewModel.changeToolBarVisibility(
                                    AdsorptionEdgeViewModel.ToolBarState.NONE
                                )
                            } else {
                                adsorptionEdgeViewModel.changeToolBarVisibility(
                                    AdsorptionEdgeViewModel.ToolBarState.SHOW
                                )
                                binding.toolBar.show {}
                            }
                        }
                    }
                }

                handled
            }

    }

    private var translateLoadingDialog: PadLogoLoadingDialog? = null
    private var translateJob: Job? = null

    private fun showTranslateLoadingDialog() {
        val dialog = parentFragmentManager.findFragmentByTag(TRANSLATE_LOADING_DIALOG_TAG)
        if (dialog != null) return
        if (translateLoadingDialog == null) {
            translateLoadingDialog = PadLogoLoadingDialog().apply {
                setTip(<EMAIL>().resources.getString(R.string.translate_loading_tip))
                onCancelClick = {
                    <EMAIL> {
                        <EMAIL>(Lifecycle.State.RESUMED) {
                            translateJob?.cancel()
                        }
                    }
                }
            }
        }
        translateLoadingDialog?.safeShow(parentFragmentManager, TRANSLATE_LOADING_DIALOG_TAG)
    }

    private fun hideTranslateLoadingDialog() {
        if (translateLoadingDialog == null) {
            translateLoadingDialog =
                parentFragmentManager.findFragmentByTag(TRANSLATE_LOADING_DIALOG_TAG) as? PadLogoLoadingDialog
        }
        translateLoadingDialog?.safeDismiss(parentFragmentManager)
    }

    override fun onPause() {
        super.onPause()
        (activity as? MainActivity)?.unRegisterClearFocusAndHideSoftKeyBoardListener()
        StylusToolKit.obtainStylusFunctionSwitch()
            ?.unregisterFunctionSwitchCallback(requireContext(), this)
    }

    private fun initNoteSearch() {
        searchViewModel.snippetAlreadySearchSuccessState?.let { snippetSearchSuccessState ->
            val searchType = SearchType.SNIPPET
            val snippetSearchResult = snippetSearchSuccessState.searchResultList
            noteSearchFrameLayout?.let {
                it.refreshSnippetList(snippetSearchResult.toMutableList())
                if (snippetSearchSuccessState.isFinished && searchViewModel.snippetShowNoMoreData == true) {
                    it.setShowNoMoreData(searchType)
                }
                it.restoreSearchListPosition(searchType, searchViewModel.snippetScrollPosition)
            }
        }

        searchViewModel.currentDocAlreadySearchSuccessState?.let { currentDocSearchSuccessState ->
            noteSearchFrameLayout?.let {
                val searchType = SearchType.CURRENT_DOC
                if (currentDocSearchSuccessState.isFinished) {
                    it.clearDocSearchFooterMessage(searchType)
                }
                it.updateDocSearchResult(
                    currentDocSearchSuccessState.searchResultList,
                    searchType
                )
                if (currentDocSearchSuccessState.isFinished && searchViewModel.currentDocShowNoMoreData == true) {
                    it.setShowNoMoreData(searchType)
                }
                it.restoreSearchListPosition(
                    searchType,
                    searchViewModel.currentDocScrollPosition
                )
            }
        }

        searchViewModel.otherDocAlreadySearchSuccessState?.let { otherDocSearchSuccessState ->
            noteSearchFrameLayout?.let {
                val searchType = SearchType.OTHER_DOC
                if (otherDocSearchSuccessState.isFinished) {
                    it.clearDocSearchFooterMessage(searchType)
                }
                it.updateDocSearchResult(
                    otherDocSearchSuccessState.searchResultList,
                    searchType
                )
                if (otherDocSearchSuccessState.isFinished && searchViewModel.otherDocShowNoMoreData == true) {
                    it.setShowNoMoreData(searchType)
                }
                it.restoreSearchListPosition(searchType, searchViewModel.otherDocScrollPosition)
            }
        }

        searchViewModel.searchStateSnippet.observe(viewLifecycleOwner) { searchState ->
            val searchType = SearchType.SNIPPET
            when (searchState) {
                is SearchStart -> {
                    noteSearchFrameLayout?.refreshSnippetList(mutableListOf())
                }

                is SearchSuccess<*> -> {
                    noteSearchFrameLayout?.let {
                        val snippetSearchResult =
                            (searchState.searchResultList as List<NoteSnippetItem>)
                        it.refreshSnippetList(snippetSearchResult.toMutableList())
                        if (searchState.isFinished) {
                            if (searchViewModel.snippetShowNoMoreData == null) {
                                it.checkIfShowNoMoreData(searchType) { show ->
                                    searchViewModel.snippetShowNoMoreData = show
                                    if (show) {
                                        it.setShowNoMoreData(searchType)
                                    }
                                }
                            } else if (searchViewModel.snippetShowNoMoreData == true) {
                                it.setShowNoMoreData(searchType)
                            }
                        }
                        it.restoreSearchListPosition(
                            searchType,
                            searchViewModel.snippetScrollPosition
                        )
                    }

                }

                else -> {}
            }
        }
        searchViewModel.searchStateCurrentDoc.observe(viewLifecycleOwner) { searchState ->
            val searchType = SearchType.CURRENT_DOC
            when (searchState) {
                is SearchSuccess<*> -> {
                    noteSearchFrameLayout?.let {
                        if (searchState.isFinished) {
                            it.clearDocSearchFooterMessage(searchType)
                        }
                        it.updateDocSearchResult(
                            searchState.searchResultList as List<DocumentSearchGroup>,
                            searchType
                        )
                        if (searchState.isFinished) {
                            if (searchViewModel.currentDocShowNoMoreData == null) {
                                it.checkIfShowNoMoreData(searchType) { show ->
                                    searchViewModel.currentDocShowNoMoreData = show
                                    if (show) {
                                        it.setShowNoMoreData(searchType)
                                    }
                                }
                            } else if (searchViewModel.currentDocShowNoMoreData == true) {
                                it.setShowNoMoreData(searchType)
                            }
                        }
                        it.restoreSearchListPosition(
                            searchType,
                            searchViewModel.currentDocScrollPosition
                        )
                    }
                }

                is SearchStart -> {
                    noteSearchFrameLayout?.updateDocSearchResult(
                        emptyList(),
                        searchType
                    )
                    noteSearchFrameLayout?.clearDocSearchFooterMessage(searchType)
                }

                else -> {}
            }
        }
        searchViewModel.searchStateOtherDoc.observe(viewLifecycleOwner) { searchState ->
            val searchType = SearchType.OTHER_DOC
            when (searchState) {
                is SearchSuccess<*> -> {
                    noteSearchFrameLayout?.let {
                        if (searchState.isFinished) {
                            it.clearDocSearchFooterMessage(searchType)
                        }
                        it.updateDocSearchResult(
                            searchState.searchResultList as List<DocumentSearchGroup>,
                            searchType
                        )
                        if (searchState.isFinished) {
                            if (searchViewModel.otherDocShowNoMoreData == null) {
                                it.checkIfShowNoMoreData(searchType) { show ->
                                    searchViewModel.otherDocShowNoMoreData = show
                                    if (show) {
                                        it.setShowNoMoreData(searchType)
                                    }
                                }
                            } else if (searchViewModel.otherDocShowNoMoreData == true) {
                                it.setShowNoMoreData(searchType)
                            }
                        }
                        it.restoreSearchListPosition(
                            searchType,
                            searchViewModel.otherDocScrollPosition
                        )
                    }
                }

                is SearchStart -> {
                    noteSearchFrameLayout?.updateDocSearchResult(
                        emptyList(),
                        searchType
                    )
                    noteSearchFrameLayout?.clearDocSearchFooterMessage(searchType)
                }

                else -> {}
            }
        }
        searchViewModel.searchType.observe(viewLifecycleOwner) {
            noteSearchFrameLayout?.currentSelectSearchType = it
        }

        searchViewModel.currentSearchState.observe(viewLifecycleOwner) { searchState ->
            noteSearchFrameLayout?.changeCurrentDateStateView(searchState)
        }

        binding.expandedImageContent.apply {
            doOnPreviewClose = {
                doodleView.updateVisualManager()
            }
            hideEditIcon()
            hideMoreIcon()
        }

        noteSearchFrameLayout?.apply {
            onChangeSearchTypeAction = { searchType ->
                if (noteViewModel.isHiddenSpaceMode.value == true) {
                    searchViewModel.changeSearchType(
                        searchType,
                        noteViewModel.allHiddenDocuments
                    )
                } else {
                    searchViewModel.changeSearchType(
                        searchType,
                        noteViewModel.allUnhiddenDocuments
                    )
                }
            }
            onCloseClickedAction = {
                changeCurrentSidebarStatus(Sidebar.NONE)
                searchViewModel.resetBackPageUUID()
                refreshSearchBackPageIcon(null)
            }
            onSearchByNetAction = {
                searchViewModel.symbols.value?.let { showCommonWebSideBar(it, true) }
            }
            onSearchNew = { newKeyword ->
                SearchEvent.sendSearchBoxKeywordInput(SearchEvent.SEARCH_SOURCE_USER_INPUT)
                if (noteViewModel.isHiddenSpaceMode.value == true) {
                    searchViewModel.changeSymbols(newKeyword, noteViewModel.allHiddenDocuments)
                } else {
                    searchViewModel.changeSymbols(
                        newKeyword,
                        noteViewModel.allUnhiddenDocuments
                    )
                }
                hideOtherDocPreviewBottomSheet()
            }
            onSearchGroupItemClickedAction = { group, docSearchResult ->
                if (docSearchResult.docId == currentDoc.uuid) {
                    SearchEvent.sendNoteSearchResultItemClickEvent("current_note")
                    showCurrentDocSearchPage(docSearchResult.pageId)
                } else {
                    SearchEvent.sendNoteSearchResultItemClickEvent("other_note")
                    showOtherDocPreviewBottomSheet(group, docSearchResult)
                }
            }
            snippetPreviewCallback = { view, noteSnippetItem, showDuration ->
                SearchEvent.sendNoteSearchResultItemClickEvent("snippet")
                binding.expandedImageContent.showPreview(
                    childFragmentManager,
                    getPreviewSnippetWidth(),
                    getPreviewSnippetHeight(),
                    view,
                    noteSnippetItem,
                    showDuration
                )
            }
        }
        searchViewModel.currentDocSearchResult.observe(viewLifecycleOwner) { docSearchResult ->
            if (docSearchResult != null) {
                setPageAndUpdateHighlightRect(
                    currentDoc.getCurrentPage(),
                    docSearchResult.docRenderInfo
                )
            } else {
                setPageAndUpdateHighlightRect(currentDoc.getCurrentPage(), null)
            }
        }

        searchViewModel.symbols.observe(viewLifecycleOwner) { symbols ->
            if (symbols != null) {
                noteSearchFrameLayout?.setSearchBoxText(symbols)
            }
        }
    }

    private val draftPaperLifecycleEventObserver =
        LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_START) {
                binding.draftPaperFragmentContainer.run {
                    bindChangePositionView(
                        draftPaperFragment?.requireView()
                            ?.findViewById(R.id.change_position_view)
                    )
                    bindChangeSizeView(
                        draftPaperFragment?.requireView()?.findViewById(R.id.change_Layout_view)
                    )
                    onChangeSizeFinish = {
                        draftPaperFragment?.changeSizeFinish()
                        draftPaperViewModel.draftPaperViewWidth = width
                        draftPaperViewModel.draftPaperViewHeight = height
                    }

                    onChangingPosition = {
                        draftPaperFragment?.onChangingPosition()
                        val screenDimensions = DimensionUtil.getScreenDimensions(context)
                        draftPaperViewModel.draftPaperViewXRelativeScreenRatio =
                            x / screenDimensions.widthPixels
                        draftPaperViewModel.draftPaperViewYRelativeScreenRatio =
                            y / screenDimensions.heightPixels
                    }
                }
            }
            if (event == Lifecycle.Event.ON_STOP) {
                binding.draftPaperFragmentContainer.run {
                    bindChangePositionView(null)
                    bindChangeSizeView(null)
                    onChangeSizeFinish = null
                }
            }
        }

    private fun addDraftPaperFragment() {
        draftPaperFragment =
            childFragmentManager.findFragmentByTag(DRAFT_PAPER_FRAGMENT_TAG) as? DraftPaperFragment
                ?: DraftPaperFragment()
        draftPaperFragment?.let {
            it.lifecycle?.addObserver(draftPaperLifecycleEventObserver)
            childFragmentManager.beginTransaction()
                .replace(
                    R.id.draft_paper_fragment_container,
                    it,
                    DRAFT_PAPER_FRAGMENT_TAG
                )
                .commitNowAllowingStateLoss()
        }
    }

    private fun removeAndHideDraftPaper() {
        if (!checkViewBindingValidity()) return
        binding.draftPaperShowBtn.visibility = View.INVISIBLE
        binding.draftPaperFragmentContainer.visibility = View.INVISIBLE
        draftPaperAnimationTime = NO_ANIMATION_DURATION
        childFragmentManager.findFragmentByTag(DRAFT_PAPER_FRAGMENT_TAG)
            ?.let { draftPaperFragment ->
                childFragmentManager.beginTransaction().remove(draftPaperFragment).commit()
            }
    }

    private fun initNoteAi() {
        if (aiViewModel.completions.isEmpty()) {
            aiViewModel.getHistoryByPage(aiViewModel.currentPageIndex) {
                aiViewModel.completions.addAll(it)
            }
        }
        binding.noteMajorToolLayout.showAi.setOnClickListener {
            if (sideBarViewModel.currentSidebarState.value == Sidebar.AI) {
                AiEvent.sendAiNavButtonClick(openPanelAction = false)
                changeCurrentSidebarStatus(Sidebar.NONE)
            } else {
                aiViewModel.startSession()
                aiViewModel.acquireAIIntegral()
                AiEvent.sendAiNavButtonClick(openPanelAction = true)
                changeCurrentSidebarStatus(Sidebar.AI)
            }
        }
        binding.noteMajorToolLayout.showAi.isVisible = !isLandOneThirdScreenOrLandHalfScreen()
    }

    private fun showCurrentDocSearchPage(pageId: UUID) {
        currentDoc.getPageByUUID(pageId)?.let { page ->
            val currentPage = currentDoc.getCurrentPage()
            if (currentPage.uuid != pageId) {
                searchViewModel.refreshBackPageUUID()
                currentDoc.viewingPageIndex = currentDoc.pages.indexOf(page)
                setPage(page, false)
            }
        }
    }

    private fun showAIFragment() {
        val fm = childFragmentManager
        val aiFragment = fm.findFragmentByTag(NoteAIFragment.TAG)
        noteAIFragment = if (aiFragment == null) {
            NoteAIFragment()
        } else {
            aiFragment as NoteAIFragment
        }
        noteAIFragment?.apply {
            this.pickImageRequester = aiPickImageRequester
            this.permissionRequester = <EMAIL>
            this.takePhotoLauncher = aiTakePhotoLauncher

            onCloseClickedAction = {
                changeCurrentSidebarStatus(Sidebar.NONE)
            }
            onGoUpgradeClickedAction = {
                if (UserManager.hasLoggedInUser()) {
                    val action = NoteEditorFragmentDirections.actionNoteEditorToVipStore()
                    action.source = NaviEnum.AI_FUEL_PACK
                    safeNavigate(action)
                } else {
                    callLogin()
                }
            }
            onCopyClickedAction = {
                Clipboard.copyToMarkdown(it)
                if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2) {
                    ToastUtils.topCenter(
                        requireContext(),
                        AppUtils.getString(R.string.ai_copy_toast)
                    )
                }
            }
        }
        if (!activityIsFinishing(requireActivity())) {
            noteAIFragment?.let {
                childFragmentManager.beginTransaction().remove(it).commitNowAllowingStateLoss()
            }
            aiContainer?.id?.let {
                noteAIFragment?.show(
                    childFragmentManager,
                    NoteAIFragment.TAG,
                    it
                )
            }
        }
    }

    private fun showOtherDocPreviewBottomSheet(
        group: DocumentSearchGroup,
        docSearchResult: DocSearchResult,
    ) {
        SearchEvent.sendNoteSearchOtherNotePreviewShowEvent()
        val doc = NoteRepository.getDocumentFromCacheById(docSearchResult.docId) ?: return
        val fm = childFragmentManager
        val bottomSheet = fm.findFragmentByTag(OTHER_DOC_PREVIEW_BOTTOM_SHEET_TAG)
        if (bottomSheet == null) {
            val docPreviewBottomSheet = OtherDocPreviewFragment().apply {
                initDoc(doc, docSearchResult.pageId, group.searchResults, docSearchResult)
                setSearchViewWidth(
                    if (isLandOneThirdScreen()) {
                        0
                    } else {
                        noteSearchFrameLayout!!.getRealWidth()
                    }
                )
            }
            docPreviewBottomSheet.show(childFragmentManager, OTHER_DOC_PREVIEW_BOTTOM_SHEET_TAG)
        } else {
            (bottomSheet as OtherDocPreviewFragment).apply {
                updateShowPageInfo(
                    doc,
                    docSearchResult.pageId,
                    group.searchResults,
                    docSearchResult
                )
            }
        }
    }

    private fun hideOtherDocPreviewBottomSheet() {
        val bottomSheet =
            childFragmentManager.findFragmentByTag(OTHER_DOC_PREVIEW_BOTTOM_SHEET_TAG)
        if (bottomSheet is OtherDocPreviewFragment) {
            bottomSheet.dismiss()
        }
    }

    private fun isLandOneThirdScreen(): Boolean {
        return DimensionUtil.isLandAndOneThirdScreen(context) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
            context
        )
    }

    private fun initNoteMaterial() {
        noteMaterialViewModel.run {
            val position = noteMaterialViewModel.materialCategoryStickerList.value?.indexOf(
                noteMaterialViewModel.currentSelectMaterialCategoryInfo.value
            )
            if (position != null) {
                binding.noteMaterialView.materialCategorySmoothMoveToPosition(position)
                binding.noteMaterialView.materialSmoothMoveToPosition(
                    noteMaterialViewModel.materialListScrollPosition
                )
            }
            initLoadNoteMaterialCategoryStickerList()
            materialCategoryStickerList.observe(viewLifecycleOwner) { materialCategoryStickerList ->
                binding.noteMaterialView.refreshTypeList(materialCategoryStickerList)
                noteMaterialWindow?.refreshTypeList(materialCategoryStickerList)
            }
            currentSelectMaterialCategoryInfo.observe(viewLifecycleOwner) { currentSelectMaterialCategoryInfo ->
                currentSelectMaterialCategoryInfo?.let {
                    if (it.noteMaterialCategory.id != oldStickerCategoryId) {
                        StickerDataCollection.stickerCategoryData(
                            it.noteMaterialCategory,
                            DataType.NOTEBOOKS_STICKER_CATEGORY_VIEW
                        )
                        oldStickerCategoryId = it.noteMaterialCategory.id
                    }
                    noteMaterialViewModel.selectedMaterial = true
                    binding.noteMaterialView.select(it)
                    noteMaterialWindow?.select(it)
                }
            }
            currentMaterialStickerList.observe(viewLifecycleOwner) { currentMaterialStickerList ->
                if (noteMaterialViewModel.selectedMaterial) {
                    currentMaterialStickerList?.let {
                        binding.noteMaterialView.refreshStickerList(it)
                        noteMaterialWindow?.refreshStickerList(it)
                    }
                }
            }
            currentStickerCategoryState.observe(viewLifecycleOwner) { categoryState ->
                when (categoryState) {
                    NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD -> {
                        binding.noteMaterialView.showBottomDownloadView()
                        noteMaterialWindow?.showBottomDownloadView()
                    }

                    NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                        binding.noteMaterialView.showBottomNeedVipView()
                        noteMaterialWindow?.showBottomNeedVipView()
                    }

                    NoteMaterialViewModel.MaterialCategoryState.DOWNLOADING -> {
                        binding.noteMaterialView.showBottomDownloadingView()
                        noteMaterialWindow?.showBottomDownloadingView()
                    }

                    NoteMaterialViewModel.MaterialCategoryState.DOWNLOADED,
                    NoteMaterialViewModel.MaterialCategoryState.NO_NEED_DOWNLOAD,
                        -> {
                        binding.noteMaterialView.hideBottomView()
                        noteMaterialWindow?.hideBottomView()
                    }

                    NoteMaterialViewModel.MaterialCategoryState.NET_LOST -> {
                        binding.noteMaterialView.hideBottomView()
                        noteMaterialWindow?.hideBottomView()
                    }

                    else -> {}
                }
            }
            showMaterialReloadView.observe(viewLifecycleOwner) { showMaterialReloadView ->
                if (showMaterialReloadView) {
                    binding.noteMaterialView.showReloadView()
                    noteMaterialWindow?.showReloadView()
                } else {
                    binding.noteMaterialView.hideReloadView()
                    noteMaterialWindow?.hideReloadView()
                }
            }
            currentSelectPaperCutCategory.observe(viewLifecycleOwner) { currentSelectPaperCutCategory ->
                currentSelectPaperCutCategory?.let {
                    noteMaterialViewModel.selectedMaterial = false
                    noteMaterialViewModel.refreshPaperCutStatus()
                    binding.noteMaterialView.selectPaperType(it)
                    noteMaterialWindow?.selectPaperType(it)
                }
            }
            currentCustomMaterialList.observe(viewLifecycleOwner) { currentCustomMaterialList ->
                if (!selectedMaterial && currentSelectPaperCutCategory.value == PaperCutTool.CUSTOM_MATERIAL
                ) {
                    binding.noteMaterialView.refreshPaperCutList(currentCustomMaterialList)
                    noteMaterialWindow?.refreshPaperCutList(currentCustomMaterialList)
                }
            }
            signForShowMaterial.observe(viewLifecycleOwner) { signForShowMaterial ->
                val customTools = customToolsViewModel.targetCustomTools.value ?: return@observe
                if (customTools.isEmpty()) return@observe
                binding.noteMajorToolLayout.setSignForShowMaterialVisible(
                    signForShowMaterial && !isLandOneThirdScreenOrLandHalfScreen(),
                    customToolsViewModel.getHideDoodleTools(customTools)
                )
                binding.noteMaterialView.refreshAdapter()
                noteMaterialWindow?.refreshAdapter()
            }
        }
        noteEditorBackStatusViewModel.backStatusFromMaterialTool.observe(viewLifecycleOwner) { backStatusFromMaterialTool ->
            when (backStatusFromMaterialTool) {
                NoteEditorBackStatus.BACK_FROM_ON_SAVE -> {
                    noteMaterialViewModel.changeCurrentSelectPaperCutCategory(PaperCutTool.CUSTOM_MATERIAL)
                    noteEditorBackStatusViewModel.changeBackStatusFromMaterialTool(
                        NoteEditorBackStatus.NORMAL
                    )
                }

                NoteEditorBackStatus.BACK_FROM_ON_TO_CROP -> {
                    noteMaterialViewModel.changeCurrentSelectPaperCutCategory(PaperCutTool.CUSTOM_MATERIAL)
                    noteEditorBackStatusViewModel.changeBackStatusFromMaterialTool(
                        NoteEditorBackStatus.NORMAL
                    )
                    doodleView.doAfterUpdateCurrentPageCallback {
                        doodleView.doAfterUpdateCurrentPageCallback(null)
                        if (noteEditorBackStatusViewModel.instantAlphaMaterial.value != null) {
                            noteEditorBackStatusViewModel.instantAlphaMaterial.value?.let { customMaterial ->
                                insertCustomMaterial(
                                    customMaterial,
                                    isFinished = {
                                        noteEditorBackStatusViewModel.instantAlphaMaterialAfterCrop.value?.let { customMaterial ->
                                            customMaterial.first?.let { material ->
                                                if (noteEditorBackStatusViewModel.isJustChangeAlpha) {
                                                    insertCustomMaterial(
                                                        material,
                                                        alpha = customMaterial.second
                                                    )
                                                } else {
                                                    insertCustomMaterialAfterCrop(
                                                        material,
                                                        alpha = customMaterial.second
                                                    )
                                                }
                                            }
                                        }
                                    }
                                )
                            }
                        } else {
                            noteEditorBackStatusViewModel.instantAlphaMaterialAfterCrop.value?.let { customMaterial ->
                                customMaterial.first?.let { material ->
                                    if (noteEditorBackStatusViewModel.isJustChangeAlpha) {
                                        insertCustomMaterial(
                                            material,
                                            alpha = customMaterial.second
                                        )
                                    } else {
                                        insertCustomMaterialAfterCrop(
                                            material,
                                            alpha = customMaterial.second
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                NoteEditorBackStatus.BACK_FROM_ON_CUSTOM_MATERIAL -> {
                    changeCurrentSidebarStatus(Sidebar.MATERIAL)
                    noteMaterialViewModel.changeCurrentSelectPaperCutCategory(PaperCutTool.CUSTOM_MATERIAL)
                    noteEditorBackStatusViewModel.changeBackStatusFromMaterialTool(
                        NoteEditorBackStatus.NORMAL
                    )
                }

                else -> {}
            }
        }
        addPageViewModel.onPageAdd.observe(viewLifecycleOwner) { onPageAdd ->
            if (onPageAdd == null) return@observe
            val position = onPageAdd.insertPosition
            val paper = onPageAdd.paper
            EditEvent.sendEditAddPageSuccess(
                paper.isBuiltin(),
                paper.builtinColorType,
                position,
                paper.builtinStyleType
            )
            changeCurrentSidebarStatus(Sidebar.NONE)
            addPageViewModel.clearAddResult()
            if (addPageViewModel.newPageJob == null) {
                addPageViewModel.newPageJob =
                    addPageViewModel.viewModelScope.launch(Dispatchers.IO) {
                        val viewingPageIndex = currentDoc.viewingPageIndex
                        val page = when (position) {
                            InsertPosition.REPLACE -> {
                                showLoadingDialog()
                                val page = editorViewModel.replacePage(
                                    currentDoc,
                                    paper,
                                    AddPageSource.version,
                                    pageViewPortOffsetToPaperInPoint = onPageAdd.pageViewPortOffsetToPaperInPoint,
                                    pageViewPortBackgroundColor = onPageAdd.pageViewPortBackgroundColor,
                                    pageBackgroundDrawableTemplate = onPageAdd.pageBackgroundDrawableTemplate
                                )
                                TemplatePageUsageHelper.deleteTemplatePage(page)
                                page
                            }

                            InsertPosition.NEXT -> {
                                val page = editorViewModel.newPageAfter(
                                    currentDoc,
                                    paper,
                                    AddPageSource.version,
                                    pageViewPortOffsetToPaperInPoint = onPageAdd.pageViewPortOffsetToPaperInPoint,
                                    pageViewPortBackgroundColor = onPageAdd.pageViewPortBackgroundColor,
                                    pageBackgroundDrawableTemplate = onPageAdd.pageBackgroundDrawableTemplate
                                )
                                if (checkViewBindingValidity()) {
                                    binding.thumbnailAndOutlineView.addThumbnailPageWithIndex(
                                        viewingPageIndex + 1,
                                        page
                                    )
                                }
                                page
                            }

                            InsertPosition.PREVIOUS -> {
                                val page = editorViewModel.newPageBefore(
                                    currentDoc,
                                    paper,
                                    AddPageSource.version,
                                    pageViewPortOffsetToPaperInPoint = onPageAdd.pageViewPortOffsetToPaperInPoint,
                                    pageViewPortBackgroundColor = onPageAdd.pageViewPortBackgroundColor,
                                    pageBackgroundDrawableTemplate = onPageAdd.pageBackgroundDrawableTemplate
                                )
                                if (checkViewBindingValidity()) {
                                    binding.thumbnailAndOutlineView.addThumbnailPageWithIndex(
                                        viewingPageIndex,
                                        page
                                    )
                                }
                                page
                            }

                            InsertPosition.LAST -> {
                                val page = editorViewModel.newPageLast(
                                    currentDoc,
                                    paper,
                                    AddPageSource.version,
                                    pageViewPortOffsetToPaperInPoint = onPageAdd.pageViewPortOffsetToPaperInPoint,
                                    pageViewPortBackgroundColor = onPageAdd.pageViewPortBackgroundColor,
                                    pageBackgroundDrawableTemplate = onPageAdd.pageBackgroundDrawableTemplate
                                )
                                if (checkViewBindingValidity()) {
                                    binding.thumbnailAndOutlineView.appendThumbnailPage(page)
                                }
                                page
                            }
                        }
                        withContext(Dispatchers.Main) {
                            if (page == Page.EMPTY) {
                                showStorageNotEnoughToEnableFunctionDialog()
                            } else {
                                if (isAdded) {
                                    setPage(page, isNewPage = true)
                                    //更新侧边栏
                                    notifyThumbnailListRangeUpdate(viewingPageIndex)
                                }
                            }
                            addPageViewModel.newPageJob = null
                        }
                    }
            }
        }

        addPageViewModel.onTemplateAdd.observe(viewLifecycleOwner) { onTemplateAdd ->
            if (onTemplateAdd == null) return@observe
            val insertPosition = onTemplateAdd.insertPosition
            val document = onTemplateAdd.document
            val position = onTemplateAdd.position
            changeCurrentSidebarStatus(Sidebar.NONE)
            addPageViewModel.clearAddResult()
            if (addPageViewModel.newPageJob == null) {
                addPageViewModel.newPageJob =
                    addPageViewModel.viewModelScope.launch(Dispatchers.Main) {
                        val pageIndex = when (insertPosition) {
                            InsertPosition.REPLACE -> currentDoc.viewingPageIndex
                            InsertPosition.NEXT -> currentDoc.viewingPageIndex + 1
                            InsertPosition.PREVIOUS -> currentDoc.viewingPageIndex
                            InsertPosition.LAST -> currentDoc.pageCount
                        }
                        noteViewModel.copyPageFromOthers(
                            insertPosition,
                            document,
                            position,
                            pageIndex
                        ) { page, resourceLost ->
                            if (null == page) {
                                if (resourceLost) {
                                    showTemplateFileErrorDialog()
                                } else {
                                    showStorageNotEnoughToEnableFunctionDialog()
                                }
                            } else {
                                if (insertPosition == InsertPosition.REPLACE) {
                                    binding.thumbnailAndOutlineView.setThumbnailPageWithIndex(
                                        pageIndex,
                                        page
                                    )
                                } else {
                                    binding.thumbnailAndOutlineView.addThumbnailPageWithIndex(
                                        pageIndex,
                                        page
                                    )
                                }
                                setPage(page, isNewPage = true)
                                TemplatePageUsageHelper.addTemplatePage(
                                    page,
                                    onTemplateAdd.template
                                )
                                //更新侧边栏
                                notifyThumbnailListRangeUpdate(pageIndex)
                            }

                            addPageViewModel.newPageJob = null
                        }
                    }
            }
        }
        binding.noteMaterialView.run {
            onNewTypeSelectedAction = {
                EditEvent.sendEditMaterialCategoryClick(it.noteMaterialCategory.name)
                noteMaterialViewModel.changeCurrentSelectPaperCutCategory(null)
                noteMaterialViewModel.selectNoteMaterialSticker(it)
            }
            onStickerClickedAction = {
                when (noteMaterialViewModel.currentStickerCategoryState.value) {
                    NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                        activity?.let { ToastUtils.windowTopCenter(it, R.string.need_buy_vip) }

                    }

                    NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD,
                    NoteMaterialViewModel.MaterialCategoryState.DOWNLOADED,
                        -> {
                        val filePath = it.file
                        if (filePath == null) {
                            activity?.let {
                                ToastUtils.windowTopCenter(it, R.string.need_download)
                            }
                        } else {
                            val file = File(filePath)
                            if (file.exists()) {
                                editorModeViewModel.switchMode(InputMode.IMAGE)
                                val uri = Uri.fromFile(file)
                                insertImageElement(uri)
                                StickerDataCollection.stickerData(
                                    it.noteMaterialSticker,
                                    DataType.NOTEBOOKS_STICKER_USE
                                )
                            } else {
                                activity?.let {
                                    ToastUtils.windowTopCenter(
                                        it,
                                        R.string.need_download
                                    )
                                }
                            }
                        }
                    }

                    else -> {}
                }
            }
            setPaperCutToolOnClickAction { it ->
                when (it) {
                    PaperCutToolType.INSTANT_ALPHA.position -> {
                        InstantAlphaEvent.sendMaterialToolKeying("banner")
                        showInstantAlpha()
                    }

                    PaperCutToolType.PAPER_CUT.position -> {
                        EditEvent.sendPaperCutClick()
                        showDecoupage()
                    }
                }
            }
            onCloseClickedAction = {
                changeCurrentSidebarStatus(Sidebar.NONE)
            }
            onConfirmClickedAction = {
                when (noteMaterialViewModel.currentStickerCategoryState.value) {
                    NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD -> {
                        noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.let {
                            EditEvent.sendEditMaterialDownloadClick(it.noteMaterialCategory.name)
                        }
                        if (NetworkUtils.isNetworkAvailable()) {
                            if (noteMaterialViewModel.isStorageNotEnoughToDownload()) {
                                showStorageNotEnoughToDownloadResourceDialog()
                            } else {
                                noteMaterialViewModel.downloadMaterialSticker(object :
                                    NoteMaterialViewModel.StickerItemDownloadCallback {
                                    override fun onProgress(
                                        stickerInfo: NoteMaterialRepository.NoteMaterialStickerInfo,
                                        progress: Float,
                                    ) {
                                        refreshSticker(stickerInfo, progress)
                                    }

                                    override fun onSuccess(
                                        stickerInfo: NoteMaterialRepository.NoteMaterialStickerInfo,
                                        file: String,
                                    ) {
                                        refreshSticker(stickerInfo, file)
                                    }
                                }) { _, name, allDownloaded ->
                                    if (allDownloaded) {
                                        activity?.let {
                                            ToastUtils.windowTopCenter(
                                                it,
                                                getString(R.string.download_success, name)
                                            )
                                        }

                                        noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.let {
                                            StickerDataCollection.stickerCategoryData(
                                                it.noteMaterialCategory,
                                                DataType.NOTEBOOKS_STICKER_CATEGORY_DOWNLOAD
                                            )
                                        }
                                    }
                                }
                            }
                        } else {
                            activity?.let {
                                ToastUtils.windowTopCenter(
                                    it,
                                    R.string.toast_no_internet
                                )
                            }
                        }
                    }

                    NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                        noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.let {
                            EditEvent.sendEditMaterialGoToVipClick(it.noteMaterialCategory.name)
                        }
                        val action = NoteEditorFragmentDirections.actionNoteEditorToVipStore()
                        safeNavigate(action)
                    }

                    else -> {
                    }
                }
            }
            onReloadClickedAction = {
                if (NetworkUtils.isNetworkAvailable()) {
                    noteMaterialViewModel.reload()
                } else {
                    activity?.let {
                        ToastUtils.windowTopCenter(it, R.string.toast_no_internet)
                    }
                }
            }
            post {
                noteMaterialViewModel.initLoadNoteMaterialCategoryStickerList()
            }
            onCustomMaterialTypeSelectedAction = {
                if (it == PaperCutTool.CUSTOM_MATERIAL) {
                    EditEvent.sendCustomMaterialClick()
                } else if (it == PaperCutTool.PAPER_CUT_TOOL) {
                    UserUsageConfig.isFirstUseMaterialTool = false
                    EditEvent.sendMaterialToolClick()
                }
                noteMaterialViewModel.selectNoteMaterialSticker(null)
                noteMaterialViewModel.changeCurrentSelectPaperCutCategory(it)
            }
            onCustomMaterialItemDelClicked = { customMaterial, _ ->
                AlertDialog.Builder()
                    .setTitle(resources.getString(R.string.paper_cut_del_tips))
                    .setPositiveBtn(resources.getString(R.string.paper_cut_del_confirm)) {
                        noteMaterialViewModel.deleteCustomMaterial(customMaterial)
                    }
                    .setNegativeBtnColor(getColor(R.color.text_secondary))
                    .setNegativeBtn(resources.getString(R.string.cancel)) { }
                    .build().show(childFragmentManager, "")
            }
            onCustomMaterialSwap = {
                noteMaterialViewModel.saveCustomMaterials(it)
            }
            onCustomMaterialClick = {
                insertCustomMaterial(it)
            }
            materialListScrollPositionChange = {
                noteMaterialViewModel.changeMaterialListScrollPosition(it)
            }
            materialListVisibilityChange = { first, last ->
                val stickerList =
                    noteMaterialViewModel.currentMaterialStickerList.value?.map { it.noteMaterialSticker }
                val categoryId =
                    noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.noteMaterialCategory?.id
                if (stickerList != null && categoryId != null) {
                    StickerDataCollection.stickerListData(
                        stickerList,
                        categoryId,
                        first,
                        last,
                        DataType.NOTEBOOKS_STICKER_VIEW
                    )
                }
            }
        }
        binding.noteMajorToolLayout.showMaterial.setOnClickListener(AntiShakeClickListener {
            EditEvent.sendEditMaterialClick()
            if (sideBarViewModel.currentSidebarState.value == Sidebar.MATERIAL) {
                changeCurrentSidebarStatus(Sidebar.NONE)
            } else {
                changeCurrentSidebarStatus(Sidebar.MATERIAL)
            }
        }
        )
    }

    private fun showMoreColorWindow(anchorView: View? = null) {
        binding.toolBar.post {
            if (!isAdded) return@post
            val guideWindow = ColorGuideWindow(requireContext()).apply {
                toolbarEdge = adsorptionEdgeViewModel.edge
            }
            guideWindow.show(if (anchorView == null) binding.toolBar else anchorView)
        }
    }

    private fun addTemplate(template: Template, document: Document) {
        if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE) {
            AlertDialog.Builder()
                .setTitle(resources.getString(R.string.page_replace_title))
                .setMsg(resources.getString(R.string.page_replace_tip))
                .setPositiveBtn(resources.getString(R.string.add_page_replace)) {
                    addPageViewModel.addTemplate(
                        template,
                        addPageViewModel.insertPosition.value!!,
                        document,
                        0
                    )
                    changeCurrentSidebarStatus(Sidebar.NONE)
                }
                .setNegativeBtnColor(getColor(R.color.text_secondary))
                .setNegativeBtn(resources.getString(R.string.cancel)) { }
                .build().show(childFragmentManager, "")
        } else {
            addPageViewModel.addTemplate(
                template,
                addPageViewModel.insertPosition.value!!,
                document,
                0
            )
            changeCurrentSidebarStatus(Sidebar.NONE)
        }
    }


    private fun getFontDownloadDialog(): FontDownloadProgressDialog? {
        val fragmentManager = activity?.supportFragmentManager ?: return null
        val dialog = fragmentManager.findFragmentByTag(FontDownloadProgressDialog.TAG)
        return if (dialog is FontDownloadProgressDialog) {
            dialog
        } else {
            null
        }
    }

    private fun showFontDownloadDialog(): FontDownloadProgressDialog? {
        val fragmentManager = activity?.supportFragmentManager ?: return null
        val dialog = fragmentManager.findFragmentByTag(FontDownloadProgressDialog.TAG)
        if (dialog is FontDownloadProgressDialog && dialog.isVisible) return null
        return FontDownloadProgressDialog().apply {
            setOnCloseListener {
                fontDownloadViewModel.cancelAllDownload()
                fontDownloadViewModel.removeNetworkChangeListener()
                fontDownloadViewModel.resetDownloadState()
            }
            show(fragmentManager, FontDownloadProgressDialog.TAG)
        }
    }

    private fun updateDoodleDeviceMode() {
        binding.doodle.configDeviceMode(UserUsageConfig.isSupportBluetoothPen)
        draftPaperViewModel.configDeviceMode(UserUsageConfig.isSupportBluetoothPen)
        binding.doodle.bluetoothPenConnectState = isPenConnected()
    }

    private fun initBluetoothConnectStatus() {
        val isPenConnected = isPenConnected()
        moreToolWindow?.binding?.bluetoothConnectSwitch?.isChecked =
            UserUsageConfig.isSupportBluetoothPen
        binding.doodle.bluetoothPenConnectState = isPenConnected
        updateDoodleDeviceMode()
    }

    private fun isPenConnected(): Boolean = isPenConnected(inputManager)

    private fun sendPadAndInputDevice(device: InputDevice?) {
        device?.let { inputDevice ->
            if (Build.BRAND.equals(BRAND_SAMSUNG, true)) {
                if (!isExternal(inputDevice)) {
                    InputDeviceEvent.sendPadAndInputDeviceDetail(inputDevice)
                }
            } else {
                if (isExternal(inputDevice)) {
                    val inputDeviceVendorAndProductId = "${device.vendorId}-${device.productId}"
                    val currentVendorAndProductIds = UserUsageConfig.inputDeviceVendorAndProductIds
                    if (!currentVendorAndProductIds.contains(inputDeviceVendorAndProductId)) {
                        val newVendorAndProductIds = mutableListOf<String>()
                        newVendorAndProductIds.addAll(currentVendorAndProductIds)
                        newVendorAndProductIds.add(inputDeviceVendorAndProductId)
                        InputDeviceEvent.sendPadAndInputDeviceDetail(device)
                        UserUsageConfig.inputDeviceVendorAndProductIds = newVendorAndProductIds
                    }
                }
            }
        }
    }

    override fun getToolColorAdapter(): ToolColorAdapter? {
        return (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.find {
            it is ToolColorAdapter
        } as? ToolColorAdapter
    }

    private fun getToolTextSizeAdapter(): ToolTextSizeAdapter? {
        return (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.find {
            it is ToolTextSizeAdapter
        } as? ToolTextSizeAdapter
    }

    private fun getToolTextParagraphStyleAdapter(): ToolTextParagraphAdapter? {
        return (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.find {
            it is ToolTextParagraphAdapter
        } as? ToolTextParagraphAdapter
    }

    private fun getToolTextFontAdapter(): ToolTextFontAdapter? {
        return (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.find {
            it is ToolTextFontAdapter
        } as? ToolTextFontAdapter
    }

    override fun createWindowColorPickView(): WindowColorPickView {
        return WindowColorPickView(
            requireContext(),
            resources.getDimensionPixelSize(R.dimen.dp_40).toFloat(),
            resources.getDimensionPixelSize(R.dimen.dp_20).toFloat(),
            resources.getDimensionPixelSize(R.dimen.dp_4).toFloat(),
            Color.BLACK,
            ResourcesCompat.getDrawable(
                resources,
                R.drawable.pick_color_icon_indicator,
                null
            ),
            ResourcesCompat.getDrawable(
                resources,
                R.drawable.pick_color_icon_confirm,
                null
            ),
            ResourcesCompat.getDrawable(resources, R.drawable.pick_color_icon_cancel, null),
        )
    }

    override fun createSelectColorWindow(
        color: Int,
        colorList: List<ColorWindowItem>,
        type: Int,
    ): BaseSelectColorWindow {
        return SelectColorWindow(
            requireContext(),
            if (DimensionUtil.getOrientation(requireContext()) == Configuration.ORIENTATION_PORTRAIT
                && DimensionUtil.getMultiWindowRatio(requireContext()) <= DimensionUtil.ONE_THIRD_SCREEN_LIMIT
            ) {
                SelectColorWindow.LAYOUT_HORIZONTAL
            } else {
                SelectColorWindow.LAYOUT_VERTICAL
            },
            color,
            colorList.filter {
                it.type.ordinal == ColorWindowItemType.PRESET_COLOR.ordinal
            },
            Color.alpha(color)
        )
    }

    override fun onPenColorSelected(color: ColorWindowItem) {
        super.onPenColorSelected(color)
        getToolColorAdapter()?.setCurrentColor(color.res)
        selectColorWindow?.dismiss()
    }

    override fun onHighlighterColorSelected(color: ColorWindowItem) {
        super.onHighlighterColorSelected(color)
        getToolColorAdapter()?.setCurrentColor(color.res)
        selectColorWindow?.dismiss()
    }

    override fun onTextColorSelected(color: ColorWindowItem) {
        super.onTextColorSelected(color)
        getToolColorAdapter()?.setCurrentColor(color.res)
        selectColorWindow?.dismiss()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (checkViewBindingValidity()) {
            binding.webSidebarView.saveWebViewState(outState)
        }
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        if (savedInstanceState != null) {
            binding.webSidebarView.restoreWebViewState(savedInstanceState)
        }
    }

    private fun changeShadow(edge: AdsorptionEdgeLayout.EDGE) {
        if (!checkViewBindingValidity()) return
        binding.minorToolContainer.apply {
            setShadowOffsetX(0F)
            when (edge) {
                AdsorptionEdgeLayout.EDGE.LEFT -> {
                    setShadowHiddenLeft(true)
                    setShadowHiddenTop(true)
                    setShadowHiddenRight(false)
                    setShadowHiddenBottom(true)
                }

                AdsorptionEdgeLayout.EDGE.BOTTOM -> {
                    setShadowHiddenLeft(true)
                    setShadowHiddenTop(false)
                    setShadowHiddenRight(true)
                    setShadowHiddenBottom(true)
                }

                AdsorptionEdgeLayout.EDGE.TOP -> {
                    setShadowHiddenLeft(true)
                    setShadowHiddenTop(true)
                    setShadowHiddenRight(true)
                    setShadowHiddenBottom(false)
                }

                AdsorptionEdgeLayout.EDGE.RIGHT -> {
                    setShadowHiddenLeft(false)
                    setShadowHiddenTop(true)
                    setShadowHiddenRight(true)
                    setShadowHiddenBottom(true)
                }
            }
        }
    }

    private fun jumpToPageSizingFragment() {
        noteViewModel.currentDoc?.let { currentDoc ->
            pageViewPortAdjustmentViewModel.initData(
                currentDoc,
                currentDoc.getCurrentPage().cloneWithIdNotChange()
            )
        }
        safeNavigate(NoteEditorFragmentDirections.actionNoteEditorToPageSizing())
    }

    override fun onDestroyView() {
        editorAdViewModel.setShouldAutoLoadInterstitialAdWhenOverTime(false)
        if (!invalidCurrentDoc()) {
            UserUsageConfig.setNeedShowPageResizingTip(currentDoc, false)
        }
        ConsoleCommandManager.commandExecuteCallback = null
        ConsoleCommandManager.unregisterCommandReceiver(this)
        ConsoleCommandManager.unregisterCommandReceiver(binding.doodle)
        doodleView.modelManager.getInsertableObjects().forEach { insertableObject ->
            insertableObject.clearPropertyChangedListener()
        }
        binding.webSidebarView.destroyWebView()
        super.onDestroyView()
        inputManager.unregisterInputDeviceListener(inputDeviceListener)
        //为了popWindow销毁时，不改变vm中的状态
        popWindow?.setOnDismissListener { }
        popWindow?.dismiss()
        selectPenSizeWindow?.dismiss()
        selectPenSizeWindow = null
        selectTextSizeWindow?.dismiss()
        selectTextSizeWindow = null
        selectParagraphStyleWindow?.dismiss()
        selectParagraphStyleWindow = null
        recordListWindow?.setOnDismissListener { }
        recordListWindow?.dismiss()
        recordListWindow = null
        recordSettingWindow?.setOnDismissListener { }
        recordSettingWindow?.dismiss()
        recordSettingWindow = null
        consoleSettingWindow?.setOnDismissListener { }
        consoleSettingWindow?.dismiss()
        consoleSettingWindow = null
        draftPaperMorePopupGuideWindow?.setOnDismissListener { }
        draftPaperMorePopupGuideWindow?.dismiss()
        draftPaperMorePopupGuideWindow = null
        penToolsGraphGuideWindow?.setOnDismissListener { }
        penToolsGraphGuideWindow?.dismiss()
        penToolsGraphGuideWindow = null
        draftPaperFunctionIntroductionGuideWindow?.setOnDismissListener { }
        draftPaperFunctionIntroductionGuideWindow?.dismiss()
        draftPaperFunctionIntroductionGuideWindow = null
        moreToolWindow?.setOnDismissListener { }
        moreToolWindow?.dismiss()
        moreToolWindow = null
        customSelectToolsWindow?.setOnDismissListener { }
        customSelectToolsWindow?.dismiss()
        customSelectToolsWindow = null
        noteSnippetGuideWindow?.setOnDismissListener { }
        noteSnippetGuideWindow?.dismiss()
        noteSnippetGuideWindow = null
        exportNoteWindow?.setOnDismissListener { }
        exportNoteWindow?.dismiss()
        exportNoteOneThirdWindow?.setOnDismissListener { }
        exportNoteOneThirdWindow?.dismiss()
        shareImgWindow?.setOnDismissListener { }
        shareImgWindow?.dismiss()
        shareImgOneThirdWindow?.setOnDismissListener { }
        shareImgOneThirdWindow?.dismiss()
        noteMaterialWindow?.setOnDismissListener {}
        noteMaterialWindow?.dismiss()
        noteMaterialWindow = null
        noteSnippetDragGuideWindow?.setOnDismissListener { }
        noteSnippetDragGuideWindow?.dismiss()
        noteSnippetDragGuideWindow = null
        outlineCreateDialog?.setOnDismissListener {}
        fontListWindow?.dismiss()
        fontListWindow = null
        tapeStyleShowControlWindow?.dismiss()
        tapeStyleShowControlWindow = null
        consoleGuideWindow?.setOnDismissListener { }
        consoleGuideWindow?.dismiss()
        consoleGuideWindow = null
        TextRecognitionManager.removeHighPriorityTaskFinishCallback(onFinishAction)
        TextRecognitionManager.removeRecognitionTimeOutCallback()
        aiViewModel.endSession()
        draftPaperFragment?.lifecycle?.removeObserver(draftPaperLifecycleEventObserver)
        doodleView.doodleTouchLayer.removeOnTransformChangeListeners(onTransformChanged)
        scaleLockedHiddenDelayTimer.cancel()
        undoAndRedoDelayTimer.cancel()
    }

    private fun initThumbnailAndOutlineView() {
        binding.thumbnailAndOutlineView.apply {
            setDocument(currentDoc)
            setOnThumbnailItemClickListener { position ->
                showLoadingDialog()
                onThumbnailChecked(position)
            }
            setOnThumbnailMenuClickListener { params ->
                noteViewModel.pageThumbnailActionWindowStatus.postValue(params)
            }
            setThumbnailTouchAction(this@NoteEditorFragment)
            setAfterThumbnailScrollStateChange { position, offset ->
                noteViewModel.pageThumbnailScrollPosition.postValue(
                    ThumbnailListScrollPosition(
                        position,
                        offset
                    )
                )
            }
            setOnThumbnailPageIndexChangeListener {
                updatePageIndex()
            }
            setAfterThumbnailCurrentPageChange {
                currentDoc.updateOutline {
                    notifyOutlineDataSetChanged()
                }
                NoteRepository.runInNoteOperationScopeAsync {
                    NoteRepository.saveDocumentInfo(currentDoc)
                }
            }
            setAfterInitThumbnailList {
                thumbnailViewModel.updatePosition.observe(viewLifecycleOwner) { updateIndex ->
                    val firstVisibleItemPosition =
                        binding.thumbnailAndOutlineView.findThumbnailFirstVisibleItemPosition()

                    val lastVisibleItemPosition =
                        binding.thumbnailAndOutlineView.findThumbnailLastVisibleItemPosition()
                    if (updateIndex in firstVisibleItemPosition..lastVisibleItemPosition) {
                        binding.thumbnailAndOutlineView.notifyThumbnailItemChanged(updateIndex)
                    }
                    if (exportNoteWindow?.isShowing == true) {
                        exportNoteWindow?.notifyItemChanged(updateIndex)
                    }
                    if (exportNoteOneThirdWindow?.isShowing == true) {
                        exportNoteOneThirdWindow?.notifyItemChanged(updateIndex)
                    }
                }
            }
            setAfterUpdatePageIndexCallBack { previousPage, nextPage, pageIndex ->
                if (!checkViewBindingValidity()) return@setAfterUpdatePageIndexCallBack
                binding.doodle.post {
                    val doodleWidth = binding.doodle.width
                    val doodleHeight = binding.doodle.height
                    coroutineScope.async(Dispatchers.IO) {
                        val currentPage = currentDoc[pageIndex]
                        previousPage?.updateBackground(currentDoc, doodleWidth, doodleHeight)
                        nextPage?.updateBackground(currentDoc, doodleWidth, doodleHeight)
                        binding.doodle.doodleTouchLayer.updateThumbnail(
                            currentDoc,
                            currentPage,
                            previousPage,
                            nextPage
                        )
                        binding.doodle.doodleTouchLayer.updateThumbnailForSlideAddPag(
                            currentDoc,
                            currentPage,
                            previousPage,
                            nextPage
                        )
                    }
                }
            }
            setOnThumbnailTabClickListener {
                thumbnailAndOutlineViewStatusViewModel.setThumbnailAndOutlineViewSelectThumbnail()
            }
            setOnOutlineTabClickListener {
                thumbnailAndOutlineViewStatusViewModel.setThumbnailAndOutlineViewSelectOutline()
            }
            setOnOutlineEditBtnClickListener {
                thumbnailAndOutlineViewStatusViewModel.setOutlineViewIsEditMode(true)
            }

            setOnOutlineEditAllSelectedClickListener { isSelected ->
                if (isSelected) {
                    thumbnailAndOutlineViewStatusViewModel.addAllCurrentSelectedOutline(currentDoc.outlineList)
                } else {
                    thumbnailAndOutlineViewStatusViewModel.clearCurrentSelectedOutline()
                }
            }
            setOnOutlineEditDeleteBtnClickListener {
                showDeleteOutlineInBatchesAlertDialog()
            }
            setOnOutlineEditFinishBtnClickListener {
                thumbnailAndOutlineViewStatusViewModel.setOutlineViewIsEditMode(false)
                thumbnailAndOutlineViewStatusViewModel.clearCurrentSelectedOutline()
            }
            setOnOutlineAddBtnClickListener {
                thumbnailAndOutlineViewStatusViewModel.showOutlineCreateDialog()
            }

            setOnOutlineItemEditModeClickListener { outlineEntity ->
                OutlineEvent.outlineDialogCreateSource = OutlineEvent.EDIT_ADD
                thumbnailAndOutlineViewStatusViewModel.showOutlineEditDialog(outlineEntity)
            }
            setOnOutlineItemEditModeCheckedListener { outlineEntity, isSelected ->
                if (isSelected) {
                    thumbnailAndOutlineViewStatusViewModel.addCurrentSelectedOutline(outlineEntity)
                } else {
                    thumbnailAndOutlineViewStatusViewModel.removeCurrentSelectedOutline(
                        outlineEntity
                    )
                }
            }
            setOnOutlineItemNormalModeClickListener { outline ->
                val page =
                    DocumentManager.findPageInDocumentByPageUUID(
                        outline.linkedPageUUID,
                        currentDoc
                    )
                if (page != null) {
                    val pageIndex = currentDoc.pages.indexOf(page)
                    if (pageIndex >= 0 && isAdded) {
                        currentDoc.viewingPageIndex = pageIndex
                        setPage(page)
                    }
                }
            }
        }


    }

    private fun showDeleteOutlineInBatchesAlertDialog(isRestore: Boolean = false) {
        thumbnailAndOutlineViewStatusViewModel.currentSelectedOutlineEntities.value?.let { currentSelectedOutlineList ->
            val deleteOutlineAlertDialog = if (isRestore) {
                parentFragmentManager.findFragmentByTag(OUTLINE_DELETE_ALERT_DIALOG_TAG) as? AlertDialog
            } else {
                AlertDialog.Builder()
                    .setTitle(resources.getString(R.string.outline_delete_alert_dialog_title))
                    .setMsg(
                        resources.getString(
                            R.string.outline_delete_alert_dialog_msg,
                            "${currentSelectedOutlineList.size}"
                        )
                    )
                    .setPositiveBtn(resources.getString(R.string.cancel)) {}
                    .setPositiveBtnColor(getColor(R.color.text_secondary))
                    .setNegativeBtn(resources.getString(R.string.delete)) {}
                    .setNegativeBtnColor(getColor(R.color.note_list_delete_doc_text_color))
                    .build()
            }
            deleteOutlineAlertDialog?.setNegativeBtnListener {
                currentDoc.deleteOutlineInBatches(currentSelectedOutlineList) {
                    NoteRepository.runInNoteOperationScopeAsync {
                        saveDocumentInfo(currentDoc)
                    }
                    context?.let { context ->
                        ToastUtils.topCenter(
                            context,
                            resources.getString(R.string.note_outline_delete_success)
                        )
                    }
                    thumbnailAndOutlineViewStatusViewModel.clearCurrentSelectedOutline()
                    if (currentDoc.outlineList.isEmpty()) {
                        thumbnailAndOutlineViewStatusViewModel.setOutlineViewIsEditMode(false)
                    }
                    binding.thumbnailAndOutlineView.notifyOutlineDataSetChanged()
                }
                OutlineEvent.sendOutlineDeleteInBatchConfirm()
            }
            if (!isRestore) {
                deleteOutlineAlertDialog?.show(
                    parentFragmentManager,
                    OUTLINE_DELETE_ALERT_DIALOG_TAG
                )
            }
        }
    }

    private var snippetOptionWindow: SnippetOptionWindow? = null
    private val searchDelayTimer = Timer()

    private fun initPagesSnippet() {
        binding.pageSnippetList.apply {
            setUpColorSelector(
                SnippetManager.DEFAULT_SNIPPET_COLOR_LIST,
                snippetViewModel.currentSelectedColor.value,
                !(snippetViewModel.snippetListIsSelect.value ?: false)
            )

            setOnLabelSelectorIsFoldIconClickListener {
                snippetViewModel.switchLabelSelectorIsFold()
                SnippetEvent.sendSnippetLabelSelectorIsFoldClick()
            }

            setOnLabelSelectorItemClickListener { label ->
                //便签item点击回调,label为标签对象
                snippetViewModel.setCurrentSelectedLabel(label)
            }

            setOnColorSelectorItemClickListener { color ->
                //颜色item点击回调,color为颜色Int值;选择「全部颜色」时回调color=null
                snippetViewModel.setCurrentSelectedColor(color)

            }
            optionRoomClick = { isExpand ->
                if (isExpand) {
                    snippetViewModel.checkAndChangeSnippetListViewState(
                        SnippetViewModel.SnippetListViewState.SHOWN,
                        SnippetViewModel.SnippetListViewState.EXPANDED
                    )
                } else {
                    snippetViewModel.checkAndChangeSnippetListViewState(
                        SnippetViewModel.SnippetListViewState.EXPANDED,
                        SnippetViewModel.SnippetListViewState.SHOWN
                    )
                }
            }
            optionSelectClick = { isSelect ->
                if (isSelect) {
                    snippetViewModel.checkAndChangeSnippetListViewState(
                        SnippetViewModel.SnippetListViewState.SHOWN,
                        SnippetViewModel.SnippetListViewState.EXPANDED
                    )
                } else {
                    snippetViewModel.checkAndChangeSnippetListViewState(
                        SnippetViewModel.SnippetListViewState.EXPANDED,
                        SnippetViewModel.SnippetListViewState.SHOWN
                    )
                }
                snippetViewModel.changeSnippetListSelectStatus(isSelect)
            }
            optionAddLabelClick = {
                val selectedSnippet = mutableListOf<NoteSnippet>()
                snippetViewModel.selectedSnippetIndex.forEach { index ->
                    getAllSnippets().getOrNull(index)?.snippet?.let { noteSnippet ->
                        selectedSnippet.add(noteSnippet)
                    }
                }
                snippetManagerViewModel.showManagerOrMoreDialogTag =
                    SNIPPET_MORE_SHOW_FLAG
                snippetManagerViewModel.currentAddedTags = emptyList()

                snippetManagerViewModel.currentSelectedSnippetList = selectedSnippet
                snippetManagerViewModel.changeManagerSnippetTagVisible(
                    true
                )
                doodleView.modelManager.unselect()
            }
            snippetClickCallback = snippetClickCallback@{ position, view ->
                editorModeViewModel.switchModeImmediately(InputMode.SNIPPET)
                if (snippetViewModel.snippetListIsSelect.value != true) {
                    val noteSnippetItem = getItem(position)
                    snippetViewModel.changeCurrentSelectSnippetPosition(position)
                    if (noteSnippetItem == null) return@snippetClickCallback
                    if (currentDoc.uuid == noteSnippetItem.snippet.documentId) {
                        val pageIndex =
                            currentDoc.pages.indexOfFirst { page -> page.uuid == noteSnippetItem.snippet.pageId }
                        if (pageIndex >= 0 && isAdded) {
                            if (pageIndex == currentDoc.viewingPageIndex) {
                                doodleView.modelManager.snippetEditor.highlightSnippet(
                                    noteSnippetItem.snippet
                                )
                            } else {
                                onSnippetListenerSetFinishedAction = {
                                    if (isAdded) {
                                        doodleView.modelManager.snippetEditor.highlightSnippet(
                                            noteSnippetItem.snippet
                                        )
                                    }
                                    onSnippetListenerSetFinishedAction = null
                                }
                                currentDoc.viewingPageIndex = pageIndex
                                setPage(currentDoc[pageIndex])
                            }
                        }
                    }
                    snippetOptionWindow = SnippetOptionWindow(context).apply {
                        optionCallback = { snippetOption ->
                            dismiss()
                            if (<EMAIL>) {
                                val snippet = noteSnippetItem.snippet
                                when (snippetOption) {
                                    SnippetOption.EDIT_TITLE -> {
                                        <EMAIL>(
                                            position
                                        )
                                        doodleView.modelManager.snippetEditor.unselectSnippet()
                                    }

                                    SnippetOption.ADD_LABEL -> {
                                        snippetViewModel.requestNoteSnippetTags(snippet.snippetId.toString()) { _, _, addedTags ->
                                            snippetManagerViewModel.currentSelectedSnippetList =
                                                listOf(snippet)
                                            snippetManagerViewModel.showManagerOrMoreDialogTag =
                                                SNIPPET_MORE_SHOW_FLAG
                                            snippetManagerViewModel.currentAddedTags = addedTags
                                            snippetManagerViewModel.changeManagerSnippetTagVisible(
                                                true
                                            )
                                        }
                                        doodleView.modelManager.snippetEditor.unselectSnippet()
                                    }

                                    SnippetOption.DELETE -> {
                                        snippetViewModel.deleteSnippet(snippet)
                                        doodleView.modelManager.snippetEditor.deleteSnippet(snippet)
                                    }
                                }
                            } else {
                                LogHelper.d(
                                    defaultTag,
                                    "this NoteEditorFragment is not added on optionCallback"
                                )
                            }
                        }
                        setOnDismissListener {
                            snippetOptionWindow = null
                        }
                    }
                    snippetOptionWindow?.showAsBubble(
                        view,
                        getSnippetListTranslationY(),
                        BubbleLayout.BubbleOrientation.LEFT
                    )
                } else {
                    getItem(position)?.let {
                        it.isSelected = !it.isSelected
                        if (it.isSelected) {
                            snippetViewModel.selectedSnippetIndex.add(position)
                        } else {
                            snippetViewModel.selectedSnippetIndex.remove(position)
                        }
                    }
                    <EMAIL> {
                        refreshItemByPosition(
                            position
                        )
                        refreshSelectedNumber(snippetViewModel.selectedSnippetIndex.size)
                    }
                }
            }
            snippetTitleChangedCallback = {
                snippetViewModel.updateSnippet(it.snippet)
            }
            onSearchModeSwitch = { inSearchMode ->
                if (inSearchMode) {
                    snippetViewModel.enterSearchMode()
                    if (snippetViewModel.searchingKeyword.isEmpty()) {
                        search("") // 将卡片列表置空
                    }
                } else {
                    snippetViewModel.exitSearchMode()
                }
            }
            onSearchTextChanged = { searchKeyword: String ->
                if (snippetViewModel.inSearchMode) {
                    searchDelayTimer.cancel()
                    searchDelayTimer.start(delayedMs = SEARCH_DELAY_INTERVAL) {
                        search(searchKeyword)
                    }
                }
            }

            onSearchBoxFocusChanged = { focused -> snippetViewModel.searchBoxFocused = focused }

            refreshSnippetListCallback = {
                <EMAIL>()
                if (isCreateSnippet) {
                    smoothScrollToTop()
                    isCreateSnippet = false
                }
                if (snippetOptionWindow?.isShowing == true) {
                    snippetOptionWindow?.dismiss()
                }
            }
        }
    }

    private fun search(keyword: String) {
        snippetViewModel.currentSelectedLabel.value?.let { currentTag ->
            lifecycleScope.launch {
                val documentId =
                    if (currentTag.pseudoTagFlag == SnippetTag.FLAG_TAG_PSEUDO_CURRENT_NOTE) currentDoc.uuid else null
                if (snippetViewModel.searchFlow(
                        currentTag,
                        snippetViewModel.currentSelectedColor.value,
                        documentId,
                        keyword,
                        emptyResultForEmptyKeyword = true
                    )
                ) {
                    if (checkViewBindingValidity()) {
                        binding.pageSnippetList.refreshPagingSource()
                    }
                }
            }
        }
    }

    private fun refreshCurrentSnippetItemList() {
        if (checkViewBindingValidity()) {
            binding.pageSnippetList.refreshPagingSource()
        }
    }

    private fun showFileCorruptedDialog(document: Document): AlertDialog? {
        val fragmentManager = activity?.supportFragmentManager ?: return null
        val dialog = fragmentManager.findFragmentByTag(FILE_CORRUPTED_DIALOG_TAG)
        if (dialog is AlertDialog && dialog.isVisible) return null
        return AlertDialog.Builder()
            .setTitle(getString(R.string.notebook_exception_dialog_title))
            .setMsg(
                getString(
                    R.string.notebook_exception_dialog_message
                )
            )
            .setPositiveBtn(getString(R.string.export)) {
                EditEvent.sendEditExportClick()
                exportViewModel.resetExportDocument(document)
                exportViewModel.exportTypeMode = ExportTypeMode.ZIP_TYPE
                exportViewModel.export(requireContext())
            }
            .setNegativeBtn(getString(R.string.cancel)) {
            }
            .build().apply {
                show(fragmentManager, FILE_CORRUPTED_DIALOG_TAG)
            }
    }

    private fun showDocumentPageMissingDialog() {
        val dialog = AlertDialog.Builder()
            .setTitle(getString(R.string.file_corrupted_title))
            .setMsg(
                if (BuildConfig.FLAVOR_CHANNEL == Channel.PLAY.channel) getString(R.string.page_missing_content) else getString(
                    R.string.page_missing_content_not_gp
                )
            )
            .setPositiveBtn(getString(R.string.ok)) {}
            .build()
        dialog.show(parentFragmentManager, DOCUMENT_PAGE_MISSING_DIALOG_TAG)
    }

    private fun setPageAndUpdateHighlightRect(page: Page, docRenderInfo: DocRenderInfo?) {
        if (doodleView.isSamePage(page)) {
            doodleView.doodleStickyOnTopLayer.updateHighlightRect(docRenderInfo)
            return
        }
        if (doodleView.modelManager.currentPage == null) {
            return
        }
        setPage(page, false, docRenderInfo)
    }

    override fun setPage(page: Page, isNewPage: Boolean, docRenderInfo: DocRenderInfo?) {
        if (!checkViewBindingValidity()) return
        super.setPage(page, isNewPage, docRenderInfo)
        showPageResizingTipIfNeed()
        val pageIndex = currentDoc.viewingPageIndex
        binding.thumbnailAndOutlineView.changeThumbnailCheckedItem(pageIndex)
        binding.thumbnailAndOutlineView.updateCurrentSelectedOutlineList(
            DocumentManager.findCurrentViewPageOutlineEntities(
                currentDoc
            )
        )
        binding.doodle.doOnSizeConfirmed {
            if (!checkViewBindingValidity()) return@doOnSizeConfirmed

            if (noteViewModel.orientationChange.value!!.listScroll) {
                noteViewModel.orientationChange.value!!.listScroll = false

                val scrollPosition = noteViewModel.pageThumbnailScrollPosition.value!!
                binding.thumbnailAndOutlineView.scrollThumbnailToPositionWithOffset(
                    scrollPosition.position,
                    scrollPosition.offset
                )
            } else {
                if (binding.noteMajorToolLayout.showThumbnail.isSelected
                    && pageIndex !in binding.thumbnailAndOutlineView.findThumbnailFirstVisibleItemPosition()..binding.thumbnailAndOutlineView.findThumbnailLastVisibleItemPosition()
                ) {
                    binding.thumbnailAndOutlineView.scrollThumbnailToPositionWithOffset(
                        pageIndex,
                        0
                    )
                } else {
                    binding.thumbnailAndOutlineView.scrollThumbnailToPosition(pageIndex)
                }
            }
        }
        binding.noteAddPageLayout.refreshTemplate()
    }

    override fun onPageSet(page: Page) {
        super.onPageSet(page)
        lifecycleScope.launch(Dispatchers.IO) {
            val docSearchResult =
                searchViewModel.getCurrentPageSearchResult(currentDoc.uuid, page.uuid)
            withContext(Dispatchers.Main) {
                searchViewModel.setCurrentDocSearchResult(docSearchResult)
            }
        }
        refreshSearchBackPageIcon(page)
        setRecordToolsClickListener()
        doodleView.doodleEditLayer.verifyRecordStateAction = IVerifyRecordStateAction {
            val isRecording = recordViewModel.isRecording.value ?: false
            if (isRecording) {
                activity?.let {
                    ToastUtils.windowTopCenter(
                        it,
                        R.string.note_record_recording_in_progress_not_allowed_paly
                    )
                }
            }
            !isRecording
        }
        if (noteViewModel.checkNeedShowConsoleGuide()) {
            noteViewModel.isShowConsoleGuideWindow.value = true
        }
    }

    override fun doOnDoodleThumbnailSet() {
        super.doOnDoodleThumbnailSet()
        if (isConsoleState) {
            doodleView.hidePagePreviewView()
            doodleView.doodleTouchLayer.visibility = View.VISIBLE
        }
    }

    private fun setRecordToolsClickListener() {
        doodleView.doodleEditLayer.mRecordToolsClickListener = (object : RecordToolsClickListener {
            override fun pauseRecord(id: UUID, recordId: UUID, recordTagId: UUID?) {
                recordViewModel.pausePlay()
            }

            override fun playRecord(id: UUID, recordId: UUID, recordTagId: UUID?) {
                val sourceId = recordTagId ?: recordId
                if (recordViewModel.isShowRecordControlView.value != true) {
                    recordViewModel.switchIsShowRecordControlView()
                }
                recordViewModel.stopPlay()
                recordViewModel.preparePlay(sourceId)
                recordViewModel.startPlay(mainHandler, id)
            }

            override fun checkRecord(id: UUID, recordId: UUID, recordTagId: UUID?) {
                recordViewModel.refreshRecordItemList()
                val needExpandRecordItem =
                    recordViewModel.currentRecordItemList.firstOrNull { noteRecordItem -> noteRecordItem.record.uuid == recordId }
                needExpandRecordItem?.isExpand = true
                RecordViewModel.currentSelectedRecordUUID = recordId
                RecordViewModel.currentSelectedRecordTagUUID = recordTagId
                if (recordViewModel.isShowRecordControlView.value != true) {
                    recordViewModel.switchIsShowRecordControlView()
                }
                recordViewModel.isShowRecordWindow.value = true
            }

        })
        doodleView.doodleStickyOnTopLayer.onRecordStateChange(
            recordViewModel.currentPlayObjectId,
            recordViewModel.isPlaying.value ?: false
        )
    }

    private fun refreshSearchBackPageIcon(page: Page?) {
        if (!checkViewBindingValidity()) return
        val backPageUUID = searchViewModel.getBackPageUUID()
        if (backPageUUID != null && backPageUUID != page?.uuid) {
            binding.searchPageBack.visibility = View.VISIBLE
        } else {
            binding.searchPageBack.visibility = View.GONE
        }
    }

    override fun showCropDialog(insertableBitmap: InsertableBitmap?, cropType: CropType) {
        if (!isAdded) return
        ImageCallEvent.sendPicturesTransparencyClick()
        ImageCropDialogFragment().apply {
            this.insertableBitmap = insertableBitmap
            setOnCropCompleteListener(this@NoteEditorFragment)
            val bundle = Bundle()
            bundle.putString(BaseImageCropDialogFragment.CROP_TYPE_NAME, cropType.name)
            arguments = bundle
        }.show(parentFragmentManager, BaseImageCropDialogFragment.TAG)

        noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
        snippetViewModel.changeSnippetListViewState(SnippetViewModel.SnippetListViewState.HIDDEN)
    }


    private fun showGenerateLongPictureProgressDialog() {
        val dialog =
            parentFragmentManager.findFragmentByTag(GenerateLongPictureProgressDialog.TAG)
        if (dialog != null && dialog is GenerateLongPictureProgressDialog) {
            dialog.setOnCloseListener {
                noteViewModel.cancelPictureShare()
            }
            return
        }
        generateLongPictureProgressDialog = GenerateLongPictureProgressDialog().apply {
            setOnCloseListener {
                noteViewModel.cancelPictureShare()
            }
        }
        generateLongPictureProgressDialog?.safeShow(
            parentFragmentManager,
            GenerateLongPictureProgressDialog.TAG
        )
    }

    private fun showCustomToolsDialog() {
        val dialog =
            parentFragmentManager.findFragmentByTag(CustomToolbarDialog.TAG)
        if (dialog != null && dialog is CustomToolbarDialog) {
            dialog.apply {
                doOnSave = { result ->
                    customToolsViewModel.saveCustomTools(result) {
                        customToolsViewModel.hideCustomToolDialog()
                    }
                }
                doOnCancel = {
                    customToolsViewModel.hideCustomToolDialog()
                }

                doOnHideToolChange = { isShow ->
                    UserUsageConfig.showHideToolsEnterIcon = isShow
                    updateHideToolsShowIcon(
                        inputMode = binding.doodle.inputMode,
                        isShow
                    )
                }

                doOnResetDefaultClick = {
                    customToolsDialog?.setData(customToolsViewModel.getDefaultCustomTools())
                }
            }
            customToolsDialog = dialog
            return
        }

        customToolsViewModel.getCustomTools { customList ->
            customToolsDialog = CustomToolbarDialog(customList).apply {
                doOnSave = { result ->
                    customToolsViewModel.saveCustomTools(result) {
                        customToolsViewModel.hideCustomToolDialog()
                    }
                }

                doOnCancel = {
                    customToolsViewModel.hideCustomToolDialog()
                }

                doOnHideToolChange = { isShow ->
                    UserUsageConfig.showHideToolsEnterIcon = isShow
                    updateHideToolsShowIcon(
                        inputMode = binding.doodle.inputMode,
                        isShow
                    )
                }

                doOnResetDefaultClick = {
                    customToolsDialog?.setData(customToolsViewModel.getDefaultCustomTools())
                }
            }
            customToolsDialog?.safeShow(
                parentFragmentManager,
                CustomToolbarDialog.TAG
            )
        }
    }

    private fun hideCustomToolsDialog() {
        customToolsDialog?.safeDismiss(parentFragmentManager)
    }

    private fun updateGenerateLongPictureProgressDialogProgress(progress: Float) {
        generateLongPictureProgressDialog?.updateProgressInternal(progress)
    }

    private fun hideGenerateLongPictureProgressDialog() {
        generateLongPictureProgressDialog?.dismiss()
    }

    override fun updatePageIndex() {
        if (!checkViewBindingValidity()) return
        binding.pageIndicator.text = getString(
            R.string.page_indicator,
            currentDoc.viewingPageIndex + 1,
            currentDoc.pageCount
        )
    }

    private fun updateModeStatus(view: View) = majorToolViews.forEach {
        it.isSelected = (view == it)
    }

    private fun selectMajorTool(customTool: CustomTool) {
        when (customTool.inputMode) {
            InputMode.DRAW -> {
                if (editorModeViewModel.editorMode.value == InputMode.DRAW
                    || editorModeViewModel.editorMode.value == InputMode.PEN
                    || editorModeViewModel.editorMode.value == InputMode.PAINTBRUSH
                ) {
                    resetAllToolViews()
                    noteViewModel.isShowRecognizeWindow.value = true
                } else {
                    changeToolbar(UserUsageConfig.lastSelectedPenMode)
                }
            }

            InputMode.HIGHLIGHTER -> {
                if (editorModeViewModel.editorMode.value == InputMode.HIGHLIGHTER) {
                    resetAllToolViews()
                    noteViewModel.isShowHighlighterWindow.value = true
                } else {
                    changeToolbar(InputMode.HIGHLIGHTER)
                }
            }

            InputMode.ERASER -> {
                if (editorModeViewModel.editorMode.value == InputMode.ERASER) {
                    resetAllToolViews()
                    noteViewModel.isShowEraseToolWindow.value = true
                } else {
                    changeToolbar(InputMode.ERASER)
                }
            }

            InputMode.LASSO -> {
                val language = Locale.getDefault().language
                if (editorModeViewModel.editorMode.value == InputMode.LASSO && language in supportChangeRecognitionLanguage) {
                    resetAllToolViews()
                    noteViewModel.isShowLassoLanguageWindow.value = true
                } else {
                    changeToolbar(InputMode.LASSO)
                }
            }

            InputMode.IMAGE -> {
                if (editorModeViewModel.editorMode.value == InputMode.IMAGE) {
                    editorViewModel.selectedInsertableBitmap = null
                    checkAndPickImage()
                } else {
                    checkImagePermission { state ->
                        if (state == PermissionRequester.PermissionState.PERMISSION_GRANTED) {
                            imageFetchViewModel.loadSidebarPictures()
                        }
                    }
                    changeToolbar(InputMode.IMAGE)
                }
            }

            InputMode.TEXT -> {
                changeToolbar(InputMode.TEXT)
            }

            InputMode.GRAFFITI, InputMode.OUTLINEPEN, InputMode.LINEDRAW -> {
                EditEvent.sendEditGraffitipenClick()
                if (editorModeViewModel.editorMode.value == InputMode.GRAFFITI || editorModeViewModel.editorMode.value == InputMode.LINEDRAW || editorModeViewModel.editorMode.value == InputMode.OUTLINEPEN) {
                    resetAllToolViews()
                    noteViewModel.isShowGraffitiWindow.value = true
                } else {
                    changeToolbar(InputMode.GRAFFITI)
                }
            }

            InputMode.GRAPH -> {
                if (editorModeViewModel.editorMode.value == InputMode.GRAPH) {
                    resetAllToolViews()
                } else {
                    changeToolbar(InputMode.GRAPH)
                }
                noteViewModel.changeGraphWindowStatues(true)
                GraphEvent.sendGRAPHTOOLCLICK()
                GraphEvent.sendGRAPHTOOLWINDOWSHOW("toolbar")
            }

            InputMode.SNIPPET -> {
                EditEvent.sendEditSnippetClick()
                changeToolbar(InputMode.SNIPPET)
            }

            InputMode.VIEW -> {
                if (editorModeViewModel.editorMode.value == InputMode.VIEW) {
                    resetAllToolViews()
                    noteViewModel.isShowReadingStyleSelectWindow.value = true
                } else {
                    changeToolbar(InputMode.VIEW)
                }
            }

            InputMode.PRESENTATION -> {
                EditEvent.sendLaserClick()
                changeToolbar(InputMode.PRESENTATION)
            }

            InputMode.TAPE -> {
                EditEvent.sendTapeClick()
                changeToolbar(InputMode.TAPE)
            }

            else -> {
            }
        }
    }

    private fun changeToolbar(inputMode: InputMode) {
        if (!binding.toolBar.isVisible) {
            if (inputMode == InputMode.VIEW) {
                adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.NONE)
            } else {
                adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
                binding.toolBar.show {}
            }
        }
        if (inputMode != editorModeViewModel.editorMode.value) {
            editorModeViewModel.switchMode(inputMode)
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun setToolContainerHeightWhenMultiWindow() {
        //改变左侧工具栏高度
        val minorToolContainerLayoutParams =
            binding.minorToolContainer.layoutParams as ConstraintLayout.LayoutParams
        val maxHeight = minorToolContainerLayoutParams.matchConstraintMaxHeight
        val appHeight = DimensionUtil.getScreenDimensions(requireContext()).heightPixels
        //因为上方工具栏高度是定值，所以可以这么写
        val blurViewHeight = binding.blurView.layoutParams.height
        val currentDoodleHeight = appHeight - blurViewHeight
        if (maxHeight > currentDoodleHeight) {
            minorToolContainerLayoutParams.matchConstraintMaxHeight = currentDoodleHeight
            binding.minorToolContainer.layoutParams = minorToolContainerLayoutParams
        }
    }

    private fun processThumbnailAndSnippetAnimation() {
        val targetThumbnailState = noteViewModel.thumbnailListViewState.value ?: return
        val targetSnippetState = snippetViewModel.snippetListViewState.value ?: return
        if (!targetThumbnailState.isFinal() || !targetSnippetState.isFinal()
            || (targetThumbnailState.isStillShown() && targetSnippetState.isStillShown())
        ) {
            throw Exception(
                "processThumbnailAndSnippetAnimation with unexpected target state:" +
                        " thumbnail = $targetThumbnailState, snippet = $targetSnippetState"
            )
        }
        if (!thumbnailListViewState.isStill() || !snippetListViewState.isStill()) return
        LogHelper.d(
            defaultTag,
            "processThumbnailAndSnippetAnimation: thumbnail current = $thumbnailListViewState," +
                    " target = $targetThumbnailState, snippet current = $snippetListViewState, target = $targetSnippetState"
        )
        when {
            thumbnailListViewState == NoteViewModel.ThumbnailListViewState.SHOWN -> {
                if (targetThumbnailState == NoteViewModel.ThumbnailListViewState.HIDDEN) {
                    changePageThumbnailPosition(false, snippetAnimationTime) {
                        processThumbnailAndSnippetAnimation()
                    }
                }
            }

            snippetListViewState == SnippetViewModel.SnippetListViewState.EXPANDED -> {
                if (targetSnippetState == SnippetViewModel.SnippetListViewState.SHOWN) {
                    changeSnippetZoom(false, snippetAnimationTime)
                } else if (targetSnippetState == SnippetViewModel.SnippetListViewState.HIDDEN) {
                    changePageSnippetPosition(false, snippetAnimationTime) {
                        processThumbnailAndSnippetAnimation()
                    }
                }
            }

            snippetListViewState == SnippetViewModel.SnippetListViewState.SHOWN -> {
                if (targetSnippetState == SnippetViewModel.SnippetListViewState.EXPANDED) {
                    changeSnippetZoom(true, snippetAnimationTime)
                } else if (targetSnippetState == SnippetViewModel.SnippetListViewState.HIDDEN) {
                    changePageSnippetPosition(false, snippetAnimationTime) {
                        processThumbnailAndSnippetAnimation()
                    }
                }
            }

            snippetListViewState == SnippetViewModel.SnippetListViewState.HIDDEN_EXPANDED -> {
                if (targetSnippetState == SnippetViewModel.SnippetListViewState.HIDDEN) {
                    changeSnippetZoom(false, NO_ANIMATION_DURATION, false) {
                        processThumbnailAndSnippetAnimation()
                    }
                }
            }

            targetThumbnailState == NoteViewModel.ThumbnailListViewState.SHOWN -> {
                if (thumbnailListViewState == NoteViewModel.ThumbnailListViewState.HIDDEN) {
                    changePageThumbnailPosition(true, snippetAnimationTime)
                }
            }

            targetSnippetState == SnippetViewModel.SnippetListViewState.SHOWN -> {
                if (snippetListViewState == SnippetViewModel.SnippetListViewState.HIDDEN) {
                    if (checkViewBindingValidity()) {
                        binding.pageSnippetList.post {
                            if (isAdded) {
                                changePageSnippetPosition(true, snippetAnimationTime)
                            }
                        }
                    }
                }
            }

            targetSnippetState == SnippetViewModel.SnippetListViewState.EXPANDED -> {
                if (snippetListViewState == SnippetViewModel.SnippetListViewState.SHOWN) {
                    changeSnippetZoom(true, snippetAnimationTime)
                } else if (snippetListViewState == SnippetViewModel.SnippetListViewState.HIDDEN) {
                    changePageSnippetPosition(true, snippetAnimationTime) {
                        processThumbnailAndSnippetAnimation()
                    }
                }
            }
        }
    }

    private fun changePageThumbnailPosition(
        isShow: Boolean,
        animationTime: Long,
        animationEndListener: (() -> Unit)? = null,
    ) {
        if (!checkViewBindingValidity()) return
        thumbnailListViewState = if (isShow) {
            NoteViewModel.ThumbnailListViewState.SHOWING
        } else {
            NoteViewModel.ThumbnailListViewState.HIDING
        }
        val animatorArgs = if (isShow) {
            floatArrayOf(binding.thumbnailAndOutlineView.width.toFloat())
        } else {
            floatArrayOf(
                binding.thumbnailAndOutlineView.width.toFloat(),
                0F
            )
        }
        thumbnailAnimator = ValueAnimator.ofFloat(
            *animatorArgs
        ).apply {
            duration = animationTime
            addUpdateListener {
                if (checkViewBindingValidity()) {
                    binding.layer.translationX =
                        if (!KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                    binding.toolBar.updateLocation()
                }
            }
            doOnStart {
                if (checkViewBindingValidity()) {
                    if (isShow) {
                        binding.thumbnailAndOutlineView.visibility = View.VISIBLE
                    }
                }
            }
            doOnEnd {
                if (checkViewBindingValidity()) {
                    if (!isShow) {
                        binding.thumbnailAndOutlineView.visibility = View.INVISIBLE
                    }
                }
                thumbnailListViewState = if (isShow) {
                    NoteViewModel.ThumbnailListViewState.SHOWN
                } else {
                    NoteViewModel.ThumbnailListViewState.HIDDEN
                }
                animationEndListener?.invoke()
                noteSnippetDragGuideWindow?.dismiss()
            }
        }
        thumbnailAnimator?.start()
    }

    private fun changeAiViewPosition(
        isShow: Boolean,
        animationTime: Long,
        animationEndListener: (() -> Unit)? = null,
    ) {
        if (!checkViewBindingValidity()) return
        val animatorArgs = if (isShow) {
            if (KiloApp.isLayoutRtl) {
                binding.aiContainer.let {
                    binding.toolBar.changeBoundaryView(
                        it.id,
                        AdsorptionEdgeLayout.EDGE.LEFT
                    )
                }
            } else {
                binding.toolBar.changeBoundaryView(
                    binding.aiContainer.id,
                    AdsorptionEdgeLayout.EDGE.RIGHT
                )
            }
            floatArrayOf(binding.aiContainer.width.toFloat())
        } else {
            floatArrayOf(
                binding.aiContainer.width.toFloat(),
                0F
            )
        }
        aiAnimator = ValueAnimator.ofFloat(
            *animatorArgs
        ).apply {
            duration = animationTime
            addUpdateListener {
                if (checkViewBindingValidity()) {
                    binding.aiContainer.translationX =
                        if (KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                    binding.toolBar.updateLocation()
                    if (isLandOneThirdScreen()) binding.draftPaperShowBtn.x =
                        (-binding.draftPaperShowBtn.width).toFloat() else changeDraftPaperShowBtnPosition(
                        binding.aiContainer
                    )
                }
            }
            doOnStart {
                if (checkViewBindingValidity()) {
                    if (isShow) {
                        binding.aiContainer.visibility = View.VISIBLE
                    }
                }
            }
            doOnEnd {
                if (checkViewBindingValidity()) {
                    if (!isShow) {
                        binding.aiContainer.visibility = View.INVISIBLE
                    }
                }
                animationEndListener?.invoke()
            }
        }
        aiAnimator?.start()
    }

    private fun changeSearchViewPosition(
        isShow: Boolean,
        animationTime: Long,
        animationEndListener: (() -> Unit)? = null,
    ) {
        if (!checkViewBindingValidity()) return
        noteSearchFrameLayout?.let { noteSearchLayout ->
            val noteSearchAnimatorArgs = if (isShow) {
                if (KiloApp.isLayoutRtl) {
                    binding.toolBar.changeBoundaryView(
                        noteSearchLayout.id,
                        AdsorptionEdgeLayout.EDGE.LEFT
                    )
                } else {
                    binding.toolBar.changeBoundaryView(
                        noteSearchLayout.id,
                        AdsorptionEdgeLayout.EDGE.RIGHT
                    )
                }
                if (isLandOneThirdScreen()) {
                    floatArrayOf(getNoteSearchFrameLayoutHeight().toFloat())
                } else {
                    floatArrayOf(getNoteSearchFrameLayoutWidth().toFloat())
                }
            } else {
                if (isLandOneThirdScreen()) {
                    floatArrayOf(
                        getNoteSearchFrameLayoutHeight().toFloat(),
                        0F
                    )
                } else {
                    floatArrayOf(
                        getNoteSearchFrameLayoutWidth().toFloat(),
                        0F
                    )
                }
            }

            noteSearchLayout.visibility = View.VISIBLE
            searchAnimator = ValueAnimator.ofFloat(
                *noteSearchAnimatorArgs
            ).apply {
                duration = animationTime
                addUpdateListener {
                    if (checkViewBindingValidity()) {
                        if (isLandOneThirdScreen()) {
                            noteSearchLayout.translationY =
                                if (KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                            resetDraftPaperShowBtnPosition()
                        } else {
                            noteSearchLayout.translationX =
                                if (KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                            changeDraftPaperShowBtnPosition(noteSearchLayout)
                        }
                        binding.toolBar.updateLocation()
                    }
                }
                doOnStart {
                    if (checkViewBindingValidity()) {
                        if (isShow) {
                            noteSearchLayout.visibility = View.VISIBLE
                        }
                    }
                }
                doOnEnd {
                    if (checkViewBindingValidity()) {
                        if (!isShow) {
                            noteSearchLayout.visibility = View.INVISIBLE
                        }
                    }
                    animationEndListener?.invoke()
                }
            }
            searchAnimator?.start()
        }
    }

    private fun changeWebCommonSidebarViewPosition(
        isShow: Boolean,
        animationTime: Long,
        animationEndListener: (() -> Unit)? = null,
    ) {
        if (!checkViewBindingValidity()) return
        val animatorArgs = if (isShow) {
            if (KiloApp.isLayoutRtl) {
                binding.toolBar.changeBoundaryView(
                    binding.webSidebarView.id,
                    AdsorptionEdgeLayout.EDGE.LEFT
                )
            } else {
                binding.toolBar.changeBoundaryView(
                    binding.webSidebarView.id,
                    AdsorptionEdgeLayout.EDGE.RIGHT
                )
            }
            floatArrayOf(0F, -(getWebSidebarViewWidth()))
        } else {
            binding.webSidebarView.clearHistory()
            floatArrayOf(-(getWebSidebarViewWidth()), 0F)
        }

        webSidebarViewAnimator = ValueAnimator.ofFloat(
            *animatorArgs
        ).apply {
            duration = animationTime
            addUpdateListener {
                if (isAdded && checkViewBindingValidity()) {
                    binding.webSidebarView.translationX =
                        if (!KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                    binding.toolBar.updateLocation()
                }
                changeDraftPaperShowBtnPosition(binding.webSidebarView)
            }
            doOnStart {
                if (isShow) {
                    binding.webSidebarView.visibility = View.VISIBLE
                }
            }
            doOnEnd {
                if (!isShow && isAdded && checkViewBindingValidity()) {
                    binding.webSidebarView.visibility = View.INVISIBLE
                    if (binding.webSidebarView.hasFocus()
                        && WindowInsetsUtils.isSoftKeyBoardShow(binding.webSidebarView)
                    ) {
                        WindowInsetsUtils.hideSoftKeyBoard(binding.webSidebarView)
                    }
                }
                animationEndListener?.invoke()
            }
        }
        webSidebarViewAnimator?.start()
    }

    private var addPageViewShowingStatus = false

    private fun changeAddPageViewPosition(
        isShow: Boolean,
        animationTime: Long,
        animationEndListener: (() -> Unit)? = null,
    ) {
        if (!checkViewBindingValidity()) return
        val multiWindowCondition = DimensionUtil.isLandAndOneThirdScreen(context)
                || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)
        if (multiWindowCondition) {
            animationEndListener?.invoke()
            return
        }

        if (addPageAnimator?.isRunning == true) {
            if (addPageViewShowingStatus == isShow) {
                return
            } else {
                addPageAnimator?.end()
            }
        }

        addPageViewShowingStatus = isShow
        val animatorArgs = if (isShow) {
            if (KiloApp.isLayoutRtl) {
                binding.toolBar.changeBoundaryView(
                    binding.noteAddPageLayout.id,
                    AdsorptionEdgeLayout.EDGE.LEFT
                )
            } else {
                binding.toolBar.changeBoundaryView(
                    binding.noteAddPageLayout.id,
                    AdsorptionEdgeLayout.EDGE.RIGHT
                )
            }
            floatArrayOf(binding.noteAddPageLayout.width.toFloat())
        } else {
            floatArrayOf(
                binding.noteAddPageLayout.width.toFloat(),
                0F
            )
        }
        addPageAnimator = ValueAnimator.ofFloat(
            *animatorArgs
        ).apply {
            duration = animationTime
            addUpdateListener {
                if (checkViewBindingValidity()) {
                    binding.noteAddPageLayout.translationX =
                        if (KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                    binding.toolBar.updateLocation()
                    changeDraftPaperShowBtnPosition(binding.noteAddPageLayout)
                }
            }
            doOnStart {
                if (checkViewBindingValidity()) {
                    if (isShow) {
                        binding.noteAddPageLayout.visibility = View.VISIBLE
                    }
                }
            }
            doOnEnd {
                if (checkViewBindingValidity()) {
                    if (!isShow) {
                        binding.noteAddPageLayout.visibility = View.INVISIBLE
                    }
                }
                animationEndListener?.invoke()
            }
        }
        addPageAnimator?.start()
    }

    private fun getToolTapeControlView(): View? {
        if (checkViewBindingValidity()) {
            return binding.minorToolRecyclerView.findViewHolderForAdapterPosition(
                TAPE_CONTROL_VIEW_INDEX
            )?.itemView
        }
        return null
    }

    private fun changeMaterialViewPosition(
        isShow: Boolean,
        animationTime: Long,
        animationEndListener: (() -> Unit)? = null,
    ) {
        if (!checkViewBindingValidity()) return
        val animatorArgs = if (isShow) {
            if (KiloApp.isLayoutRtl) {
                binding.toolBar.changeBoundaryView(
                    binding.noteMaterialView.id,
                    AdsorptionEdgeLayout.EDGE.LEFT
                )
            } else {
                binding.toolBar.changeBoundaryView(
                    binding.noteMaterialView.id,
                    AdsorptionEdgeLayout.EDGE.RIGHT
                )
            }
            floatArrayOf(binding.noteMaterialView.width.toFloat())
        } else {
            floatArrayOf(
                binding.noteMaterialView.width.toFloat(),
                0F
            )
        }
        materialAnimator = ValueAnimator.ofFloat(
            *animatorArgs
        ).apply {
            duration = animationTime
            addUpdateListener {
                if (checkViewBindingValidity()) {
                    binding.noteMaterialView.translationX =
                        if (KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                    binding.toolBar.updateLocation()
                    changeDraftPaperShowBtnPosition(binding.noteMaterialView)
                }
            }
            doOnStart {
                if (checkViewBindingValidity()) {
                    if (isShow) {
                        binding.noteMaterialView.visibility = View.VISIBLE
                    }
                }
            }
            doOnEnd {
                if (checkViewBindingValidity()) {
                    if (!isShow) {
                        binding.noteMaterialView.visibility = View.INVISIBLE
                    }
                }
                animationEndListener?.invoke()
            }
        }
        materialAnimator?.start()
    }

    private fun changePageSnippetPosition(
        isShow: Boolean,
        animationTime: Long,
        animationEndListener: (() -> Unit)? = null,
    ) {
        if (!checkViewBindingValidity()) return
        val oldState = snippetListViewState
        snippetListViewState = if (isShow) {
            SnippetViewModel.SnippetListViewState.SHOWING
        } else {
            SnippetViewModel.SnippetListViewState.HIDING
        }
        val animatorArgs = if (isShow) {
            floatArrayOf(binding.pageSnippetList.width.toFloat())
        } else {
            floatArrayOf(
                binding.pageSnippetList.width.toFloat(),
                0F
            )
        }
        snippetAnimator = ValueAnimator.ofFloat(
            *animatorArgs
        ).apply {
            duration = animationTime
            addUpdateListener {
                if (checkViewBindingValidity()) {
                    binding.layer.translationX =
                        if (!KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                    binding.toolBar.updateLocation()
                }
            }
            doOnStart {
                if (isShow) {
                    if (checkViewBindingValidity()) {
                        binding.pageSnippetList.visibility = View.VISIBLE
                    }
                }
            }
            doOnEnd {
                if (!isShow) {
                    snippetViewModel.resetSnippet()
                    if (checkViewBindingValidity()) {
                        binding.pageSnippetList.visibility = View.INVISIBLE
                    }
                } else {
                    isSnippetsExpandAnimationEnd = true
                    if (UserUsageConfig.isNeedShowSnippetDragGuide) {
                        if (checkViewBindingValidity()) {
                            val topSnippetView =
                                binding.pageSnippetList.getTopSnippetView()
                            topSnippetView?.post {
                                if (!isAdded) return@post
                                showNoteSnippetDragGuideWindow()
                            }
                        }
                    }
                }
                snippetListViewState = if (isShow) {
                    SnippetViewModel.SnippetListViewState.SHOWN
                } else {
                    if (oldState == SnippetViewModel.SnippetListViewState.EXPANDED) {
                        SnippetViewModel.SnippetListViewState.HIDDEN_EXPANDED
                    } else {
                        SnippetViewModel.SnippetListViewState.HIDDEN
                    }
                }
                animationEndListener?.invoke()
            }
        }
        snippetAnimator?.start()
    }


    private fun showDraftPaper() {
        if (!checkViewBindingValidity()) return
        draftAnimator?.cancel()
        draftAnimator = AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(
                    binding.draftPaperFragmentContainer,
                    View.X,
                    getDraftStartBounds().left,
                    getDraftFinalBounds().left
                ),
                ObjectAnimator.ofFloat(
                    binding.draftPaperFragmentContainer,
                    View.Y,
                    getDraftStartBounds().top,
                    getDraftFinalBounds().top
                ),
                ObjectAnimator.ofFloat(
                    binding.draftPaperFragmentContainer,
                    View.SCALE_X,
                    getDraftShowScaleX(),
                    1F
                ),
                ObjectAnimator.ofFloat(
                    binding.draftPaperFragmentContainer,
                    View.SCALE_Y,
                    getDraftShowScaleY(),
                    1F
                )
            )
            duration = draftPaperAnimationTime
            interpolator = DecelerateInterpolator()
            addListener(object : AnimatorListenerAdapter() {

                override fun onAnimationStart(animation: Animator) {
                    if (checkViewBindingValidity()) {
                        binding.draftPaperFragmentContainer.enableTouch = false
                        binding.draftPaperShowBtn.visibility = View.INVISIBLE
                        binding.draftPaperFragmentContainer.visibility = View.VISIBLE
                        addDraftPaperFragment()
                    }
                }

                override fun onAnimationEnd(animation: Animator) {
                    if (checkViewBindingValidity()) {
                        binding.draftPaperFragmentContainer.enableTouch = true
                        draftPaperAnimationTime = ANIMATION_DURATION_TIME
                        draftAnimator = null
                        if (UserUsageConfig.isNeedShowDraftPaperFunctionInstructionGuide) {
                            binding.draftPaperFragmentContainer.post {
                                if (!isAdded) return@post
                                showDraftPaperFunctionIntroductionGuideWindow()
                            }
                        }
                    }
                }

                override fun onAnimationCancel(animation: Animator) {
                    draftAnimator = null
                }
            })
            start()
        }
    }

    private fun showDraftPaperFunctionIntroductionGuideWindow() {
        if (draftPaperFunctionIntroductionGuideWindow != null && draftPaperFunctionIntroductionGuideWindow!!.isShowing) {
            draftPaperFunctionIntroductionGuideWindow!!.setOnDismissListener { }
            draftPaperFunctionIntroductionGuideWindow!!.dismiss()
        }
        (childFragmentManager.findFragmentByTag(DRAFT_PAPER_FRAGMENT_TAG) as? DraftPaperFragment)?.let {
            draftPaperFunctionIntroductionGuideWindow =
                DraftPaperFunctionIntroductionGuidePopupWindow(
                    requireContext(),
                    FloatArray(it.getDraftPaperToolPenOrPreviewLocation().size) { index -> it.getDraftPaperToolPenOrPreviewLocation()[index].toFloat() },
                    FloatArray(it.getDraftPaperChangeLayoutViewLocation().size) { index -> it.getDraftPaperChangeLayoutViewLocation()[index].toFloat() })
            PopupWindowCompat.setWindowLayoutType(
                draftPaperFunctionIntroductionGuideWindow!!,
                WindowManager.LayoutParams.TYPE_APPLICATION_SUB_PANEL + 100
            )
            draftPaperFunctionIntroductionGuideWindow!!.isClippingEnabled = false
            draftPaperFunctionIntroductionGuideWindow!!.showAtLocation(
                binding.draftPaperFragmentContainer, Gravity.NO_GRAVITY, 0, 0
            )
        }
    }

    private fun closeDraftPaper() {
        if (!checkViewBindingValidity()) return
        // 个别pad（华为MatePad），动画duration=0，会闪一下（bug单号56851），因此不做duration=0的动画
        if (draftPaperAnimationTime == NO_ANIMATION_DURATION) {
            draftPaperAnimationTime = ANIMATION_DURATION_TIME
            binding.draftPaperFragmentContainer.visibility = View.INVISIBLE
            binding.draftPaperShowBtn.visibility = View.VISIBLE
            resetDraftSizeAndLocation()
            return
        }
        draftAnimator?.cancel()
        val finalBounds = getDraftFinalBounds()
        draftAnimator = AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(
                    binding.draftPaperFragmentContainer,
                    View.X,
                    getDraftFinalBounds().left,
                    getDraftStartBounds().left
                ),
                ObjectAnimator.ofFloat(
                    binding.draftPaperFragmentContainer,
                    View.Y,
                    getDraftFinalBounds().top,
                    getDraftStartBounds().top
                ),
                ObjectAnimator.ofFloat(
                    binding.draftPaperFragmentContainer,
                    View.SCALE_X,
                    1F,
                    getDraftShowScaleX()
                ),
                ObjectAnimator.ofFloat(
                    binding.draftPaperFragmentContainer,
                    View.SCALE_Y,
                    1F,
                    getDraftShowScaleY()
                )
            )
            duration = draftPaperAnimationTime
            interpolator = DecelerateInterpolator()
            addListener(object : AnimatorListenerAdapter() {

                override fun onAnimationStart(animation: Animator) {
                    if (checkViewBindingValidity()) {
                        binding.draftPaperFragmentContainer.enableTouch = false
                        draftPaperFragment?.onChangingPosition()
                        draftPaperFragment?.resetToolViews(false)
                    }
                }

                override fun onAnimationEnd(animation: Animator) {
                    draftPaperAnimationTime = ANIMATION_DURATION_TIME
                    if (checkViewBindingValidity()) {
                        binding.draftPaperFragmentContainer.x = finalBounds.left
                        binding.draftPaperFragmentContainer.y = finalBounds.top
                        binding.draftPaperFragmentContainer.visibility = View.INVISIBLE
                        binding.draftPaperShowBtn.visibility = View.VISIBLE
                        resetDraftSizeAndLocation()
                    }
                    draftAnimator = null
                }

                override fun onAnimationCancel(animation: Animator) {
                    draftAnimator = null
                }
            })
            start()
        }
    }

    private fun resetDraftSizeAndLocation() {
        binding.draftPaperFragmentContainer.apply {
            val lp = layoutParams
            lp.apply {
                width =
                    draftPaperSizeAndLocationHelper.getDefDraftPaperWidth(context)
                height =
                    draftPaperSizeAndLocationHelper.getDefDraftPaperHeight(context)
            }
            layoutParams = lp
            x = draftPaperSizeAndLocationHelper.getDraftPaperInitX(
                context,
                draftPaperViewModel.draftPaperViewXRelativeScreenRatio,
                draftPaperViewModel.draftPaperViewWidth
            )
            y = draftPaperSizeAndLocationHelper.getDraftPaperInitY(
                context,
                binding.doodle,
                draftPaperViewModel.draftPaperViewYRelativeScreenRatio,
                draftPaperViewModel.draftPaperViewHeight
            )
        }
        draftPaperViewModel.resetDraftPaperSizeAndLocation()
    }

    private fun getDraftShowScaleX(): Float {
        return if (binding.draftPaperFragmentContainer.width == 0) {
            binding.draftPaperShowBtn.width.toFloat() / draftPaperSizeAndLocationHelper.getDraftPaperInitWidth(
                requireContext(),
                draftPaperViewModel.draftPaperViewWidth
            )
        } else {
            binding.draftPaperShowBtn.width.toFloat() / binding.draftPaperFragmentContainer.width
        }
    }

    private fun getDraftShowScaleY(): Float {
        return return if (binding.draftPaperFragmentContainer.width == 0) {
            binding.draftPaperShowBtn.height.toFloat() / draftPaperSizeAndLocationHelper.getDraftPaperInitHeight(
                requireContext(),
                binding.doodle,
                draftPaperViewModel.draftPaperViewHeight
            )
        } else {
            binding.draftPaperShowBtn.height.toFloat() / binding.draftPaperFragmentContainer.height
        }
    }

    private fun getDraftStartBounds(): RectF {
        val targetView = binding.draftPaperShowBtn
        val targetViewLocation = IntArray(2)
        targetView.getLocationInWindow(targetViewLocation)
        val targetViewX = targetViewLocation[0].toFloat()
        val targetViewY = targetViewLocation[1].toFloat()
        return RectF(
            targetViewX,
            targetViewY,
            (targetViewX + targetView.width),
            (targetViewY + targetView.height)
        )
    }

    private fun getDraftFinalBounds(): RectF {
        val targetView = binding.draftPaperFragmentContainer
        val targetViewLocation = IntArray(2)
        targetView.getLocationInWindow(targetViewLocation)
        val targetViewX = targetViewLocation[0].toFloat()
        val targetViewY = targetViewLocation[1].toFloat()
        return RectF(
            targetViewX,
            targetViewY,
            (targetViewX + targetView.width),
            (targetViewY + targetView.height)
        )
    }

    private fun changeDraftPaperShowBtnPosition(view: View) {
        val sidebarCloseBtnWidth = resources.getDimensionPixelSize(R.dimen.dp_42)
        if (KiloApp.isLayoutRtl) {
            val maxViewX = (view.width - sidebarCloseBtnWidth).toFloat()
            val expectedViewX = view.translationX
            binding.draftPaperShowBtn.x = minOf(expectedViewX, maxViewX)
        } else {
            val maxViewX =
                (DimensionUtil.getScreenDimensions(context).widthPixels - binding.draftPaperShowBtn.width).toFloat()
            val expectedViewX =
                view.left + view.translationX - binding.draftPaperShowBtn.width + sidebarCloseBtnWidth
            binding.draftPaperShowBtn.x = minOf(expectedViewX, maxViewX)
        }
    }

    private fun resetDraftPaperShowBtnPosition() {
        binding.draftPaperShowBtn.x =
            (DimensionUtil.getScreenDimensions(context).widthPixels - binding.draftPaperShowBtn.width).toFloat()
    }

    private fun changeCurrentSidebarStatus(
        configSidebar: Sidebar,
        resetSearch: Boolean = true,
    ) {
        when (sideBarViewModel.currentSidebarState.value) {
            Sidebar.ADD_PAGE -> {
                changeAddPageViewPosition(false, sidebarAnimationTime) {
                    setCurrentSidebar(configSidebar)
                    changeAddPageState(false)
                }
            }

            Sidebar.MATERIAL -> {
                changeMaterialViewPosition(false, sidebarAnimationTime) {
                    setCurrentSidebar(configSidebar)
                    changeMaterialState(false)
                }
            }

            Sidebar.DOC_SEARCH -> {
                hideOtherDocPreviewBottomSheet()
                changeSearchViewPosition(false, sidebarAnimationTime) {
                    setCurrentSidebar(configSidebar)
                    if (resetSearch) {
                        searchViewModel.resetSearchJob()
                        searchViewModel.resetSearchState()
                        searchViewModel.resetSearchType()
                        searchViewModel.resetCurrentDocSearchResult()
                    }
                }
            }

            Sidebar.WEB_SEARCH -> {
                changeWebCommonSidebarViewPosition(false, sidebarAnimationTime) {
                    setCurrentSidebar(configSidebar)
                    if (resetSearch) {
                        searchViewModel.resetSearchJob()
                        searchViewModel.resetSearchState()
                        searchViewModel.resetSearchType()
                        searchViewModel.resetCurrentDocSearchResult()
                    }
                }
            }

            Sidebar.TRANSLATE -> {
                changeWebCommonSidebarViewPosition(false, sidebarAnimationTime) {
                    setCurrentSidebar(configSidebar)
                }
            }

            Sidebar.AI -> {
                changeAiViewPosition(false, sidebarAnimationTime) {
                    UserUsageConfig.firstShowAiSidebar = false
                    setCurrentSidebar(configSidebar)
                    changeAIState(false)
                }
            }

            else -> {
                setCurrentSidebar(configSidebar)
            }
        }
    }

    private fun setCurrentSidebar(sidebar: Sidebar) {
        sideBarViewModel.changeSidebarState(sidebar)
        sidebarAnimationTime = ANIMATION_DURATION_TIME
    }


    private fun changeSnippetZoom(
        isExpand: Boolean,
        animationTime: Long,
        moveLayer: Boolean = true,
        animationEndListener: (() -> Unit)? = null,
    ) {
        if (!checkViewBindingValidity()) return
        val oldState = snippetListViewState
        snippetListViewState = if (isExpand) {
            SnippetViewModel.SnippetListViewState.SHOWING
        } else {
            SnippetViewModel.SnippetListViewState.HIDING
        }
        val snippetCollapseWidth =
            resources.getDimensionPixelSize(R.dimen.dp_420)
        val snippetExpandWidth =
            resources.getDimensionPixelSize(R.dimen.dp_840)
        val animatorArgs = if (isExpand) {
            intArrayOf(snippetCollapseWidth, snippetExpandWidth)
        } else {
            intArrayOf(
                snippetExpandWidth, snippetCollapseWidth
            )
        }
        snippetZoomAnimator = ValueAnimator.ofInt(
            *animatorArgs
        ).apply {
            duration = animationTime
            addUpdateListener {
                if (checkViewBindingValidity()) {
                    val targetWidth = it.animatedValue as Int
                    binding.pageSnippetList.let { snippetLayout ->
                        val lp = snippetLayout.layoutParams
                        lp.width = targetWidth
                        snippetLayout.layoutParams = lp
                    }
                    if (moveLayer) {
                        binding.layer.translationX =
                            if (!KiloApp.isLayoutRtl) (it.animatedValue as Int).toFloat() else -(it.animatedValue as Int).toFloat()
                        binding.toolBar.updateLocation()
                    }
                }
            }
            doOnStart {
                if (checkViewBindingValidity()) {
                    binding.pageSnippetList.removeFirstRowLastPositionDecoration()
                    if (!isExpand) {
                        binding.pageSnippetList.refreshRoomState(false)
                    } else {
                        if (animationTime == NO_ANIMATION_DURATION) {
                            binding.pageSnippetList.refreshRoomState(true)
                        }
                    }
                }
            }
            doOnEnd {
                if (checkViewBindingValidity()) {
                    if (isExpand && animationTime != NO_ANIMATION_DURATION) {
                        binding.pageSnippetList.refreshRoomState(true)
                    }
                    binding.pageSnippetList.changeLabelSelectorIsExtended(isExpand)
                    binding.pageSnippetList.changeColorSelectorIsExtended(isExpand)
                    snippetListViewState = if (isExpand) {
                        SnippetViewModel.SnippetListViewState.EXPANDED
                    } else {
                        if (oldState == SnippetViewModel.SnippetListViewState.HIDDEN_EXPANDED) {
                            SnippetViewModel.SnippetListViewState.HIDDEN
                        } else {
                            SnippetViewModel.SnippetListViewState.SHOWN
                        }
                    }
                    animationEndListener?.invoke()
                }
            }
        }
        snippetZoomAnimator?.start()
    }

    fun resetSideBarViewState() {
        thumbnailListViewState = NoteViewModel.ThumbnailListViewState.HIDDEN
        snippetListViewState = SnippetViewModel.SnippetListViewState.HIDDEN
        sidebarAnimationTime = NO_ANIMATION_DURATION
    }

    private fun showTagDeleteDialog(noteRecordItem: NoteRecordItem, recordTag: RecordTag) {
        val alertDialog = AlertDialog.Builder()
            .setTitle(resources.getString(R.string.note_record_tag_delete_title))
            .setMsg(resources.getString(R.string.note_record_tag_delete_msg))
            .setIsCancelable(false)
            .setPositiveBtn(resources.getString(R.string.cancel)) {

            }
            .setPositiveBtnColor(
                resources.getColor(
                    R.color.text_secondary,
                    null
                )
            )
            .setNegativeBtn(resources.getString(R.string.confirm)) {
                recordViewModel.deleteTag(recordTag.uuid)
                noteRecordItem.record.removeTag(recordTag.uuid)
                recordListWindow?.refreshRecordListByRecordItem(noteRecordItem)
            }
            .setNegativeBtnColor(
                resources.getColor(
                    R.color.note_record_delete_confirm_color,
                    null
                )
            )
            .build()
        alertDialog.show(parentFragmentManager, null)
    }

    override fun onThumbnailChecked(position: Int) {
        if (currentDoc.viewingPageIndex == position) {
            hideLoadingDialog()
            return
        }
        currentDoc.viewingPageIndex = position
        setPage(currentDoc[position])
    }

    override fun onThumbnailAdd(position: Int) {
        EditEvent.sendEditSidebarUsage("add")
        val targetPage = currentDoc[position]
        val paper = targetPage.paper.clone()
        lifecycleScope.launch(Dispatchers.IO) {
            val page = editorViewModel.newPageAfter(
                currentDoc,
                paper,
                targetPage.version,
                position,
                targetPage.pageViewPortOffsetToPaperInPoint,
                targetPage.pageViewPortBackgroundColor
            )
            withContext(Dispatchers.Main) {
                binding.thumbnailAndOutlineView.addThumbnailPageWithIndex(position + 1, page)
                notifyThumbnailListRangeUpdate(position)
                setPage(page, isNewPage = true)
                updatePageIndex()
            }
        }
    }

    override fun onThumbnailDel(position: Int) {
        if (currentDoc.pageCount == 1) return
        if (addPageViewModel.newPageJob != null && addPageViewModel.newPageJob!!.isActive) return
        EditEvent.sendEditSidebarUsage("delete")
        if (DocumentManager.checkPageHasLinkedWithOutline(currentDoc, position)) {
            thumbnailAndOutlineViewStatusViewModel.deleteThumbnailPosition = position
            showDeleteThumbnailWithOutlineAlertDialog()
        } else {
            deleteThumbnailAction(position)
        }
    }

    override fun onThumbnailClear(position: Int) {
        EditEvent.sendEditSidebarUsage("erase")
        val page = currentDoc.pages[position]
        noteViewModel.currentDoc?.updateAndStoreModifiedTime()
        coroutineScope.launch(Dispatchers.IO) {
            var loadDrawsHere = false
            if (!page.isDrawsLoaded && page.draws.isEmpty()) {
                if (currentDoc.isStoredInObsoleteKiloNotesRoom()) {
                    DocumentManager.parsePageDraws(currentDoc, page)
                } else {
                    NoteRepository.inflatePageDrawingElements(page)
                }
                loadDrawsHere = true
                val showSnippetUUID =
                    SnippetManager.getSnippetsOfSpecificPage(currentDoc, page)
                page.needShowSnippetIds = showSnippetUUID
            }
            withContext(Dispatchers.Main) {
                if (position == currentDoc.viewingPageIndex) {
                    doodleView.clear()
                } else {
                    page.clearDraws()
                }
            }
            NoteRepository.savePageDrawingElementsOrder(page, page.draws.map { it.elementId })
            SearchManager.updatePageContentSearchIndexAsyncIfNeeded(currentDoc, page, true)
            if (page.draws.isEmpty()) {
                ThumbnailManager.saveDefaultThumbnail(currentDoc, position)
            } else {
                val job = ThumbnailManager.savePageThumbnail(currentDoc, page)
                job.await()
            }
            withContext(Dispatchers.Main) {
                noteViewModel.cancelUpdateThumbnailJob(page)
                binding.thumbnailAndOutlineView.notifyThumbnailItemChanged(position)
            }
            NoteRepository.saveDocumentInfo(currentDoc)
            if (loadDrawsHere) {
                page.unloadDraws()
            }
        }

        thumbnailViewModel.clearCommands(page)
    }

    override fun onUpdateTextAttributes(textAttributes: TextAttributes) {
        if (!checkViewBindingValidity()) return
        getToolTextSizeAdapter()?.apply {
            val action = onSizeChangeAction
            onSizeChangeAction = null
            updateTextSize(textAttributes.size.value.toInt())
            onSizeChangeAction = action
        }
        getToolColorAdapter()?.apply {
            val action = colorChangedAction
            colorChangedAction = null
            setFirstColorPosition(textAttributes.color)
            colorChangedAction = action
        }
        getToolTextParagraphStyleAdapter()?.apply {
            updateTextParagraphIcon(textAttributes.textGravity)
        }
    }

    override fun onUpdateFontAttributes(fontAttributes: FontAttributes) {
        getToolTextFontAdapter()?.apply {
            updateTextFont(fontAttributes.fontInfo)
        }
    }

    /**
     * 1. 复制PDF，缩略图到/cache/copy下，如果该目录下有文件则删除
     * 2. 获取noteViewModel.copyPage 加入当前document中
     * 3. 将/cache/copy复制到对应目录 并更新 recyclerView
     */
    override fun onThumbnailCopy(position: Int) {
        EditEvent.sendEditSidebarUsage("copy")
        noteViewModel.copyPage(currentDoc, position)
    }

    override fun onThumbnailPaste(position: Int) {
        val copyPage = noteViewModel.getCopiedPageWithDocumentId(currentDoc.uuid) ?: return
        EditEvent.sendEditSidebarUsage("paste")
        if (noteViewModel.copyResult.value?.copyStatus == NoteViewModel.CopyStatus.SUCCESS) {
            noteViewModel.pastePage(position, currentDoc, copyPage) {
                noteViewModel.currentDoc?.updateAndStoreModifiedTime()
                pasteFinish(position)
            }
        } else if (noteViewModel.copyResult.value?.copyStatus == NoteViewModel.CopyStatus.PROGRESS) {
            val value = noteViewModel.pasteResult.value?.apply {
                this.pasteStatus = NoteViewModel.PasteStatus.PENDING
                this.targetDoc = currentDoc
                this.position = position
            }
            LogHelper.d(defaultTag, "PROGRESS")
            noteViewModel.pasteResult.postValue(value)
        }
    }

    /**
     * 仅更新可视区域的RecyclerView
     * RecyclerView的默认缓存是2，所以要连着缓存一起更新
     */
    private fun notifyThumbnailListRangeUpdate(startPos: Int) {
        val itemCount =
            binding.thumbnailAndOutlineView.getThumbnailItemCount()
        binding.thumbnailAndOutlineView.notifyThumbnailItemRangeChanged(
            startPos,
            itemCount - startPos + 1
        )
    }

    private fun pasteFinish(position: Int) {
        val pastePos = position + 1
        binding.thumbnailAndOutlineView.addThumbnailPageWithIndex(pastePos, currentDoc[pastePos])

        notifyThumbnailListRangeUpdate(position)
        if (position < currentDoc.viewingPageIndex) {
            currentDoc.viewingPageIndex++
            binding.thumbnailAndOutlineView.changeThumbnailCheckedItem(currentDoc.viewingPageIndex)
        }
        if (pastePos == currentDoc.viewingPageIndex - 1 || pastePos == currentDoc.viewingPageIndex + 1) {
            binding.thumbnailAndOutlineView.updatePageThumbnail()
        }
        updatePageIndex()
    }


    private fun updateDoodlePageIfNeed(position: Int) {
        if (position in currentDoc.viewingPageIndex - 1..currentDoc.viewingPageIndex + 1) {
            setPage(currentDoc[currentDoc.viewingPageIndex])
        }
    }

    override fun onUndoStatusChange(isEnable: Boolean) {
        if (checkViewBindingValidity()) {
            binding.undo.isEnabled = isEnable
            binding.consoleUndoRedo.setCanUndo(isEnable)
            commandViewModel.undoStack.value?.let { undoStack ->
                val currentPage = currentDoc[currentDoc.viewingPageIndex]
                val currentUndoStack = undoStack[currentPage]
                val size = currentUndoStack?.size ?: 0
                binding.consoleUndoRedo.setMaxUndo(size)
            }
        }
        if (isEnable && !isConsoleState) {
            DigitalInkRecognitionManager.isRecognizedJustNow = false
        }
    }

    override fun onRedoStatusChange(isEnable: Boolean) {
        if (checkViewBindingValidity()) {
            binding.redo.isEnabled = isEnable
            binding.consoleUndoRedo.setCanRedo(isEnable)
            commandViewModel.redoStack.value?.let { redoStack ->
                val currentPage = currentDoc[currentDoc.viewingPageIndex]
                val currentRedoStack = redoStack[currentPage]
                val size = currentRedoStack?.size ?: 0
                binding.consoleUndoRedo.setMaxRedo(size)
            }
        }
    }

    override fun onInputTextViewShow() {
        super.onInputTextViewShow()
        if (checkViewBindingValidity()) {
            binding.textOperationContainer.isVisible = true
            if (adsorptionEdgeViewModel.edge == AdsorptionEdgeLayout.EDGE.BOTTOM) {
                binding.toolBar.changeBoundaryView(
                    binding.textOperationContainer.id,
                    AdsorptionEdgeLayout.EDGE.BOTTOM
                )
            }
            (binding.textOperationContainer.layoutParams as? MarginLayoutParams)?.let { layoutParams ->
                layoutParams.bottomMargin =
                    if (adsorptionEdgeViewModel.edge == AdsorptionEdgeLayout.EDGE.BOTTOM) binding.minorToolContent.height else 0
                binding.textOperationContainer.layoutParams = layoutParams
            }
        }
    }

    override fun onInputTextViewDismiss() {
        super.onInputTextViewDismiss()
        if (checkViewBindingValidity()) {
            binding.textOperationContainer.isVisible = false
            binding.toolBar.changeBoundaryView(
                ResourcesCompat.ID_NULL,
                AdsorptionEdgeLayout.EDGE.BOTTOM
            )
        }
        toolAttributesViewModel.setTextStrikethrough(false)
        toolAttributesViewModel.setTextBold(false)
        toolAttributesViewModel.setTextUnderLine(false)
    }

    override fun getDoodleViewBottomOffset(): Int {
        val bottomOffset = resources.getDimensionPixelSize(R.dimen.dp_10)
        return binding.textOperationContainer.height + bottomOffset
    }

    override fun navigateToInstantAlpha(source: String, uri: Uri, alpha: Int) {
        super.navigateToInstantAlpha(source, uri, alpha)
        parentFragment?.safeNavigate(
            R.id.note_editor,
            NoteEditorFragmentDirections.actionNoteEditorToInstantAlphaFragment()
                .apply {
                    this.source = source
                    this.imageUri = uri
                    this.alpha = alpha
                })
    }

    private val recognizeTaskMap = mutableMapOf<UUID, Bitmap>()
    private val fetchTextTaskMap = mutableMapOf<UUID, Bitmap>()
    private val outlineAdditionTaskMap = mutableMapOf<UUID, Bitmap>()
    private val searchTaskMap = mutableMapOf<UUID, Bitmap>()
    var onFinishAction: (result: TextRecognitionResult, taskId: UUID) -> Unit = { result, taskId ->
        var recognizeBitmap = recognizeTaskMap.remove(taskId)
        if (recognizeBitmap != null) {
            showCommonWebSideBar(result.symbols)
            recognizeBitmap.recycle()
        }
        var fetchTextResult = fetchTextTaskMap.remove(taskId)
        if (fetchTextResult != null) {
            if (result.symbols.isNotEmpty() && result.symbols.isNotBlank()) {
                EditEvent.senLassoToolsFetchTextBtnClick(result.symbols)
                ClipboardUtil.newPlain(requireContext(), result.symbols)
                fetchTextResult.recycle()
                if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2) {
                    ToastUtils.topCenter(
                        requireContext(),
                        AppUtils.getString(R.string.lasso_text_recognition_succeed)
                    )
                }
                EditEvent.sendEditLassoFetchText("success")
            } else {
                ToastUtils.topCenter(
                    requireContext(),
                    AppUtils.getString(R.string.lasso_text_recognition_fail)
                )
                EditEvent.sendEditLassoFetchText("fail")
            }
        }
        var outlineAdditionTitle = outlineAdditionTaskMap.remove(taskId)
        if (outlineAdditionTitle != null) {
            OutlineEvent.outlineDialogCreateSource = OutlineEvent.LASSO
            showCreateOutlineDialog(result.symbols)
            outlineAdditionTitle.recycle()
        }
        var searchBitmap = searchTaskMap.remove(taskId)
        if (searchBitmap != null) {
            if (result.symbols.isNotEmpty() && result.symbols.isNotBlank()) {
                EditEvent.senLassoToolsSearchBtnClick(result.symbols)
                val textToSearch =
                    CharacterUtils.removeFirstAndLastSymbol(result.symbols) //删除首尾非字母或数字的字符
                if (sideBarViewModel.currentSidebarState.value != Sidebar.DOC_SEARCH) {
                    changeCurrentSidebarStatus(Sidebar.DOC_SEARCH)
                }
                SearchEvent.sendSearchBoxKeywordInput(SearchEvent.SEARCH_SOURCE_OCR)
                if (noteViewModel.isHiddenSpaceMode.value == true) {
                    searchViewModel.changeSymbols(textToSearch, noteViewModel.allHiddenDocuments)
                } else {
                    searchViewModel.changeSymbols(
                        textToSearch,
                        noteViewModel.allUnhiddenDocuments
                    )
                }
                hideOtherDocPreviewBottomSheet()
                searchBitmap.recycle()
            } else {
                searchViewModel.emptySymbols()
                noteSearchFrameLayout?.setSearchBoxText("")
                ToastUtils.topCenter(
                    requireContext(),
                    AppUtils.getString(R.string.search_no_result)
                )
                SearchEvent.sendNoteSearchException(SearchEvent.SEARCH_EXCEPTION_NO_SEARCH_KEYWORDS)
            }
        }
    }

    private fun showOutlineCreateDialog() {
        val oldOutlineCreateDialog =
            parentFragmentManager.findFragmentByTag(OutlineCreateDialog.TAG) as? OutlineCreateDialog
        outlineCreateDialog = oldOutlineCreateDialog ?: OutlineCreateDialog()
        outlineCreateDialog?.apply {
            getPageSize = {
                currentDoc.pageCount
            }
            getCurrentPageUUID = {
                currentDoc.getCurrentPage().uuid
            }
            getUUIDByPageIndex = { index ->
                currentDoc.pages[index].uuid
            }
            deleteOutline = { outlineEntity ->
                currentDoc.deleteOutline(outlineEntity) {
                    NoteRepository.runInNoteOperationScopeAsync {
                        saveDocumentInfo(currentDoc)
                    }
                    if (currentDoc.outlineList.isEmpty()) {
                        thumbnailAndOutlineViewStatusViewModel.setOutlineViewIsEditMode(false)
                    }
                    thumbnailAndOutlineViewStatusViewModel.removeCurrentSelectedOutline(
                        outlineEntity
                    )
                    binding.thumbnailAndOutlineView.notifyOutlineDataSetChanged()
                }
                thumbnailAndOutlineViewStatusViewModel.hideOutlineEditDialog()
                ToastUtils.topCenter(
                    requireContext(),
                    AppUtils.getString(R.string.note_outline_delete_success)
                )
            }
            addOutline = { outlineEntity ->
                currentDoc.addOutline(outlineEntity) {
                    NoteRepository.runInNoteOperationScopeAsync {
                        saveDocumentInfo(currentDoc)
                    }
                    binding.thumbnailAndOutlineView.notifyOutlineDataSetChanged()
                }
                thumbnailAndOutlineViewStatusViewModel.hideOutlineEditDialog()
                ToastUtils.topCenter(
                    requireContext(),
                    AppUtils.getString(R.string.note_outline_create_success)
                )
            }
            updateOutline = { _ ->
                currentDoc.updateOutline() {
                    NoteRepository.runInNoteOperationScopeAsync {
                        saveDocumentInfo(currentDoc)
                    }
                    binding.thumbnailAndOutlineView.notifyOutlineDataSetChanged()
                }
                thumbnailAndOutlineViewStatusViewModel.hideOutlineEditDialog()
            }
            outlinePageIsCurrent = { uuid ->
                currentDoc.getCurrentPage().uuid == uuid
            }
            getIndexByPageUUID = { uuid ->
                var pageIndex: Int? = null
                currentDoc.pages.forEachIndexed { index, page ->
                    if (page.uuid == uuid) {
                        pageIndex = index
                    }
                }
                pageIndex
            }
            setOnDismissListener {
                outlineCreateDialog = null
            }
        }
        if (oldOutlineCreateDialog == null) {
            outlineCreateDialog?.show(parentFragmentManager, OutlineCreateDialog.TAG)
        }
    }

    override fun translateByBitmap(bitmap: Bitmap) {
        super.translateByBitmap(bitmap)
        val taskId = TextRecognitionManager.addRecognitionTask(
            bitmap,
            TextRecognitionManager.HIGH_PRIORITY
        )
        recognizeTaskMap[taskId] = bitmap
    }

    override fun searchByBitmap(bitmap: Bitmap) {
        super.searchByBitmap(bitmap)
        val taskId = TextRecognitionManager.addRecognitionTask(
            bitmap,
            TextRecognitionManager.HIGH_PRIORITY
        )
        searchTaskMap[taskId] = bitmap
    }

    override fun fetchTextByLasso(bitmap: Bitmap) {
        TextRecognitionManager.changeRecognitionLanguage(UserUsageConfig.lassoFetchTextLanguage)
        val taskId = TextRecognitionManager.addRecognitionTask(
            bitmap,
            TextRecognitionManager.HIGH_PRIORITY
        )
        fetchTextTaskMap[taskId] = bitmap
    }

    override fun outlineAdditionByLasso(bitmap: Bitmap) {
        super.outlineAdditionByLasso(bitmap)
        val taskId = TextRecognitionManager.addRecognitionTask(
            bitmap,
            TextRecognitionManager.HIGH_PRIORITY
        )
        outlineAdditionTaskMap[taskId] = bitmap
    }

    override fun showCreateOutlineDialog(title: String) {
        thumbnailAndOutlineViewStatusViewModel.showOutlineEditDialogByTitle(title)
    }

    private fun showDeleteThumbnailWithOutlineAlertDialog() {
        deleteThumbnailWithOutlineAlertDialog = AlertDialog.Builder()
            .setTitle(resources.getString(R.string.page_overview_delete))
            .setMsg(resources.getString(R.string.note_thumbnail_delete_thumbnail_with_outline_tip_message))
            .setPositiveBtn(resources.getString(R.string.cancel)) {
                thumbnailAndOutlineViewStatusViewModel.deleteThumbnailPosition = null
            }
            .setPositiveBtnColor(getColor(R.color.text_secondary))
            .setNegativeBtn(resources.getString(R.string.delete)) {
                thumbnailAndOutlineViewStatusViewModel.deleteThumbnailPosition?.let { position ->
                    deleteThumbnailAction(position)
                }
                thumbnailAndOutlineViewStatusViewModel.deleteThumbnailPosition = null
            }
            .setNegativeBtnColor(
                getColor(
                    R.color.note_list_delete_doc_text_color
                )
            ).build()
        deleteThumbnailWithOutlineAlertDialog?.safeShow(
            parentFragmentManager,
            DELETETHUMBNAIL_WITH_OUTLINE_ALERT_DIALOG
        )
    }

    private fun deleteThumbnailAction(position: Int) {
        val targetPage = currentDoc[position]
        val jumpIndex = if (position == currentDoc.pageCount - 1) {
            position - 1
        } else {
            position
        }
        currentDoc.pageIds.remove(targetPage.uuid)
        currentDoc.pages.remove(targetPage)
        coroutineScope.launch(Dispatchers.IO) {
            targetPage.thumbnail?.let { thumbnail ->
                ThumbnailManager.deleteThumbnail(currentDoc, thumbnail)
            }
            DocumentManager.deletePdfIfNeed(currentDoc, targetPage)
        }
        binding.thumbnailAndOutlineView.removeThumbnailPage(targetPage)
        binding.thumbnailAndOutlineView.notifyThumbnailItemRemoved(position)
        notifyThumbnailListRangeUpdate(jumpIndex)
        if (currentDoc.viewingPageIndex != 0 && jumpIndex < currentDoc.viewingPageIndex) {
            currentDoc.viewingPageIndex--
            binding.thumbnailAndOutlineView.changeThumbnailCheckedItem(currentDoc.viewingPageIndex)
        }
        updateDoodlePageIfNeed(jumpIndex)
        updatePageIndex()
        currentDoc.deleteOutlineByUUID(targetPage.uuid) {
            binding.thumbnailAndOutlineView.notifyOutlineDataSetChanged()
        }
        if (position == 0) {
            currentDoc.coverCategoryId = NotebookCoverType.CUSTOM.categoryId
            val width = currentDoc.pages[position].width
            val height = currentDoc.pages[position].height
            currentDoc.coverId = if (width > height) {
                CreateNotebookViewModel.HORIZONTAL_CUSTOM_COVER_ID
            } else {
                CreateNotebookViewModel.VERTICAL_CUSTOM_COVER_ID
            }
        }
        SearchManager.deleteSearchIndexEntityAsync(currentDoc.uuid, targetPage.uuid)
        noteViewModel.currentDoc?.updateAndStoreModifiedTime()
        NoteRepository.runInNoteOperationScopeAsync {
            NoteRepository.deletePageOfDocument(targetPage, currentDoc)
        }
    }

    override fun onDestroy() {
        thumbnailAnimator?.cancel()
        snippetAnimator?.cancel()
        snippetZoomAnimator?.cancel()
        materialOrTemplateAnimator?.cancel()
        materialAnimator?.cancel()
        addPageAnimator?.cancel()
        webSidebarViewAnimator?.cancel()
        searchAnimator?.cancel()
        aiAnimator?.cancel()
        draftAnimator?.cancel()
        super.onDestroy()
        //activity destroy or fragment exit
        if (!requireActivity().isChangingConfigurations) {
            templateViewModel.cancelCurrentDownload(true)
            TextRecognitionManager.waitRecognitionTaskFinishAndCloseRecognizer()
        }
    }

    override fun onWindowInsetsChange(
        isSoftKeyboardShowing: Boolean,
        softKeyboardHeight: Int,
        isStatusBarShowing: Boolean,
        statusBarHeight: Int,
        isNavigationBarShowing: Boolean,
        navigationBarHeight: Int,
    ) {
        super.onWindowInsetsChange(
            isSoftKeyboardShowing,
            softKeyboardHeight,
            isStatusBarShowing,
            statusBarHeight,
            isNavigationBarShowing,
            navigationBarHeight
        )
        binding.root.apply {
            setPadding(
                paddingLeft, paddingTop, paddingRight, if (isNavigationBarShowing) {
                    navigationBarHeight
                } else {
                    0
                }
            )
        }

        noteAIFragment?.onSoftKeyboardStateChange(
            isSoftKeyboardShowing,
            softKeyboardHeight,
            isNavigationBarShowing,
            navigationBarHeight
        )

        val textOperationTranslationY = if (isSoftKeyboardShowing) {
            -softKeyboardHeight.toFloat() + navigationBarHeight + binding.bannerAdContainer.height
        } else {
            0F
        }
        if (DimensionUtil.isLandAndFullScreen(context) ||
            DimensionUtil.isLandAndOneThirdScreen(context) ||
            DimensionUtil.isLandAndHalfScreen(context) ||
            DimensionUtil.isPortraitAndFullScreen(context)
        ) {
            binding.pageSnippetList.softKeyboardHeightUpdate(softKeyboardHeight)
        }
        if (!isSoftKeyboardShowing) {
            binding.pageSnippetList.removeEditTextFocus()
            outlineCreateDialog?.clearFocus()
            (activity as? MainActivity)?.unRegisterClearFocusAndHideSoftKeyBoardListener()
        }
        binding.textOperationContainer.animate().translationY(textOperationTranslationY)
            .setDuration(0)
            .start()

        if (adsorptionEdgeViewModel.edge == AdsorptionEdgeLayout.EDGE.BOTTOM) {
            binding.toolBar.updatePositionWithAnimation(duration = 0L)
        }
        binding.draftPaperFragmentContainer.moveMarginBottom =
            (navigationBarHeight + draftPaperSizeAndLocationHelper.getDraftPaperVerticalMinMargin(
                requireContext()
            )).toFloat() + binding.bannerAdContainer.height

        if (!isSoftKeyboardShowing) {
            val fragmentManager = activity?.supportFragmentManager
            val noteSnippetManagerDialog =
                fragmentManager?.findFragmentByTag(NoteSnippetManagerDialog.TAG)
            if (noteSnippetManagerDialog is NoteSnippetManagerDialog && noteSnippetManagerDialog.isVisible) {
                noteSnippetManagerDialog.saveSnippetNameAndClearFocus()
            }
            if (DimensionUtil.isLandAndFullScreen(context) || DimensionUtil.isPortraitAndFullScreen(
                    context
                )
            ) {
                recordListWindow?.notifyItemToNormalStyle()
            }
            binding.pageSnippetList.clearSearchBoxCursor()
        }
        recordListWindow?.translateRecordList(softKeyboardHeight, isSoftKeyboardShowing)
    }

    private fun showDecoupage() {
        safeNavigate(R.id.decoupage_fragment)
    }

    private fun showInstantAlpha() {
        safeNavigate(
            R.id.note_editor,
            NoteEditorFragmentDirections.actionNoteEditorToInstantAlphaFragment().apply {
                source = "banner"
            })
    }

    private fun isLandOneThirdScreenOrLandHalfScreen(): Boolean {
        return DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext())
                || DimensionUtil.isLandAndOneThirdScreen(requireContext())
                || DimensionUtil.isLandAndHalfScreen(requireContext())
    }

    private fun changeAIState(isShow: Boolean) {
        if (!checkViewBindingValidity()) return
        binding.noteMajorToolLayout.showAi.isSelected = isShow
        if (!isShow) {
            aiViewModel.aiChatListScrollPosition = null
            aiViewModel.endSession()
            noteAIFragment?.dismiss()
        }
    }

    private fun changeAddPageState(isShow: Boolean) {
        if (!isAdded) return
        val multiWindowCondition = DimensionUtil.isLandAndOneThirdScreen(context)
                || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)
        if (multiWindowCondition) return
        if (!checkViewBindingValidity()) return
        binding.noteMajorToolLayout.newPage.isSelected = isShow
        if (isShow) {
            binding.noteAddPageLayout.addPageCallback = { paper, pageVersion ->
                val pdfFile = currentDoc.resources.openFile(paper.file)
                if (!paper.isBuiltin() && !pdfFile.exists()) {
                    LogHelper.d("NoteEditorFragment", "AddPage 纸张PDF资源缺失")
                    false
                } else {
                    if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE) {
                        AlertDialog.Builder()
                            .setTitle(resources.getString(R.string.page_replace_title))
                            .setMsg(resources.getString(R.string.page_replace_tip))
                            .setPositiveBtn(resources.getString(R.string.add_page_replace)) {
                                addPageViewModel.addPage(
                                    InsertPosition.REPLACE,
                                    paper,
                                    pageVersion
                                )
                                changeCurrentSidebarStatus(Sidebar.NONE)
                            }
                            .setNegativeBtnColor(getColor(R.color.text_secondary))
                            .setNegativeBtn(resources.getString(R.string.cancel)) { }
                            .build().show(childFragmentManager, "")
                        true
                    } else {
                        addPageViewModel.addPage(
                            addPageViewModel.insertPosition.value!!,
                            paper,
                            pageVersion
                        )
                        changeCurrentSidebarStatus(Sidebar.NONE)
                        true
                    }
                }
            }
            binding.noteAddPageLayout.importImagePageCallback = {
                checkAndPickImageForCreatePage()
            }
            binding.noteAddPageLayout.onCloseCallback = {
                changeCurrentSidebarStatus(Sidebar.NONE)
            }
            binding.noteAddPageLayout.addTemplateCallback = { template, document ->
                if (NetworkUtils.isNetworkAvailable()) {
                    lifecycleScope.launch(Dispatchers.IO) {
                        val job = async {
                            fontDownloadViewModel.template = template
                            fontDownloadViewModel.checkFontResources(document)
                        }
                        val result = job.await()
                        if (result) {
                            withContext(Dispatchers.Main) {
                                addTemplate(template, document)
                            }
                        }
                    }
                } else {
                    addTemplate(template, document)
                }
                true
            }
            binding.noteAddPageLayout.show(
                editorViewModel.page.paper,
                noteViewModel.currentDoc!!
            )
            binding.noteAddPageLayout.navigationCallback = {
                safeNavigate(it)
            }
        }
    }

    private fun changeMaterialState(isShow: Boolean) {
        if (!isAdded) return
        if (!checkViewBindingValidity()) return
        val multiWindowCondition =
            DimensionUtil.isLandAndOneThirdScreen(requireContext()) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                requireContext()
            )
        if (Preferences.needReportByXuanhu && Preferences.needReportShowStickerByXuanhu && isShow) {
            XuanhuHelper.sendEvent("qb_sticker_show")
            LogHelper.d(XUANHU_TAG, "悬壶：打开素材库")
            Preferences.needReportShowStickerByXuanhu = false
        }
        if (isShow) {
            PushManager.addTag(requireContext(), PushTag.OPEN_MATERIAL_USER)
        }
        if (multiWindowCondition) {
            if (isShow) {
                if (noteMaterialWindow == null) {
                    noteMaterialWindow = NoteMaterialWindow(requireContext()).apply {
                        val position =
                            noteMaterialViewModel.materialCategoryStickerList.value?.indexOf(
                                noteMaterialViewModel.currentSelectMaterialCategoryInfo.value
                            )
                        if (position != null) {
                            materialCategorySmoothMoveToPosition(position)
                            materialSmoothMoveToPosition(
                                noteMaterialViewModel.materialListScrollPosition
                            )
                        }
                        noteMaterialViewModel.initLoadNoteMaterialCategoryStickerList()
                        noteMaterialViewModel.materialCategoryStickerList.value?.let {
                            refreshTypeList(it)
                        }
                        noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.let {
                            select(it)
                        }
                        noteMaterialViewModel.currentMaterialStickerList.value?.let {
                            if (noteMaterialViewModel.selectedMaterial) {
                                refreshStickerList(it)
                            }
                        }
                        noteMaterialViewModel.currentSelectPaperCutCategory.value?.let {
                            selectPaperType(it)
                        }
                        noteMaterialViewModel.currentCustomMaterialList.value?.let {
                            if (!noteMaterialViewModel.selectedMaterial && noteMaterialViewModel.currentSelectPaperCutCategory.value == PaperCutTool.CUSTOM_MATERIAL) {
                                refreshPaperCutList(it)
                            }
                        }
                        noteMaterialViewModel.currentStickerCategoryState.value?.let { categoryState ->
                            when (categoryState) {
                                NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD -> {
                                    showBottomDownloadView()
                                }

                                NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                                    showBottomNeedVipView()
                                }

                                NoteMaterialViewModel.MaterialCategoryState.DOWNLOADING -> {
                                    showBottomDownloadingView()
                                }

                                NoteMaterialViewModel.MaterialCategoryState.DOWNLOADED,
                                NoteMaterialViewModel.MaterialCategoryState.NO_NEED_DOWNLOAD,
                                    -> {
                                    hideBottomView()
                                }

                                else -> {}
                            }
                        }
                        noteMaterialViewModel.showMaterialReloadView.value?.let {
                            if (it) {
                                showReloadView()
                            } else {
                                hideReloadView()
                            }
                        }
                        onNewTypeSelectedAction = {
                            noteMaterialViewModel.changeCurrentSelectPaperCutCategory(null)
                            noteMaterialViewModel.selectNoteMaterialSticker(it)
                        }
                        onStickerClickedAction = {
                            when (noteMaterialViewModel.currentStickerCategoryState.value) {
                                NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                                    activity?.let { activity ->
                                        ToastUtils.windowTopCenter(
                                            activity,
                                            R.string.need_buy_vip
                                        )
                                    }
                                }

                                NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD,
                                NoteMaterialViewModel.MaterialCategoryState.DOWNLOADED,
                                    -> {
                                    val filePath = it.file
                                    if (filePath == null) {
                                        activity?.let { activity ->
                                            ToastUtils.windowTopCenter(
                                                activity,
                                                R.string.need_download
                                            )
                                        }
                                    } else {
                                        val file = File(filePath)
                                        if (file.exists()) {
                                            dismiss()
                                            editorModeViewModel.switchMode(InputMode.IMAGE)
                                            val uri = Uri.fromFile(file)
                                            insertImageElement(uri)
                                            StickerDataCollection.stickerData(
                                                it.noteMaterialSticker,
                                                DataType.NOTEBOOKS_STICKER_USE
                                            )
                                        } else {
                                            activity?.let { activity ->
                                                ToastUtils.windowTopCenter(
                                                    activity,
                                                    R.string.need_download
                                                )
                                            }
                                        }
                                    }
                                }

                                else -> {}
                            }
                        }
                        onConfirmClickedAction = {
                            when (noteMaterialViewModel.currentStickerCategoryState.value) {
                                NoteMaterialViewModel.MaterialCategoryState.FREE_DOWNLOAD -> {
                                    if (NetworkUtils.isNetworkAvailable()) {
                                        if (noteMaterialViewModel.isStorageNotEnoughToDownload()) {
                                            showStorageNotEnoughToDownloadResourceDialog()
                                        } else {
                                            noteMaterialViewModel.downloadMaterialSticker(
                                                object :
                                                    NoteMaterialViewModel.StickerItemDownloadCallback {
                                                    override fun onProgress(
                                                        stickerInfo: NoteMaterialRepository.NoteMaterialStickerInfo,
                                                        progress: Float,
                                                    ) {
                                                        refreshSticker(
                                                            stickerInfo,
                                                            progress
                                                        )
                                                    }

                                                    override fun onSuccess(
                                                        stickerInfo: NoteMaterialRepository.NoteMaterialStickerInfo,
                                                        file: String,
                                                    ) {
                                                        refreshSticker(stickerInfo, file)
                                                    }
                                                }) { _, name, allDownloaded ->
                                                if (allDownloaded) {
                                                    activity?.let { activity ->
                                                        ToastUtils.windowTopCenter(
                                                            activity,
                                                            getString(
                                                                R.string.download_success,
                                                                name
                                                            )
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        activity?.let { activity ->
                                            ToastUtils.windowTopCenter(
                                                activity,
                                                R.string.toast_no_internet
                                            )
                                        }
                                    }
                                }

                                NoteMaterialViewModel.MaterialCategoryState.ONLY_VIP_DOWNLOAD -> {
                                    noteMaterialViewModel.currentSelectMaterialCategoryInfo.value?.let {
                                        EditEvent.sendEditMaterialGoToVipClick(it.noteMaterialCategory.name)
                                    }
                                    val action =
                                        NoteEditorFragmentDirections.actionNoteEditorToVipStore()
                                    safeNavigate(action)
                                }

                                else -> {
                                }
                            }
                        }
                        onReloadClickedAction = {
                            if (NetworkUtils.isNetworkAvailable()) {
                                noteMaterialViewModel.reload()
                            } else {
                                activity?.let { activity ->
                                    ToastUtils.windowTopCenter(
                                        activity,
                                        R.string.toast_no_internet
                                    )
                                }
                            }
                        }
                        setOnDismissListener {
                            changeCurrentSidebarStatus(Sidebar.NONE)
                        }

                        onCustomMaterialTypeSelectedAction = {
                            if (it == PaperCutTool.CUSTOM_MATERIAL) {
                                EditEvent.sendCustomMaterialClick()
                            } else if (it == PaperCutTool.PAPER_CUT_TOOL) {
                                UserUsageConfig.isFirstUseMaterialTool = false
                                EditEvent.sendMaterialToolClick()
                            }
                            noteMaterialViewModel.selectNoteMaterialSticker(null)
                            noteMaterialViewModel.changeCurrentSelectPaperCutCategory(it)
                        }
                        onCustomMaterialItemDelClicked = { customMaterial, _ ->
                            AlertDialog.Builder()
                                .setTitle(resources.getString(R.string.paper_cut_del_tips))
                                .setPositiveBtn(resources.getString(R.string.paper_cut_del_confirm)) {
                                    noteMaterialViewModel.deleteCustomMaterial(
                                        customMaterial
                                    )
                                }
                                .setNegativeBtnColor(getColor(R.color.text_secondary))
                                .setNegativeBtn(resources.getString(R.string.cancel)) { }
                                .build().show(childFragmentManager, "")
                        }
                        onCustomMaterialSwap = {
                            noteMaterialViewModel.saveCustomMaterials(it)
                        }
                        onCustomMaterialClick = {
                            dismiss()
                            insertCustomMaterial(it)

                        }
                        materialListScrollPositionChange = {
                            noteMaterialViewModel.changeMaterialListScrollPosition(it)
                        }
                        setPaperCutToolOnClickAction { type ->
                            when (type) {
                                PaperCutToolType.INSTANT_ALPHA.position -> {
                                    InstantAlphaEvent.sendMaterialToolKeying("banner")
                                    showInstantAlpha()
                                }

                                PaperCutToolType.PAPER_CUT.position -> {
                                    EditEvent.sendPaperCutClick()
                                    showDecoupage()
                                }
                            }
                        }
                    }
                }

                noteMaterialWindow?.exitCustomMaterial()
                binding.root.post {
                    if (activity?.isFinishing == false && checkViewBindingValidity()) {
                        noteMaterialWindow?.showAsDropDown(binding.root)
                    }
                }

                val position =
                    noteMaterialViewModel.materialCategoryStickerList.value?.indexOf(
                        noteMaterialViewModel.currentSelectMaterialCategoryInfo.value
                    )
                if (position != null) {
                    noteMaterialWindow?.materialCategorySmoothMoveToPosition(position)
                }
            } else {
                noteMaterialWindow?.dismiss()
            }
        } else {
            binding.noteMajorToolLayout.showMaterial.isSelected = isShow
        }
    }

    override fun jumpToVip() {
        val action = NoteEditorFragmentDirections.actionNoteEditorToVipStore()
        safeNavigate(action)
    }

    override fun replaceImage() {
        if (!checkViewBindingValidity()) return
        val selectedObjects = doodleView.modelManager.getSelectedObjects()
        if (selectedObjects.size == 1) {
            val insertableObject = selectedObjects.first()
            if (insertableObject is InsertableBitmap) {
                editorViewModel.selectedInsertableBitmap = insertableObject
                checkAndPickImage()
            }
        }
    }

    override fun checkImagePermission(
        onCancel: ((state: PermissionRequester.PermissionState) -> Unit)?,
        onResult: (state: PermissionRequester.PermissionState) -> Unit,
    ) {
        if (!isAdded) return
        val needRequestPermission =
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                Manifest.permission.READ_EXTERNAL_STORAGE
            } else {
                Manifest.permission.READ_MEDIA_IMAGES
            }
        PermissionRequester.showPermissionRationaleDialogThenRequest(
            title = AppUtils.getString(R.string.permission_rationale_title_for_storage),
            message = AppUtils.getString(R.string.permission_rationale_content_for_storage),
            permissionState = PermissionRequester.checkPermissionState(
                requireActivity(),
                needRequestPermission
            ),
            onRationaleDialogCanceled = {
                onCancel?.invoke(it)
            },
            fragmentManager = parentFragmentManager
        ) {
            permissionRequester.request(needRequestPermission) { isGranted ->
                if (isGranted) {
                    onResult.invoke(PermissionRequester.PermissionState.PERMISSION_GRANTED)
                } else {
                    val state = PermissionRequester.checkPermissionState(
                        this@NoteEditorFragment,
                        needRequestPermission
                    )

                    if (state == PermissionRequester.PermissionState.PERMISSION_NOT_ASK_AGAIN) {
                        val alertDialog = AlertDialog.Builder()
                            .setMsg(resources.getString(R.string.never_aks_read_external_storage))
                            .setPositiveBtn(resources.getString(R.string.go_to_set)) {
                                PermissionRequester.jumpToSetting(this@NoteEditorFragment)
                            }
                            .setNegativeBtn(resources.getString(R.string.ok)) {

                            }
                            .build()
                        alertDialog.show(
                            parentFragmentManager,
                            null
                        )
                    }

                    onResult.invoke(state)
                    LogHelper.w(
                        defaultTag,
                        "no permission to access images",
                        report2Bugly = true
                    )
                }
            }
        }
    }

    override fun checkAndPickImage() {
        checkImagePermission { state ->
            if (state == PermissionRequester.PermissionState.PERMISSION_GRANTED) {
                imageFetchViewModel.loadSidebarPictures()
                val bundle = Bundle().apply {
                    putBoolean(
                        SelectPhotoDialogActivity.BUNDLE_KEY_NEED_CROP_AND_CHANGE_ALPHA,
                        true
                    )
                }
                pickImage(bundle)
            }
        }
    }

    private fun checkAndPickImageForCreatePage() {
        checkImagePermission { state ->
            if (state == PermissionRequester.PermissionState.PERMISSION_GRANTED) {
                val bundle = Bundle().apply {
                    putBoolean(
                        SelectPhotoDialogActivity.BUNDLE_KEY_NEED_CROP_IMAGE,
                        true
                    )
                    putBoolean(
                        SelectPhotoDialogActivity.BUNDLE_KEY_NEED_FIX_RATIO,
                        true
                    )
                    putBoolean(
                        SelectPhotoDialogActivity.BUNDLE_KEY_NEED_SELECT_RATIO,
                        true
                    )
                    putBoolean(
                        SelectPhotoDialogActivity.BUNDLE_KEY_FORBID_ZOOM,
                        true
                    )
                    putBoolean(
                        SelectPhotoDialogActivity.CREATE_IS_VERTICAL,
                        !UserUsageConfig.defaultPaperOrientation
                    )
                }
                imageFetchViewModel.pickImage(
                    requireContext(),
                    pickImageForCreatePageRequester,
                    bundle
                )
            }
        }
    }

    override fun onRequestImagePermissionFromModelManager(
        onCancel: ((state: PermissionRequester.PermissionState) -> Unit)?,
        callback: (result: PermissionRequester.PermissionState) -> Unit,
    ) {
        checkImagePermission(onCancel = {
            onCancel?.invoke(it)
        }) { state ->
            if (state == PermissionRequester.PermissionState.PERMISSION_GRANTED) {
                imageFetchViewModel.loadSidebarPictures()
            }
            callback(state)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_WRITE_EXTERNAL_STORAGE_CODE && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            val isGranted = grantResults[0] == PackageManager.PERMISSION_GRANTED
            if (isGranted) {
                //权限申请成功可以保存图片
                val selectedList = shareImgViewModel.shareImgSelectedIndexList
                if (selectedList.isNotEmpty()) {
                    shareLongPicture(selectedList.sortedBy { it })
                }
            } else {
                val state = PermissionRequester.checkPermissionState(
                    this,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )

                if (state == PermissionRequester.PermissionState.PERMISSION_NOT_ASK_AGAIN) {
                    val alertDialog = AlertDialog.Builder()
                        .setMsg(resources.getString(R.string.never_aks_read_external_storage))
                        .setPositiveBtn(resources.getString(R.string.go_to_set)) {
                            PermissionRequester.jumpToSetting(this)
                        }
                        .setNegativeBtn(resources.getString(R.string.ok)) {

                        }
                        .build()
                    alertDialog.show(
                        parentFragmentManager,
                        null
                    )
                }
            }
        }
    }

    private fun shareLongPicture(pageIndexList: List<Int>) {
        val context = context ?: return

        val needRequestPermission =
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q && ContextCompat.checkSelfPermission(
                    context, Manifest.permission.WRITE_EXTERNAL_STORAGE
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            } else {
                null
            }
        if (needRequestPermission != null) {
            PermissionRequester.showPermissionRationaleDialogThenRequest(
                title = AppUtils.getString(R.string.permission_rationale_title_for_storage),
                message = AppUtils.getString(R.string.permission_rationale_content_for_storage),
                permissionState = PermissionRequester.checkPermissionState(
                    requireActivity(),
                    needRequestPermission
                ),
                fragmentManager = parentFragmentManager
            ) {
                requestPermissions(
                    arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    PhoneNoteImgShareBottomSheet.REQUEST_WRITE_EXTERNAL_STORAGE_CODE
                )
            }
        } else {
            noteViewModel.shareLongPicture(context, pageIndexList)
        }
    }

    private fun needPermissions(context: Context): Boolean {
        val needPermissions = Build.VERSION.SDK_INT < 29 && ContextCompat.checkSelfPermission(
            context, Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) != PackageManager.PERMISSION_GRANTED
        if (needPermissions) {
            requestPermissions(
                arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                REQUEST_WRITE_EXTERNAL_STORAGE_CODE
            )
        }
        return needPermissions
    }

    private fun showMoreToolWindowAtTopRight(popupWindow: PopupWindow) {
        popupWindow.apply {
            if (binding.noteMajorToolLayout.more.isVisible) {
                showAsDropDown(binding.noteMajorToolLayout.more)
            } else {
                showAsDropDown(binding.blurView, 0, 0, Gravity.END)
            }
        }
    }

    private fun checkShowDraftPaperMorePopupGuideWindow() {
        moreToolWindow?.let {
            if (UserUsageConfig.isNeedShowDraftPaperMorePopupGuide) {
                if (!isAdded) return
                it.contentView.post {
                    if (checkViewBindingValidity()) {
                        showDraftPaperMorePopupGuideWindow()
                    }
                }
            }
        }
    }

    private fun getPreviewSnippetWidth(): Int {
        val widthPixels = DimensionUtil.getScreenDimensions(context).widthPixels
        val previewSnippetWidth =
            if (DimensionUtil.isLandAndOneThirdScreen(context) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                    context
                )
            ) {
                widthPixels * (600F / 640F)
            } else if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
                widthPixels * (700F / 1080F)
            } else if (DimensionUtil.isPortraitScreen(context)) {
                widthPixels * (800F / 1080F)
            } else if (DimensionUtil.isLandAndHalfScreen(context)) {
                widthPixels * (900F / 960F)
            } else if (DimensionUtil.isLandTwoThirdScreen(context)) {
                widthPixels * (1160F / 1280F)
            } else {
                widthPixels * (1260F / 1920F)
            }
        return previewSnippetWidth.toInt()
    }

    private fun getPreviewSnippetHeight(): Int {
        return (getPreviewSnippetWidth() * (3F / 5F)).toInt()
    }

    private fun showUserBenefitDialog(isShow: Boolean) {
        if (isShow) {
            val dialog =
                parentFragmentManager.findFragmentByTag(VipExclusiveDialog.TAG) as? VipExclusiveDialog
            vipExclusiveDialog = dialog ?: VipExclusiveDialog()
            val activity = activity
            if (activity is BaseHandleImportActivity) {
                activity.globalDialogViewModel.vipExclusiveConfigByServer = false
                vipExclusiveDialog?.apply {
                    resetDialogConfig()
                    setContentMsg(AppUtils.getString(R.string.tape_control_window_vip_message))
                    setVipClickedAction {
                        noteViewModel.changeUserBenefitDialogState(false)
                        val action = NoteEditorFragmentDirections.actionNoteEditorToVipStore()
                        safeNavigate(action)
                    }
                    setCloseClickedAction {
                        noteViewModel.changeUserBenefitDialogState(false)
                    }
                }
                if (dialog == null) {
                    vipExclusiveDialog?.safeShow(parentFragmentManager, VipExclusiveDialog.TAG)
                }
            }
        }
    }

    override fun supportInputMode(): List<InputMode> {
        return listOf(
            InputMode.DRAW,
            InputMode.ERASER,
            InputMode.LASSO,
            InputMode.IMAGE,
            InputMode.HIGHLIGHTER,
            InputMode.GRAFFITI,
            InputMode.OUTLINEPEN,
            InputMode.LINEDRAW,
            InputMode.VIEW,
            InputMode.TEXT,
            InputMode.SNIPPET,
            InputMode.PRESENTATION,
            InputMode.GRAPH,
            InputMode.TAPE,
            InputMode.PEN,
            InputMode.PAINTBRUSH
        )
    }

    override fun switchToToolPen() {
        consoleViewModel.changeConsoleState(false)
        if (editorModeViewModel.editorMode.value != UserUsageConfig.lastSelectedPenMode) {
            adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
            binding.toolBar.show {}
        }
        editorModeViewModel.switchModeImmediately(UserUsageConfig.lastSelectedPenMode)

        binding.consoleNote.apply {
            isSelected = false
            changeConsoleNoteStates(isSelected)
        }
        binding.console.isSelected = false
        binding.consoleIcon.isSelected = false
    }

    override fun switchToToolHighlighter() {
        consoleViewModel.changeConsoleState(false)
        if (editorModeViewModel.editorMode.value != InputMode.HIGHLIGHTER) {
            adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
            binding.toolBar.show {}
        }
        editorModeViewModel.switchModeImmediately(InputMode.HIGHLIGHTER)
        binding.consoleNote.apply {
            isSelected = false
            changeConsoleNoteStates(isSelected)
        }
        binding.console.isSelected = false
        binding.consoleIcon.isSelected = false
    }

    override fun switchToToolImage(drawPath: Path, pathBounds: RectF) {
        consoleViewModel.changeConsoleState(false)
        if (editorModeViewModel.editorMode.value != InputMode.IMAGE) {
            adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
            binding.toolBar.show {}
        }
        editorModeViewModel.switchModeImmediately(InputMode.IMAGE)
        binding.consoleNote.apply {
            isSelected = false
            changeConsoleNoteStates(isSelected)
        }
        binding.console.isSelected = false
        binding.consoleIcon.isSelected = false

        val inDoodle = doodleView.transformToDoodle(drawPath, pathBounds)
        doodleView.doodleEditLayer.showImageInsertWindow(
            MotionEvent.obtain(
                SystemClock.uptimeMillis(),
                SystemClock.uptimeMillis(),
                MotionEvent.ACTION_DOWN,
                inDoodle.second.centerX(),
                inDoodle.second.centerY(),
                0
            ),
            true
        )
    }

    override fun switchToolText(drawPath: Path, pathBounds: RectF) {
        consoleViewModel.changeConsoleState(false)
        if (editorModeViewModel.editorMode.value != InputMode.TEXT) {
            adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
            binding.toolBar.show {}
        }
        editorModeViewModel.switchModeImmediately(InputMode.TEXT)
        binding.consoleNote.apply {
            isSelected = false
            changeConsoleNoteStates(isSelected)
        }
        binding.console.isSelected = false
        binding.consoleIcon.isSelected = false
        val inDoodle = doodleView.transformToDoodle(drawPath, pathBounds)
        val intersect = inDoodle.second.intersect(doodleView.clipRect.toRectF())
        if (intersect) {
            doodleView.modelManager.textEditor.inputTextAt(
                inDoodle.second.left,
                inDoodle.second.top
            )
            onInputTextViewShow()
        }
    }

    override fun refreshNoteAddPageLayout(paper: Paper, document: Document) {
        if (sideBarViewModel.currentSidebarState.value == Sidebar.ADD_PAGE) {
            binding.noteAddPageLayout.refreshPageList(paper, document)
        }
    }

    private fun callLogin() {
        if (checkNetwork()) {
            val fragment = parentFragmentManager.findFragmentByTag(UnifiedLogin.TAG)
            if (fragment != null && fragment is UnifiedLogin) {
                return
            }
            UserEvent.sendLoginPopoverShow(LoginLocation.EDITOR_PAGE_AI)
            UnifiedLogin().apply {
                setLoginLocation(LoginLocation.EDITOR_PAGE_AI)
                safeShow(
                    <EMAIL>,
                    UnifiedLogin.TAG
                )
            }
        }
    }

    private fun checkNetwork(): Boolean {
        return if (NetworkUtils.isNetworkAvailable()) {
            true
        } else {
            ToastUtils.topCenter(requireContext(), R.string.toast_no_internet)
            false
        }
    }
}


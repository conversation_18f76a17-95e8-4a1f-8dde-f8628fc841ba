package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.marginBottom
import androidx.core.view.marginEnd
import androidx.core.view.marginTop
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.signature.ObjectKey
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
import com.topstack.kilonotes.base.ktx.removeAllItemDecorations
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.netcover.model.CoverCategory
import com.topstack.kilonotes.base.netcover.model.NoteCover
import com.topstack.kilonotes.base.note.model.NotebookCoverType
import com.topstack.kilonotes.base.note.viewmodel.CreateNotebookViewModel
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.CoverListItemBinding
import com.topstack.kilonotes.databinding.PadFragmentCreateNoteCoverCategoryItemBinding
import java.io.File

class CoverSelectView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : OverScrollCoordinatorRecyclerView(context, attrs, defStyleAttr) {

    private lateinit var coverCategoryAdapter: CoverCategoryAdapter

    private var hasCustomCoverSelectedSet: Boolean = false

    private var customCoverUri: String? = null

    fun setCustomCoverSelected(onCustomCoverSelectedCallBack: (Pair<Int, Int>) -> Unit) {
        hasCustomCoverSelectedSet = true
        if (!::coverCategoryAdapter.isInitialized) return
        if (coverCategoryAdapter.coverCategoryList.isEmpty()) return
        coverCategoryAdapter.setCustomCoverSelected(onCustomCoverSelectedCallBack)
    }

    fun setCustomCoverUri(uri: String?) {
        customCoverUri = uri
        if (!::coverCategoryAdapter.isInitialized) return
        coverCategoryAdapter.setCustomCoverUri(uri)
    }

    fun setSelectedCoverPosition(position: Pair<Int, Int>) {
        if (!::coverCategoryAdapter.isInitialized) return
        coverCategoryAdapter.selectedCoverPosition = position
    }

    fun setupVertical(
        initialPosition: Pair<Int, Int>,
        coverCategoryList: List<CoverCategory>,
        onCoverClick: (NoteCover, Pair<Int, Int>, Int) -> Unit,
        onCustomCoverClick: (NoteCover, Boolean) -> Boolean,
        onDeleteClick: (NoteCover) -> Unit,
        onCustomCoverSelectedCallBack: (Pair<Int, Int>) -> Unit
    ) {
        overScrollRecyclerView.removeAllItemDecorations()
        coverCategoryAdapter = CoverCategoryAdapter(
            initialPosition,
            coverCategoryList,
            onCoverClick,
            onCustomCoverClick,
            onDeleteClick
        )
        coverCategoryAdapter.setCustomCoverUri(customCoverUri)
        if (hasCustomCoverSelectedSet) {
            coverCategoryAdapter.setCustomCoverSelected(onCustomCoverSelectedCallBack)
            hasCustomCoverSelectedSet = false
        }
        overScrollRecyclerView.adapter = coverCategoryAdapter
        overScrollRecyclerView.layoutManager =
            LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        overScrollRecyclerView.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                if (parent.getChildAdapterPosition(view) == 0) {
                    outRect.top = resources.getDimensionPixelSize(R.dimen.dp_48)
                } else {
                    outRect.top = resources.getDimensionPixelSize(R.dimen.dp_35)
                }
            }
        })
    }

}

class CoverCategoryAdapter(
    val initialPosition: Pair<Int, Int>,
    val coverCategoryList: List<CoverCategory>,
    val onCoverClick: (NoteCover, Pair<Int, Int>, Int) -> Unit,
    val onCustomCoverClick: (NoteCover, Boolean) -> Boolean,
    val onDeleteClick: (NoteCover) -> Unit
) : RecyclerView.Adapter<CoverCategoryAdapter.CoverCategoryViewHolder>() {

    var selectedCoverPosition: Pair<Int, Int> = initialPosition
        set(value) {
            if (field != value) {
                val oldPosition = field
                field = value
                if (oldPosition.first >= 0 && oldPosition.first < adapterList.size) {
                    if (oldPosition.second >= 0 && oldPosition.second < adapterList[oldPosition.first].itemCount) {
                        adapterList[oldPosition.first].isSelect = oldPosition.first == value.first
                        adapterList[oldPosition.first].notifyItemChanged(
                            oldPosition.second,
                            CoverAdapter.UPDATE_CHECK_STATE
                        )
                    }
                }
                if (value.first >= 0 && value.first < adapterList.size) {
                    if (value.second >= 0 && value.second < adapterList[value.first].itemCount) {
                        adapterList[value.first].isSelect = true
                        adapterList[value.first].selectedPosition = value.second
                        adapterList[value.first].notifyItemChanged(
                            value.second,
                            CoverAdapter.UPDATE_CHECK_STATE
                        )
                    }
                }
            }
        }

    val adapterList = mutableListOf<CoverAdapter>()


    init {
        coverCategoryList.forEachIndexed { position, coverCategory ->
            adapterList.add(
                CoverAdapter(
                    coverCategory.coverList,
                    Pair(selectedCoverPosition.first == position, selectedCoverPosition.second),
                    coverCategory.format,
                    coverCategoryList[initialPosition.first].coverList[initialPosition.second],
                    onCoverClick = { cover, coverIndex ->
                        onCoverClick.invoke(cover, Pair(position, coverIndex), coverCategory.format)
                    },
                    onCustomCoverClick = { cover, auto ->
                        onCustomCoverClick.invoke(cover, auto)
                    }
                ) {
                    onDeleteClick.invoke(it)
                    if (coverCategoryList[selectedCoverPosition.first].categoryId == NotebookCoverType.CUSTOM.categoryId) {
                        setDefaultCoverSelected()
                    }
                }
            )
        }
    }

    fun setCustomCoverUri(uri: String?) {
        if (adapterList.isNotEmpty()) {
            adapterList.last().customCoverUri = uri
        }
    }

    fun setCustomCoverSelected(onCustomCoverSelectedCallBack: (Pair<Int, Int>) -> Unit) {
        selectedCoverPosition = Pair(adapterList.size - 1, 0)
        onCustomCoverSelectedCallBack.invoke(selectedCoverPosition)
    }

    fun setDefaultCoverSelected() {
        selectedCoverPosition = initialPosition
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CoverCategoryViewHolder {
        val binding = PadFragmentCreateNoteCoverCategoryItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        binding.root.layoutParams = RecyclerView.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        return CoverCategoryViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CoverCategoryViewHolder, position: Int) {
        holder.bind(coverCategoryList[position], adapterList[position])
        if (position == coverCategoryList.size - 1) {
            holder.line.visibility = View.GONE
        } else {
            holder.line.visibility = View.VISIBLE
        }
    }

    override fun getItemCount(): Int {
        return coverCategoryList.size
    }


    class CoverCategoryViewHolder(binding: PadFragmentCreateNoteCoverCategoryItemBinding) : RecyclerView.ViewHolder(binding.root) {
        val categoryName = binding.coverCategoryName
        val coverListRecyclerView = binding.coverListCoordinator.overScrollRecyclerView
        val line = binding.line
        fun bind(coverCategory: CoverCategory, coverAdapter: CoverAdapter) {
            categoryName.apply {
                text = coverCategory.categoryName
                if (DimensionUtil.isLandAndHalfScreen(context) || DimensionUtil.isLandAndOneThirdScreen(
                        context
                    )
                ) {
                    setMargins(
                        resources.getDimensionPixelSize(R.dimen.dp_30),
                        marginTop,
                        marginEnd,
                        marginBottom
                    )
                }
            }
            line.apply {
                if (DimensionUtil.isLandAndHalfScreen(context) || DimensionUtil.isLandAndOneThirdScreen(
                        context
                    )
                ) {
                    setMargins(
                        resources.getDimensionPixelSize(R.dimen.dp_30),
                        marginTop,
                        marginEnd,
                        marginBottom
                    )
                }
            }
            coverListRecyclerView.apply {
                removeAllItemDecorations()
                adapter = coverAdapter
                layoutManager = LinearLayoutManager(context).apply {
                    orientation = LinearLayoutManager.HORIZONTAL
                }
                addItemDecoration(object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        val isLandAndOneThirdScreen =
                            DimensionUtil.isLandAndOneThirdScreen(view.context)
                        val isLandAndHalfScreen = DimensionUtil.isLandAndHalfScreen(view.context)
                        if (parent.getChildAdapterPosition(view) == 0) {
                            if (isLandAndOneThirdScreen || isLandAndHalfScreen) {
                                outRect.left = resources.getDimensionPixelSize(R.dimen.dp_30)
                            } else {
                                outRect.left = resources.getDimensionPixelSize(R.dimen.dp_48)
                            }
                        }
                        if (isLandAndOneThirdScreen) {
                            outRect.right = resources.getDimensionPixelSize(R.dimen.dp_20)
                        } else {
                            outRect.right = resources.getDimensionPixelSize(R.dimen.dp_48)
                        }
                        outRect.top = resources.getDimensionPixelSize(R.dimen.dp_18)
                        outRect.bottom = resources.getDimensionPixelSize(R.dimen.dp_36)
                    }
                })
            }
        }
    }
}


class CoverAdapter(
    val coverList: List<NoteCover>,
    val isSelected: Pair<Boolean, Int>,
    val coverFormatType: Int,
    val defaultCover: NoteCover,
    private val onCoverClick: (NoteCover, Int) -> Unit,
    val onCustomCoverClick: (NoteCover, Boolean) -> Boolean,
    val onDeleteClick: (NoteCover) -> Unit
) : RecyclerView.Adapter<CoverAdapter.CoverHolder>() {

    companion object {
        const val UPDATE_CHECK_STATE = 1
    }

    var isSelect: Boolean = isSelected.first

    var selectedPosition: Int = isSelected.second

    var customCoverUri: String? = null
        set(value) {
            field = value
            notifyItemChanged(0)
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CoverHolder {
        val itemBinding =
            CoverListItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        //横版
        if (coverFormatType == CreateNotebookViewModel.FORMAT_HORIZONTAL) {
            val layoutParams = itemBinding.root.layoutParams
            layoutParams.width = parent.resources.getDimensionPixelSize(R.dimen.dp_258)
            itemBinding.root.layoutParams = layoutParams

            var params = itemBinding.imageShadow.layoutParams
            params.width = parent.resources.getDimensionPixelSize(R.dimen.dp_258)
            params.height = parent.resources.getDimensionPixelSize(R.dimen.dp_194)
            itemBinding.imageShadow.layoutParams = params

        } else {
            val layoutParams = itemBinding.root.layoutParams
            val width = parent.resources.getDimensionPixelSize(R.dimen.dp_194)
            layoutParams.width = width
            itemBinding.root.layoutParams = layoutParams

            var params = itemBinding.imageShadow.layoutParams
            params.width = parent.resources.getDimensionPixelSize(R.dimen.dp_194)
            params.height = parent.resources.getDimensionPixelSize(R.dimen.dp_258)
            itemBinding.imageShadow.layoutParams = params
        }
        return CoverHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: CoverHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty() && payloads[0] == UPDATE_CHECK_STATE) {
            changeItemSelectState(holder, position)
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    override fun onBindViewHolder(holder: CoverHolder, position: Int) {
        val cover = coverList[position]
        changeItemSelectState(holder, position)
        holder.image.apply {
            if (coverList[position].categoryId == NotebookCoverType.CUSTOM.categoryId) {
                if (customCoverUri == null) {
                    if (cover.isBuiltin) {
                        Glide.with(context)
                            .load(cover.imgUrl)
                            .centerCrop()
                            .signature(ObjectKey(File(cover.imgUrl).lastModified()))
                            .into(this)
                    } else {
                        Glide.with(context)
                            .load(cover.drawableId)
                            .centerCrop()
                            .into(this)
                    }
                } else {
                    Glide.with(context)
                        .load(customCoverUri)
                        .signature(ObjectKey(File(customCoverUri).lastModified()))
                        .into(this)
                }
                //横版
                if (coverFormatType == CreateNotebookViewModel.FORMAT_HORIZONTAL) {
                    var params = holder.image.layoutParams
                    params.width = context.resources.getDimensionPixelSize(R.dimen.dp_258)
                    params.height = context.resources.getDimensionPixelSize(R.dimen.dp_194)
                    holder.image.layoutParams = params
                } else {
                    var params = holder.image.layoutParams
                    params.width = context.resources.getDimensionPixelSize(R.dimen.dp_194)
                    params.height = context.resources.getDimensionPixelSize(R.dimen.dp_258)
                    holder.image.layoutParams = params
                }


                setOnClickListener {
                    onCustomCoverClick.invoke(cover, false)
                }
            } else {
                if (cover.isBuiltin) {
                    this.setImageResource(cover.drawableId)
                } else {
                    val filePath = CreateNotebookViewModel.getLocalImgPath(cover)
                    Glide.with(context)
                        .load(filePath)
                        .signature(ObjectKey(File(filePath).lastModified()))
                        .into(this)
                }
                setOnClickListener {
                    onCoverClick.invoke(cover, position)
                }
            }
        }
        holder.deleteIcon.setOnClickListener {
            onDeleteClick.invoke(defaultCover)
            isSelect = false
        }
    }

    private fun changeItemSelectState(holder: CoverHolder, position: Int) {
        holder.selected.visibility =
            if (isSelect && selectedPosition == position) View.VISIBLE else View.INVISIBLE
        holder.imageOutline.visibility =
            if (isSelect && selectedPosition == position) View.VISIBLE else View.INVISIBLE
        holder.deleteIcon.visibility =
            if (coverList[position].categoryId == NotebookCoverType.CUSTOM.categoryId && customCoverUri != null) View.VISIBLE else View.INVISIBLE
    }


    override fun getItemCount(): Int = coverList.size

    class CoverHolder(
        binding: CoverListItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        val image = binding.image
        val deleteIcon = binding.coverDelete
        val selected = binding.selected
        val imageOutline = binding.imageOutline
    }
}
package com.topstack.kilonotes.pad.console.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.topstack.kilonotes.base.note.viewmodel.NoteViewModel
import com.topstack.kilonotes.base.note.viewmodel.SnippetViewModel
import com.topstack.kilonotes.pad.note.model.Sidebar

class ConsoleViewModel: ViewModel() {

    private val _isConsoleState: MutableLiveData<Boolean> = MutableLiveData(false)
    val isConsoleState: LiveData<Boolean>
        get() = _isConsoleState

    fun changeConsoleState(consoleState: Boolean) {
        _isConsoleState.value = consoleState
    }

    private val _isShowConsoleInstructionsView: MutableLiveData<Boolean> = MutableLiveData(false)
    val isShowConsoleInstructionsView: LiveData<Boolean>
        get() = _isShowConsoleInstructionsView

    fun changeIsShowConsoleInstructionsView(isShow: Boolean) {
        _isShowConsoleInstructionsView.value = isShow
    }

    private val _isShowConsoleSettingWindow: MutableLiveData<Boolean> = MutableLiveData(false)
    val isShowConsoleSettingWindow: LiveData<Boolean>
        get() = _isShowConsoleSettingWindow

    fun changeIsShowConsoleSettingWindow(isShow: Boolean) {
        _isShowConsoleSettingWindow.value = isShow
    }

    /**
     * K模式与所有的侧边组件的交互
     * 进入K模式时，需要将所有的侧边组件隐藏，并记录隐藏前的状态到这个ViewModel
     * 退出K模式时，需要将侧边组件状态恢复至隐藏前的状态
     */
    // 录音下拉栏的显示状态
    var isShowRecordControlView: Boolean? = false
    // 超级白板
    var isShowDraftPaper: Boolean? = false
    // 右侧的侧边栏（素材库，添加页面，搜索，翻译，网络搜索，AI）
    var currentSidebarState: Sidebar? = Sidebar.NONE
    // 左侧（缩略图和大纲）侧边栏
    var thumbnailListViewState: NoteViewModel.ThumbnailListViewState? = NoteViewModel.ThumbnailListViewState.HIDDEN
    // 左侧（卡片）侧边栏
    var snippetListViewState: SnippetViewModel.SnippetListViewState? = SnippetViewModel.SnippetListViewState.HIDDEN

    fun resetSideBarState() {
        isShowRecordControlView = null
        isShowDraftPaper = null
        currentSidebarState = null
        thumbnailListViewState = null
        snippetListViewState = null
    }
}
package com.topstack.kilonotes.pad.agreement.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.databinding.DemesticDetailedInfoItemBinding
import com.topstack.kilonotes.pad.agreement.model.DetailedInfo

class DomesticDetailedInfoAdapter(val detailedInfos: List<DetailedInfo>) :
    RecyclerView.Adapter<DomesticDetailedInfoAdapter.ViewHolder>() {

    inner class ViewHolder(binding: DemesticDetailedInfoItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = DemesticDetailedInfoItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return detailedInfos.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val detailedInfo = detailedInfos[position]
        val binding = DemesticDetailedInfoItemBinding.bind(holder.itemView)
        binding.detailedInfoTitle.text = detailedInfo.title
        binding.contentDescribe.text = detailedInfo.content
        binding.goalDescribe.text = detailedInfo.goal
        binding.sceneDescribe.text = detailedInfo.scene
        binding.collectCount.text = detailedInfo.collectCount.toString()
    }
}
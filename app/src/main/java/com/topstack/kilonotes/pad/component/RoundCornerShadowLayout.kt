package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import com.topstack.kilonotes.R
import com.topstack.kilonotes.infra.util.AppUtils
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.min

class RoundCornerShadowLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val DEFAULT_STROKE_WIDTH = 10F
        private const val DEFAULT_CORNER_RADIUS = 10F
        private const val DEFAULT_OUTER_BOARDER_COLOR = Color.BLACK
        private const val DEFAULT_INNER_BOARDER_COLOR = Color.WHITE
        private const val DEFAULT_BACKGROUND_COLOR = Color.GRAY
        private const val DEFAULT_SHADOW_COLOR = Color.GRAY
        private const val DEFAULT_SHADOW_SIZE = 10F
        private const val DEFAULT_INNER_SHADOW_SIZE = 1F
        private const val DEFAULT_SHADOW_X_OFFSET = 0F
        private const val DEFAULT_SHADOW_Y_OFFSET = 0F
    }

    enum class BorderType {
        OUTER_BORDER_OVERLAP,
        INNER_BORDER_OVERLAP,
        INNER_BORDER_OVERLAP_COMPLETELY_BACKGROUND
    }

    private var borderStrokeWidth: Float
    private var halfStrokeWidth: Float
    private var outerBorderColor: Int
    private var innerBorderColor: Int
    private var borderCornerRadius: Float
    private var backgroundColor: Int
    private var borderType: BorderType
    private var borderShadowColor: Int
    private var borderShadowInnerColor: Int
    private var borderShadowSize: Float
    private var borderShadowInnerSize: Float
    private var borderShadowXOffset: Float
    private var borderShadowYOffset: Float
    private lateinit var borderPaint: Paint
    private lateinit var backgroundPaint: Paint
    private lateinit var shadowPaint: Paint
    private lateinit var innerShadowPaint: Paint
    private lateinit var shadowClipPaint: Paint

    private fun copyWith(
        borderStrokeWidth: Float = this.borderStrokeWidth,
        halfStrokeWidth: Float = this.halfStrokeWidth,
        outerBorderColor: Int = this.outerBorderColor,
        innerBorderColor: Int = this.innerBorderColor,
        borderCornerRadius: Float = this.borderCornerRadius,
        backgroundColor: Int = this.backgroundColor,
        borderType: BorderType = this.borderType,
        borderShadowColor: Int = this.borderShadowColor,
        borderShadowSize: Float = this.borderShadowSize,
        borderShadowXOffset: Float = this.borderShadowXOffset,
        borderShadowYOffset: Float = this.borderShadowYOffset,
    ) {
        this.borderStrokeWidth = borderStrokeWidth
        this.halfStrokeWidth = halfStrokeWidth
        this.outerBorderColor = outerBorderColor
        this.innerBorderColor = innerBorderColor
        this.borderCornerRadius = borderCornerRadius
        this.backgroundColor = backgroundColor
        this.borderType = borderType
        this.borderShadowColor = borderShadowColor
        this.borderShadowSize = borderShadowSize
        this.borderShadowXOffset = borderShadowXOffset
        this.borderShadowYOffset = borderShadowYOffset
    }

    fun changeConsoleNoteStates(isSelected: Boolean) {
        if (isSelected) {
            copyWith(
                backgroundColor = AppUtils.getColor(R.color.console_document_list_background),
                innerBorderColor = AppUtils.getColor(R.color.console_note_btn_inner_border_selected),
                outerBorderColor = AppUtils.getColor(R.color.console_document_list_background_outer_border),
                borderShadowColor = AppUtils.getColor(R.color.console_btn_margin_selected)
            )
        } else {
            copyWith(
                backgroundColor = AppUtils.getColor(R.color.console_preview_page_list_background_color),
                innerBorderColor = AppUtils.getColor(R.color.console_note_btn_inner_border),
                outerBorderColor = AppUtils.getColor(R.color.black_15),
                borderShadowColor = AppUtils.getColor(R.color.console_btn_margin_selected)
            )
        }
        initPaint()
        requestLayout()
    }


    init {
        setWillNotDraw(false)
        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.ConsoleBorderShadowView,
            defStyleAttr, 0
        ).apply {
            try {
                borderShadowInnerSize = getDimension(
                    R.styleable.ConsoleBorderShadowView_border_shadow_inner_size,
                    DEFAULT_INNER_SHADOW_SIZE
                )
                borderShadowInnerColor = getColor(
                    R.styleable.ConsoleBorderShadowView_border_shadow_inner_color,
                    DEFAULT_SHADOW_COLOR
                )
                borderStrokeWidth = getDimension(
                    R.styleable.ConsoleBorderShadowView_border_stroke_width,
                    DEFAULT_STROKE_WIDTH
                ).coerceAtLeast(0F)
                halfStrokeWidth = borderStrokeWidth / 2
                borderCornerRadius = getDimension(
                    R.styleable.ConsoleBorderShadowView_border_corner_radius,
                    DEFAULT_CORNER_RADIUS
                ).coerceAtLeast(0F)
                outerBorderColor = getColor(
                    R.styleable.ConsoleBorderShadowView_outer_border_color,
                    DEFAULT_OUTER_BOARDER_COLOR
                )
                innerBorderColor = getColor(
                    R.styleable.ConsoleBorderShadowView_inner_border_color,
                    DEFAULT_INNER_BOARDER_COLOR
                )
                backgroundColor = getColor(
                    R.styleable.ConsoleBorderShadowView_inside_background_color,
                    DEFAULT_BACKGROUND_COLOR
                )
                borderType = BorderType.values()[
                    getInt(
                        R.styleable.ConsoleBorderShadowView_border_type,
                        BorderType.OUTER_BORDER_OVERLAP.ordinal
                    )
                ]
                borderShadowColor = getColor(
                    R.styleable.ConsoleBorderShadowView_border_shadow_color,
                    DEFAULT_SHADOW_COLOR
                )
                borderShadowSize = getDimension(
                    R.styleable.ConsoleBorderShadowView_border_shadow_size,
                    DEFAULT_SHADOW_SIZE
                ).coerceAtLeast(0F)
                borderShadowXOffset = getDimension(
                    R.styleable.ConsoleBorderShadowView_border_shadow_x_offset,
                    DEFAULT_SHADOW_X_OFFSET
                ).coerceIn(0F, borderShadowSize)
                borderShadowYOffset = getDimension(
                    R.styleable.ConsoleBorderShadowView_border_shadow_y_offset,
                    DEFAULT_SHADOW_Y_OFFSET
                ).coerceIn(0F, borderShadowSize)
            } finally {
                recycle()
            }
        }
        initPaint()
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        setBackgroundColor(Color.TRANSPARENT)
    }

    private fun initPaint() {
        borderPaint = Paint().apply {
            isAntiAlias = true
            style = Paint.Style.STROKE
            isDither = true
            strokeWidth = borderStrokeWidth
        }
        backgroundPaint = Paint().apply {
            isAntiAlias = true
            isDither = true
            style = Paint.Style.FILL
            color = backgroundColor
        }
        shadowPaint = Paint().apply {
            isAntiAlias = true
            isDither = true
            style = Paint.Style.FILL
            color = borderShadowColor
            maskFilter = BlurMaskFilter(borderShadowSize / 2, BlurMaskFilter.Blur.NORMAL)
        }
        shadowClipPaint = Paint().apply {
            isAntiAlias = true
            style = Paint.Style.FILL
            isDither = true
            xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
        }
        innerShadowPaint = Paint().apply {
            isAntiAlias = true
            isDither = true
            style = Paint.Style.FILL
            color = borderShadowInnerColor
            maskFilter = BlurMaskFilter(borderShadowInnerSize / 2, BlurMaskFilter.Blur.NORMAL)
        }
    }

    private val rect = RectF()

    override fun onDraw(canvas: Canvas) {
        drawShadow(canvas, shadowPaint)
        if (borderShadowInnerSize > 1) {
            drawShadow(canvas, innerShadowPaint)
        }
        drawBorder(canvas)
    }

    private fun drawShadow(canvas: Canvas, paint: Paint) {
        rect.set(
            borderShadowSize,
            borderShadowSize,
            width - borderShadowSize,
            height - borderShadowSize
        )
        canvas.drawRoundRect(
            rect,
            borderCornerRadius + borderStrokeWidth,
            borderCornerRadius + borderStrokeWidth,
            paint
        )
        rect.set(
            borderShadowSize - borderShadowXOffset,
            borderShadowSize - borderShadowYOffset,
            width - borderShadowSize - borderShadowXOffset,
            height - borderShadowSize - borderShadowYOffset
        )
        canvas.drawRoundRect(
            rect,
            borderCornerRadius + borderStrokeWidth,
            borderCornerRadius + borderStrokeWidth,
            shadowClipPaint
        )
    }

    private fun drawBorder(canvas: Canvas) {
        when (borderType) {
            BorderType.OUTER_BORDER_OVERLAP -> {
                rect.set(
                    borderStrokeWidth + borderShadowSize - borderShadowXOffset,
                    borderStrokeWidth + borderShadowSize - borderShadowYOffset,
                    width - borderStrokeWidth - borderShadowSize - borderShadowXOffset,
                    height - borderStrokeWidth - borderShadowSize - borderShadowYOffset
                )
                borderPaint.color = outerBorderColor
                borderPaint.strokeWidth = borderStrokeWidth * 2
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius,
                    borderCornerRadius,
                    borderPaint
                )

                rect.inset(halfStrokeWidth, halfStrokeWidth)
                borderPaint.color = innerBorderColor
                borderPaint.strokeWidth = borderStrokeWidth
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius - halfStrokeWidth,
                    borderCornerRadius - halfStrokeWidth,
                    borderPaint
                )

                rect.inset(halfStrokeWidth, halfStrokeWidth)
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius - borderStrokeWidth,
                    borderCornerRadius - borderStrokeWidth,
                    backgroundPaint
                )
            }

            BorderType.INNER_BORDER_OVERLAP -> {
                rect.set(
                    halfStrokeWidth + borderShadowSize - borderShadowXOffset,
                    halfStrokeWidth + borderShadowSize - borderShadowYOffset,
                    width - halfStrokeWidth - borderShadowSize - borderShadowXOffset,
                    height - halfStrokeWidth - borderShadowSize - borderShadowYOffset,
                )
                borderPaint.color = outerBorderColor
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius + halfStrokeWidth,
                    borderCornerRadius + halfStrokeWidth,
                    borderPaint
                )
                rect.inset(halfStrokeWidth, halfStrokeWidth)
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius,
                    borderCornerRadius,
                    backgroundPaint
                )
                rect.inset(halfStrokeWidth, halfStrokeWidth)
                borderPaint.color = innerBorderColor
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius - halfStrokeWidth,
                    borderCornerRadius - halfStrokeWidth,
                    borderPaint
                )
            }

            BorderType.INNER_BORDER_OVERLAP_COMPLETELY_BACKGROUND -> {
                rect.set(
                    halfStrokeWidth + borderShadowSize - borderShadowXOffset,
                    halfStrokeWidth + borderShadowSize - borderShadowYOffset,
                    width - halfStrokeWidth - borderShadowSize - borderShadowXOffset,
                    height - halfStrokeWidth - borderShadowSize - borderShadowYOffset,
                )
                borderPaint.color = outerBorderColor
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius + halfStrokeWidth,
                    borderCornerRadius + halfStrokeWidth,
                    borderPaint
                )
                rect.inset(halfStrokeWidth, halfStrokeWidth)
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius,
                    borderCornerRadius,
                    backgroundPaint
                )
                rect.inset(halfStrokeWidth, halfStrokeWidth)
                borderPaint.style = Paint.Style.FILL_AND_STROKE
                borderPaint.color = innerBorderColor
                canvas.drawRoundRect(
                    rect,
                    borderCornerRadius - halfStrokeWidth,
                    borderCornerRadius - halfStrokeWidth,
                    borderPaint
                )
            }
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = MeasureSpec.getSize(widthMeasureSpec)
        val height = MeasureSpec.getSize(heightMeasureSpec)
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val expectWidth = ceil(measuredWidth + borderShadowSize * 2).toInt()
        val expectHeight = ceil(measuredHeight + borderShadowSize * 2).toInt()
        val realWidth = when (widthMode) {
            MeasureSpec.EXACTLY -> width
            MeasureSpec.AT_MOST -> min(width, expectWidth)
            else -> expectWidth
        }
        val realHeight = when (heightMode) {
            MeasureSpec.EXACTLY -> height
            MeasureSpec.AT_MOST -> min(height, expectHeight)
            else -> expectHeight
        }
        super.onMeasure(
            MeasureSpec.makeMeasureSpec(ceil(realWidth - borderShadowSize * 2).toInt(), widthMode),
            MeasureSpec.makeMeasureSpec(ceil(realHeight - borderShadowSize * 2).toInt(), heightMode)
        )
        setMeasuredDimension(realWidth, realHeight)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        for (i in 0 until childCount) {
            val child = getChildAt(0)
            val lp = child.layoutParams as LayoutParams
            val childLeft = borderShadowSize - borderShadowXOffset + lp.leftMargin
            val childTop = borderShadowSize - borderShadowYOffset + lp.topMargin
            child.layout(
                floor(childLeft).toInt(),
                floor(childTop).toInt(),
                ceil(childLeft + child.measuredWidth).toInt(),
                ceil(childTop + child.measuredHeight).toInt()
            )
        }
    }

    var onVisibilityChangedAction: ((Int) -> Unit)? = null

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (changedView === this) {
            onVisibilityChangedAction?.invoke(visibility)
        }
    }
}
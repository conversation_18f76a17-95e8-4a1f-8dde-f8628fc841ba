package com.topstack.kilonotes.pad.note.preview.otherdoc

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.DocumentManager
import com.topstack.kilonotes.base.doodle.views.doodleview.InputMode
import com.topstack.kilonotes.base.doodle.views.doodleview.OnPageChangeListener
import com.topstack.kilonotes.base.search.SearchManager
import com.topstack.kilonotes.base.search.model.DocRenderInfo
import com.topstack.kilonotes.base.search.model.DocSearchResult
import com.topstack.kilonotes.base.search.model.HighlightRect
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.OtherDocPreviewBottomSheetBinding
import com.topstack.kilonotes.notedata.NoteRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.UUID

class OtherDocPreviewFragment : BaseFragment() {
    private lateinit var binding: OtherDocPreviewBottomSheetBinding

    private val otherDocPreviewViewModel: OtherDocPreviewViewModel by viewModels()

    private var document: Document? = null

    private var pageIdResultMap: Map<UUID, List<DocSearchResult>> = emptyMap()

    private var needCenterRenderSearchResult: DocSearchResult? = null

    private var viewIndex = 0

    private var searchViewWidth = 0

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = OtherDocPreviewBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    private fun isFullScreenWidthMode(): Boolean {
        return DimensionUtil.isLandAndOneThirdScreen(context) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
            context
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (savedInstanceState != null) {
            document = otherDocPreviewViewModel.document
            viewIndex = otherDocPreviewViewModel.viewIndex
            pageIdResultMap = otherDocPreviewViewModel.pageIdResultMap
            searchViewWidth = otherDocPreviewViewModel.searchViewWidth
            needCenterRenderSearchResult = otherDocPreviewViewModel.needCenterRenderSearchResult
        }
        if (document == null) {
            dismiss()
            return
        }
        initView()
        configDoodle()
    }

    private fun configDoodle() {
        binding.doodle.doodleModeConfig.apply {
            minScale = 1F
            maxScale = minScale + 2F
            enterOtherDocPreviewMode()
        }
        binding.doodle.inputMode = InputMode.VIEW
        binding.doodle.doodleTouchLayer.onPageChangeListener = OnPageChangeListener { direction, _ ->
            when (direction) {
                OnPageChangeListener.Direction.PREVIOUS -> {
                    if (viewIndex > 0) {
                        viewIndex--
                        setPage()
                        updateNextAndPrePageButtonState()
                    }
                }

                OnPageChangeListener.Direction.NEXT -> {
                    if (viewIndex < document!!.pageCount - 1) {
                        viewIndex++
                        setPage()
                        updateNextAndPrePageButtonState()
                    }
                }

                else -> {}
            }
        }
        setPage()
    }

    private fun initView() {
        if (isFullScreenWidthMode()) {
            val doodleLayoutParams = binding.doodle.layoutParams
            doodleLayoutParams.height = resources.getDimensionPixelSize(R.dimen.dp_647)
            doodleLayoutParams.width = LayoutParams.MATCH_PARENT
            val closeLayoutParams = binding.close.layoutParams as MarginLayoutParams
            closeLayoutParams.marginEnd = resources.getDimensionPixelSize(R.dimen.dp_24)
            val nextLayoutParams = binding.nextPage.layoutParams as MarginLayoutParams
            nextLayoutParams.marginEnd = resources.getDimensionPixelSize(R.dimen.dp_24)
            val preLayoutParams = binding.prePage.layoutParams as MarginLayoutParams
            preLayoutParams.marginEnd = resources.getDimensionPixelSize(R.dimen.dp_24)
        } else {
            val doodleLayoutParams = binding.doodle.layoutParams
            doodleLayoutParams.height = resources.getDimensionPixelSize(R.dimen.dp_364)
            doodleLayoutParams.width =
                DimensionUtil.getScreenDimensions(requireContext()).widthPixels - searchViewWidth
            val closeLayoutParams = binding.close.layoutParams as MarginLayoutParams
            closeLayoutParams.marginEnd = resources.getDimensionPixelSize(R.dimen.dp_32)
            val nextLayoutParams = binding.nextPage.layoutParams as MarginLayoutParams
            nextLayoutParams.marginEnd = resources.getDimensionPixelSize(R.dimen.dp_32)
            val preLayoutParams = binding.prePage.layoutParams as MarginLayoutParams
            preLayoutParams.marginEnd = resources.getDimensionPixelSize(R.dimen.dp_32)
        }
        updateNextAndPrePageButtonState()
        binding.close.setOnClickListener(AntiShakeClickListener {
            dismiss()
        })

        binding.nextPage.setOnClickListener(AntiShakeClickListener {
            if (viewIndex < document!!.pageCount - 1) {
                viewIndex++
                setPage()
                updateNextAndPrePageButtonState()
            }
        })

        binding.prePage.setOnClickListener(AntiShakeClickListener {
            if (viewIndex > 0) {
                viewIndex--
                setPage()
                updateNextAndPrePageButtonState()
            }
        })
    }

    private fun updateNextAndPrePageButtonState() {
        val document = document ?: return
        binding.nextPage.isEnabled = viewIndex < document.pageCount - 1
        binding.prePage.isEnabled = viewIndex > 0
    }

    private fun setPage() {
        binding.doodle.doOnSizeConfirmed {
            lifecycleScope.launch(Dispatchers.Main.immediate) {
                if (!isAdded) return@launch
                val currentDoc = document ?: return@launch
                if (currentDoc.pageIds.isNotEmpty() && currentDoc.pages.isEmpty()) {
                    withContext(Dispatchers.IO) {
                        if (currentDoc.isStoredInObsoleteKiloNotesRoom()) {
                            DocumentManager.parsePagesInfo(currentDoc)
                        } else {
                            NoteRepository.inflateDocumentPages(currentDoc)
                        }
                    }
                }
                withContext(Dispatchers.IO) {
                    currentDoc.recordManager.loadRecordsInfo()
                }
                val page = currentDoc[viewIndex]
                val currentHighlightLightRectF =
                    if (needCenterRenderSearchResult?.pageId == page.uuid) {
                        needCenterRenderSearchResult?.docRenderInfo?.currentHighlightRectList?.firstOrNull()
                            ?.getTransformRectF()
                    } else {
                        null
                    }
                val docRenderInfo =
                    getCurrentPageDocRenderInfo(pageIdResultMap[page.uuid])

                binding.doodle.doodlePageLoader.loadPage(
                    currentDoc,
                    page,
                    viewIndex,
                    loadSidePage = true,
                    currentHighlightLightRectF,
                    doOnPageSet = {
                        binding.doodle.doodleStickyOnTopLayer.updateHighlightRect(docRenderInfo)
                    }
                )
            }
        }
    }

    private fun getCurrentPageDocRenderInfo(docSearchResultList: List<DocSearchResult>?): DocRenderInfo? {
        docSearchResultList ?: return null
        if (docSearchResultList.isEmpty()) {
            return null
        } else {
            val docId = docSearchResultList.first().docId
            val pageId = docSearchResultList.first().pageId
            val docRenderInfo = DocRenderInfo(
                currentHighlightRectList = emptyList(),
                allHighlightRectList = emptyList()
            )
            val highlights = mutableSetOf<HighlightRect>()
            docSearchResultList.forEach { result ->
                if (result.needInflate) {
                    NoteRepository.getDocumentFromCacheById(docId)?.let { document ->
                        if (document.pages.size != document.pageIds.size) {
                            SearchManager.inflateSearchResult(document, result)
                        }
                    }
                }
                result.docRenderInfo?.let { docRenderInfo ->
                    highlights.addAll(docRenderInfo.allHighlightRectList)
                }
            }
            docRenderInfo.allHighlightRectList = highlights.toList()
            return docRenderInfo
        }
    }

    fun initDoc(
        document: Document,
        pageUUID: UUID,
        docSearchResultList: List<DocSearchResult>,
        needCenterRenderSearchResult: DocSearchResult? = null
    ) {
        this.document = document
        viewIndex = document.getPageIndexByUUID(pageUUID)
        pageIdResultMap = docSearchResultList.groupBy { result ->
            result.pageId
        }
        this.needCenterRenderSearchResult = needCenterRenderSearchResult
    }

    fun setSearchViewWidth(width: Int) {
        searchViewWidth = width
    }

    fun updateShowPageInfo(
        document: Document,
        pageUUID: UUID,
        docSearchResultList: List<DocSearchResult>,
        needCenterRenderSearchResult: DocSearchResult? = null
    ) {
        val isDifferentDoc = document != this.document
        if (isDifferentDoc) {
            initDoc(document, pageUUID, docSearchResultList, needCenterRenderSearchResult)
        } else {
            viewIndex = document.getPageIndexByUUID(pageUUID)
            pageIdResultMap = docSearchResultList.groupBy { result ->
                result.pageId
            }
            this.needCenterRenderSearchResult = needCenterRenderSearchResult
        }
        if (isAdded) {
            updateNextAndPrePageButtonState()
            setPage()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        otherDocPreviewViewModel.document = document
        otherDocPreviewViewModel.viewIndex = viewIndex
        otherDocPreviewViewModel.pageIdResultMap = pageIdResultMap
        otherDocPreviewViewModel.searchViewWidth = searchViewWidth
        otherDocPreviewViewModel.needCenterRenderSearchResult = needCenterRenderSearchResult
    }

    fun dismiss() {
        if (!isAdded) return
        parentFragmentManager.beginTransaction()
            .setCustomAnimations(R.anim.other_doc_preview_in, R.anim.other_doc_preview_out)
            .remove(this)
            .commit()
    }

    fun show(fragmentManager: FragmentManager, tag: String?) {
        fragmentManager.beginTransaction()
            .setCustomAnimations(R.anim.other_doc_preview_in, R.anim.other_doc_preview_out)
            .replace(
                R.id.other_doc_preview_container,
                this,
                tag
            )
            .show(this)
            .commit()

    }

}
package com.topstack.kilonotes.pad.console

import android.util.ArraySet
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.doodle.views.multiselectview.Selection
import com.topstack.kilonotes.base.doodle.views.multiselectview.SelectionCategory
import com.topstack.kilonotes.base.i18n.isChineseLanguage
import com.topstack.kilonotes.infra.util.AppUtils

/**
 *
 */

fun getConsoleToastString(command: ConsoleCommand, selections: List<Selection>): String {
    return when (command.commandType) {
        ConsoleCommandType.SWITCH_TO_MODE_PEN -> AppUtils.getString(R.string.console_toast_switch_to_draw)
        ConsoleCommandType.SWITCH_TO_MODE_HIGHLIGHTER -> AppUtils.getString(R.string.console_tips_switch_to_highlighter)
        ConsoleCommandType.SWITCH_TO_MODE_PICTURE -> AppUtils.getString(R.string.console_tips_switch_to_image)
        ConsoleCommandType.SWITCH_TO_MODE_TEXT -> AppUtils.getString(R.string.console_tips_switch_to_text)
        ConsoleCommandType.SELECT -> generateToastForSelect(selections)
        ConsoleCommandType.SELECT_ALL -> AppUtils.getString(R.string.select_all)
        ConsoleCommandType.DELETE -> generateToastForDelete(selections)
        ConsoleCommandType.DELETE_ALL -> AppUtils.getString(R.string.console_toast_delete_all)
        ConsoleCommandType.REMOVE -> generateToastForRemove(selections)
        ConsoleCommandType.COPY -> if (selections.isEmpty()) "" else AppUtils.getString(R.string.copy)
        ConsoleCommandType.PASTE -> if (selections.isEmpty()) "" else AppUtils.getString(R.string.paste)
        ConsoleCommandType.UNDO -> if (command is DirectCommand && command.args != null) AppUtils.getString(
            R.string.console_toast_undo_steps,
            command.args
        ) else ""

        ConsoleCommandType.REDO -> if (command is DirectCommand && command.args != null) AppUtils.getString(
            R.string.console_toast_redo_steps,
            command.args
        ) else ""

        else -> ""
    }
}

private fun generateToastForSelect(selections: List<Selection>): String {
    var toast = ""
    toast = if (selections.isEmpty()) {
        AppUtils.getString(R.string.console_toast_no_selected_content)
    } else {
        val elements = generateElementsString(selections)

        AppUtils.getString(R.string.console_toast_selected_elements, elements)
    }
    return toast
}

private fun generateToastForDelete(selections: List<Selection>): String {
    var toast = ""
    toast = if (selections.isEmpty()) {
        AppUtils.getString(R.string.console_toast_no_deleted_content)
    } else {
        val elements = generateElementsString(selections)

        AppUtils.getString(R.string.console_toast_deleted_elements, elements)
    }
    return toast
}

private fun generateToastForRemove(selections: List<Selection>): String {
    var toast = ""
    toast = if (selections.isEmpty()) {
        AppUtils.getString(R.string.console_toast_no_deleted_content)
    } else {
        AppUtils.getString(R.string.console_instructions_scribble)
    }
    return toast
}

private fun generateElementsString(selections: List<Selection>): String {
    val elementSeparator = if (isChineseLanguage()) "、" else ", "
    val elements = buildString {
        val categories = ArraySet<SelectionCategory>()
        var hasGraph = false
        selections.forEach { selection ->
            if (selection.category == SelectionCategory.PATTERN || selection.category == SelectionCategory.GRAPH) {
                if (!hasGraph) {
                    hasGraph = true
                } else {
                    return@forEach
                }
            }
            categories.add(selection.category)
        }

        categories.forEach { category ->
            val categoryString = category.getCategoryString()
            if (categoryString.isNotEmpty()) {
                append(categoryString)
                append(elementSeparator)
            }
        }
    }.removeSuffix(elementSeparator)
    return elements
}
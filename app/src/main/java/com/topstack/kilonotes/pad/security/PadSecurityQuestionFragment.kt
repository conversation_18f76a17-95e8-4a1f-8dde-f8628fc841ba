package com.topstack.kilonotes.pad.security

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.lihang.ShadowLayout
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.security.BaseSecurityQuestionFragment
import com.topstack.kilonotes.base.util.DimensionUtil

class PadSecurityQuestionFragment : BaseSecurityQuestionFragment() {

    companion object {
        const val TAG = "PadSecurityQuestionFragment"
    }

    lateinit var backgroundMark: View
    lateinit var content: ShadowLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(
            if (DimensionUtil.isLandAndOneThirdScreen(requireContext())) {
                R.layout.dialog_security_question_one_third_vertical
            } else {
                R.layout.dialog_security_question
            }, container, false
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        backgroundMark.isClickable = true
        if (!DimensionUtil.isLandAndOneThirdScreen(requireContext())) {
            content.apply {
                val params = layoutParams
                params.width = resources.getDimensionPixelSize(R.dimen.dp_760)
                params.height =
                    if (DimensionUtil.isPortraitAndHalfScreen(requireContext()) ||
                        DimensionUtil.isPortraitAndOneThirdScreen(requireContext())
                    ) {
                        ViewGroup.LayoutParams.MATCH_PARENT
                    } else {
                        resources.getDimensionPixelSize(R.dimen.dp_924)
                    }
                layoutParams = params
            }
        }
    }

    override fun bindView(view: View) {
        super.bindView(view)
        backgroundMark = view.findViewById(R.id.background_mask)
        content = view.findViewById(R.id.content)
    }

}
package com.topstack.kilonotes.pad.console

import android.graphics.Rect
import android.graphics.RectF
import android.view.MotionEvent
import com.topstack.kilonotes.base.doodle.views.doodleview.IInternalDoodle
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.mlkit.recognition.digitalink.model.DigitalInkRawRecognize
import com.topstack.kilonotes.mlkit.recognition.digitalink.model.Source

object ConsoleCommandManager : ICommandDispatcher by Console<PERSON>ommandDispatcher {

    private const val TAG = "ConsoleCommandManager"

    private var lastCommandReceivers = mutableListOf<IConsoleCommandReceiver>()

    var commandExecuteCallback: ICommandExecuteCallback? = null

    fun sendCommand(recognize: DigitalInkRawRecognize, doodle: IInternalDoodle) {
        if (recognize.recognizeSuccess) {
            when (recognize.source) {
                Source.GESTURE -> {
                    when (recognize.recognizeRet) {
                        DigitalInkRawRecognize.RECOGNIZE_GESTURE_SCRIBBLE -> sendCommand(
                            PathCommand(
                                ConsoleCommandType.REMOVE,
                                recognize.recognizeRet,
                                recognize.inputPath
                            )
                        )
                    }
                }

                Source.ALPHABET -> {
                    val pathBounds = RectF().also { recognize.inputPath.computeBounds(it, true) }
                    val commandType = when (recognize.recognizeRet) {
                        "0", "o", "O" -> ConsoleCommandType.SELECT
                        "A" -> ConsoleCommandType.SELECT_ALL
                        "x", "X" -> {
                            val pageRect = doodle.frameTransform.transformedDrawRect
                            if (pathBounds.width() * 2 > pageRect.width() || pathBounds.height() * 2 > pageRect.height()) {
                                ConsoleCommandType.DELETE_ALL
                            } else {
                                ConsoleCommandType.DELETE
                            }
                        }

                        "c", "C" -> ConsoleCommandType.COPY
                        "v", "V" -> ConsoleCommandType.PASTE
                        "L" -> ConsoleCommandType.SWITCH_TO_MODE_HIGHLIGHTER
                        "p", "P" -> ConsoleCommandType.SWITCH_TO_MODE_PICTURE
                        "7", "T" -> ConsoleCommandType.SWITCH_TO_MODE_TEXT
                        "N" -> ConsoleCommandType.SWITCH_TO_MODE_PEN
                        else -> ConsoleCommandType.UNKNOWN
                    }
                    sendCommand(
                        PathCommand(
                            commandType,
                            recognize.recognizeRet,
                            recognize.inputPath,
                            pathBounds
                        )
                    )
                }
            }
        } else {
            sendCommand(
                PathCommand(
                    ConsoleCommandType.UNKNOWN,
                    recognize.recognizeRet,
                    recognize.inputPath
                )
            )
        }
    }

    fun sendCommand(command: ConsoleCommand) {
        if (command.commandType == ConsoleCommandType.UNKNOWN) {
            commandExecuteCallback?.onExecute(command)
            return
        }
        val commandReceivers = ConsoleCommandDispatcher.dispatchCommand(command).onEach {
            executeCommand(command, it)
        }
        lastCommandReceivers.clear()
        lastCommandReceivers.addAll(commandReceivers)
    }

    private fun executeCommand(command: ConsoleCommand, receiver: IConsoleCommandReceiver) {
        when (receiver) {
            is IConsoleInsertableObjectCommandReceiver -> {
                when (command) {
                    is PathCommand -> {
                        val selections = when (command.commandType) {
                            ConsoleCommandType.SELECT -> receiver.select(
                                command.drawPath, command.pathBounds
                            )

                            ConsoleCommandType.DELETE -> receiver.delete(
                                command.drawPath, command.pathBounds
                            )

                            ConsoleCommandType.REMOVE -> receiver.remove(
                                command.drawPath, command.pathBounds
                            )

                            ConsoleCommandType.COPY -> receiver.copy(
                                command.drawPath, command.pathBounds
                            )

                            ConsoleCommandType.PASTE -> receiver.paste(
                                command.drawPath, command.pathBounds
                            )

                            ConsoleCommandType.SELECT_ALL -> receiver.selectAll()
                            ConsoleCommandType.DELETE_ALL -> receiver.deleteAll()

                            else -> {
                                LogHelper.w(TAG, "illegal command: $command")
                                return
                            }
                        }
                        commandExecuteCallback?.onExecute(command, selections)
                    }

                    is DirectCommand -> {
                        when (command.commandType) {
                            ConsoleCommandType.UNDO -> {
                                if (command.args == null || command.args <= 0) {
                                    LogHelper.w(TAG, "illegal command: $command")
                                    return
                                } else {
                                    receiver.undo(command.args)
                                }
                            }

                            ConsoleCommandType.REDO -> {
                                if (command.args == null || command.args <= 0) {
                                    LogHelper.w(TAG, "illegal command: $command")
                                    return
                                } else {
                                    receiver.redo(command.args)
                                }
                            }

                            else -> {
                                LogHelper.w(TAG, "illegal command: $command")
                                return
                            }
                        }
                        commandExecuteCallback?.onExecute(command)
                    }
                }

            }

            is IConsoleSwitchToolCommandReceiver -> {
                when (command) {
                    is DirectCommand -> {
                        LogHelper.w(TAG, "illegal command: $command")
                        return
                    }

                    is PathCommand -> {
                        when (command.commandType) {
                            ConsoleCommandType.SWITCH_TO_MODE_PEN -> receiver.switchToToolPen()

                            ConsoleCommandType.SWITCH_TO_MODE_HIGHLIGHTER -> receiver.switchToToolHighlighter()

                            ConsoleCommandType.SWITCH_TO_MODE_PICTURE -> receiver.switchToToolImage(
                                command.drawPath, command.pathBounds
                            )

                            ConsoleCommandType.SWITCH_TO_MODE_TEXT -> receiver.switchToolText(
                                command.drawPath, command.pathBounds
                            )

                            else -> {
                                LogHelper.w(TAG, "illegal command: $command")
                                return
                            }
                        }
                    }
                }
                commandExecuteCallback?.onExecute(command)
            }
        }
    }

    fun checkIfRequestInterceptTouchEvent(event: MotionEvent, globalRect: Rect) =
        lastCommandReceivers.any { it.interceptTouchEvent(event, globalRect) }

    fun reset() {
        lastCommandReceivers.forEach { it.exitCommandMode() }
        lastCommandReceivers.clear()
    }
}
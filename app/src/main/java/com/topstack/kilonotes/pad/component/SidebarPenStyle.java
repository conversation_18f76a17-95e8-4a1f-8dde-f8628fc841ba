package com.topstack.kilonotes.pad.component;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.topstack.kilonotes.R;

public class SidebarPenStyle extends View {
    private static final int DEFAULT_HEIGHT = 10;
    private static final int DEFAULT_PEN_STYLE_COLOR = 0xFF727272;

    private int height;
    private int paintHeight;
    private int paintWidth;
    private int width;
    private final Paint painter = new Paint(Paint.ANTI_ALIAS_FLAG);

    public SidebarPenStyle(Context context) {
        super(context);
    }

    public SidebarPenStyle(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.SidebarPenStyle);
        int color = array.getColor(R.styleable.SidebarPenStyle_brushColor, DEFAULT_PEN_STYLE_COLOR);
        paintHeight = array.getDimensionPixelSize(R.styleable.SidebarPenStyle_brushHeight, DEFAULT_HEIGHT);
        paintWidth = getContext().getResources().getDimensionPixelSize(R.dimen.dp_40);
        array.recycle();
        init(color);
    }

    private void init(int color) {
        painter.setStyle(Paint.Style.STROKE);
        painter.setColor(color);
        painter.setStrokeWidth((float) paintHeight);
        painter.setStrokeCap(Paint.Cap.ROUND);
        //抗锯齿
        painter.setAntiAlias(true);
    }

    @Override
    protected void onSizeChanged(int xNew, int yNew, int xOld, int yOld) {
        super.onSizeChanged(xNew, yNew, xOld, yOld);
        width = xNew;
        height = yNew;
    }

    public void setColor(int color) {
        painter.setColor(color);
        invalidate();
    }

    public void setHeight(int height) {
        this.paintHeight = height;
        painter.setStrokeWidth(height);
        invalidate();
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawLine(
                (float) (width - paintWidth) / 2 + (float) paintHeight / 2,
                (float) height / 2,
                (float) (paintWidth + (width - paintWidth) / 2) - (float) paintHeight / 2,
                (float) height / 2,
                painter);
    }

}

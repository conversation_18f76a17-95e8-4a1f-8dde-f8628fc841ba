package com.topstack.kilonotes.pad.agreement

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.webkit.WebSettings
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.activity.BaseDialogActivity
import com.topstack.kilonotes.base.config.Preferences.agreeUserPrivacy
import com.topstack.kilonotes.base.flavor.ProductFlavor.getDomesticPrivacy
import com.topstack.kilonotes.base.flavor.ProductFlavor.getDomesticThirdParty
import com.topstack.kilonotes.base.flavor.ProductFlavor.getUserPrivacy
import com.topstack.kilonotes.base.flavor.ProductFlavor.getUserTermsUrl
import com.topstack.kilonotes.base.guide.PolicyType
import com.topstack.kilonotes.databinding.DialogPolicyBinding

class UserAgreementActivity : BaseDialogActivity() {
    private lateinit var binding: DialogPolicyBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = DialogPolicyBinding.inflate(layoutInflater)
        setContentView(binding.root)
        // reset webview url cache
        binding.policyWebView.settings.cacheMode = WebSettings.LOAD_NO_CACHE

        val type: PolicyType? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getSerializableExtra(POLICY_TYPE_INTENT_KEY, PolicyType::class.java)
        } else {
            intent.getSerializableExtra(POLICY_TYPE_INTENT_KEY) as? PolicyType
        }

        when (type) {
            PolicyType.POLICY_AGREEMENT -> {
                binding.policyTitle.setText(R.string.policy_agreement)
                binding.policyWebView.loadUrl(getUserPrivacy())
            }

            PolicyType.TERMS_OF_USER -> {
                binding.policyTitle.setText(R.string.terms_of_users_service)
                binding.policyWebView.loadUrl(getUserTermsUrl())
            }

            PolicyType.DOMESTIC_POLICY -> {
                binding.policyTitle.setText(R.string.privacy_policy_title)
                binding.policyWebView.loadUrl(getDomesticPrivacy())
            }

            PolicyType.DOMESTIC_THIRD_PARTY -> {
                binding.policyTitle.setText(R.string.third_party_title)
                binding.policyWebView.loadUrl(getDomesticThirdParty())
            }

            null -> {

            }
        }
        binding.policyWebView.overScrollMode = View.OVER_SCROLL_NEVER
        binding.policyWebView.isVerticalScrollBarEnabled = false
        val DENSITY_FOR_NORMAL_WEB_TEXT_SIZE = 2f // 网页字体在该 density 下显示正常大小
        // 根据当前设备的 density 计算 WebView 中字体缩放比例
        val webTextScale = DENSITY_FOR_NORMAL_WEB_TEXT_SIZE / resources.displayMetrics.density
        binding.policyWebView.settings.textZoom = (webTextScale * 100).toInt()
        binding.policyConfirm.setOnClickListener { v: View? -> finish() }
    }

    companion object {
        const val POLICY_TYPE_INTENT_KEY: String = "policy"
        fun openPolicyActivity(context: Context, type: PolicyType) {
            if (agreeUserPrivacy) {
                val intent = Intent(context, UserAgreementActivity::class.java)
                intent.putExtra(POLICY_TYPE_INTENT_KEY, type)
                context.startActivity(intent)
            } else {
                val uri = if (type == PolicyType.TERMS_OF_USER) {
                    Uri.parse(getUserTermsUrl())
                } else {
                    Uri.parse(getUserPrivacy())
                }
                val intent = Intent(Intent.ACTION_VIEW, uri)
                context.startActivity(intent)
            }
        }

        fun showUsersTerms(context: Context) {
            openPolicyActivity(context, PolicyType.TERMS_OF_USER)
        }

        fun showPolicyAgreement(context: Context) {
            openPolicyActivity(context, PolicyType.POLICY_AGREEMENT)
        }
    }
}

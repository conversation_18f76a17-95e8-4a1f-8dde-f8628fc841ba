package com.topstack.kilonotes.pad.component

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.PorterDuff
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.OverScrollCoordinatorRecyclerView
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PaperListItemBinding
import com.topstack.kilonotes.databinding.PaperListItemImportBinding

class PaperSelectView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : OverScrollCoordinatorRecyclerView(context, attrs, defStyleAttr) {

    private lateinit var builtinPaperAdapter: PaperAdapter
    private lateinit var importImagePaperAdapter: ImportImagePaperAdapter

    companion object {
        const val UNSELECTED_POSITION = -1
        const val IMPORT_IMAGE_POSITION = -2
    }

    fun setupHorizontal(
        initialPosition: Int,
        paperList: List<Paper>,
        isHorizontalPaper: Boolean,
        onPaperSelected: (Paper?, Int) -> Unit,
        onImportPaper: () -> Unit,
        onPaperInitSelected: (Paper?, Int) -> Unit,
    ) {
        val myItemDecoration = object : RecyclerView.ItemDecoration() {

            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State,
            ) {
                val isLandAndOneThirdScreen =
                    DimensionUtil.isLandAndOneThirdScreen(view.context)
                val isLandAndHalfScreen = DimensionUtil.isLandAndHalfScreen(view.context)
                if (parent.getChildAdapterPosition(view) == 0) {
                    if (isLandAndOneThirdScreen || isLandAndHalfScreen) {
                        outRect.left = resources.getDimensionPixelSize(R.dimen.dp_30)
                    } else {
                        outRect.left = resources.getDimensionPixelSize(R.dimen.dp_48)
                    }
                }
                if (isLandAndOneThirdScreen) {
                    outRect.right = resources.getDimensionPixelSize(R.dimen.dp_24)
                } else {
                    outRect.right = resources.getDimensionPixelSize(R.dimen.dp_48)
                }
            }
        }
        val myLayoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        setup(
            initialPosition,
            paperList,
            myItemDecoration,
            myLayoutManager,
            isHorizontalPaper,
            onPaperSelected,
            onImportPaper,
            onPaperInitSelected
        )
    }

    fun setupGrid(
        spanCount: Int,
        initialPosition: Int,
        paperList: List<Paper>,
        isHorizontalPaper: Boolean,
        onPaperSelected: (Paper?, Int) -> Unit,
        onImportPaper: () -> Unit,
        onPaperInitSelected: (Paper?, Int) -> Unit,
    ) {
        val myItemDecoration = object : RecyclerView.ItemDecoration() {
            private val horizontalInterval = context.resources.getDimensionPixelSize(R.dimen.dp_20)
            private val topInterval = context.resources.getDimensionPixelSize(R.dimen.dp_30)

            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State,
            ) {
                outRect.top = topInterval
                outRect.left = horizontalInterval
                outRect.right = horizontalInterval
            }
        }
        val myLayoutManager = GridLayoutManager(context, spanCount)
        setup(
            initialPosition,
            paperList,
            myItemDecoration,
            myLayoutManager,
            isHorizontalPaper,
            onPaperSelected,
            onImportPaper,
            onPaperInitSelected
        )
    }

    private fun setup(
        initialSelectedPosition: Int,
        paperList: List<Paper>,
        myItemDecoration: RecyclerView.ItemDecoration?,
        myLayoutManager: RecyclerView.LayoutManager,
        isHorizontalPaper: Boolean,
        onPaperSelected: (Paper?, Int) -> Unit,
        onImportPaper: () -> Unit,
        onPaperInitSelected: (Paper?, Int) -> Unit,
    ) {
        PaperAdapter(
            initialSelectedPosition,
            paperList.toMutableList(),
            isHorizontalPaper
        ) { position ->
            builtinPaperAdapter.currentSelectPosition = position
            if (builtinPaperAdapter.currentSelectPosition == UNSELECTED_POSITION
                || builtinPaperAdapter.currentSelectPosition == IMPORT_IMAGE_POSITION
            ) {
                onPaperSelected(null, 0)
            } else {
                onPaperSelected(
                    builtinPaperAdapter.paperList[builtinPaperAdapter.currentSelectPosition],
                    position
                )
            }

        }.let {
            builtinPaperAdapter = it
        }

        importImagePaperAdapter =
            ImportImagePaperAdapter(isHorizontalPaper) { onImportPaper() }.apply {
                isSelect = builtinPaperAdapter.currentSelectPosition == IMPORT_IMAGE_POSITION
            }

        overScrollRecyclerView.adapter = ConcatAdapter(
            builtinPaperAdapter,
            importImagePaperAdapter
        )
        overScrollRecyclerView.itemAnimator = null
        while (overScrollRecyclerView.itemDecorationCount > 0) {
            overScrollRecyclerView.removeItemDecorationAt(0)
        }
        if (myItemDecoration != null) {
            overScrollRecyclerView.addItemDecoration(myItemDecoration)
        }
        overScrollRecyclerView.layoutManager = myLayoutManager
        if (builtinPaperAdapter.currentSelectPosition == UNSELECTED_POSITION
            || builtinPaperAdapter.currentSelectPosition !in builtinPaperAdapter.paperList.indices
        ) {
            onPaperInitSelected(null, 0)
        } else {
            onPaperInitSelected(
                builtinPaperAdapter.paperList[builtinPaperAdapter.currentSelectPosition],
                builtinPaperAdapter.currentSelectPosition
            )
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateSelectedPosition(position: Int) {
        if (!isSetup()) return
        val currentPosition = builtinPaperAdapter.currentSelectPosition
        builtinPaperAdapter.currentSelectPosition = position
        builtinPaperAdapter.notifyItemChanged(currentPosition)
        if (position in builtinPaperAdapter.paperList.indices) {
            builtinPaperAdapter.notifyItemChanged(position)
        }
        importImagePaperAdapter.isSelect = position == IMPORT_IMAGE_POSITION
        importImagePaperAdapter.notifyDataSetChanged()
    }

    fun isSetup() = ::builtinPaperAdapter.isInitialized

    fun isHorizontalPaper() = builtinPaperAdapter.isHorizontalPaper

    @SuppressLint("NotifyDataSetChanged")
    fun updatePaperList(
        paperList: List<Paper>,
        selectedPosition: Int,
        onPaperInitSelected: (Paper?, Int) -> Unit,
    ) {
        if (!isSetup()) return
        builtinPaperAdapter.paperList.clear()
        builtinPaperAdapter.paperList.addAll(paperList)
        builtinPaperAdapter.notifyDataSetChanged()
        builtinPaperAdapter.currentSelectPosition = selectedPosition
        if (builtinPaperAdapter.currentSelectPosition == UNSELECTED_POSITION
            || builtinPaperAdapter.currentSelectPosition !in builtinPaperAdapter.paperList.indices
        ) {
            onPaperInitSelected(null, 0)
        } else {
            onPaperInitSelected(
                builtinPaperAdapter.paperList[builtinPaperAdapter.currentSelectPosition],
                builtinPaperAdapter.currentSelectPosition
            )
        }
    }
}

class PaperAdapter(
    initialSelectedPosition: Int,
    val paperList: MutableList<Paper>,
    val isHorizontalPaper: Boolean,
    private val onClick: (position: Int) -> Unit,
) : RecyclerView.Adapter<PaperAdapter.PaperHolder>() {

    var currentSelectPosition: Int = initialSelectedPosition
        set(value) {
            if (field != value) {
                notifyItemChanged(field)
                field = value
                notifyItemChanged(value)
            }
        }

    override fun getItemViewType(viewType: Int): Int {
        return viewType
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PaperHolder {
        val itemBinding =
            PaperListItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PaperHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: PaperHolder, position: Int) {
        val paper = paperList[position]
        holder.bind(
            paper,
            currentSelectPosition == position,
            isHorizontalPaper
        ) {
            onClick(position)
        }
    }

    override fun getItemCount(): Int = paperList.size

    class PaperHolder(private val binding: PaperListItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(paper: Paper, selected: Boolean, isHorizontalPaper: Boolean, onClick: () -> Unit) {
            binding.type.setText(paper.getBuiltinPaperStyleTypeNameRes())
            binding.type.isSelected = selected
            binding.selected.visibility = if (selected) View.VISIBLE else View.INVISIBLE
            binding.imageOutline.visibility = if (selected) View.VISIBLE else View.INVISIBLE
            binding.image.apply {
                val params = layoutParams
                if (isHorizontalPaper) {
                    params.width = resources.getDimensionPixelSize(R.dimen.dp_258)
                    params.height = resources.getDimensionPixelSize(R.dimen.dp_194)
                } else {
                    params.width = resources.getDimensionPixelSize(R.dimen.dp_194)
                    params.height = resources.getDimensionPixelSize(R.dimen.dp_258)
                }
                layoutParams = params
                setBackgroundColor(paper.getBuiltinBackgroundColor())
                imageTintList = ColorStateList.valueOf(paper.getBuiltinForegroundColor())
                imageTintMode = PorterDuff.Mode.SRC_IN
                Glide.with(context)
                    .load(paper.getBuiltinPaperThumbnailFile())
                    .into(this)
                setOnClickListener {
                    onClick()
                }
            }
        }
    }
}

class ImportImagePaperAdapter(
    val isHorizontalPaper: Boolean,
    private val onClick: (position: Int) -> Unit,
) : RecyclerView.Adapter<ImportImagePaperAdapter.ImportImagePaperHolder>() {

    var isSelect: Boolean = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImportImagePaperHolder {
        val itemBinding =
            PaperListItemImportBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ImportImagePaperHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: ImportImagePaperHolder, position: Int) {
        holder.bind(
            isHorizontalPaper,
            isSelect
        ) {
            onClick(position)
        }
    }

    override fun getItemCount(): Int = 1

    class ImportImagePaperHolder(private val binding: PaperListItemImportBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(isHorizontalPaper: Boolean, isSelect: Boolean, onClick: () -> Unit) {
            binding.imageBg.apply {
                val params = layoutParams
                if (isHorizontalPaper) {
                    params.width = resources.getDimensionPixelSize(R.dimen.dp_258)
                    params.height = resources.getDimensionPixelSize(R.dimen.dp_194)
                } else {
                    params.width = resources.getDimensionPixelSize(R.dimen.dp_194)
                    params.height = resources.getDimensionPixelSize(R.dimen.dp_258)
                }
                layoutParams = params
                setOnClickListener {
                    onClick()
                }
            }

            binding.selected.visibility = if (isSelect) View.VISIBLE else View.INVISIBLE
            binding.imageOutline.visibility = if (isSelect) View.VISIBLE else View.INVISIBLE
        }
    }
}
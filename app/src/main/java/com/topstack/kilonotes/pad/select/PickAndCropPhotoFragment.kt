package com.topstack.kilonotes.pad.select

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.RectF
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.doodle.utils.BitmapUtils
import com.topstack.kilonotes.base.imagecrop.CropOptions
import com.topstack.kilonotes.base.select.BUNDLE_PATH_KEY
import com.topstack.kilonotes.base.select.BUNDLE_URI_KEY
import com.topstack.kilonotes.base.select.PICK_PIC_RESULT_CODE
import com.topstack.kilonotes.databinding.FragmentPickAndCropPhotoBinding
import com.topstack.kilonotes.infra.util.LogHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.FileNotFoundException
import java.io.InputStream

class PickAndCropPhotoFragment : BaseFragment(R.layout.fragment_pick_and_crop_photo) {
    private lateinit var binding: FragmentPickAndCropPhotoBinding
    private val selectPhotoViewModel: PadSelectPhotoViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentPickAndCropPhotoBinding.bind(view)


        val uri = arguments?.getParcelable<Uri>(BUNDLE_URI_KEY)
        if (null == uri) {
            findNavController().popBackStack()
            return
        }

        val contentResolver = requireContext().contentResolver
        var imageStream: InputStream? = null
        try {
            imageStream = contentResolver.openInputStream(uri)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            LogHelper.d(defaultTag, "openInputStream error uri = $uri", e, true)
        }

        if (imageStream == null) {
            findNavController().popBackStack()
            return
        }

        var bitmap: Bitmap?
        val option = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        imageStream.use {
            BitmapFactory.decodeStream(it, null, option)
            option.inSampleSize = BitmapUtils.calculateInSampleSize(
                option,
                resources.displayMetrics.widthPixels,
                resources.displayMetrics.heightPixels
            )
            option.inJustDecodeBounds = false
        }

        contentResolver.openInputStream(uri).use {
            bitmap = BitmapFactory.decodeStream(it, null, option)
        }

        val degree = BitmapUtils.getBitmapDegree(requireContext(), uri)
        if (degree == 90 || degree == 180 || degree == 270) {
            bitmap = BitmapUtils.rotate(bitmap, degree)
        }

        if (bitmap == null) {
            findNavController().popBackStack()
            return
        }
        val cropOptions = CropOptions().apply {
            maskColor = resources.getColor(R.color.white_60)
            adjustableAreaMaskColor = maskColor
            val radius = resources.getDimension(R.dimen.dp_24)
            cornerRadius = CropOptions.CornerRadius(0f, 0f, radius, radius)
        }
        if (selectPhotoViewModel.needFixRatio) {
            val fixedRatio = if (selectPhotoViewModel.isVertical) {
                450f / 600f
            } else {
                600f / 450f
            }

            binding.cropView.setCropOptions(cropOptions)
            binding.cropView.enterFixRatioMode(fixedRatio)
        } else {
            cropOptions.cropAreaOffset = RectF(0f, 0f, 0f, resources.getDimension(R.dimen.dp_124))
            binding.cropView.setCropOptions(cropOptions)
            binding.cropView.enterSquareShapeCropMode()
        }
        binding.cropView.setSourceBitmap(bitmap!!)

        binding.back.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.useTv.setOnClickListener {
            lifecycleScope.launch(Dispatchers.IO) {
                val imageFilePath = binding.cropView.getCroppedImageFile()?.absolutePath ?: ""
                withContext(Dispatchers.Main) {
                    val activity = requireActivity()
                    val intent = activity.intent
                    intent.putExtra(BUNDLE_PATH_KEY, imageFilePath)
                    activity.setResult(PICK_PIC_RESULT_CODE, intent)
                    activity.finish()
                }
            }
        }
    }
}
package com.topstack.kilonotes.pad.guide

import android.animation.ValueAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadFragmentSecondGuidePageBinding

class PadSecondGuidePageFragment : Fragment() {
    private lateinit var binding: PadFragmentSecondGuidePageBinding
    private var viewCreated = false
    private val sliceTextMarginTop = 150

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = PadFragmentSecondGuidePageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewCreated = true
        binding.sliceText.text = binding.sliceText.text.replace(Regex("\n"), " ")

        if (DimensionUtil.isLandAndOneThirdScreen(requireContext()) ||
            DimensionUtil.isLandAndHalfScreen(requireContext()) ||
            DimensionUtil.isLandTwoThirdScreen(requireContext()) ||
            DimensionUtil.isLandAndFullScreen(requireContext())
        ) {
            binding.sliceText.setMargins(0, sliceTextMarginTop, 0, 0)
        }
    }

    override fun onResume() {
        super.onResume()
        startAnimation()
    }

    fun sliceAnimation(valueAnimator: ValueAnimator, values: Float, offset: Float) {
        valueAnimator.addUpdateListener {
            if (viewCreated) {

                binding.apply {
                    sliceText.apply {
                        scaleX = 1 - values * 0.8f
                        scaleY = 1 - values * 0.8f
                        alpha = 1 - values * 0.8f
                    }
                    drawTools.apply {
                        scaleX = 1 - values * 1.5f
                        scaleY = 1 - values * 1.5f
                        alpha = 1 - values * 1.5f
                        translationX = offset
                    }
                    handbook.apply {
                        translationX = -offset * 0.4f
                        alpha = 1 - values * 0.8f
                    }
                    templateBlue.apply {
                        translationX = -offset * 0.4f
                        alpha = 1 - values * 0.8f
                    }
                    templateWhite.apply {
                        translationX = -offset * 0.3f
                        alpha = 1 - values * 0.8f
                    }
                    templatePurple.apply {
                        translationX = -offset * 0.6f
                        alpha = 1 - values * 0.8f
                    }
                    textTools.apply {
                        translationX = -offset * 0.6f
                        alpha = 1 - values * 0.8f
                    }
                }
            }
        }
    }

    fun sliceBackAnimation(valueAnimator: ValueAnimator, values: Float) {
        valueAnimator.addUpdateListener {
            if (viewCreated) {
                binding.sliceText.apply {
                    scaleX = 0.2f + values * 0.8f
                    scaleY = 0.2f + values * 0.8f
                    alpha = 0.2f + values * 0.8f
                }
            }
        }
    }

    fun startAnimation() {
        if (viewCreated) {
            binding.root.transitionToEnd()
        }
    }

    fun setProgress(progress: Float) {
        if (viewCreated) {
            binding.root.progress = progress
        }
    }
}
package com.topstack.kilonotes.pad.console

import android.graphics.Path
import android.graphics.RectF
import com.topstack.kilonotes.base.doodle.model.InsertableObject

sealed class ConsoleCommand(val commandType: ConsoleCommandType) {
    override fun toString(): String {
        return "commandType = $commandType"
    }
}

class PathCommand(
    commandType: ConsoleCommandType,
    private val recognizeResult: String,
    val drawPath: Path,
    val pathBounds: RectF = RectF().also { drawPath.computeBounds(it, true) }
) : ConsoleCommand(commandType) {
    override fun toString(): String {
        return "commandType = $commandType, recognizeResult = $recognizeResult, pathBounds = $pathBounds"
    }
}

class DirectCommand(commandType: ConsoleCommandType, val args: Int? = null) :
    ConsoleCommand(commandType) {
    override fun toString(): String {
        return "commandType = $commandType, args = $args"
    }
}
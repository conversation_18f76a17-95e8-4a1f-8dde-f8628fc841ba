package com.topstack.kilonotes.pad.agreement

import android.graphics.Rect
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.activity.BaseDialogActivity
import com.topstack.kilonotes.databinding.DialogDomesticPolicyBinding
import com.topstack.kilonotes.pad.agreement.adapter.DomesticDetailedInfoAdapter
import com.topstack.kilonotes.pad.agreement.model.CollectInfoUtil

class DomesticInfoCollectActivity : BaseDialogActivity() {
    private lateinit var binding: DialogDomesticPolicyBinding
    private val collectInfoUtil = CollectInfoUtil()

    private var domesticDetailedInfoAdapter: DomesticDetailedInfoAdapter? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogDomesticPolicyBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.detailedInfoList.layoutManager = LinearLayoutManager(this)
        binding.detailedInfoList.addItemDecoration(object : ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                outRect.top = resources.getDimensionPixelSize(R.dimen.dp_40)
            }
        })

        binding.userPersonalInfoConstrain.setOnClickListener {
            binding.collectInfoGroup.visibility = View.INVISIBLE
            binding.detailedInfoGroup.visibility = View.VISIBLE
            domesticDetailedInfoAdapter = DomesticDetailedInfoAdapter(
                collectInfoUtil.getPersonalDataInfos()
            )
            binding.detailedInfoList.adapter = domesticDetailedInfoAdapter
        }

        binding.useProcedureInfoConstrain.setOnClickListener {
            binding.collectInfoGroup.visibility = View.INVISIBLE
            binding.detailedInfoGroup.visibility = View.VISIBLE
            domesticDetailedInfoAdapter = DomesticDetailedInfoAdapter(
                collectInfoUtil.getUseProcedureInfos()
            )
            binding.detailedInfoList.adapter = domesticDetailedInfoAdapter

        }
        binding.deviceInfoConstrain.setOnClickListener {
            binding.collectInfoGroup.visibility = View.INVISIBLE
            binding.detailedInfoGroup.visibility = View.VISIBLE
            domesticDetailedInfoAdapter = DomesticDetailedInfoAdapter(
                collectInfoUtil.getDeviceInfoInfos()
            )
            binding.detailedInfoList.adapter = domesticDetailedInfoAdapter

        }
        binding.appInfoConstrain.setOnClickListener {
            binding.collectInfoGroup.visibility = View.INVISIBLE
            binding.detailedInfoGroup.visibility = View.VISIBLE
            domesticDetailedInfoAdapter = DomesticDetailedInfoAdapter(
                collectInfoUtil.getAppInfoInfos()
            )
            binding.detailedInfoList.adapter = domesticDetailedInfoAdapter
        }
        binding.back.setOnClickListener {
            binding.collectInfoGroup.visibility = View.VISIBLE
            binding.detailedInfoGroup.visibility = View.INVISIBLE
        }


        binding.closeBtn.setOnClickListener {
            finish()
        }

        binding.sevenDay.isSelected = true

        binding.sevenDay.setOnClickListener {
            binding.sevenDay.isSelected = true
            binding.thirtyDay.isSelected = false
            binding.oneYear.isSelected = false
        }

        binding.thirtyDay.setOnClickListener {
            binding.sevenDay.isSelected = false
            binding.thirtyDay.isSelected = true
            binding.oneYear.isSelected = false
        }

        binding.oneYear.setOnClickListener {
            binding.sevenDay.isSelected = false
            binding.thirtyDay.isSelected = false
            binding.oneYear.isSelected = true
        }

    }

    override fun computeHeight(heightPixels: Int): Int {
        return resources.getDimensionPixelOffset(R.dimen.dp_864)
    }

    override fun computeWidth(widthPixels: Int): Int {
        return resources.getDimensionPixelOffset(R.dimen.dp_700)
    }
}
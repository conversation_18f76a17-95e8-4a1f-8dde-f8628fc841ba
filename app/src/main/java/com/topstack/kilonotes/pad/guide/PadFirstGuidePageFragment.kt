package com.topstack.kilonotes.pad.guide

import android.animation.ValueAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadFragmentFirstGuidePageBinding

class PadFirstGuidePageFragment : Fragment() {
    private lateinit var binding: PadFragmentFirstGuidePageBinding
    private var viewCreated = false
    private val sliceTextMarginTop = 150

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = PadFragmentFirstGuidePageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewCreated = true
        binding.sliceText.text = binding.sliceText.text.replace(Regex("\n"), " ")

        if (DimensionUtil.isLandAndOneThirdScreen(requireContext()) ||
            DimensionUtil.isLandAndHalfScreen(requireContext()) ||
            DimensionUtil.isLandTwoThirdScreen(requireContext()) ||
            DimensionUtil.isLandAndFullScreen(requireContext())
        ) {
            binding.sliceText.setMargins(0, sliceTextMarginTop, 0, 0)
        }
    }

    override fun onResume() {
        super.onResume()
        startAnimation()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewCreated = false
    }

    fun startAnimation() {
        if (viewCreated) {
            binding.root.transitionToEnd()
        }
    }

    fun sliceAnimation(valueAnimator: ValueAnimator, values: Float, offset: Float) {
        valueAnimator.apply {
            addUpdateListener {
                if (viewCreated) {

                    binding.apply {
                        sliceText.apply {
                            scaleX = 1 - values * 0.8f
                            scaleY = 1 - values * 0.8f
                            alpha = 1 - values * 0.8f
                        }
                        tools.apply {
                            scaleX = 1 - values * 1.5f
                            scaleY = 1 - values * 1.5f
                            alpha = 1 - values * 1.5f
                            translationX = offset
                        }
                        handbook.apply {
                            translationX = -offset * 0.4f
                            alpha = 1 - values * 0.8f
                        }
                        templateRed.apply {
                            translationX = -offset * 0.5f
                            alpha = 1 - values * 0.8f
                        }
                        templateGreen.apply {
                            translationX = -offset * 0.4f
                            alpha = 1 - values * 0.8f
                        }
                        templateTravelSummary.apply {
                            translationX = -offset * 0.7f
                            alpha = 1 - values * 0.8f
                        }
                    }
                }
            }
        }
    }
}
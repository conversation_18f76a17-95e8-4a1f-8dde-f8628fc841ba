package com.topstack.kilonotes.pad.setting

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.View.MeasureSpec
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.core.view.marginLeft
import androidx.core.view.marginRight
import androidx.core.view.marginTop
import com.topstack.kilonotes.BuildConfig
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.FeedbackDialog
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.creatorCommunity.config.CreatorCommunityConfig
import com.topstack.kilonotes.base.flavor.Channel
import com.topstack.kilonotes.base.flavor.ProductFlavor
import com.topstack.kilonotes.base.guide.PolicyType
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.track.event.DataBackUpEvent
import com.topstack.kilonotes.base.track.event.SettingEvent
import com.topstack.kilonotes.base.track.event.SyncBaiduEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PopupSettingBinding
import com.topstack.kilonotes.infra.config.BUILD_TYPE_TEMPLATE
import com.topstack.kilonotes.pad.agreement.DomesticInfoCollectActivity
import com.topstack.kilonotes.pad.agreement.UserAgreementActivity

/**
 * <AUTHOR>
 */
class SettingWindow(val context: Context, val isHiddenSpace: Boolean) : PopupWindow() {

    val binding: PopupSettingBinding by lazy {
        PopupSettingBinding.inflate(LayoutInflater.from(context))
    }

    var onMenuClickAction: ((type: MenuType) -> Unit)? = null

    init {
        if (ProductFlavor.isPlayChannel()) {
            binding.privacyPolicyContainer.isVisible = false
            binding.thirdPartyContainer.isVisible = false
            binding.infoCollectContainer.isVisible = false
        }
        binding.privacyPolicyContainer.setOnClickListener {
            val intent = Intent(context, UserAgreementActivity::class.java)
            intent.putExtra(
                UserAgreementActivity.POLICY_TYPE_INTENT_KEY,
                PolicyType.DOMESTIC_POLICY
            )
            context.startActivity(intent)
        }

        binding.thirdPartyContainer.setOnClickListener {
            val intent = Intent(context, UserAgreementActivity::class.java)
            intent.putExtra(
                UserAgreementActivity.POLICY_TYPE_INTENT_KEY,
                PolicyType.DOMESTIC_THIRD_PARTY
            )
            context.startActivity(intent)
        }

        binding.infoCollectContainer.setOnClickListener {
            val intent = Intent(context, DomesticInfoCollectActivity::class.java)
            context.startActivity(intent)
        }

        contentView = binding.root
        width = ViewGroup.LayoutParams.WRAP_CONTENT
        isFocusable = true
        isOutsideTouchable = true
        binding.feedbackDesc.text = context.getText(R.string.feedback_email)
        binding.aboutBg.isVisible = UserUsageConfig.isNeedShowHiddenSpaceGuide

        binding.myBackupSpaceContainer.setOnClickListener {
            onMenuClickAction?.invoke(MenuType.BACKUP_SPACE)
        }

        binding.aiPointsContainer.setOnClickListener {
            onMenuClickAction?.invoke(MenuType.AI_POINTS)
        }

        binding.backupContainer.setOnClickListener {
            DataBackUpEvent.sendDataBackUpClickEvent()
            onMenuClickAction?.invoke(MenuType.BACKUP)
        }

        binding.syncContainer.setOnClickListener {
            SyncBaiduEvent.sendSettingDialogSyncIconClickEvent()
            onMenuClickAction?.invoke(MenuType.SYNC)
        }

        binding.combineDocumentsContainer.setOnClickListener {
            onMenuClickAction?.invoke(MenuType.COMBINE_DOCUMENTS)
        }

        binding.aboutContainer.setOnClickListener {
            onMenuClickAction?.invoke(MenuType.ABOUT)
        }

        binding.feedbackDialogContainer.setOnClickListener {
            val dialogFragment = FeedbackDialog()
            dialogFragment.show(
                (context as AppCompatActivity).supportFragmentManager,
                "feedback_dialog"
            )
        }

        binding.creatorCommunityContainer.isVisible = CreatorCommunityConfig.isOpenByServer

        binding.creatorCommunityContainer.setOnClickListener {
            onMenuClickAction?.invoke(MenuType.CREATOR_COMMUNITY)
        }

        binding.feedbackContainer.setOnLongClickListener {
            onMenuClickAction?.invoke(MenuType.FEEDBACK)
            return@setOnLongClickListener true
        }

        binding.cancelContainer.setOnClickListener {
            onMenuClickAction?.invoke(MenuType.CANCEL)
        }

        binding.redeemCodeContainer.setOnClickListener {
            onMenuClickAction?.invoke(MenuType.REDEEM_CODE)
        }

        if (BuildConfig.FLAVOR_CHANNEL == Channel.PLAY.channel) {
            binding.redeemCodeContainer.visibility = View.GONE
        }

        /**
         * 初始化按钮状态，只有点击时才会更改状态值
         */
        binding.imageOptimizationSwitch.isChecked = UserUsageConfig.isUseImageCompress
        binding.imageOptimizationSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            UserUsageConfig.isUseImageCompress = isChecked
            if (!UserUsageConfig.isUseImageCompress) {
                SettingEvent.sendImagePerformanceCloseEvent()
            }
        }


        if (BuildConfig.BUILD_TYPE.equals(BUILD_TYPE_TEMPLATE, ignoreCase = true)) {
            binding.templateTool.run {
                isVisible = true
                setOnClickListener {
                    onMenuClickAction?.invoke(MenuType.TEMPLATE_TOOL)
                }
            }
        }

        if (isHiddenSpace) {
            binding.run {
                setting.visibility = View.GONE
                aboutContainer.visibility = View.GONE
                combineDocumentsContainer.visibility = View.GONE
                feedbackContainer.visibility = View.GONE
                cancelContainer.visibility = View.GONE
                redeemCodeContainer.visibility = View.GONE
                imageOptimizationContainer.visibility = View.GONE
                syncContainer.setMargins(
                    syncContainer.marginLeft,
                    syncContainer.marginTop,
                    syncContainer.marginRight,
                    context.resources.getDimensionPixelSize(R.dimen.dp_20)
                )
            }
        }

        binding.backupIconJumpIv.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        binding.aboutIconJumpIv.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        binding.cancelIconJumpIv.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        binding.syncIconJumpIv.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        binding.redeemCodeIconJumpIv.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        binding.combineDocumentsIconJumpIv.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        binding.templateIconJumpIv.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
    }

    fun setMenuClickAction(action: (type: MenuType) -> Unit) {
        onMenuClickAction = action
    }

    fun updateAboutBgView() {
        binding.aboutBg.isVisible = UserUsageConfig.isNeedShowHiddenSpaceGuide
    }

    override fun setOnDismissListener(onDismissListener: OnDismissListener?) {
        super.setOnDismissListener(onDismissListener)
    }

    override fun showAtLocation(parent: View?, gravity: Int, x: Int, y: Int) {
        binding.root.measure(
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
        )
        val screenDimensions = DimensionUtil.getScreenDimensions(context)
        height =
            if (binding.root.measuredHeight >= screenDimensions.heightPixels - context.resources.getDimensionPixelSize(
                    R.dimen.dp_150
                )
            ) {
                screenDimensions.heightPixels - context.resources.getDimensionPixelSize(R.dimen.dp_150)
            } else {
                ViewGroup.LayoutParams.WRAP_CONTENT
            }
        width =
            if (binding.root.measuredWidth < context.resources.getDimensionPixelSize(R.dimen.dp_240)) {
                context.resources.getDimensionPixelSize(R.dimen.dp_380)
            } else {
                ViewGroup.LayoutParams.WRAP_CONTENT
            }
        super.showAtLocation(parent, gravity, x, y)
    }

    enum class MenuType {
        BACKUP_SPACE,
        AI_POINTS,
        BACKUP,
        ABOUT,
        FEEDBACK,
        CREATOR_COMMUNITY,
        CANCEL,
        TEMPLATE_TOOL,
        REDEEM_CODE,
        SYNC,
        COMBINE_DOCUMENTS
    }

}
package com.topstack.kilonotes.pad.component

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.TextViewCompat
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.limit.CreateNoteLimit
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadFreeNoteCountLayoutBinding
import com.topstack.kilonotes.databinding.PadFreeNoteCountLayoutOneThirdScreenBinding
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.pad.promotion.checkin.GainNoteLimitsPromotionManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FreeNoteCountLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val unlock: AppCompatTextView
    private val askLater: AppCompatTextView
    private val freeNoteCountTitle: AppCompatTextView

    private var onUnlockClick: (() -> Unit)? = null
    private var onAskLaterClick: (() -> Unit)? = null

    init {
        if (DimensionUtil.isLandAndOneThirdScreen(context) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                context
            )
        ) {
            PadFreeNoteCountLayoutOneThirdScreenBinding.inflate(
                LayoutInflater.from(context),
                this,
                true
            )
        } else {
            PadFreeNoteCountLayoutBinding.inflate(LayoutInflater.from(context), this, true)
        }

        unlock = findViewById(R.id.unlock)
        askLater = findViewById(R.id.ask_later)
        freeNoteCountTitle = findViewById(R.id.free_note_count_title)

        TextViewCompat.setAutoSizeTextTypeWithDefaults(
            unlock,
            TextViewCompat.AUTO_SIZE_TEXT_TYPE_UNIFORM
        )
        TextViewCompat.setAutoSizeTextTypeUniformWithConfiguration(
            unlock, resources.getDimensionPixelSize(
                R.dimen.sp_20
            ), resources.getDimensionPixelSize(
                R.dimen.sp_30
            ), resources.getDimensionPixelSize(
                R.dimen.sp_1
            ), TypedValue.COMPLEX_UNIT_PX
        )

        unlock.setOnClickListener {
            onUnlockClick?.invoke()
        }

        askLater.setOnClickListener {
            onAskLaterClick?.invoke()
        }
        if (CreateNoteLimit.currentFreeCreatedNoteNumber == CreateNoteLimit.currentMaxFreeCreatedNoteNumber) {
            changeFreeNoteCountTitle(AppUtils.getString(R.string.free_note_title_used_up))
        } else {
            val bonusLimit =
                CreateNoteLimit.currentMaxFreeCreatedNoteNumber - CreateNoteLimit.originalMaxFreeCreatedNoteNumber
            changeFreeNoteCountTitle(
                AppUtils.getString(
                    R.string.free_note_title_not_used_up,
                    CreateNoteLimit.currentFreeCreatedNoteNumber,
                    CreateNoteLimit.currentMaxFreeCreatedNoteNumber
                )
            )
            GlobalScope.launch(Dispatchers.IO) {
                val validDays = GainNoteLimitsPromotionManager.getNoteLimitsValidDays()
                withContext(Dispatchers.Main) {
                    changeFreeNoteCountTitle(
                        AppUtils.getString(
                            R.string.free_note_title_not_used_up,
                            CreateNoteLimit.currentFreeCreatedNoteNumber,
                            CreateNoteLimit.currentMaxFreeCreatedNoteNumber
                        ) + if (bonusLimit > 0 && validDays > 0) {
                            AppUtils.getString(
                                R.string.free_note_title_check_in_quota,
                                bonusLimit
                            )
                        } else {
                            ""
                        }
                    )
                }
            }
        }
    }

    fun setOnUnlockClick(action: () -> Unit) {
        onUnlockClick = action
    }

    fun setOnAskLaterClick(action: () -> Unit) {
        onAskLaterClick = action
    }

    fun changeFreeNoteCountTitle(title: String) {
        freeNoteCountTitle.setText(title)
    }
}
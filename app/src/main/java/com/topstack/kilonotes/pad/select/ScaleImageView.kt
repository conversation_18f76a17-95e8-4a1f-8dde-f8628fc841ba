package com.topstack.kilonotes.pad.select

import android.content.Context
import android.graphics.Matrix
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatImageView
import com.topstack.kilonotes.base.imagecrop.ImageGestureDetector

class ScaleImageView @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attributeSet, defStyleAttr) {


    private val imageGestureListener = object : ImageGestureDetector.OnImageGestureListener {
        override fun onScroll(distanceX: Float, distanceY: Float): Boolean {
            var scale = getScale()
            if (scale > initScale) {
                scaleMatrix.mapRect(desRf, srcRf);

                var disX = distanceX
                var disY = distanceY

                val moveTop: Boolean = distanceY > 0
                val targetTop: Float = desRf.top - distanceY
                val targetBottom: Float = desRf.bottom - distanceY

                val moveLeft: Boolean = distanceX > 0
                val targetLeft: Float = desRf.left - distanceX
                val targetRight: Float = desRf.right - distanceX

                val width = width
                val height = height

                if (!moveLeft && targetLeft > 0) {
                    disX = if (desRf.left > 0) {
                        0f
                    } else {
                        desRf.left
                    }
                }

                if (moveLeft && targetRight < width) {
                    disX = if (desRf.right < width) {
                        0f
                    } else {
                        desRf.right - width;
                    }
                }

                if (!moveTop && targetTop > 0) {
                    disY = if (desRf.top > 0) {
                        0f
                    } else {
                        desRf.top

                    }
                }

                if (moveTop && targetBottom < height) {
                    disY = if (desRf.bottom < height) {
                        0f
                    } else {
                        desRf.bottom - height
                    }
                }


                scaleMatrix.postTranslate(-disX, -disY)
                imageMatrix = scaleMatrix
            }
            return true;
        }

        override fun onScale(scaleFactor: Float, focusX: Float, focusY: Float): Boolean {
            var scale = getScale()
            var scaleFactor = scaleFactor
            if (drawable == null)
                return true
            if ((scale < maxScale && scaleFactor > 1.0f)
                || (scale > initScale && scaleFactor < 1.0f)
            ) {
                if (scaleFactor * scale < initScale)
                    scaleFactor = initScale / scale
                if (scaleFactor * scale > maxScale)
                    scaleFactor = maxScale / scale
                //设置缩放比例
                scaleMatrix.postScale(
                    scaleFactor, scaleFactor,
                    focusX, focusY
                )
                checkBorderAndCenterWhenScale()
                imageMatrix = scaleMatrix
            }
            return true
        }

        override fun onEnd() {
        }
    }

    private val maxScale: Float = 4.0f
    private var initScale: Float = 1.0f
    private var imageGestureDetector: ImageGestureDetector? = null
    private var scaleMatrix: Matrix = Matrix()
    private var desRf: RectF = RectF()
    private var srcRf: RectF = RectF()
    private var matrixValue: FloatArray = FloatArray(9)


    init {
        scaleType = ScaleType.MATRIX
        imageGestureDetector = ImageGestureDetector(context, imageGestureListener)
    }


    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event != null) {
            imageGestureDetector?.onTouchEvent(event)
        }
        return true
    }


    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        if (drawable != null) {
            var imageWidth: Int = drawable.intrinsicWidth
            var imageHeight: Int = drawable.intrinsicHeight

            var height: Int = this.height
            var width: Int = this.width

            srcRf = RectF(0F, 0F, imageWidth * 1.0f, imageHeight * 1.0f)

            val scaleY: Float = 1f * height / imageHeight
            val scaleX: Float = 1f * width / imageWidth
            val scale: Float = Math.min(scaleX, scaleY)
            //innerDrawable 在缩放居中显示
            scaleMatrix.reset()
            scaleMatrix.postScale(scale, scale)
            scaleMatrix.mapRect(desRf, srcRf)

            val translateX: Float = Math.abs((1f * width - desRf.width())) / 2
            val translateY: Float = Math.abs((1f * height - desRf.height())) / 2
            scaleMatrix.postTranslate(translateX, translateY)

            scaleMatrix.mapRect(desRf, srcRf)
            initScale = scale
            imageMatrix = scaleMatrix
        }
    }

    fun checkBorderAndCenterWhenScale() {

        var matrix = scaleMatrix

        if (drawable != null) {
            matrix.mapRect(desRf, srcRf)
        }
        var deltaX = 0f
        var deltaY = 0f
        var width = this.width
        var height = this.height
        // 如果宽或高大于屏幕，则控制范围
        if (desRf.width() >= width) {
            if (desRf.left > 0) {
                deltaX = -desRf.left
            }
            if (desRf.right < width) {
                deltaX = width - desRf.right
            }
        }
        if (desRf.height() >= height) {
            if (desRf.top > 0) {
                deltaY = -desRf.top
            }
            if (desRf.bottom < height) {
                deltaY = height - desRf.bottom
            }
        }
        // 如果宽或高小于屏幕，则让其居中
        if (desRf.width() < width) {
            deltaX = width * 0.5f - desRf.right + 0.5f * desRf.width()
        }
        if (desRf.height() < height) {
            deltaY = height * 0.5f - desRf.bottom + 0.5f * desRf.height()
        }
        scaleMatrix.postTranslate(deltaX, deltaY)
    }

    fun getScale(): Float {
        scaleMatrix.getValues(matrixValue)
        return matrixValue[Matrix.MSCALE_X]
    }
}


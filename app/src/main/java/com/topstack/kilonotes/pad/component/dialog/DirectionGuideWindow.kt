package com.topstack.kilonotes.pad.component.dialog

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.BubbleLayout
import com.topstack.kilonotes.databinding.WindowCreateNotebookGuideBinding

open class DirectionGuideWindow(
    val context: Context,
    var knowClicked: (() -> Unit)? = null
) : PopupWindow() {

    private var rootView: View

    companion object {
        const val BRAND = "HUAWEI"
    }


    private val binding: WindowCreateNotebookGuideBinding by lazy {
        WindowCreateNotebookGuideBinding.inflate(LayoutInflater.from(context))
    }

    init {
        rootView = binding.root
        contentView = rootView
        width = context.resources.getDimensionPixelSize(R.dimen.dp_294)
        height = context.resources.getDimensionPixelSize(R.dimen.dp_148)
        isOutsideTouchable = true
        isFocusable = true
        //禁用PopupWindow默认动画
        animationStyle = 0
        if (KiloApp.isLayoutRtl) {
            binding.bubble.setBubbleOrientation(BubbleLayout.BubbleOrientation.RIGHT)
        } else {
            binding.bubble.setBubbleOrientation(BubbleLayout.BubbleOrientation.LEFT)
        }
        initView()
    }

    private fun initView() {
        binding.know.setOnClickListener { view ->
            knowClicked?.invoke()
        }
    }

    fun show(anchor: View) {
        anchor.post {
            if (anchor.windowToken == null) return@post
            val context = anchor.context ?: return@post
            val viewLocation = IntArray(2)
            anchor.getLocationInWindow(viewLocation)
            val offset = viewLocation[1] + anchor.height / 2f
            binding.root.run {
                setBubbleLegOffset(offset)
            }
            showAtLocation(
                anchor,
                Gravity.TOP or Gravity.START,
                if (KiloApp.isLayoutRtl) (viewLocation[0] - width - context.resources.getDimension(
                    R.dimen.dp_10
                )).toInt() else (viewLocation[0] + anchor.width + context.resources.getDimension(R.dimen.dp_10)).toInt(),
                (viewLocation[1] - height / 2)
            )
        }
    }
}
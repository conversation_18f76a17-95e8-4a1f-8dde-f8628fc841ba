package com.topstack.kilonotes.pad.component.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.marginBottom
import androidx.core.view.marginLeft
import androidx.core.view.marginTop
import com.bumptech.glide.Glide
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.BaseDialogFragment
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadHiddenSpaceNoticeDialogBinding

class PadHiddenSpaceNoticeDialog : BaseDialogFragment() {

    companion object {
        const val TAG = "PadHiddenSpaceNoticeDialog"
    }

    private lateinit var binding: PadHiddenSpaceNoticeDialogBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = PadHiddenSpaceNoticeDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initListener()
    }

    override fun onStart() {
        super.onStart()

        val layoutWidth =
            if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext()) || DimensionUtil.isLandAndOneThirdScreen(
                    requireContext()
                )
            ) {
                ViewGroup.LayoutParams.MATCH_PARENT
            } else {
                resources.getDimension(R.dimen.dp_700).toInt()
            }


        val layoutHeight =
            if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext()) || DimensionUtil.isLandAndOneThirdScreen(
                    requireContext()
                ) || DimensionUtil.isPortraitAndHalfScreen(requireContext())
            ) {
                ViewGroup.LayoutParams.MATCH_PARENT
            } else {
                resources.getDimension(R.dimen.dp_935).toInt()
            }

        dialog?.window?.apply {
            setLayout(
                layoutWidth,
                layoutHeight
            )
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }
    }

    private fun initView() {
        Glide.with(requireContext())
            .load(R.drawable.pad_hidden_space_notice_guide_banner)
            .into(binding.guideImage)

        val layoutBackground =
            if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext()) || DimensionUtil.isLandAndOneThirdScreen(
                    requireContext()
                )
            ) {
                ColorDrawable(Color.WHITE)
            } else {
                resources.getDrawable(R.drawable.pad_hidden_space_dialog_background, null)
            }
        binding.root.background = layoutBackground

        if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext()) || DimensionUtil.isLandAndOneThirdScreen(
                requireContext()
            )
        ) {
            binding.guideImage.setMargins(
                binding.guideImage.marginLeft,
                binding.guideImage.marginTop,
                resources.getDimension(R.dimen.dp_2).toInt(),
                binding.guideImage.marginBottom
            )
        }

        val messageTips = resources.getString(R.string.hidden_space_notice_tips)
        val messageDataBackup =
            resources.getString(R.string.hidden_space_notice_data_backup_message)
        val colorSpan = ForegroundColorSpan(
            resources.getColor(
                R.color.hidden_space_notice_kind_tips_color,
                null
            )
        )
        val char = messageTips[messageTips.length - 1]
        val message = messageTips + messageDataBackup
        val index = message.indexOf(char)
        val spannableMessage = SpannableString(message)
        if (index > 0) {
            spannableMessage.setSpan(
                colorSpan,
                0,
                index,
                SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        binding.messageKindTips.text = spannableMessage

    }


    private fun initListener() {
        binding.close.setOnClickListener {
            dismiss()
        }
    }
}
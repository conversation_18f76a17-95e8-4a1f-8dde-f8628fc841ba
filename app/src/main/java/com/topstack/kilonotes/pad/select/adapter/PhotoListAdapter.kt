package com.topstack.kilonotes.pad.select.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.topstack.kilonotes.R
import com.topstack.kilonotes.databinding.FragmentPhotoListRvItemBinding
import com.topstack.kilonotes.base.imagefetch.model.Image

class PhotoListAdapter(
    private val inputImageList: List<Image>,
    private val context: Context,
    private val onClick: (img: Image) -> Unit
) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val imageList = inputImageList.toList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view = FragmentPhotoListRvItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PhotoListViewHolder(view)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is PhotoListViewHolder) {
            val imageView = holder.imageView
            val curImg = imageList[position]
            Glide.with(context)
                .load(curImg.uri)
                .placeholder(R.drawable.note_main_sidebar_pic_default)
                .error(R.drawable.note_main_sidebar_pic_error)
                .into(imageView)
            imageView.setOnClickListener {
                onClick(curImg)
            }
        }
    }

    override fun getItemCount(): Int {
        return imageList.size
    }


    private class PhotoListViewHolder(binding: FragmentPhotoListRvItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val imageView: ImageView = binding.itemPhoto
    }

}
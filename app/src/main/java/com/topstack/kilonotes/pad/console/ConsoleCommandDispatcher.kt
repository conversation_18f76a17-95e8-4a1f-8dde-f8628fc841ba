package com.topstack.kilonotes.pad.console

object ConsoleCommandDispatcher : ICommandDispatcher {

    private val commandReceivers = mutableListOf<IConsoleCommandReceiver>()

    override fun dispatchCommand(command: ConsoleCommand): List<IConsoleCommandReceiver> {
        return commandReceivers.filter {
            when (it) {
                is IConsoleInsertableObjectCommandReceiver -> {
                    command.commandType == ConsoleCommandType.SELECT
                            || command.commandType == ConsoleCommandType.SELECT_ALL
                            || command.commandType == ConsoleCommandType.DELETE
                            || command.commandType == ConsoleCommandType.DELETE_ALL
                            || command.commandType == ConsoleCommandType.REMOVE
                            || command.commandType == ConsoleCommandType.COPY
                            || command.commandType == ConsoleCommandType.PASTE
                            || command.commandType == ConsoleCommandType.UNDO
                            || command.commandType == ConsoleCommandType.REDO
                }

                is IConsoleSwitchToolCommandReceiver -> {
                    command.commandType == ConsoleCommandType.SWITCH_TO_MODE_PEN
                            || command.commandType == ConsoleCommandType.SWITCH_TO_MODE_HIGHLIGHTER
                            || command.commandType == ConsoleCommandType.SWITCH_TO_MODE_PICTURE
                            || command.commandType == ConsoleCommandType.SWITCH_TO_MODE_TEXT
                }
            }
        }
    }

    override fun registerCommandReceiver(receiver: IConsoleCommandReceiver) {
        if (!commandReceivers.contains(receiver)) {
            commandReceivers.add(receiver)
        }
    }

    override fun unregisterCommandReceiver(receiver: IConsoleCommandReceiver) {
        commandReceivers.remove(receiver)
    }

}
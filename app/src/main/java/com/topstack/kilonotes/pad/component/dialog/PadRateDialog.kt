package com.topstack.kilonotes.pad.component.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.RateDialog
import com.topstack.kilonotes.databinding.PadRateDialogBinding

class PadRateDialog : RateDialog() {

    private var _binding: PadRateDialogBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!
    private lateinit var mContext: Context

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mContext = requireContext()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PadRateDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("ResourceType")
    override fun onStart() {
        super.onStart()
        binding.close.setOnClickListener {
            dismiss()
        }
        binding.goToRate.setOnClickListener {
            jumpToMarket()
            dialog?.setOnKeyListener(null)
            dismiss()
        }
        dialog?.apply {
            setOnKeyListener { _, keyCode, _ -> keyCode == KeyEvent.KEYCODE_BACK }
            setCanceledOnTouchOutside(false)
        }
        dialog?.window?.apply {
            setBackgroundDrawable(
                Drawable.createFromXml(
                    mContext.resources,
                    mContext.resources.getXml(
                        R.drawable.dialog_rate_background
                    )
                )
            )
            setLayout(
                resources.getDimension(R.dimen.dp_480).toInt(),
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
package com.topstack.kilonotes.pad.console.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target.SIZE_ORIGINAL
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.util.loadDocumentCover
import com.topstack.kilonotes.databinding.ConsoleRecycleItemLastDocumentsBinding

/**
 * console last documents
 */
class ConsoleLastDocumentsAdapter(
    private var documents: List<Document>,
    private var currentSelectPosition: Int,
    private val coverCorners: Int,
    private val onDocumentSelectAction: (Document, Boolean) -> Boolean
) : RecyclerView.Adapter<ConsoleLastDocumentsAdapter.DocumentViewHolder>() {

    companion object {
        private const val SELECT_STATE_CHANGED = 0
    }

    private val requestOptions = RequestOptions.bitmapTransform(RoundedCorners(coverCorners))

    class DocumentViewHolder(val binding: ConsoleRecycleItemLastDocumentsBinding) :
        RecyclerView.ViewHolder(binding.root)

    fun switchToNext(): Int {
        if (documents.isEmpty()) return  -1
        val newSelectPosition = if (currentSelectPosition < documents.size - 1) {
            currentSelectPosition + 1
        } else {
            0
        }
        if (onDocumentSelectAction(documents[newSelectPosition], false)) {
            updateSelectPosition(newSelectPosition)
        }
        return newSelectPosition
    }

    private fun updateSelectPosition(newSelectPosition: Int) {
        val oldSelectPosition = currentSelectPosition
        currentSelectPosition = newSelectPosition
        notifyItemChanged(oldSelectPosition, SELECT_STATE_CHANGED)
        notifyItemChanged(newSelectPosition, SELECT_STATE_CHANGED)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun update(newDocuments: List<Document>, newSelectPosition: Int) {
        documents = newDocuments
        currentSelectPosition = newSelectPosition
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DocumentViewHolder {
        val binding = ConsoleRecycleItemLastDocumentsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DocumentViewHolder(binding).apply {
            binding.root.setOnClickListener {
                val newSelectPosition = bindingAdapterPosition
                if (onDocumentSelectAction(documents[newSelectPosition], true)) {
                    updateSelectPosition(newSelectPosition)
                }
            }
        }
    }

    override fun getItemCount(): Int = documents.size

    override fun onBindViewHolder(
        holder: DocumentViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isNotEmpty() && payloads.first() == SELECT_STATE_CHANGED) {
            updateHolderSelectState(holder, position)
            return
        }
        super.onBindViewHolder(holder, position, payloads)
    }

    override fun onBindViewHolder(holder: DocumentViewHolder, position: Int) {
        val currentDocument = documents[position]
        loadDocumentCover(currentDocument, holder.binding.cover, requestOptions)
        holder.binding.title.text = currentDocument.title
        updateHolderSelectState(holder, position)
    }

    private fun updateHolderSelectState(holder: DocumentViewHolder, position: Int) {
        if (position == currentSelectPosition) {
            holder.binding.selectIcon.visibility = View.VISIBLE
            holder.binding.selectBorder.visibility = View.VISIBLE
        } else {
            holder.binding.selectIcon.visibility = View.INVISIBLE
            holder.binding.selectBorder.visibility = View.INVISIBLE
        }
    }

}



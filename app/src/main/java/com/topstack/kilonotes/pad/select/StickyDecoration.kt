package com.topstack.kilonotes.pad.select

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.text.TextPaint
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R

/**
 * Description:RecyclerView 粘性头部效果的 Decoration
 */
abstract class StickyDecoration(val spanCount: Int, val context: Context) :
    RecyclerView.ItemDecoration() {
    /**
     * 粘性头部相关参数
     * 高、起始位置偏移量
     */
    private val mHeight: Int = context.resources.getDimension(R.dimen.sp_36).toInt()
    private val mTextStart: Float = context.resources.getDimension(R.dimen.sp_24)
    private val mPaint: Paint = Paint()
    private val mTextPaint: TextPaint
    private val mTextBounds: Rect
    private val mSpanCount: Int

    /**
     * 滚动条相关参数
     */
    private val scrollHeight = context.resources.getDimension(R.dimen.dp_60)
    private val scrollWidth = context.resources.getDimension(R.dimen.dp_8)
    private val barY = context.resources.getDimension(R.dimen.dp_6)

    override fun onDrawOver(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDrawOver(c, parent, state)
        var previousStickyHeaderName: String? = null
        var currentStickyHeaderName: String? = null
        val left = parent.left
        //Decoration 的右边位置
        val right = parent.right
        //获取 RecyclerView 的 Item 数量
        val childCount = parent.childCount
        for (i in 0 until childCount) {
            val childView = parent.getChildAt(i)
            //判断上一个 position 粘性头部的文字与当前 position 的粘性头部文字是否相同，如果相同则跳过绘制
            val position = parent.getChildAdapterPosition(childView)
            currentStickyHeaderName = getStickyHeaderName(position)
            if (TextUtils.isEmpty(currentStickyHeaderName)) {
                continue
            }
            if (position < mSpanCount || i < mSpanCount) {
                //Decoration 的底边位置
                var bottom = childView.top.coerceAtLeast(mHeight)
                //当当前 Decoration 的 Bottom 比下一个 View 的 Decoration 的 Top （即下一个 View 的 getTop() - mHeight）大时
                //就应该使当前 Decoration 的 Bottom 等于下一个 Decoration 的 Top，形成推动效果
                val nextChildView = parent.getChildAt(i + mSpanCount)
                val nextStickyHeaderName = getStickyHeaderName(position + mSpanCount)
                if (nextChildView != null && !TextUtils.equals(
                        currentStickyHeaderName,
                        nextStickyHeaderName
                    ) && bottom > nextChildView.top - mHeight
                ) {
                    bottom = nextChildView.top - mHeight
                }
                //Decoration 的顶边位置
                val top = bottom - mHeight
                c.drawRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), mPaint)
                //绘制文字
                mTextPaint.getTextBounds(
                    currentStickyHeaderName,
                    0,
                    currentStickyHeaderName!!.length,
                    mTextBounds
                )
                c.drawText(
                    currentStickyHeaderName,
                    mTextStart,
                    (bottom - mHeight / 2 + mTextBounds.height() / 2).toFloat(),
                    mTextPaint
                )
                continue
            }
            previousStickyHeaderName = getStickyHeaderName(position - mSpanCount)
            if (!TextUtils.equals(previousStickyHeaderName, currentStickyHeaderName)) {
                //Decoration 的底边位置
                var bottom = childView.top.coerceAtLeast(mHeight)
                //当当前 Decoration 的 Bottom 比下一个 View 的 Decoration 的 Top （即下一个 View 的 getTop() - mHeight）大时
                //就应该使当前 Decoration 的 Bottom 等于下一个 Decoration 的 Top，形成推动效果
                val nextChildView = parent.getChildAt(i + mSpanCount)
                var nextStickyHeaderName: String? = ""
                if (position + mSpanCount < childCount) {
                    nextStickyHeaderName = getStickyHeaderName(position + mSpanCount)
                }
                if (nextChildView != null && !TextUtils.equals(
                        currentStickyHeaderName,
                        nextStickyHeaderName
                    ) && bottom > nextChildView.top - mHeight
                ) {
                    bottom = nextChildView.top - mHeight
                }
                //Decoration 的顶边位置
                val top = bottom - mHeight
                c.drawRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), mPaint)
                //绘制文字
                mTextPaint.getTextBounds(
                    currentStickyHeaderName,
                    0,
                    currentStickyHeaderName!!.length,
                    mTextBounds
                )
                c.drawText(
                    currentStickyHeaderName,
                    mTextStart,
                    (bottom - mHeight / 2 + mTextBounds.height() / 2).toFloat(),
                    mTextPaint
                )
            }
        }

        /**
         * 滚动条效果
         */
        val barX = (parent.width - scrollWidth / 2 - context.resources.getDimension(R.dimen.dp_6))
        val extent = parent.computeVerticalScrollExtent()
        val range = parent.computeVerticalScrollRange()
        val offset = parent.computeVerticalScrollOffset()
        val maxEndY = (range - extent).toFloat()
        val proportion = offset / maxEndY
        val scrollableDistance = parent.height - scrollHeight - barY * 2
        val offsetY = scrollableDistance * proportion
        val paint = Paint()
        paint.isAntiAlias = true
        paint.color = Color.parseColor("#FFD8D8D8")
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeWidth = scrollWidth
        c.drawLine(barX, barY + offsetY, barX, barY + offsetY + scrollHeight, paint)
    }

    /**
     * Description:为 Decoration 设置偏移
     */
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        //outRect 相当于 Item 的整体绘制区域,设置 left、top、right、bottom 相当于设置左上右下的内间距
        //如设置 outRect.top = 5 则相当于设置 paddingTop 为 5px。
        val position = parent.getChildAdapterPosition(view)
        val stickyHeaderName = getStickyHeaderName(position)
        if (TextUtils.isEmpty(stickyHeaderName)) {
            return
        }
        if (position < mSpanCount) {
            outRect.top = mHeight
            return
        }
        val previousStickyHeaderName = getStickyHeaderName(position - mSpanCount)
        if (!TextUtils.equals(stickyHeaderName, previousStickyHeaderName)) {
            outRect.top = mHeight
        }
    }

    /**
     * Description:提供给外部设置每一个 position 的粘性头部的文字的方法
     * param
     * return
     */
    abstract fun getStickyHeaderName(position: Int): String?

    init {
        mPaint.isAntiAlias = true
        mPaint.color = Color.parseColor("#FFF5F5F5")
        mTextPaint = TextPaint()
        mTextPaint.isAntiAlias = true
        mTextPaint.color = Color.parseColor("#FF333333")
        mTextPaint.textSize = context.resources.getDimension(R.dimen.sp_24)
        mTextBounds = Rect()
        mSpanCount = spanCount
    }
}
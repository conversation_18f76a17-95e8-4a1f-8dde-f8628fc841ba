package com.topstack.kilonotes.pad.component.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.BaseHomeDialog
import com.topstack.kilonotes.base.component.fragment.NaviEnum
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.ktx.safeNavigate
import com.topstack.kilonotes.infra.device.DeviceUtils
import com.topstack.kilonotes.pad.note.NoteListFragmentDirections
import com.topstack.kilonotes.phone.note.PhoneNoteListFragmentDirections

class SupportAppDialog : BaseHomeDialog() {

    companion object {
        const val TAG = "SupportAppDialog"
    }

    private val close: ImageView by lazy { requireView().findViewById(R.id.close) }
    private val open: TextView by lazy { requireView().findViewById(R.id.open) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_support_app_dialog, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }

    override fun onStart() {
        super.onStart()

        close.setOnClickListener {
            dismiss()
        }

        open.setOnClickListener {
            setIsNeedRefreshNextDialog(false)
            if (DeviceUtils.isPhoneType(KiloApp.deviceType)) {
                val action = PhoneNoteListFragmentDirections.actionBuyMembershipStore()
                action.source = NaviEnum.SECOND_NOTE
                safeNavigate(R.id.note_list, action)
            } else {
                val action = NoteListFragmentDirections.actionBuyMembershipStore()
                action.source = NaviEnum.SECOND_NOTE
                safeNavigate(R.id.note_list, action)
            }
            dismiss()
        }

        dialog?.window?.apply {
            val params = attributes
            params.width = ViewGroup.LayoutParams.WRAP_CONTENT
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            attributes = params
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setGravity(Gravity.CENTER)
        }
    }

    override fun dismiss() {
        Preferences.needShowSupportAppDialog = false
        super.dismiss()
    }

}
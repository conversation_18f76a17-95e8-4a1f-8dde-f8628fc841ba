package com.topstack.kilonotes.pad.customtool.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.base.customtool.model.CustomTool
import com.topstack.kilonotes.base.doodle.views.doodleview.InputMode
import com.topstack.kilonotes.databinding.ItemCustomHideToolBinding
import com.topstack.kilonotes.infra.util.AppUtils

class CustomHideToolAdapter(
    val context: Context,
    var inputMode: InputMode?,
    val snippetIsShow: Boolean = false,
    val materialIsShow: Boolean = false,
    val doOnItemClick: ((CustomTool) -> Unit)? = null,
    val doOnNextClick: ((CustomTool) -> Unit)? = null
) :
    RecyclerView.Adapter<CustomHideToolAdapter.CustomHideTooViewHolder>() {
    private val data: MutableList<CustomTool> = mutableListOf()

    inner class CustomHideTooViewHolder(
        val binding: ItemCustomHideToolBinding,
        val doOnItemClick: ((position: Int) -> Unit)? = null,
        val doOnNextClick: ((position: Int) -> Unit)? = null
    ) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                doOnItemClick?.invoke(bindingAdapterPosition)
            }

            binding.nextIcon.setOnClickListener {
                doOnNextClick?.invoke(bindingAdapterPosition)
            }
        }

        fun bindView(customTool: CustomTool) {
            binding.toolIcon.setImageDrawable(
                AppUtils.getDrawable(
                    customTool.getCustomToolIconRes()
                )
            )
            binding.toolName.text = customTool.toolName
            binding.selectedIcon.isVisible =
                customTool.inputMode == inputMode
                        || customTool.isDrawMode(inputMode)
                        || customTool.isGraffiti(inputMode)
            binding.nextIcon.isVisible = customTool.hasSecondPage
            if (customTool.isToolSnippet()) {
                binding.selectedIcon.isVisible = snippetIsShow
            }
            if (customTool.isToolSticker()) {
                binding.selectedIcon.isVisible = materialIsShow
            }
        }

    }

    fun setData(newData: List<CustomTool>) {
        data.clear()
        data.addAll(newData)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CustomHideTooViewHolder {
        return CustomHideTooViewHolder(
            ItemCustomHideToolBinding.inflate(
                LayoutInflater.from(
                    context
                ),
                parent,
                false
            ),
            doOnItemClick = { position ->
                if (position < 0) return@CustomHideTooViewHolder
                if (position >= data.size) return@CustomHideTooViewHolder
                val customTool = data[position]
                doOnItemClick?.invoke(customTool)
            },
            doOnNextClick = { position ->
                if (position < 0) return@CustomHideTooViewHolder
                if (position >= data.size) return@CustomHideTooViewHolder
                doOnNextClick?.invoke(data[position])
            }
        )
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: CustomHideTooViewHolder, position: Int) {
        val customTool = data[position]
        holder.bindView(customTool)
    }
}
package com.topstack.kilonotes.pad.component.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.BaseHomeDialog
import com.topstack.kilonotes.base.component.fragment.NaviEnum
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.ktx.safeNavigate
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.infra.device.DeviceUtils
import com.topstack.kilonotes.pad.note.NoteListFragmentDirections
import com.topstack.kilonotes.phone.note.PhoneNoteListFragmentDirections

class CreateNoteRightsDialog : BaseHomeDialog() {

    companion object{
        const val TAG = "CreateNoteRightsDialog"
    }

    private val close: ImageView by lazy { requireView().findViewById(R.id.close) }
    private val open: TextView by lazy { requireView().findViewById(R.id.open) }
    private lateinit var displayMetrics: DisplayMetrics
    private var contentViewMeasuredWidth = 0
    private var contentViewMeasuredHeight = 0
    private val isWidthLayout
        get() = displayMetrics.widthPixels > displayMetrics.heightPixels

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
        displayMetrics = DimensionUtil.getScreenDimensions(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_create_note_rights, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }

    override fun onStart() {
        super.onStart()

        close.setOnClickListener {
            dismiss()
        }

        open.setOnClickListener {
            setIsNeedRefreshNextDialog(false)
            if (DeviceUtils.isPhoneType(KiloApp.deviceType)) {
                val action = PhoneNoteListFragmentDirections.actionBuyMembershipStore()
                action.source = NaviEnum.FOURTH_NOTE
                safeNavigate(R.id.note_list, action)
            } else {
                val action = NoteListFragmentDirections.actionBuyMembershipStore()
                action.source = NaviEnum.FOURTH_NOTE
                safeNavigate(R.id.note_list, action)
            }
            dismiss()
        }

        dialog?.window?.apply {
            val params = attributes
            params.width = ViewGroup.LayoutParams.WRAP_CONTENT
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            attributes = params
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setGravity(Gravity.CENTER)
        }
    }

    override fun dismiss() {
        Preferences.needShowCreateNoteRightsDialog = false
        super.dismiss()
    }

}
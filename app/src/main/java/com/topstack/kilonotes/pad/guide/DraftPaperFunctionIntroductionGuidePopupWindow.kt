package com.topstack.kilonotes.pad.guide

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.constraintlayout.helper.widget.Layer
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.ktx.adjustBackgroundRtlOrLtr
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.DraftPaperFunctionIntroductionGuideBinding
import com.topstack.kilonotes.databinding.DraftPaperFunctionIntroductionGuideOneThirdBinding
import com.topstack.kilonotes.infra.util.AppUtils.getString
import com.topstack.kilonotes.pad.component.CommonGuideCircularView

class DraftPaperFunctionIntroductionGuidePopupWindow(
    private val context: Context,
    private var stepOneOffsetArray: FloatArray,
    private var stepFourOffsetArray: FloatArray
) : PopupWindow() {

    private lateinit var rootView: View
    lateinit var arrow: ImageView
    lateinit var guideLayer: Layer
    lateinit var knowBg: ImageView
    lateinit var guideText: TextView
    lateinit var guideBtn: TextView
    private lateinit var arrowFour: ImageView
    private var arrowWidth = 0

    companion object {
        const val TAG = "DraftPaperFunctionIntroductionGuidePopupWindow"
        const val STEPONE = 1
        const val STEPTWO = 2
        const val STEPTHREE = 3
        const val STEPFOUR = 4
        private var currentStep = STEPONE //分屏和转屏时候能记录当前状态
    }

    init {
        width = ViewGroup.LayoutParams.MATCH_PARENT
        height = ViewGroup.LayoutParams.MATCH_PARENT
        isOutsideTouchable = false
        isFocusable = true
        arrowWidth = context.resources.getDimensionPixelSize(
            R.dimen.dp_254
        )
        initViewAndSetClickListener()
    }

    // 在这里进行视图的初始化和点击事件的设置
    private fun initViewAndSetClickListener() {
        rootView = if (isLandOneThirdScreen())
            DraftPaperFunctionIntroductionGuideOneThirdBinding.inflate(
                LayoutInflater.from(context)
            ).root
        else
            DraftPaperFunctionIntroductionGuideBinding.inflate(LayoutInflater.from(context)).root

        contentView = rootView
        guideText = rootView.findViewById(R.id.tips)
        guideBtn = rootView.findViewById(R.id.know)
        guideLayer = rootView.findViewById(R.id.guide_layer)
        arrow = rootView.findViewById(R.id.arrow)
        knowBg = rootView.findViewById(R.id.know_bg)
        arrowFour = rootView.findViewById(R.id.arrow_four)

        val circular =
            rootView.findViewById<CommonGuideCircularView>(R.id.circular)
        switchViewContentByCurrentStep(
            circular,
            rootView as ConstraintLayout
        )
        contentView.setOnClickListener {
            currentStep++
            switchViewContentByCurrentStep(
                circular,
                rootView as ConstraintLayout
            )
        }
    }


    // 切换视图内容的方法
    private fun switchViewContentByCurrentStep(
        circularView: CommonGuideCircularView,
        contentViewLayout: ConstraintLayout
    ) {
        arrowFour.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        arrow.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        knowBg.adjustBackgroundRtlOrLtr(KiloApp.isLayoutRtl)

        when (currentStep) {
            // 根据不同的步骤切换内容和按钮文本
            STEPONE -> {
                guideText.text = combiningTextAndImage()
                guideBtn.text = getString(R.string.next_step)

                circularView.setSettingOffsets(
                    stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                        R.dimen.dp_18
                    ),
                    stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(
                        R.dimen.dp_18
                    )
                )
                if (isLandOneThirdScreen()) {
                    arrow.post {
                        if (arrow.isAttachedToWindow) {
                            arrow.apply {
                                x = if (KiloApp.isLayoutRtl) {
                                    stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                                        R.dimen.dp_18
                                    ) - arrowWidth
                                } else {
                                    stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                                        R.dimen.dp_18
                                    )
                                }
                                y =
                                    stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(
                                        R.dimen.dp_38
                                    )
                            }
                        }
                    }
                    guideLayer.apply {
                        y =
                            stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_38) + arrow.height
                    }

                } else {
                    updateGuideWindowContent(
                        contentViewLayout,
                        stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(R.dimen.dp_18),
                        stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_38)
                    )
                }

            }

            STEPTWO -> {
                guideText.text = getString(R.string.draft_paper_function_introduction_guide_two)
                guideBtn.text = getString(R.string.next_step)

                circularView.setSettingOffsets(
                    if (KiloApp.isLayoutRtl) {
                        stepOneOffsetArray[0] - context.resources.getDimensionPixelSize(
                            R.dimen.dp_42
                        )
                    } else {
                        stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                            R.dimen.dp_78
                        )
                    },
                    stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_18)
                )
                if (isLandOneThirdScreen()) {
                    arrow.apply {
                        x = if (KiloApp.isLayoutRtl) {
                            stepOneOffsetArray[0] - context.resources.getDimensionPixelSize(
                                R.dimen.dp_42
                            ) - arrowWidth
                        } else {
                            stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                                R.dimen.dp_78
                            )
                        }
                        y =
                            stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_38)
                    }
                    guideLayer.apply {
                        y =
                            stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_38) + arrow.height
                    }

                } else {
                    updateGuideWindowContent(
                        contentViewLayout,
                        if (KiloApp.isLayoutRtl) {
                            stepOneOffsetArray[0] - context.resources.getDimensionPixelSize(
                                R.dimen.dp_42
                            )
                        } else {
                            stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                                R.dimen.dp_78
                            )
                        },
                        stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_38)
                    )
                }
            }

            STEPTHREE -> {
                guideText.text =
                    getString(R.string.draft_paper_function_introduction_guide_three)
                guideBtn.text = getString(R.string.next_step)

                circularView.setSettingOffsets(
                    if (KiloApp.isLayoutRtl) {
                        stepOneOffsetArray[0] - context.resources.getDimensionPixelSize(
                            R.dimen.dp_102
                        )
                    } else {
                        stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                            R.dimen.dp_138
                        )
                    },
                    stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_18)
                )
                if (isLandOneThirdScreen()) {
                    arrow.apply {
                        x = if (KiloApp.isLayoutRtl) {
                            stepOneOffsetArray[0] - context.resources.getDimensionPixelSize(
                                R.dimen.dp_102
                            ) - arrowWidth
                        } else {
                            stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                                R.dimen.dp_138
                            )
                        }
                        y =
                            stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_38)

                    }
                    guideLayer.apply {
                        y =
                            stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_38) + arrow.height
                    }
                } else {
                    updateGuideWindowContent(
                        contentViewLayout,
                        if (KiloApp.isLayoutRtl) {
                            stepOneOffsetArray[0] - context.resources.getDimensionPixelSize(
                                R.dimen.dp_102
                            )
                        } else {
                            stepOneOffsetArray[0] + context.resources.getDimensionPixelSize(
                                R.dimen.dp_138
                            )
                        },
                        stepOneOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_38)
                    )
                }
            }

            STEPFOUR -> {
                arrow.visibility = View.GONE
                guideText.text =
                    getString(R.string.draft_paper_function_introduction_guide_four)
                guideBtn.text = getString(R.string.finish_text_edit)
                guideText.post {
                    if (isLandOneThirdScreen()) {
                        arrowFour.apply {
                            x = if (KiloApp.isLayoutRtl) {
                                stepFourOffsetArray[0] + context.resources.getDimensionPixelSize(
                                    R.dimen.dp_24
                                )
                            } else {
                                stepFourOffsetArray[0] - width + context.resources.getDimensionPixelSize(
                                    R.dimen.dp_53
                                )
                            }
                            y = stepFourOffsetArray[1] - height
                            visibility = View.VISIBLE
                        }

                        guideLayer.apply {
                            y = stepFourOffsetArray[1] - arrowFour.height - guideText.height
                        }
                        guideBtn.apply {
                            y =
                                stepFourOffsetArray[1] - arrowFour.height + context.resources.getDimensionPixelSize(
                                    R.dimen.dp_100
                                )
                        }
                        knowBg.apply {
                            y =
                                stepFourOffsetArray[1] - arrowFour.height + context.resources.getDimensionPixelSize(
                                    R.dimen.dp_100
                                )
                        }

                    } else {
                        arrowFour.visibility = View.VISIBLE
                        updateGuideWindowContent(
                            contentViewLayout,
                            if (KiloApp.isLayoutRtl) {
                                stepFourOffsetArray[0] + context.resources.getDimensionPixelSize(
                                    R.dimen.dp_24
                                )
                            } else {
                                stepFourOffsetArray[0] - arrowFour.width - guideText.width + context.resources.getDimensionPixelSize(
                                    R.dimen.dp_30
                                )
                            },
                            stepFourOffsetArray[1] - arrowFour.height - guideText.height + context.resources.getDimensionPixelSize(
                                R.dimen.dp_22
                            ),
                            false
                        )
                    }
                    circularView.setSettingOffsets(
                        if (KiloApp.isLayoutRtl) {
                            stepFourOffsetArray[0] + context.resources.getDimensionPixelSize(R.dimen.dp_23)

                        } else {
                            stepFourOffsetArray[0] + context.resources.getDimensionPixelSize(R.dimen.dp_53)
                        },
                        stepFourOffsetArray[1] + context.resources.getDimensionPixelSize(R.dimen.dp_53)
                    )
                }

            }

            else -> {
                currentStep = STEPONE
                dismiss()
                UserUsageConfig.isNeedShowDraftPaperFunctionInstructionGuide = false
                return
            }
        }
    }

    private fun combiningTextAndImage(): SpannableString {
        val text = getString(R.string.draft_paper_function_introduction_guide_one)
        val drawable =
            ContextCompat.getDrawable(context, R.drawable.draft_paper_guide_preview_icon)
        drawable?.setBounds(
            0,
            0,
            context.resources.getDimensionPixelSize(R.dimen.dp_25),
            context.resources.getDimensionPixelSize(R.dimen.dp_18)
        )
        val spannableString = SpannableString("$text \u200B")
        drawable.let { d ->
            val imageSpan = d?.let { ImageSpan(it, ImageSpan.ALIGN_BASELINE) }
            spannableString.setSpan(
                imageSpan,
                spannableString.length - 1,
                spannableString.length,
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        return spannableString
    }

    private fun updateGuideWindowContent(
        constraintLayout: ConstraintLayout,
        distanceX: Float,
        distanceY: Float,
        needOffsetWindowWidth: Boolean = true
    ) {
        constraintLayout.findViewById<ConstraintLayout>(R.id.draft_paper_guide_constraintLayout)
            .apply {
                post {
                    if (isAttachedToWindow) {
                        if (KiloApp.isLayoutRtl && needOffsetWindowWidth) {
                            x = distanceX - width
                            y = distanceY
                        } else {
                            x = distanceX
                            y = distanceY
                        }
                    }
                }
            }
    }

    private fun isLandOneThirdScreen(): Boolean {
        return DimensionUtil.isLandAndOneThirdScreen(context) ||
                DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)
    }

}

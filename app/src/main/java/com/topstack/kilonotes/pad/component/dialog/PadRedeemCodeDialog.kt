package com.topstack.kilonotes.pad.component.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.base.account.UnifiedLogin
import com.topstack.kilonotes.base.account.WechatLoginDialog
import com.topstack.kilonotes.base.component.dialog.BaseDialogFragment
import com.topstack.kilonotes.base.component.view.FormatVerificationInputLayout
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.ktx.safeShow
import com.topstack.kilonotes.base.redeem.RedeemViewModel
import com.topstack.kilonotes.base.redeem.model.RedeemResultCode
import com.topstack.kilonotes.base.track.event.LoginLocation
import com.topstack.kilonotes.base.track.event.RedeemEvent
import com.topstack.kilonotes.base.track.event.StoreAndLoginEvent
import com.topstack.kilonotes.base.track.event.UserEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.databinding.PadDialogRedeemCodeBinding
import com.topstack.kilonotes.databinding.PadDialogRedeemCodeOneThirdScreenHorizontalBinding
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.infra.util.AppUtils

class PadRedeemCodeDialog : BaseDialogFragment() {

    companion object {
        const val TAG = "PadRedeemCodeDialog"
        private const val ANTI_SHAKE_CLICK_LISTENER_IGNORE_TIME = 2000
    }

    private val loginTips: TextView by lazy { requireView().findViewById(R.id.login_tips) }
    private val loginBtn: TextView by lazy { requireView().findViewById(R.id.login_btn) }
    private val input: FormatVerificationInputLayout by lazy { requireView().findViewById(R.id.input) }
    private val confirm: TextView by lazy { requireView().findViewById(R.id.confirm) }
    private val close: ImageView by lazy { requireView().findViewById(R.id.close) }

    private val redeemViewModel: RedeemViewModel by viewModels()

    private var inputManager: InputMethodManager? = null
    private var wechatLoginDialog: WechatLoginDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (savedInstanceState != null) {
            if (childFragmentManager.findFragmentByTag(WechatLoginDialog.TAG) != null) {
                val wechatLoginDialog =
                    childFragmentManager.findFragmentByTag(WechatLoginDialog.TAG) as WechatLoginDialog
                wechatLoginDialog.loginSuccessCallback = {
                    redeemViewModel.changeLoginState()
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return if (DimensionUtil.isLandAndOneThirdScreen(requireContext()) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                requireContext()
            )
        ) {
            PadDialogRedeemCodeOneThirdScreenHorizontalBinding.inflate(
                inflater,
                container,
                false
            ).root
        } else {
            PadDialogRedeemCodeBinding.inflate(inflater, container, false).root
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isCancelable = false
        inputManager =
            context?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
        initView()
        initListener()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            if (DimensionUtil.isLandAndOneThirdScreen(requireContext()) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                    requireContext()
                )
            ) {
                setLayout(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                setGravity(Gravity.BOTTOM)
            } else {
                setLayout(
                    requireContext().resources.getDimension(R.dimen.dp_700).toInt(),
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            }

            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

            setFlags(
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
            )
            decorView.setOnTouchListener { view, event ->
                if (event.action == MotionEvent.ACTION_DOWN) {
                    val view = currentFocus
                    if (isShouldHideInput(view, event)) {
                        view?.clearFocus()
                        hideSoftKeyboard()
                        return@setOnTouchListener true
                    }
                }
                return@setOnTouchListener false
            }
        }
    }

    private fun initView() {
        if (UserManager.loggedInUser != null) {
            loginTips.isVisible = false
            loginBtn.isVisible = false
        } else {
            input.setEditTextIsEnabled(false)
        }
        confirm.isEnabled = false

        redeemViewModel.addOnUserInfoChangedListener()
        redeemViewModel.setDoOnUserInfoChange { userInfo, isSubscriber ->
            if (userInfo != null) {
                loginTips.isVisible = false
                loginBtn.isVisible = false
                input.setEditTextIsEnabled(true)
            }
        }
    }

    private fun initListener() {
        loginBtn.setOnClickListener {
            callLogin()
        }
        confirm.setOnClickListener(
            AntiShakeClickListener(
                false,
                ANTI_SHAKE_CLICK_LISTENER_IGNORE_TIME
            ) {
                RedeemEvent.sendSettingsExchangeCode()
                if (checkNetwork()) {
                    input.verifyText()
                }
                hideSoftKeyboard()
            })
        input.setDoAfterFormatVerification { isPass, redeemCode ->
            if (isPass && checkNetwork()) {
                redeemViewModel.redeemVip(redeemCode)
            }
        }
        input.setIsFormatVerifyPassed { isPassed ->
            confirm.isEnabled = isPassed
        }
        close.setOnClickListener {
            hideSoftKeyboard()
            dismiss()
        }
        redeemViewModel.redeemResult.observe(viewLifecycleOwner) { redeemResult ->
            if (redeemResult != null) {
                when (redeemResult.resultCode) {
                    RedeemResultCode.ACQUIRE_SUCCESS -> {
                        dismiss()
                        showRedeemCodeConvertSuccessDialog(
                            redeemResult.redeemItemDuration,
                            redeemResult.redeemItemDurationUnit
                        )
                    }

                    RedeemResultCode.CODE_INVALID -> {
                        ToastUtils.topCenter(
                            requireContext(),
                            AppUtils.getString(R.string.redeem_fail_code_invalid)
                        )
                    }

                    RedeemResultCode.NEED_UPDATE -> {
                        ToastUtils.topCenter(
                            requireContext(),
                            AppUtils.getString(R.string.redeem_fail_need_update)
                        )

                    }

                    RedeemResultCode.FREQUENT_OPERATION -> {
                        ToastUtils.topCenter(
                            requireContext(),
                            AppUtils.getString(R.string.redeem_fail_frequent_operation)
                        )

                    }

                    RedeemResultCode.EXPIRED -> {
                        ToastUtils.topCenter(
                            requireContext(), AppUtils.getString(R.string.redeem_fail_expired)
                        )
                    }

                    RedeemResultCode.SERVER_EXCEPTION, RedeemResultCode.REQUEST_ERROR -> {
                        ToastUtils.topCenter(
                            requireContext(),
                            AppUtils.getString(R.string.redeem_fail_server_exception)
                        )
                    }

                    RedeemResultCode.BEEN_CONVERTED -> {
                        ToastUtils.topCenter(
                            requireContext(),
                            AppUtils.getString(R.string.redeem_fail_been_converted)
                        )
                    }

                    RedeemResultCode.UNKNOWN -> {
                        ToastUtils.topCenter(
                            requireContext(),
                            AppUtils.getString(R.string.network_not_connected)
                        )
                    }
                }
                redeemViewModel.clearRedeemResult()
            }
        }

        redeemViewModel.loginSuccessful.observe(viewLifecycleOwner) { isLoginSuccess ->
            if (isLoginSuccess) {
                ToastUtils.topCenter(requireContext(), R.string.redeem_toast_login_successful)
                redeemViewModel.reset()
            }
        }

    }

    private fun hideSoftKeyboard() {
        input.clearEditTextFocus()
        inputManager?.hideSoftInputFromWindow(
            input.windowToken,
            0
        )
    }

    private fun showRedeemCodeConvertSuccessDialog(duration: Int, durationUnit: String) {
        if (parentFragmentManager.findFragmentByTag(PadRedeemCodeConvertSuccessDialog.TAG) != null) return
        PadRedeemCodeConvertSuccessDialog().apply {
            this.duration = duration
            this.durationUnit = durationUnit
        }.safeShow(parentFragmentManager, PadRedeemCodeConvertSuccessDialog.TAG)
    }

    private fun callLogin() {
        if (checkNetwork()) {
            val fragment = parentFragmentManager.findFragmentByTag(UnifiedLogin.TAG)
            if (fragment != null && fragment is UnifiedLogin) {
                return
            }
            StoreAndLoginEvent.sendLoginClickEvent()
            UnifiedLogin().apply {
                setLoginLocation(LoginLocation.REDEEM_PAGE)
                safeShow(<EMAIL>, UnifiedLogin.TAG)
            }
            UserEvent.sendLoginPopoverShow(LoginLocation.REDEEM_PAGE)
            dismiss()
        }
    }

    private fun checkNetwork(): Boolean {
        return if (NetworkUtils.isNetworkAvailable()) {
            true
        } else {
            ToastUtils.topCenter(requireContext(), R.string.toast_no_internet)
            false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        redeemViewModel.removeUserInfoChangedListener()
    }

    //判断是否隐藏键盘
    private fun isShouldHideInput(v: View?, event: MotionEvent): Boolean {
        if (v != null && v is EditText) {
            val leftTop = intArrayOf(0, 0)
            //获取输入框当前的location位置
            v.getLocationInWindow(leftTop)
            val left = leftTop[0]
            val top = leftTop[1]
            val bottom = top + v.getHeight()
            val right = left + v.getWidth()
            return if (event.x > left && event.x < right && event.y > top && event.y < bottom) {
                // 点击的是输入框区域，保留点击EditText的事件
                false
            } else {
                true
            }
        }
        return false
    }
}
package com.topstack.kilonotes.pad.select

import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.imagefetch.ImageFetcher
import com.topstack.kilonotes.base.note.decoration.NoteGridSpacingItemDecoration
import com.topstack.kilonotes.base.select.*
import com.topstack.kilonotes.databinding.FragmentAlbumPhotoListBinding
import com.topstack.kilonotes.pad.select.adapter.PhotoListAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AlbumPhotoListFragment : BaseFragment(R.layout.fragment_album_photo_list),BasePhotoCropDialogFragment.IOnCropCompleteListener {
    private val PHOTO_SPAN_COUNT = 5
    private lateinit var albumAid: String
    private lateinit var binding: FragmentAlbumPhotoListBinding
    private val imageFetcher: ImageFetcher by lazy {
        ImageFetcher(requireContext())
    }

    private val selectPhotoViewModel: PadSelectPhotoViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentAlbumPhotoListBinding.bind(view)
        val albumAid = arguments?.getString(BUNDLE_ALBUM_AID_KEY)
        if (TextUtils.isEmpty(albumAid)) {
            findNavController().popBackStack()
            return
        }
        if (savedInstanceState != null){
            val cropDialog =
                parentFragmentManager.findFragmentByTag(BasePhotoCropDialogFragment.TAG)
            if (cropDialog is PhotoCropDialogFragment){
                cropDialog.setOnCropCompleteListener(this)
            }
        }
        this.albumAid = albumAid!!
        initView()
        initListener()
    }

    private fun initView() {
        lifecycleScope.launch(Dispatchers.IO) {
            val curAlbum = imageFetcher.getAlbumData(albumAid)
            withContext(Dispatchers.Main) {
                if (curAlbum == null) {
                    findNavController().popBackStack()
                } else {
                    val gridLayoutManager = GridLayoutManager(requireContext(), getSpanCount())
                    val decoration =
                        NoteGridSpacingItemDecoration(
                            resources.getDimension(R.dimen.dp_2).toInt()
                        )
                    binding.photoRv.adapter = PhotoListAdapter(curAlbum.images, requireContext()) {
                        if (selectPhotoViewModel.ignoreDetailImage) {
                            val activity = requireActivity()
                            val intent = activity.intent
                            intent.putExtra(BUNDLE_URI_KEY, it.uri)
                            activity.setResult(PICK_PIC_RESULT_CODE, intent)
                            activity.finish()
                            return@PhotoListAdapter
                        }
                        val bundle = Bundle()
                        bundle.putParcelable(BUNDLE_URI_KEY, it.uri)
                        if (selectPhotoViewModel.needCropImage) {
                            if (selectPhotoViewModel.needSelectRatio) {
                                findNavController().navigate(
                                    R.id.pick_and_selectable_fixed_crop_photo_fragment,
                                    bundle
                                )
                            } else {
                                findNavController().navigate(
                                    R.id.pick_and_crop_photo_fragment,
                                    bundle
                                )
                            }
                        } else if (selectPhotoViewModel.needCropAndChangeAlpha) {
                            val photoCropDialogFragment = PhotoCropDialogFragment().apply {
                                photoUri = it.uri
                                setOnCropCompleteListener(this@AlbumPhotoListFragment)
                            }
                            photoCropDialogFragment.show(parentFragmentManager, BasePhotoCropDialogFragment.TAG)
                        } else {
                            findNavController().navigate(R.id.pickPhotoFragment, bundle)
                        }
                    }

                    binding.photoRv.layoutManager = gridLayoutManager
                    binding.photoRv.addItemDecoration(decoration)

                    binding.albumTitle.text = curAlbum.name
                }
            }
        }

    }

    private fun initListener() {
        binding.back.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.cancelTv.setOnClickListener {
            requireActivity().finish()
        }
    }

    private fun getSpanCount(): Int {
        return PHOTO_SPAN_COUNT
    }

    override fun onCropSuccess(uri: Uri, imageAlpha: Int) {
        val requireActivity = requireActivity()
        val intent = requireActivity.intent
        intent.putExtra(BUNDLE_URI_KEY, uri)
        intent.putExtra(BUNDLE_ALPHA_KEY, imageAlpha)
        requireActivity.setResult(PICK_PIC_RESULT_CODE, intent)
        requireActivity.finish()
    }

    override fun onCropFailed() {
    }

    override fun onCancel() {
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (requireActivity().isChangingConfigurations){
            binding.photoRv.stopScroll()
        }
    }

}
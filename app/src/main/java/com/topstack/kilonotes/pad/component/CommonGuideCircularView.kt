package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.util.AttributeSet
import android.view.View
import com.topstack.kilonotes.R
import com.topstack.kilonotes.infra.util.AppUtils


class CommonGuideCircularView(
    context: Context,
    attrs: AttributeSet? = null
) : View(context, attrs) {
    private var circleCenterX: Float = 0f
    private var circleCenterY: Float = 0f
    private var circleRadius: Float = context.resources.getDimensionPixelSize(R.dimen.dp_25).toFloat()

    private val maskPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val mXfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)

    fun setSettingOffsetsAndRadius(xOffset: Float, yOffset: Float, radius: Float) {
        circleRadius = radius
        setSettingOffsets(xOffset,yOffset)
    }

    fun setSettingOffsets(xOffset: Float, yOffset: Float) {
        circleCenterX = xOffset
        circleCenterY = yOffset
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        maskPaint.color = AppUtils.getColor(R.color.hidden_space_guide_bg)
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), maskPaint)
        maskPaint.alpha = 255 //混合模式透明度值为百分百才可以把绘制相交的部分完全去除
        maskPaint.xfermode = mXfermode
        canvas.drawCircle(
            circleCenterX,
            circleCenterY,
            circleRadius,
            maskPaint
        )
        maskPaint.xfermode = null
    }
}










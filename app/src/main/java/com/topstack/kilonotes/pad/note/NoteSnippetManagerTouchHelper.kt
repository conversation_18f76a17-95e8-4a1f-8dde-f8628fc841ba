package com.topstack.kilonotes.pad.note

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.base.note.snippet.data.SnippetTag
import com.topstack.kilonotes.pad.note.adapter.NoteSnippetListAdapter

class NoteSnippetManagerTouchHelper : ItemTouchHelper.Callback() {
    private var dragedItem: SnippetTag? = null
    private var enableDrag = false

    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        val adapter = recyclerView.adapter
        enableDrag =
            adapter is NoteSnippetListAdapter
        val dragFlags = if (enableDrag) {
            ItemTouchHelper.DOWN or ItemTouchHelper.UP
        } else {
            0
        }
        val swipeFlags = 0
        return makeMovementFlags(dragFlags, swipeFlags)
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        var adapter = recyclerView.adapter
        if (adapter !is NoteSnippetListAdapter) return true
        val fromPosition = viewHolder.bindingAdapterPosition
        if (dragedItem == null) {
            dragedItem = adapter.getItemData(fromPosition)
        }
        val targetPosition = target.bindingAdapterPosition
        adapter.onSwapNoteSnippet(fromPosition, targetPosition)
        return true
    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        super.onSelectedChanged(viewHolder, actionState)
    }

    override fun isLongPressDragEnabled(): Boolean {
        return false
    }


    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        val adapter = recyclerView.adapter
        if (adapter is NoteSnippetListAdapter) {
            val preItem = adapter.getItemData(viewHolder.bindingAdapterPosition - 1)
            val nextItem = adapter.getItemData(viewHolder.bindingAdapterPosition + 1)
            if (viewHolder is NoteSnippetListAdapter.ViewHolder) {
                viewHolder.downTranslationZ()
            }
            if (dragedItem != null) {
                if (recyclerView.isComputingLayout) return
                adapter.onSwapFinish(
                    viewHolder.bindingAdapterPosition,
                    dragedItem!!,
                    preItem,
                    nextItem
                )
            }
        }
        dragedItem = null
    }
}
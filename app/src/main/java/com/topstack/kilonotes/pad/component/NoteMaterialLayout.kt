package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.*
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.base.ktx.removeAllItemDecorations
import com.topstack.kilonotes.base.material.repository.NoteMaterialRepository.NoteMaterialStickerCategoryInfo
import com.topstack.kilonotes.base.material.repository.NoteMaterialRepository.NoteMaterialStickerInfo
import com.topstack.kilonotes.base.mymaterial.model.CustomMaterial
import com.topstack.kilonotes.base.note.decoration.GridLayoutItemDecoration
import com.topstack.kilonotes.databinding.NoteMaterialBinding
import com.topstack.kilonotes.pad.note.adapter.*
import com.topstack.kilonotes.pad.note.decoration.CustomMaterialDecoration
import com.topstack.kilonotes.pad.note.model.PaperCutState
import com.topstack.kilonotes.pad.note.model.PaperCutTool

class NoteMaterialLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val STICKER_SPAN_COUNT = 3
    }

    private var materialCategoryPosition = 0
    private var materialPosition = hashMapOf<NoteMaterialStickerCategoryInfo, Int>()
    private var currentSelectedStickerCategory: NoteMaterialStickerCategoryInfo? = null
    private val binding: NoteMaterialBinding =
        NoteMaterialBinding.inflate(LayoutInflater.from(context), this, true)

    private val paperCutToolsName = PaperCutTool.values()

    private var materialCategoryAdapter: MaterialCategoryAdapter? = null
    private var materialStickerAdapter: MaterialStickerAdapter? = null
    private var paperCutToolsAdapter: PaperCutToolsAdapter =
        PaperCutToolsAdapter(context, paperCutToolsName).apply {
            doOnPaperCutTypeSelected {
                selectPaperType(it)
                onCustomMaterialTypeSelectedAction?.invoke(it)
            }
        }

    private val isVip = UserManager.isVip()
    private val paperCutToolContentAdapter: PaperCutToolContentAdapter by lazy(LazyThreadSafetyMode.NONE) {
        PaperCutToolContentAdapter(context, isVip).apply {
            setItemClickAction {
                onPaperCutToolClickedAction?.invoke(it)
            }
        }
    }

    fun setPaperCutToolOnClickAction(action: (position: Int) -> Unit) {
        onPaperCutToolClickedAction = action
    }

    private val customMaterialListAdapter: CustomMaterialListAdapter by lazy(LazyThreadSafetyMode.NONE) {
        CustomMaterialListAdapter(context).apply {
            doOnItemDelClicked { customMaterial, position ->
                onCustomMaterialItemDelClicked?.invoke(customMaterial, position)
            }
            doOnSwapFinish {
                onCustomMaterialSwap?.invoke(it)
            }
            doOnCustomMaterialClick {
                onCustomMaterialClick?.invoke(it)
            }
        }
    }

    private val itemTouchHelper: ItemTouchHelper by lazy(LazyThreadSafetyMode.NONE) {
        ItemTouchHelper(CustomMaterialItemTouch())
    }

    var onNewTypeSelectedAction: ((NoteMaterialStickerCategoryInfo) -> Unit)? = null
    var onStickerClickedAction: ((NoteMaterialStickerInfo) -> Unit)? = null
    var onCloseClickedAction: (() -> Unit)? = null
    var onConfirmClickedAction: (() -> Unit)? = null
    var onReloadClickedAction: (() -> Unit)? = null
    var onPaperCutToolClickedAction: ((Int) -> Unit)? = null
    var onCustomMaterialTypeSelectedAction: ((PaperCutTool) -> Unit)? = null
    var onCustomMaterialItemDelClicked: ((CustomMaterial, Int) -> Unit)? = null
    var onCustomMaterialSwap: ((List<CustomMaterial>) -> Unit)? = null
    var onCustomMaterialClick: ((CustomMaterial) -> Unit)? = null
    var materialListScrollPositionChange: ((Int) -> Unit)? = null
    var materialListVisibilityChange: ((Int, Int) -> Unit)? = null

    fun select(materialCategory: NoteMaterialStickerCategoryInfo) {
        currentSelectedStickerCategory = materialCategory
        paperCutToolsAdapter.clearSelected()
        materialCategoryAdapter?.setCurrentSelect(materialCategory)
    }


    private val materialListScrollListener: RecyclerView.OnScrollListener = object :
        RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            val hasEnd = newState == RecyclerView.SCROLL_STATE_IDLE
            if (hasEnd && recyclerView.layoutManager != null) {
                val linearLayoutManager = recyclerView.layoutManager!! as? LinearLayoutManager
                val materialPosition = linearLayoutManager?.findFirstVisibleItemPosition()
                if (materialPosition != null) {
                    materialListScrollPositionChange?.invoke(materialPosition)
                    materialListVisibilityChange?.invoke(
                        linearLayoutManager.findFirstVisibleItemPosition(),
                        linearLayoutManager.findLastVisibleItemPosition()
                    )
                }
            }
        }
    }

    fun materialCategorySmoothMoveToPosition(position: Int) {
        materialCategoryPosition = position
    }

    fun materialSmoothMoveToPosition(materialPositionMap: HashMap<NoteMaterialStickerCategoryInfo, Int>) {
        materialPosition = materialPositionMap
    }

    fun refreshTypeList(categoryInfoList: List<NoteMaterialStickerCategoryInfo>) {
        if (materialCategoryAdapter == null) {
            val sortedCategoryInfoList = categoryInfoList.toMutableList()
            sortedCategoryInfoList.sortBy { it.noteMaterialCategory.sort }
            materialCategoryAdapter = MaterialCategoryAdapter(
                sortedCategoryInfoList,
            ).apply {
                doOnNewTypeSelected { position, info ->
                    onNewTypeSelectedAction?.invoke(info)
                }
            }
            binding.materialTypeList.overScrollRecyclerView.run {
                adapter = ConcatAdapter(paperCutToolsAdapter, materialCategoryAdapter)
                layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
                addItemDecoration(object : RecyclerView.ItemDecoration() {
                    private val startInterval =
                        context.resources.getDimensionPixelSize(R.dimen.dp_16)
                    private val itemInterval =
                        context.resources.getDimensionPixelSize(R.dimen.dp_16)

                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        if (parent.getChildAdapterPosition(view) == 0) {
                            outRect.left = startInterval
                        }
                        outRect.right = itemInterval
                    }
                })
            }
        } else {
            materialCategoryAdapter?.refreshList(categoryInfoList)
        }
        if (materialCategoryPosition >= 0) {
            var position = materialCategoryPosition + paperCutToolsAdapter.itemCount
            if (materialCategoryPosition == 0) position--
            binding.materialTypeList.overScrollRecyclerView.adapter?.let {
                if (position < it.itemCount) {
                    binding.materialTypeList.overScrollRecyclerView.scrollToPosition(position)
                }
            }
        }
    }

    fun refreshStickerList(stickerInfoList: List<NoteMaterialStickerInfo>) {
        hidePaperCutEditView()
        hideCustomMaterialEmptyTip()
        binding.materialList.run {
            setPadding(paddingLeft, 0, paddingRight, paddingBottom)
        }
        if (binding.materialList.overScrollRecyclerView.itemDecorationCount > 0) {
            binding.materialList.overScrollRecyclerView.removeAllItemDecorations()
        }
        if (materialStickerAdapter == null) {
            materialStickerAdapter = MaterialStickerAdapter(
                stickerInfoList.toMutableList(),
            ).apply {
                doOnStickerClicked {
                    onStickerClickedAction?.invoke(it)
                }
            }
            binding.materialList.overScrollRecyclerView.run {
                adapter = materialStickerAdapter
                layoutManager =
                    GridLayoutManager(context, STICKER_SPAN_COUNT, RecyclerView.VERTICAL, false)
                addItemDecoration(
                    GridLayoutItemDecoration(
                        STICKER_SPAN_COUNT,
                        context.resources.getDimensionPixelSize(R.dimen.dp_18),
                        context.resources.getDimensionPixelSize(R.dimen.dp_21),
                        context.resources.getDimensionPixelSize(R.dimen.dp_18)
                    )
                )
            }
        } else {
            binding.materialList.overScrollRecyclerView.run {
                adapter = materialStickerAdapter
                layoutManager =
                    GridLayoutManager(context, STICKER_SPAN_COUNT, RecyclerView.VERTICAL, false)
                addItemDecoration(
                    GridLayoutItemDecoration(
                        STICKER_SPAN_COUNT,
                        context.resources.getDimensionPixelSize(R.dimen.dp_18),
                        context.resources.getDimensionPixelSize(R.dimen.dp_21),
                        context.resources.getDimensionPixelSize(R.dimen.dp_18)
                    )
                )
            }
            materialStickerAdapter?.refreshList(stickerInfoList)
        }
        binding.materialList.overScrollRecyclerView.clearOnScrollListeners()
        binding.materialList.overScrollRecyclerView.addOnScrollListener(materialListScrollListener)
        if (currentSelectedStickerCategory != null && materialPosition[currentSelectedStickerCategory] !== null) {
            binding.materialList.overScrollRecyclerView.scrollToPosition(materialPosition[currentSelectedStickerCategory]!!)
        }
        binding.materialList.post {
            val layoutManager =
                binding.materialList.overScrollRecyclerView.layoutManager as? LinearLayoutManager
            layoutManager?.let {
                materialListVisibilityChange?.invoke(
                    it.findFirstVisibleItemPosition(),
                    it.findLastVisibleItemPosition()
                )
            }
        }

        itemTouchHelper.attachToRecyclerView(null)
        customMaterialListAdapter.paperCutState = PaperCutState.NORMAL
    }

    fun refreshSticker(stickerInfo: NoteMaterialStickerInfo, param: Any) {
        materialStickerAdapter?.refreshItem(stickerInfo, param)
    }

    fun hideBottomView() {
        refreshBottomView(show = false, showHint = false, resId = -1, enabled = false)
    }

    fun showBottomDownloadView() {
        refreshBottomView(show = true, showHint = false, resId = R.string.download, enabled = true)
    }

    fun showBottomDownloadingView() {
        refreshBottomView(
            show = true,
            showHint = false,
            resId = R.string.downloading,
            enabled = false
        )
    }

    fun showBottomNeedVipView() {
        refreshBottomView(show = true, showHint = true, resId = R.string.go_to_vip, enabled = true)
    }

    private fun refreshBottomView(show: Boolean, showHint: Boolean, resId: Int, enabled: Boolean) {
        if (show) {
            binding.bottomViewGroup.visibility = View.VISIBLE
            binding.bottomHintText.visibility = if (showHint) {
                View.VISIBLE
            } else {
                View.GONE
            }
            binding.bottomConfirmText.text = context.getString(resId)
            binding.bottomConfirmText.isEnabled = enabled
            hideCustomMaterialEmptyTip()
        } else {
            binding.bottomViewGroup.visibility = View.GONE
        }
    }

    fun showReloadView() {
        binding.reloadGroup.visibility = View.VISIBLE
    }

    fun hideReloadView() {
        binding.reloadGroup.visibility = View.INVISIBLE
    }

    fun selectPaperType(type: PaperCutTool) {
        hideReloadView()
        materialCategoryAdapter?.clearSelected()
        paperCutToolsAdapter.setCurrentSelect(type)
        binding.materialTypeList.overScrollRecyclerView.adapter?.let {
            val toolList = PaperCutTool.values()
            val position = toolList.indexOf(type)
            if (position in toolList.indices) {
                binding.materialTypeList.overScrollRecyclerView.scrollToPosition(position)
            }
        }
        if (type == PaperCutTool.PAPER_CUT_TOOL) {
            showPaperCutsTools()
        }
    }

    private fun showPaperCutsTools() {
        hidePaperCutEditView()
        hideCustomMaterialEmptyTip()
        binding.materialList.overScrollRecyclerView.run {
            setPadding(paddingLeft, 0, paddingRight, paddingBottom)
            if (this.itemDecorationCount > 0) {
                removeAllItemDecorations()
            }
            adapter = paperCutToolContentAdapter
            layoutManager =
                LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            val paperCutsToolsItemDecorations = object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    val position = parent.getChildAdapterPosition(view)
                    if (position != 0) {
                        outRect.top = resources.getDimension(R.dimen.dp_30).toInt()
                    }
                }
            }
            addItemDecoration(paperCutsToolsItemDecorations)
        }
        itemTouchHelper.attachToRecyclerView(null)
        customMaterialListAdapter.paperCutState = PaperCutState.NORMAL
    }

    fun refreshPaperCutList(paperCuts: List<CustomMaterial>) {
        showPaperCutEditView(paperCuts)
        if (paperCuts.isEmpty()) {
            //无剪纸素材
            showCustomMaterialEmptyTip()
            binding.materialList.overScrollRecyclerView.adapter = null
            return
        }
        val lastAdapter = binding.materialList.overScrollRecyclerView.adapter
        if (lastAdapter is CustomMaterialListAdapter) {
            lastAdapter.customMaterialChange(paperCuts)
            return
        }
        binding.materialList.overScrollRecyclerView.run {
            setPadding(
                context.resources.getDimensionPixelSize(R.dimen.dp_11),
                context.resources.getDimensionPixelSize(R.dimen.dp_47),
                context.resources.getDimensionPixelSize(R.dimen.dp_11),
                paddingBottom
            )
            var hasCustomMaterialDecoration = false
            for (i in 0 until itemDecorationCount) {
                val itemDecoration = getItemDecorationAt(i)
                if (itemDecoration is CustomMaterialDecoration) {
                    hasCustomMaterialDecoration = true
                }
            }
            if (!hasCustomMaterialDecoration) {
                removeAllItemDecorations()
            }
            adapter = customMaterialListAdapter
            customMaterialListAdapter.exitEditMode()
            customMaterialListAdapter.customMaterialChange(paperCuts)
            layoutManager =
                GridLayoutManager(context, STICKER_SPAN_COUNT, RecyclerView.VERTICAL, false)
            itemTouchHelper.attachToRecyclerView(this)
        }
    }

    fun showPaperCutEditView(lists: List<CustomMaterial>?) {
        binding.paperCutEditBtn.run {
            isVisible = true
            isEnabled = !lists.isNullOrEmpty()
            isSelected = customMaterialListAdapter.paperCutState == PaperCutState.EDIT
        }
    }

    fun setupBlurView(root: ViewGroup) {
        binding.materialBlurView.setupWith(root)
            .setBlurRadius(25F)
    }

    fun hidePaperCutEditView() {
        binding.paperCutEditBtn.isVisible = false
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        binding.materialList.setLayerType(LAYER_TYPE_HARDWARE, null)
        binding.materialTypeList.setLayerType(LAYER_TYPE_HARDWARE, null)
        binding.borderView.onButtonClickedAction = {
            onCloseClickedAction?.invoke()
        }
        binding.bottomConfirmText.setOnClickListener {
            onConfirmClickedAction?.invoke()
        }
        binding.reloadText.setOnClickListener {
            onReloadClickedAction?.invoke()
        }
        binding.paperCutEditBtn.setOnClickListener {
            customMaterialListAdapter.triggerEditMode()
            it.isSelected = customMaterialListAdapter.paperCutState == PaperCutState.EDIT
        }
    }

    fun exitCustomMaterial() {
        customMaterialListAdapter.exitEditMode()
        binding.paperCutEditBtn.isSelected = false
    }

    fun hideCustomMaterialEmptyTip() {
        binding.paperCutEmptyTips.isVisible = false
    }

    fun showCustomMaterialEmptyTip() {
        binding.paperCutEmptyTips.isVisible = true
    }

    fun refreshAdapter() {
        paperCutToolsAdapter?.notifyDataSetChanged()
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        super.dispatchTouchEvent(ev)
        return true
    }
}


package com.topstack.kilonotes.pad.setting

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.HiddenSpaceGuideWindowBinding
import com.topstack.kilonotes.databinding.HiddenSpaceGuideWindowOneHalfScreenBinding
import com.topstack.kilonotes.databinding.HiddenSpaceGuideWindowPortraitHalfScreenBinding

class HiddenSpaceGuideWindow(val context: Context, val isLandAndHalfScreen: Boolean) :
    PopupWindow() {

    companion object {
        const val DEFAULT_TIPS_LINE = 2
    }

    private var rootView: View
    private var know: TextView
    private var tips: TextView
    private var arrow: ImageView


    init {
        if (isLandAndHalfScreen) {
            val binding =
                HiddenSpaceGuideWindowOneHalfScreenBinding.inflate(LayoutInflater.from(context))
            rootView = binding.root
            know = binding.oneHalfScreenKnow
            tips = binding.oneHalfScreenTips
            arrow = binding.oneHalfScreenArrow
        } else if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)) {
            val binding =
                HiddenSpaceGuideWindowPortraitHalfScreenBinding.inflate(LayoutInflater.from(context))
            rootView = binding.root
            know = binding.know
            tips = binding.tips
            arrow = binding.arrow
        } else {
            val binding = HiddenSpaceGuideWindowBinding.inflate(LayoutInflater.from(context))
            rootView = binding.root
            know = binding.know
            tips = binding.tips
            arrow = binding.arrow
        }
        isFocusable = true
        isOutsideTouchable = true
        contentView = rootView
        width = LinearLayout.LayoutParams.WRAP_CONTENT
        height = LinearLayout.LayoutParams.WRAP_CONTENT
        know.setOnClickListener {
            dismiss()
        }
        arrow.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        know.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
    }

    fun getTitleLines(): Int {
        return tips.lineCount
    }


}
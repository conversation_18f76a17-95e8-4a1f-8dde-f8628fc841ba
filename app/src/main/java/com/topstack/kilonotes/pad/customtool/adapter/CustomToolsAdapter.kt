package com.topstack.kilonotes.pad.customtool.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.customtool.model.CustomTool
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.databinding.ItemCustomDisplayToolsBinding
import com.topstack.kilonotes.infra.util.AppUtils

class CustomToolsAdapter(
    val context: Context,
    val itemTouch: ItemTouchHelper,
    val doOnAddOrHideIcon: ((targetData: CustomTool, (MutableList<CustomTool>)) -> Unit)? = null
) : RecyclerView.Adapter<CustomToolsAdapter.CustomToolsViewHolder>() {

    private val data: MutableList<CustomTool> = mutableListOf()
    var onSwapFinish: ((List<CustomTool>) -> Unit)? = null

    fun setData(newData: List<CustomTool>) {
        data.clear()
        data.addAll(newData)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CustomToolsViewHolder {
        return CustomToolsViewHolder(
            ItemCustomDisplayToolsBinding.inflate(
                LayoutInflater.from(
                    context
                ),
                parent,
                false
            ),
            doOnAddOrHideIcon
        )
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: CustomToolsViewHolder, position: Int) {
        val customTool = data[position]
        holder.bindView(customTool)
    }

    fun removeCustomTool(position: Int): MutableList<CustomTool> {
        if (data.size <= position) return mutableListOf()
        data.removeAt(position)
        notifyItemRemoved(position)
        return data
    }

    fun insertCustomToolById(customTool: CustomTool): MutableList<CustomTool> {
        data.add(customTool)
        val newData = data.sortedBy {
            it.id
        }
        data.clear()
        data.addAll(newData)
        val position = data.indexOf(customTool)
        notifyItemInserted(position)
        return data
    }

    fun insertCustomToolAtLast(customTool: CustomTool): MutableList<CustomTool> {
        data.add(customTool)
        val position = data.indexOf(customTool)
        notifyItemInserted(position)
        return data
    }

    inner class CustomToolsViewHolder(
        val binding: ItemCustomDisplayToolsBinding,
        val doOnAddOrHideIcon: ((targetData: CustomTool, (MutableList<CustomTool>)) -> Unit)? = null
    ) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.moveIcon.setOnTouchListener { v, event ->
                if (event.actionMasked == MotionEvent.ACTION_DOWN) {
                    val position = bindingAdapterPosition
                    if (position != RecyclerView.NO_POSITION) {
                        itemTouch.startDrag(this) // 开始拖动
                    }
                }
                return@setOnTouchListener false
            }

            binding.addOrHideIcon.setOnClickListener(AntiShakeClickListener {
                if (bindingAdapterPosition < 0) return@AntiShakeClickListener
                if (bindingAdapterPosition >= data.size) return@AntiShakeClickListener
                val customTool = data[bindingAdapterPosition]
                if (data.size == 1 && customTool.isDisplay && customTool.isDoodleTool) {
                    ToastUtils.topCenter(
                        context,
                        AppUtils.getString(R.string.custom_tool_last_itme_tips)
                    )
                } else {
                    doOnAddOrHideIcon?.invoke(customTool, removeCustomTool(bindingAdapterPosition))
                }
            })
        }

        fun bindView(customTool: CustomTool) {
            binding.toolIcon.setImageDrawable(
                AppUtils.getDrawable(
                    customTool.getCustomToolIconRes()
                )
            )
            binding.toolName.text = customTool.toolName
            binding.moveIcon.isVisible = customTool.canMove
            binding.addOrHideIcon.setImageDrawable(
                if (customTool.isDisplay) AppUtils.getDrawable(R.drawable.custom_tool_hide_icon) else AppUtils.getDrawable(
                    R.drawable.custom_tool_add_icon
                )
            )
        }

        fun canMove(): Boolean {
            return binding.moveIcon.isVisible
        }

        fun onSwapCustomTool(from: Int, to: Int) {
            val target = data.removeAt(from)
            data.add(to, target)
            notifyItemMoved(from, to)
        }

        fun onSwapFinish(changePos: Int) {
            onSwapFinish?.invoke(data)
            notifyItemChanged(changePos)
        }
    }
}
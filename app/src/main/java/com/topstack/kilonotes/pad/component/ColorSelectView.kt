package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.view.setPadding
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.ColorSelectItemBinding

class ColorSelectView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private var itemSize: Float
    private var itemInterval: Float

    private lateinit var colorAdapter: ColorAdapter

    init {
        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.ColorSelectView,
            defStyleAttr, 0
        ).apply {
            try {
                itemSize = getDimension(R.styleable.ColorSelectView_item_size, 44F)
                itemInterval = getDimension(R.styleable.ColorSelectView_item_interval, 24F)
            } finally {
                recycle()
            }
        }
    }

    fun setup(
        initialPosition: Int,
        colorList: List<String>,
        onColorSelected: (color: String) -> Unit
    ) {
        ColorAdapter(initialPosition, colorList) { position ->
            colorAdapter.currentSelectPosition = position
            onColorSelected(colorAdapter.colors[position])
        }.let {
            adapter = it
            colorAdapter = it
        }
        while (itemDecorationCount > 0) {
            removeItemDecorationAt(0)
        }
        addItemDecoration(object : RecyclerView.ItemDecoration() {
            private val startEndInterval = context.resources.getDimensionPixelSize(R.dimen.dp_48)
            private val horizontalInterval = context.resources.getDimensionPixelSize(R.dimen.dp_24)

            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: State
            ) {
                val isLandAndOneThirdScreen =
                    DimensionUtil.isLandAndOneThirdScreen(view.context)
                val isLandAndHalfScreen = DimensionUtil.isLandAndHalfScreen(view.context)
                val position = getChildAdapterPosition(view)
                outRect.left = if (position == 0) {
                    if (isLandAndHalfScreen || isLandAndOneThirdScreen) {
                        resources.getDimensionPixelSize(R.dimen.dp_30)
                    } else {
                        startEndInterval
                    }
                } else {
                    if (isLandAndHalfScreen || isLandAndOneThirdScreen) {
                        resources.getDimensionPixelSize(R.dimen.dp_20)
                    } else {
                        horizontalInterval
                    }
                }
                outRect.right = if (colorList.size == position + 1) {
                    startEndInterval
                } else {
                    0
                }
            }
        })
        layoutManager = LinearLayoutManager(context, HORIZONTAL, false)
    }
}

class ColorAdapter(
    initialPosition: Int,
    val colors: List<String>,
    private val onClick: (position: Int) -> Unit
) : RecyclerView.Adapter<ColorAdapter.ColorHolder>() {

    var currentSelectPosition: Int = initialPosition
        set(value) {
            if (field != value) {
                notifyItemChanged(field)
                field = value
                notifyItemChanged(value)
                onClick(value)
            }
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ColorHolder {
        val itemBinding =
            ColorSelectItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ColorHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: ColorHolder, position: Int) {
        holder.bind(colors[position], currentSelectPosition == position) {
            onClick(position)
        }
    }

    override fun getItemCount(): Int = colors.size

    class ColorHolder(private val binding: ColorSelectItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        private val setMarginForColorSelectItem = 1
        fun bind(color: String, selected: Boolean = false, onClick: () -> Unit) {
            binding.color.apply {
                setPadding(setMarginForColorSelectItem)
                setBackgroundColor(Paper.getBuiltinBackgroundColor(color))
                strokeWidth = if (selected) {
                    setImageDrawable(
                        AppCompatResources.getDrawable(
                            context,
                            R.drawable.color_selected
                        )
                    )
                    0F
                } else {
                    setImageDrawable(null)
                    1F
                }
                setOnClickListener {
                    onClick()
                }
            }
        }
    }
}


package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import androidx.recyclerview.widget.RecyclerView.LayoutManager
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.layoutmanager.FixFlexboxLayoutManager
import com.topstack.kilonotes.base.ktx.isLayoutRtl
import com.topstack.kilonotes.base.note.snippet.data.SnippetTag
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.animation.RotateAnimationUtils
import com.topstack.kilonotes.databinding.NoteSnippetLabelItemBinding
import com.topstack.kilonotes.databinding.NoteSnippetLabelSelectViewBinding
import com.topstack.kilonotes.infra.util.AppUtils

class NoteSnippetLabelSelectView(
    context: Context,
    attrs: AttributeSet
) : ConstraintLayout(context, attrs) {

    companion object {
        private val LABEL_LIST_RECYCLER_VIEW_FIRST_ROW_MARGIN_END =
            AppUtils.appContext.resources.getDimension(R.dimen.dp_79)
        private val LABEL_LIST_RECYCLER_VIEW_MARGIN_START =
            AppUtils.appContext.resources.getDimension(R.dimen.dp_20)
    }

    private var binding: NoteSnippetLabelSelectViewBinding
    private var labelAdapter: NoteSnippetLabelAdapter? = null
    private var onLabelClickListener: ((label: SnippetTag) -> Unit)? = null
    private var onFoldIconClickListener: (() -> Unit)? = null
    private var onSnippetLabelRecyclerViewLayoutChangeListener: OnLayoutChangeListener? = null

    private var firstRowLastItemPosition = -1
    private var firstRowLastItemMarginEnd = 0

    private val HORIZONTAL_DIRECTION = 0
    private val VERTICAL_DIRECTION = 1


    private val decoration = object : ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val position = parent.getChildAdapterPosition(view)
            val isLayoutRtl = parent.isLayoutRtl()
            val start = if (isFold && position == 0) {
                LABEL_LIST_RECYCLER_VIEW_MARGIN_START.toInt()
            } else {
                0
            }
            val end = context.resources.getDimension(R.dimen.dp_12).toInt()
            outRect.left = if (isLayoutRtl) end else start
            outRect.right = if (isLayoutRtl) start else end
            outRect.top = context.resources.getDimension(R.dimen.dp_6).toInt()
            outRect.bottom = context.resources.getDimension(R.dimen.dp_6).toInt()
        }
    }

    private val firstRowLastItemDecoration = object : ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            if (firstRowLastItemPosition > 0) {
                val position = parent.getChildAdapterPosition(view)
                if (position == firstRowLastItemPosition) {
                    if (parent.isLayoutRtl()) {
                        outRect.left = firstRowLastItemMarginEnd
                    } else {
                        outRect.right = firstRowLastItemMarginEnd
                    }
                }
            }
        }
    }

    //竖向折叠/展开
    private var isFold: Boolean = true
        set(value) {
            if (field != value) {
                field = value
                RotateAnimationUtils.startRotateValueAnimator(
                    binding.isFold,
                    200,
                    isReverse = value
                )
                transitLayoutWithIsFoldChange()
                updateFirstRowLastItemDecoration()
                if (value) {
                    labelAdapter?.let { adapter ->
                        val position = adapter.getCurrentSelectLabelPosition()
                        if (position >= 0) {
                            binding.recyclerViewLabel.overScrollRecyclerView.scrollToPosition(
                                position
                            )
                        }
                    }
                }
            }
        }

    //横向延长
    private var isExtended: Boolean = false
        set(value) {
            field = value
            transitLayoutWithIsExtendChange()
            transitLayoutWithIsFoldChange()
            updateFirstRowLastItemDecoration()
        }

    //选项是否可点击
    private var isItemClickable: Boolean = true
        set(value) {
            field = value
            labelAdapter?.setIsItemClickable(value)
        }

    var isAnimationRunning = false

    init {
        binding =
            NoteSnippetLabelSelectViewBinding.inflate(LayoutInflater.from(context), this, true)
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        binding.isFold.setOnClickListener {
            if (isAnimationRunning) return@setOnClickListener
            onFoldIconClickListener?.invoke()
        }
    }

    fun setUpRecyclerView(
        isFold: Boolean = true,
        isExtended: Boolean = false,
        isItemClickable: Boolean = true,
        labelList: List<SnippetTag>,
        initialSelectedLabel: SnippetTag
    ) {
        this.isFold = isFold
        this.isExtended = isExtended
        labelAdapter =
            NoteSnippetLabelAdapter(context, labelList, initialSelectedLabel, onLabelClickListener)
        binding.recyclerViewLabel.overScrollRecyclerView.stopScroll()
        // todo: update refresh data solution
        binding.recyclerViewLabel.overScrollRecyclerView.apply {
            this.adapter = labelAdapter
            transitLayoutWithIsExtendChange()
            transitLayoutWithIsFoldChange()
            updateFirstRowLastItemDecoration()
        }
        this.isItemClickable = isItemClickable
    }


    private fun transitLayoutWithIsExtendChange() {
        val recyclerViewLayoutParams =
            binding.recyclerViewLabel.layoutParams as ConstraintLayout.LayoutParams
        if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
            recyclerViewLayoutParams.matchConstraintMaxHeight =
                resources.getDimension(R.dimen.dp_110).toInt()
        } else {
            if (isExtended) {
                recyclerViewLayoutParams.matchConstraintMaxHeight =
                    resources.getDimension(R.dimen.dp_200).toInt()
            } else {
                recyclerViewLayoutParams.matchConstraintMaxHeight =
                    resources.getDimension(R.dimen.dp_380).toInt()
            }
        }

        binding.recyclerViewLabel.layoutParams = recyclerViewLayoutParams
        binding.recyclerViewLabel.overScrollRecyclerView.removeItemDecoration(decoration)
        binding.recyclerViewLabel.overScrollRecyclerView.addItemDecoration(decoration)
    }

    private fun transitLayoutWithIsFoldChange() {
        val layoutManager: LayoutManager = if (isFold) {
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        } else {
            FixFlexboxLayoutManager(context)
        }
        binding.recyclerViewLabel.overScrollRecyclerView.layoutManager = layoutManager
        binding.recyclerViewLabel.setRecyclerViewOverScrollBehavior(
            if (isFold)
                HORIZONTAL_DIRECTION
            else
                VERTICAL_DIRECTION
        )
        val recyclerViewLayoutParams =
            binding.recyclerViewLabel.layoutParams as LayoutParams
        if (isFold) {
            recyclerViewLayoutParams.marginStart =
                context.resources.getDimension(R.dimen.dp_0).toInt()
            recyclerViewLayoutParams.marginEnd =
                LABEL_LIST_RECYCLER_VIEW_FIRST_ROW_MARGIN_END.toInt()
        } else {
            recyclerViewLayoutParams.marginStart =
                LABEL_LIST_RECYCLER_VIEW_MARGIN_START.toInt()
            recyclerViewLayoutParams.marginEnd =
                context.resources.getDimension(R.dimen.dp_0).toInt()
        }
        binding.recyclerViewLabel.layoutParams = recyclerViewLayoutParams
        binding.recyclerViewLabel.overScrollRecyclerView.removeItemDecoration(decoration)
        binding.recyclerViewLabel.overScrollRecyclerView.addItemDecoration(decoration)
    }

    private fun updateFirstRowLastItemDecoration() {
        binding.recyclerViewLabel.overScrollRecyclerView.removeItemDecoration(
            firstRowLastItemDecoration
        )
        if (isFold) {
            firstRowLastItemPosition = -1
            firstRowLastItemMarginEnd = 0
        } else {
            binding.recyclerViewLabel.overScrollRecyclerView.post {
                computeFirstRowLastItemPosition(binding.recyclerViewLabel.overScrollRecyclerView)
            }
        }
        updateIsFoldIconVisibility()
    }


    //计算最后一个能够不被遮挡、完全显示的item的下标，并对其右侧进行填充，使得下一个item换行
    private fun computeFirstRowLastItemPosition(recyclerView: RecyclerView) {
        var firstColumnWidth = if (isFold) {
            LABEL_LIST_RECYCLER_VIEW_MARGIN_START.toInt()
        } else {
            0
        }
        val minDistance = context.resources.getDimension(R.dimen.dp_79)
        for (i in 0 until recyclerView.childCount) {
            //以遍历宽度总和+当前item的宽度+当前item的装饰器宽度
            firstColumnWidth =
                firstColumnWidth + recyclerView.getChildAt(i).width + context.resources.getDimension(
                    R.dimen.dp_12
                ).toInt()
            val distance =
                recyclerView.width - firstColumnWidth
            if (i < recyclerView.childCount - 1) {
                //当前还未被遮挡但加入下一个item的宽度后会被遮挡时
                if (distance <= minDistance || (distance > minDistance && (distance - recyclerView.getChildAt(
                        i + 1
                    ).width) < minDistance)
                ) {
                    //记录最后一个能够完全显示的item的下标
                    firstRowLastItemPosition = i
                    //记录最后一个能够完全显示的item的右侧与父布局右侧剩余距离
                    firstRowLastItemMarginEnd = distance
                    recyclerView.removeItemDecoration(firstRowLastItemDecoration)
                    //添加装饰器把最后一个item右侧距离填满，使得下一个item换行
                    recyclerView.addItemDecoration(firstRowLastItemDecoration)
                    break
                }
            }
        }
    }

    private fun updateIsFoldIconVisibility() {
        binding.recyclerViewLabel.overScrollRecyclerView.post {
            var itemViewWidth = if (isFold) {
                LABEL_LIST_RECYCLER_VIEW_MARGIN_START
            } else {
                LABEL_LIST_RECYCLER_VIEW_FIRST_ROW_MARGIN_END
            }
            for (i in 0 until binding.recyclerViewLabel.overScrollRecyclerView.childCount) {
                itemViewWidth =
                    itemViewWidth + binding.recyclerViewLabel.overScrollRecyclerView.getChildAt(i).width + context.resources.getDimension(
                        R.dimen.dp_12
                    ).toInt()
            }
            binding.isFold.isVisible =
                itemViewWidth > binding.recyclerViewLabel.overScrollRecyclerView.width
        }
    }


    fun setOnLabelClickListener(action: (label: SnippetTag) -> Unit) {
        onLabelClickListener = action
    }

    fun setOnFoldIconClickListener(action: () -> Unit) {
        onFoldIconClickListener = action
    }

    fun setIsFold(isFold: Boolean) {
        this.isFold = isFold
    }

    fun setIsExtended(isExtended: Boolean) {
        this.isExtended = isExtended
    }

    fun setIsItemClickable(isItemClickable: Boolean) {
        this.isItemClickable = isItemClickable
    }

    fun removeFirstRowLastPositionDecoration() {
        binding.recyclerViewLabel.overScrollRecyclerView.removeItemDecoration(
            firstRowLastItemDecoration
        )
    }

    fun setOnSnippetLabelRecyclerViewLayoutChangeListener(listener: OnLayoutChangeListener) {
        binding.recyclerViewLabel.removeOnLayoutChangeListener(
            onSnippetLabelRecyclerViewLayoutChangeListener
        )
        onSnippetLabelRecyclerViewLayoutChangeListener = listener
        binding.recyclerViewLabel.addOnLayoutChangeListener(listener)
    }

    fun setCurrentSelectedLabel(label: SnippetTag) {
        labelAdapter?.setCurrentSelectedLabel(label)
    }

}


class NoteSnippetLabelAdapter(
    val context: Context,
    private val labelList: List<SnippetTag>,
    private val initialSelectedLabel: SnippetTag,
    private val onLabelClickListener: ((label: SnippetTag) -> Unit)? = null
) : RecyclerView.Adapter<NoteSnippetLabelAdapter.LabelViewHolder>() {

    private var currentSelectLabel: SnippetTag = initialSelectedLabel
        set(value) {
            if (field != value) {
                val oldPosition = findLabelPositionInLabelList(field)
                val newPosition = findLabelPositionInLabelList(value)
                field = value
                if (oldPosition in labelList.indices) {
                    notifyItemChanged(oldPosition)
                }
                if (newPosition in labelList.indices) {
                    notifyItemChanged(newPosition)
                }
            }
        }

    private var isItemClickable: Boolean = true
        set(value) {
            if (field != value) {
                field = value
            }
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LabelViewHolder {
        return LabelViewHolder(NoteSnippetLabelItemBinding.inflate(LayoutInflater.from(context)))
    }

    override fun onBindViewHolder(holder: LabelViewHolder, position: Int) {
        holder.name.text = labelList[position].getNameString()
        if (currentSelectLabel.tagId == labelList[position].tagId) {
            holder.name.isSelected = true
            holder.name.setTextColor(Color.WHITE)
        } else {
            holder.name.isSelected = false
            holder.name.setTextColor(context.resources.getColor(R.color.text_secondary, null))
        }
        holder.name.setOnClickListener {
            if (isItemClickable) {
                currentSelectLabel = labelList[position]
                onLabelClickListener?.invoke(labelList[position])
            }
        }
    }

    override fun getItemCount(): Int {
        return labelList.size
    }

    private fun findLabelPositionInLabelList(label: SnippetTag): Int {
        var result = -1
        labelList.forEachIndexed { index, snippetTag ->
            if (label.tagId == snippetTag.tagId) {
                result = index
            }
        }
        return result
    }

    fun getCurrentSelectLabelPosition(): Int {
        var result = -1
        labelList.forEachIndexed { index, snippetTag ->
            if (currentSelectLabel.tagId == snippetTag.tagId) {
                result = index
            }
        }
        return result
    }

    fun setIsItemClickable(isItemClickable: Boolean) {
        this.isItemClickable = isItemClickable
    }

    fun setCurrentSelectedLabel(label: SnippetTag) {
        this.currentSelectLabel = label
    }


    class LabelViewHolder(binding: NoteSnippetLabelItemBinding) : ViewHolder(binding.root) {
        val name = binding.labelName
    }
}
package com.topstack.kilonotes.pad.select.adapter

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseExpandableListAdapter
import android.widget.ImageView
import android.widget.TextView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.fonts.CirclePgBar
import com.topstack.kilonotes.base.fonts.FontFamilyInfo
import com.topstack.kilonotes.base.fonts.FontInfo
import com.topstack.kilonotes.base.fonts.FontManager
import com.topstack.kilonotes.base.fonts.FontTypeGroup
import com.topstack.kilonotes.base.fonts.IFontStateListener
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.databinding.DialogFontListChildBinding
import com.topstack.kilonotes.databinding.DialogFontListGroupBinding
import com.topstack.kilonotes.infra.network.NetworkUtils


class FontGroupExpandableListAdapter(
    private val context: Context,
    private val fontGroup: FontTypeGroup,
    private val position: Int,
    private val groupShow: (Int) -> Boolean,
    private val fontDownLoad: (Int) -> Boolean,
) : BaseExpandableListAdapter() {
    private val inflater: LayoutInflater = LayoutInflater.from(context)
    private lateinit var groupBinding: DialogFontListGroupBinding
    private lateinit var childBinding: DialogFontListChildBinding


    var onFontChangeAction: ((FontInfo) -> Unit)? = null
    var fontDown:((String,IFontStateListener) -> Unit)? =null

    override fun getGroupCount(): Int {
        return 1
    }

    override fun getChildrenCount(groupPosition: Int): Int {
        return fontGroup.childList[groupPosition].size
    }

    override fun getGroup(groupPosition: Int): Any {
        return fontGroup.groupName[groupPosition]
    }

    override fun getChild(groupPosition: Int, childPosition: Int): Any {
        return fontGroup.childList[groupPosition][childPosition]
    }

    override fun getGroupId(groupPosition: Int): Long {
        return groupPosition.toLong()
    }

    override fun getChildId(groupPosition: Int, childPosition: Int): Long {
        return childPosition.toLong()
    }

    override fun hasStableIds(): Boolean {
        return false
    }

    override fun getGroupView(
        groupPosition: Int,
        isExpanded: Boolean,
        convertView: View?,
        parent: ViewGroup?
    ): View {
        var convertView = convertView
        val groupHolder: ExpandableGroupHolder
        if (convertView == null) {
            groupBinding = DialogFontListGroupBinding.inflate(inflater)
            convertView = groupBinding.root
            groupHolder = ExpandableGroupHolder(groupBinding)
            convertView.tag = groupHolder
        } else {
            groupHolder = convertView.tag as ExpandableGroupHolder
        }
        val groupTitle = getGroup(groupPosition) as String
        if (fontGroup.isExpand) {
            groupHolder.groupState.setImageResource(R.drawable.font_list_close)
        } else {
            groupHolder.groupState.setImageResource(R.drawable.font_list_open)
        }
        groupHolder.groupProgress.progress = 0
        groupHolder.groupProgress.visibility = View.GONE
        if (fontGroup.childList[0].isEmpty()) {
            groupHolder.groupState.visibility = View.GONE
            groupHolder.groupView.setOnClickListener {
                fontGroup.fontInfo.let { it1 ->
                    onFontChangeAction?.invoke(it1)
                }
            }
        } else {
            groupHolder.groupView.setOnClickListener {
                (fontGroup.fontInfo as FontFamilyInfo).getDefaultFontInfo()?.let { it1 ->
                    onFontChangeAction?.invoke(it1)
                }
            }
        }

        val fontTypeface = fontGroup.fontInfo.getTypeface()
        val isAvailableFonts = fontGroup.fontInfo.isAvailableFonts()
        if (fontTypeface != null && isAvailableFonts) {
            groupHolder.groupView.typeface = fontTypeface
            groupHolder.groupState.setOnClickListener {
                groupShow.invoke(position)
            }
        } else {
            groupHolder.groupView.setOnClickListener {
                fontDownloadClick(groupHolder)
            }
            if (fontGroup.childList[0].isEmpty()) {
                if (fontGroup.fontInfo.isDownloading()){
                    groupHolder.groupProgress.progress = fontGroup.fontInfo.downloadProgress
                    groupHolder.groupState.visibility = View.GONE
                    groupHolder.groupProgress.visibility = View.VISIBLE
                }else{
                    groupHolder.groupState.setImageResource(R.drawable.font_down)
                    groupHolder.groupProgress.visibility = View.GONE
                    groupHolder.groupState.visibility = View.VISIBLE
                }
            }
            groupHolder.groupState.setOnClickListener {
                fontDownloadClick(groupHolder)
            }
        }
        groupHolder.groupView.text = groupTitle
        return convertView

    }


    override fun getChildView(
        groupPosition: Int,
        childPosition: Int,
        isLastChild: Boolean,
        convertView: View?,
        parent: ViewGroup?
    ): View {
        var convertView = convertView
        val childHolder: ExpandableChildHolder
        if (convertView == null) {
            childBinding = DialogFontListChildBinding.inflate(inflater)
            convertView = childBinding.root
            childHolder = ExpandableChildHolder(childBinding)
            convertView.tag = childHolder
        } else {
            childHolder = convertView.tag as ExpandableChildHolder
        }
        val childListText = getChild(groupPosition, childPosition) as String
        childHolder.fontTypeView.text = childListText
        childHolder.fontTypeView.setOnClickListener {
            (fontGroup.fontInfo as? FontFamilyInfo)?.getFontInfo(childListText)?.let { it1 ->
                onFontChangeAction?.invoke(it1)
                Log.i("suc", "childHolder: ")

            }
        }
        val fontTypeface = (fontGroup.fontInfo as? FontFamilyInfo)?.getFontInfo(childListText)?.getTypeface()
        childHolder.fontTypeView.typeface = fontTypeface
        return convertView
    }

    override fun isChildSelectable(groupPosition: Int, childPosition: Int): Boolean {
        return true
    }


    //组单元
    class ExpandableGroupHolder(binding:DialogFontListGroupBinding) {
        internal var groupPreView: ImageView = binding.fontGroupPreView
        internal var groupProgress: CirclePgBar = binding.fontGroupProgress.apply {
            radius = context.resources.getDimensionPixelSize(R.dimen.dp_12)
            paintWidth = context.resources.getDimensionPixelSize(R.dimen.dp_3)
        }
        internal var groupView: TextView = binding.fontGroupName
        internal var groupState: ImageView = binding.fontGroupState
    }

    //子单元
    class ExpandableChildHolder(binding:DialogFontListChildBinding) {
        internal var fontTypeView: TextView =binding.fontChildList
        internal var fontTypeState: ImageView = binding.fontChildState
    }

    private fun fontDownloadClick(groupHolder: ExpandableGroupHolder) {
        if (NetworkUtils.isNetworkAvailable()) {
            groupHolder.groupState.visibility = View.INVISIBLE
            groupHolder.groupProgress.visibility = View.VISIBLE
            fontGroup.fontInfo.downloadProgress = 0
            val listener = object : IFontStateListener {
                override fun onDownloading(progress: Int) {
                    groupHolder.groupProgress.progress = progress
                    fontGroup.fontInfo.downloadProgress = progress
                }

                override fun onDownloadResult(result: Boolean) {
                    if (!result) {
                        ToastUtils.topCenter(context, R.string.font_download_no_internet)
                    }
                    fontGroup.fontInfo.downloadProgress = FontManager.DEF_FONT_DOWNLOAD_PROGRESS
                    notifyDataSetChanged()
                    fontDownLoad.invoke(position)
                }

                override fun onDownloadError(error: Int) {
                    fontGroup.fontInfo.downloadProgress = FontManager.DEF_FONT_DOWNLOAD_PROGRESS
                    ToastUtils.topCenter(context, R.string.download_fail)
                }
            }
            fontDown?.invoke(fontGroup.fontInfo.url, listener)
        } else {
            ToastUtils.topCenter(context, R.string.toast_no_internet)
        }
    }

}




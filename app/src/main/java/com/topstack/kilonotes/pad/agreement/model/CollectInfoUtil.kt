package com.topstack.kilonotes.pad.agreement.model

import com.topstack.kilonotes.infra.config.PersonalInfoCollectionCounter

class CollectInfoUtil {
    fun getPersonalDataInfos(): List<DetailedInfo> {
        return listOf(
            DetailedInfo(
                "基本资料",
                "基本资料",
                "创建账号、登录、身份验证以及鉴权",
                "登录与绑定账号",
                PersonalInfoCollectionCounter.loginCount
            )
        )
    }

    fun getUseProcedureInfos(): List<DetailedInfo> {
        return listOf(
            DetailedInfo(
                "交易信息",
                "交易信息",
                "记录历史交易信息",
                "订购VIP、手账本、贴纸、模板等资源",
                PersonalInfoCollectionCounter.paymentCount
            ),
            DetailedInfo(
                "网盘账号信息",
                "网盘账号信息",
                "笔记数据备份到网盘",
                "网盘备份",
                PersonalInfoCollectionCounter.cloudBackupLoginCount
            ),
            DetailedInfo(
                "笔记信息",
                "笔记信息",
                "根据关键字信息搜索笔记",
                "搜索",
                PersonalInfoCollectionCounter.searchCount

            )
        )
    }

    fun getUsePhoneProcedureInfos(): List<DetailedInfo> {
        return listOf(
            DetailedInfo(
                "交易信息",
                "交易信息",
                "记录历史交易信息",
                "订购VIP、手账本、贴纸、模板等资源",
                PersonalInfoCollectionCounter.paymentCount
            ),
            DetailedInfo(
                "笔记信息",
                "笔记信息",
                "根据关键字信息搜索笔记",
                "搜索",
                PersonalInfoCollectionCounter.searchCount

            )
        )
    }

    fun getDeviceInfoInfos(): List<DetailedInfo> {
        return listOf(
            DetailedInfo(
                "设备操作系统",
                "设备操作系统",
                "适配您当前设备",
                "应用第一次安装启动时",
                1
            ),
            DetailedInfo(
                "设备系统版本",
                "设备系统版本",
                "适配您当前设备",
                "应用第一次安装启动时",
                1
            ),
            DetailedInfo(
                "设备分辨率",
                "设备分辨率",
                "适配您当前设备",
                "应用第一次安装启动时",
                1
            ),
            DetailedInfo(
                "设备语言",
                "设备语言",
                "适配您当前设备",
                "应用第一次安装启动时",
                1
            )
        )
    }

    fun getAppInfoInfos(): List<DetailedInfo> {
        return listOf(
            DetailedInfo(
                "APP下载渠道",
                "APP下载渠道",
                "分析与统计",
                "应用第一次安装启动时",
                1
            ),
            DetailedInfo(
                "APP版本号",
                "APP版本号",
                "分析与统计",
                "应用第一次安装启动时",
                1
            )
        )
    }

}
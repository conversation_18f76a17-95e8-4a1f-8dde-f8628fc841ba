package com.topstack.kilonotes.pad.select.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.topstack.kilonotes.R
import com.topstack.kilonotes.databinding.FragmentAlbumListRvItemBinding
import com.topstack.kilonotes.base.imagefetch.model.Album

class AlbumListAdapter(
    inputAlbumList: List<Album>,
    private val context: Context,
    private val onClick: (album: Album) -> Unit
) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val albumList = inputAlbumList.toList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view = FragmentAlbumListRvItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PhotoListViewHolder(view)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is PhotoListViewHolder) {
            val imageView = holder.imageView
            val curAlbum = albumList[position]
            if (curAlbum.images.isNotEmpty()) {
                val firstUri = curAlbum.images.first().uri
                holder.nameTextView.text = curAlbum.name
                holder.countTextView.text = curAlbum.count.toString()
                Glide.with(context)
                    .load(firstUri)
                    .placeholder(R.drawable.note_main_sidebar_pic_default)
                    .error(R.drawable.note_main_sidebar_pic_error)
                    .apply(
                        RequestOptions().transform(
                            CenterCrop(), RoundedCorners(
                                context.resources.getDimension(
                                    R.dimen.dp_12
                                ).toInt()
                            )
                        )
                    )
                    .into(imageView)
            }
            imageView.setOnClickListener {
                onClick(curAlbum)
            }
        }
    }

    override fun getItemCount(): Int {
        return albumList.size
    }


    private class PhotoListViewHolder(binding: FragmentAlbumListRvItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val imageView: ImageView = binding.itemPhoto
        val nameTextView: TextView = binding.photoName
        val countTextView: TextView = binding.photoCount
    }

}
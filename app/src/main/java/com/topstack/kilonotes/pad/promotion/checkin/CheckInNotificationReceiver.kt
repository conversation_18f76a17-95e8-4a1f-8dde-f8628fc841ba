package com.topstack.kilonotes.pad.promotion.checkin

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.topstack.kilonotes.R
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.pad.MainActivity
import java.time.LocalTime

/**
 *
 */
class CheckInNotificationReceiver : BroadcastReceiver() {
    companion object {
        const val EXTRA_FORM_CHECK_IN_NOTIFICATION = "fromCheckInNotification"
    }

    private val TAG = "CheckInNotificationReceiver"

    override fun onReceive(context: Context?, intent: Intent?) {
        LogHelper.d(TAG, "receive show notification request")
        GainNoteLimitsPromotionManager.setupCheckInNotification()
        if (context == null) return
        notifyToCheckIn(context)
    }

    private fun notifyToCheckIn(context: Context) {
        val scheduleTime = LocalTime.of(19, 0)
        val now = LocalTime.now()
        if (now >= scheduleTime && now < scheduleTime.plusMinutes(1)) {
            val channelId = context.getString(R.string.check_in)
            val notificationManager = NotificationManagerCompat.from(context)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Create the NotificationChannel.
                val name = context.getString(R.string.check_in)
                val descriptionText = context.getString(R.string.check_in_notice)
                val importance = NotificationManager.IMPORTANCE_DEFAULT
                val mChannel = NotificationChannel(channelId, name, importance)
                mChannel.description = descriptionText
                notificationManager.createNotificationChannel(mChannel)
            }

            val intent = Intent(context, MainActivity::class.java).apply {
                putExtra(EXTRA_FORM_CHECK_IN_NOTIFICATION, true)
            }
            val pendingIntent: PendingIntent =
                PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)

            val builder = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentText(context.getString(R.string.check_in_notification_content))
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)

            notificationManager.notify(R.id.check_in_notice_switch, builder.build())
        }
    }
}
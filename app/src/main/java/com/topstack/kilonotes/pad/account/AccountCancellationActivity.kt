package com.topstack.kilonotes.pad.account

import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import android.view.View
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.lifecycle.lifecycleScope
import com.topstack.kilonotes.R
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.account.UserManager.loggedInUser
import com.topstack.kilonotes.base.component.activity.BaseDialogActivity
import com.topstack.kilonotes.base.doodle.manager.modelmager.Clipboard
import com.topstack.kilonotes.base.util.ClipboardUtil
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.databinding.AccountCancellationActivityBinding
import kotlinx.coroutines.launch

class AccountCancellationActivity : BaseDialogActivity() {
    private lateinit var binding: AccountCancellationActivityBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = AccountCancellationActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.close.setOnClickListener {
            finish()
        }
        binding.content.movementMethod = ScrollingMovementMethod.getInstance();
        val loggedInUser = UserManager.loggedInUser
        if (loggedInUser != null) {
            binding.cancelUserId.text = String.format(
                resources.getString(R.string.cancel_user_id),
                loggedInUser.userId
            )
            binding.copy.setOnClickListener {
                ClipboardUtil.newPlain(this, binding.cancelUserId.text.toString())
                ToastUtils.topCenter(this, R.string.copy_success)
            }
        } else {
            binding.cancelUserId.visibility = View.GONE
            binding.copy.visibility = View.GONE
        }
        binding.knowContainer.setOnClickListener {
            finish()
        }

        setFinishOnTouchOutside(false)
    }

    override fun computeHeight(heightPixels: Int): Int {
        return resources.getDimensionPixelOffset(R.dimen.dp_660)
    }

    override fun computeWidth(widthPixels: Int): Int {
        return resources.getDimensionPixelOffset(R.dimen.dp_700)
    }
}
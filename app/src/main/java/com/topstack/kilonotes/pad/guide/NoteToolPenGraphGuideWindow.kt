package com.topstack.kilonotes.pad.guide

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.widget.PopupWindow
import com.bumptech.glide.Glide
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.databinding.NoteToolPenGraphGuideBinding

class NoteToolPenGraphGuideWindow(val context: Context, val isPortraitAndHalfScreen: Boolean) :
    PopupWindow() {
    private val binding: NoteToolPenGraphGuideBinding by lazy {
        NoteToolPenGraphGuideBinding.inflate(LayoutInflater.from(context))
    }

    init {
        width = LayoutParams.WRAP_CONTENT
        height = LayoutParams.WRAP_CONTENT
        isFocusable = true
        isOutsideTouchable = true
        if (isPortraitAndHalfScreen) {
            binding.arrow.apply {
                scaleY = -1f
                setMargins(
                    binding.arrow.left,
                    binding.arrow.top + context.resources.getDimensionPixelSize(R.dimen.dp_71),
                    binding.arrow.right,
                    binding.arrow.bottom
                )
            }
        }
        binding.arrow.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        binding.oneThirdScreenArrow.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        contentView = binding.root
    }

    fun showGuideWindow(view: View, isOneThirdScreen: Boolean, xOffset: Int, yOffset: Int) {
        view.post {
            if (view.windowToken == null) return@post
            val context = view.context ?: return@post
            val viewLocation = IntArray(2)
            view.getLocationInWindow(viewLocation)
            if (isOneThirdScreen) {
                binding.oneThirdScreenGuide.visibility = View.VISIBLE
                binding.graphGuide.visibility = View.GONE
                Glide.with(context).asGif().load(R.drawable.pen_window_guide_gif)
                    .into(binding.guideOneThirdGif)
                Glide.with(context).clear(binding.guideGif)
            } else {
                binding.oneThirdScreenGuide.visibility = View.GONE
                binding.graphGuide.visibility = View.VISIBLE
                Glide.with(context).asGif().load(R.drawable.pen_window_guide_gif)
                    .into(binding.guideGif)
                Glide.with(context).clear(binding.guideOneThirdGif)
            }
            showAtLocation(
                view,
                Gravity.NO_GRAVITY,
                if (KiloApp.isLayoutRtl) viewLocation[0] - xOffset else viewLocation[0] + xOffset,
                viewLocation[1] + yOffset
            )
        }
    }
}
package com.topstack.kilonotes.pad.guide

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.ktx.adjustBackgroundRtlOrLtr
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.FileManagerDesktopOutlineGuideBinding
import com.topstack.kilonotes.pad.component.CommonGuideCircularView

class FileManagerDesktopOutlineGuideWindow(var context: Context, view: View) : PopupWindow() {

    companion object {
        const val DEFAULT_TIPS_LINE = 1
    }

    private var rootView: View
    private var know: TextView
    private var knowBg: ImageView
    private var tips: TextView
    private var arrow: ImageView

    init {
        val binding = FileManagerDesktopOutlineGuideBinding.inflate(LayoutInflater.from(context))
        rootView = binding.root
        know = binding.know
        knowBg = binding.knowBg
        tips = binding.tips
        arrow = binding.arrow
        isFocusable = true
        isOutsideTouchable = false
        contentView = rootView
        width = ViewGroup.LayoutParams.MATCH_PARENT
        height = ViewGroup.LayoutParams.MATCH_PARENT
        rootView.setOnClickListener {
            dismiss()
        }
        know.setOnClickListener {
            dismiss()
        }
        val viewLocation = IntArray(2)
        view.getLocationInWindow(viewLocation)
        val circular =
            rootView.findViewById<CommonGuideCircularView>(R.id.circular)
        circular.setSettingOffsets(
            viewLocation[0].toFloat() + context.resources.getDimensionPixelSize(
                R.dimen.dp_33
            ),
            viewLocation[1].toFloat() + context.resources.getDimensionPixelSize(
                R.dimen.dp_33
            )
        )
        updateGuideWindowContent(
            rootView as ConstraintLayout,
            viewLocation[0].toFloat() + view.width / 2,
            viewLocation[1].toFloat() + view.height + context.resources.getDimensionPixelSize(
                R.dimen.dp_23
            )
        )
        if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
            tips.apply {
                y -= (getTitleLines() - DEFAULT_TIPS_LINE) * context.resources.getDimension(R.dimen.dp_23)
            }
        }
        rootView.measure(
            View.MeasureSpec.UNSPECIFIED,
            View.MeasureSpec.UNSPECIFIED
        )
    }

    private fun updateGuideWindowContent(
        constraintLayout: ConstraintLayout,
        distanceX: Float,
        distanceY: Float,
        needOffsetWindowWidth: Boolean = true
    ) {
        arrow.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        knowBg.adjustBackgroundRtlOrLtr(KiloApp.isLayoutRtl)
        constraintLayout.findViewById<ConstraintLayout>(R.id.desktop_outline_guide_constraintLayout)
            .apply {
                post {
                    if (isAttachedToWindow) {
                        if (KiloApp.isLayoutRtl && needOffsetWindowWidth) {
                            x = distanceX - width
                            y = distanceY
                        } else {
                            x = distanceX
                            y = distanceY
                        }
                    }
                    <EMAIL> = View.VISIBLE
                }
            }
    }

    private fun getTitleLines(): Int {
        return tips.lineCount
    }

}
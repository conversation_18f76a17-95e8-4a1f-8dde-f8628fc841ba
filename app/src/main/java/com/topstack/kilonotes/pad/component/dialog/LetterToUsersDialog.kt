package com.topstack.kilonotes.pad.component.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.BaseDialogFragment
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.i18n.isChineseLanguage
import com.topstack.kilonotes.base.util.DimensionUtil
import kotlin.math.max

class LetterToUsersDialog : BaseDialogFragment() {


    companion object {
        const val TAG = "LetterToUsersDialog"
    }

    private val close: ImageView by lazy { requireView().findViewById(R.id.close) }
    private val image: ImageView by lazy { requireView().findViewById(R.id.image) }

    private lateinit var displayMetrics: DisplayMetrics
    private val isWidthLayout
        get() = displayMetrics.widthPixels > displayMetrics.heightPixels
    private var contentViewMeasuredWidth = 0
    private var contentViewMeasuredHeight = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
        displayMetrics = DimensionUtil.getScreenDimensions(requireContext())
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_letter_to_users, container, false).apply {
            measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            contentViewMeasuredWidth = measuredWidth
            contentViewMeasuredHeight = measuredHeight
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Glide.with(requireContext())
            .load(if (isChineseLanguage()) R.drawable.dialog_letter_to_users_image_zh else R.drawable.dialog_letter_to_users_image_en)
            .into(image)

        close.setOnClickListener {
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setLayout(displayMetrics.widthPixels, displayMetrics.heightPixels)
            setGravity(Gravity.CENTER)
        }
        val padding = requireContext().resources.getDimensionPixelSize(R.dimen.dp_32)
        val widthRatio =
            (contentViewMeasuredWidth.toFloat() + padding * 2) / displayMetrics.widthPixels
        val heightRatio =
            (contentViewMeasuredHeight.toFloat() + padding * 2) / displayMetrics.heightPixels
        val maxRatio = max(widthRatio, heightRatio)
        if (maxRatio > 1) {
            image.layoutParams = image.layoutParams.apply {
                width = (contentViewMeasuredWidth / maxRatio).toInt()
            }
        }
    }


    override fun dismiss() {
        Preferences.needShowLetterToUsersDialog = false
        super.dismiss()
    }
}
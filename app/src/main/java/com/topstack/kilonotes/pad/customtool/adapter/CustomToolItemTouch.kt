package com.topstack.kilonotes.pad.customtool.adapter

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.base.ktx.setMargins

class CustomToolItemTouch : ItemTouchHelper.Callback() {
    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        val enableDrag =
            viewHolder is CustomToolsAdapter.CustomToolsViewHolder
        val dragFlags = if (enableDrag) {
            ItemTouchHelper.UP or ItemTouchHelper.DOWN
        } else {
            0
        }
        val swipeFlags = 0
        return makeMovementFlags(dragFlags, swipeFlags)
    }

    override fun isLongPressDragEnabled(): Boolean {
        return false
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        if (target is CustomToolsAdapter.CustomToolsViewHolder && viewHolder is CustomToolsAdapter.CustomToolsViewHolder) {
            if (target.canMove() && viewHolder.canMove()) {
                val fromPosition = viewHolder.bindingAdapterPosition
                val toPosition = target.bindingAdapterPosition
                target.onSwapCustomTool(fromPosition, toPosition)
                viewHolder.itemView.setMargins(0, 0, 0, 0)
                target.itemView.setMargins(0, 0, 0, 0)
                return true
            }
        }
        return false
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        if (viewHolder is CustomToolsAdapter.CustomToolsViewHolder) {
            viewHolder.onSwapFinish(viewHolder.bindingAdapterPosition)
        }
    }
}
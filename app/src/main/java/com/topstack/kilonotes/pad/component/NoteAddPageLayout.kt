package com.topstack.kilonotes.pad.component

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavDirections
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.handbook.model.NO_TEMPLATE_ASSOCIATED_NOTE_ID
import com.topstack.kilonotes.base.handbook.model.Template
import com.topstack.kilonotes.base.handbook.model.TemplateCategory
import com.topstack.kilonotes.base.handbook.model.TemplateCategoryWithList
import com.topstack.kilonotes.base.note.model.InsertPosition
import com.topstack.kilonotes.base.note.viewmodel.AddPageViewModel
import com.topstack.kilonotes.base.note.viewmodel.BaseTemplateViewModel
import com.topstack.kilonotes.base.note.viewmodel.NoteTemplateHelper
import com.topstack.kilonotes.base.note.viewmodel.NoteViewModel
import com.topstack.kilonotes.base.note.viewmodel.TemplateViewModel
import com.topstack.kilonotes.base.track.event.AddTemplateEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.base.vip.viewmodel.HandbookViewModel
import com.topstack.kilonotes.databinding.NoteAddPageLayoutBinding
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.pad.note.NoteEditorFragmentDirections
import com.topstack.kilonotes.pad.note.adapter.NoteAddPageEmptyDataAdapter
import com.topstack.kilonotes.pad.note.adapter.NoteAddPageSelectAddTypeAndColorAdapter
import com.topstack.kilonotes.pad.note.adapter.NoteAddTemplateAdapter
import com.topstack.kilonotes.pad.note.adapter.TemplateStorageNotEnoughAdapter
import com.topstack.kilonotes.phone.note.TemplateState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class NoteAddPageLayout : FrameLayout {
    constructor(context: Context, attr: AttributeSet?, style: Int) : super(context, attr, style)
    constructor(context: Context, attr: AttributeSet?) : this(context, attr, 0)
    constructor(context: Context) : this(context, null)

    var addPageCallback: ((Paper, Int) -> Boolean)? = null
    var importImagePageCallback: (() -> Unit)? = null
    var addTemplateCallback: ((Template, Document) -> Boolean)? = null
    var navigationCallback: ((NavDirections) -> Unit)? = null
    var onCloseCallback: (() -> Unit)? = null
    private val noteViewModel: NoteViewModel by (context as AppCompatActivity).viewModels()
    private val addPageViewModel: AddPageViewModel by (context as AppCompatActivity).viewModels()
    private val handbookViewModel: HandbookViewModel by (context as AppCompatActivity).viewModels()

    var insertPosition: InsertPosition?
        get() = addPageViewModel.insertPosition.value
        set(value) {
            addPageViewModel.changeInsertPosition(value!!)
        }

    private var concatAdapter: ConcatAdapter? = null
    private lateinit var addTemplateAdapter: NoteAddTemplateAdapter
    private var noteAddPageSelectAddTypeAndColorAdapter: NoteAddPageSelectAddTypeAndColorAdapter? =
        null
    val templateViewModel by (context as AppCompatActivity).viewModels<TemplateViewModel>()
    private val binding: NoteAddPageLayoutBinding =
        NoteAddPageLayoutBinding.inflate(LayoutInflater.from(context), this, true)

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        initLayoutParams()
        initListener()
        initAddPageList()
    }

    fun show(paper: Paper, document: Document) {
        addPageViewModel.changeInsertPosition(InsertPosition.NEXT)
        noteAddPageSelectAddTypeAndColorAdapter?.apply {
            updateInsertPosition(InsertPosition.NEXT)
            this.addPageCallback = <EMAIL>
            this.importImagePageCallback = <EMAIL>
            this.document = document
            refreshCurrentPaper(paper, document)
        }
    }

    private fun initListener() {
        binding.addPageBorder.onButtonClickedAction = {
            onCloseCallback?.invoke()
        }
    }

    private fun initLayoutParams() {
        if (DimensionUtil.isLandAndOneThirdScreen(context)
            || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)
        ) {
            val params = layoutParams
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            layoutParams = params
        } else {
            val params = layoutParams
            params.width = resources.getDimensionPixelSize(R.dimen.dp_581)
            layoutParams = params
        }
    }

    fun refreshPageList(paper: Paper, document: Document) {
        noteAddPageSelectAddTypeAndColorAdapter?.refreshCurrentPaper(paper, document)
    }


    private val templateObserver = Observer<List<TemplateCategoryWithList>> { list ->
        updateView(list.isEmpty())
        addTemplateAdapter.updateAll(list)
    }

    private fun changeTemplateObserver(isHorizontal: Boolean) {
        findViewTreeLifecycleOwner()?.let {
            templateViewModel.templateCategoryHorizontalList.removeObserver(templateObserver)
            templateViewModel.templateCategoryVerticalList.removeObserver(templateObserver)
            if (isHorizontal) {
                templateViewModel.templateCategoryHorizontalList.observe(it, templateObserver)
            } else {
                templateViewModel.templateCategoryVerticalList.observe(it, templateObserver)
            }
        }
    }

    private fun initAddPageList(paper: Paper? = null, document: Document? = null) {
        //noteEditFragment 这个时候会退栈，但是inflate会走到这
        if (noteViewModel.currentDoc == null) return
        binding.addPageView.overScrollRecyclerView.itemAnimator = null
        binding.addPageView.overScrollRecyclerView.layoutManager = LinearLayoutManager(context)
        noteAddPageSelectAddTypeAndColorAdapter = NoteAddPageSelectAddTypeAndColorAdapter(
            insertPosition,
            addPageCallback = addPageCallback,
            importImagePageCallback = importImagePageCallback,
            insertPositionChangeCallback = { insertPosition ->
                insertPosition?.let {
                    addPageViewModel.changeInsertPosition(insertPosition)
                }
            },
            orientationChangeCallback = {
                changeTemplateObserver(UserUsageConfig.defaultPaperOrientation)
            },
            context
        ).apply {
            this.paper = paper
            this.document = document
        }

        findViewTreeLifecycleOwner()?.let {
            addTemplateAdapter = NoteAddTemplateAdapter(context, it.lifecycleScope).apply {
                templateClickCallback = { template ->
                    if (template.file.isNullOrBlank()) {
                        template.file =
                            BaseTemplateViewModel.getTemplateFileByUrl(template)?.absolutePath
                    }
                    templateViewModel.viewModelScope.launch(Dispatchers.IO) {
                        val state = NoteTemplateHelper.getTemplateState(
                            template,
                            templateViewModel,
                            handbookViewModel
                        )
                        withContext(Dispatchers.Main) {
                            when (state) {
                                TemplateState.DOWNLOADED -> {
                                    addTemplate(template)
                                }

                                TemplateState.FREE -> {
                                    downLoadTemplate(template)
                                }

                                TemplateState.ONLY_VIP -> {
                                    val category = templateViewModel.getTemplateCategory(template)
                                    category?.let {
                                        buyTemplate(it)
                                        AddTemplateEvent.sendBuyTemplateClick("add_t")
                                    }
                                }

                                else -> {

                                }
                            }
                        }
                    }
                }
            }
            if (concatAdapter == null) {
                concatAdapter = ConcatAdapter(
                    noteAddPageSelectAddTypeAndColorAdapter, addTemplateAdapter
                )
            }

            binding.addPageView.overScrollRecyclerView.adapter = concatAdapter


            templateViewModel.remoteNotDownload.observe(it) { remoteNotDownload ->
                if (remoteNotDownload && ::addTemplateAdapter.isInitialized) {
                    concatAdapter = ConcatAdapter(
                        noteAddPageSelectAddTypeAndColorAdapter,
                        addTemplateAdapter,
                        TemplateStorageNotEnoughAdapter()
                    )
                    binding.addPageView.overScrollRecyclerView.adapter = concatAdapter
                }
            }
            addPageViewModel.insertPosition.observe(it) { insertPosition ->
                val doc = noteViewModel.currentDoc
                val pageIndex = doc!!.viewingPageIndex
                concatAdapter?.let { concatAdapter ->
                    val itemCount = concatAdapter.itemCount
                    if (insertPosition == InsertPosition.REPLACE) {
                        NoteTemplateHelper.isTemplateMarkVisible = doc[pageIndex].draws.isNotEmpty()
                        concatAdapter.notifyItemRangeChanged(
                            1,
                            itemCount - 1
                        )
                    } else {
                        NoteTemplateHelper.isTemplateMarkVisible = false
                        concatAdapter.notifyItemRangeChanged(
                            1,
                            itemCount - 1
                        )
                    }
                }
            }
        }
        binding.addPageView.overScrollRecyclerView.addItemDecoration(object :
            RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                concatAdapter?.let { concatAdapter ->
                    val position = parent.getChildAdapterPosition(view)
                    if (position >= 1) {
                        outRect.top = resources.getDimensionPixelSize(R.dimen.dp_24)
                        outRect.left = resources.getDimensionPixelSize(R.dimen.dp_30)
                    }
                    if (position == 1) {
                        outRect.top = resources.getDimensionPixelSize(R.dimen.dp_12)
                    }
                    if (position == concatAdapter.itemCount - 1) {
                        outRect.bottom = resources.getDimensionPixelSize(R.dimen.dp_24)
                    }
                }
            }
        })
        changeTemplateObserver(UserUsageConfig.defaultPaperOrientation)
    }

    fun setupBlurView(root: ViewGroup) {
        binding.addPageBlurView.setupWith(root)
            .setBlurRadius(15F)
    }

    @SuppressLint("NotifyDataSetChanged")
    suspend fun updateAdapter(template: Template) {
        addTemplateAdapter.notifyDataSetChanged()
        addTemplate(template)
    }

    private suspend fun addTemplate(template: Template) {
        val doc = noteViewModel.currentDoc
        val pageIndex = doc!!.viewingPageIndex
        if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE && doc[pageIndex].draws.isNotEmpty()) {

        } else {
            val document = BaseTemplateViewModel.getDocument(template.file)
            document?.let {
                addTemplateCallback?.invoke(template, document)
            }
        }
    }

    fun refreshTemplate() {
        val doc = noteViewModel.currentDoc!!
        val pageIndex = doc.viewingPageIndex
        if (insertPosition == InsertPosition.REPLACE) {
            NoteTemplateHelper.isTemplateMarkVisible =
                doc[pageIndex].draws.isNotEmpty()
            concatAdapter?.let { concatAdapter ->
                val itemCount = concatAdapter.itemCount
                if (this::addTemplateAdapter.isInitialized) {
                    concatAdapter.notifyItemRangeChanged(1, itemCount - 1)
                }
            }
        }
    }

    private fun downLoadTemplate(template: Template) {
        val doc = noteViewModel.currentDoc
        val pageIndex = doc!!.viewingPageIndex
        if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE && doc[pageIndex].draws.isNotEmpty()) {

        } else {
            if (NetworkUtils.isNetworkAvailable()) {
                templateViewModel.downloadTemplate(template)
            } else {
                ToastUtils.topCenter(context, R.string.toast_no_internet)
            }
        }
    }

    private fun buyTemplate(templateCategory: TemplateCategory) {
        val doc = noteViewModel.currentDoc
        val pageIndex = doc!!.viewingPageIndex
        if (addPageViewModel.insertPosition.value == InsertPosition.REPLACE && doc[pageIndex].draws.isNotEmpty()) {

        } else {
            if (templateCategory.noteId == NO_TEMPLATE_ASSOCIATED_NOTE_ID || handbookViewModel.findHandbookCoverByNoteId(
                    templateCategory.noteId
                ) == null
            ) {
                val action = NoteEditorFragmentDirections.actionNoteEditorToVipStore()
                navigationCallback?.invoke(action)

            } else {
                val action = NoteEditorFragmentDirections.buyTemplate(templateCategory.noteId)
                navigationCallback?.invoke(action)
            }
        }
    }

    private fun updateView(dataEmpty: Boolean) {
        if (dataEmpty) {
            concatAdapter = ConcatAdapter(
                noteAddPageSelectAddTypeAndColorAdapter,
                NoteAddPageEmptyDataAdapter(
                    context,
                    templateViewModel.isStorageNotEnoughToDownload()
                )
            )
            binding.addPageView.overScrollRecyclerView.adapter = concatAdapter
        }
    }

}
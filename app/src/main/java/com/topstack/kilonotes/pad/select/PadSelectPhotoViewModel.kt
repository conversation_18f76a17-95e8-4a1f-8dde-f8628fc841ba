package com.topstack.kilonotes.pad.select

import androidx.lifecycle.ViewModel

/**
 *
 */
class PadSelectPhotoViewModel : ViewModel() {
    companion object {
        const val horizontalRatio = 0
        const val verticalRatio = 1
        const val adaptiveRatio = 2
    }

    var needCropImage: Boolean = false
    var needFixRatio: Boolean = false
    var needSelectRatio: Boolean = false
    var forbidZoom: Boolean = false
    var ignoreDetailImage: Boolean = false
    var isVertical: Boolean = true
    var needCropAndChangeAlpha: Boolean = false

    var currentRatioType = horizontalRatio
}
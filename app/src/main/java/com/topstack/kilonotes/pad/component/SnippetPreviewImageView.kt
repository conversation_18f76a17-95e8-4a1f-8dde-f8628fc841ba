package com.topstack.kilonotes.pad.component

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Insets
import android.graphics.Rect
import android.graphics.RectF
import android.os.Build
import android.text.SpannableString
import android.text.Spanned
import android.text.style.BackgroundColorSpan
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.WindowInsets
import android.view.WindowManager
import android.view.WindowMetrics
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import com.bumptech.glide.Glide
import com.bumptech.glide.signature.ObjectKey
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.BubbleLayout
import com.topstack.kilonotes.base.component.view.HighlightImageView
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.note.snippet.SnippetManager
import com.topstack.kilonotes.base.note.snippet.SnippetType
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippet
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippetItem
import com.topstack.kilonotes.base.track.event.SnippetEvent
import com.topstack.kilonotes.databinding.PopupSnippetPreviewMoreBinding
import com.topstack.kilonotes.databinding.SnippetPreviewImageViewBinding
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.notedata.NoteRepository
import java.io.File
import java.util.UUID


class SnippetPreviewImageView : ConstraintLayout {

    companion object {
        const val SHORT_ANIMATION_NO_DURATION: Long = 0L
        const val SHORT_ANIMATION_DEF_DURATION: Long = 100L
        const val SCALE_MIN_SIZE = 0.1F
    }

    lateinit var binding: SnippetPreviewImageViewBinding
    private var currentAnimator: Animator? = null
    lateinit var previewContainer: View
    lateinit var previewContentBg: ConstraintLayout
    lateinit var previewTitleContent: ConstraintLayout
    lateinit var previewContent: FrameLayout
    lateinit var previewImage: HighlightImageView
    lateinit var previewText: TextView
    lateinit var startBounds: RectF
    lateinit var finalBounds: RectF
    private var loadImgWidth = 0
    private var loadImgHeight = 0
    private var startScaleX = 0.1F
    private var startScaleY = 0.1F
    private var expandedContentWidth = 0
    private var expandedContentHeight = 0
    private var titleContentHeight: Int = 0
    private var snippetPreviewMoreWindow: SnippetPreviewMoreWindow? = null
    private var snippetSourceDocument: Document? = null
    private var snippetSourcePageId: UUID? = null
    private var fragmentManager: FragmentManager? = null

    var doOnPreviewClose: (() -> Unit)? = null

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        binding = SnippetPreviewImageViewBinding.inflate(LayoutInflater.from(context), this, true)
        initView()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        titleContentHeight = previewTitleContent.measuredHeight
    }

    private fun initView() {
        previewContentBg = this
        previewContainer = binding.root
        previewTitleContent = binding.snippetPreviewTitleContent
        previewContent = binding.snippetPreviewContent
        previewImage = binding.previewImage
        previewText = binding.previewText

        binding.snippetSourceText.setOnClickListener {
            SnippetEvent.sendSnippetShowSourceClick()
            showSnippetDocumentPreview()
        }
        binding.snippetSourceGo.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
    }

    private fun getHighlightedContent(snippetItem: NoteSnippetItem): CharSequence {
        val snippetContent = SpannableString(snippetItem.snippet.text)
        val searchKeyword = snippetItem.searchKeyword
        if (snippetContent.isNotEmpty() && searchKeyword.isNotEmpty()) {
            var startIndex = snippetContent.indexOf(searchKeyword, ignoreCase = true)
            while (startIndex >= 0) {
                val highlight = BackgroundColorSpan(
                    AppUtils.getColor(R.color.snippet_search_highlight_background)
                )
                snippetContent.setSpan(
                    highlight,
                    startIndex,
                    startIndex + searchKeyword.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                startIndex = snippetContent.indexOf(
                    searchKeyword,
                    startIndex + searchKeyword.length,
                    ignoreCase = true
                )
            }
        }
        return snippetContent
    }

    fun restorePreview(
        fragmentManager: FragmentManager,
        preViewMaxWidth: Int,
        preViewMaxHeight: Int,
        noteSnippet: NoteSnippet,
        snippetEditCallback: (() -> Unit)? = null
    ) {
        this.fragmentManager = fragmentManager
        binding.snippetSource.isVisible = false
        snippetSourceDocument = null
        snippetSourcePageId = null
        val fromDocumentId = noteSnippet.documentId
        val fromPageId = noteSnippet.pageId
        if (fromDocumentId != null && fromPageId != null) {
            NoteRepository.getDocumentFromCacheById(fromDocumentId)?.let { fromDocument ->
                if (fromDocument.pageIds.contains(fromPageId)) {
                    snippetSourceDocument = fromDocument
                    snippetSourcePageId = fromPageId

                    binding.snippetSourceText.text =
                        resources.getString(R.string.note_snippet_source, fromDocument.title)
                    binding.snippetSource.isVisible = true
                }
            }
        }

        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }

        currentAnimator?.cancel()
        expandedContentWidth = preViewMaxWidth
        expandedContentHeight = preViewMaxHeight + titleContentHeight
        previewTitleContent.apply {
            visibility = VISIBLE
        }
        previewContainer.apply {
            val expandedImageContentLp = layoutParams
            expandedImageContentLp.width = expandedContentWidth
            expandedImageContentLp.height = expandedContentHeight
            layoutParams = expandedImageContentLp
        }
        initTitleBar(noteSnippet, snippetEditCallback)

        if (noteSnippet.snippetType == SnippetType.TEXT) {
            val previewTextView = previewText.layoutParams
            previewTextView.width = preViewMaxWidth
            previewText.layoutParams = previewTextView

            previewText.text = noteSnippet.text
        } else {
            val snippetImagePath =
                SnippetManager.getSnippetImageAbsoluteFilePath(noteSnippet)
            BitmapFactory.decodeFile(snippetImagePath, options)
            if (options.outHeight <= 0 || options.outWidth <= 0) {
                return
            }
            val expandedImageLp = previewImage.layoutParams

            val snippetHeight = preViewMaxWidth * options.outHeight / options.outWidth
            expandedImageLp.width = preViewMaxWidth
            expandedImageLp.height = snippetHeight

            previewImage.layoutParams = expandedImageLp

            loadImgWidth = if (options.outHeight < preViewMaxWidth) {
                options.outWidth
            } else {
                preViewMaxWidth
            }
            loadImgHeight = if (options.outWidth < preViewMaxHeight) {
                options.outHeight
            } else {
                preViewMaxHeight
            }

            Glide.with(context)
                .load(snippetImagePath)
                .override(loadImgWidth, loadImgHeight)
                .signature(ObjectKey(File(snippetImagePath).lastModified()))
                .into(previewImage)
        }

        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val widthPixels: Int
        val heightPixels: Int
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowMetrics: WindowMetrics = windowManager.currentWindowMetrics
            val insets: Insets = windowMetrics.windowInsets
                .getInsetsIgnoringVisibility(WindowInsets.Type.systemBars())
            widthPixels = windowMetrics.bounds.width() - insets.left - insets.right
            heightPixels = windowMetrics.bounds.height() - insets.top - insets.bottom
        } else {
            val displayMetrics = DisplayMetrics()
            windowManager.getDefaultDisplay().getMetrics(displayMetrics)
            widthPixels = displayMetrics.widthPixels
            heightPixels = displayMetrics.heightPixels
        }

        val expandedImageContentX =
            (widthPixels.toFloat() - expandedContentWidth) / 2
        val expandedImageContentY =
            (heightPixels.toFloat() - expandedContentHeight) / 2
        finalBounds = RectF(
            expandedImageContentX,
            expandedImageContentY,
            expandedImageContentX + expandedContentWidth,
            expandedImageContentY + expandedContentHeight
        )
        previewContentBg.setBackgroundColor(AppUtils.getColor(R.color.black_30))
        previewContainer.x = finalBounds.left
        previewContainer.y = finalBounds.top
        previewContentBg.visibility = View.VISIBLE

        resetSnippetDocumentPreview()
    }

    fun initPreview(
        targetView: View?,
        dismissCallback: (() -> Unit)? = null,
        showDeleteDialogAction: (() -> Unit)? = null
    ) {
        initScale(targetView)
        initBounds(targetView)
        previewContainer.pivotX = 0f
        previewContainer.pivotY = 0f
        previewContainer.x = finalBounds.left
        previewContainer.y = finalBounds.top
        previewContentBg.visibility = View.VISIBLE

        fun closePreview() {
            currentAnimator?.cancel()
            currentAnimator = AnimatorSet().apply {
                playTogether(
                    ObjectAnimator.ofFloat(
                        previewContainer,
                        View.X,
                        finalBounds.left,
                        startBounds.left
                    ),
                    ObjectAnimator.ofFloat(
                        previewContainer,
                        View.Y,
                        finalBounds.top,
                        startBounds.top
                    ),
                    ObjectAnimator.ofFloat(previewContainer, View.SCALE_X, 1F, startScaleX),
                    ObjectAnimator.ofFloat(previewContainer, View.SCALE_Y, 1F, startScaleY)
                )
                duration = SHORT_ANIMATION_DEF_DURATION
                interpolator = DecelerateInterpolator()
                addListener(object : AnimatorListenerAdapter() {

                    override fun onAnimationStart(animation: Animator) {
                        previewContentBg.setBackgroundColor(Color.TRANSPARENT)
                        dismissCallback?.invoke()
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        previewContentBg.visibility = View.INVISIBLE
                        resetPreviewContentLayout()
                        currentAnimator = null
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        previewContentBg.visibility = View.INVISIBLE
                        resetPreviewContentLayout()
                        currentAnimator = null
                    }
                })
                start()
            }
        }
        previewContentBg.setOnClickListener {
            closePreview()
        }
        binding.more.setOnClickListener {
            snippetPreviewMoreWindow = SnippetPreviewMoreWindow(context)
            snippetPreviewMoreWindow?.apply {
                onDeleteSnippetAction = {
                    showDeleteDialogAction?.invoke()
                }
            }?.showAsBubble(it)
        }
        binding.close.setOnClickListener {
            closePreview()
        }
    }

    fun showPreview(
        fragmentManager: FragmentManager,
        preViewMaxWidth: Int,
        preViewMaxHeight: Int,
        targetView: View?,
        noteSnippetItem: NoteSnippetItem,
        showDuration: Long,
        snippetEditCallback: (() -> Unit)? = null,
        dismissCallback: (() -> Unit)? = null,
        showDeleteDialogAction: (() -> Unit)? = null
    ) {
        this.fragmentManager = fragmentManager
        binding.snippetSource.isVisible = false
        val noteSnippet = noteSnippetItem.snippet
        snippetSourceDocument = null
        snippetSourcePageId = null
        val fromDocumentId = noteSnippet.documentId
        val fromPageId = noteSnippet.pageId
        if (fromDocumentId != null && fromPageId != null) {
            NoteRepository.getDocumentFromCacheById(fromDocumentId)?.let { fromDocument ->
                if (fromDocument.pageIds.contains(fromPageId)) {
                    snippetSourceDocument = fromDocument
                    snippetSourcePageId = fromPageId

                    binding.snippetSourceText.text =
                        resources.getString(R.string.note_snippet_source, fromDocument.title)
                    binding.snippetSource.isVisible = true
                }
            }
        }

        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }

        currentAnimator?.cancel()
        expandedContentWidth = preViewMaxWidth
        expandedContentHeight = preViewMaxHeight + titleContentHeight
        previewTitleContent.apply {
            visibility = VISIBLE
        }
        previewContainer.apply {
            val expandedImageContentLp = layoutParams
            expandedImageContentLp.width = expandedContentWidth
            expandedImageContentLp.height = expandedContentHeight
            layoutParams = expandedImageContentLp
        }
        initTitleBar(noteSnippet, snippetEditCallback)

        if (noteSnippet.snippetType == SnippetType.TEXT) {
            previewImage.visibility = View.GONE
            previewText.visibility = View.VISIBLE
            val previewTextView = previewText.layoutParams
            previewTextView.width = preViewMaxWidth
            previewText.layoutParams = previewTextView

            previewText.text = getHighlightedContent(noteSnippetItem)
        } else {
            val snippetImagePath =
                SnippetManager.getSnippetImageAbsoluteFilePath(noteSnippet)
            BitmapFactory.decodeFile(snippetImagePath, options)
            if (options.outHeight <= 0 || options.outWidth <= 0) {
                return
            }
            previewText.visibility = View.GONE
            previewImage.visibility = View.VISIBLE
            val expandedImageLp = previewImage.layoutParams
            val snippetHeight = preViewMaxWidth * options.outHeight / options.outWidth
            expandedImageLp.width = preViewMaxWidth
            expandedImageLp.height = snippetHeight
            previewImage.layoutParams = expandedImageLp

            loadImgWidth = if (options.outHeight < preViewMaxWidth) {
                options.outWidth
            } else {
                preViewMaxWidth
            }
            loadImgHeight = if (options.outWidth < preViewMaxHeight) {
                options.outHeight
            } else {
                preViewMaxHeight
            }
            Glide.with(context)
                .load(snippetImagePath)
                .override(loadImgWidth, loadImgHeight)
                .signature(ObjectKey(File(snippetImagePath).lastModified()))
                .into(previewImage)
        }

        initScale(targetView)
        initBounds(targetView)
        previewContainer.pivotX = 0f
        previewContainer.pivotY = 0f
        previewContentBg.visibility = View.VISIBLE
        currentAnimator = AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(
                    previewContainer,
                    View.X,
                    startBounds.left,
                    finalBounds.left
                ),
                ObjectAnimator.ofFloat(
                    previewContainer,
                    View.Y,
                    startBounds.top,
                    finalBounds.top
                ),
                ObjectAnimator.ofFloat(previewContainer, View.SCALE_X, startScaleX, 1F),
                ObjectAnimator.ofFloat(previewContainer, View.SCALE_Y, startScaleY, 1F)
            )
            duration = showDuration
            interpolator = DecelerateInterpolator()
            addListener(object : AnimatorListenerAdapter() {

                override fun onAnimationStart(animation: Animator) {
                    previewContentBg.setBackgroundColor(AppUtils.getColor(R.color.black_30))
                }

                override fun onAnimationEnd(animation: Animator) {
                    if (noteSnippet.snippetType == SnippetType.TEXT) {

                    } else {
                        previewImage.setImageOriginalSize(options.outWidth, options.outHeight)
                        val symbolsRectList = noteSnippet.textRecognitionResult?.symbolsRectList
                        val contentHighlightRanges =
                            noteSnippetItem.searchResultContentMatchedRanges
                        val contentHighlights = mutableListOf<Rect>()
                        if (symbolsRectList != null && contentHighlightRanges.isNotEmpty()) {
                            contentHighlightRanges.forEach {
                                val contentHighlightStart =
                                    it.first.coerceIn(symbolsRectList.indices)
                                val contentHighlightEnd = it.last.coerceIn(symbolsRectList.indices)
                                for (i in contentHighlightStart..contentHighlightEnd) {
                                    contentHighlights.add(Rect(symbolsRectList[i]))
                                }
                            }
                        }
                        previewImage.highlights = contentHighlights
                    }
                    currentAnimator = null
                }

                override fun onAnimationCancel(animation: Animator) {
                    currentAnimator = null
                }
            })
            start()
        }

        fun closePreview() {
            doOnPreviewClose?.invoke()
            currentAnimator?.cancel()
            currentAnimator = AnimatorSet().apply {
                playTogether(
                    ObjectAnimator.ofFloat(
                        previewContainer,
                        View.X,
                        finalBounds.left,
                        startBounds.left
                    ),
                    ObjectAnimator.ofFloat(
                        previewContainer,
                        View.Y,
                        finalBounds.top,
                        startBounds.top
                    ),
                    ObjectAnimator.ofFloat(previewContainer, View.SCALE_X, 1F, startScaleX),
                    ObjectAnimator.ofFloat(previewContainer, View.SCALE_Y, 1F, startScaleY)
                )
                duration = SHORT_ANIMATION_DEF_DURATION
                interpolator = DecelerateInterpolator()
                addListener(object : AnimatorListenerAdapter() {

                    override fun onAnimationStart(animation: Animator) {
                        previewContentBg.setBackgroundColor(Color.TRANSPARENT)
                        dismissCallback?.invoke()
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        previewContentBg.visibility = View.INVISIBLE
                        resetPreviewContentLayout()
                        currentAnimator = null
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        previewContentBg.visibility = View.INVISIBLE
                        resetPreviewContentLayout()
                        currentAnimator = null
                    }
                })
                start()
            }
        }
        previewContentBg.setOnClickListener {
            closePreview()
        }
        binding.more.setOnClickListener {
            snippetPreviewMoreWindow = SnippetPreviewMoreWindow(context)
            snippetPreviewMoreWindow?.apply {
                onDeleteSnippetAction = {
                    showDeleteDialogAction?.invoke()
                }
            }?.showAsBubble(it)
        }
        binding.close.setOnClickListener {
            closePreview()
        }
    }

    fun showPreview(
        preViewMaxWidth: Int,
        preViewMaxHeight: Int,
        targetView: View?,
        bitmap: Bitmap,
        showDuration: Long,
    ) {
        currentAnimator?.cancel()
        expandedContentWidth = preViewMaxWidth
        expandedContentHeight = preViewMaxHeight + titleContentHeight
        previewTitleContent.apply {
            visibility = GONE
        }
        previewContainer.apply {
            val expandedImageContentLp = layoutParams
            expandedImageContentLp.width = expandedContentWidth
            expandedImageContentLp.height = expandedContentHeight
            layoutParams = expandedImageContentLp
        }
        val expandedImageLp = previewImage.layoutParams
        expandedImageLp.width = preViewMaxWidth
        expandedImageLp.height = preViewMaxHeight
        previewImage.layoutParams = expandedImageLp
        loadImgWidth = preViewMaxWidth
        loadImgHeight = preViewMaxHeight
        previewImage.scaleType = ImageView.ScaleType.FIT_CENTER
        binding.snippetPreviewContent.setBackgroundColor(Color.TRANSPARENT)
        Glide.with(context)
            .load(bitmap)
            .override(loadImgWidth, loadImgHeight)
            .signature(ObjectKey(System.currentTimeMillis()))
            .into(previewImage)
        initScale(targetView)
        initBounds(targetView)
        previewContainer.pivotX = 0f
        previewContainer.pivotY = 0f
        previewContentBg.visibility = View.VISIBLE
        currentAnimator = AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(
                    previewContainer,
                    View.X,
                    startBounds.left,
                    finalBounds.left
                ),
                ObjectAnimator.ofFloat(
                    previewContainer,
                    View.Y,
                    startBounds.top,
                    finalBounds.top
                ),
                ObjectAnimator.ofFloat(previewContainer, View.SCALE_X, startScaleX, 1F),
                ObjectAnimator.ofFloat(previewContainer, View.SCALE_Y, startScaleY, 1F)
            )
            duration = showDuration
            interpolator = DecelerateInterpolator()
            addListener(object : AnimatorListenerAdapter() {

                override fun onAnimationStart(animation: Animator) {
                    previewContentBg.setBackgroundColor(Color.TRANSPARENT)
                }

                override fun onAnimationEnd(animation: Animator) {
                    currentAnimator = null
                }

                override fun onAnimationCancel(animation: Animator) {
                    currentAnimator = null
                }
            })
            start()
        }
    }

    fun hidePreview() {
        snippetPreviewMoreWindow?.dismiss()
        previewContentBg.visibility = View.INVISIBLE
        resetPreviewContentLayout()
    }

    fun hideEditIcon() {
        binding.edit.visibility = View.GONE
    }

    fun hideMoreIcon() {
        binding.more.visibility = View.GONE
    }

    private fun initTitleBar(
        noteSnippet: NoteSnippet,
        snippetEditCallback: (() -> Unit)? = null
    ) {
        binding.snippetTitle.text = noteSnippet.title
        binding.edit.setOnClickListener(AntiShakeClickListener {
            snippetEditCallback?.invoke()
        })
    }

    private fun initScale(targetView: View?) {
        if (targetView == null) {
            startScaleX = 1F
            startScaleY = 1F
        } else if (expandedContentWidth == 0 || expandedContentHeight == 0) {
            startScaleX = SCALE_MIN_SIZE
            startScaleY = SCALE_MIN_SIZE
        } else {
            startScaleX = targetView.width.toFloat() / expandedContentWidth
            startScaleY = targetView.height.toFloat() / expandedContentHeight
        }
    }

    private fun initBounds(targetView: View?) {
        val expandedImageContentX =
            (previewContentBg.width.toFloat() - expandedContentWidth) / 2
        val expandedImageContentY =
            (previewContentBg.height.toFloat() - expandedContentHeight) / 2
        finalBounds = RectF(
            expandedImageContentX,
            expandedImageContentY,
            expandedImageContentX + expandedContentWidth,
            expandedImageContentY + expandedContentHeight
        )
        startBounds = if (targetView != null) {
            val targetViewLocation = IntArray(2)
            targetView.getLocationOnScreen(targetViewLocation)
            val targetViewX = targetViewLocation[0].toFloat()
            val targetViewY = targetViewLocation[1].toFloat()
            RectF(
                targetViewX,
                targetViewY,
                (targetViewX + targetView.width),
                (targetViewY + targetView.height)
            )
        } else {
            RectF(finalBounds)
        }
    }

    private fun resetPreviewContentLayout() {
        previewContent.apply {
            val expandedImageContentLp = layoutParams
            expandedImageContentLp.width = 0
            expandedImageContentLp.height = 0
            layoutParams = expandedImageContentLp
        }
        previewImage.setImageDrawable(null)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        currentAnimator?.cancel()
        currentAnimator = null
        snippetPreviewMoreWindow?.dismiss()
    }

    private fun showSnippetDocumentPreview() {
        val sourceDocument = snippetSourceDocument
        val sourcePageId = snippetSourcePageId
        val fm = fragmentManager
        if (fm != null && sourceDocument != null && sourcePageId != null) {
            val fragment =
                fm.findFragmentByTag(SnippetDocPreviewFragment.snippetDocPreviewFragmentTag) as? SnippetDocPreviewFragment
            if (fragment == null) {
                SnippetDocPreviewFragment().apply {
                    blurRootView = <EMAIL> as? ViewGroup
                    initDoc(sourceDocument, sourcePageId)
                }.also {
                    it.show(fm)
                }
            } else {
                fragment.updateShowPageInfo(sourceDocument, sourcePageId)
            }
        }
    }

    private fun resetSnippetDocumentPreview() {
        val sourceDocument = snippetSourceDocument
        val sourcePageId = snippetSourcePageId
        val fm = fragmentManager
        if (fm != null && sourceDocument != null && sourcePageId != null) {
            val fragment =
                fm.findFragmentByTag(SnippetDocPreviewFragment.snippetDocPreviewFragmentTag) as? SnippetDocPreviewFragment
            if (fragment != null) {
                fragment.blurRootView = <EMAIL> as? ViewGroup
            }
        }
    }
}

class SnippetPreviewMoreWindow(val context: Context) : PopupWindow() {

    private val binding: PopupSnippetPreviewMoreBinding by lazy {
        PopupSnippetPreviewMoreBinding.inflate(
            LayoutInflater.from(context)
        )
    }

    var onDeleteSnippetAction: (() -> Unit)? = null

    init {
        height = LayoutParams.WRAP_CONTENT
        width = context.resources.getDimensionPixelSize(R.dimen.dp_238)
        isClippingEnabled = true
        isFocusable = true
        isOutsideTouchable = true
        binding.deleteSnippet.setOnClickListener {
            onDeleteSnippetAction?.invoke()
        }
    }

    fun showAsBubble(view: View) {
        view.post {
            if (view.windowToken == null) return@post
            val context = view.context ?: return@post
            val viewLocation = IntArray(2)
            view.getLocationOnScreen(viewLocation)
            val bubbleLegWidth = context.resources.getDimension(R.dimen.dp_30)
            val bubbleLegHeight = context.resources.getDimension(R.dimen.dp_16)
            val shadowRadius = context.resources.getDimension(R.dimen.dp_12)
            val bubbleLegOffset =
                viewLocation[0] + (view.width - bubbleLegWidth) / 2 + shadowRadius
            val orientation = BubbleLayout.BubbleOrientation.TOP
            val bubbleLayout =
                BubbleLayout(
                    context,
                    bubbleLegOffset,
                    bubbleLegWidth,
                    bubbleLegHeight,
                    orientation,
                    context.resources.getDimension(R.dimen.dp_12),
                    Color.WHITE,
                    shadowRadius,
                    BubbleLayout.BubbleType.TRIANGLE
                )
            bubbleLayout.addView(binding.root)
            contentView = bubbleLayout
            showAsDropDown(
                view,
                (view.width - width) / 2,
                -shadowRadius.toInt() / 2
            )
        }
    }
}

package com.topstack.kilonotes.pad.select

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Rect
import android.net.Uri
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.MeasureSpec
import android.widget.PopupWindow
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.BubbleLayout
import com.topstack.kilonotes.base.imagefetch.ImageFetcher
import com.topstack.kilonotes.base.imagefetch.model.Album
import com.topstack.kilonotes.databinding.PadPickImageBubbleListBinding
import com.topstack.kilonotes.pad.select.adapter.BubbleAlbumListAdapter
import com.topstack.kilonotes.pad.select.adapter.BubbleImageListAdapter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 *
 */
class PickImageBubbleWindow(context: Context) : PopupWindow() {
    val windowHeight by lazy {
        context.resources.getDimension(R.dimen.dp_540)
    }
    val windowWidth by lazy {
        context.resources.getDimension(R.dimen.dp_410)
    }
    private val spanCount = 3
    private var selectedAlbum: Album? = null
    private val ioScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val imageFetcher: ImageFetcher by lazy {
        ImageFetcher(context)
    }
    private var albums: List<Album> = emptyList()

    private var actionOnImageSelected: ((uri: Uri) -> Unit)? = null

    fun doOnImageSelected(action: ((uri: Uri) -> Unit)?) {
        actionOnImageSelected = action
    }

    private var dismissBySelection = false

    private val imageListAdapter = BubbleImageListAdapter().apply {
        doOnItemClick { position, image ->
            actionOnImageSelected?.invoke(image.uri)
            dismissBySelection = true
            dismiss()
        }
    }

    private val albumListAdapter = BubbleAlbumListAdapter().apply {
        doOnAlbumSelected { album ->
            updateData(album)
            hideAlbumsList()
        }
    }

    private val binding: PadPickImageBubbleListBinding by lazy {
        PadPickImageBubbleListBinding.inflate(LayoutInflater.from(context))
    }

    private var isAlbumListShowing = false

    private val onViewClickedListener = { view: View ->
        when (view.id) {
            binding.cancelButton.id -> {
                actionOnCancel?.invoke()
                dismiss()
            }

            binding.selectedAlbumName.id,
            binding.selectedAlbumIcon.id -> {
                if (isAlbumListShowing) {
                    hideAlbumsList()
                } else {
                    showAlbumsList()
                }
            }
        }
    }

    private var actionOnCancel: (() -> Unit)? = null

    fun doCancel(action: (() -> Unit)?) {
        actionOnCancel = action
    }

    init {
        isOutsideTouchable = true
        isFocusable = true
        initView(context)

        ioScope.launch {
            val allAlbums = imageFetcher.getAlbums()
            withContext(Dispatchers.Main) {
                albums = allAlbums
                val album = albums.firstOrNull()
                albumListAdapter.selectedAlbum = album
                albumListAdapter.submitList(albums)
                hideAlbumsList(true)
                updateData(album)
            }
        }
    }

    private fun initView(context: Context) {
        binding.cancelButton.setOnClickListener(onViewClickedListener)
        binding.selectedAlbumName.setOnClickListener(onViewClickedListener)
        binding.selectedAlbumIcon.setOnClickListener(onViewClickedListener)

        binding.imageList.run {
            itemAnimator = null
            layoutManager = GridLayoutManager(context, spanCount)
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    val space = parent.context.resources.getDimensionPixelOffset(R.dimen.dp_4)
                    val position = parent.getChildAdapterPosition(view)
                    val left = if (position % spanCount == 0) {
                        0
                    } else {
                        space / 2
                    }
                    val right = if (position % spanCount == (spanCount - 1)) {
                        0
                    } else {
                        space / 2
                    }

                    outRect.set(left, 0, right, space)
                }
            })
            adapter = imageListAdapter
        }

        binding.albumList.run {
            layoutManager = LinearLayoutManager(context)
            adapter = albumListAdapter
            itemAnimator = null

            addItemDecoration(DividerItemDecoration(context, LinearLayoutManager.VERTICAL).apply {
                setDrawable(resources.getDrawable(R.drawable.pad_bubble_album_list_divider, null))
            })
        }

        setOnDismissListener {
            if (!dismissBySelection) {
                actionOnCancel?.invoke()
            }
        }
    }

    private fun updateData(selectedAlbum: Album?) {
        if (this.selectedAlbum != selectedAlbum) {
            this.selectedAlbum = selectedAlbum
            selectedAlbum?.let { album ->
                binding.selectedAlbumName.text = album.name
                imageListAdapter.submitList(album.images)
            }
        }
    }

    private val animDuration = 200L
    private var showAlbumListAnim: AnimatorSet? = null
    private var hideAlbumListAnim: AnimatorSet? = null

    private fun showAlbumsList() {
        isAlbumListShowing = true
        showAlbumListAnim?.cancel()
        hideAlbumListAnim?.cancel()

        val anims = AnimatorSet()
        anims.duration = animDuration
        val translationAnim =
            ObjectAnimator.ofFloat(
                binding.albumList,
                "translationY",
                -binding.albumList.height.toFloat(),
                0F
            )
        val rotationAnim = ObjectAnimator.ofFloat(
            binding.selectedAlbumIcon,
            "rotation",
            0F,
            180F
        )
        anims.playTogether(translationAnim, rotationAnim)
        anims.start()
        showAlbumListAnim = anims
    }


    private fun hideAlbumsList(immediately: Boolean = false) {
        isAlbumListShowing = false
        showAlbumListAnim?.cancel()
        hideAlbumListAnim?.cancel()

        val anims = AnimatorSet()
        anims.duration = if (immediately) 0L else animDuration
        val translationAnim =
            ObjectAnimator.ofFloat(
                binding.albumList,
                "translationY",
                0F,
                -binding.albumList.height.toFloat(),
            )
        val rotationAnim = ObjectAnimator.ofFloat(
            binding.selectedAlbumIcon,
            "rotation",
            180F,
            360F
        )
        anims.playTogether(translationAnim, rotationAnim)
        anims.start()
        hideAlbumListAnim = anims
    }

    fun showAsBubble(view: View, orientation: BubbleLayout.BubbleOrientation) {
        val context = view.context
        val viewLocation = IntArray(2)
        view.getLocationOnScreen(viewLocation)
        var bubbleLegWidth = context.resources.getDimension(R.dimen.dp_16)
        var bubbleLegHeight = context.resources.getDimension(R.dimen.dp_36)
        val shadowRadius = context.resources.getDimension(R.dimen.dp_10)
        val definition = context.resources.getDimension(R.dimen.dp_1).toInt()
        var offset = viewLocation[1] + view.height / 2F
        when (orientation) {
            BubbleLayout.BubbleOrientation.TOP, BubbleLayout.BubbleOrientation.BOTTOM -> {
                val temp = bubbleLegWidth
                bubbleLegWidth = bubbleLegHeight
                bubbleLegHeight = temp
                offset = viewLocation[0] + view.width / 2F
            }

            else -> {}
        }

        binding.root.run {
            setBubbleLegSize(bubbleLegWidth, bubbleLegHeight)
            setBubbleOrientation(orientation)
            setBubbleLegOffset(offset)
        }
        view.getLocationInWindow(viewLocation)
        contentView = binding.root
        binding.root.measure(
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
        )
        binding.root.layout(0, 0, binding.root.measuredWidth, binding.root.measuredHeight)
        height = binding.root.measuredHeight
        width = binding.root.measuredWidth
        hideAlbumsList(true)
        if (albums.isEmpty()) {
            ioScope.launch {
                val allAlbums = imageFetcher.getAlbums()
                withContext(Dispatchers.Main) {
                    albums = allAlbums
                    val album = albums.firstOrNull()
                    albumListAdapter.selectedAlbum = album
                    albumListAdapter.submitList(albums)
                    hideAlbumsList(true)
                    updateData(album)
                }
            }
        }

        when (orientation) {
            BubbleLayout.BubbleOrientation.LEFT -> {
                showAtLocation(
                    view,
                    Gravity.NO_GRAVITY,
                    viewLocation[0] + view.width,
                    viewLocation[1] - height / 2 + view.height / 2
                )
            }

            BubbleLayout.BubbleOrientation.RIGHT -> {
                showAtLocation(
                    view,
                    Gravity.NO_GRAVITY,
                    viewLocation[0] - width + definition,
                    viewLocation[1] - height / 2 + view.height / 2
                )
            }

            BubbleLayout.BubbleOrientation.TOP -> {
                showAtLocation(
                    view,
                    Gravity.NO_GRAVITY,
                    viewLocation[0] - width / 2 + view.width / 2,
                    viewLocation[1] + view.height
                )
            }

            BubbleLayout.BubbleOrientation.BOTTOM -> {
                showAtLocation(
                    view,
                    Gravity.NO_GRAVITY,
                    viewLocation[0] - width / 2 + view.width / 2,
                    viewLocation[1] - height + definition
                )
            }
        }
    }
}
package com.topstack.kilonotes.pad.backup.fragment

import android.graphics.Rect
import android.view.Gravity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.doOnLayout
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.backup.adapter.BaseBackupSpaceNotesAdapter
import com.topstack.kilonotes.base.backup.fragment.BaseBackupSpaceFragment
import com.topstack.kilonotes.pad.backup.adapter.PadBackupSpaceNotesAdapter

class PadBackupSpaceFragment : BaseBackupSpaceFragment() {
    private val topSpace by lazy {
        requireContext().resources.getDimensionPixelSize(R.dimen.dp_14)
    }

    private val devicesTabLeftSpace by lazy {
        requireContext().resources.getDimensionPixelSize(R.dimen.dp_17)
    }

    private val lineLeftPadding by lazy {
        resources.getDimension(R.dimen.dp_108)
    }

    private val lineRightPadding by lazy {
        resources.getDimension(R.dimen.dp_33)
    }

    override fun containerLayoutId(): Int {
        return if (isLandAndOneThirdScreen() || isLikeXiaoMiPad5PortraitHalfScreen()) {
            R.layout.fragment_backup_space_one_third
        } else {
            R.layout.fragment_backup_space
        }
    }

    override fun initView() {
        super.initView()
        if (isPortraitScreen()) {
            val params = openBg.layoutParams as ConstraintLayout.LayoutParams
            if (isPortraitAndHalfScreen() || isPortraitAndOneThirdScreen()) {
                params.matchConstraintPercentWidth = 1F
                params.matchConstraintPercentHeight = 0.2F
            } else {
                params.matchConstraintPercentWidth = 0.8f
            }
            openBg.layoutParams = params
        }

        if (isLandAndHalfScreen() || isLandTwoThirdScreen()) {
            val params = openBg.layoutParams as ConstraintLayout.LayoutParams
            params.matchConstraintPercentWidth = 0.6f
            openBg.layoutParams = params
        }

        if (!isPortraitScreen() && !isLandAndFullScreen()) {
            backupUsedSpace.gravity = Gravity.START
        }

        if (!isPortraitScreen() && !isLandAndFullScreen() && !isLandAndOneThirdScreen()) {
            adapterTitleLocation()
        }

        container.doOnLayout {
            val backupSpaceTitleRect = Rect()
            backupSpaceTitle.getGlobalVisibleRect(backupSpaceTitleRect)
            val uploadIconRect = Rect()
            uploadIcon.getGlobalVisibleRect(uploadIconRect)
            val titleIsOverlap = backupSpaceTitleRect.right > uploadIconRect.left
            if (titleIsOverlap) {
                adapterTitleLocation()
            }
        }
    }

    fun adapterTitleLocation() {
        val constraintLayout = container
        val constraintSet = ConstraintSet().apply {
            clone(constraintLayout)
        }
        with(constraintSet) {
            clear(R.id.backup_space_title, ConstraintSet.START)
            clear(R.id.backup_space_title, ConstraintSet.END)
            clear(R.id.backup_space_title, ConstraintSet.BOTTOM)

            connect(
                R.id.backup_space_title,
                ConstraintSet.START,
                ConstraintLayout.LayoutParams.PARENT_ID,
                ConstraintSet.START,
                requireContext().resources.getDimensionPixelSize(R.dimen.dp_58)
            )
            connect(
                R.id.backup_space_title,
                ConstraintSet.TOP,
                R.id.backup_space_close,
                ConstraintSet.BOTTOM,
                requireContext().resources.getDimensionPixelSize(R.dimen.dp_32)
            )
        }
        with(constraintSet) {
            clear(R.id.backup_used_space, ConstraintSet.START)
            clear(R.id.backup_used_space, ConstraintSet.END)

            connect(
                R.id.backup_used_space,
                ConstraintSet.START,
                R.id.backup_space_title,
                ConstraintSet.START
            )
            connect(
                R.id.backup_used_space,
                ConstraintSet.TOP,
                R.id.backup_space_title,
                ConstraintSet.BOTTOM
            )
        }
        constraintSet.applyTo(constraintLayout)
    }

    override fun getBackupNotesListTopSpace(): Int {
        return topSpace
    }

    override fun getDevicesTabListLeftSpace(): Int {
        return devicesTabLeftSpace
    }

    override fun getDevicesTabListPaddingHorizontal(): Int {
        return if (isLandAndOneThirdScreen() || isLikeXiaoMiPad5PortraitHalfScreen()) {
            resources.getDimensionPixelSize(R.dimen.dp_32)
        } else {
            resources.getDimensionPixelSize(R.dimen.dp_58)
        }
    }

    override fun getNotesListLineLeftPadding(): Float {
        return lineLeftPadding
    }

    override fun getNotesListLineRightPadding(): Float {
        return lineRightPadding
    }

    override fun getBackupSpaceNotesAdapter(): BaseBackupSpaceNotesAdapter {
        return PadBackupSpaceNotesAdapter(requireContext(), backupDownloadViewModel)
    }
}
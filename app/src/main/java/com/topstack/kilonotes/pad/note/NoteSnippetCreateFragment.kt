package com.topstack.kilonotes.pad.note

import android.Manifest
import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.topstack.kilonotes.BuildConfig
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.AlertDialog
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.ktx.isLayoutRtl
import com.topstack.kilonotes.base.ktx.safeShow
import com.topstack.kilonotes.base.note.snippet.SnippetManager
import com.topstack.kilonotes.base.note.snippet.SnippetTagInputFilter
import com.topstack.kilonotes.base.note.snippet.SnippetType
import com.topstack.kilonotes.base.note.snippet.data.NoteSnippet
import com.topstack.kilonotes.base.note.snippet.data.SnippetTag
import com.topstack.kilonotes.base.note.viewmodel.NoteSnippetCreateViewModel
import com.topstack.kilonotes.base.note.viewmodel.SnippetEditViewModel
import com.topstack.kilonotes.base.select.BUNDLE_URI_KEY
import com.topstack.kilonotes.base.select.BasePhotoCropDialogFragment
import com.topstack.kilonotes.base.select.PICK_PIC_RESULT_CODE
import com.topstack.kilonotes.base.track.event.SnippetEvent
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.ImageUtils
import com.topstack.kilonotes.base.util.ModelUtils
import com.topstack.kilonotes.base.util.PermissionRequester
import com.topstack.kilonotes.base.util.SoftInputUtil
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.base.util.WindowInsetsUtils
import com.topstack.kilonotes.databinding.FragmentNoteSnippetCreateBinding
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.notedata.NoteRepository
import com.topstack.kilonotes.pad.MainActivity
import com.topstack.kilonotes.pad.note.adapter.NoteSnippetCreateSelectColorAdapter
import com.topstack.kilonotes.pad.note.model.NoteSnippetCreateMode
import com.topstack.kilonotes.pad.select.PhotoCropDialogFragment
import com.topstack.kilonotes.pad.select.SelectPhotoDialogActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.UUID

class NoteSnippetCreateFragment : BaseFragment(),
    BasePhotoCropDialogFragment.IOnCropCompleteListener {
    companion object {
        const val TAG = "NoteSnippetCreateFragment"
        private val MARGIN_DP_5 =
            AppUtils.appContext.resources.getDimensionPixelSize(R.dimen.dp_5)
        private val MARGIN_DP_20 =
            AppUtils.appContext.resources.getDimensionPixelSize(R.dimen.dp_20)
        private val MARGIN_DP_15 =
            AppUtils.appContext.resources.getDimensionPixelSize(R.dimen.dp_15)
        private val MARGIN_DP_30 =
            AppUtils.appContext.resources.getDimensionPixelSize(R.dimen.dp_30)
        private val MARGIN_DP_60 =
            AppUtils.appContext.resources.getDimensionPixelSize(R.dimen.dp_60)

        const val SAVE_ALERT_DIALOG_TAG = "SaveAlertDialog"
        const val CHANGE_MODE_ALERT_DIALOG_TAG = "ChangeModeAlertDialog"
    }

    private lateinit var binding: FragmentNoteSnippetCreateBinding

    private val permissionRequester = PermissionRequester(this)

    private val noteSnippetCreateViewModel: NoteSnippetCreateViewModel by navGraphViewModels(R.id.create_snippet_fragment)

    private val snippetEditViewModel: SnippetEditViewModel by activityViewModels()

    private var colorAdapter: NoteSnippetCreateSelectColorAdapter? = null

    private var saveAlertDialog: AlertDialog? = null
    private var changeModeAlertDialog: AlertDialog? = null

    private var pictureUri: Uri? = null

    private var photoCropDialogFragment: PhotoCropDialogFragment? = null

    private val args: NoteSnippetCreateFragmentArgs by navArgs()

    private var lastSoftKeyboardShowStatus: Boolean = false
    private var lastSoftKeyboardHeight: Int = 0

    private val snippetNameInputClearIconSize by lazy {
        resources.getDimensionPixelOffset(R.dimen.dp_26)
    }

    private val selectImageLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) GetContent@{
        noteSnippetCreateViewModel.changeOpenAlbumStatus(false)
        val data = it.data
        val resultCode = it.resultCode
        if (PICK_PIC_RESULT_CODE == resultCode) {
            val uri = data?.extras?.get(BUNDLE_URI_KEY) ?: return@GetContent
            val path = uri as Uri
            noteSnippetCreateViewModel.setCurrentSelectedImageUri(path)
        }
    }

    private val takePhotoLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) {
        if (it) {
            photoCropDialogFragment = PhotoCropDialogFragment().apply {
                cropFileContainAlpha = true
                photoUri = noteSnippetCreateViewModel.currentTakePhotoUri.value
                setOnCropCompleteListener(this@NoteSnippetCreateFragment)
            }
            photoCropDialogFragment?.show(
                parentFragmentManager,
                BasePhotoCropDialogFragment.TAG
            )
        } else {
            //没有拍照，直接退出
            pictureUri = null
        }
    }

    private val editModeInputViewLocation = IntArray(2)
    private val softKeyboardInterval by lazy {
        requireContext().resources.getDimensionPixelOffset(R.dimen.dp_20)
    }

    private val colorItemDecoration: ItemDecoration = object : ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val position = parent.getChildAdapterPosition(view)
            if (position != 0) {
                if (parent.isLayoutRtl()) {
                    outRect.right = resources.getDimensionPixelOffset(R.dimen.dp_16)
                } else {
                    outRect.left = resources.getDimensionPixelOffset(R.dimen.dp_16)
                }
            }
        }
    }

    //文本模式输入框光标所在行数
    private var editModeInputCursorCurrentLine: Int? = null
        set(value) {
            if (field != value) {
                if (field != null && value != null) {
                    val layout = binding.snippetEditText.layout
                    layout?.let {
                        if (binding.snippetEditText.height > layout.height) {
                            val offset = (field!! - value) * binding.snippetEditText.lineHeight / 2
                            binding.operationBar.translationY += offset
                        }
                    }
                }
                field = value
            }
        }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentNoteSnippetCreateBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        transitLayout()
        tryToRebuildDialog()
        initObserve()
        initView()
        if (args.isEditMode && savedInstanceState == null) {
            noteSnippetCreateViewModel.currentSnippet = snippetEditViewModel.currentEditSnippet
            noteSnippetCreateViewModel.currentSnippet?.let { snippet ->
                val mode = if (noteSnippetCreateViewModel.currentDoodleBitmap.value != null) {
                    NoteSnippetCreateMode.DOODLE
                } else {
                    when (snippet.snippetType) {
                        SnippetType.CAMERA -> NoteSnippetCreateMode.CAMERA
                        SnippetType.TEXT -> NoteSnippetCreateMode.TEXT
                        SnippetType.IMAGE -> NoteSnippetCreateMode.IMAGE
                        SnippetType.DOODLE -> NoteSnippetCreateMode.DOODLE
                    }
                }
                noteSnippetCreateViewModel.setCurrentSelectedMode(mode)
                binding.snippetNameInput.text = snippetEditViewModel.currentEditSnippet?.title
                snippetEditViewModel.currentEditSnippet?.color?.let { selectedColor ->
                    noteSnippetCreateViewModel.setCurrentSelectedColor(selectedColor)
                }

                when (mode) {
                    NoteSnippetCreateMode.TEXT -> {
                        noteSnippetCreateViewModel.setCurrentTextModeInputResult(snippet.text)
                        binding.snippetEditText.setText(snippet.text)
                    }

                    NoteSnippetCreateMode.DOODLE -> {
                        if (noteSnippetCreateViewModel.currentDoodleBitmap.value == null) {
                            val path = SnippetManager.getSnippetImageAbsoluteFilePath(snippet)
                            noteSnippetCreateViewModel.setCurrentDoodleBitmap(
                                SnippetManager.getSnippetImageAbsoluteFileUri(snippet),
                                BitmapFactory.decodeFile(
                                    path
                                )
                            )
                        }
                    }

                    NoteSnippetCreateMode.IMAGE -> {
                        noteSnippetCreateViewModel.setCurrentSelectedImageUri(
                            SnippetManager.getSnippetImageAbsoluteFileUri(
                                snippet
                            )
                        )
                    }

                    NoteSnippetCreateMode.CAMERA -> {
                        noteSnippetCreateViewModel.setCurrentSelectedImageUri(
                            SnippetManager.getSnippetImageAbsoluteFileUri(
                                snippet
                            )
                        )
                    }
                }
            }
        }
        val dialog = parentFragmentManager.findFragmentByTag(BasePhotoCropDialogFragment.TAG)
        if (dialog != null && dialog is BasePhotoCropDialogFragment) {
            photoCropDialogFragment = dialog as PhotoCropDialogFragment
            dialog.apply {
                photoUri = noteSnippetCreateViewModel.currentSelectedImageUri.value
                setOnCropCompleteListener(this@NoteSnippetCreateFragment)
            }
        }
        snippetEditViewModel.changeSnippetPageEditMode(false)
        snippetEditViewModel.clearCurrentSelectedSnippet()
    }

    private fun initView() {
        if (args.isEditMode) {
            binding.create.text = AppUtils.getString(R.string.instant_alpha_save)
            binding.title.text =
                AppUtils.getString(R.string.note_snippet_create_fragment_edit_mode_title)
        } else {
            binding.create.text = AppUtils.getString(R.string.create)
            binding.title.text = AppUtils.getString(R.string.note_snippet_create_fragment_title)
        }
        binding.back.setOnClickListener {
            LogHelper.d(TAG, "binding.back_clicked")
            if (checkBeforeBack()) {
                releaseResourcesExit(noteSnippetCreateViewModel.currentSnippet, isNotSave = true)
            } else {
                SnippetEvent.sendSnippetSaveDialogShow()
                showSaveAlertDialog()
            }
        }
        noteSnippetCreateViewModel.currentSelectedColor.value?.let { currentColor ->
            colorAdapter = NoteSnippetCreateSelectColorAdapter(
                requireContext(),
                SnippetManager.DEFAULT_SNIPPET_COLOR_LIST,
                currentColor
            ).apply {
                setOnColorClickListener { color ->
                    noteSnippetCreateViewModel.setCurrentSelectedColor(color)
                }
            }
        }
        binding.colorSelector.overScrollRecyclerView.apply {
            adapter = colorAdapter
            layoutManager =
                object : LinearLayoutManager(context, HORIZONTAL, false) {
                    override fun canScrollHorizontally(): Boolean {
                        return false
                    }
                }
            removeItemDecoration(colorItemDecoration)
            addItemDecoration(colorItemDecoration)
        }
        binding.textMode.setOnClickListener {
            if (checkBeforeChangeMode(NoteSnippetCreateMode.TEXT)) {
                noteSnippetCreateViewModel.setCurrentSelectedMode(NoteSnippetCreateMode.TEXT)
            }
        }
        binding.doodleMode.setOnClickListener {
            if (checkBeforeChangeMode(NoteSnippetCreateMode.DOODLE)) {
                noteSnippetCreateViewModel.setCurrentSelectedMode(NoteSnippetCreateMode.DOODLE)
            }
        }
        binding.imageMode.setOnClickListener {
            if (checkBeforeChangeMode(NoteSnippetCreateMode.IMAGE)) {
                noteSnippetCreateViewModel.setCurrentSelectedMode(NoteSnippetCreateMode.IMAGE)
            }
        }
        binding.cameraMode.setOnClickListener {
            if (checkBeforeChangeMode(NoteSnippetCreateMode.CAMERA)) {
                noteSnippetCreateViewModel.setCurrentSelectedMode(NoteSnippetCreateMode.CAMERA)
            }
        }
        binding.operationBar.setOnClickListener {
            if (noteSnippetCreateViewModel.isAddedSnippet.value == true) return@setOnClickListener
            noteSnippetCreateViewModel.currentSelectedMode.value?.let { mode ->
                when (mode) {
                    NoteSnippetCreateMode.TEXT -> {
                        onTextOperationClick()
                    }

                    NoteSnippetCreateMode.DOODLE -> {
                        onDoodleOperationClick()
                    }

                    NoteSnippetCreateMode.IMAGE -> {
                        onImageOperationClick()
                    }

                    NoteSnippetCreateMode.CAMERA -> {
                        if (DimensionUtil.isPortraitAndFullScreen(requireContext()) || DimensionUtil.isLandAndFullScreen(
                                requireContext()
                            )
                        ) {
                            onCameraOperationClick()
                        } else {
                            ToastUtils.topCenter(
                                requireContext(),
                                R.string.note_snippet_create_camera_can_not_use
                            )
                        }

                    }
                }
            }
        }
        binding.readd.setOnClickListener {
            noteSnippetCreateViewModel.currentSelectedMode.value?.let { mode ->
                when (mode) {
                    NoteSnippetCreateMode.TEXT -> {
                    }

                    NoteSnippetCreateMode.DOODLE -> {
                        onDoodleOperationClick()
                    }

                    NoteSnippetCreateMode.IMAGE -> {
                        onImageOperationClick()
                    }

                    NoteSnippetCreateMode.CAMERA -> {
                        onCameraOperationClick()
                    }
                }
            }
        }
        binding.create.apply {
            isEnabled = false
            setOnClickListener {
                saveSnippet()
            }
        }
        binding.addTag.setOnClickListener {
            noteSnippetCreateViewModel.setTagInputVisible()
        }

        binding.snippetNameInput.apply {
            setClearIconSize(
                snippetNameInputClearIconSize,
                snippetNameInputClearIconSize
            )
            setClearIconMargin(0, 0, 0, 0)
            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {

                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    if (binding.snippetNameInput.hasFocus() && binding.snippetNameInput.text?.isNotEmpty() == true) {
                        binding.snippetNameInput.setClearIconVisibility(true)
                    } else {
                        binding.snippetNameInput.setClearIconVisibility(false)
                    }
                }

                override fun afterTextChanged(s: Editable?) {

                }

            })
            setInputLayoutOnFocusChangeListener { v, hasFocus ->
                if (!hasFocus) {
                    binding.snippetNameInput.setClearIconVisibility(false)
                    resetInputAttrWhenNotHasFocus()
                } else {
                    if (binding.snippetNameInput.text?.isNotEmpty() == true) {
                        binding.snippetNameInput.setClearIconVisibility(true)
                    }
                }
            }
            setOnInputClickListener {
                resetInputAttrWhenHasFocus()
            }
        }
        binding.snippetEditText.apply {
            var isScroll = false
            setOnTouchListener { v, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        setInputFocusable(binding.snippetEditText, true)
                        isScroll = false
                    }

                    MotionEvent.ACTION_MOVE -> {
                        isScroll = true
                    }

                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                        if (!isScroll) {
                            if (!binding.snippetEditText.hasFocus()) {
                                requestFocus()
                            }
                            WindowInsetsUtils.showSoftKeyBoard(binding.snippetEditText)
                        }
                        true
                    }
                }
                false
            }

            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus) {
                    noteSnippetCreateViewModel.setCurrentTextModeInputResult(
                        if (text?.isEmpty() == true || text.isBlank()) {
                            binding.snippetEditText.text = null
                            null
                        } else {
                            text.toString()
                        }
                    )
                }
            }
            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    val cursorPosition = binding.snippetEditText.selectionStart
                    if (cursorPosition != -1) {
                        val layout = binding.snippetEditText.layout
                        layout?.let {
                            editModeInputCursorCurrentLine = layout.getLineForOffset(cursorPosition)
                            val textOffset =
                                layout.getLineBaseline(layout.getLineForOffset(cursorPosition))
                        }
                    }
                }

                override fun afterTextChanged(s: Editable?) {
                    binding.snippetEditTextDuplicate.text = s
                    if (s?.isNotEmpty() == true) {
                        noteSnippetCreateViewModel.setCurrentTextModeInputResult(s.toString())
                    }
                }
            })
        }
        binding.snippetEditText.post {
            if (!isAdded) return@post
            binding.snippetEditText.getLocationInWindow(editModeInputViewLocation)
        }
        binding.tagInput.apply {
            filters = arrayOf(SnippetTagInputFilter())
            setOnEditorActionListener { editText, _, event ->
                if (event?.keyCode == KeyEvent.KEYCODE_ENTER) {
                    SoftInputUtil.hideSoftInput(editText)
                    true
                } else {
                    false
                }
            }
        }

        noteSnippetCreateViewModel.setCurrentSelectedColor(UserUsageConfig.editSnippetColor)
        when (UserUsageConfig.editSnippetMode) {
            NoteSnippetCreateMode.TEXT.ordinal -> {
                noteSnippetCreateViewModel.setCurrentSelectedMode(NoteSnippetCreateMode.TEXT)
            }

            NoteSnippetCreateMode.CAMERA.ordinal -> {
                noteSnippetCreateViewModel.setCurrentSelectedMode(NoteSnippetCreateMode.CAMERA)
            }

            NoteSnippetCreateMode.IMAGE.ordinal -> {
                noteSnippetCreateViewModel.setCurrentSelectedMode(NoteSnippetCreateMode.IMAGE)
            }

            NoteSnippetCreateMode.DOODLE.ordinal -> {
                noteSnippetCreateViewModel.setCurrentSelectedMode(NoteSnippetCreateMode.DOODLE)
            }
        }

        binding.back.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
    }

    private fun updateHeaderAndTopGradientBackgroundLayoutToFitStatusBar() {
        requestLayoutToFitStatusBar(binding.header)
        val params = binding.topGradientBackground.layoutParams as MarginLayoutParams
        params.bottomMargin = -resources.getDimension(R.dimen.dp_60).toInt()
        binding.topGradientBackground.layoutParams = params
    }

    private fun updateTopGradientBackground(startColor: Int) {
        binding.topGradientBackground.background =
            GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM,
                IntArray(2).apply {
                    set(0, startColor)
                    set(1, Color.WHITE)
                }
            )
    }

    private fun initObserve() {
        noteSnippetCreateViewModel.apply {
            currentSelectedMode.observe(viewLifecycleOwner) { mode ->
                WindowInsetsUtils.hideSoftKeyBoard(binding.root)
                binding.textMode.isSelected = mode == NoteSnippetCreateMode.TEXT
                binding.doodleMode.isSelected = mode == NoteSnippetCreateMode.DOODLE
                binding.cameraMode.isSelected = mode == NoteSnippetCreateMode.CAMERA
                binding.imageMode.isSelected = mode == NoteSnippetCreateMode.IMAGE
                when (mode) {
                    NoteSnippetCreateMode.TEXT -> {
                        binding.operationModeText.text =
                            AppUtils.getString(R.string.note_snippet_create_text_null_hint)
                        Glide.with(requireContext())
                            .load(R.drawable.note_snippet_create_operation_icon_text)
                            .into(binding.operationModeImage)
                    }

                    NoteSnippetCreateMode.DOODLE -> {
                        binding.operationModeText.text =
                            AppUtils.getString(R.string.note_snippet_create_operation_bar_text)
                        Glide.with(requireContext())
                            .load(R.drawable.note_snippet_create_operation_icon_doodle)
                            .into(binding.operationModeImage)
                    }

                    NoteSnippetCreateMode.CAMERA -> {
                        binding.operationModeText.text =
                            AppUtils.getString(R.string.note_snippet_create_camera_null_hint)
                        Glide.with(requireContext())
                            .load(R.drawable.note_snippet_create_operation_icon_camera)
                            .into(binding.operationModeImage)
                    }

                    NoteSnippetCreateMode.IMAGE -> {
                        binding.operationModeText.text =
                            AppUtils.getString(R.string.note_snippet_create_image_null_hint)
                        Glide.with(requireContext())
                            .load(R.drawable.note_snippet_create_operation_icon_image)
                            .into(binding.operationModeImage)
                    }
                }
            }

            currentSelectedColor.observe(viewLifecycleOwner) { color ->
                updateTopGradientBackground(color)
                colorAdapter?.setSelectedColor(color)
            }

            tagInputIsVisible.observe(viewLifecycleOwner) { isVisible ->
                binding.tagInput.visibility = if (isVisible) {
                    View.VISIBLE
                } else {
                    View.INVISIBLE
                }
            }

            isAddedSnippet.observe(viewLifecycleOwner) { isAdded ->
                binding.create.isEnabled = isAdded
                if (currentSelectedMode.value == NoteSnippetCreateMode.TEXT) {
                    binding.readd.isVisible = false
                } else {
                    binding.readd.isVisible = isAdded
                }
                if (isAdded) {
                    binding.operationModeImage.visibility = View.INVISIBLE
                    binding.operationModeText.visibility = View.INVISIBLE
                    displayTagsIfEditSnippet()
                } else {
                    binding.operationModeImage.visibility = View.VISIBLE
                    binding.operationModeText.visibility = View.VISIBLE
                }
            }
            currentSelectedImageUri.observe(viewLifecycleOwner) { uri ->
                if (uri != null) {
                    binding.operationScrollContainer.visibility = View.VISIBLE
                    insertImageElement(uri)
                } else {
                    binding.operationScrollContainer.visibility = View.GONE
                }
            }
            currentTextModeInputResult.observe(viewLifecycleOwner) { inputResult ->
                binding.snippetEditText.isVisible = inputResult != null
            }
            currentDoodleBitmap.observe(viewLifecycleOwner) { bitmap ->
                binding.doodleResult.isVisible = bitmap != null
                bitmap?.let {
                    Glide.with(requireContext())
                        .load(it)
                        .into(binding.doodleResult)
                }
            }
        }
    }

    //文本模式下点击编辑
    private fun onTextOperationClick() {
        noteSnippetCreateViewModel.setCurrentTextModeInputResult("")
        setInputFocusable(binding.snippetEditText, true)
        binding.snippetEditText.requestFocus()
        binding.snippetEditText.post {
            if (!isAdded) return@post
            WindowInsetsUtils.showSoftKeyBoard(binding.snippetEditText)
        }
    }

    //相机模式下点击编辑
    private fun onCameraOperationClick() {
        takePhoto()
    }

    //图片模式下点击编辑
    private fun onImageOperationClick() {
        openAlbum()
    }

    //画笔模式下点击编辑
    private fun onDoodleOperationClick() {
        val color = noteSnippetCreateViewModel.currentSelectedColor.value ?: return
        lifecycleScope.launch {
            val doc = if (noteSnippetCreateViewModel.currentSnippet == null) {
                val snippet = SnippetManager.generateDoodleSnippet(color)
                noteSnippetCreateViewModel.currentSnippet = snippet
                SnippetManager.getSnippetDocument(snippet)
            } else {
                SnippetManager.getSnippetDocument(noteSnippetCreateViewModel.currentSnippet!!)
            }
            withContext(Dispatchers.Main) {
                noteSnippetCreateViewModel.currentDoodleResultDoc = doc
                safeNavigate(R.id.hand_write_snippet_edit)
            }
        }
    }

    //分屏适配
    private fun transitLayout() {
        if (DimensionUtil.isLandAndOneThirdScreen(requireContext()) || DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(
                requireContext()
            )
        ) {
            val nameParams = binding.snippetNameInput.layoutParams as MarginLayoutParams
            nameParams.marginStart = MARGIN_DP_20
            nameParams.marginEnd = MARGIN_DP_20
            binding.snippetNameInput.layoutParams = nameParams

            val tagInputParams = binding.tagInput.layoutParams as MarginLayoutParams
            tagInputParams.bottomMargin = MARGIN_DP_30
            binding.tagInput.layoutParams = tagInputParams

            val createBtnParams = binding.create.layoutParams as MarginLayoutParams
            createBtnParams.marginEnd = MARGIN_DP_20
            binding.create.layoutParams = createBtnParams

            val backBtnParams = binding.back.layoutParams as MarginLayoutParams
            backBtnParams.marginStart = MARGIN_DP_20
            binding.back.layoutParams = backBtnParams
        } else if (DimensionUtil.isLandAndHalfScreen(requireContext())) {
            val nameParams = binding.snippetNameInput.layoutParams as MarginLayoutParams
            nameParams.marginStart = MARGIN_DP_30
            nameParams.marginEnd = MARGIN_DP_30
            binding.snippetNameInput.layoutParams = nameParams

            val tagInputParams = binding.tagInput.layoutParams as MarginLayoutParams
            tagInputParams.bottomMargin = MARGIN_DP_30
            binding.tagInput.layoutParams = tagInputParams


            val createBtnParams = binding.create.layoutParams as MarginLayoutParams
            createBtnParams.marginEnd = MARGIN_DP_30
            binding.create.layoutParams = createBtnParams

            val backBtnParams = binding.back.layoutParams as MarginLayoutParams
            backBtnParams.marginStart = MARGIN_DP_30
            binding.back.layoutParams = backBtnParams
        } else if (DimensionUtil.isLandTwoThirdScreen(requireContext())) {
            val nameParams = binding.snippetNameInput.layoutParams as MarginLayoutParams
            nameParams.marginStart = MARGIN_DP_60
            nameParams.marginEnd = MARGIN_DP_60
            binding.snippetNameInput.layoutParams = nameParams

            val tagInputParams = binding.tagInput.layoutParams as MarginLayoutParams
            tagInputParams.bottomMargin = MARGIN_DP_30
            binding.tagInput.layoutParams = tagInputParams

            val createBtnParams = binding.create.layoutParams as MarginLayoutParams
            createBtnParams.marginEnd = MARGIN_DP_30
            binding.create.layoutParams = createBtnParams

            val backBtnParams = binding.back.layoutParams as MarginLayoutParams
            backBtnParams.marginStart = MARGIN_DP_30
            binding.back.layoutParams = backBtnParams

        } else if (DimensionUtil.isPortraitScreen(requireContext())) {
            val nameParams = binding.snippetNameInput.layoutParams as MarginLayoutParams
            nameParams.marginStart = MARGIN_DP_30
            nameParams.marginEnd = MARGIN_DP_30
            binding.snippetNameInput.layoutParams = nameParams

            val createBtnParams = binding.create.layoutParams as MarginLayoutParams
            createBtnParams.marginEnd = MARGIN_DP_30
            binding.create.layoutParams = createBtnParams

            val backBtnParams = binding.back.layoutParams as MarginLayoutParams
            backBtnParams.marginStart = MARGIN_DP_15
            binding.back.layoutParams = backBtnParams

            if (DimensionUtil.isPortraitAndOneThirdScreen(requireContext())) {
                val tagInputParams = binding.tagInput.layoutParams as MarginLayoutParams
                tagInputParams.bottomMargin = MARGIN_DP_5
                binding.tagInput.layoutParams = tagInputParams
            } else {
                val tagInputParams = binding.tagInput.layoutParams as MarginLayoutParams
                tagInputParams.bottomMargin = MARGIN_DP_30
                binding.tagInput.layoutParams = tagInputParams
            }
        } else {

        }
        updateHeaderAndTopGradientBackgroundLayoutToFitStatusBar()
    }

    private fun showSaveAlertDialog() {
        saveAlertDialog = AlertDialog.Builder()
            .setTitle(
                resources.getString(
                    if (args.isEditMode) {
                        R.string.note_snippet_edit_save_alert_dialog_title
                    } else {
                        R.string.note_snippet_create_save_alert_dialog_title
                    }
                )
            )
            .setPositiveBtn(resources.getString(R.string.instant_alpha_save)) {
                onSaveDialogPositiveBtnClick()
            }
            .setPositiveBtnColor(AppUtils.getColor(R.color.text_secondary))
            .setNegativeBtn(resources.getString(R.string.note_snippet_create_not_save)) {
                SnippetEvent.sendSnippetSaveDialogBtnClick("cancel")
                releaseResourcesExit(noteSnippetCreateViewModel.currentSnippet, isNotSave = true)
            }
            .setNegativeBtnColor(
                AppUtils.getColor(
                    R.color.note_list_delete_doc_text_color
                )
            ).build()
        saveAlertDialog?.safeShow(parentFragmentManager, SAVE_ALERT_DIALOG_TAG)
    }

    private fun showChangeModeAlertDialog() {
        changeModeAlertDialog = AlertDialog.Builder()
            .setTitle(resources.getString(R.string.note_snippet_create_change_mode_alert_dialog_title))
            .setMsg(resources.getString(R.string.note_snippet_create_change_mode_alert_dialog_message))
            .setPositiveBtn(resources.getString(R.string.cancel)) {

            }
            .setPositiveBtnColor(AppUtils.getColor(R.color.text_secondary))
            .setNegativeBtn(resources.getString(R.string.note_snippet_create_discard_content)) {
                onChangeModeDialogNegativeBtnClick()
            }
            .setNegativeBtnColor(
                AppUtils.getColor(
                    R.color.note_list_delete_doc_text_color
                )
            ).build()
        changeModeAlertDialog?.safeShow(parentFragmentManager, CHANGE_MODE_ALERT_DIALOG_TAG)
    }

    private fun tryToRebuildDialog() {
        val saveDialog = parentFragmentManager.findFragmentByTag(SAVE_ALERT_DIALOG_TAG)
        if (saveDialog != null && saveDialog is AlertDialog) {
            saveDialog.setPositiveBtnListener {
                onSaveDialogPositiveBtnClick()
            }
            saveDialog.setNegativeBtnListener {
                SnippetEvent.sendSnippetSaveDialogBtnClick("cancel")
                releaseResourcesExit(noteSnippetCreateViewModel.currentSnippet, isNotSave = true)
            }
        }
        val changeModeDialog =
            parentFragmentManager.findFragmentByTag(CHANGE_MODE_ALERT_DIALOG_TAG)
        if (changeModeDialog != null && changeModeDialog is AlertDialog) {
            changeModeDialog.setPositiveBtnListener {

            }
            changeModeDialog.setNegativeBtnListener {
                onChangeModeDialogNegativeBtnClick()
            }
        }
    }

    private fun checkBeforeChangeMode(destinationMode: NoteSnippetCreateMode): Boolean {
        val currentMode = noteSnippetCreateViewModel.currentSelectedMode.value ?: return false
        if (currentMode != destinationMode) {
            noteSnippetCreateViewModel.destinationMode = destinationMode
            if (currentMode == NoteSnippetCreateMode.TEXT) {
                return if (noteSnippetCreateViewModel.isAddedSnippet.value == true
                    && noteSnippetCreateViewModel.currentTextModeInputResult.value?.isNotBlank() == true
                ) {
                    showChangeModeAlertDialog()
                    false
                } else {
                    noteSnippetCreateViewModel.setCurrentTextModeInputResult(null)
                    true
                }
            } else {
                if (noteSnippetCreateViewModel.isAddedSnippet.value == true) {
                    showChangeModeAlertDialog()
                    return false
                }
            }
        }
        return true
    }

    private fun onChangeModeDialogNegativeBtnClick() {
        val destinationMode = noteSnippetCreateViewModel.destinationMode ?: return
        val currentMode = noteSnippetCreateViewModel.currentSelectedMode.value ?: return
        when (currentMode) {
            NoteSnippetCreateMode.TEXT -> {
                binding.snippetEditText.text.clear()
                noteSnippetCreateViewModel.setCurrentTextModeInputResult(null)
            }

            NoteSnippetCreateMode.IMAGE -> {
                noteSnippetCreateViewModel.setCurrentSelectedImageUri(null)
            }

            NoteSnippetCreateMode.CAMERA -> {
                noteSnippetCreateViewModel.setCurrentSelectedImageUri(null)
            }

            NoteSnippetCreateMode.DOODLE -> {
                noteSnippetCreateViewModel.deleteCurrentDoodleBitmap()
                val document = noteSnippetCreateViewModel.currentDoodleResultDoc
                if (document != null) {
                    val page = document.pages.firstOrNull()
                    if (page != null) {
                        val thumbnail = page.thumbnail
                        if (thumbnail != null) {
                            val file = document.resources.openFile(thumbnail)
                            if (file.exists()) {
                                file.delete()
                            }
                            page.thumbnail = null
                        }
                        page.draws?.clear()
                        NoteRepository.runInNoteOperationScopeAsync {
                            savePageInfo(page)
                            savePageDrawingElementsOrder(page, emptyList())
                        }
                    }
                }
            }
        }
        noteSnippetCreateViewModel.setCurrentSelectedMode(destinationMode)
        noteSnippetCreateViewModel.isContentClear = true
    }

    private fun onSaveDialogPositiveBtnClick() {
        SnippetEvent.sendSnippetSaveDialogBtnClick("save")
        saveSnippet()
    }

    private fun checkBeforeBack(): Boolean {
        val mode = noteSnippetCreateViewModel.currentSelectedMode.value ?: return true
        var currentSnippetThumbnailUri: Uri? = null
        noteSnippetCreateViewModel.currentSnippet?.let { snippet ->
            currentSnippetThumbnailUri = SnippetManager.getSnippetImageAbsoluteFileUri(
                snippet
            )
        }
        var notChanged = when (mode) {
            NoteSnippetCreateMode.TEXT -> {
                if (args.isEditMode) {
                    !noteSnippetCreateViewModel.isContentClear
                            && (!binding.snippetEditText.isVisible
                            || noteSnippetCreateViewModel.currentTextModeInputResult.value == null
                            || noteSnippetCreateViewModel.currentSnippet?.text == noteSnippetCreateViewModel.currentTextModeInputResult.value)
                } else {
                    noteSnippetCreateViewModel.currentSnippet?.text == noteSnippetCreateViewModel.currentTextModeInputResult.value
                }
            }

            NoteSnippetCreateMode.IMAGE -> {
                if (args.isEditMode) {
                    !noteSnippetCreateViewModel.isContentClear
                            && (noteSnippetCreateViewModel.currentSelectedImageUri.value == null
                            || currentSnippetThumbnailUri == noteSnippetCreateViewModel.currentSelectedImageUri.value)
                } else {
                    noteSnippetCreateViewModel.currentSelectedImageUri.value == null
                }
            }

            NoteSnippetCreateMode.CAMERA -> {
                if (args.isEditMode) {
                    !noteSnippetCreateViewModel.isContentClear
                            && (noteSnippetCreateViewModel.currentSelectedImageUri.value == null
                            || currentSnippetThumbnailUri == noteSnippetCreateViewModel.currentSelectedImageUri.value)
                } else {
                    noteSnippetCreateViewModel.currentSelectedImageUri.value == null
                }
            }

            NoteSnippetCreateMode.DOODLE -> {
                if (args.isEditMode) {
                    !noteSnippetCreateViewModel.isContentClear
                            && (noteSnippetCreateViewModel.currentDoodleBitmap.value == null
                            || currentSnippetThumbnailUri == noteSnippetCreateViewModel.currentDoodleThumbnailPath)
                } else {
                    noteSnippetCreateViewModel.currentDoodleBitmap.value == null
                }
            }
        }

        if (args.isEditMode) {
            noteSnippetCreateViewModel.currentSnippet?.let { snippet ->
                notChanged =
                    notChanged && (binding.snippetNameInput.text.toString() == snippet.title)
                noteSnippetCreateViewModel.currentSelectedColor.value?.let { color ->
                    notChanged = notChanged && (color == snippet.color)
                }
            }
        }

        return notChanged
    }


    private fun saveTextSnippet(snippetTags: List<SnippetTag>) {
        val color = noteSnippetCreateViewModel.currentSelectedColor.value
            ?: return
        val text = binding.snippetEditText.text.toString()
        val title = binding.snippetNameInput.text.toString()
        if (text.isBlank() || text.isEmpty()) {
            noteSnippetCreateViewModel.setCurrentTextModeInputResult(null)
            ToastUtils.topCenter(
                requireContext(),
                R.string.note_snippet_create_text_null_hint
            )
        } else {
            binding.snippetEditTextDuplicate.text = binding.snippetEditText.text
            GlobalScope.launch(Dispatchers.IO) {
                if (args.isEditMode) {
                    noteSnippetCreateViewModel.currentSnippet?.let { snippet ->
                        snippet.text = text
                        snippet.color = color
                        snippet.title = title
                        SnippetManager.updateSnippet(
                            snippet,
                            targetSnippetType = SnippetType.TEXT,
                        )
                        resetAndBindSnippetAndTags(snippet, snippetTags)
                    }
                } else {
                    val snippetId = UUID.randomUUID()
                    val snippet = SnippetManager.newTextSnippet(snippetId, text, color, title)
                    resetAndBindSnippetAndTags(snippet, snippetTags)
                }

                withContext(Dispatchers.Main) {
                    if (isAdded) {
                        releaseResourcesExit(
                            noteSnippetCreateViewModel.currentSnippet,
                            isNotSave = false
                        )
                    }
                }
            }
        }
    }

    private fun saveImageSnippet(
        snippetTags: List<SnippetTag>,
        createMode: NoteSnippetCreateMode
    ) {
        val color = noteSnippetCreateViewModel.currentSelectedColor.value
            ?: return
        val uri = noteSnippetCreateViewModel.currentSelectedImageUri.value
        val targetSnippetType = if (createMode ==
            NoteSnippetCreateMode.IMAGE
        ) SnippetType.IMAGE else SnippetType.CAMERA

        if (uri != null) {
            val title = binding.snippetNameInput.text.toString()
            if (args.isEditMode) {
                noteSnippetCreateViewModel.currentSnippet?.let { snippet ->
                    snippet.title = title
                    snippet.color = color
                    GlobalScope.launch(Dispatchers.IO) {
                        SnippetManager.updateSnippet(
                            snippet,
                            targetSnippetType,
                            imageUri = uri
                        )
                        SnippetManager.waitRecognizeFinished(snippet)
                        resetAndBindSnippetAndTags(snippet, snippetTags)
                        withContext(Dispatchers.Main) {
                            if (isAdded) {
                                releaseResourcesExit(
                                    noteSnippetCreateViewModel.currentSnippet,
                                    isNotSave = false
                                )
                            }
                        }
                    }
                }
            } else {
                GlobalScope.launch(Dispatchers.IO) {
                    SnippetManager.newImageSnippetFromUri(uri, color, title, targetSnippetType)
                        ?.let { snippet ->
                            resetAndBindSnippetAndTags(snippet, snippetTags)
                        }
                    withContext(Dispatchers.Main) {
                        if (isAdded) {
                            releaseResourcesExit(
                                noteSnippetCreateViewModel.currentSnippet,
                                isNotSave = false
                            )
                        }
                    }
                }
            }
        } else {
            ToastUtils.topCenter(
                requireContext(),
                R.string.note_snippet_create_text_null_hint
            )
        }
    }

    private fun saveDoodleSnippet(snippetTags: List<SnippetTag>) {
        val color = noteSnippetCreateViewModel.currentSelectedColor.value
            ?: return
        val title = binding.snippetNameInput.text.toString()

        if (noteSnippetCreateViewModel.currentDoodleBitmap.value != null) {
            noteSnippetCreateViewModel.currentSnippet?.let { snippet ->
                snippet.color = color
                snippet.title = title
                snippet.doodleInitialScale =
                    noteSnippetCreateViewModel.currentDoodleResultDoc?.get(0)?.initialScale
                        ?: -1F
                GlobalScope.launch(Dispatchers.IO) {
                    SnippetManager.updateSnippet(
                        snippet,
                        targetSnippetType = SnippetType.DOODLE,
                        thumbnailBitmap = noteSnippetCreateViewModel.currentDoodleBitmap.value
                    )
                    SnippetManager.waitRecognizeFinished(snippet)
                    resetAndBindSnippetAndTags(snippet, snippetTags)
                    withContext(Dispatchers.Main) {
                        if (isAdded) {
                            safePopBackStack()
                        }
                    }
                }
            }
        } else {
            ToastUtils.topCenter(
                requireContext(),
                R.string.note_snippet_create_text_null_hint
            )
        }
    }


    private fun saveSnippet() {
        val mode = noteSnippetCreateViewModel.currentSelectedMode.value
            ?: return
        val snippetTags =
            binding.tagInput.text.split(SnippetTagInputFilter.SNIPPET_TAG_INPUT_FILTER_SPLIT_WELL_NUMBER)
                .filter {
                    it.isNotEmpty()
                }.distinct()

        lifecycleScope.launch(Dispatchers.IO) {
            val snippetTagList = snippetTags.map { snippetTag ->
                LogHelper.d(TAG, "snippetTag = $snippetTag")
                SnippetManager.getOrCreateCommonTagByName(snippetTag)
            }.filterNotNull()

            if (!checkBeforeBack()) {
                noteSnippetCreateViewModel.currentSnippet?.let { snippet ->
                    snippet.documentId = null
                    snippet.pageId = null
                }
            }

            withContext(Dispatchers.Main) {
                snippetEditViewModel.updateCurrentEditSnippetTags(snippetTagList)
                when (mode) {
                    NoteSnippetCreateMode.TEXT -> {
                        SnippetEvent.sendSnippetCreateSuccess("text", snippetTagList.size)
                        saveTextSnippet(snippetTagList)
                    }

                    NoteSnippetCreateMode.DOODLE -> {
                        SnippetEvent.sendSnippetCreateSuccess("doodle", snippetTagList.size)
                        saveDoodleSnippet(snippetTagList)
                    }

                    NoteSnippetCreateMode.IMAGE -> {
                        SnippetEvent.sendSnippetCreateSuccess("image", snippetTagList.size)
                        saveImageSnippet(snippetTagList, mode)
                    }

                    NoteSnippetCreateMode.CAMERA -> {
                        SnippetEvent.sendSnippetCreateSuccess("camera", snippetTagList.size)
                        saveImageSnippet(snippetTagList, mode)
                    }
                }
            }
        }
    }

    override fun isListenerWindowInsets(): Boolean {
        return true
    }

    @SuppressLint("ServiceCast")
    override fun onWindowInsetsChange(
        isSoftKeyboardShowing: Boolean,
        softKeyboardHeight: Int,
        isStatusBarShowing: Boolean,
        statusBarHeight: Int,
        isNavigationBarShowing: Boolean,
        navigationBarHeight: Int
    ) {
        super.onWindowInsetsChange(
            isSoftKeyboardShowing,
            softKeyboardHeight,
            isStatusBarShowing,
            statusBarHeight,
            isNavigationBarShowing,
            navigationBarHeight
        )
        binding.root.setPadding(
            binding.root.paddingLeft,
            binding.root.paddingTop,
            binding.root.paddingRight,
            navigationBarHeight
        )
        onSoftKeyboardChange(isSoftKeyboardShowing, softKeyboardHeight)
    }

    fun setInputFocusable(view: View, focusable: Boolean) {
        view.isFocusable = focusable
        view.isFocusableInTouchMode = focusable
    }

    private fun isSoftKeyboardEqualWithLastStatus(
        isSoftKeyboardShowing: Boolean,
        softKeyboardHeight: Int
    ): Boolean {
        return (isSoftKeyboardShowing == lastSoftKeyboardShowStatus
                && softKeyboardHeight == lastSoftKeyboardHeight)
    }

    private fun onSoftKeyboardChange(
        isSoftKeyboardShowing: Boolean,
        softKeyboardHeight: Int
    ) {
        if (isSoftKeyboardEqualWithLastStatus(isSoftKeyboardShowing, softKeyboardHeight)) {
            return
        }
        lastSoftKeyboardShowStatus = isSoftKeyboardShowing
        lastSoftKeyboardHeight = softKeyboardHeight
        if (isSoftKeyboardShowing) {
            //计算操作栏输入光标能够完全显示所需要的偏移量
            if (binding.snippetEditText.hasFocus()) {
                val cursorPosition: Int = binding.snippetEditText.selectionStart
                if (cursorPosition != -1) {
                    val layout = binding.snippetEditText.layout
                    layout?.let {
                        val windowHeight =
                            requireContext().resources.displayMetrics.heightPixels
                        //获取EditText可见区域相对于整个EditText内容的坐标
                        val visibleTextRect = Rect()
                        binding.snippetEditText.getLocalVisibleRect(visibleTextRect)
                        //文字顶部至EditText布局顶部空白高度
                        val topBlankHeight =
                            if ((binding.snippetEditText.height - layout.height) / 2 > 0) {
                                (binding.snippetEditText.height - layout.height) / 2
                            } else {
                                0
                            }
                        //光标距离屏幕顶部距离
                        val textAbsoluteHeight =
                            editModeInputViewLocation[1].toFloat() + layout.getLineBaseline(
                                layout.getLineForOffset(cursorPosition)
                            ) - visibleTextRect.top + topBlankHeight
                        editModeInputCursorCurrentLine = layout.getLineForOffset(cursorPosition)
                        //光标至屏幕底部距离
                        val textToWindowBottomDistance = windowHeight - textAbsoluteHeight
                        val yOffset = softKeyboardHeight - textToWindowBottomDistance
                        if (yOffset > 0) {
                            binding.operationBar.translationY =
                                -(yOffset + softKeyboardInterval)
                        }
                    }
                }
            }
            if (binding.tagInput.hasFocus()) {
                binding.operationBar.translationY = -softKeyboardHeight.toFloat()
                binding.readd.translationY = -softKeyboardHeight.toFloat()
                binding.addTag.translationY = -softKeyboardHeight.toFloat()
                binding.tagInput.translationY = -softKeyboardHeight.toFloat()
            }
            (activity as? MainActivity)?.registerClearFocusAndHideSoftKeyBoardListener(
                isIntercept = { true }
            )
        } else {
            setInputFocusable(binding.tagInput, false)
            setInputFocusable(binding.snippetNameInput, false)
            setInputFocusable(binding.snippetEditText, false)
            editModeInputCursorCurrentLine = null
            if (binding.tagInput.hasFocus()) {
                binding.tagInput.clearFocus()
            }
            if (binding.snippetNameInput.hasFocus()) {
                binding.snippetNameInput.clearFocus()
            }
            if (binding.snippetEditText.hasFocus()) {
                binding.snippetEditText.clearFocus()
            }
            setInputFocusable(binding.tagInput, true)
            setInputFocusable(binding.snippetNameInput, true)
            setInputFocusable(binding.snippetEditText, true)
            binding.operationBar.translationY = 0F
            binding.readd.translationY = 0F
            binding.addTag.translationY = 0F
            binding.tagInput.translationY = 0F
        }
    }

    private fun takePhoto() {
        try {
            pictureUri = generatePictureUri()
            noteSnippetCreateViewModel.setCurrentTakePhotoUri(pictureUri)
            takePhotoLauncher.launch(pictureUri)
        } catch (e: ActivityNotFoundException) {
            ToastUtils.topCenter(requireContext(), R.string.note_snippet_create_camera_unsupported)
            SnippetEvent.sendSnippetCreateCameraUnsupported()
        }
    }

    private fun openAlbum() {
        val needRequestPermission = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_EXTERNAL_STORAGE
        } else {
            Manifest.permission.READ_MEDIA_IMAGES
        }
        PermissionRequester.showPermissionRationaleDialogThenRequest(
            title = AppUtils.getString(R.string.permission_rationale_title_for_storage),
            message = AppUtils.getString(R.string.permission_rationale_content_for_storage),
            permissionState = PermissionRequester.checkPermissionState(
                requireActivity(),
                needRequestPermission
            ),
            fragmentManager = parentFragmentManager
        ) {
            permissionRequester.request(needRequestPermission) { isGranted ->
                if (isGranted) {
                    if (noteSnippetCreateViewModel.isOpenAlbum) return@request
                    noteSnippetCreateViewModel.changeOpenAlbumStatus(true)
                    selectImageLauncher.launch(
                        Intent(
                            requireContext(),
                            SelectPhotoDialogActivity::class.java
                        ).apply {
                            putExtra(SelectPhotoDialogActivity.BUNDLE_KEY_IGNORE_DETAIL_IMAGE, true)
                        }
                    )
                } else {
                    val permissionState = PermissionRequester.checkPermissionState(
                        this,
                        needRequestPermission
                    )
                    if (permissionState == PermissionRequester.PermissionState.PERMISSION_NOT_ASK_AGAIN) {
                        val alertDialog = AlertDialog.Builder()
                            .setMsg(resources.getString(R.string.never_aks_read_external_storage))
                            .setPositiveBtn(resources.getString(R.string.go_to_set)) {
                                PermissionRequester.jumpToSetting(this)
                            }
                            .setNegativeBtn(resources.getString(R.string.ok)) {

                            }
                            .build()
                        alertDialog.show(parentFragmentManager, null)
                    }
                }
            }
        }
    }

    // only call with observer
    private fun insertImageElement(
        path: Uri,
    ) {
        lifecycleScope.launch(Dispatchers.IO) {
            if (!isAdded) return@launch
            val isDamage = ImageUtils.checkImageDamage(path, requireContext())
            if (isDamage) {
                withContext(Dispatchers.Main) {
                    ToastUtils.topCenter(requireContext(), R.string.toast_image_damage)
                }
            } else {
                withContext(Dispatchers.Main) {
                    if (!isAdded) return@withContext
                    Glide.with(requireContext())
                        .asBitmap()
                        .load(path)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .into(object : CustomTarget<Bitmap>() {
                            override fun onResourceReady(
                                resource: Bitmap,
                                transition: Transition<in Bitmap>?
                            ) {
                                if (!isAdded) return
                                val width = resource.width
                                val height = resource.height
                                val params = binding.operationModeImageContent.layoutParams
                                params.height =
                                    height * binding.operationModeImageContent.width / width

                                val scrollViewParams =
                                    binding.operationScrollContainer.layoutParams
                                scrollViewParams.height = if (
                                    height * binding.operationModeImageContent.width / width <
                                    binding.operationScrollContainer.height
                                ) {
                                    height * binding.operationModeImageContent.width / width
                                } else {
                                    requireContext().resources.getDimension(R.dimen.dp_0)
                                        .toInt()
                                }
                                binding.operationScrollContainer.layoutParams = scrollViewParams
                                binding.operationModeImageContent.layoutParams = params
                                binding.operationModeImageContent.setImageBitmap(resource)
                            }

                            override fun onLoadCleared(placeholder: Drawable?) {

                            }

                        })
                }
            }
        }
    }

    private fun generatePictureUri(): Uri? {
        return try {
            val dir = File(AppUtils.appContext.externalCacheDir, "pictures")
            if (!dir.exists()) {
                dir.mkdirs()
            }
            val file = File(dir, "pictureFromCamera.jpeg")
            if (file.exists()) {
                file.delete()
            }
            FileProvider.getUriForFile(
                AppUtils.appContext,
                BuildConfig.APPLICATION_ID + ".provider",
                file
            )
        } catch (e: Exception) {
            null
        }
    }

    override fun onCropSuccess(uri: Uri, imageAlpha: Int) {
        photoCropDialogFragment?.dismiss()
        noteSnippetCreateViewModel.setCurrentSelectedImageUri(uri)
        pictureUri = null
    }

    override fun onCropFailed() {
        pictureUri = null
    }

    override fun onCancel() {
        noteSnippetCreateViewModel.currentTakePhotoUri.value?.let { currentTakePhotoUri ->
            noteSnippetCreateViewModel.setCurrentSelectedImageUri(currentTakePhotoUri)
        }
        pictureUri = null
    }

    private suspend fun resetAndBindSnippetAndTags(
        snippet: NoteSnippet,
        snippetTags: List<SnippetTag>
    ) {
        val snippetWithTags = SnippetManager.getSnippetAndTags(snippet.snippetId.toString())
        snippetWithTags?.tags?.forEach { snippetTagId ->
            SnippetManager.unbindSnippetAndTag(
                snippetWithTags.noteSnippet.snippetId,
                snippetTagId.tagId
            )
        }
        SnippetManager.bindSnippetAndTags(snippet, snippetTags)
    }

    private fun displayTagsIfEditSnippet() {
        if (noteSnippetCreateViewModel.tagInputIsVisible.value == false) {
            snippetEditViewModel.currentEditSnippet?.snippetId?.let { currentSnippetId ->
                lifecycleScope.launch(Dispatchers.IO) {
                    val snippetAndTags =
                        SnippetManager.getSnippetAndTags(currentSnippetId.toString())
                    var tagInputContent = ""
                    snippetAndTags?.tags?.forEach { snippetTag ->
                        tagInputContent += "${SnippetTagInputFilter.SNIPPET_TAG_INPUT_FILTER_SPLIT_WELL_NUMBER}${snippetTag.name.trim()}"
                    }
                    if (tagInputContent.isNotEmpty()) {
                        withContext(Dispatchers.Main) {
                            noteSnippetCreateViewModel.setTagInputVisible()
                            binding.tagInput.setText(tagInputContent)
                        }
                    }
                }
            }
        }
    }

    private fun releaseResourcesExit(noteSnippet: NoteSnippet?, isNotSave: Boolean) {
        if (noteSnippet == null) {
            safePopBackStack()
        } else {
            if (args.isEditMode) {
                if (noteSnippet.doodleId != null && noteSnippet.snippetType == SnippetType.DOODLE) {
                    SnippetManager.clearDocumentCache(noteSnippet.snippetId)
                }
                if (noteSnippet.doodleId != null && noteSnippet.snippetType != SnippetType.DOODLE) {
                    noteSnippetCreateViewModel.deleteSnippetDocument(noteSnippet) {
                        safePopBackStack()
                    }
                } else {
                    safePopBackStack()
                }
            } else {
                if (isNotSave || noteSnippet.snippetType != SnippetType.DOODLE && noteSnippet.doodleId != null) {
                    noteSnippetCreateViewModel.deleteSnippetDocument(noteSnippet) {
                        safePopBackStack()
                    }
                } else {
                    safePopBackStack()
                }
            }
        }
    }

    override fun getCurrentNavigationId(): Int {
        return R.id.create_snippet_fragment
    }

    override fun isShieldSoftInputModeAdjust(): Boolean {
        //MatePadPro在左侧分屏时EditText获取焦点后会被键盘遮挡时系统会推动window导致页面卡死，所以针对MatePadPro做特殊处理
        return if (ModelUtils.isHuaweiMatePadPro()) {
            true
        } else {
            super.isShieldSoftInputModeAdjust()
        }
    }
}
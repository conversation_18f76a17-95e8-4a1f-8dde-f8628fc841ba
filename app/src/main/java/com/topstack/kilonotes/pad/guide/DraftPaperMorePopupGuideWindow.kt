package com.topstack.kilonotes.pad.guide

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.view.marginBottom
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.ktx.setMargins
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.DraftPaperNoteMorePopupGuideBinding
import com.topstack.kilonotes.databinding.DraftPaperNoteMorePopupGuideLandOneThirdBinding
import com.topstack.kilonotes.databinding.DraftPaperNoteMorePopupGuidePortraitThirdScreenBinding

class DraftPaperMorePopupGuideWindow(var context: Context, isLandOneThirdScreen: Boolean) :
    PopupWindow() {

    companion object {
        const val DEFAULT_TIPS_LINE = 2
    }

    private var rootView: View
    private var know: TextView
    private var knowBg: ImageView
    private var tips: TextView
    private var distanceY: Int = 0
    private var arrow: ImageView

    init {
        if (isLandOneThirdScreen) {
            val binding =
                DraftPaperNoteMorePopupGuideLandOneThirdBinding.inflate(LayoutInflater.from(context))
            rootView = binding.root
            know = binding.oneThirdScreenKnow
            knowBg = binding.knowBg
            tips = binding.oneThirdScreenTips
            arrow = binding.oneThirdScreenArrow
        } else if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context) || DimensionUtil.isPortraitAndOneThirdScreen(
                context
            )
        ) {
            val binding = DraftPaperNoteMorePopupGuidePortraitThirdScreenBinding.inflate(
                LayoutInflater.from(context)
            )
            rootView = binding.root
            know = binding.knowPortraitThird
            knowBg = binding.knowBg
            tips = binding.tipsPortraitThird
            arrow = binding.arrowPortraitThird
        } else {
            val binding = DraftPaperNoteMorePopupGuideBinding.inflate(LayoutInflater.from(context))
            rootView = binding.root
            know = binding.know
            knowBg = binding.knowBg
            tips = binding.tips
            arrow = binding.arrow
            if (DimensionUtil.isPortraitAndHalfScreen((context))
            ) {
                know.setMargins(
                    know.marginStart,
                    context.resources.getDimensionPixelSize(R.dimen.dp_5),
                    know.marginEnd,
                    know.marginBottom
                )
            }
        }
        isFocusable = true
        isOutsideTouchable = true
        contentView = rootView
        width = LinearLayout.LayoutParams.WRAP_CONTENT
        height = LinearLayout.LayoutParams.WRAP_CONTENT
        know.setOnClickListener {
            UserUsageConfig.isNeedShowDraftPaperMorePopupGuide = false
            dismiss()
        }
        rootView.measure(
            View.MeasureSpec.UNSPECIFIED,
            View.MeasureSpec.UNSPECIFIED
        )
        distanceY =
            if (DimensionUtil.isPortraitAndHalfScreen((context))) {
                (rootView.measuredHeight * 0.45).toInt()
            } else if (isLandOneThirdScreen) {
                context.resources.getDimensionPixelSize(
                    R.dimen.dp_0
                )
            } else {
                (rootView.measuredHeight * 0.40).toInt()
            }
        arrow.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        knowBg.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)

    }

    fun show(view: View, settingXOffset: Int, settingYOffset: Int) {
        showAtLocation(
            view,
            Gravity.NO_GRAVITY,
            if (KiloApp.isLayoutRtl) settingXOffset else settingXOffset - rootView.measuredWidth,
            if (DimensionUtil.isPortraitAndOneThirdScreen((context))) context.resources.getDimensionPixelSize(
                R.dimen.dp_115
            ) else settingYOffset - distanceY
        )


    }


    fun getTitleLines(): Int {
        return tips.lineCount
    }

}
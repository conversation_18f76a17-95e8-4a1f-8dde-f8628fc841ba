package com.topstack.kilonotes.pad.customtool.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.databinding.ItemCustomShowTitleBinding

class CustomShowTitleAdapter(val context: Context, val title: String) :
    RecyclerView.Adapter<CustomShowTitleAdapter.ShowTitleViewHolder>() {

    inner class ShowTitleViewHolder(val binding: ItemCustomShowTitleBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.showTitle.text = title
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ShowTitleViewHolder {
        return ShowTitleViewHolder(
            ItemCustomShowTitleBinding.inflate(
                LayoutInflater.from(
                    context
                ),
                parent,
                false
            )
        )
    }

    override fun getItemCount(): Int {
        return 1
    }

    override fun onBindViewHolder(holder: ShowTit<PERSON><PERSON><PERSON>wHolder, position: Int) {

    }
}
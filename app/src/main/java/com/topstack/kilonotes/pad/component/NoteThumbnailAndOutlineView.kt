package com.topstack.kilonotes.pad.component

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.note.listener.ThumbnailTouchAction
import com.topstack.kilonotes.base.track.event.OutlineEvent
import com.topstack.kilonotes.databinding.NoteThumbnailAndOutlineViewBinding
import com.topstack.kilonotes.pad.note.adapter.NoteOutlinePagerAdapter
import com.topstack.kilonotes.pad.note.adapter.NoteThumbnailPagerAdapter
import com.topstack.kilonotes.pad.note.model.ThumbnailActionWindowStatus
import com.topstack.kilonotes.pad.note.outline.OutlineEntity

class NoteThumbnailAndOutlineView : ConstraintLayout {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes)

    companion object {
        private const val MODE_THUMBNAIL = 0
        private const val MODE_OUTLINE = 1
        private const val ANIMATION_DURATION = 200L
        private const val THUMBNAIL_LIST_DEFAULT_SPAN_COUNT = 2
    }

    private val binding: NoteThumbnailAndOutlineViewBinding

    private var noteThumbnailAdapter: NoteThumbnailPagerAdapter? = null
    private var noteOutlinePagerAdapter: NoteOutlinePagerAdapter? = null

    private var onThumbnailTabClickListener: (() -> Unit)? = null
    private var onOutlineTabClickListener: (() -> Unit)? = null
    private var onOutlineAddClickListener: (() -> Unit)? = null

    private var thumbnailListSpanCount = THUMBNAIL_LIST_DEFAULT_SPAN_COUNT

    private var isFirstTimeChangeMode = true


    private var currentDoc: Document? = null
        set(value) {
            field = value
            if (value != null) {
                initViewPager(value, thumbnailListSpanCount)
                initListener()
            }
        }

    private var currentSelectMode: Int = MODE_THUMBNAIL
        set(value) {
            field = value
            if (value == MODE_THUMBNAIL) {
                isOutlineEditMode = false
            }
            changeTabSelectedStatus()
            //转屏时ViewModel会传来当前选中模式，用于恢复设置的模式，但若smoothScroll设为true即开启动画时页面会发生错误，故首次设置模式时不开启动画
            binding.viewPager.setCurrentItem(value, !isFirstTimeChangeMode)
            isFirstTimeChangeMode = false
        }

    private var isOutlineEditMode: Boolean = false
        set(value) {
            if (field != value) {
                field = value
                binding.viewPager.post {
                    updateLayoutWithOutlineEditModeChange()
                    noteOutlinePagerAdapter?.setIsEditMode(value)
                    binding.viewPager.isUserInputEnabled = !value
                }
            }
        }

    init {
        binding =
            NoteThumbnailAndOutlineViewBinding.inflate(LayoutInflater.from(context), this, true)
        changeTabSelectedStatus()
    }


    private fun initViewPager(doc: Document, thumbnailListSpanCount: Int) {
        noteThumbnailAdapter =
            NoteThumbnailPagerAdapter(context, doc, spanCount = thumbnailListSpanCount)
        noteOutlinePagerAdapter = NoteOutlinePagerAdapter(context, doc).apply {
            setIsEditMode(isOutlineEditMode)
        }
        binding.viewPager.adapter = ConcatAdapter(noteThumbnailAdapter, noteOutlinePagerAdapter)
        binding.viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
            }

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                if (position == MODE_THUMBNAIL) {
                    onThumbnailTabClickListener?.invoke()
                } else {
                    onOutlineTabClickListener?.invoke()
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
            }
        })
    }

    private fun initSelector() {
        binding.thumbnailTab.setOnClickListener {
            onThumbnailTabClickListener?.invoke()
        }
        binding.outlineTab.setOnClickListener {
            OutlineEvent.sendOutlineSidebarTabClickEvent()
            onOutlineTabClickListener?.invoke()
        }
    }

    private fun initListener() {
        initSelector()
        binding.editToAddText.setOnClickListener {
            OutlineEvent.outlineDialogCreateSource = OutlineEvent.EDIT_ADD
            onOutlineAddClickListener?.invoke()
        }
        binding.editToAddImage.setOnClickListener {
            OutlineEvent.outlineDialogCreateSource = OutlineEvent.EDIT_ADD
            onOutlineAddClickListener?.invoke()
        }
    }

    private fun changeTabSelectedStatus() {
        binding.thumbnailTab.isSelected = currentSelectMode == MODE_THUMBNAIL
        binding.outlineTab.isSelected = currentSelectMode == MODE_OUTLINE
        val set = ConstraintSet()
        set.clone(binding.topTabSelector)
        if (currentSelectMode == MODE_THUMBNAIL) {
            set.connect(
                binding.indicator.id,
                ConstraintSet.START,
                binding.thumbnailTab.id,
                ConstraintSet.START
            )
            set.connect(
                binding.indicator.id,
                ConstraintSet.END,
                binding.thumbnailTab.id,
                ConstraintSet.END
            )
        } else {
            set.connect(
                binding.indicator.id,
                ConstraintSet.START,
                binding.outlineTab.id,
                ConstraintSet.START
            )
            set.connect(
                binding.indicator.id,
                ConstraintSet.END,
                binding.outlineTab.id,
                ConstraintSet.END
            )
        }
        androidx.transition.TransitionManager.beginDelayedTransition(
            binding.topTabSelector,
            androidx.transition.AutoTransition().apply {
                duration = ANIMATION_DURATION
            })
        set.applyTo(binding.topTabSelector)
    }

    private fun updateLayoutWithOutlineEditModeChange() {
        if (currentSelectMode == MODE_THUMBNAIL) {
            setOutlineIsEditMode(false)
            return
        }
        binding.outlineTab.isVisible = !isOutlineEditMode
        binding.thumbnailTab.isVisible = !isOutlineEditMode
        binding.indicator.isVisible = !isOutlineEditMode
        binding.editToAddImage.isVisible = isOutlineEditMode
        binding.editToAddText.isVisible = isOutlineEditMode
    }

    fun setDocument(document: Document) {
        currentDoc = document
    }

    fun getNoteThumbnailAdapter(): NoteThumbnailPagerAdapter? {
        return noteThumbnailAdapter
    }

    fun addThumbnailPageWithIndex(position: Int, page: Page) {
        noteThumbnailAdapter?.addPageWithIndex(position, page)
    }

    fun appendThumbnailPage(page: Page) {
        noteThumbnailAdapter?.appendPage(page)
    }

    fun findViewInThumbnailByPosition(position: Int): View? {
        return noteThumbnailAdapter?.findViewByPosition(position)
    }

    fun notifyThumbnailItemInsert(position: Int) {
        noteThumbnailAdapter?.notifyItemInserted(position)
    }

    fun setThumbnailPageWithIndex(pageIndex: Int, page: Page) {
        noteThumbnailAdapter?.setPage(pageIndex, page)
    }

    fun setOnThumbnailListScrollListener(listener: RecyclerView.OnScrollListener) {
        noteThumbnailAdapter?.setOnThumbnailListScrollListener(listener)
    }

    fun setThumbnailTouchAction(action: ThumbnailTouchAction) {
        noteThumbnailAdapter?.setThumbnailTouchAction(action)
    }

    fun setOnThumbnailPageIndexChangeListener(action: () -> Unit) {
        noteThumbnailAdapter?.setOnPageIndexChangeListener(action)
    }

    fun setAfterThumbnailCurrentPageChange(action: () -> Unit) {
        noteThumbnailAdapter?.setAfterCurrentPageChange(action)
    }

    fun setAfterThumbnailScrollStateChange(action: (position: Int, offset: Int) -> Unit) {
        noteThumbnailAdapter?.setAfterScrollStateChange(action)
    }

    fun setAfterInitThumbnailList(action: () -> Unit) {
        noteThumbnailAdapter?.setAfterInitThumbnailList(action)
    }

    fun setOnThumbnailItemClickListener(action: (position: Int) -> Unit) {
        noteThumbnailAdapter?.setOnItemClick(action)
    }

    fun setOnThumbnailMenuClickListener(action: (params: ThumbnailActionWindowStatus) -> Unit) {
        noteThumbnailAdapter?.setOnMenuClick(action)
    }

    fun findThumbnailFirstVisibleItemPosition(): Int {
        return noteThumbnailAdapter?.findFirstVisibleItemPosition() ?: -1
    }

    fun findThumbnailLastVisibleItemPosition(): Int {
        return noteThumbnailAdapter?.findLastVisibleItemPosition() ?: -1
    }

    fun notifyThumbnailItemChanged(position: Int) {
        noteThumbnailAdapter?.notifyThumbnailItemChanged(position)
    }

    fun changeThumbnailCheckedItem(position: Int) {
        noteThumbnailAdapter?.changeCheckedItem(position)
    }

    fun removeThumbnailPage(page: Page) {
        noteThumbnailAdapter?.removePage(page)
    }

    fun notifyThumbnailItemRemoved(position: Int) {
        noteThumbnailAdapter?.notifyThumbnailItemRemoved(position)
    }

    fun scrollThumbnailToPositionWithOffset(position: Int, offset: Int) {
        noteThumbnailAdapter?.scrollToPositionWithOffset(position, offset)
    }

    fun scrollThumbnailToPosition(position: Int) {
        noteThumbnailAdapter?.scrollToPosition(position)
    }

    fun notifyThumbnailItemRangeChanged(startPos: Int, endPos: Int) {
        noteThumbnailAdapter?.notifyThumbnailItemRangeChanged(startPos, endPos)
    }

    fun getThumbnailItemCount(): Int {
        return noteThumbnailAdapter?.getThumbnailItemCount() ?: 0
    }

    fun updatePageThumbnail() {
        noteThumbnailAdapter?.updatePageThumbnail()
    }

    fun setAfterUpdatePageIndexCallBack(action: (previousPage: Page?, nextPage: Page?, pageIndex: Int) -> Unit) {
        noteThumbnailAdapter?.setAfterUpdatePageIndexCallBack(action)
    }

    fun setOnThumbnailTabClickListener(action: () -> Unit) {
        onThumbnailTabClickListener = action
    }

    fun setOnOutlineTabClickListener(action: () -> Unit) {
        onOutlineTabClickListener = action
    }

    fun setCurrentSelectedMode(mode: Int) {
        currentSelectMode = mode
    }

    fun updateCurrentSelectedOutlineList(list: List<OutlineEntity>) {
        noteOutlinePagerAdapter?.updateCurrentSelectedOutlineList(list)
    }

    fun setOnOutlineAddBtnClickListener(action: () -> Unit) {
        onOutlineAddClickListener = action
        noteOutlinePagerAdapter?.setOnAddBtnClickListener(action)
    }

    fun setOnOutlineEditBtnClickListener(action: () -> Unit) {
        noteOutlinePagerAdapter?.setOnEditBtnClickListener(action)
    }

    fun setOnOutlineItemNormalModeClickListener(action: (outlineEntity: OutlineEntity) -> Unit) {
        noteOutlinePagerAdapter?.setOnOutlineItemNormalModeClickListener(action)
    }

    fun setOnOutlineItemEditModeClickListener(action: (outlineEntity: OutlineEntity) -> Unit) {
        noteOutlinePagerAdapter?.setOnOutlineItemEditModeClickListener(action)
    }

    fun setOnOutlineItemEditModeCheckedListener(action: (outlineEntity: OutlineEntity, isSelected: Boolean) -> Unit) {
        noteOutlinePagerAdapter?.setOnOutlineItemEditModeCheckedListener(action)
    }

    fun setOnOutlineEditAllSelectedClickListener(action: (Boolean) -> Unit) {
        noteOutlinePagerAdapter?.setOnEditAllSelectedClickListener(action)
    }

    fun setOnOutlineEditDeleteBtnClickListener(action: () -> Unit) {
        noteOutlinePagerAdapter?.setOnEditDeleteBtnClickListener(action)
    }

    fun setOnOutlineEditFinishBtnClickListener(action: () -> Unit) {
        noteOutlinePagerAdapter?.setOnEditFinishBtnClickListener(action)
    }

    fun setOutlineIsEditMode(isEditMode: Boolean) {
        isOutlineEditMode = isEditMode

    }

    fun setThumbnailListSpanCount(spanCount: Int) {
        thumbnailListSpanCount = spanCount
    }

    fun notifyOutlineDataSetChanged() {
        if (currentDoc?.outlineList?.isEmpty() == true) {
            setOutlineIsEditMode(false)
        }
        noteOutlinePagerAdapter?.notifyOutlineDataSetChanged()
    }

    fun getThumbnailListViewHeight(): Int {
        return binding.viewPager.height
    }

    fun setCurrentSelectedOutlineList(currentSelectedOutlineEntities: MutableList<OutlineEntity>) {
        noteOutlinePagerAdapter?.setCurrentSelectedOutlineList(currentSelectedOutlineEntities)
    }

}
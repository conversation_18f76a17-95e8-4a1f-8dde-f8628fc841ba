package com.topstack.kilonotes.pad.select

import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.navigation.fragment.findNavController
import com.bumptech.glide.Glide
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.select.BUNDLE_URI_KEY
import com.topstack.kilonotes.base.select.PICK_PIC_RESULT_CODE
import com.topstack.kilonotes.databinding.FragmentPickPhotoBinding

class PickPhotoFragment : BaseFragment(R.layout.fragment_pick_photo) {
    private lateinit var binding: FragmentPickPhotoBinding

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentPickPhotoBinding.bind(view)


        val uri = arguments?.getParcelable<Uri>(BUNDLE_URI_KEY)
        if (null == uri) {
            findNavController().popBackStack()
        }
        Glide.with(this)
            .load(uri)
            .placeholder(R.drawable.note_main_sidebar_pic_default)
            .error(R.drawable.note_main_sidebar_pic_error)
            .into(binding.bigPic)

        binding.back.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.useTv.setOnClickListener {
            val activity = requireActivity()
            val intent = activity.intent
            intent.putExtra(BUNDLE_URI_KEY, uri)
            activity.setResult(PICK_PIC_RESULT_CODE, intent)
            activity.finish()
        }
    }


}
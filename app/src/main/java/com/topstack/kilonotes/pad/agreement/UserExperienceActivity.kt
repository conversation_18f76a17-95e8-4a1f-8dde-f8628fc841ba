package com.topstack.kilonotes.pad.agreement

import android.os.Bundle
import androidx.core.view.isVisible
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.ad.AdHelper
import com.topstack.kilonotes.base.config.Preferences.userExperience
import com.topstack.kilonotes.base.component.activity.BaseDialogActivity
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.databinding.UserExperienceActivityBinding

class UserExperienceActivity : BaseDialogActivity() {
    private lateinit var binding: UserExperienceActivityBinding
    private val upm by lazy { AdHelper.getUMP(this)}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = UserExperienceActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initView()
        initListener()
    }

    private fun initView() {
        binding.userExperienceSwitch.isChecked = userExperience
        binding.userExperienceBack.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
        binding.thirdPartyAd.isVisible = upm.isPrivacyOptionsRequired
    }

    private fun initListener() {
        binding.userExperienceBack.setOnClickListener { finish() }
        binding.userExperienceSwitch.setOnCheckedChangeListener { _, isChecked ->
            userExperience = isChecked
        }
        binding.thirdPartyAd.setOnClickListener {
            upm.showPrivacyOptionsForm(this) {}
        }
    }

}
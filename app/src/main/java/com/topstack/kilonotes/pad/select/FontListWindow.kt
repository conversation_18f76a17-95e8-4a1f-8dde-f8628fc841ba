package com.topstack.kilonotes.pad.select

import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import androidx.recyclerview.widget.LinearLayoutManager
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.BubbleLayout
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.fonts.FontGroupInfo
import com.topstack.kilonotes.base.fonts.FontInfo
import com.topstack.kilonotes.base.fonts.IFontStateListener
import com.topstack.kilonotes.base.ktx.removeAllItemDecorations
import com.topstack.kilonotes.databinding.DialogFontListBinding
import com.topstack.kilonotes.infra.util.AppUtils.getString
import com.topstack.kilonotes.pad.select.adapter.FontAdapter

class FontListWindow(
    val context: Context,
    var fontGroupInfo: FontGroupInfo,
) : PopupWindow() {


    var onFontChangeAction: ((fontInfo: FontInfo) -> Unit)? = null
        set(value) {
            field = value
            fontAdapter.onFontChangeAction = value
        }

    var fontDown: ((url: String, listener: IFontStateListener) -> Unit)? = null
        set(value) {
            field = value
            fontAdapter.fontDown = value
        }


    private val binding: DialogFontListBinding by lazy {
        DialogFontListBinding.inflate(LayoutInflater.from(context))
    }

    val fontAdapter = FontAdapter(context, fontGroupInfo.fontTypeGroupList).also { adapter ->
        adapter.onFontChangeAction = this.onFontChangeAction
        adapter.fontDown = this.fontDown
    }

    var fontAdd: (() -> Unit)? = null

    init {
        height = context.resources.getDimensionPixelSize(R.dimen.dp_792)
        width = context.resources.getDimensionPixelSize(R.dimen.dp_475)
        isFocusable = true
        isOutsideTouchable = true
        initView()
    }


    fun initView() {
        binding.fontList.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        refreshItemDecoration()
        binding.fontList.adapter = fontAdapter
        binding.fontAdd.setOnClickListener(AntiShakeClickListener {
            fontAdd?.invoke()
        })
    }

    fun refreshFontList(
        fontGroupInfo: FontGroupInfo,
    ) {
        this.fontGroupInfo = fontGroupInfo
        refreshItemDecoration()
        this.fontAdapter.refreshFontList(fontGroupInfo.fontTypeGroupList)
    }

    private fun refreshItemDecoration() {
        context.let {
            binding.fontList.removeAllItemDecorations()
            binding.fontList.addItemDecoration(object :
                StickyDecoration(1, it) {
                override fun getStickyHeaderName(position: Int): String {
                    val fontTypeGroupList = fontGroupInfo.fontTypeGroupList
                    val recentlyUsedSize = fontGroupInfo.recentlyUsedSize
                    val importFontSize = fontGroupInfo.importFontSize
                    val recommendFontSize = fontGroupInfo.recommendFontSize

                    return when (position) {
                        in 0 until recentlyUsedSize -> {
                            getString(R.string.recently_used_font)
                        }

                        in recentlyUsedSize until recentlyUsedSize + importFontSize -> {
                            getString(R.string.font_title_import_font)
                        }

                        in recentlyUsedSize + importFontSize until recentlyUsedSize + importFontSize + recommendFontSize -> {
                            getString(R.string.recommend_font)
                        }

                        else -> {
                            if (position in 0 until fontTypeGroupList.size) {
                                fontTypeGroupList[position].groupName[0].substring(0, 1)
                            } else {
                                ""
                            }
                        }
                    }
                }
            })
        }
    }


    fun showAsBubble(
        view: View,
        useViewHeight: Boolean,
        orientation: BubbleLayout.BubbleOrientation,
    ) {
        val context = view.context
        val viewLocation = IntArray(2)
        view.getLocationOnScreen(viewLocation)
        var bubbleLegWidth = context.resources.getDimension(R.dimen.dp_15)
        var bubbleLegHeight = if (useViewHeight) view.height.toFloat() else view.width.toFloat()
        val shadowRadius = context.resources.getDimension(R.dimen.dp_10)
        val definition = context.resources.getDimension(R.dimen.dp_1).toInt()
        var offset = viewLocation[1].toFloat()
        when (orientation) {
            BubbleLayout.BubbleOrientation.TOP, BubbleLayout.BubbleOrientation.BOTTOM -> {
                val temp = bubbleLegWidth
                bubbleLegWidth = bubbleLegHeight
                bubbleLegHeight = temp
                offset = viewLocation[0].toFloat()
            }

            else -> {}
        }
        val bubbleLayout =
            BubbleLayout(
                view.context,
                offset,
                bubbleLegWidth,
                bubbleLegHeight,
                orientation,
                context.resources.getDimension(R.dimen.dp_30),
                Color.WHITE,
                shadowRadius
            )
        bubbleLayout.addView(binding.root)
        view.getLocationInWindow(viewLocation)
        contentView = bubbleLayout
        when (orientation) {
            BubbleLayout.BubbleOrientation.LEFT -> {
                showAtLocation(
                    view,
                    Gravity.NO_GRAVITY,
                    viewLocation[0] + view.width,
                    viewLocation[1] - height * 3 / 5
                )
            }

            BubbleLayout.BubbleOrientation.RIGHT -> {
                showAtLocation(
                    view,
                    Gravity.NO_GRAVITY,
                    viewLocation[0] - width + definition,
                    viewLocation[1] - height * 3 / 5
                )
            }

            BubbleLayout.BubbleOrientation.TOP -> {
                showAtLocation(
                    view,
                    Gravity.NO_GRAVITY,
                    viewLocation[0] - width * 2 / 5,
                    viewLocation[1] + (if (useViewHeight) 0 else view.height)
                )
            }

            BubbleLayout.BubbleOrientation.BOTTOM -> {
                showAtLocation(
                    view,
                    Gravity.NO_GRAVITY,
                    viewLocation[0] - width * 2 / 5,
                    viewLocation[1] - (if (useViewHeight) view.width else 0) - height + definition
                )
            }
        }

    }

}

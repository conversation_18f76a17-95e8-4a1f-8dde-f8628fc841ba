package com.topstack.kilonotes.pad.guide;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.topstack.kilonotes.R;
import com.topstack.kilonotes.base.component.activity.BaseActivity;
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener;
import com.topstack.kilonotes.base.config.Preferences;
import com.topstack.kilonotes.base.guide.ViewPager2SlowScrollHelper;
import com.topstack.kilonotes.base.track.event.GuideEvent;
import com.topstack.kilonotes.databinding.PadGuideActivityBinding;
import com.topstack.kilonotes.language.LanguageManager;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;

public class PadGuideActivity extends BaseActivity {
    private ViewPager2.OnPageChangeCallback pageChangeCallback;
    private PadGuideActivityBinding binding;
    private int mSkippedPage = -1;
    private PadFirstPenGuidePageFragment mFirstPenGuidePageFragment;
    private PadSecondGuidePageFragment mPadSecondGuidePageFragment;
    private PadThirdGuidePageFragment mPadThirdGuidePageFragment;
    private List<Fragment> guidePagesList;
    private ValueAnimator sliceAnimation;
    private Handler handler;

    private GuideViewModel mViewModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        GuideEvent.INSTANCE.sendLeadPageShow();
        super.onCreate(savedInstanceState);
        mViewModel = new ViewModelProvider(getViewModelStore(), getDefaultViewModelProviderFactory()).get(GuideViewModel.class);

        binding = PadGuideActivityBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        initData(savedInstanceState != null);
        initListener();

        binding.pager.setCurrentItem(mViewModel.getCurrentPosition(), false);
        binding.guideDot.setCurrentPage(mViewModel.getCurrentPosition());
    }

    private void initData(boolean isRestore) {
        guidePagesList = new ArrayList<>();

        mFirstPenGuidePageFragment = new PadFirstPenGuidePageFragment();
        mPadSecondGuidePageFragment = new PadSecondGuidePageFragment();
        mPadThirdGuidePageFragment = new PadThirdGuidePageFragment();

        guidePagesList.add(mFirstPenGuidePageFragment);
        guidePagesList.add(mPadSecondGuidePageFragment);
        guidePagesList.add(mPadThirdGuidePageFragment);
        handler = new Handler();

        FragmentStateAdapter mPagerAdapter = new FragmentStateAdapter(this) {
            @NonNull
            @Override
            public Fragment createFragment(int position) {
                return guidePagesList.get(position);
            }

            @Override
            public int getItemCount() {
                return guidePagesList.size();
            }
        };

        binding.pager.setAdapter(mPagerAdapter);
        binding.pager.setOffscreenPageLimit(2);
        binding.pager.setSaveEnabled(false);

        binding.guideDot.initData(guidePagesList.size(), 0);
    }

    private void initListener() {
        sliceAnimation = ObjectAnimator.ofFloat(0f, 1f);
        pageChangeCallback = new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels);
                if (position == 0) {
                    mFirstPenGuidePageFragment.sliceAnimation(sliceAnimation, positionOffset, positionOffsetPixels);
                    mPadSecondGuidePageFragment.sliceBackAnimation(sliceAnimation, positionOffset);
                } else if (position == 1) {
                    mPadSecondGuidePageFragment.sliceAnimation(sliceAnimation, positionOffset, positionOffsetPixels);
                    mPadThirdGuidePageFragment.sliceBackAnimation(sliceAnimation, positionOffset);
                }
                sliceAnimation.start();
            }

            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                int currentPosition = mViewModel.getCurrentPosition();
                if (position != guidePagesList.size() - 1) {
                    //末页动画完成后，禁止往后滑动再次触发动画
                    mViewModel.setAnimationExecuted(false);
                }
                if (currentPosition != guidePagesList.size() - 1) {
                    mPadThirdGuidePageFragment.setStartState();
                }

                sliceAnimation.removeAllUpdateListeners();
                binding.guideDot.setCurrentPage(position);
                if (position != guidePagesList.size() - 1) {
                    binding.guideSkip.setVisibility(View.VISIBLE);
                    binding.guideDot.setVisibility(View.VISIBLE);
                } else {
                    binding.guideSkip.setVisibility(View.INVISIBLE);
                    binding.guideDot.setVisibility(View.INVISIBLE);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                super.onPageScrollStateChanged(state);
                if (state == ViewPager2.SCROLL_STATE_IDLE) {
                    int currentPosition = binding.pager.getCurrentItem();
                    mViewModel.setCurrentPosition(currentPosition);
                }
            }
        };
        binding.pager.registerOnPageChangeCallback(pageChangeCallback);
        binding.guideSkip.setOnClickListener(new AntiShakeClickListener(view -> {
            mSkippedPage = binding.pager.getCurrentItem();
            mViewModel.setCurrentPosition(guidePagesList.size() - 1);
            mPadThirdGuidePageFragment.setStartState();
            binding.pager.setCurrentItem(guidePagesList.size() - 1, false);
            return Unit.INSTANCE;
        }));
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mSkippedPage >= 0 && Preferences.getAgreeUserPrivacy()) {
            GuideEvent.INSTANCE.sendGuideSkipEvent(mSkippedPage);
        }
        binding.pager.unregisterOnPageChangeCallback(pageChangeCallback);
        sliceAnimation.cancel();
        handler.removeCallbacks(runnable);
    }

    @Override
    protected void onPause() {
        super.onPause();
        handler.removeCallbacks(runnable);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mViewModel.getNeedAutoScroll()) {
            int period = 5000;
            handler.postDelayed(runnable, period);
        }
        binding.pager.registerOnPageChangeCallback(pageChangeCallback);
    }

    @Override
    public void onBackPressed() {
        if (binding.pager.getCurrentItem() == 0) {
            super.onBackPressed();
        } else {
            binding.pager.setCurrentItem(binding.pager.getCurrentItem() - 1);
        }
    }

    @Override
    public String getTrackPageName() {
        return getResources().getString(R.string.page_guide);
    }

    Runnable runnable = new Runnable() {
        @Override
        public void run() {
            long sliceShowDuration = 800;
            new ViewPager2SlowScrollHelper(binding.pager, sliceShowDuration).setCurrentItem(mViewModel.getCurrentPosition() + 1);
            int delay = 4000;
            handler.postDelayed(this, delay);
        }
    };

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            handler.removeCallbacks(runnable);
            mViewModel.setNeedAutoScroll(false);
        }
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public Context refreshConfigurationChanged(Context sourceContext) {
        Configuration refreshConfig = sourceContext.getResources().getConfiguration();
        refreshConfig.smallestScreenWidthDp = Math.min(refreshConfig.screenWidthDp, refreshConfig.screenHeightDp);
        refreshConfig.fontScale = 1F;
        LanguageManager.INSTANCE.updateConfiguration(refreshConfig);
        return sourceContext.createConfigurationContext(refreshConfig);
    }
}

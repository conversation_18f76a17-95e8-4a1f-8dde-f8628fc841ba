package com.topstack.kilonotes.pad.component.dialog

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.hiddenspace.BasePasswordKeyboardDialog
import com.topstack.kilonotes.base.util.DimensionUtil

class PasswordKeyboardDialog : BasePasswordKeyboardDialog() {

    override fun createView(inflater: LayoutInflater, container: ViewGroup?): View {
        return if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
            inflater.inflate(
                R.layout.dialog_hidden_space_password_keyboard_one_third_vertical,
                container
            )
        } else {
            inflater.inflate(R.layout.dialog_hidden_space_password_keyboard, container)
        }
    }

    override fun getAnimationAmplitude(): Int {
        return resources.getDimensionPixelSize(R.dimen.dp_20)
    }

    override fun getDialogWidth(): Int {
        return if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
            resources.getDimensionPixelSize(R.dimen.dp_360)
        } else {
            resources.getDimensionPixelSize(R.dimen.dp_442)
        }
    }

}
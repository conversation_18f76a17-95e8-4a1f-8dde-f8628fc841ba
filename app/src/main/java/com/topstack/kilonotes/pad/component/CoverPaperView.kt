package com.topstack.kilonotes.pad.component

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.PorterDuff
import android.graphics.Rect
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.GestureDetectorCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.doc.io.ResourceManager
import com.topstack.kilonotes.base.doodle.model.Paper
import com.topstack.kilonotes.base.ktx.disableClipChildrenOnParents
import com.topstack.kilonotes.base.netcover.model.NoteCover
import com.topstack.kilonotes.base.note.model.NotebookCoverType
import com.topstack.kilonotes.base.util.DEFAULT_COVER
import com.topstack.kilonotes.databinding.CoverPaperItemBinding
import com.topstack.kilonotes.databinding.CoverPaperItemVerticalBinding
import com.topstack.kilonotes.pad.component.CoverPaperAdapter.Companion.COVER_POSITION
import com.topstack.kilonotes.pad.component.CoverPaperAdapter.Companion.PAPER_POSITION
import kotlin.math.abs

class CoverPaperView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private lateinit var viewPager2: ViewPager2

    private var coverPaperAdapter: CoverPaperAdapter? = null

    private lateinit var recyclerView: RecyclerView

    private val gestureDetector = GestureDetectorCompat(context, SimpleOnGestureListener())

    var selectedCallback: CoverPaperSelectedCallback? = null

    var firstShowCover: Boolean = true

    private var initialCover: NoteCover? = null

    private var initialPaper: Paper? = null

    var horizontalLayoutRatio: Float = 1F

    var horizontalLayout: Boolean = true
        set(value) {
            if (field != value) {
                field = value
                checkSetup()
            }
        }

    var onlyShowCover: Boolean = false
        set(value) {
            if (field != value) {
                field = value
                checkSetup()
            }
        }

    var customCoverUri: String? = null
        set(value) {
            if (field != value) {
                field = value
                coverPaperAdapter?.customCoverUri = value
            }
        }

    var resourceManager: ResourceManager? = null
        set(value) {
            if (field != value) {
                field = value
                coverPaperAdapter?.resourceManager = value
            }
        }

    fun setCover(cover: NoteCover) {
        if (coverPaperAdapter == null) {
            initialCover = cover
            checkSetup()
        } else {
            coverPaperAdapter!!.currentCover = cover
        }
    }

    fun setPaper(paper: Paper) {
        if (coverPaperAdapter == null) {
            initialPaper = paper
            checkSetup()
        } else {
            coverPaperAdapter!!.currentPaper = paper
        }
    }

    private fun checkSetup() {
        val cover = initialCover
        val paper = initialPaper
        if (cover != null && (paper != null || onlyShowCover) && coverPaperAdapter == null) {
            viewPager2 = ViewPager2(context).apply {
                layoutParams = if (horizontalLayout) {
                    LayoutParams(
                        context.resources.getDimensionPixelSize(R.dimen.dp_382),
                        LayoutParams.WRAP_CONTENT
                    ).apply {
                        gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
                    }
                } else {
                    LayoutParams(
                        LayoutParams.WRAP_CONTENT,
                        context.resources.getDimensionPixelSize(R.dimen.dp_275),
                    ).apply {
                        gravity = Gravity.CENTER
                    }
                }
                offscreenPageLimit = 1
                orientation = if (horizontalLayout) {
                    ViewPager2.ORIENTATION_HORIZONTAL
                } else {
                    ViewPager2.ORIENTATION_VERTICAL
                }
                recyclerView = getChildAt(0).apply {
                    overScrollMode = RecyclerView.OVER_SCROLL_NEVER
                } as RecyclerView
                setPageTransformer { page, position ->
                    val r = 1F - abs(position)
                    val scale = 0.77F + r * 0.23F
                    val type = page.findViewById<TextView>(R.id.type)
                    type.isSelected = position == 0F
                    if (horizontalLayout) {
                        val image = page.findViewById<ImageView>(R.id.image)
                        image.scaleX = scale
                        image.scaleY = scale
                        image.translationY = image.height * (1 - scale) / 2
                        val foreground = page.findViewById<ImageView>(R.id.cover_foreground)
                        foreground.scaleX = scale
                        foreground.scaleY = scale
                        foreground.translationX = (1 - scale) * (image.width - foreground.width) / 2
                        foreground.translationY = foreground.height * (1 - scale) / 2
                        type.scaleX = scale
                        type.scaleY = scale
                        type.translationY = -type.height * (1 - scale) / 2
                    } else {
                        page.scaleX = scale
                        page.scaleY = scale
                    }
                }
                registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {

                    override fun onPageSelected(position: Int) {
                        val callback = selectedCallback
                        val adapter = coverPaperAdapter
                        if (callback != null && adapter != null) {
                            when (position) {
                                COVER_POSITION -> callback.onCoverSelected(
                                    adapter.currentCover
                                )
                                PAPER_POSITION -> callback.onPaperSelected(
                                    adapter.currentPaper!!
                                )
                            }
                        }
                    }
                })
                CoverPaperAdapter(
                    cover,
                    if (onlyShowCover) null else paper,
                    customCoverUri,
                    resourceManager,
                    if (horizontalLayout) {
                        GranularRoundedCorners(
                            context.resources.getDimension(R.dimen.dp_8) * horizontalLayoutRatio,
                            context.resources.getDimension(R.dimen.dp_20) * horizontalLayoutRatio,
                            context.resources.getDimension(R.dimen.dp_20) * horizontalLayoutRatio,
                            context.resources.getDimension(R.dimen.dp_8) * horizontalLayoutRatio
                        )
                    } else {
                        GranularRoundedCorners(
                            context.resources.getDimension(R.dimen.dp_8),
                            context.resources.getDimension(R.dimen.dp_20),
                            context.resources.getDimension(R.dimen.dp_20),
                            context.resources.getDimension(R.dimen.dp_8)
                        )
                    },
                    horizontalLayout,
                    horizontalLayoutRatio
                ) {
                    requestTransform()
                }.let {
                    coverPaperAdapter = it
                    adapter = it
                    setCurrentItem(if (firstShowCover) COVER_POSITION else PAPER_POSITION, false)
                }
            }
            addView(viewPager2)
            recyclerView.disableClipChildrenOnParents(this)
        }
    }


    private inner class SimpleOnGestureListener : GestureDetector.SimpleOnGestureListener() {
        private val localVisibleRect = Rect()
        private val locationOnScreen = intArrayOf(0, 0)

        fun getHolderPositionByLocation(x: Float, y: Float): Int {
            if (!this@CoverPaperView::recyclerView.isInitialized) return -1
            for (position in 0 until recyclerView.childCount) {
                val holder = recyclerView.findViewHolderForAdapterPosition(position)
                        as CoverPaperAdapter.PageHolder
                val view = holder.imageView
                view.getLocalVisibleRect(localVisibleRect)
                view.getLocationOnScreen(locationOnScreen)
                if (localVisibleRect.contains(
                        x.toInt() - locationOnScreen[0],
                        y.toInt() - locationOnScreen[1]
                    )
                ) {
                    return position
                }
            }
            return -1
        }

        override fun onDown(e: MotionEvent): Boolean {
            return true
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            if (!this@CoverPaperView::viewPager2.isInitialized) return false
            return when (val position = getHolderPositionByLocation(e.rawX, e.rawY)) {
                COVER_POSITION, PAPER_POSITION -> {
                    viewPager2.currentItem = position
                    true
                }
                else -> {
                    false
                }
            }
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            return true
        }

        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            var currentItem = viewPager2.currentItem
            if (horizontalLayout) {
                if (velocityX > 0) {
                    currentItem--
                } else {
                    currentItem++
                }
            } else {
                if (velocityY > 0) {
                    currentItem--
                } else {
                    currentItem++
                }
            }
            return if (currentItem in 0 until recyclerView.childCount) {
                viewPager2.currentItem = currentItem
                true
            } else {
                false
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event == null) return super.onTouchEvent(event)
        return if (gestureDetector.onTouchEvent(event)) {
            true
        } else {
            super.onTouchEvent(event)
        }
    }
}

class CoverPaperAdapter(
    cover: NoteCover,
    paper: Paper?,
    coverUri: String?,
    resourceManager: ResourceManager?,
    private val granularRoundedCorners: GranularRoundedCorners,
    private val horizontalLayout: Boolean,
    private var ratio: Float,
    private val onChildItemLayoutChangedListener: () -> Unit
) : RecyclerView.Adapter<CoverPaperAdapter.PageHolder>() {

    companion object {
        const val COVER_POSITION = 0
        const val PAPER_POSITION = 1
    }

    var currentCover: NoteCover = cover
        set(value) {
            if (field != value) {
                field = value
                notifyItemChanged(COVER_POSITION)
            }
        }

    var currentPaper: Paper? = paper
        set(value) {
            if (field != value) {
                field = value
                notifyItemChanged(PAPER_POSITION)
            }
        }

    var customCoverUri: String? = coverUri
        set(value) {
            if (field != value) {
                field = value
                notifyItemChanged(COVER_POSITION)
            }
        }

    var resourceManager: ResourceManager? = resourceManager
        set(value) {
            if (field != value) {
                field = value
                notifyItemChanged(COVER_POSITION)
            }
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PageHolder {
        if (horizontalLayout) {
            val binding =
                CoverPaperItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            val layoutParams = binding.image.layoutParams
            layoutParams.width = (layoutParams.width * ratio).toInt()
            binding.image.layoutParams = layoutParams
            binding.image.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                onChildItemLayoutChangedListener()
            }
            binding.type.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                onChildItemLayoutChangedListener()
            }
            return PageHolder(
                binding.root,
                binding.image,
                binding.coverForeground,
                binding.type,
                granularRoundedCorners
            )
        } else {
            val binding =
                CoverPaperItemVerticalBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            binding.image.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                onChildItemLayoutChangedListener()
            }
            binding.type.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                onChildItemLayoutChangedListener()
            }
            return PageHolder(
                binding.root,
                binding.image,
                binding.coverForeground,
                binding.type,
                granularRoundedCorners
            )
        }
    }

    override fun onBindViewHolder(holder: PageHolder, position: Int) {
        when (position) {
            COVER_POSITION -> {
                if (currentCover.categoryId == NotebookCoverType.CUSTOM.categoryId) {
                    val uri = customCoverUri ?: return
                    if (ResourceManager.isKiloPath(uri)) {
                        val resource = resourceManager ?: return
                        holder.bindCover(uri, resource)
                    } else {
                        holder.bindCover(uri)
                    }
                } else {
                    holder.bindCover(currentCover)
                }
            }
            PAPER_POSITION -> holder.bindPaper(currentPaper!!)
        }
    }

    override fun getItemCount(): Int = if (currentPaper == null) 1 else 2

    class PageHolder(
        itemView: View,
        val imageView: ImageView,
        private val foreground: ImageView,
        private val textView: TextView,
        private val granularRoundedCorners: GranularRoundedCorners
    ) : RecyclerView.ViewHolder(itemView) {

        fun bindCover(uri: String, resourceManager: ResourceManager) {
            imageView.apply {
                Glide.with(context)
                    .load(resourceManager.openFile(uri))
                    .apply(
                        RequestOptions.bitmapTransform(
                            granularRoundedCorners
                        )
                    )
                    .placeholder(DEFAULT_COVER)
                    .into(this)
            }
            bindCoverTextAndForeground()
        }

        fun bindCover(uri: String) {
            imageView.apply {
                Glide.with(context)
                    .load(uri)
                    .apply(
                        RequestOptions.bitmapTransform(
                            granularRoundedCorners
                        )
                    )
                    .placeholder(DEFAULT_COVER)
                    .into(this)
            }
            bindCoverTextAndForeground()
        }

        fun bindCover(cover: NoteCover) {
            imageView.apply {
                Glide.with(context)
                    .load(if (cover.isBuiltin) cover.drawableId else cover.imgUrl)
                    .apply(
                        RequestOptions.bitmapTransform(
                            granularRoundedCorners
                        )
                    )
                    .placeholder(DEFAULT_COVER)
                    .into(this)
            }
            bindCoverTextAndForeground()
        }

        private fun bindCoverTextAndForeground() {
            foreground.visibility = View.VISIBLE
            textView.apply {
                text = context.getString(R.string.note_book_cover)
            }
        }

        @SuppressLint("SetTextI18n")
        fun bindPaper(paper: Paper) {
            imageView.apply {
                setBackgroundColor(paper.getBuiltinBackgroundColor())
                imageTintList = ColorStateList.valueOf(paper.getBuiltinForegroundColor())
                imageTintMode = PorterDuff.Mode.SRC_IN
                Glide.with(context)
                    .load(paper.getBuiltinPaperThumbnailFile())
                    .into(this)
            }
            foreground.visibility = View.INVISIBLE
            textView.apply {
                text = context.getString(R.string.notebook_paper_prefix) +
                        context.getString(paper.getBuiltinPaperStyleTypeNameRes())
            }
        }
    }
}

interface CoverPaperSelectedCallback {

    fun onCoverSelected(cover: NoteCover)

    fun onPaperSelected(paper: Paper)

}
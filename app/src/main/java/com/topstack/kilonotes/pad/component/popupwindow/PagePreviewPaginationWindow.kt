package com.topstack.kilonotes.pad.component.popupwindow

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import com.topstack.kilonotes.R
import com.topstack.kilonotes.databinding.PagePreviewPaginationWindowBinding

class PagePreviewPaginationWindow(val context: Context) : PopupWindow() {

    private val binding by lazy {
        PagePreviewPaginationWindowBinding.inflate(LayoutInflater.from(context))
    }

    fun show(view: View, offsetX: Float, viewWidth: Float, pagination: String) {
        val viewLocation = IntArray(2)
        view.getLocationOnScreen(viewLocation)
        val offset = viewLocation[0] + offsetX
        binding.root.run {
            setBubbleLegOffset(offset + viewWidth / 2)
        }
        binding.pagination.text = pagination
        contentView = binding.root
        binding.root.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        binding.root.layout(0, 0, binding.root.measuredWidth, binding.root.measuredHeight)
        height = binding.root.measuredHeight
        width = binding.root.measuredWidth
        showAsDropDown(
            view,
            (offsetX - (width - viewWidth) / 2).toInt(),
            context.resources.getDimensionPixelOffset(R.dimen.dp_17),
            Gravity.TOP or Gravity.START
        )
    }

    fun updateLocation(view: View, offsetX: Float, viewWidth: Float, pagination: String) {
        val viewLocation = IntArray(2)
        view.getLocationOnScreen(viewLocation)
        val offset = viewLocation[0] + offsetX
        binding.root.run {
            setBubbleLegOffset(offset + viewWidth / 2)
        }
        binding.pagination.text = pagination
        contentView = binding.root
        binding.root.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        binding.root.layout(0, 0, binding.root.measuredWidth, binding.root.measuredHeight)
        height = binding.root.measuredHeight
        width = binding.root.measuredWidth
        update(
            view,
            (offsetX - (width - viewWidth) / 2).toInt(),
            context.resources.getDimensionPixelOffset(R.dimen.dp_17),
            -1,
            -1
        )
    }
}
package com.topstack.kilonotes.pad.component.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.BaseHomeDialog
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.ktx.getStatusBarHeight
import com.topstack.kilonotes.base.ktx.insideArea
import com.topstack.kilonotes.base.util.DimensionUtil

class CreateFolderGuideDialog : BaseHomeDialog() {

    companion object {
        const val TAG = "CreateFolderGuideDialog"
        const val StepOne = 1
        const val StepTwo = 2
        const val StepThree = 3
        var currentStep = StepOne
    }

    private lateinit var guideImg: ImageView
    private lateinit var guideText: TextView
    private lateinit var guideBtn: TextView
    private lateinit var guideBtnBg: ImageView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return if (isLandOneThirdScreen()) {
            inflater.inflate(
                R.layout.dialog_create_folder_guide_one_three_horizontal,
                container,
                false
            )
        } else if (isPortraitOneThirdScreen()) {
            inflater.inflate(
                R.layout.dialog_create_folder_guide_one_three_vertical,
                container,
                false
            )
        } else {
            inflater.inflate(R.layout.dialog_create_folder_guide, container, false)
        }.apply {
            measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        guideBtn = view.findViewById(R.id.guide_btn)
        guideText = view.findViewById(R.id.guide_text)
        guideImg = view.findViewById(R.id.guide_img)
        guideBtnBg = view.findViewById(R.id.guide_btn_bg)
        switchViewContentByCurrentStep()
        requireView().setOnTouchListener { _, event ->
            if (!guideImg.insideArea(event) && !guideText.insideArea(event)) {
                currentStep++
                switchViewContentByCurrentStep()
                true
            }
            false
        }
        adjustRtlLayout()
        Preferences.needShowCreateFolderDialog = false
    }

    private fun adjustRtlLayout() {
        guideBtnBg.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
    }

    private fun switchViewContentByCurrentStep() {
        val guideImgId: Int
        val guideNote: String
        val guideBtnText: String
        when (currentStep) {
            StepOne -> {
                guideImgId = if (isLandOneThirdScreen()) {
                    R.drawable.create_folder_guide_img_one_three_step_one
                } else {
                    R.drawable.create_folder_guide_img_step_one
                }
                guideNote = getString(R.string.folder_guide_note_1)
                guideBtnText = getString(R.string.next_step)
            }

            StepTwo -> {
                guideImgId = if (isLandOneThirdScreen()) {
                    R.drawable.create_folder_guide_img_one_three_step_two
                } else {
                    R.drawable.create_folder_guide_img_step_two
                }
                guideNote = getString(R.string.folder_guide_note_2)
                guideBtnText = getString(R.string.next_step)
            }

            StepThree -> {
                guideImgId = if (isLandOneThirdScreen()) {
                    R.drawable.create_folder_guide_img_one_three_step_three
                } else {
                    R.drawable.create_folder_guide_img_step_three
                }
                guideNote = getString(R.string.folder_guide_note_3)
                guideBtnText = getString(R.string.finish_text_edit)
            }

            else -> {
                dismiss()
                return
            }
        }
        Glide.with(this)
            .load(guideImgId)
            .override(guideImg.width, guideImg.height)
            .into(guideImg)
        guideText.text = guideNote
        guideBtn.text = guideBtnText
    }

    private fun isLandOneThirdScreen(): Boolean {
        return DimensionUtil.isLandAndOneThirdScreen(requireContext()) ||
                DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext())
    }

    private fun isPortraitOneThirdScreen(): Boolean {
        return DimensionUtil.isPortraitAndOneThirdScreen(context)
    }

    override fun onStart() {
        super.onStart()
        dialog?.apply {
            setOnKeyListener { _, keyCode, _ -> keyCode == KeyEvent.KEYCODE_BACK }
            setCanceledOnTouchOutside(false)
        }
        dialog?.window?.apply {
            val params = attributes
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.MATCH_PARENT
            params.y = getStatusBarHeight()
            attributes = params
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setGravity(Gravity.CENTER)
        }
    }

    override fun dismiss() {
        Preferences.needShowCreateFolderDialog = false
        super.dismiss()
    }

}
package com.topstack.kilonotes.pad.select

import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.BubbleLayout
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.DialogSnippetOptionBinding

class SnippetOptionWindow(
    context: Context,
) : PopupWindow() {

    var optionCallback: ((SnippetOption) -> Unit)? = null
    private val viewLocationByWindow = IntArray(2)

    private val binding: DialogSnippetOptionBinding by lazy {
        DialogSnippetOptionBinding.inflate(LayoutInflater.from(context))
    }


    init {
        height = context.resources.getDimensionPixelSize(R.dimen.dp_240)
        width = context.resources.getDimensionPixelSize(R.dimen.dp_314)
        isFocusable = true
        isOutsideTouchable = true
        binding.snippetTitleEdit.setOnClickListener {
            optionCallback?.invoke(SnippetOption.EDIT_TITLE)
        }
        binding.snippetLabelAdd.setOnClickListener {
            optionCallback?.invoke(SnippetOption.ADD_LABEL)
        }
        binding.snippetDelete.setOnClickListener {
            optionCallback?.invoke(SnippetOption.DELETE)
        }
    }

    fun showAsBubble(anchorView: View, offsetY: Float, orientation: BubbleLayout.BubbleOrientation) {
        val context = anchorView.context
        val viewLocationByScreen = IntArray(2)
        anchorView.getLocationOnScreen(viewLocationByScreen)
        anchorView.getLocationInWindow(viewLocationByWindow)
        val orientation = if (isBeyondTheEndSideOfTheScreen(context, anchorView)) {
            BubbleLayout.BubbleOrientation.RIGHT
        } else {
            BubbleLayout.BubbleOrientation.LEFT
        }
        val bubbleLegWidth = context.resources.getDimension(R.dimen.dp_15)
        val bubbleLegHeight = context.resources.getDimension(R.dimen.dp_30)
        val shadowRadius = context.resources.getDimension(R.dimen.dp_10)
        val bubbleLegOffset =
            viewLocationByScreen[1] + anchorView.height.toFloat() / 2 - bubbleLegHeight / 2 + shadowRadius - offsetY.toInt()
        val bubbleLayout =
            BubbleLayout(
                anchorView.context,
                bubbleLegOffset,
                bubbleLegWidth,
                bubbleLegHeight,
                orientation,
                context.resources.getDimension(R.dimen.dp_30),
                Color.WHITE,
                shadowRadius,
                BubbleLayout.BubbleType.TRIANGLE
            )
        bubbleLayout.addView(binding.root)
        contentView = bubbleLayout
        when (orientation) {
            BubbleLayout.BubbleOrientation.LEFT -> {
                showAtLocation(
                    anchorView,
                    Gravity.NO_GRAVITY,
                    viewLocationByWindow[0] + anchorView.width,
                    viewLocationByWindow[1] + (anchorView.height - height) / 2 - offsetY.toInt()
                )
            }

            BubbleLayout.BubbleOrientation.RIGHT -> {
                showAtLocation(
                    anchorView,
                    Gravity.NO_GRAVITY,
                    viewLocationByWindow[0] - width,
                    viewLocationByWindow[1] + (anchorView.height - height) / 2 - offsetY.toInt()
                )
            }

            else -> {}
        }
    }

    private fun isBeyondTheEndSideOfTheScreen(context: Context, anchorView: View): Boolean {
        return (viewLocationByWindow[0] + anchorView.width + width) > DimensionUtil.getScreenDimensions(
            context
        ).widthPixels
    }
}

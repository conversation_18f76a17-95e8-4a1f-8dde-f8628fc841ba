package com.topstack.kilonotes.pad.promotion

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.topstack.kilonotes.infra.util.TimeUtil.ONE_HOUR_TO_MILLISECOND
import com.topstack.kilonotes.pad.promotion.Promotion.Companion.TABLE_NAME
import java.util.UUID

/**
 *
 */
@Keep
@Entity(tableName = TABLE_NAME)
data class Promotion(
    @PrimaryKey
    val promotionId: UUID,
    val name: String,
    val startTime: Long,
    val endTime: Long,
    var overtimeHour: Int,
    var markFinished: Boolean
) {
    companion object {
        // Table name & column names
        const val TABLE_NAME = "promotion"


        // Promotion names
        const val DAILY_CHECK_IN_GAIN_NOTE_LIMITS = "daily_check_in_gain_note_limits"
    }

    fun isFinished(): Boolean =
        markFinished || System.currentTimeMillis() > endTime + (overtimeHour * ONE_HOUR_TO_MILLISECOND)
}

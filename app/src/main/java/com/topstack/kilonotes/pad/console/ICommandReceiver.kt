package com.topstack.kilonotes.pad.console

import android.graphics.Path
import android.graphics.Rect
import android.graphics.RectF
import android.view.MotionEvent
import com.topstack.kilonotes.base.doodle.views.multiselectview.Selection

@JvmDefaultWithoutCompatibility
sealed interface IConsoleCommandReceiver {

    fun interceptTouchEvent(event: MotionEvent, globalRect: Rect): Boolean = false

    fun exitCommandMode() {}

    fun onConsoleStateChanged(inConsoleState: Boolean) {}
}

@JvmDefaultWithoutCompatibility
interface IConsoleInsertableObjectCommandReceiver : IConsoleCommandReceiver {

    fun select(drawPath: Path, pathBounds: RectF): List<Selection> = emptyList()

    fun delete(drawPath: Path, pathBounds: RectF): List<Selection> = emptyList()

    fun remove(drawPath: Path, pathBounds: RectF): List<Selection> = emptyList()

    fun copy(drawPath: Path, pathBounds: RectF): List<Selection> = emptyList()

    fun paste(drawPath: Path, pathBounds: RectF): List<Selection> = emptyList()

    fun selectAll(): List<Selection> = emptyList()

    fun deleteAll(): List<Selection> = emptyList()

    fun undo(step: Int)

    fun redo(step: Int)

}

interface IConsoleSwitchToolCommandReceiver : IConsoleCommandReceiver {

    fun switchToToolPen()

    fun switchToToolHighlighter()

    fun switchToToolImage(drawPath: Path, pathBounds: RectF)

    fun switchToolText(drawPath: Path, pathBounds: RectF)

}
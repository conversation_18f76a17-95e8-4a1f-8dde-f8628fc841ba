package com.topstack.kilonotes.pad.component

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.bumptech.glide.Glide
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.ktx.isLayoutRtl
import com.topstack.kilonotes.databinding.NoteSnippetColorItemBinding
import com.topstack.kilonotes.databinding.NoteSnippetColorSelectViewBinding

class NoteSnippetColorSelectView(
    context: Context,
    attrs: AttributeSet
) : ConstraintLayout(context, attrs) {
    var binding: NoteSnippetColorSelectViewBinding

    private var onItemClickListener: ((color: Int?) -> Unit)? = null

    private var allColorAdapter: NoteSnippetAllColorAdapter? = null

    private var colorAdapter: NoteSnippetColorAdapter? = null

    private var isExtended: Boolean = false
        set(value) {
            field = value
        }

    private var isItemClickable: Boolean = false
        set(value) {
            field = value
            allColorAdapter?.setIsItemClickable(value)
            colorAdapter?.setIsItemClickable(value)
        }


    val decoration = object : ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val position = parent.getChildAdapterPosition(view)
            if (position != 0) {
                if (parent.isLayoutRtl()) {
                    outRect.right = resources.getDimension(R.dimen.dp_15).toInt()
                } else {
                    outRect.left = resources.getDimension(R.dimen.dp_15).toInt()
                }
            }
        }
    }

    init {
        binding =
            NoteSnippetColorSelectViewBinding.inflate(LayoutInflater.from(context), this, true)
    }

    fun setUpRecyclerView(
        colorList: List<Int>,
        initialSelectedColor: Int?,
        isItemClickable: Boolean
    ) {
        allColorAdapter = NoteSnippetAllColorAdapter(context)
        colorAdapter = NoteSnippetColorAdapter(context, colorList)
        allColorAdapter?.setOnClickListener {
            colorAdapter?.currentSelectedColor = null
            onItemClickListener?.invoke(null)
        }
        colorAdapter?.setOnColorClickListener { color ->
            allColorAdapter?.isSelected = false
            onItemClickListener?.invoke(color)
        }
        allColorAdapter?.isSelected = initialSelectedColor == null
        colorAdapter?.currentSelectedColor = initialSelectedColor
        binding.recyclerViewColor.apply {
            adapter = ConcatAdapter(allColorAdapter, colorAdapter)
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            removeItemDecoration(decoration)
            addItemDecoration(decoration)
        }
        this.isItemClickable = isItemClickable
    }

    fun setOnItemClickListener(action: (color: Int?) -> Unit) {
        onItemClickListener = action
    }

    fun setIsExtended(isExtended: Boolean) {
        this.isExtended = isExtended
    }

    fun setIsItemClickable(isItemClickable: Boolean) {
        this.isItemClickable = isItemClickable
    }

    fun setCurrentSelectedColor(color: Int?) {
        allColorAdapter?.isSelected = color == null
        colorAdapter?.currentSelectedColor = color
    }

}


class NoteSnippetColorAdapter(
    val context: Context,
    private val colorList: List<Int>,
    private var onColorClickListener: ((color: Int) -> Unit)? = null
) : RecyclerView.Adapter<NoteSnippetColorAdapter.ColorViewHolder>() {


    companion object {
        const val COLOR_ITEM_OVERRIDE_SIZE = 36
    }


    var currentSelectedColor: Int? = null
        set(value) {
            val oldPosition = colorList.indexOf(field)
            val newPosition = colorList.indexOf(value)
            field = value
            if (oldPosition in colorList.indices) {
                notifyItemChanged(oldPosition)
            }
            if (newPosition in colorList.indices) {
                notifyItemChanged(newPosition)
            }
        }

    private var isItemClickable: Boolean = true
        set(value) {
            if (field != value) {
                field = value
            }
        }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): NoteSnippetColorAdapter.ColorViewHolder {
        return ColorViewHolder(NoteSnippetColorItemBinding.inflate(LayoutInflater.from(context)))
    }

    override fun onBindViewHolder(holder: NoteSnippetColorAdapter.ColorViewHolder, position: Int) {
        Glide.with(context)
            .load(ColorDrawable(colorList[position]))
            .circleCrop()
            .override(COLOR_ITEM_OVERRIDE_SIZE)
            .into(holder.color)
        holder.isSelected.isVisible = currentSelectedColor == colorList[position]
        holder.color.setOnClickListener {
            if (isItemClickable) {
                currentSelectedColor = colorList[position]
                onColorClickListener?.invoke(colorList[position])
            }
        }
    }

    override fun getItemCount(): Int {
        return colorList.size
    }

    fun setOnColorClickListener(action: (color: Int) -> Unit) {
        onColorClickListener = action
    }

    fun setIsItemClickable(isItemClickable: Boolean) {
        this.isItemClickable = isItemClickable
    }


    class ColorViewHolder(binding: NoteSnippetColorItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val color = binding.color
        val isSelected = binding.isSelected
    }

}


class NoteSnippetAllColorAdapter(
    val context: Context,
    private var onClickListener: (() -> Unit)? = null
) : RecyclerView.Adapter<NoteSnippetColorAdapter.ColorViewHolder>() {

    var isSelected = true
        set(value) {
            if (field != value) {
                field = value
                notifyItemChanged(0)
            }
        }

    private var isItemClickable: Boolean = true
        set(value) {
            if (field != value) {
                field = value
            }
        }


    var textViewHolderWidth: Int? = null

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): NoteSnippetColorAdapter.ColorViewHolder {
        return NoteSnippetColorAdapter.ColorViewHolder(
            NoteSnippetColorItemBinding.inflate(
                LayoutInflater.from(context)
            )
        )
    }

    override fun onBindViewHolder(
        holder: NoteSnippetColorAdapter.ColorViewHolder,
        position: Int
    ) {
        Glide.with(context)
            .load(R.drawable.snippet_page_color_all_bg)
            .circleCrop()
            .override(NoteSnippetColorAdapter.COLOR_ITEM_OVERRIDE_SIZE)
            .into(holder.color)
        holder.isSelected.isVisible = isSelected
        holder.color.setOnClickListener {
            if (isItemClickable) {
                isSelected = true
                onClickListener?.invoke()
                notifyItemChanged(position)
            }
        }
    }

    override fun getItemCount(): Int {
        return 1
    }

    fun setOnClickListener(action: () -> Unit) {
        onClickListener = action
    }

    fun setIsItemClickable(isItemClickable: Boolean) {
        this.isItemClickable = isItemClickable
    }
}
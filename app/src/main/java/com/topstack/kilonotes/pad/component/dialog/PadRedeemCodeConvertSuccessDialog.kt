package com.topstack.kilonotes.pad.component.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.topstack.kilonotes.base.component.dialog.BaseRedeemCodeConvertSuccessDialog
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadRedeemCodeConvertSuccessDialogBinding
import com.topstack.kilonotes.databinding.PadRedeemCodeConvertSuccessDialogOneThirdScreenBinding

class PadRedeemCodeConvertSuccessDialog : BaseRedeemCodeConvertSuccessDialog() {
    companion object {
        const val TAG = "PadRedeemCodeConvertSuccessDialog"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return if (DimensionUtil.isPortraitAndOneThirdScreen(requireContext())) {
            PadRedeemCodeConvertSuccessDialogOneThirdScreenBinding.inflate(
                inflater,
                container,
                false
            ).root
        } else {
            PadRedeemCodeConvertSuccessDialogBinding.inflate(inflater, container, false).root
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            if (DimensionUtil.isPortraitAndOneThirdScreen(requireContext())) ViewGroup.LayoutParams.MATCH_PARENT else ViewGroup.LayoutParams.WRAP_CONTENT
        )

    }
}
package com.topstack.kilonotes.pad.console.view

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.track.event.ConsoleEvent
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.infra.util.MathUtils
import com.topstack.kilonotes.mlkit.recognition.digitalink.DigitalInkRecognitionManager
import com.topstack.kilonotes.pad.console.ConsoleCommandManager
import com.topstack.kilonotes.pad.console.ConsoleCommandType
import com.topstack.kilonotes.pad.console.DirectCommand
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.acos

class ConsoleUndoRedoView : View{
    constructor(context: Context): this(context, null)
    constructor(context: Context, attrs: AttributeSet?): this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int): super(context, attrs,defStyleAttr) {
        initView()
    }

    companion object {
        private const val TAG = "ConsoleUndoRedoView"
        private const val SCALE_SIZE_ORIGIN = 1.0F
        private const val SCALE_SIZE_MAX = 1.7F
        private const val SCALE_SIZE_PART_TWO_END = 1.43F
        private const val ANIMATION_DURATION_100L = 100L
        private const val ANIMATION_DURATION_200L = 200L
        private const val PROGRESS_PER_UNIT = 30F * 10F / 11F
        private const val MAX_ANGLE_MOVED = PROGRESS_PER_UNIT * 1F / 2F
        private const val EXTRA_ANGLE_MOVED = 13.5F

        private const val ADSORB_THRESHOLD = 0.8F
        private const val ONE_SECOND = 1000L

        /**
         * 表示位于第几象限或位于坐标轴上
         */
        private const val COORDINATE_AXIS = 0
        private const val FIRST_QUADRANT = 1
        private const val SECOND_QUADRANT = 2
        private const val THIRD_QUADRANT = 3
        private const val FOURTH_QUADRANT = 4

        /**
         * DIRECTION_CLOCKWISE 顺时针 重做
         * DIRECTION_ANTICLOCKWISE 逆时针 撤销
         */
        private const val DIRECTION_UNKNOWN = 0
        private const val DIRECTION_CLOCKWISE = 1
        private const val DIRECTION_ANTICLOCKWISE = 2
    }

    private var currentDirection = DIRECTION_UNKNOWN

    private val colorTriangle = resources.getColor(R.color.console_undo_redo_triangle_color, null)
    private val colorShadow = resources.getColor(R.color.black_37, null)
    private val colorProgress = resources.getColor(R.color.progress_bar, null)
    private val colorInnerCircle = resources.getColor(R.color.console_note_btn, null)
    private val colorMidCircle = resources.getColor(R.color.console_note_btn, null)
    private val colorInnerBorder = resources.getColor(R.color.white_35, null)
    private val colorOuterBorder = resources.getColor(R.color.black_15, null)
    private val colorInnerCircleNormal = resources.getColor(R.color.console_note_btn, null)
    private val colorInnerCirclePressed = resources.getColor(R.color.black_30, null)
    private val colorInnerCircleBorder = resources.getColor(R.color.white_25, null)

    private val shadowWidth = resources.getDimension(R.dimen.dp_5)
    private val triangleStrokeWidth = resources.getDimension(R.dimen.dp_1)
    private val shadowStrokeWidth = resources.getDimension(R.dimen.dp_1)
    private val outerCircleStrokeWidth = resources.getDimension(R.dimen.dp_3)
    private val progressStrokeWidth = resources.getDimension(R.dimen.dp_30)
    private val radiusInnerCircleBorder: Float = resources.getDimension(R.dimen.dp_39)
    private val radiusInnerCircle: Float = resources.getDimension(R.dimen.dp_36)
    private val radiusInnerCircleSpot: Float = resources.getDimension(R.dimen.dp_1)
    private val radiusInnerCircleSpotTrack: Float = resources.getDimension(R.dimen.dp_30)
    private val radiusMidBackgroundCircleInitial: Float = resources.getDimension(R.dimen.dp_61)
    private var radiusMidBackgroundCircle: Float = radiusMidBackgroundCircleInitial
    private val radiusOuterBorderCircleInitial: Float = resources.getDimension(R.dimen.dp_63)
    private var radiusOuterBorderCircle: Float = radiusOuterBorderCircleInitial
    private val maxValidTouchDistance = radiusOuterBorderCircleInitial + outerCircleStrokeWidth

    private val radiusWhiteSpotCircle: Float = resources.getDimension(R.dimen.dp_64)
    private val undoRedoRectWidth = resources.getDimension(R.dimen.dp_26)
    private val undoRedoRectHeight = resources.getDimension(R.dimen.dp_18)
    private val whiteSpotRectWidth = resources.getDimension(R.dimen.dp_128)

    lateinit var paint : Paint
    lateinit var shadowClipPaint : Paint
    private val shadowRadius = resources.getDimension(R.dimen.dp_18)
    private val shadowYOffset = resources.getDimension(R.dimen.dp_2)
    private val shadowBlur = BlurMaskFilter(shadowRadius, BlurMaskFilter.Blur.NORMAL)
    private var lastAngle : Float = 0F
    private var currentAngle : Float = 0F

    private var centerX = resources.getDimension(R.dimen.dp_111)
    private var centerY = resources.getDimension(R.dimen.dp_111)
    private var currentActionDownX : Float = 0F
    private var currentActionDownY : Float = resources.getDimension(R.dimen.dp_100)
    private var currentActionMoveX : Float = 0F
    private var currentActionMoveY : Float = resources.getDimension(R.dimen.dp_100)

    private var lastTouchX : Float = 0F
    private var lastTouchY : Float = resources.getDimension(R.dimen.dp_100)

    private var triangleHeight : Float = resources.getDimension(R.dimen.dp_10)
    private var triangleBottom : Float = resources.getDimension(R.dimen.dp_6)
    private val pathTriangle : Path = Path()
    private val pathTriangleBorder : Path = Path()
    private val trianglePathMatrix : Matrix = Matrix()

    private val linearGradient = LinearGradient(
        centerX - radiusInnerCircle,
        centerY,
        centerX - triangleHeight - radiusInnerCircle,
        centerY,
        context.getColor(R.color.white_22),
        context.getColor(R.color.white_79),
        Shader.TileMode.CLAMP
    )

    private val canvasMatrix : Matrix = Matrix()
    private val ovalProgress : RectF = RectF()

    private var currentAnimatorSize : Float = SCALE_SIZE_ORIGIN

    private var lastUndoTime: Long = 0L
    private var lastRedoTime: Long = 0L
    private var stepsUndo = 0
    private var stepsRedo = 0
    private var canUndo = false
    private var canRedo = false
    private var maxUndo = 0
    private var maxRedo = 0
    private var maxUndoNewest = 0
    private var maxRedoNewest = 0
    private var isMaxUndoChangedDuringPressed = false
    private var isMaxRedoChangedDuringPressed = false
    private var onNoUndoAction: (() -> Unit)? = null
    private var onNoRedoAction: (() -> Unit)? = null

    private var maxRedoProgress = -1
    private var maxUndoProgress = 1

    private var isFirstCheckCanRedo = false
    private var isFirstCheckCanUndo = false

    private val scope = CoroutineScope(Dispatchers.IO)
    private lateinit var job: Job
    fun setCanUndo(canUndo: Boolean) {
        this.canUndo = canUndo
        invalidate()
    }
    fun setCanRedo(canRedo: Boolean) {
        this.canRedo = canRedo
        invalidate()
    }

    fun setMaxUndo(maxUndo: Int) {
        if (!isPressed) {
            this.maxUndo = maxUndo
        } else {
            isMaxUndoChangedDuringPressed = true
            this.maxUndoNewest = maxUndo
        }
    }
    fun setMaxRedo(maxRedo: Int) {
        if (!isPressed) {
            this.maxRedo = maxRedo
        } else {
            isMaxRedoChangedDuringPressed = true
            this.maxRedoNewest = maxRedo
        }
    }

    fun setOnNoUndoAction (noUndoAction: (() -> Unit)?) {
        onNoUndoAction = noUndoAction
    }
    fun setOnNoRedoAction (noRedoAction: (() -> Unit)?) {
        onNoRedoAction = noRedoAction
    }

    private val rectUndo = Rect()
    private val rectRedo = Rect()
    private val rectWhiteSpot = Rect()
    private var bitmapUndo : Bitmap = BitmapFactory.decodeResource(resources, R.drawable.console_undo_disabled)
    private var bitmapRedo : Bitmap = BitmapFactory.decodeResource(resources, R.drawable.console_redo_disabled)
    private var bitmapUndoEnabled : Bitmap = BitmapFactory.decodeResource(resources, R.drawable.console_undo_enabled)
    private var bitmapRedoEnabled : Bitmap = BitmapFactory.decodeResource(resources, R.drawable.console_redo_enabled)
    private var bitmapWhiteSpot : Bitmap = BitmapFactory.decodeResource(resources, R.drawable.console_white_spot)

    private var isPressed = false

    private fun initView() {
        paint = Paint()
        paint.isAntiAlias = true
        paint.color = colorInnerCircle
        paint.style = Paint.Style.FILL

        shadowClipPaint = Paint().apply {
            isAntiAlias = true
            style = Paint.Style.FILL
            isDither = true
            xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
        }

        setLayerType(LAYER_TYPE_SOFTWARE, null)
    }

    private val animatorScalePartOne = ValueAnimator.ofFloat(SCALE_SIZE_ORIGIN, SCALE_SIZE_MAX).apply {
        addUpdateListener { value ->
            currentAnimatorSize = value.animatedValue as Float
            radiusMidBackgroundCircle = radiusMidBackgroundCircleInitial * (value.animatedValue as Float)
            radiusOuterBorderCircle = radiusOuterBorderCircleInitial * (value.animatedValue as Float)
            invalidate()
        }
        duration = ANIMATION_DURATION_100L
    }

    private val animatorScalePartTwo = ValueAnimator.ofFloat(SCALE_SIZE_MAX, SCALE_SIZE_PART_TWO_END).apply {
        addUpdateListener { value ->
            currentAnimatorSize = value.animatedValue as Float
            radiusMidBackgroundCircle = radiusMidBackgroundCircleInitial * (value.animatedValue as Float)
            radiusOuterBorderCircle = radiusOuterBorderCircleInitial * (value.animatedValue as Float)
            invalidate()
        }
        duration = ANIMATION_DURATION_100L
    }

    private val animatorScaleSet = AnimatorSet().apply {
        playSequentially(animatorScalePartOne, animatorScalePartTwo)
    }

    private val animatorResetScale = ValueAnimator().apply {
        addUpdateListener { value ->
            radiusMidBackgroundCircle = radiusMidBackgroundCircleInitial * (value.animatedValue as Float)
            radiusOuterBorderCircle = radiusOuterBorderCircleInitial * (value.animatedValue as Float)
            invalidate()
        }
        duration = ANIMATION_DURATION_100L
    }

    private val animatorResetPointer = ValueAnimator().apply {
        addUpdateListener { value ->
            lastAngle = value.animatedValue as Float
            invalidate()
        }
        duration = ANIMATION_DURATION_200L
    }

    private fun startScaleAnimator() {
        animatorScaleSet.start()
    }

    private fun startResetAnimator() {
        animatorScaleSet.cancel()
        animatorResetScale.setFloatValues(currentAnimatorSize, SCALE_SIZE_ORIGIN)
        animatorResetScale.start()
    }

    private fun startResetPointerAnimator() {
        animatorResetPointer.setFloatValues(lastAngle, 0f)
        animatorResetPointer.start()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvasMatrix.reset()
        canvas.setMatrix(canvasMatrix)

        val width = measuredWidth
        val height = measuredHeight
        val circleCenterX = width / 2F
        val circleCenterY = height / 2F
        centerX = circleCenterX
        centerY = circleCenterY

        //绘制阴影
        paint.color = colorShadow
        paint.maskFilter = shadowBlur
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = shadowWidth
        canvas.drawCircle(circleCenterX , circleCenterY + shadowYOffset, radiusOuterBorderCircle + outerCircleStrokeWidth / 2f + shadowStrokeWidth, paint)
        paint.maskFilter = null

        canvas.drawCircle(circleCenterX, circleCenterY, radiusOuterBorderCircle + outerCircleStrokeWidth / 2f + shadowStrokeWidth, shadowClipPaint)

        //绘制中间的圆形背景
        paint.color = colorMidCircle
        paint.style = Paint.Style.FILL
        canvas.drawCircle(circleCenterX, circleCenterX, radiusMidBackgroundCircle, paint)

        //绘制内边框
        paint.color = colorInnerBorder
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = outerCircleStrokeWidth
        canvas.drawCircle(circleCenterX, circleCenterX, radiusMidBackgroundCircle + outerCircleStrokeWidth / 2f, paint)

        //绘制外边框
        paint.color = colorOuterBorder
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = outerCircleStrokeWidth
        canvas.drawCircle(circleCenterX, circleCenterX, radiusOuterBorderCircle + outerCircleStrokeWidth / 2f, paint)

        if (isPressed) {
            rectWhiteSpot.set(circleCenterX.toInt() - (whiteSpotRectWidth / 2f).toInt(), circleCenterY.toInt() - (whiteSpotRectWidth / 2f).toInt(), circleCenterX.toInt() + (whiteSpotRectWidth / 2f).toInt(), circleCenterY.toInt() + (whiteSpotRectWidth / 2f).toInt())
            canvas.drawBitmap(bitmapWhiteSpot, null, rectWhiteSpot, null)
        }

        //绘制内部圆的背景
        paint.color = colorInnerCircleBorder
        paint.style = Paint.Style.FILL
        canvas.drawCircle(circleCenterX, circleCenterY, radiusInnerCircleBorder, paint)

        //绘制内部圆
        paint.color = if (isPressed) colorInnerCirclePressed else colorInnerCircleNormal
        paint.style = Paint.Style.FILL
        canvas.drawCircle(circleCenterX, circleCenterY, radiusInnerCircle, paint)

        //绘制一圈圆点
        paint.color = colorInnerCircleBorder
        paint.style = Paint.Style.FILL
        for (i in 0..47) {
            canvasMatrix.reset()
            canvasMatrix.postRotate(currentAngle + 7.5F * i.toFloat(), circleCenterX, circleCenterY)
            canvas.setMatrix(canvasMatrix)
            canvas.drawCircle(circleCenterX - radiusInnerCircleSpotTrack + radiusInnerCircleSpot, circleCenterY, radiusInnerCircleSpot, paint)
            canvasMatrix.reset()
            canvas.setMatrix(canvasMatrix)
        }

        //画三角形
        var drawAngle = currentAngle

        val currentAdsorbAngleClock = if (currentAngle >= 0) {
            (currentAngle / 30F).toInt() * 30F + PROGRESS_PER_UNIT * ADSORB_THRESHOLD
        } else {
            (currentAngle / 30F).toInt() * 30F - PROGRESS_PER_UNIT* (1F - ADSORB_THRESHOLD)
        }
        val currentFullAngleClock = if (currentAngle >= 0) {
            (currentAngle / 30F).toInt() * 30F + PROGRESS_PER_UNIT
        } else {
            (currentAngle / 30F).toInt() * 30F
        }


        val currentAdsorbAngleAntiClock = if (currentAngle < 0) {
            (currentAngle / 30F).toInt() * 30F -PROGRESS_PER_UNIT * ADSORB_THRESHOLD
        } else {
            (currentAngle / 30F).toInt() * 30F + PROGRESS_PER_UNIT* (1F - ADSORB_THRESHOLD)
        }
        val currentFullAngleAntiClock = if (currentAngle < 0) {
            (currentAngle / 30F).toInt() * 30F -PROGRESS_PER_UNIT
        } else {
            (currentAngle / 30F).toInt() * 30F
        }

        when (currentDirection) {
            DIRECTION_CLOCKWISE -> {
                if (currentAngle >= currentAdsorbAngleClock) {
                    drawAngle = currentFullAngleClock
                }
            }
            DIRECTION_ANTICLOCKWISE -> {
                if (currentAngle <= currentAdsorbAngleAntiClock) {
                    drawAngle = currentFullAngleAntiClock
                }
            }
            else -> {}
        }

        val topX = circleCenterX - triangleHeight - radiusInnerCircle
        val topY = circleCenterY

        val leftBottomX = topX + triangleHeight
        val leftBottomY = topY + triangleBottom / 2F

        val rightBottomX = topX + triangleHeight
        val rightBottomY = topY - triangleBottom / 2F

        pathTriangle.reset()
        pathTriangle.moveTo(topX, topY)
        pathTriangle.lineTo(leftBottomX.toFloat(), leftBottomY.toFloat())
        pathTriangle.lineTo(rightBottomX.toFloat(), rightBottomY.toFloat())
        pathTriangle.close()
        paint.color = colorTriangle
        paint.style = Paint.Style.FILL
        trianglePathMatrix.reset()
        trianglePathMatrix.postRotate(drawAngle, circleCenterX, circleCenterY)
        pathTriangle.transform(trianglePathMatrix)
        canvas.drawPath(pathTriangle, paint)

        pathTriangleBorder.reset()
        pathTriangleBorder.moveTo(leftBottomX.toFloat(), leftBottomY.toFloat())
        pathTriangleBorder.lineTo(topX, topY)
        pathTriangleBorder.lineTo(rightBottomX.toFloat(), rightBottomY.toFloat())
        paint.shader = linearGradient
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = triangleStrokeWidth

        canvasMatrix.reset()
        canvasMatrix.postRotate(drawAngle, circleCenterX, circleCenterY)
        canvas.setMatrix(canvasMatrix)
        canvas.drawPath(pathTriangleBorder, paint)
        canvasMatrix.reset()
        canvas.setMatrix(canvasMatrix)
        paint.shader = null

        //undo、redo图标
        rectUndo.set(circleCenterX.toInt() - (undoRedoRectWidth / 2f).toInt(), circleCenterY.toInt() - undoRedoRectHeight.toInt(), circleCenterX.toInt() + (undoRedoRectWidth / 2f).toInt(), circleCenterY.toInt())
        canvas.drawBitmap(if (canUndo) bitmapUndoEnabled else bitmapUndo, null, rectUndo, null)
        rectRedo.set(circleCenterX.toInt() - (undoRedoRectWidth / 2f).toInt(), circleCenterY.toInt(), circleCenterX.toInt() + (undoRedoRectWidth / 2f).toInt(), circleCenterY.toInt() + undoRedoRectHeight.toInt())
        canvas.drawBitmap(if (canRedo) bitmapRedoEnabled else bitmapRedo, null, rectRedo, null)

        //绘制 12进度格 圆弧
        if (isPressed) {
            canvasMatrix.reset()
            canvas.setMatrix(canvasMatrix)
            paint.color = colorProgress
            paint.strokeWidth = progressStrokeWidth
            paint.style = Paint.Style.STROKE
            ovalProgress.left = circleCenterX - radiusWhiteSpotCircle
            ovalProgress.right = circleCenterX + radiusWhiteSpotCircle
            ovalProgress.top = circleCenterY - radiusWhiteSpotCircle
            ovalProgress.bottom = circleCenterY + radiusWhiteSpotCircle
            val count = if (drawAngle >= 0) (drawAngle / 30F).toInt() else (-drawAngle / 30F).toInt()
            for (i in 0 until count) {
                canvas.drawArc(ovalProgress, if (drawAngle >= 0) -180f + i * 30F else -180f - i * 30F,if (drawAngle >= 0) PROGRESS_PER_UNIT else -PROGRESS_PER_UNIT, false, paint)
            }
            var remainedAngle = if (drawAngle > 0 ) drawAngle - count.toFloat() * 30F else (-drawAngle - count.toFloat() * 30F)
            if (remainedAngle < 0) {
                remainedAngle = -remainedAngle
            }
            if (remainedAngle > PROGRESS_PER_UNIT) {
                remainedAngle = PROGRESS_PER_UNIT
            }
            canvas.drawArc(ovalProgress, if (drawAngle >= 0) -180f + count * 30F else -180f - count * 30F,if (drawAngle >= 0) remainedAngle else -remainedAngle, false, paint)

        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event == null) return true
        val x = event.x
        val y = event.y
        val currentEventTime = event.eventTime
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                if (MathUtils.calculatePointDistance(centerX, centerY, x, y) > maxValidTouchDistance) {
                    return false
                }
                LogHelper.d(TAG,"ACTION_DOWN")
                isFirstCheckCanRedo = true
                isFirstCheckCanUndo = true
                isPressed = true
                currentActionDownX = x
                currentActionDownY = y
                lastTouchX = x
                lastTouchY = y
                invalidate()
                startScaleAnimator()
            }
            MotionEvent.ACTION_MOVE -> {
                var angleMoved = calculateMovedAngle(lastTouchX, lastTouchY, x, y)
                if (angleMoved > MAX_ANGLE_MOVED) {
                    angleMoved = MAX_ANGLE_MOVED
                } else if (angleMoved < -MAX_ANGLE_MOVED) {
                    angleMoved = -MAX_ANGLE_MOVED
                }
                currentDirection = if (angleMoved > 0) {
                    DIRECTION_CLOCKWISE
                } else if (angleMoved < 0) {
                    DIRECTION_ANTICLOCKWISE
                } else {
                    DIRECTION_UNKNOWN
                }
                val lastTotalAngle = currentAngle
                when (currentDirection) {
                    DIRECTION_CLOCKWISE -> {
                        if (canRedo) {
                            lastUndoTime = 0L
                            stepsUndo = 0
                            isFirstCheckCanRedo = true
                            currentAngle += angleMoved
                            val maxAngle = maxRedo * 30F
                            if (currentAngle > maxAngle) currentAngle = maxAngle
                            maxRedoProgress = maxRedo
                            val targetProgressAngle = (currentAngle / 30F).toInt() * 30F + if (currentAngle >= 0) PROGRESS_PER_UNIT / 2F else -PROGRESS_PER_UNIT / 2F
                            if (currentAngle >= targetProgressAngle && lastTotalAngle < targetProgressAngle) {
                                if (lastRedoTime == 0L) {
                                    lastRedoTime = currentEventTime
                                    stepsRedo = 1
                                } else {
                                    val pastTimeAfterLastRedo = currentEventTime - lastRedoTime
                                    if (pastTimeAfterLastRedo >= ONE_SECOND) {
                                        lastRedoTime = currentEventTime
                                        stepsRedo = 1
                                    } else {
                                        job.cancel()
                                        lastRedoTime = currentEventTime
                                        stepsRedo ++
                                    }
                                }
                                //发送命令，命令内容为（类型：redo， 当前步数：stepsRedo）
                                val tempStepsRedo = stepsRedo
                                job = scope.launch {
                                    delay(1000)
                                    ConsoleEvent.sendConsoleDiskRedo(tempStepsRedo)
                                }
                                ConsoleCommandManager.sendCommand(DirectCommand(ConsoleCommandType.REDO, args = stepsRedo))
                            }
                            invalidate()
                        } else {
                            currentAngle += angleMoved
                            val maxAngle = maxRedo * 30F + EXTRA_ANGLE_MOVED
                            if (currentAngle <= maxAngle) {
                                invalidate()
                            } else {
                                currentAngle -= angleMoved
                                //发送一条"没有可重做内容"的命令
                                if (isFirstCheckCanRedo) {
                                    onNoRedoAction?.invoke()
                                    isFirstCheckCanRedo = false
                                }
                            }
                        }
                        lastTouchX = x
                        lastTouchY = y
                    }
                    DIRECTION_ANTICLOCKWISE-> {
                        if (canUndo) {
                            lastRedoTime = 0L
                            stepsRedo = 0
                            isFirstCheckCanUndo = true
                            currentAngle += angleMoved
                            val maxAngle = maxUndo * (-30F)
                            if (currentAngle < maxAngle) currentAngle = maxAngle
                            maxUndoProgress = maxUndo
                            val targetProgressAngle = (currentAngle / 30F).toInt() * 30F + if (currentAngle < 0) -PROGRESS_PER_UNIT / 2F else (PROGRESS_PER_UNIT / 2F )
                            if (currentAngle <= targetProgressAngle && lastTotalAngle > targetProgressAngle) {
                                if (lastUndoTime == 0L) {
                                    lastUndoTime = currentEventTime
                                    stepsUndo = 1
                                } else {
                                    val pastTimeAfterLastUndo = currentEventTime - lastUndoTime
                                    if (pastTimeAfterLastUndo >= ONE_SECOND) {
                                        lastUndoTime = currentEventTime
                                        stepsUndo = 1
                                    } else {
                                        job.cancel()
                                        lastUndoTime = currentEventTime
                                        stepsUndo ++
                                    }
                                }
                                //发送命令，命令内容为（类型：undo， 当前步数：stepsUndo）
                                val tempStepsUndo = stepsUndo
                                job = scope.launch {
                                    delay(1000)
                                    ConsoleEvent.sendConsoleDiskUndo(tempStepsUndo)
                                }
                                if (DigitalInkRecognitionManager.isRecognizedJustNow) {
                                    ConsoleEvent.sendConsoleUndoLocation(ConsoleEvent.CONSOLE_LOCATION_CONSOLE)
                                }
                                ConsoleCommandManager.sendCommand(DirectCommand(ConsoleCommandType.UNDO, args = stepsUndo))
                            }
                            invalidate()

                        } else {
                            currentAngle += angleMoved
                            val maxAngle = maxUndo * (-30F) - EXTRA_ANGLE_MOVED
                            if (currentAngle >= maxAngle) {
                                invalidate()
                            } else {
                                currentAngle -= angleMoved
                                //发送一条"没有可撤销内容"的命令
                                if (isFirstCheckCanUndo) {
                                    onNoUndoAction?.invoke()
                                    isFirstCheckCanUndo = false
                                }
                            }
                        }
                        lastTouchX = x
                        lastTouchY = y
                    }
                    DIRECTION_UNKNOWN -> {}
                }
            }
            MotionEvent.ACTION_UP -> {
                currentActionMoveX = x
                currentActionMoveY = y
                isPressed = false
                if (isMaxUndoChangedDuringPressed) {
                    maxUndo = maxUndoNewest
                }
                if (isMaxRedoChangedDuringPressed) {
                    maxRedo = maxRedoNewest
                }
                reset()
            }
            MotionEvent.ACTION_CANCEL -> {
                LogHelper.d(TAG,"ACTION_CANCEL")
                isPressed = false
                if (isMaxUndoChangedDuringPressed) {
                    maxUndo = maxUndoNewest
                }
                if (isMaxRedoChangedDuringPressed) {
                    maxRedo = maxRedoNewest
                }
                reset()
            }
            else -> {
                LogHelper.d(TAG,"ACTION_else")
            }
        }
        return true
    }

    private fun reset() {
        lastAngle = currentAngle
        maxUndoNewest = 0
        maxRedoNewest = 0
        isMaxUndoChangedDuringPressed = false
        isMaxRedoChangedDuringPressed = false
        currentAngle = 0F
        lastUndoTime = 0L
        lastRedoTime = 0L
        stepsUndo = 0
        stepsRedo = 0
        maxRedoProgress = -1
        maxUndoProgress = 1
        isFirstCheckCanRedo = false
        isFirstCheckCanUndo = false
        startResetPointerAnimator()
        startResetAnimator()
    }

    private fun getAngle(x1: Float, y1: Float, x2: Float, y2: Float): Float {
        val xStart = x1 - centerX
        val yStart = y1 - centerY
        val xEnd = x2 - centerX
        val yEnd = y2 - centerY
        val cosValue = (xStart * xEnd + yStart * yEnd).toDouble() / (Math.sqrt((xStart * xStart + yStart * yStart).toDouble()) * Math.sqrt((xEnd * xEnd + yEnd * yEnd).toDouble()))
        if (cosValue > 1.0) return 0F
        val angle = Math.toDegrees(acos(cosValue))
        return angle.toFloat()
    }

    private fun isAtXPositive(currentX: Float, currentY: Float, centerX: Float, centerY: Float) : Boolean {
        return currentY == centerY && currentX > centerX
    }
    private fun isAtXNegative(currentX: Float, currentY: Float, centerX: Float, centerY: Float) : Boolean {
        return currentY == centerY && currentX < centerX
    }
    private fun isAtYPositive(currentX: Float, currentY: Float, centerX: Float, centerY: Float) : Boolean {
        return currentX == centerX && currentY > centerY
    }
    private fun isAtYNegative(currentX: Float, currentY: Float, centerX: Float, centerY: Float) : Boolean {
        return currentX == centerX && currentY < centerY
    }

    private fun calculateMovedAngle(lastX: Float, lastY: Float, currentX: Float, currentY: Float): Float {
        val width = measuredWidth
        val height = measuredHeight
        val circleCenterX = width/2F
        val circleCenterY = height/2F

        val lastQuadrant = isWhichQuadrant(lastX, lastY)
        val currentQuadrant = isWhichQuadrant(currentX, currentY)

        val xOffset = currentX - lastX
        val yOffset = currentY - lastY

        var isNeedAppendAngle = false
        var angleTobeAppended = 0f

        when (lastQuadrant) {
            FIRST_QUADRANT -> {
                when (currentQuadrant) {
                    FIRST_QUADRANT -> {
                        if (abs(xOffset) > abs(yOffset)) {
                            if (xOffset > 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                            } else if (xOffset < 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                            }
                        } else if (abs(xOffset) < abs(yOffset)) {
                            if (yOffset > 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                            } else if (yOffset < 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                            }
                        } else {
                            isNeedAppendAngle = false
                        }
                    }

                    SECOND_QUADRANT -> {
                        isNeedAppendAngle = true
                        angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                    }
                    THIRD_QUADRANT -> {}
                    FOURTH_QUADRANT -> {
                        isNeedAppendAngle = true
                        angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                    }
                    COORDINATE_AXIS -> {
                        if (currentY == circleCenterY && currentX > circleCenterX) {
                            isNeedAppendAngle = true
                            angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                        } else if (currentX == circleCenterX && currentY < circleCenterY) {
                            isNeedAppendAngle = true
                            angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                        }
                    }

                }
            }

            SECOND_QUADRANT -> {
                when (currentQuadrant) {
                    FIRST_QUADRANT -> {
                        isNeedAppendAngle = true
                        angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                    }
                    SECOND_QUADRANT -> {
                        if (abs(xOffset) > abs(yOffset)) {
                            if (xOffset > 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                            } else if (xOffset < 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                            }
                        } else if (abs(xOffset) < abs(yOffset)) {
                            if (yOffset > 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                            } else if (yOffset < 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                            }
                        } else {
                            isNeedAppendAngle = false
                        }

                    }
                    THIRD_QUADRANT -> {
                        isNeedAppendAngle = true
                        angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                    }
                    FOURTH_QUADRANT -> {}
                    COORDINATE_AXIS -> {
                        if (currentY == circleCenterY && currentX > circleCenterX) {
                            isNeedAppendAngle = true
                            angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                        } else if (currentX == circleCenterX && currentY > circleCenterY) {
                            isNeedAppendAngle = true
                            angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                        }
                    }
                }
            }
            THIRD_QUADRANT -> {
                when (currentQuadrant) {
                    FIRST_QUADRANT -> {}
                    SECOND_QUADRANT -> {
                        isNeedAppendAngle = true
                        angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                    }
                    THIRD_QUADRANT -> {
                        if (abs(xOffset) > abs(yOffset)) {
                            if (xOffset > 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                            } else if (xOffset < 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                            }
                        } else if (abs(xOffset) < abs(yOffset)) {
                            if (yOffset > 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                            } else if (yOffset < 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                            }
                        } else {
                            isNeedAppendAngle = false
                        }

                    }
                    FOURTH_QUADRANT -> {
                        isNeedAppendAngle = true
                        angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                    }
                    COORDINATE_AXIS -> {
                        if (currentY == circleCenterY && currentX < circleCenterX) {
                            isNeedAppendAngle = true
                            angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                        } else if (currentX == circleCenterX && currentY > circleCenterY) {
                            isNeedAppendAngle = true
                            angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                        }
                    }
                }
            }
            FOURTH_QUADRANT -> {
                when (currentQuadrant) {
                    FIRST_QUADRANT -> {
                        isNeedAppendAngle = true
                        angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                    }
                    SECOND_QUADRANT -> {}
                    THIRD_QUADRANT -> {
                        isNeedAppendAngle = true
                        angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                    }
                    FOURTH_QUADRANT -> {
                        if (abs(xOffset) > abs(yOffset)) {
                            if (xOffset > 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                            } else if (xOffset < 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                            }
                        } else if (abs(xOffset) < abs(yOffset)) {
                            if (yOffset > 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                            } else if (yOffset < 0) {
                                isNeedAppendAngle = true
                                angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                            }
                        } else {
                            isNeedAppendAngle = false
                        }
                    }
                    COORDINATE_AXIS -> {
                        if (currentY == circleCenterY && currentX < circleCenterX) {
                            isNeedAppendAngle = true
                            angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                        } else if (currentX == circleCenterX && currentY < circleCenterY) {
                            isNeedAppendAngle = true
                            angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                        }
                    }
                }
            }
            COORDINATE_AXIS -> {
                when (currentQuadrant) {
                    FIRST_QUADRANT -> {
                        if (isAtXPositive(lastX, lastY, circleCenterX, circleCenterY)) {
                            isNeedAppendAngle = true
                            angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                        } else if (isAtYNegative(lastX, lastY, circleCenterX, circleCenterY)) {
                            isNeedAppendAngle = true
                            angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                        }
                    }
                    SECOND_QUADRANT -> {
                        if (isAtXPositive(lastX, lastY, circleCenterX, circleCenterY)) {
                            isNeedAppendAngle = true
                            angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                        } else if (isAtYPositive(lastX, lastY, circleCenterX, circleCenterY)) {
                            isNeedAppendAngle = true
                            angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                        }
                    }
                    THIRD_QUADRANT -> {
                        if (isAtXNegative(lastX, lastY, circleCenterX, circleCenterY)) {
                            isNeedAppendAngle = true
                            angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                        } else if (isAtYPositive(lastX, lastY, circleCenterX, circleCenterY)) {
                            isNeedAppendAngle = true
                            angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                        }
                    }
                    FOURTH_QUADRANT -> {
                        if (isAtXNegative(lastX, lastY, circleCenterX, circleCenterY)) {
                            isNeedAppendAngle = true
                            angleTobeAppended = getAngle(lastX, lastY, currentX, currentY)
                        } else if (isAtYNegative(lastX, lastY, circleCenterX, circleCenterY)) {
                            isNeedAppendAngle = true
                            angleTobeAppended = -getAngle(lastX, lastY, currentX, currentY)
                        }
                    }

                }
            }
        }

        LogHelper.d(TAG,"isNeedAppendAngle = $isNeedAppendAngle, angleTobeAppended = $angleTobeAppended")
        return angleTobeAppended
    }

    /**
     * 判断触摸点在哪个象限(以圆心为坐标原点的坐标系)
     */
    private fun isWhichQuadrant(x: Float, y: Float): Int {
        val width = measuredWidth
        val height = measuredHeight
        val circleCenterX = width/2F
        val circleCenterY = height/2F
        return if (x > circleCenterX && y < circleCenterY) {
            FIRST_QUADRANT
        } else if (x > circleCenterX && y > circleCenterY) {
            SECOND_QUADRANT
        } else if (x < circleCenterX && y > circleCenterY) {
            THIRD_QUADRANT
        } else if (x < circleCenterX && y < circleCenterY) {
            FOURTH_QUADRANT
        } else {
            COORDINATE_AXIS
        }
    }
}
package com.topstack.kilonotes.pad.component

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.doc.record.NoteRecord
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.NoteRecordControlViewBinding
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.infra.util.TimeUtil
import java.util.UUID

class NoteRecordControlView(
    context: Context,
    attrs: AttributeSet,
) : ConstraintLayout(context, attrs) {

    companion object {
        const val TAG = "NoteRecordControlView"
        const val MILLSECONDS_PER_SECONDS = 1000
        const val SECONDS_PER_MINUTE = 60
        const val MINUTE_PER_HOUR = 60
        const val DEFAULT_TIME = 0
        const val MAX_TIME = Int.MAX_VALUE
        const val PLAY_ERROR_TIME = -1
        const val DEFAULT_INDEX = 0
        const val DEFAULT_SPEED = 1.0F
        val MARGIN_DP_10 = AppUtils.appContext.resources.getDimension(R.dimen.dp_10).toInt()
        val MARGIN_DP_20 = AppUtils.appContext.resources.getDimension(R.dimen.dp_20).toInt()
        val MARGIN_DP_30 = AppUtils.appContext.resources.getDimension(R.dimen.dp_30).toInt()
    }

    var binding: NoteRecordControlViewBinding

    private var onPlayButtonClickListener: (() -> Unit)? = null
    private var onSpeedClickListener: (() -> Unit)? = null
    private var onBackButtonClickListener: (() -> Unit)? = null
    private var onForwardButtonClickListener: (() -> Unit)? = null
    private var onAddTagButtonClickListener: (() -> Unit)? = null
    private var onShowListButtonClickListener: (() -> Unit)? = null
    private var onProgressStartTrackingListener: (() -> Unit)? = null
    private var onProgressStopTrackingListener: ((Int) -> Unit)? = null

    var recordEditState = false

    private var currentSelectedRecordList: List<NoteRecord> = listOf()
        @JvmName("currentSelectedRecordsList")
        set(value) {
            field = value
            prepareRecordListInfo(value)
        }


    private var currentTime: Int = DEFAULT_TIME
        @JvmName("currentTimeInt")
        set(value) {
            LogHelper.d(TAG, "set current time:${value}")
            if (value == MAX_TIME) {
                field = binding.progress.max
                binding.progress.progress = binding.progress.max
            } else if (value == PLAY_ERROR_TIME) {
                field = value
                binding.currentTime.text =
                    resources.getString(R.string.note_record_played_time_with_exception)
            } else {
                field = value
                binding.currentTime.text = TimeUtil.getDurationByMillisecond(value)
                binding.progress.progress = value
            }

        }

    private var totalTime: Int = DEFAULT_TIME
        @JvmName("totalTimeInt")
        set(value) {
            field = value
            binding.totalTime.text = TimeUtil.getDurationByMillisecond(value)
            binding.progress.max = value
            binding.progress.progress = currentTime
        }

    private var isPlaying: Boolean = false
        @JvmName("isPlayingBoolean")
        set(value) {
            field = value
            binding.play.isSelected = value
        }

    private var speed: Float = DEFAULT_SPEED
        @JvmName("speedFloat")
        set(value) {
            field = value
            binding.speed.setImageResource(speedResMap[value] ?: R.drawable.playback_speed_1)
        }

    private var recordList = listOf<NoteRecord>()


    init {
        binding = NoteRecordControlViewBinding.inflate(LayoutInflater.from(context), this, true)
        transitLayout()
    }

    private val speedResMap = hashMapOf(
        0.75F to R.drawable.playback_speed_0p75,
        1.0F to R.drawable.playback_speed_1,
        1.25F to R.drawable.playback_speed_1p25,
        1.5F to R.drawable.playback_speed_1p5,
        1.75F to R.drawable.playback_speed_1p75,
        2.0F to R.drawable.playback_speed_2,
        2.25F to R.drawable.playback_speed_2p25,
        2.5F to R.drawable.playback_speed_2p5,
        3.0F to R.drawable.playback_speed_3,
    )


    override fun onFinishInflate() {
        super.onFinishInflate()
        initListener()
        initView()
    }

    private fun initView() {
        binding.currentTime.text = TimeUtil.getDurationByMillisecond(currentTime)
        binding.totalTime.text = TimeUtil.getDurationByMillisecond(totalTime)
        binding.progress.progress = DEFAULT_TIME
    }

    private fun initListener() {
        binding.play.setOnClickListener {
            onPlayButtonClickListener?.invoke()
        }
        binding.speed.setOnClickListener {
            onSpeedClickListener?.invoke()
        }
        binding.back.setOnClickListener {
            onBackButtonClickListener?.invoke()
        }
        binding.forward.setOnClickListener {
            onForwardButtonClickListener?.invoke()
        }
        binding.addTag.setOnClickListener(AntiShakeClickListener(false, MILLSECONDS_PER_SECONDS) {
            onAddTagButtonClickListener?.invoke()
            updateSegmentAndTagPoints()
        })
        binding.showList.setOnClickListener(AntiShakeClickListener {
            onShowListButtonClickListener?.invoke()
        })
        binding.progress.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentTime = progress
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                onProgressStartTrackingListener?.invoke()
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                onProgressStopTrackingListener?.invoke(binding.progress.progress)
            }

        })
    }


    fun setOnPlayButtonClickListener(action: () -> Unit) {
        onPlayButtonClickListener = action
    }

    fun setOnSpeedClickListener(action: () -> Unit) {
        onSpeedClickListener = action
    }

    fun setOnBackButtonClickListener(action: () -> Unit) {
        onBackButtonClickListener = action
    }

    fun setOnForwardButtonClickListener(action: () -> Unit) {
        onForwardButtonClickListener = action
    }

    fun setOnAddTagButtonClickListener(action: () -> Unit) {
        onAddTagButtonClickListener = action
    }

    fun setOnShowListButtonClickListener(action: () -> Unit) {
        onShowListButtonClickListener = action
    }

    fun setOnProgressStartTrackingListener(action: () -> Unit) {
        onProgressStartTrackingListener = action
    }

    fun setOnProgressStopTrackingListener(action: (Int) -> Unit) {
        onProgressStopTrackingListener = action
    }

    fun setTotalTime(time: Int) {
        totalTime = time
    }

    fun setCurrentTime(time: Int) {
        currentTime = time
    }

    fun setCurrentSelectedRecordList(list: List<NoteRecord>) {
        currentSelectedRecordList = list
        updateSegmentAndTagPoints()
    }

    fun setIsPlaying(isPlaying: Boolean) {
        this.isPlaying = isPlaying
    }

    fun setSpeed(speed: Float) {
        this.speed = speed
    }

    fun deleteTag(recordId: UUID, tagId: UUID) {
        for (record in recordList) {
            if (record.uuid == recordId) {
                for (tag in record.tags) {
                    if (tag.uuid == tagId) {
                        record.removeTag(tagId)
                        break
                    }
                }
                break
            }
        }
        updateSegmentAndTagPoints()
    }

    fun setupBlurView(root: ViewGroup) {
        binding.blurView.setupWith(root)
            .setBlurRadius(15F)
    }

    private fun transitLayout() {
        if (DimensionUtil.isPortraitScreen(context)) {

            if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context)) {
                binding.speed.isVisible = false
                binding.back.isVisible = false
                binding.forward.isVisible = false
                binding.currentTime.isVisible = false
                binding.totalTime.isVisible = false

                val progressLayoutParams =
                    binding.progress.layoutParams as ConstraintLayout.LayoutParams
                progressLayoutParams.marginStart = MARGIN_DP_20
                progressLayoutParams.marginEnd = MARGIN_DP_20
                binding.progress.layoutParams = progressLayoutParams

                val addTagLayoutParams =
                    binding.addTag.layoutParams as ConstraintLayout.LayoutParams
                addTagLayoutParams.marginEnd = MARGIN_DP_30
                binding.addTag.layoutParams = addTagLayoutParams

            } else {
                val speedLayoutParams =
                    binding.speed.layoutParams as ConstraintLayout.LayoutParams
                speedLayoutParams.marginStart = MARGIN_DP_20
                binding.speed.layoutParams = speedLayoutParams

                val backLayoutParams =
                    binding.back.layoutParams as ConstraintLayout.LayoutParams
                backLayoutParams.marginStart = MARGIN_DP_20
                binding.back.layoutParams = backLayoutParams

                val forwardLayoutParams =
                    binding.forward.layoutParams as ConstraintLayout.LayoutParams
                forwardLayoutParams.marginStart = MARGIN_DP_20
                binding.forward.layoutParams = forwardLayoutParams


                val progressLayoutParams =
                    binding.progress.layoutParams as ConstraintLayout.LayoutParams
                progressLayoutParams.marginStart = MARGIN_DP_20
                progressLayoutParams.marginEnd = MARGIN_DP_20
                binding.progress.layoutParams = progressLayoutParams

                val currentTimeLayoutParams =
                    binding.currentTime.layoutParams as ConstraintLayout.LayoutParams
                currentTimeLayoutParams.marginStart = MARGIN_DP_20
                binding.currentTime.layoutParams = currentTimeLayoutParams

                val totalTimeLayoutParams =
                    binding.totalTime.layoutParams as ConstraintLayout.LayoutParams
                totalTimeLayoutParams.marginEnd = MARGIN_DP_20
                binding.totalTime.layoutParams = totalTimeLayoutParams

                val addTagLayoutParams =
                    binding.addTag.layoutParams as ConstraintLayout.LayoutParams
                addTagLayoutParams.marginEnd = MARGIN_DP_20
                binding.addTag.layoutParams = addTagLayoutParams
            }
        } else {
            if (DimensionUtil.isLandAndOneThirdScreen(context)) {
                binding.speed.isVisible = false
                binding.back.isVisible = false
                binding.forward.isVisible = false
                binding.currentTime.isVisible = false
                binding.totalTime.isVisible = false

                val progressLayoutParams =
                    binding.progress.layoutParams as ConstraintLayout.LayoutParams
                progressLayoutParams.marginStart = MARGIN_DP_20
                progressLayoutParams.marginEnd = MARGIN_DP_20
                binding.progress.layoutParams = progressLayoutParams

                val addTagLayoutParams =
                    binding.addTag.layoutParams as ConstraintLayout.LayoutParams
                addTagLayoutParams.marginEnd = MARGIN_DP_30
                binding.addTag.layoutParams = addTagLayoutParams


            } else if (DimensionUtil.isLandAndHalfScreen(context)) {
                binding.speed.isVisible = false
                binding.back.isVisible = false
                binding.forward.isVisible = false


                val progressLayoutParams =
                    binding.progress.layoutParams as ConstraintLayout.LayoutParams
                progressLayoutParams.marginStart = MARGIN_DP_10
                progressLayoutParams.marginEnd = MARGIN_DP_10
                binding.progress.layoutParams = progressLayoutParams

                val currentTimeLayoutParams =
                    binding.currentTime.layoutParams as ConstraintLayout.LayoutParams
                currentTimeLayoutParams.marginStart = MARGIN_DP_30
                binding.currentTime.layoutParams = currentTimeLayoutParams

                val totalTimeLayoutParams =
                    binding.totalTime.layoutParams as ConstraintLayout.LayoutParams
                totalTimeLayoutParams.marginEnd = MARGIN_DP_30
                binding.totalTime.layoutParams = totalTimeLayoutParams

                val addTagLayoutParams =
                    binding.addTag.layoutParams as ConstraintLayout.LayoutParams
                addTagLayoutParams.marginEnd = MARGIN_DP_30
                binding.addTag.layoutParams = addTagLayoutParams

            }
        }
    }

    private fun prepareRecordListInfo(list: List<NoteRecord>?) {
        currentTime = DEFAULT_TIME
        if (list == null) {
            totalTime = DEFAULT_TIME
            return
        }
        recordList = list
        totalTime = updateSegmentAndTagPoints()
    }

    private fun updateSegmentAndTagPoints(): Int {
        var tempTotalTime = 0
        val segmentList = mutableListOf<Int>()
        val tagList = mutableListOf<Int>()
        var currentSegmentStartPoint = 0

        recordList.forEachIndexed { index, noteRecord ->
            tempTotalTime += noteRecord.duration
            noteRecord.tags.forEach {
                tagList.add(currentSegmentStartPoint + it.checkPoint)
            }
            currentSegmentStartPoint += noteRecord.duration
            if (index != recordList.size - 1) {
                segmentList.add(currentSegmentStartPoint)
            }
        }
        binding.progress.apply {
            setSegmentList(segmentList)
            setTagList(tagList)
        }
        return tempTotalTime
    }

    fun getShowListButtonView(): View {
        return binding.showList
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        return if (recordEditState) {
            false
        } else {
            return super.dispatchTouchEvent(ev)
        }
    }

}
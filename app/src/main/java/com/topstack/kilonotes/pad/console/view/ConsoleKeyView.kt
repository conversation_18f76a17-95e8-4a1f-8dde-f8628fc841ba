package com.topstack.kilonotes.pad.console.view

import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatImageView
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.infra.util.LogHelper

class ConsoleKeyImageView: AppCompatImageView {

    companion object{
        private const val TAG = "ConsoleKeyImageView"
    }
    private var isConsoleState = false
    private var isTowardsConsoleLock = false
    private var lastEventX = 0F
    var ignoreTouchEvent = false

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    private val gesture = GestureDetector(context,object : GestureDetector.SimpleOnGestureListener(){

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            LogHelper.d(TAG,"onSingleTapUp")
            onSingleTapUp?.invoke()
            return true
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            LogHelper.d(TAG,"onDoubleTap")
            onDoubleTap?.invoke()
            return true
        }
    })

    var onDown: (() -> Unit)? = null
    var onLongPress: (() -> Unit)? = null
    var onDoubleTap: (() -> Unit)? = null
    var onSingleTapUp: (() -> Unit)? = null
    var onMove: ((MotionEvent) -> Unit)? = null
    var onUp: ((MotionEvent) -> Unit)? = null
    var onMoveTowardsConsoleLock: (() -> Unit)? = null

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (ignoreTouchEvent) return false
        if (event == null) return false
        val x = event.x
        gesture.onTouchEvent(event)
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isConsoleState = true
                lastEventX = x
                onDown?.invoke()
            }
            MotionEvent.ACTION_MOVE -> {
                if (isConsoleState) {
                    if (!isTowardsConsoleLock) {
                        isTowardsConsoleLock = isCurrentEventTowardsConsoleLock(x)
                        if (isTowardsConsoleLock) {
                            onMoveTowardsConsoleLock?.invoke()
                        }
                        lastEventX = x
                    }
                    onMove?.invoke(event)
                }
            }
            MotionEvent.ACTION_UP -> {
                if (isConsoleState) {
                    onUp?.invoke(event)
                }
                isConsoleState = false
                isTowardsConsoleLock = false
                lastEventX = 0F
            }
            MotionEvent.ACTION_CANCEL -> {
                isConsoleState = false
                isTowardsConsoleLock = false
                lastEventX = 0F
            }
        }
        return true
    }

    private fun isCurrentEventTowardsConsoleLock(currentX: Float): Boolean {
        return if (KiloApp.isLayoutRtl) {
            currentX < lastEventX
        } else {
            currentX > lastEventX
        }
    }
}
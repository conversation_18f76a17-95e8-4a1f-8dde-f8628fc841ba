package com.topstack.kilonotes.pad.component

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.marginBottom
import androidx.core.view.marginTop
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.databinding.PadHiddenSpaceNoticeTipsLayoutBinding

class PadHiddenSpaceNoticeTipsLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val binding: PadHiddenSpaceNoticeTipsLayoutBinding

    private var onRootViewClick: (() -> Unit)? = null
    private var onCloseClick: (() -> Unit)? = null

    init {
        binding = PadHiddenSpaceNoticeTipsLayoutBinding.inflate(
            LayoutInflater.from(context),
            this,
            true
        )

        if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(context) || DimensionUtil.isLandAndOneThirdScreen(
                context
            )
        ) {
            val params = binding.containerShadow.layoutParams as ConstraintLayout.LayoutParams
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.setMargins(
                context.resources.getDimension(R.dimen.dp_12).toInt(),
                binding.containerShadow.marginTop,
                context.resources.getDimension(R.dimen.dp_12).toInt(),
                binding.containerShadow.marginBottom
            )
            binding.containerShadow.layoutParams = params
        }

        binding.close.setOnClickListener(AntiShakeClickListener {
            onCloseClick?.invoke()
        })

        binding.container.setOnClickListener(AntiShakeClickListener {
            onRootViewClick?.invoke()
        })
    }

    fun setOnRootViewClickListener(action: () -> Unit) {
        onRootViewClick = action
    }

    fun setOnCloseClickListener(action: () -> Unit) {
        onCloseClick = action
    }
}
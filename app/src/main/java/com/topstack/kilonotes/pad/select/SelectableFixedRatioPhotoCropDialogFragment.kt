package com.topstack.kilonotes.pad.select

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.RectF
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.fragment.BaseFragment
import com.topstack.kilonotes.base.doodle.utils.BitmapUtils
import com.topstack.kilonotes.base.imagecrop.CropOptions
import com.topstack.kilonotes.base.select.BUNDLE_PATH_KEY
import com.topstack.kilonotes.base.select.BUNDLE_URI_KEY
import com.topstack.kilonotes.base.select.PICK_PIC_RESULT_CODE
import com.topstack.kilonotes.databinding.FragmentPickAndSelectableFixedCropPhotoBinding
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.pad.select.PadSelectPhotoViewModel.Companion.adaptiveRatio
import com.topstack.kilonotes.pad.select.PadSelectPhotoViewModel.Companion.horizontalRatio
import com.topstack.kilonotes.pad.select.PadSelectPhotoViewModel.Companion.verticalRatio
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.FileNotFoundException
import java.io.InputStream

class SelectableFixedRatioPhotoCropDialogFragment :
    BaseFragment(R.layout.fragment_pick_and_selectable_fixed_crop_photo) {

    private lateinit var binding: FragmentPickAndSelectableFixedCropPhotoBinding
    private val selectPhotoViewModel: PadSelectPhotoViewModel by activityViewModels()

    private var bitmapRatio = 0F

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentPickAndSelectableFixedCropPhotoBinding.bind(view)


        val uri = arguments?.getParcelable<Uri>(BUNDLE_URI_KEY)
        if (null == uri) {
            findNavController().popBackStack()
            return
        }

        val contentResolver = requireContext().contentResolver
        var imageStream: InputStream? = null
        try {
            imageStream = contentResolver.openInputStream(uri)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            LogHelper.d(defaultTag, "openInputStream error uri = $uri", e, true)
        }

        if (imageStream == null) {
            findNavController().popBackStack()
            return
        }

        var bitmap: Bitmap?
        val option = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        var bitmapWidth = 0
        var bitmapHeight = 0

        imageStream.use {
            BitmapFactory.decodeStream(it, null, option)
            option.inSampleSize = BitmapUtils.calculateInSampleSize(
                option,
                resources.displayMetrics.widthPixels,
                resources.displayMetrics.heightPixels
            )
            bitmapWidth = option.outWidth
            bitmapHeight = option.outHeight
            option.inJustDecodeBounds = false
        }

        contentResolver.openInputStream(uri).use {
            bitmap = BitmapFactory.decodeStream(it, null, option)
        }

        val degree = BitmapUtils.getBitmapDegree(requireContext(), uri)
        if (degree == 90 || degree == 180 || degree == 270) {
            bitmap = BitmapUtils.rotate(bitmap, degree)
        }

        if (degree == 90 || degree == 270) {
            if (bitmapWidth != 0) {
                bitmapRatio = bitmapHeight.toFloat() / bitmapWidth.toFloat()
            }
        } else {
            if (bitmapHeight != 0) {
                bitmapRatio = bitmapWidth.toFloat() / bitmapHeight.toFloat()
            }
        }

        if (bitmap == null) {
            findNavController().popBackStack()
            return
        }
        val cropOptions = CropOptions().apply {
            maskColor = resources.getColor(R.color.white_60)
            adjustableAreaMaskColor = maskColor
        }
        cropOptions.cropAreaOffset = RectF(
            resources.getDimension(R.dimen.dp_73),
            resources.getDimension(R.dimen.dp_73),
            resources.getDimension(R.dimen.dp_73),
            resources.getDimension(R.dimen.dp_73)
        )
        binding.cropView.setCropOptions(cropOptions)
        binding.cropView.isZoomForbade = selectPhotoViewModel.forbidZoom
        selectPhotoViewModel.currentRatioType = if (selectPhotoViewModel.isVertical) verticalRatio else horizontalRatio
        updateRatio()
        binding.cropView.setSourceBitmap(bitmap!!)

        binding.close.setOnClickListener {
            requireActivity().finish()
        }

        binding.confirm.setOnClickListener {
            binding.confirm.isEnabled = false
            lifecycleScope.launch(Dispatchers.IO) {
                val imageFile = binding.cropView.getCroppedImageFile()
                val imageFilePath = imageFile?.absolutePath ?: ""
                withContext(Dispatchers.Main) {
                    binding.confirm.isEnabled = true
                    val activity = requireActivity()
                    val intent = activity.intent
                    intent.putExtra(BUNDLE_PATH_KEY, imageFilePath)
                    intent.putExtra(
                        BUNDLE_URI_KEY,
                        if (imageFile == null) null else Uri.fromFile(imageFile)
                    )
                    activity.setResult(PICK_PIC_RESULT_CODE, intent)
                    activity.finish()
                }
            }
        }

        binding.ratioHorizontal.setOnClickListener {
            selectPhotoViewModel.currentRatioType = horizontalRatio
            updateRatio()
        }
        binding.ratioVertical.setOnClickListener {
            selectPhotoViewModel.currentRatioType = verticalRatio
            updateRatio()
        }
        binding.ratioAdaptive.setOnClickListener {
            selectPhotoViewModel.currentRatioType = adaptiveRatio
            updateRatio()
        }
    }

    private fun updateRatio() {
        when (selectPhotoViewModel.currentRatioType) {
            horizontalRatio -> {
                binding.cropView.enterFixRatioMode(600f / 450f, true)
                binding.ratioHorizontal.isSelected = true
                binding.ratioVertical.isSelected = false
                binding.ratioAdaptive.isSelected = false
                binding.ratioText.setText(R.string.crop_ratio_horizontal)
            }

            verticalRatio -> {
                binding.cropView.enterFixRatioMode(450f / 600f, true)
                binding.ratioHorizontal.isSelected = false
                binding.ratioVertical.isSelected = true
                binding.ratioAdaptive.isSelected = false
                binding.ratioText.setText(R.string.crop_ratio_vertical)
            }

            adaptiveRatio -> {
                binding.cropView.enterFixRatioMode(bitmapRatio, true)
                binding.ratioHorizontal.isSelected = false
                binding.ratioVertical.isSelected = false
                binding.ratioAdaptive.isSelected = true
                binding.ratioText.setText(R.string.crop_ratio_adaptive)
            }
        }
    }
}
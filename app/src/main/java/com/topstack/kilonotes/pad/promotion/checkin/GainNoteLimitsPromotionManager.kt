package com.topstack.kilonotes.pad.promotion.checkin

import android.app.AlarmManager
import android.app.PendingIntent
import android.app.PendingIntent.FLAG_CANCEL_CURRENT
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.AlarmManagerCompat
import androidx.core.app.NotificationManagerCompat
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.db.HandbookDatabase
import com.topstack.kilonotes.infra.device.DeviceUtils
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.infra.util.TimeUtil
import com.topstack.kilonotes.infra.util.TimeUtil.toTimestamp
import com.topstack.kilonotes.pad.promotion.Promotion
import com.topstack.kilonotes.pad.promotion.PromotionDao
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.locks.ReentrantLock

/**
 *
 */
object GainNoteLimitsPromotionManager {
    private val TAG = "GainNoteLimitsPromotionManager"
    const val totalScheduleCount = 7
    private val bonusScheduleIndices = listOf(1, 3, 6)
    private const val dailyCheckInBonusCount = 1
    const val extraBonusCheckInDays = 5
    private const val extraBonusCount = 2
    private const val extraBonusTag = "extra bonus"

    private const val promotionInterval = 14
    private const val expireInterval = 37
    private const val maxDisplayExpireDays = 30

    private val checkInDao: CheckInDao by lazy { HandbookDatabase.getDatabase().checkInDao() }
    private val promotionDao: PromotionDao by lazy { HandbookDatabase.getDatabase().promotionDao() }
    private val noteLimitBonusDao: NoteLimitBonusDao by lazy {
        HandbookDatabase.getDatabase().noteLimitBonusDao()
    }
    private val roleId: String by lazy { DeviceUtils.getDeviceId() }
    private const val promotionName: String = Promotion.DAILY_CHECK_IN_GAIN_NOTE_LIMITS
    private var promotion: Promotion? = null

    private val checkInRecords = mutableListOf<CheckInRecord>()
    private val _checkInSchedules = CopyOnWriteArrayList<CheckInSchedule>()
    private var todaySchedule: CheckInSchedule? = null

    private val loadLock = ReentrantLock()
    private var generatingSchedules = false

    suspend fun getCheckInSchedules(): List<CheckInSchedule> {
        if (_checkInSchedules.isNotEmpty()) {
            return _checkInSchedules
        }

        generatingSchedules = true
        if (checkInRecords.isEmpty()) {
            withContext(Dispatchers.IO) {
                checkInRecords.addAll(
                    checkInDao.getRecordsByRoleIdAndPromotionName(
                        roleId,
                        promotionName
                    )
                )
            }
        }

        loadLock.lock()
        try {
            if (_checkInSchedules.isEmpty()) {
                val checkedInDates =
                    checkInRecords.map {
                        TimeUtil.timestampToLocalDataTime(it.checkInTime).toLocalDate()
                    }
                val startDate = checkedInDates.firstOrNull() ?: LocalDate.now()
                val lastCheckedInDate = checkedInDates.lastOrNull()
                for (index in 0 until totalScheduleCount) {
                    val scheduleDate = startDate.plusDays(index.toLong())
                    val checkInStatus = if (checkedInDates.any { it.isEqual(scheduleDate) }) {
                        CheckInStatus.CHECKED
                    } else if (scheduleDate.isBefore(LocalDate.now())
                        || lastCheckedInDate != null && scheduleDate.isBefore(lastCheckedInDate)
                    ) {
                        CheckInStatus.MISSED
                    } else {
                        CheckInStatus.UNCHECKED
                    }

                    val checkInSchedule = CheckInSchedule(
                        index + 1,
                        scheduleDate,
                        index in bonusScheduleIndices,
                        checkInStatus
                    )
                    if (checkInSchedule.date.isEqual(LocalDate.now())) {
                        todaySchedule = checkInSchedule
                    }

                    _checkInSchedules.add(checkInSchedule)
                }
            }
        } finally {
            loadLock.unlock()
        }
        generatingSchedules = false

        promotionStatusListener?.onCheckedInDaysChanged(checkInRecords.size, todaySchedule)
        return _checkInSchedules
    }

    fun updateScheduleStatus(): List<CheckInSchedule> {
        if (!generatingSchedules) {
            val updateDate = checkInRecords.isEmpty()
            val startDate = LocalDate.now()
            _checkInSchedules.forEachIndexed { index, schedule ->
                if (updateDate) {
                    schedule.date = startDate.plusDays(index.toLong())
                }
                val checkedInDates =
                    checkInRecords.map {
                        TimeUtil.timestampToLocalDataTime(it.checkInTime).toLocalDate()
                    }
                val lastCheckedInDate = checkedInDates.lastOrNull()
                schedule.status = if (checkedInDates.any { it.isEqual(schedule.date) }) {
                    CheckInStatus.CHECKED
                } else if (schedule.date.isBefore(LocalDate.now())
                    || lastCheckedInDate != null && schedule.date.isBefore(lastCheckedInDate)
                ) {
                    CheckInStatus.MISSED
                } else {
                    CheckInStatus.UNCHECKED
                }
            }
        }
        return _checkInSchedules
    }

    fun getCheckInDays(): Int {
        return checkInRecords.size
    }

    suspend fun checkIn(schedule: CheckInSchedule): Boolean {
        if (schedule.status != CheckInStatus.UNCHECKED || !schedule.date.isEqual(LocalDate.now())) {
            return false
        }

        withContext(Dispatchers.IO) {
            // 开启活动
            if (checkInRecords.isEmpty()) {
                val now = LocalDateTime.now()
                val promotion = Promotion(
                    UUID.randomUUID(),
                    promotionName,
                    now.toTimestamp(),
                    now.toLocalDate().plusDays(promotionInterval.toLong()).atStartOfDay()
                        .toTimestamp(),
                    0,
                    false
                )
                promotionDao.insertPromotion(promotion)
            }


            // 添加签到记录
            val checkInRecord = CheckInRecord(
                UUID.randomUUID(),
                roleId,
                System.currentTimeMillis(),
                promotionName
            )
            checkInDao.insertRecord(checkInRecord)
            checkInRecords.add(checkInRecord)
            schedule.status = CheckInStatus.CHECKED
            if (schedule.isToday()) {
                todaySchedule?.status = CheckInStatus.CHECKED
            }
            promotionStatusListener?.onCheckedInDaysChanged(checkInRecords.size, todaySchedule)
            promotionStatusListener?.onCheckInScheduleStatusChanged(schedule)

            // 发放签到奖励
            if (schedule.hasBonus) {
                grantBonus(dailyCheckInBonusCount)
                promotionStatusListener?.onDailyCheckInBonusGained(dailyCheckInBonusCount)
                promotionStatusListener?.onNoteLimitsChanged(
                    getRemainedNoteLimits(),
                    getNoteLimitsValidDays()
                )
            }
        }

        return true
    }

    suspend fun grantExtraNoteBonus(): Boolean = withContext(Dispatchers.IO) {
        val gainedExtraBonus =
            noteLimitBonusDao.getBonusByExtraInfo(roleId, extraBonusTag).isNotEmpty()
        if (gainedExtraBonus) {
            return@withContext false
        } else {
            grantBonus(extraBonusCount, extraBonusTag)
            promotionStatusListener?.onExtraBonusGained(extraBonusCount)
            promotionStatusListener?.onNoteLimitsChanged(
                getRemainedNoteLimits(),
                getNoteLimitsValidDays()
            )
            return@withContext true
        }
    }

    /**
     * 是否已获取过满签奖励
     */
    suspend fun hasExtraNoteBonusGranted(): Boolean = withContext(Dispatchers.IO) {
        noteLimitBonusDao.getBonusByExtraInfo(roleId, extraBonusTag).isNotEmpty()
    }

    private suspend fun grantBonus(count: Int = 1, extraInfo: String = ""): Boolean =
        withContext(Dispatchers.IO) {
            val startDate =
                checkInRecords.firstOrNull()
                    ?.let { TimeUtil.timestampToLocalDataTime(it.checkInTime) }
                    ?: return@withContext false
            val expireDate = startDate.plusDays(expireInterval.toLong())

            for (i in 0 until count) {
                val bonus = NoteLimitBonus(
                    UUID.randomUUID(),
                    roleId,
                    System.currentTimeMillis(),
                    expireDate.toTimestamp(),
                    used = false,
                    isPermanent = false,
                    extraInfo = extraInfo
                )
                noteLimitBonusDao.insertBonus(bonus)
            }
            return@withContext true
        }

    fun getDaysToSchedule(schedule: CheckInSchedule): Int =
        LocalDate.now().until(schedule.date, ChronoUnit.DAYS).toInt()

    fun getDaysToNextBonus(): Int {
        var days = -1
        val lastCheckInDate = checkInRecords.lastOrNull()
            ?.let { TimeUtil.timestampToLocalDataTime(it.checkInTime).toLocalDate() }
        val today = LocalDate.now()

        val compareDate: LocalDate =
            if (lastCheckInDate?.isAfter(today) == true) lastCheckInDate else today
        var compareDayChecked = false
        for (i in _checkInSchedules.indices) {
            val schedule = _checkInSchedules[i]
            if (schedule.date == compareDate) {
                compareDayChecked = schedule.status == CheckInStatus.CHECKED
            }
            if (i in bonusScheduleIndices) {
                if (schedule.status == CheckInStatus.UNCHECKED && schedule.date.isAfter(compareDate)) {
                    days = compareDate.until(schedule.date, ChronoUnit.DAYS).toInt()
                    if (!compareDayChecked) {
                        days += 1
                    }
                    break
                }
            }
        }

        return days
    }

    suspend fun getRemainedNoteLimits(): Int = withContext(Dispatchers.IO) {
        return@withContext noteLimitBonusDao.getUnexpiredUnusedNoteLimits(
            roleId,
            System.currentTimeMillis()
        )
    }

    suspend fun getUsedNoteLimits(): Int = withContext(Dispatchers.IO) {
        return@withContext noteLimitBonusDao.getUsedNoteLimits(roleId)
    }

    suspend fun getTotalNoteLimits(): Int = withContext(Dispatchers.IO) {
        return@withContext getRemainedNoteLimits() + getUsedNoteLimits()
    }


    suspend fun getPermanentNoteLimits(): Int = withContext(Dispatchers.IO) {
        return@withContext noteLimitBonusDao.getPermanentNoteLimits(roleId)
    }

    private suspend fun getPromotion(): Promotion? {
        if (promotion != null) {
            return promotion
        }

        withContext(Dispatchers.IO) {
            promotion = promotionDao.getPromotionByName(promotionName)
        }
        return promotion
    }

    suspend fun getNoteLimitsValidDays(): Int = withContext(Dispatchers.IO) {
        val lastExpiredTime = noteLimitBonusDao.getLastExpiredTime(roleId)
        if (lastExpiredTime <= 0) {
            return@withContext -1
        }

        val lastExpiredDate = TimeUtil.timestampToLocalDataTime(lastExpiredTime).toLocalDate()
        return@withContext LocalDate.now().until(lastExpiredDate, ChronoUnit.DAYS).toInt()
            .coerceAtMost(
                maxDisplayExpireDays
            )
    }

    suspend fun useNoteLimit(count: Int = 1): Boolean = withContext(Dispatchers.IO) {
        if (count <= 0) {
            return@withContext false
        }

        noteLimitBonusDao.getUnexpiredUnusedBonus(roleId, System.currentTimeMillis(), count)
            .sortedWith { bonus1, bonus2 ->
                val weight1 = if (bonus1.isPermanent) 0 else 1
                val weight2 = if (bonus2.isPermanent) 0 else 1
                weight1 - weight2
            }
            .forEach { bonus ->
                bonus.used = true
                bonus.isPermanent = true
                noteLimitBonusDao.updateBonus(bonus)
            }

        return@withContext true
    }

    suspend fun releaseNoteLimit(count: Int = 1) = withContext(Dispatchers.IO) {
        if (count <= 0) {
            return@withContext false
        }

        noteLimitBonusDao.getUsedBonus(roleId, count)
            .forEach { bonus ->
                bonus.used = false
                noteLimitBonusDao.updateBonus(bonus)
            }

        return@withContext true
    }

    suspend fun isPromotionFinished(): Boolean = withContext(Dispatchers.IO) {
        getPromotion()?.isFinished() ?: false
    }

    var promotionStatusListener: PromotionStatusListener? = null

    fun setupCheckInNotification() {
        GlobalScope.launch {
            val shouldNotify =
                NotificationManagerCompat.from(AppUtils.appContext).areNotificationsEnabled()

            if (_checkInSchedules.isEmpty()) {
                getCheckInSchedules()
            }

            val notificationIntent =
                Intent(AppUtils.appContext, CheckInNotificationReceiver::class.java)
            val notificationPendingIntent = PendingIntent.getBroadcast(
                AppUtils.appContext,
                0,
                notificationIntent,
                FLAG_CANCEL_CURRENT or PendingIntent.FLAG_MUTABLE
            )
            val alarmManager =
                AppUtils.appContext.getSystemService(Context.ALARM_SERVICE) as AlarmManager

            val canScheduleExactAlarms = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                alarmManager.canScheduleExactAlarms()
            } else {
                true
            }
            if (shouldNotify && canScheduleExactAlarms) {
                for (schedule in _checkInSchedules) {
                    val alarmTime = schedule.date.atTime(LocalTime.of(19, 0))
                    val alarmTimeMillis = alarmTime.toTimestamp()
                    if (alarmTimeMillis > System.currentTimeMillis()) {
                        LogHelper.d(TAG, "schedule notification: $alarmTime")
                        AlarmManagerCompat.setExact(
                            alarmManager,
                            AlarmManager.RTC_WAKEUP,
                            alarmTimeMillis,
                            notificationPendingIntent
                        )
                        break
                    }
                }
            } else {
                alarmManager.cancel(notificationPendingIntent)
            }
        }
    }

    fun cancelCheckInNotificationIfPossible() =
        NotificationManagerCompat.from(AppUtils.appContext).cancel(R.id.check_in_notice_switch)

    data class CheckInSchedule(
        val schedule: Int, // 第几天
        var date: LocalDate,
        val hasBonus: Boolean,
        var status: CheckInStatus
    ) {
        fun isToday(): Boolean = LocalDate.now().isEqual(date)
    }

    interface PromotionStatusListener {
        fun onCheckedInDaysChanged(checkedInDays: Int, todaySchedule: CheckInSchedule?)
        fun onCheckInScheduleStatusChanged(schedule: CheckInSchedule)
        fun onDailyCheckInBonusGained(bonusCount: Int)
        fun onExtraBonusGained(bonusCount: Int)
        fun onNoteLimitsChanged(remainedLimits: Int, validDays: Int)
    }
}

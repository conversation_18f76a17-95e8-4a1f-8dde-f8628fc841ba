package com.topstack.kilonotes.pad.component.dialog

import android.os.Bundle
import android.view.View
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.component.dialog.ProgressDialog

class GenerateLongPictureProgressDialog : ProgressDialog() {
    companion object {
        const val TAG = "GenerateLongPictureProgressDialog"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setCloseButtonVisibility(true)
        setPositiveButtonVisibility(false)
        updateTitleInternal(resources.getString(R.string.generate_long_picture_progress_dialog_title))
    }
}
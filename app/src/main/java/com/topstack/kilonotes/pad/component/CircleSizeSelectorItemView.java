package com.topstack.kilonotes.pad.component;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.topstack.kilonotes.R;

public class CircleSizeSelectorItemView extends View {
    private static final int DEFAULT_RADIUS = 24;
    private float mRadius;
    private float size;
    private int height;
    private int width;
    private final Paint painter = new Paint(Paint.ANTI_ALIAS_FLAG);
    private int mNormalColor = getContext().getColor(R.color.note_tool_pen_thick_color_1);
    private int mSelectedColor = getContext().getColor(R.color.note_tool_pen_thick_color_2);

    public CircleSizeSelectorItemView(Context context) {
        super(context);
    }

    public CircleSizeSelectorItemView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.CircleSizeSelectorItemView);
        mRadius = array.getDimensionPixelSize(R.styleable.CircleSizeSelectorItemView_selectorRadius, DEFAULT_RADIUS);
        init();
        array.recycle();
    }

    private void init() {
        painter.setStyle(Paint.Style.FILL);
        painter.setAntiAlias(true);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        height = h;
        width = w;
    }

    public void setColor(int normalColor, int selectedColor) {
        mNormalColor = normalColor;
        mSelectedColor = selectedColor;
    }

    @Override
    public void setSelected(boolean selected) {
        super.setSelected(selected);
        invalidate();
    }

    public void setRadius(int mRadius) {
        this.mRadius = mRadius;
        invalidate();
    }

    public float getSize() {
        return size;
    }

    public void setSize(float size) {
        this.size = size;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        painter.setColor(isSelected() ? mSelectedColor : mNormalColor);
        canvas.drawCircle((float) width / 2, (float) height / 2, mRadius, painter);
    }
}

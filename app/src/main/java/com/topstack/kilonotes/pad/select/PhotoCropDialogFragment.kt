package com.topstack.kilonotes.pad.select

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import android.widget.SeekBar
import android.widget.TextView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.imagecrop.BaseImageCropDialogFragment
import com.topstack.kilonotes.base.select.BasePhotoCropDialogFragment
import com.topstack.kilonotes.base.util.DimensionUtil
import kotlin.math.roundToInt

class PhotoCropDialogFragment : BasePhotoCropDialogFragment(), View.OnClickListener {
    private var isInSmallWidthWindow = false
    private var isInSmallHeightWindow = false
    private val normalWidth by lazy { (resources.getDimension(R.dimen.dp_980)).toInt() }
    private val normalHeight by lazy { (resources.getDimension(R.dimen.dp_980)).toInt() }
    private val minAlpha = 5
    private val maxAlpha = 100

    private lateinit var alphaMinText: TextView
    private lateinit var alphaMaxText: TextView
    private lateinit var alphaText: TextView
    private lateinit var alphaSeekBar: SeekBar

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val screenDimensions = DimensionUtil.getScreenDimensions(requireContext())
        isInSmallWidthWindow =
            screenDimensions.widthPixels <= screenDimensions.heightPixels && screenDimensions.widthPixels < normalWidth
        isInSmallHeightWindow =
            screenDimensions.widthPixels > screenDimensions.heightPixels && screenDimensions.heightPixels < normalHeight

        val root = if (isInSmallWidthWindow) {
            inflater.inflate(
                R.layout.dialog_note_image_crop_small_width_window,
                container,
                false
            )
        } else if (isInSmallHeightWindow) {
            inflater.inflate(
                R.layout.dialog_note_image_crop_small_height_window,
                container,
                false
            )
        } else {
            inflater.inflate(R.layout.dialog_note_image_crop, container, false)
        }

        alphaMinText = root.findViewById(R.id.alpha_start)
        alphaMaxText = root.findViewById(R.id.alpha_end)
        alphaText = root.findViewById(R.id.alpha)
        alphaSeekBar = root.findViewById(R.id.alpha_seek_bar)

        alphaMinText.text = "$minAlpha"
        alphaMaxText.text = "$maxAlpha"
        setSeekBarThumb(requireContext())
        alphaSeekBar.run {
            max = 100 - minAlpha

            setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?,
                    progress: Int,
                    fromUser: Boolean
                ) {
                    val toAlphaPercent = minAlpha + progress
                    alphaText.text = String.format("%d%%", toAlphaPercent)
                    val alphaInt = (0xFF * toAlphaPercent / 100f).roundToInt()
                    cropView.imageAlpha = alphaInt
                    magnifierView.imageAlpha = alphaInt
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {
                }

                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                }
            })
        }
        return root
    }

    private fun setSeekBarThumb(context: Context) {
        val targetSize = context.resources.getDimensionPixelSize(R.dimen.dp_40)
        var bitmap = BitmapFactory.decodeResource(
            context.resources,
            R.drawable.pen_and_text_size_tracker_background
        )
        val newBitmap = Bitmap.createScaledBitmap(bitmap, targetSize, targetSize, true)


        alphaSeekBar.thumb = BitmapDrawable(context.resources, newBitmap)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val alphaRemap = cropView.imageAlpha * 100 / 255
        alphaSeekBar.progress = alphaRemap - minAlpha
        alphaText.text = String.format("%d%%", alphaRemap)

        if (isInSmallHeightWindow) {
            magnifierView.borderWidth = resources.getDimension(R.dimen.dp_4)
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            if (isInSmallWidthWindow) {
                setLayout(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    (resources.getDimension(R.dimen.dp_980)).toInt()
                )
                setGravity(Gravity.BOTTOM)
            } else if (isInSmallHeightWindow) {
                setLayout(
                    (resources.getDimension(R.dimen.dp_590)).toInt(),
                    (resources.getDimension(R.dimen.dp_560)).toInt()
                )
                setGravity(Gravity.CENTER)
            } else {
                setLayout(
                    (resources.getDimension(R.dimen.dp_980)).toInt(),
                    (resources.getDimension(R.dimen.dp_980)).toInt()
                )
                setGravity(Gravity.CENTER)
            }

        }
    }
}
package com.topstack.kilonotes.pad.snippet

import android.Manifest
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Color
import android.hardware.input.InputManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.FrameLayout
import android.widget.PopupWindow
import androidx.activity.addCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.transition.AutoTransition
import androidx.transition.Transition
import androidx.transition.TransitionListenerAdapter
import androidx.transition.TransitionManager
import com.topstack.kilonotes.KiloApp
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.KeyEventViewModel
import com.topstack.kilonotes.base.component.dialog.AlertDialog
import com.topstack.kilonotes.base.component.fragment.NaviEnum
import com.topstack.kilonotes.base.component.view.AdsorptionEdgeLayout
import com.topstack.kilonotes.base.component.view.CustomMaxHeightRecycleView
import com.topstack.kilonotes.base.component.view.VerticalTextView
import com.topstack.kilonotes.base.component.view.WindowColorPickView
import com.topstack.kilonotes.base.component.view.impl.AntiShakeClickListener
import com.topstack.kilonotes.base.config.Preferences
import com.topstack.kilonotes.base.config.UserUsageConfig
import com.topstack.kilonotes.base.constant.MimeType
import com.topstack.kilonotes.base.db.HandbookDatabase
import com.topstack.kilonotes.base.doc.Document
import com.topstack.kilonotes.base.doc.io.FileImporter
import com.topstack.kilonotes.base.doodle.listeners.IGraffitiSwitchListener
import com.topstack.kilonotes.base.doodle.listeners.IHighlighterSwitchListener
import com.topstack.kilonotes.base.doodle.listeners.IPatternSwitchListener
import com.topstack.kilonotes.base.doodle.manager.modelmager.OnPageThumbnailUpdatedListener
import com.topstack.kilonotes.base.doodle.model.Page
import com.topstack.kilonotes.base.doodle.model.image.InsertableBitmap
import com.topstack.kilonotes.base.doodle.views.doodleview.DoodleView
import com.topstack.kilonotes.base.doodle.views.doodleview.GraffitiToolType
import com.topstack.kilonotes.base.doodle.views.doodleview.InputMode
import com.topstack.kilonotes.base.doodle.views.guideline.GuideLineConfig
import com.topstack.kilonotes.base.doodle.views.lassoview.LassoToolType
import com.topstack.kilonotes.base.doodle.views.textborderview.TextToolType
import com.topstack.kilonotes.base.doodle.visual.brush.VisualStrokeErase
import com.topstack.kilonotes.base.fonts.FontImportError
import com.topstack.kilonotes.base.fonts.FontImportFail
import com.topstack.kilonotes.base.fonts.FontImportSuccess
import com.topstack.kilonotes.base.fonts.FontManager
import com.topstack.kilonotes.base.imagecrop.BaseImageCropDialogFragment
import com.topstack.kilonotes.base.imagecrop.CropType
import com.topstack.kilonotes.base.ktx.adjustRtlOrLtrLayout
import com.topstack.kilonotes.base.note.BaseSelectColorWindow
import com.topstack.kilonotes.base.note.BasicDoodleFragment
import com.topstack.kilonotes.base.note.FontAttributes
import com.topstack.kilonotes.base.note.GraffitiAttributes
import com.topstack.kilonotes.base.note.HighlighterAttributes
import com.topstack.kilonotes.base.note.PenAttributes
import com.topstack.kilonotes.base.note.TextAttributes
import com.topstack.kilonotes.base.note.adapter.NoteToolEraserAdapter
import com.topstack.kilonotes.base.note.adapter.NoteToolLassoAdapter
import com.topstack.kilonotes.base.note.adapter.NoteToolLassoStyleAdapter
import com.topstack.kilonotes.base.note.adapter.ToolColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolDividerAdapter
import com.topstack.kilonotes.base.note.adapter.ToolHighlighterColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolHighlighterSizeAdapter
import com.topstack.kilonotes.base.note.adapter.ToolPenColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolPenSizeAdapter
import com.topstack.kilonotes.base.note.adapter.ToolTextColorAdapter
import com.topstack.kilonotes.base.note.adapter.ToolTextFontAdapter
import com.topstack.kilonotes.base.note.adapter.ToolTextParagraphAdapter
import com.topstack.kilonotes.base.note.adapter.ToolTextSizeAdapter
import com.topstack.kilonotes.base.note.model.ColorWindowItem
import com.topstack.kilonotes.base.note.model.ColorWindowItemType
import com.topstack.kilonotes.base.note.model.EraseToolItem
import com.topstack.kilonotes.base.note.recognition.text.TextRecognitionManager
import com.topstack.kilonotes.base.note.viewmodel.AdsorptionEdgeViewModel
import com.topstack.kilonotes.base.note.viewmodel.ImportFontViewModel
import com.topstack.kilonotes.base.note.viewmodel.NoteSnippetCreateViewModel
import com.topstack.kilonotes.base.note.viewmodel.NoteViewModel
import com.topstack.kilonotes.base.note.viewmodel.SnippetViewModel
import com.topstack.kilonotes.base.pickimage.PickImageResultParser
import com.topstack.kilonotes.base.track.event.EditEvent
import com.topstack.kilonotes.base.track.event.ImageCallEvent
import com.topstack.kilonotes.base.util.ActivityResultRequester
import com.topstack.kilonotes.base.util.ClipboardUtil
import com.topstack.kilonotes.base.util.DimensionUtil
import com.topstack.kilonotes.base.util.PermissionRequester
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.base.util.WindowInsetsUtils
import com.topstack.kilonotes.base.util.getDefaultGraffitiLineDrawingPenPreferenceStyleList
import com.topstack.kilonotes.base.util.getDefaultGraffitiOutlinePenPreferenceStyleList
import com.topstack.kilonotes.base.util.getDefaultGraffitiPatternPreferenceStyleList
import com.topstack.kilonotes.base.util.getDefaultHighlighterPreferenceColorList
import com.topstack.kilonotes.base.util.getDefaultPenPreferenceColorList
import com.topstack.kilonotes.base.util.isPenDevice
import com.topstack.kilonotes.databinding.FragmentHandWriteSnippetEditBinding
import com.topstack.kilonotes.infra.size.MmSize
import com.topstack.kilonotes.infra.size.PtSize
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.mlkit.recognition.text.model.TextRecognitionResult
import com.topstack.kilonotes.notedata.NoteRepository
import com.topstack.kilonotes.pad.MainActivity
import com.topstack.kilonotes.pad.component.dialog.PadLogoLoadingDialog
import com.topstack.kilonotes.pad.guide.NoteToolPenGraphGuideWindow
import com.topstack.kilonotes.pad.imagecrop.ImageCropDialogFragment
import com.topstack.kilonotes.pad.note.NoteEditorFragment
import com.topstack.kilonotes.pad.note.adapter.NoteToolPictureAdapter
import com.topstack.kilonotes.pad.note.adapter.SelectParagraphStyleWindow
import com.topstack.kilonotes.pad.note.adapter.ToolGraffitiSizeAdapter
import com.topstack.kilonotes.pad.note.adapter.ToolGraffitiStyleAdapter
import com.topstack.kilonotes.pad.note.model.EraseToolType
import com.topstack.kilonotes.pad.note.popupwindow.ColorGuideWindow
import com.topstack.kilonotes.pad.note.popupwindow.EraseToolsWindow
import com.topstack.kilonotes.pad.note.popupwindow.GraffitiToolsWindow
import com.topstack.kilonotes.pad.note.popupwindow.GraphToolsWindow
import com.topstack.kilonotes.pad.note.popupwindow.HighlighterToolsWindow
import com.topstack.kilonotes.pad.note.popupwindow.MoreToolWindow
import com.topstack.kilonotes.pad.note.popupwindow.OverviewActionWindow
import com.topstack.kilonotes.pad.note.popupwindow.PenToolsWindow
import com.topstack.kilonotes.pad.note.popupwindow.SelectColorWindow
import com.topstack.kilonotes.pad.note.popupwindow.SelectPenSizeWindow
import com.topstack.kilonotes.pad.note.popupwindow.SelectTextSizeWindow
import com.topstack.kilonotes.pad.select.FontListWindow
import com.topstack.kilonotes.pad.select.SelectPhotoDialogActivity
import com.topstack.kilonotes.stylus.FunctionSwitchType
import com.topstack.kilonotes.stylus.StylusToolKit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.UUID

class HandWriteSnippetEditFragment : BasicDoodleFragment<FragmentHandWriteSnippetEditBinding>() {
    private lateinit var majorToolViews: List<View>

    companion object {
        const val XUANHU_TAG = "xuanhuTag"
        const val REQUEST_WRITE_EXTERNAL_STORAGE_CODE = 1000
        val toolBarShortLength: Int =
            AppUtils.appContext.resources.getDimensionPixelSize(R.dimen.dp_60)
    }

    override val pickImageRequester = ActivityResultRequester(this, PickImageResultParser()) {
        if (it?.uri != null) {
            EditEvent.sendEditPictureUsage("toolbar")
            editorModeViewModel.switchMode(InputMode.IMAGE)
            insertImageElement(it.uri, it.alpha, editorViewModel.selectedInsertableBitmap != null) {
                editorViewModel.selectedInsertableBitmap = null
            }
        }
    }

    private val permissionRequester = PermissionRequester(this)

    private var popWindow: OverviewActionWindow? = null

    private var selectPenSizeWindow: SelectPenSizeWindow? = null

    private var selectTextSizeWindow: SelectTextSizeWindow? = null

    private var selectParagraphStyleWindow: SelectParagraphStyleWindow? = null

    private var moreToolWindow: MoreToolWindow? = null

    private var penToolsWindow: PenToolsWindow? = null
    private var penToolsGraphGuideWindow: NoteToolPenGraphGuideWindow? = null

    private var graffitiToolsWindow: GraffitiToolsWindow? = null

    private var highlighterToolsWindow: HighlighterToolsWindow? = null

    private var graphToolsWindow: GraphToolsWindow? = null

    private lateinit var inputManager: InputManager

    private val inputDeviceListener = object : InputManager.InputDeviceListener {

        override fun onInputDeviceAdded(deviceId: Int) {
            updateDoodleDeviceMode()
        }

        override fun onInputDeviceRemoved(deviceId: Int) {
            updateDoodleDeviceMode()
        }

        override fun onInputDeviceChanged(deviceId: Int) {
            updateDoodleDeviceMode()
        }

    }

    private var snippetTranslateWebAnimator: ValueAnimator? = null


    private val adsorptionEdgeViewModel: AdsorptionEdgeViewModel by viewModels()

    private val snippetViewModel: SnippetViewModel by viewModels()
    private val noteSnippetCreateViewModel: NoteSnippetCreateViewModel by navGraphViewModels(R.id.create_snippet_fragment)
    private val importFontViewModel: ImportFontViewModel by viewModels()

    private val keyEventViewModel: KeyEventViewModel by activityViewModels()

    private val saveThumbnailListener: OnPageThumbnailUpdatedListener by lazy(LazyThreadSafetyMode.NONE) {
        OnPageThumbnailUpdatedListener { _, _ ->
            if (!isAdded) return@OnPageThumbnailUpdatedListener
            doodleView.post {
                noteSnippetCreateViewModel.setCurrentDoodleBitmap(
                    null,
                    if (doodleView.modelManager.document.pageCount != 0 && doodleView.modelManager.document.pages[0].draws.isNotEmpty()) {
                        doodleView.thumbnailBitmap
                    } else {
                        null
                    }
                )
                onBackPressed(false)
            }
        }
    }


    private val getFont =
        registerForActivityResult(ActivityResultContracts.OpenDocument()) { result ->
            if (result != null) {
                importFontFile(result)
            }
        }

    private var fontListWindow: FontListWindow? = null

    private var originSnippetPage: Page? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        inputManager = requireContext().getSystemService(Context.INPUT_SERVICE) as InputManager
        inputManager.registerInputDeviceListener(inputDeviceListener, null)
        //TODO 替换埋点
        when (editorModeViewModel.editorMode.value) {
            InputMode.VIEW -> {
                EditEvent.sendEditShow("view")
            }

            else -> {
                EditEvent.sendEditShow("edit")
            }
        }
        TextRecognitionManager.addHighPriorityTaskFinishCallback(onFinishAction)
        TextRecognitionManager.setOnRecognitionTimeOut {
            if (isAdded) {
                ToastUtils.topCenter(
                    requireContext(),
                    AppUtils.getString(R.string.recognition_service_exception)
                )
            }
        }
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun initBinding(inflater: LayoutInflater): FragmentHandWriteSnippetEditBinding {
        return FragmentHandWriteSnippetEditBinding.inflate(inflater)
    }

    override fun onRetrieveDoodleView(binding: FragmentHandWriteSnippetEditBinding): DoodleView {
        return binding.doodle
    }

    private fun showPenToolsWindow() {
        binding.noteMainToolPen.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (binding.noteMainToolPen.windowToken == null) return@post
                if (penToolsWindow == null) {
                    penToolsWindow =
                        PenToolsWindow(
                            requireContext(),
                            penToolWindowIsShowOtherSettingsWindow
                        ).apply {
                            onPenHandwritingParamsChanged = { params ->
                                toolAttributesViewModel.setPenHandwritingParams(params)
                            }
                            otherSettingsWindowListener = { isShowOtherSettingsWindow ->
                                penToolWindowIsShowOtherSettingsWindow = isShowOtherSettingsWindow
                            }
                        }
                    val listener = object : IPatternSwitchListener {
                        override fun onGraphDrawSwitch(isChecked: Boolean) {
                            toolAttributesViewModel.isUsePenPatternRecognition = isChecked
                        }

                        override fun onSwitchMode(mode: InputMode) {
                            editorModeViewModel.switchMode(mode)
                        }

                        override fun onStraightLineSwitch(isChecked: Boolean) {
                            toolAttributesViewModel.isUsePenStraightLine = isChecked
                        }
                    }
                    penToolsWindow!!.setPatternSwitchListener(listener)
                    penToolsWindow!!.setOnDismissListener {
                        penToolWindowIsShowOtherSettingsWindow = false
                        if (Preferences.needShowFirstGraphDrawGuide) {
                            Preferences.needShowFirstGraphDrawGuide = false
                        }
                        noteViewModel.isShowRecognizeWindow.postValue(false)
                    }
                }
                penToolsWindow!!.contentView.measure(
                    View.MeasureSpec.UNSPECIFIED,
                    View.MeasureSpec.UNSPECIFIED
                )
                penToolsWindow!!.show(binding.noteMainToolPen, binding.doodle.inputMode)
                val showGuide = Preferences.needShowFirstGraphDrawGuide
                if (showGuide) {
                    val anchorWindow = penToolsWindow ?: return@post
                    if (isPortraitAndOneThirdScreenOrLikeXiaoMiPad5PortraitHalfScreen()) {
                        anchorWindow.updateScrollView()
                    }
                    penToolsGraphGuideWindow =
                        NoteToolPenGraphGuideWindow(
                            requireContext(),
                            isPortraitAndOneThirdScreenOrLikeXiaoMiPad5PortraitHalfScreen()
                        )
                    penToolsGraphGuideWindow?.contentView?.addOnAttachStateChangeListener(
                        object : View.OnAttachStateChangeListener {
                            override fun onViewAttachedToWindow(v: View) {
                                changeWindowAlpha(ALPHA_TRANSLUCENT, false)
                            }

                            override fun onViewDetachedFromWindow(v: View) {
                                changeWindowAlpha(ALPHA_TRANSPARENT, true)
                                penToolsGraphGuideWindow?.contentView?.removeOnAttachStateChangeListener(
                                    this
                                )
                            }
                        }
                    )
                    penToolsGraphGuideWindow?.setOnDismissListener {
                        Preferences.needShowFirstGraphDrawGuide = false
                    }
                    val needChangeLayout = DimensionUtil.isLandAndOneThirdScreen(
                        requireContext()
                    ) || DimensionUtil.isLandAndHalfScreen(requireContext())
                    val xOffset =
                        if (needChangeLayout) -requireContext().resources.getDimension(
                            R.dimen.dp_30
                        ).toInt() else
                            anchorWindow.contentView.measuredWidth / 2 + requireContext().resources.getDimension(
                                R.dimen.dp_15
                            ).toInt()
                    val yOffset =
                        if (isPortraitAndOneThirdScreenOrLikeXiaoMiPad5PortraitHalfScreen()) requireContext().resources.getDimension(
                            R.dimen.dp_237
                        ).toInt() else requireContext().resources.getDimension(R.dimen.dp_433)
                            .toInt()
                    penToolsGraphGuideWindow?.showGuideWindow(
                        binding.noteMainToolPen,
                        needChangeLayout,
                        xOffset,
                        yOffset
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun showGraffitiToolsWindow() {
        binding.noteMainToolGraffiti.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (binding.noteMainToolGraffiti.windowToken == null) return@post
                if (graffitiToolsWindow == null) {
                    graffitiToolsWindow =
                        GraffitiToolsWindow(requireContext(), object : IGraffitiSwitchListener {
                            override fun onStraightLineSwitch(isChecked: Boolean) {
                                toolAttributesViewModel.isUseGraffitiStraightLine = isChecked
                            }
                        }
                        )

                    graffitiToolsWindow!!.setOnDismissListener {
                        noteViewModel.isShowGraffitiWindow.postValue(false)
                    }
                }

                graffitiToolsWindow!!.contentView.measure(
                    View.MeasureSpec.UNSPECIFIED,
                    View.MeasureSpec.UNSPECIFIED
                )
                val width = graffitiToolsWindow!!.contentView.measuredWidth

                graffitiToolsWindow!!.showAsDropDown(
                    binding.noteMainToolGraffiti,
                    (binding.noteMainToolGraffiti.width - width) / 2,
                    0
                )
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun showHighlighterToolsWindow() {
        binding.noteMainToolHighlighter.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (binding.noteMainToolHighlighter.windowToken == null) return@post
                if (highlighterToolsWindow == null) {
                    highlighterToolsWindow =
                        HighlighterToolsWindow(requireContext(), object :
                            IHighlighterSwitchListener {
                            override fun onStraightLineSwitch(isChecked: Boolean) {
                                toolAttributesViewModel.isUseHighlighterStraightLine = isChecked
                            }
                        }
                        )

                    highlighterToolsWindow!!.setOnDismissListener {
                        noteViewModel.isShowHighlighterWindow.postValue(false)
                    }
                }

                highlighterToolsWindow!!.contentView.measure(
                    View.MeasureSpec.UNSPECIFIED,
                    View.MeasureSpec.UNSPECIFIED
                )
                val width = highlighterToolsWindow!!.contentView.measuredWidth

                highlighterToolsWindow!!.showAsDropDown(
                    binding.noteMainToolHighlighter,
                    (binding.noteMainToolHighlighter.width - width) / 2,
                    0
                )
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun showEraseToolsWindow() {
        binding.noteMainToolEraser.post {
            try {
                if (activity?.isFinishing == true) return@post
                if (binding.noteMainToolEraser.windowToken == null) return@post
                if (eraseToolsWindow == null) {
                    eraseToolsWindow = EraseToolsWindow(
                        requireContext(), true, listOf(
                            EraseToolItem(EraseToolType.ERASE_PEN, UserUsageConfig.canErasePen),
                            EraseToolItem(
                                EraseToolType.ERASE_HIGHLIGHT,
                                UserUsageConfig.canEraseHighlight
                            ),
                            EraseToolItem(
                                EraseToolType.ERASE_GRAFFITI,
                                UserUsageConfig.canEraseGraffiti
                            ),
                            EraseToolItem(
                                EraseToolType.ERASE_TAPE,
                                UserUsageConfig.canEraseTape
                            ),
                            EraseToolItem(EraseToolType.AUTO_ERASE, UserUsageConfig.isUseEraseAuto)
                        )
                    ) { eraseToolItem, isLastSwitch ->
                        if (!isLastSwitch) {
                            when (eraseToolItem.eraseToolType) {
                                EraseToolType.ERASE_PEN -> {
                                    toolAttributesViewModel.setEraserSupportTypePen(
                                        eraseToolItem.isCheck
                                    )
                                }

                                EraseToolType.ERASE_HIGHLIGHT -> {
                                    UserUsageConfig.canEraseHighlight = eraseToolItem.isCheck
                                }

                                EraseToolType.ERASE_GRAFFITI -> {
                                    UserUsageConfig.canEraseGraffiti = eraseToolItem.isCheck
                                }

                                EraseToolType.ERASE_TAPE -> {
                                    UserUsageConfig.canEraseTape = eraseToolItem.isCheck
                                }

                                EraseToolType.AUTO_ERASE -> {
                                    UserUsageConfig.isUseEraseAuto = eraseToolItem.isCheck
                                }
                            }
                            setSupportEraseType()
                        } else {
                            ToastUtils.topCenter(
                                requireContext(),
                                R.string.erase_style_no_close_all
                            )
                        }
                    }

                    eraseToolsWindow?.setOnDismissListener {
                        noteViewModel.isShowEraseToolWindow.postValue(false)
                    }
                }

                eraseToolsWindow?.contentView?.measure(
                    View.MeasureSpec.UNSPECIFIED,
                    View.MeasureSpec.UNSPECIFIED
                )
                val width = eraseToolsWindow!!.contentView.measuredWidth

                eraseToolsWindow?.show(binding.noteMainToolEraser)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun showGraphToolsWindow() {
        binding.noteMainToolGraph.post {
            if (activity?.isFinishing == true || !isAdded) return@post
            if (binding.noteMainToolGraph.windowToken == null) return@post
            if (graphToolsWindow == null) {
                graphToolsWindow = GraphToolsWindow(requireContext()) {
                    graphToolsWindow?.dismiss()
                    checkViewBindingValidity()
                    binding.doodle.insertGraph(it)
                }.apply {
                    setOnDismissListener {
                        noteViewModel.changeGraphWindowStatues(false)
                    }
                }
            }
            graphToolsWindow?.show(binding.noteMainToolGraph)
        }
    }

    override fun initViews() {
        binding.blurView
            .setupWith(binding.root)
            .setFrameClearDrawable(activity?.window?.decorView?.background)
            .setBlurRadius(17F)

        binding.toolBarHide.setOnClickListener(AntiShakeClickListener {
            binding.toolBar.hide {
                adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.HIDE)
            }
        })

        binding.toolBarShow.setOnClickListener(AntiShakeClickListener {
            binding.toolBar.visibility = View.VISIBLE
            binding.toolBar.show {
                adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
            }
        })

        binding.confirm.setOnClickListener(AntiShakeClickListener {
            if (it != null) {
                WindowInsetsUtils.hideSoftKeyBoard(it)
            }
            val modelManager = doodleView.modelManager
            if (doodleView.doodleEditLayer.hasAnyLayerViewEditing()) {
                doodleView.thumbnail.addOnThumbnailUpdateListener(saveThumbnailListener)
                modelManager.resetToolViews(false)
            } else {
                noteSnippetCreateViewModel.setCurrentDoodleBitmap(
                    null,
                    if (doodleView.modelManager.document.pageCount != 0 && doodleView.modelManager.document.pages[0].draws.isNotEmpty()) {
                        doodleView.thumbnailBitmap
                    } else {
                        null
                    }
                )
                onBackPressed(false)
            }
        })

        binding.back.setOnClickListener {
            onBackPressed(true)
        }

        majorToolViews = listOf(
            binding.noteMainToolPen,
            binding.noteMainToolHighlighter,
            binding.noteMainToolEraser,
            binding.noteMainToolLasso,
            binding.noteMainToolPic,
            binding.noteMainToolText,
            binding.noteMainToolGraffiti,
            binding.noteMainToolGraph
        )

        binding.noteMainToolPen.setOnClickListener(this::selectMajorTool)
        binding.noteMainToolHighlighter.setOnClickListener(this::selectMajorTool)
        binding.noteMainToolEraser.setOnClickListener(this::selectMajorTool)
        binding.noteMainToolLasso.setOnClickListener(this::selectMajorTool)
        binding.noteMainToolPic.setOnClickListener(AntiShakeClickListener {
            if (it != null) {
                selectMajorTool(it)
            }
        })
        binding.noteMainToolText.setOnClickListener(this::selectMajorTool)
        binding.noteMainToolGraffiti.setOnClickListener(this::selectMajorTool)
        binding.noteMainToolGraph.setOnClickListener(this::selectMajorTool)

        binding.undo.setOnClickListener {
            onUndoClick(it)
        }

        binding.redo.setOnClickListener {
            onRedoClick(it)
        }
        binding.textStrikethrough.setOnClickListener {
            toolAttributesViewModel.setTextStrikethrough(!it.isSelected)
            EditEvent.sendEditTextBoxStyleClick("deleteline")
        }
        binding.textBold.setOnClickListener {
            toolAttributesViewModel.setTextBold(!it.isSelected)
            EditEvent.sendEditTextBoxStyleClick("bold")
        }
        binding.textUnderline.setOnClickListener {
            toolAttributesViewModel.setTextUnderLine(!it.isSelected)
            EditEvent.sendEditTextBoxStyleClick("underline")
        }
        binding.hideAddPageLayoutCover.setOnClickListener {
            it.visibility = View.GONE
        }
        binding.toolBar.onNewTargetEdgeAvailable =
            onNewTargetEdgeAvailable@{ newEdge, contentWidth, contentHeight ->
                if (!checkViewBindingValidity()) return@onNewTargetEdgeAvailable
                binding.toolBar.adaptViewConstraintToEdge(
                    binding.toolBarSelectionSubstitute,
                    newEdge,
                    NoteEditorFragment.toolBarShortLength
                )
                binding.toolBarSelectionSubstitute.isVisible = true
            }
        val minorToolVerticalConstraints = ConstraintSet()
        minorToolVerticalConstraints.clone(binding.minorToolContent)
        val minorToolHorizontalConstraints = ConstraintSet()
        minorToolHorizontalConstraints.clone(
            requireContext(),
            R.layout.minor_tool_content_horizontal_layout
        )
        binding.toolBar.onNewEdgeSelected =
            { newEdge, contentWidth, contentHeight ->
                if (checkViewBindingValidity()) {
                    binding.toolBarSelectionSubstitute.isVisible = false
                    val recyclerViewOrientation =
                        if (newEdge.isLeftOrRigh()) RecyclerView.VERTICAL else RecyclerView.HORIZONTAL
                    (binding.minorToolRecyclerView.layoutManager as? LinearLayoutManager)?.orientation =
                        recyclerViewOrientation

                    (binding.suppressibleToolRecyclerView.layoutManager as? LinearLayoutManager)?.orientation =
                        recyclerViewOrientation

                    changeShadow(newEdge)

                    binding.toolBar.layoutParams?.let { layoutParams ->
                        layoutParams.width =
                            if (newEdge.isLeftOrRigh()) LayoutParams.WRAP_CONTENT else LayoutParams.MATCH_PARENT
                        layoutParams.height =
                            if (newEdge.isLeftOrRigh()) LayoutParams.MATCH_PARENT else LayoutParams.WRAP_CONTENT

                        binding.toolBar.layoutParams = layoutParams
                    }

                    binding.minorToolContent.layoutParams?.let { layoutParams ->
                        layoutParams.width =
                            if (newEdge.isLeftOrRigh()) LayoutParams.WRAP_CONTENT else LayoutParams.MATCH_PARENT
                        layoutParams.height =
                            if (newEdge.isLeftOrRigh()) LayoutParams.MATCH_PARENT else LayoutParams.WRAP_CONTENT

                        binding.minorToolContent.layoutParams = layoutParams
                    }

                    TransitionManager.beginDelayedTransition(
                        binding.minorToolContent,
                        AutoTransition().apply {
                            duration = 0
                            addListener(object : TransitionListenerAdapter() {
                                override fun onTransitionEnd(transition: Transition) {
                                    binding.minorToolContainer.requestLayout()
                                }
                            })
                        })
                    val suppressibleToolRecyclerViewVisibility =
                        binding.suppressibleToolRecyclerView.visibility
                    if (newEdge.isLeftOrRigh()) {
                        minorToolVerticalConstraints.applyTo(binding.minorToolContent)
                    } else {
                        minorToolHorizontalConstraints.applyTo(binding.minorToolContent)
                    }
                    binding.suppressibleToolRecyclerView.visibility =
                        suppressibleToolRecyclerViewVisibility

                    binding.minorToolContainer.layoutParams?.let { layoutParams ->
                        layoutParams.width =
                            if (newEdge.isLeftOrRigh()) LayoutParams.WRAP_CONTENT else LayoutParams.MATCH_PARENT
                        layoutParams.height =
                            if (newEdge.isLeftOrRigh()) LayoutParams.MATCH_PARENT else LayoutParams.WRAP_CONTENT

                        binding.minorToolContainer.layoutParams = layoutParams
                    }

                    binding.toolBar.run {
                        if (selectEdge == AdsorptionEdgeLayout.EDGE.BOTTOM) {
                            if (binding.textOperationContainer.isVisible) {
                                binding.toolBar.changeBoundaryView(
                                    binding.textOperationContainer.id,
                                    AdsorptionEdgeLayout.EDGE.BOTTOM
                                )
                                binding.toolBar.updatePositionWithAnimation(duration = 0L)
                            }
                        } else {
                            if (binding.toolBar.bottomViewId == binding.textOperationContainer.id) {
                                binding.toolBar.changeBoundaryView(
                                    ResourcesCompat.ID_NULL,
                                    AdsorptionEdgeLayout.EDGE.BOTTOM
                                )
                                binding.toolBar.updatePositionWithAnimation(duration = 0L)
                            }
                        }
                        adsorptionEdgeViewModel.edge = selectEdge
                        adsorptionEdgeViewModel.leftViewId = leftViewId
                        adsorptionEdgeViewModel.topViewId = topViewId
                        adsorptionEdgeViewModel.rightViewId = rightViewId
                        adsorptionEdgeViewModel.bottomViewId = bottomViewId
                    }
                    binding.minorToolRecyclerView.adapter?.notifyDataSetChanged()
                }
            }
        binding.toolBar.contentView = binding.minorToolContent

        binding.translateWebView.onCloseListener = {
            snippetViewModel.changeTranslateViewVisible(false)
        }

        if (DimensionUtil.isLikeXiaoMiPad5PortraitHalfScreen(requireContext()) || DimensionUtil.isLandAndOneThirdScreen(
                requireContext()
            )
        ) {
            val param = binding.translateWebView.layoutParams
            val metrics = DimensionUtil.getScreenDimensions(requireContext())
            param.width = metrics.widthPixels
            binding.translateWebView.layoutParams = param
        }

        binding.back.adjustRtlOrLtrLayout(KiloApp.isLayoutRtl)
    }

    private fun onBackPressed(cancel: Boolean) {
        val originalPage = originSnippetPage
        if (cancel && originalPage != null) {
            NoteRepository.runInNoteOperationScopeAsync {
                currentDoc.pages.firstOrNull()?.let {
                    saveDrawingElementsDeletionOnPageWithFallback(it, it.draws)
                }
            }
            currentDoc.pages.clear()
            currentDoc.pages.add(originalPage)
            NoteRepository.runInNoteOperationScopeAsync {
                savePageWithDrawingElements(originalPage)
            }
        } else {
            NoteRepository.runInNoteOperationScopeAsync {
                currentDoc.pages.firstOrNull()?.let {
                    savePageWithDrawingElements(it)
                }
            }
        }
        doodleView.thumbnail.removeOnThumbnailUpdateListener(saveThumbnailListener)
        findNavController().popBackStack()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (invalidCurrentDoc()) return
        binding.doodle.doodleModeConfig.enterSnippetEditMode()
        requireActivity().onBackPressedDispatcher.addCallback(
            owner = viewLifecycleOwner,
            enabled = true,
        ) {
            onBackPressed(true)
        }

        if (savedInstanceState == null) {
            editorModeViewModel.switchModeImmediately(UserUsageConfig.handWriteSnippetInputMode)
        }
        requestLayoutToFitStatusBar(binding.blurView)
        if (Preferences.needShowFirstGraphDrawGuide) {
            showPenToolsWindow()
        }

        toolAttributesViewModel.highlighterAttributes.observe(viewLifecycleOwner) { attributes ->
            binding.noteMainToolHighlighter.setTintColor(attributes.color)
        }

        toolAttributesViewModel.penAttributes.observe(viewLifecycleOwner) { attrs ->
            binding.noteMainToolPen.setTintColor(attrs.color)
        }

        imageFetchViewModel.sidebarPictures.observe(viewLifecycleOwner) {
            val adapter = binding.minorToolRecyclerView.adapter
            if (adapter is NoteToolPictureAdapter) {
                adapter.setImageList(it)
            }
        }

        snippetViewModel.isShowTranslateView.observe(viewLifecycleOwner) { isShow ->
            if (isShow) {
                if (!binding.translateWebView.isVisible) {
                    binding.translateWebView.post {
                        if (checkViewBindingValidity()) {
                            val animatorArgs =
                                floatArrayOf(0F, -binding.translateWebView.width.toFloat())
                            snippetTranslateWebAnimator = ValueAnimator.ofFloat(
                                *animatorArgs
                            ).apply {
                                addUpdateListener {
                                    if (checkViewBindingValidity()) {
                                        binding.translateWebView.translationX =
                                            if (!KiloApp.isLayoutRtl) it.animatedValue as Float else -(it.animatedValue as Float)
                                    }
                                }
                            }
                            binding.translateWebView.visibility = View.VISIBLE
                            snippetTranslateWebAnimator?.start()
                        }
                    }
                }
            } else {
                if (binding.translateWebView.isVisible) {
                    if (binding.translateWebView.hasFocus()
                        && WindowInsetsUtils.isSoftKeyBoardShow(binding.translateWebView)
                    ) {
                        WindowInsetsUtils.hideSoftKeyBoard(binding.translateWebView)
                    }
                    binding.translateWebView.clearHistory()
                    binding.translateWebView.animate().translationX(-1F)
                        .setListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                binding.translateWebView.visibility = View.INVISIBLE
                            }
                        }).start()
                }
            }
        }

        noteViewModel.isShowRecognizeWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showPenToolsWindow()
                } else {
                    if (penToolsWindow != null && penToolsWindow!!.isShowing) {
                        penToolsWindow!!.dismiss()
                    }
                }
            }
        }

        noteViewModel.isShowGraffitiWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showGraffitiToolsWindow()
                } else {
                    if (graffitiToolsWindow != null && graffitiToolsWindow!!.isShowing) {
                        graffitiToolsWindow!!.dismiss()
                    }
                }
            }
        }

        noteViewModel.isShowHighlighterWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showHighlighterToolsWindow()
                } else {
                    if (highlighterToolsWindow != null && highlighterToolsWindow!!.isShowing) {
                        highlighterToolsWindow!!.dismiss()
                    }
                }
            }
        }

        noteViewModel.isShowGraphWindow.observe(viewLifecycleOwner) { value: Boolean ->
            lifecycleScope.launchWhenResumed {
                if (value) {
                    showGraphToolsWindow()
                } else {
                    graphToolsWindow?.let {
                        if (it.isShowing) {
                            it.dismiss()
                        }
                    }
                }
            }
        }

        binding.minorToolRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.minorToolRecyclerView.itemAnimator = null

        binding.suppressibleToolRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.suppressibleToolRecyclerView.itemAnimator = null

        updateToolPenImage(UserUsageConfig.lastSelectedPenMode)

        editorModeViewModel.editorMode.observe(viewLifecycleOwner) { mode ->
            if (!checkViewBindingValidity()) return@observe
            binding.suppressibleToolRecyclerView.isVisible =
                mode == InputMode.GRAFFITI || mode == InputMode.OUTLINEPEN || mode == InputMode.LINEDRAW
            var maxHeight = when (mode) {
                InputMode.GRAFFITI, InputMode.OUTLINEPEN, InputMode.LINEDRAW -> {
                    resources.getDimensionPixelSize(R.dimen.dp_390)
                }

                InputMode.IMAGE -> {
                    resources.getDimensionPixelSize(R.dimen.dp_450)
                }

                else -> {
                    CustomMaxHeightRecycleView.NONE_MAX_HEIGHT
                }
            }
            if (DimensionUtil.isPortraitAndOneThirdScreen(context)) {
                maxHeight = CustomMaxHeightRecycleView.NONE_MAX_HEIGHT
            }
            binding.minorToolRecyclerView.setMaxHeight(maxHeight)
            binding.toolBar.setEdge(
                adsorptionEdgeViewModel.edge,
            )
            UserUsageConfig.handWriteSnippetInputMode = mode
            binding.minorToolRecyclerView.stopScroll()
            when (mode) {
                InputMode.DRAW -> {
                    updateToolPenImage(mode)
                    EditEvent.sendEditPenClick()
                    updateModeStatus(binding.noteMainToolPen)
                    binding.doodle.inputMode = InputMode.DRAW
                    UserUsageConfig.lastSelectedPenMode = InputMode.DRAW
                    binding.minorToolContainer.visibility = View.VISIBLE
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolPenColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                            ).apply {
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showPenSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultPenPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setPenColor(color)
                                }
                            },
                            ToolPenSizeAdapter(
                                UserUsageConfig.penCurrentSizePosition,
                                UserUsageConfig.penPreferenceSizes,
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                                itemPadding = 0
                            ).apply {
                                doOnSizeChanged {
                                    toolAttributesViewModel.setPenSize(it)
                                }

                                doOnSizePositionChanged {
                                    UserUsageConfig.penCurrentSizePosition = it
                                }

                                doOnPreferredSizesChanged {
                                    UserUsageConfig.penPreferenceSizes = it
                                }

                                doOnCustomSize {
                                    val penAttributes = toolAttributesViewModel.penAttributes.value
                                        ?: PenAttributes.DEFAULT
                                    selectPenSizeWindow =
                                        SelectPenSizeWindow(
                                            requireContext(),
                                            penAttributes.width.getMmValue(),
                                            PenAttributes.MAX_WIDTH.getMmValue(),
                                            PenAttributes.MIN_WIDTH.getMmValue(),
                                            penAttributes.color
                                        ) {
                                            val currentSize = MmSize(it)
                                            toolAttributesViewModel.setPenSize(currentSize)
                                            this.updateSize(size = currentSize)
                                        }.apply {
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.PEN -> {
                    updateToolPenImage(mode)
                    updateModeStatus(binding.noteMainToolPen)
                    binding.doodle.inputMode = InputMode.PEN
                    UserUsageConfig.lastSelectedPenMode = InputMode.PEN
                    binding.minorToolContainer.visibility = View.VISIBLE
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolPenColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                            ).apply {
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showPenSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultPenPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setPenColor(color)
                                }
                            },
                            ToolPenSizeAdapter(
                                UserUsageConfig.penCurrentSizePosition,
                                UserUsageConfig.penPreferenceSizes,
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                                itemPadding = 0
                            ).apply {
                                doOnSizeChanged {
                                    toolAttributesViewModel.setPenSize(it)
                                }

                                doOnSizePositionChanged {
                                    UserUsageConfig.penCurrentSizePosition = it
                                }

                                doOnPreferredSizesChanged {
                                    UserUsageConfig.penPreferenceSizes = it
                                }

                                doOnCustomSize {
                                    val penAttributes = toolAttributesViewModel.penAttributes.value
                                        ?: PenAttributes.DEFAULT
                                    selectPenSizeWindow =
                                        SelectPenSizeWindow(
                                            requireContext(),
                                            penAttributes.width.getMmValue(),
                                            PenAttributes.MAX_WIDTH.getMmValue(),
                                            PenAttributes.MIN_WIDTH.getMmValue(),
                                            penAttributes.color
                                        ) {
                                            val currentSize = MmSize(it)
                                            toolAttributesViewModel.setPenSize(currentSize)
                                            this.updateSize(size = currentSize)
                                        }.apply {
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.PAINTBRUSH -> {
                    updateToolPenImage(mode)
                    updateModeStatus(binding.noteMainToolPen)
                    binding.doodle.inputMode = InputMode.PAINTBRUSH
                    UserUsageConfig.lastSelectedPenMode = InputMode.PAINTBRUSH
                    binding.minorToolContainer.visibility = View.VISIBLE
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolPenColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                            ).apply {
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showPenSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultPenPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setPenColor(color)
                                }
                            },
                            ToolPenSizeAdapter(
                                UserUsageConfig.penCurrentSizePosition,
                                UserUsageConfig.penPreferenceSizes,
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                                itemPadding = 0
                            ).apply {
                                doOnSizeChanged {
                                    toolAttributesViewModel.setPenSize(it)
                                }

                                doOnSizePositionChanged {
                                    UserUsageConfig.penCurrentSizePosition = it
                                }

                                doOnPreferredSizesChanged {
                                    UserUsageConfig.penPreferenceSizes = it
                                }

                                doOnCustomSize {
                                    val penAttributes = toolAttributesViewModel.penAttributes.value
                                        ?: PenAttributes.DEFAULT
                                    selectPenSizeWindow =
                                        SelectPenSizeWindow(
                                            requireContext(),
                                            penAttributes.width.getMmValue(),
                                            PenAttributes.MAX_WIDTH.getMmValue(),
                                            PenAttributes.MIN_WIDTH.getMmValue(),
                                            penAttributes.color
                                        ) {
                                            val currentSize = MmSize(it)
                                            toolAttributesViewModel.setPenSize(currentSize)
                                            this.updateSize(size = currentSize)
                                        }.apply {
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.HIGHLIGHTER -> {
                    EditEvent.sendEditHighlighterClick()
                    updateModeStatus(binding.noteMainToolHighlighter)
                    binding.doodle.inputMode = InputMode.HIGHLIGHTER
                    binding.minorToolContainer.visibility = View.VISIBLE
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolHighlighterColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                            ).apply {
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showHighlighterSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultHighlighterPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setHighlighterColor(color)
                                }
                            },
                            ToolHighlighterSizeAdapter(
                                requireContext(),
                                UserUsageConfig.highlighterCurrentSizePosition,
                                UserUsageConfig.highlighterPreferenceSizes,
                                resources.getDimensionPixelSize(R.dimen.dp_60)
                            ).apply {
                                doOnSizeChanged {
                                    toolAttributesViewModel.setHighlighterSize(it)
                                }

                                doOnSizePositionChanged {
                                    UserUsageConfig.highlighterCurrentSizePosition = it
                                }

                                doOnPreferredSizesChanged {
                                    UserUsageConfig.highlighterPreferenceSizes = it
                                }

                                doOnCustomSize {
                                    val highlighterAttributes =
                                        toolAttributesViewModel.highlighterAttributes.value
                                            ?: HighlighterAttributes.DEFAULT
                                    selectPenSizeWindow =
                                        SelectPenSizeWindow(
                                            requireContext(),
                                            highlighterAttributes.width.getMmValue(),
                                            HighlighterAttributes.MAX_WIDTH.getMmValue(),
                                            HighlighterAttributes.MIN_WIDTH.getMmValue(),
                                            highlighterAttributes.color
                                        ) {
                                            val currentSize = MmSize(it)
                                            toolAttributesViewModel.setHighlighterSize(currentSize)
                                            this.updateSize(size = currentSize)
                                        }.apply {
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.ERASER -> {
                    EditEvent.sendEditEraserClick()
                    updateModeStatus(binding.noteMainToolEraser)
                    binding.doodle.eraseType = VisualStrokeErase.EraseType.Erase_Whole
                    binding.doodle.inputMode = InputMode.ERASER
                    binding.minorToolContainer.visibility = View.VISIBLE

                    val firstItem = NoteToolEraserAdapter.EraserItem(
                        resources.getDimensionPixelSize(R.dimen.dp_60),
                        resources.getDimensionPixelSize(R.dimen.dp_6)
                    )
                    val secondItem = NoteToolEraserAdapter.EraserItem(
                        resources.getDimensionPixelSize(R.dimen.dp_60),
                        resources.getDimensionPixelSize(R.dimen.dp_12)
                    )
                    val thirdItem = NoteToolEraserAdapter.EraserItem(
                        resources.getDimensionPixelSize(R.dimen.dp_60),
                        resources.getDimensionPixelSize(R.dimen.dp_24)
                    )
                    binding.minorToolRecyclerView.adapter =
                        NoteToolEraserAdapter(
                            requireContext(),
                            firstItem,
                            secondItem,
                            thirdItem,
                            false,
                            toolAttributesViewModel.eraserAttributes
                        )
                }

                InputMode.LASSO -> {
                    EditEvent.sendEditLassoToolMode()
                    updateModeStatus(binding.noteMainToolLasso)
                    binding.doodle.inputMode = InputMode.LASSO
                    binding.minorToolContainer.visibility = View.VISIBLE
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            NoteToolLassoStyleAdapter(
                                resources.getDimensionPixelSize(R.dimen.dp_60),
                                UserUsageConfig.lassoStyle
                            ) { newStyle ->
                                toolAttributesViewModel.setLassoStyle(newStyle)
                            },
                            ToolDividerAdapter(),
                            NoteToolLassoAdapter(
                                requireContext(),
                                resources.getDimensionPixelSize(R.dimen.dp_60),
                                toolAttributesViewModel.lassoAttributes,
                                listOf(
                                    R.drawable.note_main_sidebar_lasso_stroke_normal,
                                    R.drawable.note_main_sidebar_lasso_pic,
                                    R.drawable.note_main_sidebar_lasso_text,
                                    R.drawable.note_main_sidebar_lasso_graffiti,
                                    R.drawable.note_main_sidebar_lasso_high_lighter,
                                    R.drawable.note_main_sidebar_lasso_graph
                                )
                            )
                        )
                }

                InputMode.IMAGE -> {
                    binding.doodle.inputMode = InputMode.IMAGE
                    binding.minorToolContainer.visibility = View.VISIBLE
                    updateModeStatus(binding.noteMainToolPic)

                    val noteToolPictureAdapter = NoteToolPictureAdapter(
                        requireContext(),
                        resources.getDimensionPixelSize(R.dimen.dp_60)
                    ) { path ->
                        insertImageElement(path)
                        EditEvent.sendEditPictureUsage("sidebar")
                    }
                    binding.minorToolRecyclerView.adapter = noteToolPictureAdapter
                    EditEvent.sendEditPictureClick()
                    val needRequestPermission =
                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                            Manifest.permission.READ_EXTERNAL_STORAGE
                        } else {
                            Manifest.permission.READ_MEDIA_IMAGES
                        }
                    if (PermissionRequester.checkPermissionState(
                            this,
                            needRequestPermission
                        ) == PermissionRequester.PermissionState.PERMISSION_GRANTED
                    ) {
                        imageFetchViewModel.loadSidebarPictures()
                    }
                }

                InputMode.TEXT -> {
                    EditEvent.sendEditTextInputClick()
                    binding.doodle.inputMode = InputMode.TEXT
                    updateModeStatus(binding.noteMainToolText)
                    binding.minorToolContainer.visibility = View.VISIBLE
                    binding.minorToolRecyclerView.adapter =
                        ConcatAdapter(
                            ToolTextColorAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                            ).apply {
                                colorClickAction = { _, _, _ ->
                                    EditEvent.sendEditTextColorClick()
                                }
                                colorReClickAction = { view, _, color ->
                                    colorAnchorView = view
                                    showTextSelectColorWindow(color)
                                }
                                setColorChangedActionAndGetInitialColor { color, view ->
                                    if (UserUsageConfig.isFirstChangeColor && color != getDefaultPenPreferenceColorList()[0]) {
                                        UserUsageConfig.isFirstChangeColor = false
                                        showMoreColorWindow(view)
                                    }
                                    toolAttributesViewModel.setTextColor(color)
                                }
                            },
                            ToolTextSizeAdapter(
                                requireContext(),
                                toolAttributesViewModel.textAttributes.value!!.size.getPtValue()
                                    .toInt(),
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                                textSize = resources.getDimensionPixelSize(R.dimen.dp_40),
                                itemPadding = 0
                            ).apply {
                                onSizeChangeAction = {
                                    toolAttributesViewModel.setTextSize(PtSize(it.toFloat()))
                                }
                                customSizeAction = {
                                    selectTextSizeWindow = SelectTextSizeWindow(
                                        requireContext(),
                                        toolAttributesViewModel.textAttributes.value!!.size.getPtValue()
                                            .toInt()
                                    ) {
                                        this.updateTextSize(it)
                                    }.apply {
                                        showAsBubble(
                                            it,
                                            adsorptionEdgeViewModel.getBubbleOrientation()
                                        )
                                    }
                                }
                            },
                            ToolTextFontAdapter(
                                toolAttributesViewModel.fontAttributes.value?.fontInfo
                                    ?: UserUsageConfig.fontAttributes.fontInfo
                            ) { adapter, view ->
                                showFontListWindow(adapter, view)
                            },
                            ToolTextParagraphAdapter(
                                itemSize = resources.getDimensionPixelSize(R.dimen.dp_60)
                            ).apply {
                                doOnCustomParagraphAction {
                                    EditEvent.sendEditTextParagraphClick()
                                    selectParagraphStyleWindow =
                                        SelectParagraphStyleWindow(
                                            requireContext(),
                                            toolAttributesViewModel.textAttributes.value!!.textGravity
                                        ) {
                                            toolAttributesViewModel.setTextGravity(it)
                                            this.updateTextParagraphIcon(it)
                                        }.apply {
                                            EditEvent.sendEditTextParagraphShow()
                                            inputMethodMode = PopupWindow.INPUT_METHOD_NOT_NEEDED
                                            showAsBubble(
                                                it,
                                                adsorptionEdgeViewModel.getBubbleOrientation()
                                            )
                                        }
                                }
                            }
                        )
                }

                InputMode.GRAFFITI, InputMode.OUTLINEPEN, InputMode.LINEDRAW -> {
                    val graffitiPatternStyleList = getDefaultGraffitiPatternPreferenceStyleList()
                    val graffitiOutlinePenStyleList =
                        getDefaultGraffitiOutlinePenPreferenceStyleList()
                    val graffitiLineDrawPenStyleList =
                        getDefaultGraffitiLineDrawingPenPreferenceStyleList()
                    updateModeStatus(binding.noteMainToolGraffiti)
                    when (UserUsageConfig.graffitiCurrentToolTypePosition) {
                        GraffitiToolType.PATTERN.ordinal -> binding.doodle.inputMode =
                            InputMode.GRAFFITI

                        GraffitiToolType.OUTLINEPEN.ordinal -> binding.doodle.inputMode =
                            InputMode.OUTLINEPEN

                        GraffitiToolType.LINEDRAWPEN.ordinal -> binding.doodle.inputMode =
                            InputMode.LINEDRAW
                    }
                    binding.minorToolContainer.visibility = View.VISIBLE
                    binding.minorToolRecyclerView.adapter = ConcatAdapter(
                        ToolGraffitiStyleAdapter(
                            UserUsageConfig.graffitiCurrentStylePosition,
                            graffitiPatternStyleList,
                            outlinePenStyleList = null,
                            lineDrawPenStyleList = null,
                            itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                            GraffitiToolType.PATTERN.ordinal
                        ).apply {
                            setStyleChangedActionAndGetInitialStyle { styleIndex ->
                                if (UserUsageConfig.graffitiCurrentToolTypePosition == GraffitiToolType.PATTERN.ordinal) {
                                    binding.doodle.inputMode = InputMode.GRAFFITI
                                    toolAttributesViewModel.setGraffitiPatternStyle(styleIndex)
                                    (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.forEach {
                                        if (it is ToolGraffitiStyleAdapter) {
                                            it.clearCurrentSelectedState()
                                        }
                                    }
                                }
                            }
                        },
                        ToolGraffitiStyleAdapter(
                            UserUsageConfig.graffitiCurrentStylePosition,
                            patternStyleList = null,
                            graffitiOutlinePenStyleList,
                            lineDrawPenStyleList = null,
                            itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                            GraffitiToolType.OUTLINEPEN.ordinal
                        ).apply {
                            setStyleChangedActionAndGetInitialStyle { styleIndex ->
                                if (UserUsageConfig.graffitiCurrentToolTypePosition == GraffitiToolType.OUTLINEPEN.ordinal) {
                                    binding.doodle.inputMode = InputMode.OUTLINEPEN
                                    toolAttributesViewModel.setGraffitiOutlineStyle(styleIndex)
                                    (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.forEach {
                                        if (it is ToolGraffitiStyleAdapter) {
                                            it.clearCurrentSelectedState()
                                        }
                                    }
                                }
                            }
                        },
                        ToolGraffitiStyleAdapter(
                            UserUsageConfig.graffitiCurrentStylePosition,
                            patternStyleList = null,
                            outlinePenStyleList = null,
                            graffitiLineDrawPenStyleList,
                            itemSize = resources.getDimensionPixelSize(R.dimen.dp_60),
                            GraffitiToolType.LINEDRAWPEN.ordinal
                        ).apply {
                            setStyleChangedActionAndGetInitialStyle { styleIndex ->
                                if (UserUsageConfig.graffitiCurrentToolTypePosition == GraffitiToolType.LINEDRAWPEN.ordinal) {
                                    binding.doodle.inputMode = InputMode.LINEDRAW
                                    toolAttributesViewModel.setLineDrawPenStyle(styleIndex)
                                    (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.forEach {
                                        if (it is ToolGraffitiStyleAdapter) {
                                            it.clearCurrentSelectedState()
                                        }
                                    }
                                }
                            }
                        }
                    )
                    binding.suppressibleToolRecyclerView.adapter = ToolGraffitiSizeAdapter(
                        requireContext(),
                        UserUsageConfig.graffitiCurrentSizePosition,
                        UserUsageConfig.graffitiPreferenceSizes,
                        resources.getDimensionPixelSize(R.dimen.dp_60)
                    ).apply {
                        doOnSizeChanged {
                            EditEvent.sendGraffitipenThickness(it.toString())
                            toolAttributesViewModel.setGraffitiSize(it)
                        }

                        doOnSizePositionChanged {
                            UserUsageConfig.graffitiCurrentSizePosition = it
                        }

                        doOnPreferredSizesChanged {
                            UserUsageConfig.graffitiPreferenceSizes = it
                        }

                        doOnCustomSize {
                            val graffitiAttributes =
                                toolAttributesViewModel.graffitiPatternAttributes.value
                                    ?: GraffitiAttributes.DEFAULT
                            selectPenSizeWindow =
                                SelectPenSizeWindow(
                                    requireContext(),
                                    graffitiAttributes.width.getMmValue(),
                                    GraffitiAttributes.MAX_WIDTH.getMmValue(),
                                    GraffitiAttributes.MIN_WIDTH.getMmValue(),
                                    Color.BLACK
                                ) {
                                    val currentSize = MmSize(it)
                                    toolAttributesViewModel.setGraffitiSize(currentSize)
                                    this.updateSize(size = currentSize)
                                }.apply {
                                    showAsBubble(
                                        it,
                                        adsorptionEdgeViewModel.getBubbleOrientation()
                                    )
                                    setOnDismissListener {
                                        it.isSelected = false
                                    }
                                }
                        }
                    }
                    when (UserUsageConfig.graffitiCurrentToolTypePosition) {
                        GraffitiToolType.PATTERN.ordinal -> binding.minorToolRecyclerView.scrollToPosition(
                            UserUsageConfig.graffitiCurrentStylePosition
                        )

                        GraffitiToolType.OUTLINEPEN.ordinal -> binding.minorToolRecyclerView.scrollToPosition(
                            UserUsageConfig.graffitiCurrentStylePosition + graffitiPatternStyleList.size
                        )

                        GraffitiToolType.LINEDRAWPEN.ordinal -> binding.minorToolRecyclerView.scrollToPosition(
                            UserUsageConfig.graffitiCurrentStylePosition + graffitiPatternStyleList.size + graffitiOutlinePenStyleList.size
                        )
                    }

                }

                InputMode.GRAPH -> {
                    binding.doodle.inputMode = InputMode.GRAPH
                    updateModeStatus(binding.noteMainToolGraph)
                    binding.minorToolContainer.visibility = View.VISIBLE
                    binding.minorToolRecyclerView.adapter = null
                }


                else -> {}
            }
        }

        setToolContainerHeightWhenMultiWindow()


        toolAttributesViewModel.textAttributes.observe(viewLifecycleOwner) {
            binding.textStrikethrough.isSelected = it.isStrikethrough
            binding.textBold.isSelected = it.isBold
            binding.textUnderline.isSelected = it.isUnderLine
        }
        // init
        updateDoodleDeviceMode()

        adsorptionEdgeViewModel.toolBarState.observe(viewLifecycleOwner) {
            binding.toolBar.post {
                if (!checkViewBindingValidity()) return@post
                when (it) {
                    AdsorptionEdgeViewModel.ToolBarState.SHOW -> {
                        binding.toolBar.visibility = View.VISIBLE
                        binding.toolBarShow.visibility = View.INVISIBLE
                    }

                    AdsorptionEdgeViewModel.ToolBarState.HIDE -> {
                        binding.toolBar.visibility = View.INVISIBLE
                        binding.toolBarShow.visibility = View.VISIBLE
                    }

                    AdsorptionEdgeViewModel.ToolBarState.NONE -> {
                        binding.toolBar.visibility = View.INVISIBLE
                        binding.toolBarShow.visibility = View.INVISIBLE
                    }
                }
            }
        }

        keyEventViewModel.keyEvent.observe(viewLifecycleOwner) { keyEvent ->
            StylusToolKit.obtainStylusFunctionSwitch()?.onKeyEvent(keyEvent)
        }
    }

    private fun importFontFile(uri: Uri) {
        importFontViewModel.importFile(uri) { fontImportResult ->
            lifecycleScope.launch(Dispatchers.IO) {
                val fontGroupInfo = FontManager.getFontGroupInfo()
                withContext(Dispatchers.Main) {
                    when (fontImportResult) {
                        is FontImportSuccess -> {
                            ToastUtils.topCenter(
                                requireContext(),
                                R.string.font_file_import_success_tip
                            )
                        }

                        is FontImportFail -> {
                            ToastUtils.topCenter(
                                requireContext(),
                                R.string.font_file_import_fail_tip
                            )
                        }

                        is FontImportError -> {
                            if (fontImportResult.errorCode == FileImporter.ERROR_RESTORE_ZIP_FILE_NO_FONT) {
                                ToastUtils.topCenter(
                                    requireContext(),
                                    R.string.font_file_import_fail_no_ttf
                                )
                            } else {
                                ToastUtils.topCenter(
                                    requireContext(),
                                    R.string.font_import_font_error_tip
                                )
                            }
                        }
                    }
                    fontListWindow?.refreshFontList(fontGroupInfo)
                }
            }
        }
    }

    private fun showFontListWindow(adapter: ToolTextFontAdapter, view: View) {
        lifecycleScope.launch(Dispatchers.IO) {
            val fontGroupInfo = FontManager.getFontGroupInfo()
            withContext(Dispatchers.Main) {
                fontListWindow = FontListWindow(
                    requireContext(),
                    fontGroupInfo
                ).apply {
                    inputMethodMode = PopupWindow.INPUT_METHOD_NOT_NEEDED
                    onFontChangeAction = {
                        adapter.setFontInfo(it)
                        adapter.notifyDataSetChanged()
                        dismiss()
                        toolAttributesViewModel.setFontInfo(it)
                        lifecycleScope.launch(Dispatchers.IO) {
                            it.lastUseTime = SystemClock.elapsedRealtime()
                            HandbookDatabase.getDatabase().fontDao()
                                .insertFont(it)
                        }
                    }
                    fontDown = { url, listener ->
                        lifecycleScope.launch(Dispatchers.IO) {
                            FontManager.downloadFont(url, listener)
                        }
                    }
                    fontAdd = {
                        try {
                            getFont.launch(arrayOf(MimeType.TTF.mime, MimeType.ZIP.mime))
                        } catch (e: ActivityNotFoundException) {

                        }
                    }
                }
                fontListWindow?.showAsBubble(
                    view,
                    view is VerticalTextView,
                    adsorptionEdgeViewModel.getBubbleOrientation()
                )
            }
        }
    }


    override fun getCurrentNavigationId(): Int {
        return R.id.hand_write_snippet_edit
    }

    var onSnippetListenerSetFinishedAction: (() -> Unit)? = null

    override fun onResume() {
        super.onResume()
        if (invalidCurrentDoc()) return
        StylusToolKit.obtainStylusFunctionSwitch()
            ?.registerFunctionSwitchCallback(requireContext(), this) { switchType ->
                val handled = when (switchType) {
                    FunctionSwitchType.CURRENT_TO_ERASE -> {
                        editorModeViewModel.switchBetweenCurrentAndEraserMode()
                    }

                    FunctionSwitchType.CURRENT_TO_LAST -> {
                        editorModeViewModel.switchModeToLast()
                    }

                    else -> false
                }

                if (handled) {
                    binding.toolBar.post {
                        val inputMode = editorModeViewModel.editorMode.value
                        if (inputMode == InputMode.VIEW) {
                            adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.NONE)
                        } else {
                            adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
                            binding.toolBar.show {}
                        }
                    }
                }

                handled
            }

    }

    private var translateLoadingDialog: PadLogoLoadingDialog? = null
    private var translateJob: Job? = null

    override fun onPause() {
        super.onPause()
        StylusToolKit.obtainStylusFunctionSwitch()
            ?.unregisterFunctionSwitchCallback(requireContext(), this)
    }

    private fun showMoreColorWindow(anchorView: View? = null) {
        binding.toolBar.post {
            if (!isAdded) return@post
            val guideWindow = ColorGuideWindow(requireContext()).apply {
                toolbarEdge = adsorptionEdgeViewModel.edge
            }
            guideWindow.show(if (anchorView == null) binding.toolBar else anchorView)
        }
    }

    private fun updateDoodleDeviceMode() {
        binding.doodle.configDeviceMode(UserUsageConfig.isSupportBluetoothPen)
    }

    private fun isPenConnected(): Boolean {
        inputManager.inputDeviceIds.forEach {
            val inputDevice = inputManager.getInputDevice(it)
            if (inputDevice != null && isPenDevice(inputDevice)) {
                return true
            }
        }
        return false
    }

    override fun getToolColorAdapter(): ToolColorAdapter? {
        return (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.find {
            it is ToolColorAdapter
        } as? ToolColorAdapter
    }

    private fun getToolTextSizeAdapter(): ToolTextSizeAdapter? {
        return (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.find {
            it is ToolTextSizeAdapter
        } as? ToolTextSizeAdapter
    }

    private fun getToolTextParagraphStyleAdapter(): ToolTextParagraphAdapter? {
        return binding.suppressibleToolRecyclerView.adapter as? ToolTextParagraphAdapter
    }

    private fun getToolTextFontAdapter(): ToolTextFontAdapter? {
        return (binding.minorToolRecyclerView.adapter as? ConcatAdapter)?.adapters?.find {
            it is ToolTextFontAdapter
        } as? ToolTextFontAdapter
    }

    override fun createWindowColorPickView(): WindowColorPickView {
        return WindowColorPickView(
            requireContext(),
            resources.getDimensionPixelSize(R.dimen.dp_40).toFloat(),
            resources.getDimensionPixelSize(R.dimen.dp_20).toFloat(),
            resources.getDimensionPixelSize(R.dimen.dp_4).toFloat(),
            Color.BLACK,
            ResourcesCompat.getDrawable(
                resources,
                R.drawable.pick_color_icon_indicator,
                null
            ),
            ResourcesCompat.getDrawable(
                resources,
                R.drawable.pick_color_icon_confirm,
                null
            ),
            ResourcesCompat.getDrawable(resources, R.drawable.pick_color_icon_cancel, null),
        )
    }

    override fun createSelectColorWindow(
        color: Int,
        colorList: List<ColorWindowItem>,
        type: Int,
    ): BaseSelectColorWindow {
        return SelectColorWindow(
            requireContext(),
            if (DimensionUtil.getOrientation(requireContext()) == Configuration.ORIENTATION_PORTRAIT
                && DimensionUtil.getMultiWindowRatio(requireContext()) <= DimensionUtil.ONE_THIRD_SCREEN_LIMIT
            ) {
                SelectColorWindow.LAYOUT_HORIZONTAL
            } else {
                SelectColorWindow.LAYOUT_VERTICAL
            },
            color,
            colorList.filter {
                it.type.ordinal == ColorWindowItemType.PRESET_COLOR.ordinal
            },
            Color.alpha(color)
        )
    }

    override fun onPenColorSelected(color: ColorWindowItem) {
        super.onPenColorSelected(color)
        getToolColorAdapter()?.setCurrentColor(color.res)
        selectColorWindow?.dismiss()
    }

    override fun onHighlighterColorSelected(color: ColorWindowItem) {
        super.onHighlighterColorSelected(color)
        getToolColorAdapter()?.setCurrentColor(color.res)
        selectColorWindow?.dismiss()
    }

    override fun onTextColorSelected(color: ColorWindowItem) {
        super.onTextColorSelected(color)
        getToolColorAdapter()?.setCurrentColor(color.res)
        selectColorWindow?.dismiss()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (checkViewBindingValidity()) {
            binding.translateWebView.saveWebViewState(outState)
        }
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        if (savedInstanceState != null) {
            binding.translateWebView.restoreWebViewState(savedInstanceState)
        }
    }

    private fun changeShadow(edge: AdsorptionEdgeLayout.EDGE) {
        if (!checkViewBindingValidity()) return
        binding.minorToolContainer.apply {
            setShadowOffsetX(0F)
            when (edge) {
                AdsorptionEdgeLayout.EDGE.LEFT -> {
                    setShadowHiddenLeft(true)
                    setShadowHiddenTop(true)
                    setShadowHiddenRight(false)
                    setShadowHiddenBottom(true)
                }

                AdsorptionEdgeLayout.EDGE.BOTTOM -> {
                    setShadowHiddenLeft(true)
                    setShadowHiddenTop(false)
                    setShadowHiddenRight(true)
                    setShadowHiddenBottom(true)
                }

                AdsorptionEdgeLayout.EDGE.TOP -> {
                    setShadowHiddenLeft(true)
                    setShadowHiddenTop(true)
                    setShadowHiddenRight(true)
                    setShadowHiddenBottom(false)
                }

                AdsorptionEdgeLayout.EDGE.RIGHT -> {
                    setShadowHiddenLeft(false)
                    setShadowHiddenTop(true)
                    setShadowHiddenRight(true)
                    setShadowHiddenBottom(true)
                }
            }
        }
    }

    override fun onDestroyView() {
        doodleView.modelManager.getInsertableObjects().forEach { insertableObject ->
            insertableObject.clearPropertyChangedListener()
        }
        binding.translateWebView.destroyWebView()
        super.onDestroyView()
        inputManager.unregisterInputDeviceListener(inputDeviceListener)
        //为了popWindow销毁时，不改变vm中的状态
        popWindow?.setOnDismissListener { }
        popWindow?.dismiss()
        selectPenSizeWindow?.dismiss()
        selectPenSizeWindow = null
        selectTextSizeWindow?.dismiss()
        selectTextSizeWindow = null
        selectParagraphStyleWindow?.dismiss()
        selectParagraphStyleWindow = null
        moreToolWindow?.setOnDismissListener { }
        moreToolWindow?.dismiss()
        moreToolWindow = null
        penToolsWindow?.setOnDismissListener { }
        penToolsWindow?.dismiss()
        penToolsWindow = null
        graffitiToolsWindow?.setOnDismissListener { }
        graffitiToolsWindow?.dismiss()
        graffitiToolsWindow = null
        highlighterToolsWindow?.setOnDismissListener { }
        highlighterToolsWindow?.dismiss()
        highlighterToolsWindow = null
        graphToolsWindow?.setOnDismissListener { }
        graphToolsWindow?.dismiss()
        graphToolsWindow = null
        fontListWindow?.dismiss()
        fontListWindow = null
        TextRecognitionManager.removeHighPriorityTaskFinishCallback(onFinishAction)
        TextRecognitionManager.removeRecognitionTimeOutCallback()
    }

    override fun showCropDialog(insertableBitmap: InsertableBitmap?, cropType: CropType) {
        if (!isAdded) return
        ImageCallEvent.sendPicturesTransparencyClick()
        ImageCropDialogFragment().apply {
            this.insertableBitmap = insertableBitmap
            setOnCropCompleteListener(this@HandWriteSnippetEditFragment)
            val bundle = Bundle()
            bundle.putString(BaseImageCropDialogFragment.CROP_TYPE_NAME, cropType.name)
            arguments = bundle
        }.show(parentFragmentManager, BaseImageCropDialogFragment.TAG)

        noteViewModel.changeThumbnailListViewState(NoteViewModel.ThumbnailListViewState.HIDDEN)
    }

    private fun updateModeStatus(view: View) = majorToolViews.forEach {
        it.isSelected = (view == it)
    }

    private fun selectMajorTool(view: View) {
        when (view) {
            binding.noteMainToolPen -> {
                if (editorModeViewModel.editorMode.value == InputMode.DRAW
                    || editorModeViewModel.editorMode.value == InputMode.PEN
                    || editorModeViewModel.editorMode.value == InputMode.PAINTBRUSH
                ) {
                    resetAllToolViews()
                    noteViewModel.isShowRecognizeWindow.value = true
                } else {
                    changeToolbar(UserUsageConfig.lastSelectedPenMode)
                }
            }

            binding.noteMainToolHighlighter -> {
                if (editorModeViewModel.editorMode.value == InputMode.HIGHLIGHTER) {
                    resetAllToolViews()
                    noteViewModel.isShowHighlighterWindow.value = true
                } else {
                    changeToolbar(InputMode.HIGHLIGHTER)
                }
            }

            binding.noteMainToolEraser -> {
                if (editorModeViewModel.editorMode.value == InputMode.ERASER) {
                    resetAllToolViews()
                    noteViewModel.isShowEraseToolWindow.value = true
                } else {
                    changeToolbar(InputMode.ERASER)
                }
            }

            binding.noteMainToolLasso -> {
                changeToolbar(InputMode.LASSO)
            }

            binding.noteMainToolPic -> {
                if (editorModeViewModel.editorMode.value == InputMode.IMAGE) {
                    editorViewModel.selectedInsertableBitmap = null
                    checkAndPickImage()
                } else {
                    checkImagePermission { state ->
                        if (state == PermissionRequester.PermissionState.PERMISSION_GRANTED) {
                            imageFetchViewModel.loadSidebarPictures()
                        }
                    }
                    changeToolbar(InputMode.IMAGE)
                }
            }

            binding.noteMainToolText -> {
                changeToolbar(InputMode.TEXT)
            }

            binding.noteMainToolGraffiti -> {
                EditEvent.sendEditGraffitipenClick()
                if (editorModeViewModel.editorMode.value == InputMode.GRAFFITI) {
                    resetAllToolViews()
                    noteViewModel.isShowGraffitiWindow.value = true
                } else {
                    changeToolbar(InputMode.GRAFFITI)
                }
            }

            binding.noteMainToolGraph -> {
                if (editorModeViewModel.editorMode.value == InputMode.GRAPH) {
                    resetAllToolViews()
                } else {
                    changeToolbar(InputMode.GRAPH)
                }
                noteViewModel.changeGraphWindowStatues(true)
            }

            else -> {
            }
        }
    }

    private fun changeToolbar(inputMode: InputMode) {
        if (inputMode != editorModeViewModel.editorMode.value) {
            if (!binding.toolBar.isVisible) {
                if (inputMode == InputMode.VIEW) {
                    adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.NONE)
                    editorModeViewModel.switchMode(inputMode)
                } else {
                    adsorptionEdgeViewModel.changeToolBarVisibility(AdsorptionEdgeViewModel.ToolBarState.SHOW)
                    editorModeViewModel.switchMode(inputMode)
                    binding.toolBar.show {}
                }
            } else {
                editorModeViewModel.switchMode(inputMode)
            }
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun setToolContainerHeightWhenMultiWindow() {
        //改变左侧工具栏高度
        val minorToolContainerLayoutParams =
            binding.minorToolContainer.layoutParams as ConstraintLayout.LayoutParams
        val maxHeight = minorToolContainerLayoutParams.matchConstraintMaxHeight
        val appHeight = DimensionUtil.getScreenDimensions(requireContext()).heightPixels
        //因为上方工具栏高度是定值，所以可以这么写
        val blurViewHeight = binding.blurView.layoutParams.height
        val currentDoodleHeight = appHeight - blurViewHeight
        if (maxHeight > currentDoodleHeight) {
            minorToolContainerLayoutParams.matchConstraintMaxHeight = currentDoodleHeight
            binding.minorToolContainer.layoutParams = minorToolContainerLayoutParams
        }
        setMainToolContainerLayout()
    }

    private fun setMainToolContainerLayout() {
        binding.mainToolContainer.post {
            if (!checkViewBindingValidity()) return@post
            val mainToolContainerLayoutParams =
                binding.mainToolContainer.layoutParams as FrameLayout.LayoutParams
            if (binding.mainToolContainer.width > binding.mainToolScroller.width) {
                mainToolContainerLayoutParams.gravity = Gravity.START
            } else {
                mainToolContainerLayoutParams.gravity = Gravity.CENTER_HORIZONTAL
            }

            binding.mainToolContainer.layoutParams = mainToolContainerLayoutParams
        }
    }

    override fun onUpdateTextAttributes(textAttributes: TextAttributes) {
        if (!checkViewBindingValidity()) return
        getToolTextSizeAdapter()?.apply {
            val action = onSizeChangeAction
            onSizeChangeAction = null
            updateTextSize(textAttributes.size.value.toInt())
            onSizeChangeAction = action
        }
        getToolColorAdapter()?.apply {
            val action = colorChangedAction
            colorChangedAction = null
            setFirstColorPosition(textAttributes.color)
            colorChangedAction = action
        }
        getToolTextParagraphStyleAdapter()?.apply {
            updateTextParagraphIcon(textAttributes.textGravity)
        }
    }

    override fun onUpdateFontAttributes(fontAttributes: FontAttributes) {
        getToolTextFontAdapter()?.apply {
            updateTextFont(fontAttributes.fontInfo)
        }
    }

    override fun onUndoStatusChange(isEnable: Boolean) {
        if (checkViewBindingValidity()) {
            binding.undo.isEnabled = isEnable
        }
    }

    override fun onRedoStatusChange(isEnable: Boolean) {
        if (checkViewBindingValidity()) {
            binding.redo.isEnabled = isEnable
        }
    }

    override fun onInputTextViewShow() {
        super.onInputTextViewShow()
        binding.textOperationContainer.isVisible = true
    }

    override fun onInputTextViewDismiss() {
        super.onInputTextViewDismiss()
        binding.textOperationContainer.isVisible = false
        toolAttributesViewModel.setTextStrikethrough(false)
        toolAttributesViewModel.setTextBold(false)
        toolAttributesViewModel.setTextUnderLine(false)
    }

    override fun getDoodleViewBottomOffset(): Int {
        val bottomOffset = resources.getDimensionPixelSize(R.dimen.dp_10)
        return binding.textOperationContainer.height + bottomOffset
    }

    override fun onWindowInsetsChange(
        isSoftKeyboardShowing: Boolean,
        softKeyboardHeight: Int,
        isStatusBarShowing: Boolean,
        statusBarHeight: Int,
        isNavigationBarShowing: Boolean,
        navigationBarHeight: Int,
    ) {
        super.onWindowInsetsChange(
            isSoftKeyboardShowing,
            softKeyboardHeight,
            isStatusBarShowing,
            statusBarHeight,
            isNavigationBarShowing,
            navigationBarHeight
        )

        val textOperationTranslationY = if (isSoftKeyboardShowing) {
            -softKeyboardHeight.toFloat() + navigationBarHeight
        } else {
            0F
        }
        binding.textOperationContainer.animate().translationY(textOperationTranslationY)
            .setDuration(0)
            .start()

        binding.root.apply {
            setPadding(
                paddingLeft, paddingTop, paddingRight, if (isNavigationBarShowing) {
                    navigationBarHeight
                } else {
                    0
                }
            )
        }

        if (!isSoftKeyboardShowing) {
            (activity as? MainActivity)?.unRegisterClearFocusAndHideSoftKeyBoardListener()
        } else {
            (activity as? MainActivity)?.registerClearFocusAndHideSoftKeyBoardListener(
                isIntercept = { true }
            )
        }
    }

    override fun refreshGuideLineConfig() {
        GuideLineConfig.enableGuideLine = false
    }

    override fun jumpToVip() {
        val action =
            HandWriteSnippetEditFragmentDirections.actionHandWriteSnippetEditToVipStore()
        action.source = NaviEnum.GRAFFITIPEN_MEMBERS
        safeNavigate(action)
    }

    override fun retrieveDoc(): Document {
        val inDocument = noteSnippetCreateViewModel.currentDoodleResultDoc!!
        originSnippetPage = inDocument.pages.firstOrNull()?.let { firstPage ->
            val clonedPage = firstPage.cloneWithIdNotChange()
            firstPage.draws.forEachIndexed { index, insertableObject ->
                clonedPage.draws[index].elementId = insertableObject.elementId
            }
            clonedPage
        }
        return inDocument
    }

    override fun invalidCurrentDoc(): Boolean {
        return noteSnippetCreateViewModel.currentDoodleResultDoc == null
    }

    override fun replaceImage() {
        if (!checkViewBindingValidity()) return
        val selectedObjects = doodleView.modelManager.getSelectedObjects()
        if (selectedObjects.size == 1) {
            val insertableObject = selectedObjects.first()
            if (insertableObject is InsertableBitmap) {
                editorViewModel.selectedInsertableBitmap = insertableObject
                checkAndPickImage()
            }
        }
    }

    override fun checkImagePermission(
        onCancel: ((state: PermissionRequester.PermissionState) -> Unit)?,
        onResult: (state: PermissionRequester.PermissionState) -> Unit,
    ) {
        val needRequestPermission =
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                Manifest.permission.READ_EXTERNAL_STORAGE
            } else {
                Manifest.permission.READ_MEDIA_IMAGES
            }
        PermissionRequester.showPermissionRationaleDialogThenRequest(
            title = AppUtils.getString(R.string.permission_rationale_title_for_storage),
            message = AppUtils.getString(R.string.permission_rationale_content_for_storage),
            permissionState = PermissionRequester.checkPermissionState(
                requireActivity(),
                needRequestPermission
            ),
            onRationaleDialogCanceled = {
                onCancel?.invoke(it)
            },
            fragmentManager = parentFragmentManager
        ) {
            permissionRequester.request(needRequestPermission) { isGranted ->
                if (isGranted) {
                    onResult.invoke(PermissionRequester.PermissionState.PERMISSION_GRANTED)
                } else {
                    val state = PermissionRequester.checkPermissionState(
                        this@HandWriteSnippetEditFragment,
                        needRequestPermission
                    )

                    if (state == PermissionRequester.PermissionState.PERMISSION_NOT_ASK_AGAIN) {
                        val alertDialog = AlertDialog.Builder()
                            .setMsg(resources.getString(R.string.never_aks_read_external_storage))
                            .setPositiveBtn(resources.getString(R.string.go_to_set)) {
                                PermissionRequester.jumpToSetting(this@HandWriteSnippetEditFragment)
                            }
                            .setNegativeBtn(resources.getString(R.string.ok)) {

                            }
                            .build()
                        alertDialog.show(
                            parentFragmentManager,
                            null
                        )
                    }

                    onResult.invoke(state)
                    LogHelper.w(
                        defaultTag,
                        "no permission to access images",
                        report2Bugly = true
                    )
                }
            }
        }
    }

    override fun checkAndPickImage() {
        checkImagePermission { state ->
            if (state == PermissionRequester.PermissionState.PERMISSION_GRANTED) {
                imageFetchViewModel.loadSidebarPictures()
                val bundle = Bundle().apply {
                    putBoolean(
                        SelectPhotoDialogActivity.BUNDLE_KEY_NEED_CROP_AND_CHANGE_ALPHA,
                        true
                    )
                }
                pickImage(bundle)
            }
        }
    }

    override fun onRequestImagePermissionFromModelManager(
        onCancel: ((state: PermissionRequester.PermissionState) -> Unit)?,
        callback: (result: PermissionRequester.PermissionState) -> Unit,
    ) {
        checkImagePermission(onCancel = {
            onCancel?.invoke(it)
        }) { state ->
            if (state == PermissionRequester.PermissionState.PERMISSION_GRANTED) {
                imageFetchViewModel.loadSidebarPictures()
            }
            callback(state)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_WRITE_EXTERNAL_STORAGE_CODE && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            //权限申请成功可以保存图片
            val selectedList = shareImgViewModel.shareImgSelectedIndexList
            if (selectedList.isNotEmpty()) {
                shareLongPicture(selectedList.sortedBy { it })
            }
        }
    }

    private fun shareLongPicture(pageIndexList: List<Int>) {
        val context = context ?: return
        if (needPermissions(context)) return
        noteViewModel.shareLongPicture(context, pageIndexList)
    }

    private fun needPermissions(context: Context): Boolean {
        val needPermissions = Build.VERSION.SDK_INT < 29 && ContextCompat.checkSelfPermission(
            context, Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) != PackageManager.PERMISSION_GRANTED
        if (needPermissions) {
            requestPermissions(
                arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                REQUEST_WRITE_EXTERNAL_STORAGE_CODE
            )
        }
        return needPermissions
    }

    private fun updateToolPenImage(inputMode: InputMode) {
        when (inputMode) {
            InputMode.DRAW -> {
                binding.noteMainToolPen.setTintLayerImageResource(R.drawable.note_tool_gel_pen_selected)
                binding.noteMainToolPen.setBorderLayerImageResource(R.drawable.note_tool_gel_pen_normal)
            }

            InputMode.PAINTBRUSH -> {
                binding.noteMainToolPen.setTintLayerImageResource(R.drawable.note_tool_paint_brush_selected)
                binding.noteMainToolPen.setBorderLayerImageResource(R.drawable.note_tool_paint_brush_normal)
            }

            else -> {
                binding.noteMainToolPen.setTintLayerImageResource(R.drawable.note_main_icon_tool_pen_selected)
                binding.noteMainToolPen.setBorderLayerImageResource(R.drawable.note_main_icon_tool_pen_normal)
            }
        }
    }

    override fun supportInputMode(): List<InputMode> {
        return listOf(
            InputMode.DRAW,
            InputMode.ERASER,
            InputMode.LASSO,
            InputMode.IMAGE,
            InputMode.HIGHLIGHTER,
            InputMode.GRAFFITI,
            InputMode.OUTLINEPEN,
            InputMode.LINEDRAW,
            InputMode.VIEW,
            InputMode.TEXT,
            InputMode.GRAPH,
            InputMode.PEN,
            InputMode.PAINTBRUSH
        )
    }

    override fun supportJumpInstantAlpha(): Boolean = false

    private val recognizeTaskMap = mutableMapOf<UUID, Bitmap>()
    private val fetchTextTaskMap = mutableMapOf<UUID, Bitmap>()
    var onFinishAction: (result: TextRecognitionResult, taskId: UUID) -> Unit = { result, taskId ->
        var recognizeBitmap = recognizeTaskMap.remove(taskId)
        if (recognizeBitmap != null) {
            binding.translateWebView.translate(result.symbols)
            snippetViewModel.changeTranslateViewVisible(true)
            recognizeBitmap.recycle()
            recognizeBitmap = null
        }
        var fetchTextResult = fetchTextTaskMap.remove(taskId)
        if (fetchTextResult != null) {
            if (!result.symbols.isNullOrEmpty() && result.symbols.isNotBlank()) {
                ClipboardUtil.newPlain(requireContext(), result.symbols)
                fetchTextResult.recycle()
                fetchTextResult = null
                if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2) {
                    ToastUtils.topCenter(
                        requireContext(),
                        AppUtils.getString(R.string.lasso_text_recognition_succeed)
                    )
                }
                EditEvent.sendEditLassoFetchText("success")
            } else {
                ToastUtils.topCenter(
                    requireContext(),
                    AppUtils.getString(R.string.lasso_text_recognition_fail)
                )
                EditEvent.sendEditLassoFetchText("fail")
            }
        }
    }

    override fun translateByBitmap(bitmap: Bitmap) {
        super.translateByBitmap(bitmap)
        val taskId = TextRecognitionManager.addRecognitionTask(
            bitmap,
            TextRecognitionManager.HIGH_PRIORITY
        )
        recognizeTaskMap[taskId] = bitmap
    }

    override fun fetchTextByLasso(bitmap: Bitmap) {
        TextRecognitionManager.changeRecognitionLanguage(UserUsageConfig.lassoFetchTextLanguage)
        val taskId = TextRecognitionManager.addRecognitionTask(
            bitmap,
            TextRecognitionManager.HIGH_PRIORITY
        )
        fetchTextTaskMap[taskId] = bitmap
    }

    override fun setLassoShieldMenuList(): ArrayList<LassoToolType> {
        val shieldMenuList = ArrayList<LassoToolType>()
        shieldMenuList.add(LassoToolType.OUTLINE_ADDITION)
        shieldMenuList.add(LassoToolType.SEARCH)
        return shieldMenuList
    }

    override fun setTextToolShieldMenuList(): ArrayList<TextToolType> {
        val shieldMenuList = ArrayList<TextToolType>()
        shieldMenuList.add(TextToolType.OUTLINE_ADDITION)
        return shieldMenuList
    }

    override fun enableScale(): Boolean = false

    override fun enableScroll(): Boolean = false
}
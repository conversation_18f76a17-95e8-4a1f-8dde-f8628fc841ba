package com.topstack.kilonotes.pad.select.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ExpandableListView
import androidx.recyclerview.widget.RecyclerView
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.fonts.FontInfo
import com.topstack.kilonotes.base.fonts.FontTypeGroup
import com.topstack.kilonotes.base.fonts.IFontStateListener

class FontAdapter(
    private val context: Context,
    private var dataList: List<FontTypeGroup>,
) : RecyclerView.Adapter<FontAdapter.FontFamilyHolder>() {

    private val groupHeight = context.resources.getDimension(R.dimen.dp_60).toInt()
    private val childHeight = context.resources.getDimension(R.dimen.dp_60).toInt()

    var onFontChangeAction: ((fontInfo: FontInfo) -> Unit)? = null
    var fontDown:((String, IFontStateListener) -> Unit)? =null
    private val fontTypeGroup2ExpandableAdapter = hashMapOf<FontTypeGroup,FontGroupExpandableListAdapter>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FontFamilyHolder {
        return FontFamilyHolder(
            LayoutInflater.from(parent.context).inflate(R.layout.item_font_list, parent, false)
        )
    }

    override fun onBindViewHolder(
        holder: FontFamilyHolder,
        @SuppressLint("RecyclerView") position: Int
    ) {
        val  fontTypeGroup= dataList[position]
        if (fontTypeGroup2ExpandableAdapter[fontTypeGroup] == null){
            fontTypeGroup2ExpandableAdapter[fontTypeGroup] =  FontGroupExpandableListAdapter(
                context,
                dataList[position],
                position,
                groupShow ={
                    dataList[position].isExpand = !dataList[position].isExpand
                    notifyDataSetChanged()
                    return@FontGroupExpandableListAdapter true
                },
                fontDownLoad ={
                    return@FontGroupExpandableListAdapter true
                }
            ).apply {
                onFontChangeAction={
                    <EMAIL>?.invoke(it)
                }
                fontDown = {url,fontStateListener->
                    <EMAIL>?.invoke(url,fontStateListener)
                }
            }
        }
        holder.fontTypeGroup.setAdapter(fontTypeGroup2ExpandableAdapter[fontTypeGroup])
        holder.fontTypeGroup.setGroupIndicator(null)
        holder.fontTypeGroup.isVerticalScrollBarEnabled = false
        if (fontTypeGroup.isExpand) {
            holder.fontTypeGroup.layoutParams.height =
                fontTypeGroup.childList[0].size * childHeight + groupHeight
            holder.fontTypeGroup.expandGroup(0)
        } else {
            holder.fontTypeGroup.layoutParams.height = groupHeight
        }
        holder.fontTypeGroup.setOnGroupClickListener(object :
            ExpandableListView.OnGroupClickListener {
            override fun onGroupClick(
                parent: ExpandableListView?,
                v: View?,
                groupPosition: Int,
                id: Long
            ): Boolean {
                //返回true使其点击不展开
                return true
            }
        })
        holder.fontTypeGroup.setOnChildClickListener(object :
            ExpandableListView.OnChildClickListener {
            override fun onChildClick(
                parent: ExpandableListView?,
                v: View?,
                groupPosition: Int,
                childPosition: Int,
                id: Long
            ): Boolean {
                //返回true使其可点击
                return true
            }

        })
        holder.fontTypeGroup.isGroupExpanded(0)
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    fun refreshFontList(dataList: List<FontTypeGroup>) {
        this.dataList = dataList
        notifyDataSetChanged()
    }

    class FontFamilyHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var fontTypeGroup: ExpandableListView = itemView.findViewById(R.id.font_type)
    }


}



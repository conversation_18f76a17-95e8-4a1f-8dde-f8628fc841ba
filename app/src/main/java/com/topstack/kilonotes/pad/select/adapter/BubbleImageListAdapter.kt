package com.topstack.kilonotes.pad.select.adapter

import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatImageView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.bumptech.glide.Glide
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.imagefetch.model.Image
import com.topstack.kilonotes.base.util.findActivity

/**
 *
 */
class BubbleImageListAdapter :
    ListAdapter<Image, BubbleImageViewHolder>(object : DiffUtil.ItemCallback<Image>() {
        override fun areItemsTheSame(oldItem: Image, newItem: Image): Boolean = false

        override fun areContentsTheSame(oldItem: Image, newItem: Image): Boolean {
            return oldItem == newItem
        }
    }) {

    private var recyclerView: RecyclerView? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        this.recyclerView = null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BubbleImageViewHolder {
        val imageView = AppCompatImageView(parent.context)
        val viewLength = parent.context.resources.getDimensionPixelSize(R.dimen.dp_134)
        imageView.layoutParams = RecyclerView.LayoutParams(viewLength, viewLength)
        imageView.scaleType = ImageView.ScaleType.CENTER_CROP
        return BubbleImageViewHolder(imageView, viewLength)
    }

    override fun onBindViewHolder(holder: BubbleImageViewHolder, position: Int) {
        val image = getItem(position)
        holder.bindImage(image)
        holder.itemView.setOnClickListener(viewClickListener)
    }

    private val viewClickListener = OnClickListener { v ->
        recyclerView?.getChildViewHolder(v)?.let { viewHolder ->
            val position = viewHolder.bindingAdapterPosition
            if (position >= itemCount || position < 0) return@OnClickListener
            val image = getItem(position)

            actionOnItemClick?.invoke(position, image)
        }
    }

    private var actionOnItemClick: ((position: Int, image: Image) -> Unit)? = null

    fun doOnItemClick(
        action: ((position: Int, image: Image) -> Unit)?
    ) {
        actionOnItemClick = action
    }
}

class BubbleImageViewHolder(private val imageView: ImageView, private val viewLength: Int) :
    ViewHolder(imageView) {
    fun bindImage(image: Image) {
        val activity = findActivity(imageView.context)
        if (activity?.isDestroyed == false) {
            Glide.with(imageView)
                .load(image.uri)
                .override(viewLength, viewLength)
                .into(imageView)
        }
    }
}
package com.topstack.kilonotes.pad.component

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.inputmethod.EditorInfo
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.constraintlayout.widget.ConstraintLayout
import com.topstack.kilonotes.R
import com.topstack.kilonotes.base.flavor.ProductFlavor
import com.topstack.kilonotes.base.track.event.NoteCountEvent
import com.topstack.kilonotes.base.util.ToastUtils
import com.topstack.kilonotes.base.util.WindowInsetsUtils
import com.topstack.kilonotes.databinding.NoteWebSidebarViewBinding
import com.topstack.kilonotes.infra.network.NetworkUtils
import java.net.URLEncoder

class WebSidebarView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val HTTPS_PREFIX = "https://"
        private const val HTTP_PREFIX = "http://"
        private const val BAIDU_TRANSLATE_WEBSITE = "https://fanyi.baidu.com"
        private const val BAIDU_TRANSLATE_PREFIX = "$BAIDU_TRANSLATE_WEBSITE/#auto/%s/"
        private const val GOOGLE_TRANSLATE_WEBSITE = "https://translate.google.com"
        private const val GOOGLE_TRANSLATE_PREFIX =
            "$GOOGLE_TRANSLATE_WEBSITE/?sl=auto&tl=%s&text="
        private const val GOOGLE_TRANSLATE_SUFFIX = "&op=translate"
        private const val SPACE_URL_AFTER_CONVERT = "%20"
        private const val SPACE_URL_BEFORE_CONVERT = "+"
        private const val LANGUAGE_EN = "en"
        private const val BAIDU_SEARCH_WEBSITE = "https://baidu.com/s?wd="
        private const val GOOGLE_SEARCH_WEBSITE = "https://www.google.com/search?q="
        private val TRANSLATION_LANGUAGE_MAP_BAIDU = mapOf<String, String>(
            "zh" to "zh",
            "en" to "en",
            "ru" to "ru",
            "tr" to "tr",
            "de" to "de",
            "it" to "it",
            "ja" to "jp",
            "fr" to "fra",
            "th" to "th",
            "nl" to "nl",
            "es" to "spa",
            "ko" to "kor",
            "vi" to "vie",
            "ms" to "may",
            "in" to "id",
            "uk" to "ukr",
            "el" to "el",
            "cs" to "cs",
            "pt" to "pt"
        )
        private val TRANSLATION_LANGUAGE_MAP_GOOGLE = mapOf<String, String>(
            "zh" to "zh",
            "en" to "en",
            "ru" to "ru",
            "tr" to "tr",
            "de" to "de",
            "it" to "it",
            "ja" to "ja",
            "fr" to "fr",
            "th" to "th",
            "nl" to "nl",
            "es" to "es",
            "ko" to "ko",
            "vi" to "vi",
            "ms" to "ms",
            "in" to "id",
            "uk" to "uk",
            "el" to "el",
            "cs" to "cs",
            "pt" to "pt"
        )

    }

    private var clearHistoryFlag = false

    private val binding =
        NoteWebSidebarViewBinding.inflate(LayoutInflater.from(context), this, true)

    var onCloseListener: (() -> Unit)? = null

    private var webView: WebView? = null

    init {
        webView = try {
            WebView(context)
        } catch (e: Exception) {
            null
        }
        if (webView != null) {
            val layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, 0)
            layoutParams.topToBottom = R.id.top_divider
            layoutParams.bottomToBottom = LayoutParams.PARENT_ID
            binding.webContent.addView(webView, 0, layoutParams)
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onFinishInflate() {
        super.onFinishInflate()
        binding.bottomBackground.setupWith(binding.root)
            .setBlurRadius(23F)

        binding.back.setOnClickListener {
            webView?.let {
                if (it.canGoBack()) {
                    it.goBack()
                }
            }
        }
        binding.forward.setOnClickListener {
            webView?.let {
                if (it.canGoForward()) {
                    it.goForward()
                }
            }
        }
        binding.refresh.setOnClickListener {
            webView?.reload()
        }
        binding.webClearImageView.setOnClickListener {
            binding.webSearchEditText.setText("")
            binding.webSearchEditText.requestFocus()
        }
        binding.webBorder.onButtonClickedAction = {
            onCloseListener?.invoke()
        }
        binding.webSearchImageView.setOnClickListener {
            loadInputUrl()
        }
        binding.webSearchEditText.run {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus) {
                    binding.webSearchEditText.setText(webView?.url ?: "")
                    binding.webSearchEditText.setSelection(0)
                }
            }
            setOnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_GO) {
                    loadInputUrl()
                    WindowInsetsUtils.hideSoftKeyBoard(this)
                }
                false
            }
        }
        webView?.apply {
            webViewClient = object : WebViewClient() {
                override fun doUpdateVisitedHistory(
                    view: WebView?,
                    url: String?,
                    isReload: Boolean
                ) {
                    binding.webSearchEditText.setText(url)
                    refreshBackAndForwardState()
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    NoteCountEvent.TRANSLATE_WEB_PAGE_LOAD_SUCCESS.send()
                    if (clearHistoryFlag) {
                        clearHistoryInternal()
                        clearHistoryFlag = false
                    }
                }

            }
            settings.run {
                javaScriptEnabled = true
                builtInZoomControls = true
                domStorageEnabled = true  // 启用DOM存储API
                databaseEnabled = true    // 启用数据库存储API
                // 如果需要访问https资源
                mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            }
        }
        refreshBackAndForwardState()
    }

    private fun loadInputUrl() {
        var url = binding.webSearchEditText.text.toString()
        if (!url.startsWith(HTTPS_PREFIX) && !url.startsWith(HTTP_PREFIX)) {
            url = HTTP_PREFIX + url
        }
        webView?.loadUrl(url)
    }

    private fun refreshBackAndForwardState() {
        binding.back.isEnabled = webView?.canGoBack() == true
        binding.forward.isEnabled = webView?.canGoForward() == true
    }

    fun translate(content: String) {
        val context = context
        if (!NetworkUtils.isNetworkAvailable() && context is Activity) {
            ToastUtils.windowTopCenter(context, R.string.toast_no_internet)
        }
        val encodedContent = convertStringToURLContent(content)
        val url = if (ProductFlavor.isPlayChannel()) {
            val prefix = String.format(GOOGLE_TRANSLATE_PREFIX, getDestinationLanguage())
            "$prefix$encodedContent$GOOGLE_TRANSLATE_SUFFIX"
        } else {
            if (encodedContent.isBlank()) {
                BAIDU_TRANSLATE_WEBSITE
            } else {
                val prefix = String.format(BAIDU_TRANSLATE_PREFIX, getDestinationLanguage())
                "$prefix$encodedContent"
            }
        }
        webView?.loadUrl(url)
    }

    fun search(content: String) {
        val context = context
        if (!NetworkUtils.isNetworkAvailable() && context is Activity) {
            ToastUtils.windowTopCenter(context, R.string.toast_no_internet)
        }
        val encodedContent = convertStringToURLContent(content)
        val url = if (ProductFlavor.isPlayChannel()) {
            val prefix = String.format(GOOGLE_SEARCH_WEBSITE, getDestinationLanguage())
            "$prefix$encodedContent"
        } else {
            if (encodedContent.isBlank()) {
                BAIDU_SEARCH_WEBSITE
            } else {
                val prefix = String.format(BAIDU_SEARCH_WEBSITE, getDestinationLanguage())
                "$prefix$encodedContent"
            }
        }
        webView?.loadUrl(url)
    }

    fun saveWebViewState(state: Bundle) {
        webView?.saveState(state)
    }

    fun restoreWebViewState(state: Bundle) {
        webView?.restoreState(state)
    }

    fun destroyWebView() {
        webView?.apply {
            binding.root.removeView(this)
            removeAllViews()
            destroy()
        }
    }

    // URLEncoder.encode()将空格编码为"+",百度翻译无法判断"+"由空格编码而来,所以需单独处理由空格编出来的"+"
    private fun convertStringToURLContent(content: String): String {
        val encodedContent = URLEncoder.encode(content, Charsets.UTF_8.name())
        return encodedContent.replace(SPACE_URL_BEFORE_CONVERT, SPACE_URL_AFTER_CONVERT)
    }

    private fun getCurrentSystemLanguage(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            resources.configuration.locales.get(0).language
        } else {
            resources.configuration.locale.language
        }
    }

    private fun getDestinationLanguage(): String {
        val language = getCurrentSystemLanguage()
        val result = if (ProductFlavor.isPlayChannel()) {
            TRANSLATION_LANGUAGE_MAP_GOOGLE[language]
        } else {
            TRANSLATION_LANGUAGE_MAP_BAIDU[language]
        }
        return result ?: LANGUAGE_EN
    }

    fun clearHistory() {
        clearHistoryFlag = true
    }

    //不要在url加载时调用
    private fun clearHistoryInternal() {
        webView?.apply {
            if (canGoBack()) {
                clearHistory()
                clearCache(true)
                refreshBackAndForwardState()
            }
        }
    }

}
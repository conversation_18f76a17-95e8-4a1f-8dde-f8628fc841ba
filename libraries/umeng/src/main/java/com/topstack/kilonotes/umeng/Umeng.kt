package com.topstack.kilonotes.umeng

import android.content.Context
import com.topstack.kilonotes.infra.util.LogHelper
import com.umeng.commonsdk.UMConfigure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

object Umeng {
    private const val TAG = "Umeng"
    private var isAlreadyPreInit = false
    private var isAlreadyInit = false
    private var umengInitJob: Job? = null
    private val invokeOnUmengInitFinishTasks = mutableListOf<() -> Unit>()

    @Synchronized
    fun preInitUmengIfNeed(
        context: Context,
        key: String,
        channel: String,
        inDebugMode: Boolean
    ) {
        if (isAlreadyPreInit) {
            LogHelper.d(TAG, "Umeng already preInit.")
            return
        }
        UMConfigure.setLogEnabled(inDebugMode)
        /**
         * https://developer.umeng.com/docs/119267/detail/118588
         *
         *  为保证您的在集成【友盟+】统计SDK之后，能够满足工信部相关合规要求，您应确保在App安装后首次冷启动时，在Applicaiton.onCreate函数中调用预初始化函数UMConfigure.preInit()，并弹出隐私授权弹窗给您的App用户。
         *  预初始化函数不会采集设备信息，也不会向友盟后台上报数据，同时preInit耗时极少，不会影响冷启动体验。所以务必在Applicaiton.onCreate函数中调用，否则统计日活是不准确的！
         */
        //预初始化函数需要在Application.onCreate函数中主线程内调用，预初始化函数不会采集设备信息，也不会向友盟后台上报数据。
        UMConfigure.preInit(context, key, channel)
        isAlreadyPreInit = true
    }

    @Synchronized
    fun initUmengIfNeed(
        context: Context,
        key: String,
        pushKey: String?,
        channel: String,
        invokeOnFinish: (() -> Unit)? = null
    ) {
        if (isAlreadyInit) {
            if (invokeOnFinish != null) {
                if (umengInitJob!!.isCompleted) {
                    invokeOnFinish.invoke()
                } else {
                    invokeOnUmengInitFinishTasks.add(invokeOnFinish)
                }
            }

            LogHelper.d(TAG, "Umeng already init.")
            return
        }
        //建议子线程中初始化SDK（启动优化）
        umengInitJob = GlobalScope.launch(Dispatchers.IO) {
            UMConfigure.init(
                context,
                key, // app key
                channel, // channel
                UMConfigure.DEVICE_TYPE_PHONE, // device type
                pushKey // push secret
            )
        }
        umengInitJob!!.invokeOnCompletion {
            invokeOnUmengInitFinishTasks.forEach {
                it.invoke()
            }
        }
        isAlreadyInit = true
    }

}
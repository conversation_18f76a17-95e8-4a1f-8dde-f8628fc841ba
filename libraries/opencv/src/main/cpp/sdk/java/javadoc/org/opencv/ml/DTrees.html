<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:56 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DTrees (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DTrees (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":9,"i12":9,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/Boost.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/DTrees.html" target="_top">Frames</a></li>
<li><a href="DTrees.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class DTrees" class="title">Class DTrees</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.DTrees</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/opencv/ml/Boost.html" title="class in org.opencv.ml">Boost</a>, <a href="../../../org/opencv/ml/RTrees.html" title="class in org.opencv.ml">RTrees</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DTrees</span>
extends <a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></pre>
<div class="block">The class represents a single decision tree or a collection of decision trees.

 The current public interface of the class allows user to train only a single decision tree, however
 the class is capable of storing multiple decision trees and using them for prediction (by summing
 responses or using a voting schemes), and the derived from DTrees classes (such as RTrees and Boost)
 use this capability to implement decision tree ensembles.

 SEE: REF: ml_intro_trees</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#PREDICT_AUTO">PREDICT_AUTO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#PREDICT_MASK">PREDICT_MASK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#PREDICT_MAX_VOTE">PREDICT_MAX_VOTE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#PREDICT_SUM">PREDICT_SUM</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#create--">create</a></span>()</code>
<div class="block">Creates the empty model

     The static method creates empty decision tree with the specified parameters.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getCVFolds--">getCVFolds</a></span>()</code>
<div class="block">SEE: setCVFolds</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getMaxCategories--">getMaxCategories</a></span>()</code>
<div class="block">SEE: setMaxCategories</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getMaxDepth--">getMaxDepth</a></span>()</code>
<div class="block">SEE: setMaxDepth</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getMinSampleCount--">getMinSampleCount</a></span>()</code>
<div class="block">SEE: setMinSampleCount</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getPriors--">getPriors</a></span>()</code>
<div class="block">SEE: setPriors</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getRegressionAccuracy--">getRegressionAccuracy</a></span>()</code>
<div class="block">SEE: setRegressionAccuracy</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getTruncatePrunedTree--">getTruncatePrunedTree</a></span>()</code>
<div class="block">SEE: setTruncatePrunedTree</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getUse1SERule--">getUse1SERule</a></span>()</code>
<div class="block">SEE: setUse1SERule</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#getUseSurrogates--">getUseSurrogates</a></span>()</code>
<div class="block">SEE: setUseSurrogates</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath)</code>
<div class="block">Loads and creates a serialized DTrees from a file

 Use DTree::save to serialize and store an DTree to disk.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#load-java.lang.String-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath,
    java.lang.String&nbsp;nodeName)</code>
<div class="block">Loads and creates a serialized DTrees from a file

 Use DTree::save to serialize and store an DTree to disk.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setCVFolds-int-">setCVFolds</a></span>(int&nbsp;val)</code>
<div class="block">getCVFolds SEE: getCVFolds</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setMaxCategories-int-">setMaxCategories</a></span>(int&nbsp;val)</code>
<div class="block">getMaxCategories SEE: getMaxCategories</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setMaxDepth-int-">setMaxDepth</a></span>(int&nbsp;val)</code>
<div class="block">getMaxDepth SEE: getMaxDepth</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setMinSampleCount-int-">setMinSampleCount</a></span>(int&nbsp;val)</code>
<div class="block">getMinSampleCount SEE: getMinSampleCount</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setPriors-org.opencv.core.Mat-">setPriors</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;val)</code>
<div class="block">getPriors SEE: getPriors</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setRegressionAccuracy-float-">setRegressionAccuracy</a></span>(float&nbsp;val)</code>
<div class="block">getRegressionAccuracy SEE: getRegressionAccuracy</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setTruncatePrunedTree-boolean-">setTruncatePrunedTree</a></span>(boolean&nbsp;val)</code>
<div class="block">getTruncatePrunedTree SEE: getTruncatePrunedTree</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setUse1SERule-boolean-">setUse1SERule</a></span>(boolean&nbsp;val)</code>
<div class="block">getUse1SERule SEE: getUse1SERule</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/DTrees.html#setUseSurrogates-boolean-">setUseSurrogates</a></span>(boolean&nbsp;val)</code>
<div class="block">getUseSurrogates SEE: getUseSurrogates</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#calcError-org.opencv.ml.TrainData-boolean-org.opencv.core.Mat-">calcError</a>, <a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-int-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="PREDICT_AUTO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREDICT_AUTO</h4>
<pre>public static final&nbsp;int PREDICT_AUTO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.DTrees.PREDICT_AUTO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PREDICT_MASK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREDICT_MASK</h4>
<pre>public static final&nbsp;int PREDICT_MASK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.DTrees.PREDICT_MASK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PREDICT_MAX_VOTE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREDICT_MAX_VOTE</h4>
<pre>public static final&nbsp;int PREDICT_MAX_VOTE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.DTrees.PREDICT_MAX_VOTE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PREDICT_SUM">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PREDICT_SUM</h4>
<pre>public static final&nbsp;int PREDICT_SUM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.DTrees.PREDICT_SUM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a>&nbsp;create()</pre>
<div class="block">Creates the empty model

     The static method creates empty decision tree with the specified parameters. It should be then
     trained using train method (see StatModel::train). Alternatively, you can load the model from
     file using Algorithm::load&lt;DTrees&gt;(filename).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getCVFolds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCVFolds</h4>
<pre>public&nbsp;int&nbsp;getCVFolds()</pre>
<div class="block">SEE: setCVFolds</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getMaxCategories--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxCategories</h4>
<pre>public&nbsp;int&nbsp;getMaxCategories()</pre>
<div class="block">SEE: setMaxCategories</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getMaxDepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxDepth</h4>
<pre>public&nbsp;int&nbsp;getMaxDepth()</pre>
<div class="block">SEE: setMaxDepth</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getMinSampleCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinSampleCount</h4>
<pre>public&nbsp;int&nbsp;getMinSampleCount()</pre>
<div class="block">SEE: setMinSampleCount</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getPriors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriors</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getPriors()</pre>
<div class="block">SEE: setPriors</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getRegressionAccuracy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRegressionAccuracy</h4>
<pre>public&nbsp;float&nbsp;getRegressionAccuracy()</pre>
<div class="block">SEE: setRegressionAccuracy</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTruncatePrunedTree--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTruncatePrunedTree</h4>
<pre>public&nbsp;boolean&nbsp;getTruncatePrunedTree()</pre>
<div class="block">SEE: setTruncatePrunedTree</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getUse1SERule--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUse1SERule</h4>
<pre>public&nbsp;boolean&nbsp;getUse1SERule()</pre>
<div class="block">SEE: setUse1SERule</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getUseSurrogates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseSurrogates</h4>
<pre>public&nbsp;boolean&nbsp;getUseSurrogates()</pre>
<div class="block">SEE: setUseSurrogates</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a>&nbsp;load(java.lang.String&nbsp;filepath)</pre>
<div class="block">Loads and creates a serialized DTrees from a file

 Use DTree::save to serialize and store an DTree to disk.
 Load the DTree from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized DTree</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a>&nbsp;load(java.lang.String&nbsp;filepath,
                          java.lang.String&nbsp;nodeName)</pre>
<div class="block">Loads and creates a serialized DTrees from a file

 Use DTree::save to serialize and store an DTree to disk.
 Load the DTree from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized DTree</dd>
<dd><code>nodeName</code> - name of node containing the classifier</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setCVFolds-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCVFolds</h4>
<pre>public&nbsp;void&nbsp;setCVFolds(int&nbsp;val)</pre>
<div class="block">getCVFolds SEE: getCVFolds</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setMaxCategories-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxCategories</h4>
<pre>public&nbsp;void&nbsp;setMaxCategories(int&nbsp;val)</pre>
<div class="block">getMaxCategories SEE: getMaxCategories</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setMaxDepth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxDepth</h4>
<pre>public&nbsp;void&nbsp;setMaxDepth(int&nbsp;val)</pre>
<div class="block">getMaxDepth SEE: getMaxDepth</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setMinSampleCount-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinSampleCount</h4>
<pre>public&nbsp;void&nbsp;setMinSampleCount(int&nbsp;val)</pre>
<div class="block">getMinSampleCount SEE: getMinSampleCount</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setPriors-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriors</h4>
<pre>public&nbsp;void&nbsp;setPriors(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;val)</pre>
<div class="block">getPriors SEE: getPriors</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setRegressionAccuracy-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRegressionAccuracy</h4>
<pre>public&nbsp;void&nbsp;setRegressionAccuracy(float&nbsp;val)</pre>
<div class="block">getRegressionAccuracy SEE: getRegressionAccuracy</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTruncatePrunedTree-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTruncatePrunedTree</h4>
<pre>public&nbsp;void&nbsp;setTruncatePrunedTree(boolean&nbsp;val)</pre>
<div class="block">getTruncatePrunedTree SEE: getTruncatePrunedTree</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setUse1SERule-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUse1SERule</h4>
<pre>public&nbsp;void&nbsp;setUse1SERule(boolean&nbsp;val)</pre>
<div class="block">getUse1SERule SEE: getUse1SERule</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setUseSurrogates-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setUseSurrogates</h4>
<pre>public&nbsp;void&nbsp;setUseSurrogates(boolean&nbsp;val)</pre>
<div class="block">getUseSurrogates SEE: getUseSurrogates</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/Boost.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/DTrees.html" target="_top">Frames</a></li>
<li><a href="DTrees.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

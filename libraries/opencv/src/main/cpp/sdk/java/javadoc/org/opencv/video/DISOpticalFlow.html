<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:56 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DISOpticalFlow (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DISOpticalFlow (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/DISOpticalFlow.html" target="_top">Frames</a></li>
<li><a href="DISOpticalFlow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.video</div>
<h2 title="Class DISOpticalFlow" class="title">Class DISOpticalFlow</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">org.opencv.video.DenseOpticalFlow</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.video.DISOpticalFlow</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DISOpticalFlow</span>
extends <a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></pre>
<div class="block">DIS optical flow algorithm.

 This class implements the Dense Inverse Search (DIS) optical flow algorithm. More
 details about the algorithm can be found at CITE: Kroeger2016 . Includes three presets with preselected
 parameters to provide reasonable trade-off between speed and quality. However, even the slowest preset is
 still relatively fast, use DeepFlow if you need better quality and don't care about speed.

 This implementation includes several additional features compared to the algorithm described in the paper,
 including spatial propagation of flow vectors (REF: getUseSpatialPropagation), as well as an option to
 utilize an initial flow approximation passed to REF: calc (which is, essentially, temporal propagation,
 if the previous frame's flow field is passed).</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#PRESET_FAST">PRESET_FAST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#PRESET_MEDIUM">PRESET_MEDIUM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#PRESET_ULTRAFAST">PRESET_ULTRAFAST</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video">DISOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video">DISOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#create--">create</a></span>()</code>
<div class="block">Creates an instance of DISOpticalFlow</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video">DISOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#create-int-">create</a></span>(int&nbsp;preset)</code>
<div class="block">Creates an instance of DISOpticalFlow</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getFinestScale--">getFinestScale</a></span>()</code>
<div class="block">Finest level of the Gaussian pyramid on which the flow is computed (zero level
         corresponds to the original image resolution).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getGradientDescentIterations--">getGradientDescentIterations</a></span>()</code>
<div class="block">Maximum number of gradient descent iterations in the patch inverse search stage.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getPatchSize--">getPatchSize</a></span>()</code>
<div class="block">Size of an image patch for matching (in pixels).</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getPatchStride--">getPatchStride</a></span>()</code>
<div class="block">Stride between neighbor patches.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getUseMeanNormalization--">getUseMeanNormalization</a></span>()</code>
<div class="block">Whether to use mean-normalization of patches when computing patch distance.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getUseSpatialPropagation--">getUseSpatialPropagation</a></span>()</code>
<div class="block">Whether to use spatial propagation of good optical flow vectors.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getVariationalRefinementAlpha--">getVariationalRefinementAlpha</a></span>()</code>
<div class="block">Weight of the smoothness term
 SEE: setVariationalRefinementAlpha</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getVariationalRefinementDelta--">getVariationalRefinementDelta</a></span>()</code>
<div class="block">Weight of the color constancy term
 SEE: setVariationalRefinementDelta</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getVariationalRefinementGamma--">getVariationalRefinementGamma</a></span>()</code>
<div class="block">Weight of the gradient constancy term
 SEE: setVariationalRefinementGamma</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#getVariationalRefinementIterations--">getVariationalRefinementIterations</a></span>()</code>
<div class="block">Number of fixed point iterations of variational refinement per scale.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setFinestScale-int-">setFinestScale</a></span>(int&nbsp;val)</code>
<div class="block">getFinestScale SEE: getFinestScale</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setGradientDescentIterations-int-">setGradientDescentIterations</a></span>(int&nbsp;val)</code>
<div class="block">getGradientDescentIterations SEE: getGradientDescentIterations</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setPatchSize-int-">setPatchSize</a></span>(int&nbsp;val)</code>
<div class="block">getPatchSize SEE: getPatchSize</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setPatchStride-int-">setPatchStride</a></span>(int&nbsp;val)</code>
<div class="block">getPatchStride SEE: getPatchStride</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setUseMeanNormalization-boolean-">setUseMeanNormalization</a></span>(boolean&nbsp;val)</code>
<div class="block">getUseMeanNormalization SEE: getUseMeanNormalization</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setUseSpatialPropagation-boolean-">setUseSpatialPropagation</a></span>(boolean&nbsp;val)</code>
<div class="block">getUseSpatialPropagation SEE: getUseSpatialPropagation</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setVariationalRefinementAlpha-float-">setVariationalRefinementAlpha</a></span>(float&nbsp;val)</code>
<div class="block">getVariationalRefinementAlpha SEE: getVariationalRefinementAlpha</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setVariationalRefinementDelta-float-">setVariationalRefinementDelta</a></span>(float&nbsp;val)</code>
<div class="block">getVariationalRefinementDelta SEE: getVariationalRefinementDelta</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setVariationalRefinementGamma-float-">setVariationalRefinementGamma</a></span>(float&nbsp;val)</code>
<div class="block">getVariationalRefinementGamma SEE: getVariationalRefinementGamma</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/DISOpticalFlow.html#setVariationalRefinementIterations-int-">setVariationalRefinementIterations</a></span>(int&nbsp;val)</code>
<div class="block">getGradientDescentIterations SEE: getGradientDescentIterations</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.video.DenseOpticalFlow">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.video.<a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></h3>
<code><a href="../../../org/opencv/video/DenseOpticalFlow.html#calc-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">calc</a>, <a href="../../../org/opencv/video/DenseOpticalFlow.html#collectGarbage--">collectGarbage</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="PRESET_FAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRESET_FAST</h4>
<pre>public static final&nbsp;int PRESET_FAST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.DISOpticalFlow.PRESET_FAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PRESET_MEDIUM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRESET_MEDIUM</h4>
<pre>public static final&nbsp;int PRESET_MEDIUM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.DISOpticalFlow.PRESET_MEDIUM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PRESET_ULTRAFAST">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PRESET_ULTRAFAST</h4>
<pre>public static final&nbsp;int PRESET_ULTRAFAST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.DISOpticalFlow.PRESET_ULTRAFAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video">DISOpticalFlow</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video">DISOpticalFlow</a>&nbsp;create()</pre>
<div class="block">Creates an instance of DISOpticalFlow</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video">DISOpticalFlow</a>&nbsp;create(int&nbsp;preset)</pre>
<div class="block">Creates an instance of DISOpticalFlow</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preset</code> - one of PRESET_ULTRAFAST, PRESET_FAST and PRESET_MEDIUM</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getFinestScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinestScale</h4>
<pre>public&nbsp;int&nbsp;getFinestScale()</pre>
<div class="block">Finest level of the Gaussian pyramid on which the flow is computed (zero level
         corresponds to the original image resolution). The final flow is obtained by bilinear upscaling.
 SEE: setFinestScale</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getGradientDescentIterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGradientDescentIterations</h4>
<pre>public&nbsp;int&nbsp;getGradientDescentIterations()</pre>
<div class="block">Maximum number of gradient descent iterations in the patch inverse search stage. Higher values
         may improve quality in some cases.
 SEE: setGradientDescentIterations</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getPatchSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPatchSize</h4>
<pre>public&nbsp;int&nbsp;getPatchSize()</pre>
<div class="block">Size of an image patch for matching (in pixels). Normally, default 8x8 patches work well
         enough in most cases.
 SEE: setPatchSize</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getPatchStride--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPatchStride</h4>
<pre>public&nbsp;int&nbsp;getPatchStride()</pre>
<div class="block">Stride between neighbor patches. Must be less than patch size. Lower values correspond
         to higher flow quality.
 SEE: setPatchStride</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getUseMeanNormalization--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseMeanNormalization</h4>
<pre>public&nbsp;boolean&nbsp;getUseMeanNormalization()</pre>
<div class="block">Whether to use mean-normalization of patches when computing patch distance. It is turned on
         by default as it typically provides a noticeable quality boost because of increased robustness to
         illumination variations. Turn it off if you are certain that your sequence doesn't contain any changes
         in illumination.
 SEE: setUseMeanNormalization</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getUseSpatialPropagation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseSpatialPropagation</h4>
<pre>public&nbsp;boolean&nbsp;getUseSpatialPropagation()</pre>
<div class="block">Whether to use spatial propagation of good optical flow vectors. This option is turned on by
         default, as it tends to work better on average and can sometimes help recover from major errors
         introduced by the coarse-to-fine scheme employed by the DIS optical flow algorithm. Turning this
         option off can make the output flow field a bit smoother, however.
 SEE: setUseSpatialPropagation</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVariationalRefinementAlpha--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariationalRefinementAlpha</h4>
<pre>public&nbsp;float&nbsp;getVariationalRefinementAlpha()</pre>
<div class="block">Weight of the smoothness term
 SEE: setVariationalRefinementAlpha</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVariationalRefinementDelta--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariationalRefinementDelta</h4>
<pre>public&nbsp;float&nbsp;getVariationalRefinementDelta()</pre>
<div class="block">Weight of the color constancy term
 SEE: setVariationalRefinementDelta</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVariationalRefinementGamma--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariationalRefinementGamma</h4>
<pre>public&nbsp;float&nbsp;getVariationalRefinementGamma()</pre>
<div class="block">Weight of the gradient constancy term
 SEE: setVariationalRefinementGamma</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getVariationalRefinementIterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariationalRefinementIterations</h4>
<pre>public&nbsp;int&nbsp;getVariationalRefinementIterations()</pre>
<div class="block">Number of fixed point iterations of variational refinement per scale. Set to zero to
         disable variational refinement completely. Higher values will typically result in more smooth and
         high-quality flow.
 SEE: setGradientDescentIterations</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setFinestScale-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinestScale</h4>
<pre>public&nbsp;void&nbsp;setFinestScale(int&nbsp;val)</pre>
<div class="block">getFinestScale SEE: getFinestScale</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setGradientDescentIterations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGradientDescentIterations</h4>
<pre>public&nbsp;void&nbsp;setGradientDescentIterations(int&nbsp;val)</pre>
<div class="block">getGradientDescentIterations SEE: getGradientDescentIterations</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setPatchSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPatchSize</h4>
<pre>public&nbsp;void&nbsp;setPatchSize(int&nbsp;val)</pre>
<div class="block">getPatchSize SEE: getPatchSize</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setPatchStride-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPatchStride</h4>
<pre>public&nbsp;void&nbsp;setPatchStride(int&nbsp;val)</pre>
<div class="block">getPatchStride SEE: getPatchStride</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setUseMeanNormalization-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseMeanNormalization</h4>
<pre>public&nbsp;void&nbsp;setUseMeanNormalization(boolean&nbsp;val)</pre>
<div class="block">getUseMeanNormalization SEE: getUseMeanNormalization</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setUseSpatialPropagation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseSpatialPropagation</h4>
<pre>public&nbsp;void&nbsp;setUseSpatialPropagation(boolean&nbsp;val)</pre>
<div class="block">getUseSpatialPropagation SEE: getUseSpatialPropagation</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setVariationalRefinementAlpha-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariationalRefinementAlpha</h4>
<pre>public&nbsp;void&nbsp;setVariationalRefinementAlpha(float&nbsp;val)</pre>
<div class="block">getVariationalRefinementAlpha SEE: getVariationalRefinementAlpha</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setVariationalRefinementDelta-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariationalRefinementDelta</h4>
<pre>public&nbsp;void&nbsp;setVariationalRefinementDelta(float&nbsp;val)</pre>
<div class="block">getVariationalRefinementDelta SEE: getVariationalRefinementDelta</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setVariationalRefinementGamma-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariationalRefinementGamma</h4>
<pre>public&nbsp;void&nbsp;setVariationalRefinementGamma(float&nbsp;val)</pre>
<div class="block">getVariationalRefinementGamma SEE: getVariationalRefinementGamma</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setVariationalRefinementIterations-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setVariationalRefinementIterations</h4>
<pre>public&nbsp;void&nbsp;setVariationalRefinementIterations(int&nbsp;val)</pre>
<div class="block">getGradientDescentIterations SEE: getGradientDescentIterations</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/DISOpticalFlow.html" target="_top">Frames</a></li>
<li><a href="DISOpticalFlow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

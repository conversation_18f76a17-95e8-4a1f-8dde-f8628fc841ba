<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:55 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MSER (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MSER (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/KAZE.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/MSER.html" target="_top">Frames</a></li>
<li><a href="MSER.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class MSER" class="title">Class MSER</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.MSER</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MSER</span>
extends <a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></pre>
<div class="block">Maximally stable extremal region extractor

 The class encapsulates all the parameters of the %MSER extraction algorithm (see [wiki
 article](http://en.wikipedia.org/wiki/Maximally_stable_extremal_regions)).

 <ul>
   <li>
  there are two different implementation of %MSER: one for grey image, one for color image
   </li>
 </ul>

 <ul>
   <li>
  the grey image algorithm is taken from: CITE: nister2008linear ;  the paper claims to be faster
 than union-find method; it actually get 1.5~2m/s on my centrino L7200 1.2GHz laptop.
   </li>
 </ul>

 <ul>
   <li>
  the color image algorithm is taken from: CITE: forssen2007maximally ; it should be much slower
 than grey image method ( 3~4 times )
   </li>
 </ul>

 <ul>
   <li>
  (Python) A complete example showing the use of the %MSER detector can be found at samples/python/mser.py
   </li>
 </ul></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create--">create</a></span>()</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-">create</a></span>(int&nbsp;delta)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-int-">create</a></span>(int&nbsp;delta,
      int&nbsp;min_area)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-int-int-">create</a></span>(int&nbsp;delta,
      int&nbsp;min_area,
      int&nbsp;max_area)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-int-int-double-">create</a></span>(int&nbsp;delta,
      int&nbsp;min_area,
      int&nbsp;max_area,
      double&nbsp;max_variation)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-int-int-double-double-">create</a></span>(int&nbsp;delta,
      int&nbsp;min_area,
      int&nbsp;max_area,
      double&nbsp;max_variation,
      double&nbsp;min_diversity)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-int-int-double-double-int-">create</a></span>(int&nbsp;delta,
      int&nbsp;min_area,
      int&nbsp;max_area,
      double&nbsp;max_variation,
      double&nbsp;min_diversity,
      int&nbsp;max_evolution)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-int-int-double-double-int-double-">create</a></span>(int&nbsp;delta,
      int&nbsp;min_area,
      int&nbsp;max_area,
      double&nbsp;max_variation,
      double&nbsp;min_diversity,
      int&nbsp;max_evolution,
      double&nbsp;area_threshold)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-int-int-double-double-int-double-double-">create</a></span>(int&nbsp;delta,
      int&nbsp;min_area,
      int&nbsp;max_area,
      double&nbsp;max_variation,
      double&nbsp;min_diversity,
      int&nbsp;max_evolution,
      double&nbsp;area_threshold,
      double&nbsp;min_margin)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#create-int-int-int-double-double-int-double-double-int-">create</a></span>(int&nbsp;delta,
      int&nbsp;min_area,
      int&nbsp;max_area,
      double&nbsp;max_variation,
      double&nbsp;min_diversity,
      int&nbsp;max_evolution,
      double&nbsp;area_threshold,
      double&nbsp;min_margin,
      int&nbsp;edge_blur_size)</code>
<div class="block">Full constructor for %MSER detector</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#detectRegions-org.opencv.core.Mat-java.util.List-org.opencv.core.MatOfRect-">detectRegions</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&gt;&nbsp;msers,
             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes)</code>
<div class="block">Detect %MSER regions</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#getDefaultName--">getDefaultName</a></span>()</code>
<div class="block">Returns the algorithm string identifier.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#getDelta--">getDelta</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#getMaxArea--">getMaxArea</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#getMinArea--">getMinArea</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#getPass2Only--">getPass2Only</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#setDelta-int-">setDelta</a></span>(int&nbsp;delta)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#setMaxArea-int-">setMaxArea</a></span>(int&nbsp;maxArea)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#setMinArea-int-">setMinArea</a></span>(int&nbsp;minArea)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/MSER.html#setPass2Only-boolean-">setPass2Only</a></span>(boolean&nbsp;f)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.features2d.Feature2D">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.features2d.<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="../../../org/opencv/features2d/Feature2D.html#compute-java.util.List-java.util.List-java.util.List-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#defaultNorm--">defaultNorm</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorSize--">descriptorSize</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorType--">descriptorType</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-boolean-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#empty--">empty</a>, <a href="../../../org/opencv/features2d/Feature2D.html#read-java.lang.String-">read</a>, <a href="../../../org/opencv/features2d/Feature2D.html#write-java.lang.String-">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create()</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta,
                          int&nbsp;min_area)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta,
                          int&nbsp;min_area,
                          int&nbsp;max_area)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta,
                          int&nbsp;min_area,
                          int&nbsp;max_area,
                          double&nbsp;max_variation)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta,
                          int&nbsp;min_area,
                          int&nbsp;max_area,
                          double&nbsp;max_variation,
                          double&nbsp;min_diversity)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-double-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta,
                          int&nbsp;min_area,
                          int&nbsp;max_area,
                          double&nbsp;max_variation,
                          double&nbsp;min_diversity,
                          int&nbsp;max_evolution)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dd><code>max_evolution</code> - for color image, the evolution steps</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-double-double-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta,
                          int&nbsp;min_area,
                          int&nbsp;max_area,
                          double&nbsp;max_variation,
                          double&nbsp;min_diversity,
                          int&nbsp;max_evolution,
                          double&nbsp;area_threshold)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dd><code>max_evolution</code> - for color image, the evolution steps</dd>
<dd><code>area_threshold</code> - for color image, the area threshold to cause re-initialize</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-double-double-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta,
                          int&nbsp;min_area,
                          int&nbsp;max_area,
                          double&nbsp;max_variation,
                          double&nbsp;min_diversity,
                          int&nbsp;max_evolution,
                          double&nbsp;area_threshold,
                          double&nbsp;min_margin)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dd><code>max_evolution</code> - for color image, the evolution steps</dd>
<dd><code>area_threshold</code> - for color image, the area threshold to cause re-initialize</dd>
<dd><code>min_margin</code> - for color image, ignore too small margin</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-double-double-int-double-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a>&nbsp;create(int&nbsp;delta,
                          int&nbsp;min_area,
                          int&nbsp;max_area,
                          double&nbsp;max_variation,
                          double&nbsp;min_diversity,
                          int&nbsp;max_evolution,
                          double&nbsp;area_threshold,
                          double&nbsp;min_margin,
                          int&nbsp;edge_blur_size)</pre>
<div class="block">Full constructor for %MSER detector</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - it compares \((size_{i}-size_{i-delta})/size_{i-delta}\)</dd>
<dd><code>min_area</code> - prune the area which smaller than minArea</dd>
<dd><code>max_area</code> - prune the area which bigger than maxArea</dd>
<dd><code>max_variation</code> - prune the area have similar size to its children</dd>
<dd><code>min_diversity</code> - for color image, trace back to cut off mser with diversity less than min_diversity</dd>
<dd><code>max_evolution</code> - for color image, the evolution steps</dd>
<dd><code>area_threshold</code> - for color image, the area threshold to cause re-initialize</dd>
<dd><code>min_margin</code> - for color image, ignore too small margin</dd>
<dd><code>edge_blur_size</code> - for color image, the aperture size for edge blur</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detectRegions-org.opencv.core.Mat-java.util.List-org.opencv.core.MatOfRect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectRegions</h4>
<pre>public&nbsp;void&nbsp;detectRegions(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                          java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&gt;&nbsp;msers,
                          <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes)</pre>
<div class="block">Detect %MSER regions</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image (8UC1, 8UC3 or 8UC4, must be greater or equal than 3x3)</dd>
<dd><code>msers</code> - resulting list of point sets</dd>
<dd><code>bboxes</code> - resulting bounding boxes</dd>
</dl>
</li>
</ul>
<a name="getDefaultName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/features2d/Feature2D.html#getDefaultName--">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDelta--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDelta</h4>
<pre>public&nbsp;int&nbsp;getDelta()</pre>
</li>
</ul>
<a name="getMaxArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxArea</h4>
<pre>public&nbsp;int&nbsp;getMaxArea()</pre>
</li>
</ul>
<a name="getMinArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinArea</h4>
<pre>public&nbsp;int&nbsp;getMinArea()</pre>
</li>
</ul>
<a name="getPass2Only--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPass2Only</h4>
<pre>public&nbsp;boolean&nbsp;getPass2Only()</pre>
</li>
</ul>
<a name="setDelta-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDelta</h4>
<pre>public&nbsp;void&nbsp;setDelta(int&nbsp;delta)</pre>
</li>
</ul>
<a name="setMaxArea-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxArea</h4>
<pre>public&nbsp;void&nbsp;setMaxArea(int&nbsp;maxArea)</pre>
</li>
</ul>
<a name="setMinArea-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinArea</h4>
<pre>public&nbsp;void&nbsp;setMinArea(int&nbsp;minArea)</pre>
</li>
</ul>
<a name="setPass2Only-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPass2Only</h4>
<pre>public&nbsp;void&nbsp;setPass2Only(boolean&nbsp;f)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/KAZE.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/MSER.html" target="_top">Frames</a></li>
<li><a href="MSER.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

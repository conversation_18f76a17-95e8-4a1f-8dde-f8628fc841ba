<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:57 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MatOfKeyPoint (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MatOfKeyPoint (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":9,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/MatOfInt4.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/MatOfKeyPoint.html" target="_top">Frames</a></li>
<li><a href="MatOfKeyPoint.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.org.opencv.core.Mat">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.opencv.core.Mat">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.core</div>
<h2 title="Class MatOfKeyPoint" class="title">Class MatOfKeyPoint</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">org.opencv.core.Mat</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.core.MatOfKeyPoint</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MatOfKeyPoint</span>
extends <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.opencv.core.Mat">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></h3>
<code><a href="../../../org/opencv/core/Mat.Atable.html" title="interface in org.opencv.core">Mat.Atable</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;, <a href="../../../org/opencv/core/Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="../../../org/opencv/core/Mat.Tuple2.html" title="type parameter in Mat.Tuple2">T</a>&gt;, <a href="../../../org/opencv/core/Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="../../../org/opencv/core/Mat.Tuple3.html" title="type parameter in Mat.Tuple3">T</a>&gt;, <a href="../../../org/opencv/core/Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="../../../org/opencv/core/Mat.Tuple4.html" title="type parameter in Mat.Tuple4">T</a>&gt;</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.core.Mat">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></h3>
<code><a href="../../../org/opencv/core/Mat.html#nativeObj">nativeObj</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#MatOfKeyPoint--">MatOfKeyPoint</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#MatOfKeyPoint-org.opencv.core.KeyPoint...-">MatOfKeyPoint</a></span>(<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>...&nbsp;a)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#MatOfKeyPoint-org.opencv.core.Mat-">MatOfKeyPoint</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#alloc-int-">alloc</a></span>(int&nbsp;elemNumber)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#fromArray-org.opencv.core.KeyPoint...-">fromArray</a></span>(<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>...&nbsp;a)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#fromList-java.util.List-">fromList</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>&gt;&nbsp;lkp)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#fromNativeAddr-long-">fromNativeAddr</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#toArray--">toArray</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/MatOfKeyPoint.html#toList--">toList</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Mat">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></h3>
<code><a href="../../../org/opencv/core/Mat.html#adjustROI-int-int-int-int-">adjustROI</a>, <a href="../../../org/opencv/core/Mat.html#assignTo-org.opencv.core.Mat-">assignTo</a>, <a href="../../../org/opencv/core/Mat.html#assignTo-org.opencv.core.Mat-int-">assignTo</a>, <a href="../../../org/opencv/core/Mat.html#at-java.lang.Class-int:A-">at</a>, <a href="../../../org/opencv/core/Mat.html#at-java.lang.Class-int-int-">at</a>, <a href="../../../org/opencv/core/Mat.html#channels--">channels</a>, <a href="../../../org/opencv/core/Mat.html#checkVector-int-">checkVector</a>, <a href="../../../org/opencv/core/Mat.html#checkVector-int-int-">checkVector</a>, <a href="../../../org/opencv/core/Mat.html#checkVector-int-int-boolean-">checkVector</a>, <a href="../../../org/opencv/core/Mat.html#clone--">clone</a>, <a href="../../../org/opencv/core/Mat.html#col-int-">col</a>, <a href="../../../org/opencv/core/Mat.html#colRange-int-int-">colRange</a>, <a href="../../../org/opencv/core/Mat.html#colRange-org.opencv.core.Range-">colRange</a>, <a href="../../../org/opencv/core/Mat.html#cols--">cols</a>, <a href="../../../org/opencv/core/Mat.html#convertTo-org.opencv.core.Mat-int-">convertTo</a>, <a href="../../../org/opencv/core/Mat.html#convertTo-org.opencv.core.Mat-int-double-">convertTo</a>, <a href="../../../org/opencv/core/Mat.html#convertTo-org.opencv.core.Mat-int-double-double-">convertTo</a>, <a href="../../../org/opencv/core/Mat.html#copySize-org.opencv.core.Mat-">copySize</a>, <a href="../../../org/opencv/core/Mat.html#copyTo-org.opencv.core.Mat-">copyTo</a>, <a href="../../../org/opencv/core/Mat.html#copyTo-org.opencv.core.Mat-org.opencv.core.Mat-">copyTo</a>, <a href="../../../org/opencv/core/Mat.html#create-int:A-int-">create</a>, <a href="../../../org/opencv/core/Mat.html#create-int-int-int-">create</a>, <a href="../../../org/opencv/core/Mat.html#create-org.opencv.core.Size-int-">create</a>, <a href="../../../org/opencv/core/Mat.html#cross-org.opencv.core.Mat-">cross</a>, <a href="../../../org/opencv/core/Mat.html#dataAddr--">dataAddr</a>, <a href="../../../org/opencv/core/Mat.html#depth--">depth</a>, <a href="../../../org/opencv/core/Mat.html#diag--">diag</a>, <a href="../../../org/opencv/core/Mat.html#diag-int-">diag</a>, <a href="../../../org/opencv/core/Mat.html#diag-org.opencv.core.Mat-">diag</a>, <a href="../../../org/opencv/core/Mat.html#dims--">dims</a>, <a href="../../../org/opencv/core/Mat.html#dot-org.opencv.core.Mat-">dot</a>, <a href="../../../org/opencv/core/Mat.html#dump--">dump</a>, <a href="../../../org/opencv/core/Mat.html#elemSize--">elemSize</a>, <a href="../../../org/opencv/core/Mat.html#elemSize1--">elemSize1</a>, <a href="../../../org/opencv/core/Mat.html#empty--">empty</a>, <a href="../../../org/opencv/core/Mat.html#eye-int-int-int-">eye</a>, <a href="../../../org/opencv/core/Mat.html#eye-org.opencv.core.Size-int-">eye</a>, <a href="../../../org/opencv/core/Mat.html#get-int:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int:A-byte:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int:A-double:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int:A-float:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int:A-int:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int:A-short:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int-int-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int-int-byte:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int-int-double:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int-int-float:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int-int-int:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#get-int-int-short:A-">get</a>, <a href="../../../org/opencv/core/Mat.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Mat.html#height--">height</a>, <a href="../../../org/opencv/core/Mat.html#inv--">inv</a>, <a href="../../../org/opencv/core/Mat.html#inv-int-">inv</a>, <a href="../../../org/opencv/core/Mat.html#isContinuous--">isContinuous</a>, <a href="../../../org/opencv/core/Mat.html#isSubmatrix--">isSubmatrix</a>, <a href="../../../org/opencv/core/Mat.html#locateROI-org.opencv.core.Size-org.opencv.core.Point-">locateROI</a>, <a href="../../../org/opencv/core/Mat.html#matMul-org.opencv.core.Mat-">matMul</a>, <a href="../../../org/opencv/core/Mat.html#mul-org.opencv.core.Mat-">mul</a>, <a href="../../../org/opencv/core/Mat.html#mul-org.opencv.core.Mat-double-">mul</a>, <a href="../../../org/opencv/core/Mat.html#ones-int:A-int-">ones</a>, <a href="../../../org/opencv/core/Mat.html#ones-int-int-int-">ones</a>, <a href="../../../org/opencv/core/Mat.html#ones-org.opencv.core.Size-int-">ones</a>, <a href="../../../org/opencv/core/Mat.html#push_back-org.opencv.core.Mat-">push_back</a>, <a href="../../../org/opencv/core/Mat.html#put-int:A-byte:A-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int:A-byte:A-int-int-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int:A-double...-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int:A-float:A-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int:A-int:A-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int:A-short:A-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int-int-byte:A-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int-int-byte:A-int-int-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int-int-double...-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int-int-float:A-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int-int-int:A-">put</a>, <a href="../../../org/opencv/core/Mat.html#put-int-int-short:A-">put</a>, <a href="../../../org/opencv/core/Mat.html#release--">release</a>, <a href="../../../org/opencv/core/Mat.html#reshape-int-">reshape</a>, <a href="../../../org/opencv/core/Mat.html#reshape-int-int-">reshape</a>, <a href="../../../org/opencv/core/Mat.html#reshape-int-int:A-">reshape</a>, <a href="../../../org/opencv/core/Mat.html#row-int-">row</a>, <a href="../../../org/opencv/core/Mat.html#rowRange-int-int-">rowRange</a>, <a href="../../../org/opencv/core/Mat.html#rowRange-org.opencv.core.Range-">rowRange</a>, <a href="../../../org/opencv/core/Mat.html#rows--">rows</a>, <a href="../../../org/opencv/core/Mat.html#setTo-org.opencv.core.Mat-">setTo</a>, <a href="../../../org/opencv/core/Mat.html#setTo-org.opencv.core.Mat-org.opencv.core.Mat-">setTo</a>, <a href="../../../org/opencv/core/Mat.html#setTo-org.opencv.core.Scalar-">setTo</a>, <a href="../../../org/opencv/core/Mat.html#setTo-org.opencv.core.Scalar-org.opencv.core.Mat-">setTo</a>, <a href="../../../org/opencv/core/Mat.html#size--">size</a>, <a href="../../../org/opencv/core/Mat.html#size-int-">size</a>, <a href="../../../org/opencv/core/Mat.html#step1--">step1</a>, <a href="../../../org/opencv/core/Mat.html#step1-int-">step1</a>, <a href="../../../org/opencv/core/Mat.html#submat-int-int-int-int-">submat</a>, <a href="../../../org/opencv/core/Mat.html#submat-org.opencv.core.Range:A-">submat</a>, <a href="../../../org/opencv/core/Mat.html#submat-org.opencv.core.Range-org.opencv.core.Range-">submat</a>, <a href="../../../org/opencv/core/Mat.html#submat-org.opencv.core.Rect-">submat</a>, <a href="../../../org/opencv/core/Mat.html#t--">t</a>, <a href="../../../org/opencv/core/Mat.html#toString--">toString</a>, <a href="../../../org/opencv/core/Mat.html#total--">total</a>, <a href="../../../org/opencv/core/Mat.html#type--">type</a>, <a href="../../../org/opencv/core/Mat.html#width--">width</a>, <a href="../../../org/opencv/core/Mat.html#zeros-int:A-int-">zeros</a>, <a href="../../../org/opencv/core/Mat.html#zeros-int-int-int-">zeros</a>, <a href="../../../org/opencv/core/Mat.html#zeros-org.opencv.core.Size-int-">zeros</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="MatOfKeyPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MatOfKeyPoint</h4>
<pre>public&nbsp;MatOfKeyPoint()</pre>
</li>
</ul>
<a name="MatOfKeyPoint-org.opencv.core.KeyPoint...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MatOfKeyPoint</h4>
<pre>public&nbsp;MatOfKeyPoint(<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>...&nbsp;a)</pre>
</li>
</ul>
<a name="MatOfKeyPoint-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MatOfKeyPoint</h4>
<pre>public&nbsp;MatOfKeyPoint(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="alloc-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alloc</h4>
<pre>public&nbsp;void&nbsp;alloc(int&nbsp;elemNumber)</pre>
</li>
</ul>
<a name="fromArray-org.opencv.core.KeyPoint...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromArray</h4>
<pre>public&nbsp;void&nbsp;fromArray(<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>...&nbsp;a)</pre>
</li>
</ul>
<a name="fromList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromList</h4>
<pre>public&nbsp;void&nbsp;fromList(java.util.List&lt;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>&gt;&nbsp;lkp)</pre>
</li>
</ul>
<a name="fromNativeAddr-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromNativeAddr</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;fromNativeAddr(long&nbsp;addr)</pre>
</li>
</ul>
<a name="toArray--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>[]&nbsp;toArray()</pre>
</li>
</ul>
<a name="toList--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toList</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core">KeyPoint</a>&gt;&nbsp;toList()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/MatOfInt4.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/MatOfKeyPoint.html" target="_top">Frames</a></li>
<li><a href="MatOfKeyPoint.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.org.opencv.core.Mat">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.opencv.core.Mat">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

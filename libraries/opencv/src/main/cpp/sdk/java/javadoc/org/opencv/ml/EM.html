<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:56 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>EM (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="EM (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":9,"i9":9,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/EM.html" target="_top">Frames</a></li>
<li><a href="EM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class EM" class="title">Class EM</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.EM</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">EM</span>
extends <a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></pre>
<div class="block">The class implements the Expectation Maximization algorithm.

 SEE: REF: ml_intro_em</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#COV_MAT_DEFAULT">COV_MAT_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#COV_MAT_DIAGONAL">COV_MAT_DIAGONAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#COV_MAT_GENERIC">COV_MAT_GENERIC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#COV_MAT_SPHERICAL">COV_MAT_SPHERICAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#DEFAULT_MAX_ITERS">DEFAULT_MAX_ITERS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#DEFAULT_NCLUSTERS">DEFAULT_NCLUSTERS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#START_AUTO_STEP">START_AUTO_STEP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#START_E_STEP">START_E_STEP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#START_M_STEP">START_M_STEP</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#create--">create</a></span>()</code>
<div class="block">Creates empty %EM model.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#getClustersNumber--">getClustersNumber</a></span>()</code>
<div class="block">SEE: setClustersNumber</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#getCovarianceMatrixType--">getCovarianceMatrixType</a></span>()</code>
<div class="block">SEE: setCovarianceMatrixType</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#getCovs-java.util.List-">getCovs</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;covs)</code>
<div class="block">Returns covariation matrices

     Returns vector of covariation matrices.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#getMeans--">getMeans</a></span>()</code>
<div class="block">Returns the cluster centers (means of the Gaussian mixture)

     Returns matrix with the number of rows equal to the number of mixtures and number of columns
     equal to the space dimensionality.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#getTermCriteria--">getTermCriteria</a></span>()</code>
<div class="block">SEE: setTermCriteria</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#getWeights--">getWeights</a></span>()</code>
<div class="block">Returns weights of the mixtures

     Returns vector with the number of elements equal to the number of mixtures.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath)</code>
<div class="block">Loads and creates a serialized EM from a file

 Use EM::save to serialize and store an EM to disk.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#load-java.lang.String-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath,
    java.lang.String&nbsp;nodeName)</code>
<div class="block">Loads and creates a serialized EM from a file

 Use EM::save to serialize and store an EM to disk.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#predict-org.opencv.core.Mat-">predict</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</code>
<div class="block">Returns posterior probabilities for the provided samples</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</code>
<div class="block">Returns posterior probabilities for the provided samples</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
       int&nbsp;flags)</code>
<div class="block">Returns posterior probabilities for the provided samples</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#predict2-org.opencv.core.Mat-org.opencv.core.Mat-">predict2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sample,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</code>
<div class="block">Returns a likelihood logarithm value and an index of the most probable mixture component
     for the given sample.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#setClustersNumber-int-">setClustersNumber</a></span>(int&nbsp;val)</code>
<div class="block">getClustersNumber SEE: getClustersNumber</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#setCovarianceMatrixType-int-">setCovarianceMatrixType</a></span>(int&nbsp;val)</code>
<div class="block">getCovarianceMatrixType SEE: getCovarianceMatrixType</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#setTermCriteria-org.opencv.core.TermCriteria-">setTermCriteria</a></span>(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainE-org.opencv.core.Mat-org.opencv.core.Mat-">trainE</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainE</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainE</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainE</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainE</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainE</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainEM-org.opencv.core.Mat-">trainEM</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainEM-org.opencv.core.Mat-org.opencv.core.Mat-">trainEM</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainEM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainEM</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainEM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainEM</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainM-org.opencv.core.Mat-org.opencv.core.Mat-">trainM</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainM</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainM</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/EM.html#trainM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">trainM</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</code>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#calcError-org.opencv.ml.TrainData-boolean-org.opencv.core.Mat-">calcError</a>, <a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-int-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="COV_MAT_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COV_MAT_DEFAULT</h4>
<pre>public static final&nbsp;int COV_MAT_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.COV_MAT_DEFAULT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COV_MAT_DIAGONAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COV_MAT_DIAGONAL</h4>
<pre>public static final&nbsp;int COV_MAT_DIAGONAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.COV_MAT_DIAGONAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COV_MAT_GENERIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COV_MAT_GENERIC</h4>
<pre>public static final&nbsp;int COV_MAT_GENERIC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.COV_MAT_GENERIC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COV_MAT_SPHERICAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COV_MAT_SPHERICAL</h4>
<pre>public static final&nbsp;int COV_MAT_SPHERICAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.COV_MAT_SPHERICAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DEFAULT_MAX_ITERS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_MAX_ITERS</h4>
<pre>public static final&nbsp;int DEFAULT_MAX_ITERS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.DEFAULT_MAX_ITERS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DEFAULT_NCLUSTERS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_NCLUSTERS</h4>
<pre>public static final&nbsp;int DEFAULT_NCLUSTERS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.DEFAULT_NCLUSTERS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="START_AUTO_STEP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_AUTO_STEP</h4>
<pre>public static final&nbsp;int START_AUTO_STEP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.START_AUTO_STEP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="START_E_STEP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_E_STEP</h4>
<pre>public static final&nbsp;int START_E_STEP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.START_E_STEP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="START_M_STEP">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>START_M_STEP</h4>
<pre>public static final&nbsp;int START_M_STEP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.EM.START_M_STEP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a>&nbsp;create()</pre>
<div class="block">Creates empty %EM model.
     The model should be trained then using StatModel::train(traindata, flags) method. Alternatively, you
     can use one of the EM::train\* methods or load it from file using Algorithm::load&lt;EM&gt;(filename).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getClustersNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClustersNumber</h4>
<pre>public&nbsp;int&nbsp;getClustersNumber()</pre>
<div class="block">SEE: setClustersNumber</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getCovarianceMatrixType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCovarianceMatrixType</h4>
<pre>public&nbsp;int&nbsp;getCovarianceMatrixType()</pre>
<div class="block">SEE: setCovarianceMatrixType</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getCovs-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCovs</h4>
<pre>public&nbsp;void&nbsp;getCovs(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;covs)</pre>
<div class="block">Returns covariation matrices

     Returns vector of covariation matrices. Number of matrices is the number of gaussian mixtures,
     each matrix is a square floating-point matrix NxN, where N is the space dimensionality.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>covs</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="getMeans--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMeans</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getMeans()</pre>
<div class="block">Returns the cluster centers (means of the Gaussian mixture)

     Returns matrix with the number of rows equal to the number of mixtures and number of columns
     equal to the space dimensionality.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTermCriteria--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTermCriteria</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;getTermCriteria()</pre>
<div class="block">SEE: setTermCriteria</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getWeights--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getWeights()</pre>
<div class="block">Returns weights of the mixtures

     Returns vector with the number of elements equal to the number of mixtures.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a>&nbsp;load(java.lang.String&nbsp;filepath)</pre>
<div class="block">Loads and creates a serialized EM from a file

 Use EM::save to serialize and store an EM to disk.
 Load the EM from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized EM</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a>&nbsp;load(java.lang.String&nbsp;filepath,
                      java.lang.String&nbsp;nodeName)</pre>
<div class="block">Loads and creates a serialized EM from a file

 Use EM::save to serialize and store an EM to disk.
 Load the EM from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized EM</dd>
<dd><code>nodeName</code> - name of node containing the classifier</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="predict-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predict</h4>
<pre>public&nbsp;float&nbsp;predict(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</pre>
<div class="block">Returns posterior probabilities for the provided samples</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - The input samples, floating-point matrix
     posterior probabilities for each sample from the input</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="predict-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predict</h4>
<pre>public&nbsp;float&nbsp;predict(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</pre>
<div class="block">Returns posterior probabilities for the provided samples</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - The input samples, floating-point matrix</dd>
<dd><code>results</code> - The optional output \( nSamples \times nClusters\) matrix of results. It contains
     posterior probabilities for each sample from the input</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predict</h4>
<pre>public&nbsp;float&nbsp;predict(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
                     int&nbsp;flags)</pre>
<div class="block">Returns posterior probabilities for the provided samples</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - The input samples, floating-point matrix</dd>
<dd><code>results</code> - The optional output \( nSamples \times nClusters\) matrix of results. It contains
     posterior probabilities for each sample from the input</dd>
<dd><code>flags</code> - This parameter will be ignored</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="predict2-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predict2</h4>
<pre>public&nbsp;double[]&nbsp;predict2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sample,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</pre>
<div class="block">Returns a likelihood logarithm value and an index of the most probable mixture component
     for the given sample.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sample</code> - A sample for classification. It should be a one-channel matrix of
         \(1 \times dims\) or \(dims \times 1\) size.</dd>
<dd><code>probs</code> - Optional output matrix that contains posterior probabilities of each component
         given the sample. It has \(1 \times nclusters\) size and CV_64FC1 type.

     The method returns a two-element double vector. Zero element is a likelihood logarithm value for
     the sample. First element is an index of the most probable mixture component for the given
     sample.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setClustersNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClustersNumber</h4>
<pre>public&nbsp;void&nbsp;setClustersNumber(int&nbsp;val)</pre>
<div class="block">getClustersNumber SEE: getClustersNumber</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setCovarianceMatrixType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCovarianceMatrixType</h4>
<pre>public&nbsp;void&nbsp;setCovarianceMatrixType(int&nbsp;val)</pre>
<div class="block">getCovarianceMatrixType SEE: getCovarianceMatrixType</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTermCriteria-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTermCriteria</h4>
<pre>public&nbsp;void&nbsp;setTermCriteria(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</pre>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainE-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainE</h4>
<pre>public&nbsp;boolean&nbsp;trainE(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainE</h4>
<pre>public&nbsp;boolean&nbsp;trainE(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainE</h4>
<pre>public&nbsp;boolean&nbsp;trainE(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.</dd>
<dd><code>weights0</code> - Initial weights \(\pi_k\) of mixture components. It should be a one-channel
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainE</h4>
<pre>public&nbsp;boolean&nbsp;trainE(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.</dd>
<dd><code>weights0</code> - Initial weights \(\pi_k\) of mixture components. It should be a one-channel
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainE</h4>
<pre>public&nbsp;boolean&nbsp;trainE(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.</dd>
<dd><code>weights0</code> - Initial weights \(\pi_k\) of mixture components. It should be a one-channel
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainE-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainE</h4>
<pre>public&nbsp;boolean&nbsp;trainE(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;means0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covs0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;weights0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. You need to provide initial means \(a_k\) of
     mixture components. Optionally you can pass initial weights \(\pi_k\) and covariance matrices
     \(S_k\) of mixture components.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>means0</code> - Initial means \(a_k\) of mixture components. It is a one-channel matrix of
         \(nclusters \times dims\) size. If the matrix does not have CV_64F type it will be
         converted to the inner matrix of such type for the further computing.</dd>
<dd><code>covs0</code> - The vector of initial covariance matrices \(S_k\) of mixture components. Each of
         covariance matrices is a one-channel matrix of \(dims \times dims\) size. If the matrices
         do not have CV_64F type they will be converted to the inner matrices of such type for the
         further computing.</dd>
<dd><code>weights0</code> - Initial weights \(\pi_k\) of mixture components. It should be a one-channel
         floating-point matrix with \(1 \times nclusters\) or \(nclusters \times 1\) size.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.</dd>
<dd><code>probs</code> - The optional output matrix that contains posterior probabilities of each Gaussian
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainEM-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainEM</h4>
<pre>public&nbsp;boolean&nbsp;trainEM(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. Initial values of the model parameters will be
     estimated by the k-means algorithm.

     Unlike many of the ML models, %EM is an unsupervised learning algorithm and it does not take
     responses (class labels or function values) as input. Instead, it computes the *Maximum
     Likelihood Estimate* of the Gaussian mixture parameters from an input sample set, stores all the
     parameters inside the structure: \(p_{i,k}\) in probs, \(a_k\) in means , \(S_k\) in
     covs[k], \(\pi_k\) in weights , and optionally computes the output "class label" for each
     sample: \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most
     probable mixture component for each sample).

     The trained model can be used further for prediction, just like any other classifier. The
     trained model is similar to the NormalBayesClassifier.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainEM-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainEM</h4>
<pre>public&nbsp;boolean&nbsp;trainEM(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. Initial values of the model parameters will be
     estimated by the k-means algorithm.

     Unlike many of the ML models, %EM is an unsupervised learning algorithm and it does not take
     responses (class labels or function values) as input. Instead, it computes the *Maximum
     Likelihood Estimate* of the Gaussian mixture parameters from an input sample set, stores all the
     parameters inside the structure: \(p_{i,k}\) in probs, \(a_k\) in means , \(S_k\) in
     covs[k], \(\pi_k\) in weights , and optionally computes the output "class label" for each
     sample: \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most
     probable mixture component for each sample).

     The trained model can be used further for prediction, just like any other classifier. The
     trained model is similar to the NormalBayesClassifier.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainEM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainEM</h4>
<pre>public&nbsp;boolean&nbsp;trainEM(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. Initial values of the model parameters will be
     estimated by the k-means algorithm.

     Unlike many of the ML models, %EM is an unsupervised learning algorithm and it does not take
     responses (class labels or function values) as input. Instead, it computes the *Maximum
     Likelihood Estimate* of the Gaussian mixture parameters from an input sample set, stores all the
     parameters inside the structure: \(p_{i,k}\) in probs, \(a_k\) in means , \(S_k\) in
     covs[k], \(\pi_k\) in weights , and optionally computes the output "class label" for each
     sample: \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most
     probable mixture component for each sample).

     The trained model can be used further for prediction, just like any other classifier. The
     trained model is similar to the NormalBayesClassifier.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainEM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainEM</h4>
<pre>public&nbsp;boolean&nbsp;trainEM(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Expectation step. Initial values of the model parameters will be
     estimated by the k-means algorithm.

     Unlike many of the ML models, %EM is an unsupervised learning algorithm and it does not take
     responses (class labels or function values) as input. Instead, it computes the *Maximum
     Likelihood Estimate* of the Gaussian mixture parameters from an input sample set, stores all the
     parameters inside the structure: \(p_{i,k}\) in probs, \(a_k\) in means , \(S_k\) in
     covs[k], \(\pi_k\) in weights , and optionally computes the output "class label" for each
     sample: \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most
     probable mixture component for each sample).

     The trained model can be used further for prediction, just like any other classifier. The
     trained model is similar to the NormalBayesClassifier.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.</dd>
<dd><code>probs</code> - The optional output matrix that contains posterior probabilities of each Gaussian
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainM-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainM</h4>
<pre>public&nbsp;boolean&nbsp;trainM(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Maximization step. You need to provide initial probabilities
     \(p_{i,k}\) to use this option.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>probs0</code> - the probabilities
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainM</h4>
<pre>public&nbsp;boolean&nbsp;trainM(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Maximization step. You need to provide initial probabilities
     \(p_{i,k}\) to use this option.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>probs0</code> - the probabilities</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trainM</h4>
<pre>public&nbsp;boolean&nbsp;trainM(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Maximization step. You need to provide initial probabilities
     \(p_{i,k}\) to use this option.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>probs0</code> - the probabilities</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="trainM-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>trainM</h4>
<pre>public&nbsp;boolean&nbsp;trainM(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs0,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;logLikelihoods,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;labels,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probs)</pre>
<div class="block">Estimate the Gaussian mixture parameters from a samples set.

     This variation starts with Maximization step. You need to provide initial probabilities
     \(p_{i,k}\) to use this option.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Samples from which the Gaussian mixture model will be estimated. It should be a
         one-channel matrix, each row of which is a sample. If the matrix does not have CV_64F type
         it will be converted to the inner matrix of such type for the further computing.</dd>
<dd><code>probs0</code> - the probabilities</dd>
<dd><code>logLikelihoods</code> - The optional output matrix that contains a likelihood logarithm value for
         each sample. It has \(nsamples \times 1\) size and CV_64FC1 type.</dd>
<dd><code>labels</code> - The optional output "class label" for each sample:
         \(\texttt{labels}_i=\texttt{arg max}_k(p_{i,k}), i=1..N\) (indices of the most probable
         mixture component for each sample). It has \(nsamples \times 1\) size and CV_32SC1 type.</dd>
<dd><code>probs</code> - The optional output matrix that contains posterior probabilities of each Gaussian
         mixture component given the each sample. It has \(nsamples \times nclusters\) size and
         CV_64FC1 type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/DTrees.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/EM.html" target="_top">Frames</a></li>
<li><a href="EM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

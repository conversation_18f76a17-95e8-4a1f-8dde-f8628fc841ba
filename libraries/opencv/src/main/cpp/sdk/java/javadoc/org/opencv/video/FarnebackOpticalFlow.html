<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:56 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>FarnebackOpticalFlow (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FarnebackOpticalFlow (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/KalmanFilter.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/FarnebackOpticalFlow.html" target="_top">Frames</a></li>
<li><a href="FarnebackOpticalFlow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.video</div>
<h2 title="Class FarnebackOpticalFlow" class="title">Class FarnebackOpticalFlow</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">org.opencv.video.DenseOpticalFlow</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.video.FarnebackOpticalFlow</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">FarnebackOpticalFlow</span>
extends <a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></pre>
<div class="block">Class computing a dense optical flow using the Gunnar Farneback's algorithm.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create--">create</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create-int-">create</a></span>(int&nbsp;numLevels)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create-int-double-">create</a></span>(int&nbsp;numLevels,
      double&nbsp;pyrScale)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create-int-double-boolean-">create</a></span>(int&nbsp;numLevels,
      double&nbsp;pyrScale,
      boolean&nbsp;fastPyramids)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create-int-double-boolean-int-">create</a></span>(int&nbsp;numLevels,
      double&nbsp;pyrScale,
      boolean&nbsp;fastPyramids,
      int&nbsp;winSize)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create-int-double-boolean-int-int-">create</a></span>(int&nbsp;numLevels,
      double&nbsp;pyrScale,
      boolean&nbsp;fastPyramids,
      int&nbsp;winSize,
      int&nbsp;numIters)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create-int-double-boolean-int-int-int-">create</a></span>(int&nbsp;numLevels,
      double&nbsp;pyrScale,
      boolean&nbsp;fastPyramids,
      int&nbsp;winSize,
      int&nbsp;numIters,
      int&nbsp;polyN)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create-int-double-boolean-int-int-int-double-">create</a></span>(int&nbsp;numLevels,
      double&nbsp;pyrScale,
      boolean&nbsp;fastPyramids,
      int&nbsp;winSize,
      int&nbsp;numIters,
      int&nbsp;polyN,
      double&nbsp;polySigma)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#create-int-double-boolean-int-int-int-double-int-">create</a></span>(int&nbsp;numLevels,
      double&nbsp;pyrScale,
      boolean&nbsp;fastPyramids,
      int&nbsp;winSize,
      int&nbsp;numIters,
      int&nbsp;polyN,
      double&nbsp;polySigma,
      int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#getFastPyramids--">getFastPyramids</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#getFlags--">getFlags</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#getNumIters--">getNumIters</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#getNumLevels--">getNumLevels</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#getPolyN--">getPolyN</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#getPolySigma--">getPolySigma</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#getPyrScale--">getPyrScale</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#getWinSize--">getWinSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#setFastPyramids-boolean-">setFastPyramids</a></span>(boolean&nbsp;fastPyramids)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#setFlags-int-">setFlags</a></span>(int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#setNumIters-int-">setNumIters</a></span>(int&nbsp;numIters)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#setNumLevels-int-">setNumLevels</a></span>(int&nbsp;numLevels)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#setPolyN-int-">setPolyN</a></span>(int&nbsp;polyN)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#setPolySigma-double-">setPolySigma</a></span>(double&nbsp;polySigma)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#setPyrScale-double-">setPyrScale</a></span>(double&nbsp;pyrScale)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/FarnebackOpticalFlow.html#setWinSize-int-">setWinSize</a></span>(int&nbsp;winSize)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.video.DenseOpticalFlow">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.video.<a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></h3>
<code><a href="../../../org/opencv/video/DenseOpticalFlow.html#calc-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">calc</a>, <a href="../../../org/opencv/video/DenseOpticalFlow.html#collectGarbage--">collectGarbage</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create()</pre>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create(int&nbsp;numLevels)</pre>
</li>
</ul>
<a name="create-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create(int&nbsp;numLevels,
                                          double&nbsp;pyrScale)</pre>
</li>
</ul>
<a name="create-int-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create(int&nbsp;numLevels,
                                          double&nbsp;pyrScale,
                                          boolean&nbsp;fastPyramids)</pre>
</li>
</ul>
<a name="create-int-double-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create(int&nbsp;numLevels,
                                          double&nbsp;pyrScale,
                                          boolean&nbsp;fastPyramids,
                                          int&nbsp;winSize)</pre>
</li>
</ul>
<a name="create-int-double-boolean-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create(int&nbsp;numLevels,
                                          double&nbsp;pyrScale,
                                          boolean&nbsp;fastPyramids,
                                          int&nbsp;winSize,
                                          int&nbsp;numIters)</pre>
</li>
</ul>
<a name="create-int-double-boolean-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create(int&nbsp;numLevels,
                                          double&nbsp;pyrScale,
                                          boolean&nbsp;fastPyramids,
                                          int&nbsp;winSize,
                                          int&nbsp;numIters,
                                          int&nbsp;polyN)</pre>
</li>
</ul>
<a name="create-int-double-boolean-int-int-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create(int&nbsp;numLevels,
                                          double&nbsp;pyrScale,
                                          boolean&nbsp;fastPyramids,
                                          int&nbsp;winSize,
                                          int&nbsp;numIters,
                                          int&nbsp;polyN,
                                          double&nbsp;polySigma)</pre>
</li>
</ul>
<a name="create-int-double-boolean-int-int-int-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/FarnebackOpticalFlow.html" title="class in org.opencv.video">FarnebackOpticalFlow</a>&nbsp;create(int&nbsp;numLevels,
                                          double&nbsp;pyrScale,
                                          boolean&nbsp;fastPyramids,
                                          int&nbsp;winSize,
                                          int&nbsp;numIters,
                                          int&nbsp;polyN,
                                          double&nbsp;polySigma,
                                          int&nbsp;flags)</pre>
</li>
</ul>
<a name="getFastPyramids--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFastPyramids</h4>
<pre>public&nbsp;boolean&nbsp;getFastPyramids()</pre>
</li>
</ul>
<a name="getFlags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFlags</h4>
<pre>public&nbsp;int&nbsp;getFlags()</pre>
</li>
</ul>
<a name="getNumIters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumIters</h4>
<pre>public&nbsp;int&nbsp;getNumIters()</pre>
</li>
</ul>
<a name="getNumLevels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumLevels</h4>
<pre>public&nbsp;int&nbsp;getNumLevels()</pre>
</li>
</ul>
<a name="getPolyN--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolyN</h4>
<pre>public&nbsp;int&nbsp;getPolyN()</pre>
</li>
</ul>
<a name="getPolySigma--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolySigma</h4>
<pre>public&nbsp;double&nbsp;getPolySigma()</pre>
</li>
</ul>
<a name="getPyrScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPyrScale</h4>
<pre>public&nbsp;double&nbsp;getPyrScale()</pre>
</li>
</ul>
<a name="getWinSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWinSize</h4>
<pre>public&nbsp;int&nbsp;getWinSize()</pre>
</li>
</ul>
<a name="setFastPyramids-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFastPyramids</h4>
<pre>public&nbsp;void&nbsp;setFastPyramids(boolean&nbsp;fastPyramids)</pre>
</li>
</ul>
<a name="setFlags-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFlags</h4>
<pre>public&nbsp;void&nbsp;setFlags(int&nbsp;flags)</pre>
</li>
</ul>
<a name="setNumIters-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumIters</h4>
<pre>public&nbsp;void&nbsp;setNumIters(int&nbsp;numIters)</pre>
</li>
</ul>
<a name="setNumLevels-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumLevels</h4>
<pre>public&nbsp;void&nbsp;setNumLevels(int&nbsp;numLevels)</pre>
</li>
</ul>
<a name="setPolyN-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPolyN</h4>
<pre>public&nbsp;void&nbsp;setPolyN(int&nbsp;polyN)</pre>
</li>
</ul>
<a name="setPolySigma-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPolySigma</h4>
<pre>public&nbsp;void&nbsp;setPolySigma(double&nbsp;polySigma)</pre>
</li>
</ul>
<a name="setPyrScale-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPyrScale</h4>
<pre>public&nbsp;void&nbsp;setPyrScale(double&nbsp;pyrScale)</pre>
</li>
</ul>
<a name="setWinSize-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setWinSize</h4>
<pre>public&nbsp;void&nbsp;setWinSize(int&nbsp;winSize)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/KalmanFilter.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/FarnebackOpticalFlow.html" target="_top">Frames</a></li>
<li><a href="FarnebackOpticalFlow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:58 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.dnn (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.opencv.dnn (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../org/opencv/features2d/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.opencv.dnn</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/ClassificationModel.html" title="class in org.opencv.dnn">ClassificationModel</a></td>
<td class="colLast">
<div class="block">This class represents high-level API for classification models.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/DetectionModel.html" title="class in org.opencv.dnn">DetectionModel</a></td>
<td class="colLast">
<div class="block">This class represents high-level API for object detection networks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/DictValue.html" title="class in org.opencv.dnn">DictValue</a></td>
<td class="colLast">
<div class="block">This struct stores the scalar value (or array) of one of the following type: double, cv::String or int64.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/Dnn.html" title="class in org.opencv.dnn">Dnn</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/KeypointsModel.html" title="class in org.opencv.dnn">KeypointsModel</a></td>
<td class="colLast">
<div class="block">This class represents high-level API for keypoints models

 KeypointsModel allows to set params for preprocessing input image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/Layer.html" title="class in org.opencv.dnn">Layer</a></td>
<td class="colLast">
<div class="block">This interface class allows to build new Layers - are building blocks of networks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></td>
<td class="colLast">
<div class="block">This class is presented high-level API for neural networks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></td>
<td class="colLast">
<div class="block">This class allows to create and manipulate comprehensive artificial neural networks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/SegmentationModel.html" title="class in org.opencv.dnn">SegmentationModel</a></td>
<td class="colLast">
<div class="block">This class represents high-level API for segmentation  models

 SegmentationModel allows to set params for preprocessing input image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/TextDetectionModel.html" title="class in org.opencv.dnn">TextDetectionModel</a></td>
<td class="colLast">
<div class="block">Base class for text detection networks</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/TextDetectionModel_DB.html" title="class in org.opencv.dnn">TextDetectionModel_DB</a></td>
<td class="colLast">
<div class="block">This class represents high-level API for text detection DL networks compatible with DB model.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn">TextDetectionModel_EAST</a></td>
<td class="colLast">
<div class="block">This class represents high-level API for text detection DL networks compatible with EAST model.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a></td>
<td class="colLast">
<div class="block">This class represents high-level API for text recognition networks.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../org/opencv/features2d/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:57 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CvType (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CvType (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/CvException.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/CvType.html" target="_top">Frames</a></li>
<li><a href="CvType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.core</div>
<h2 title="Class CvType" class="title">Class CvType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.core.CvType</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">CvType</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16F">CV_16F</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16FC1">CV_16FC1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16FC2">CV_16FC2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16FC3">CV_16FC3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16FC4">CV_16FC4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16S">CV_16S</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16SC1">CV_16SC1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16SC2">CV_16SC2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16SC3">CV_16SC3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16SC4">CV_16SC4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16U">CV_16U</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16UC1">CV_16UC1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16UC2">CV_16UC2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16UC3">CV_16UC3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16UC4">CV_16UC4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32F">CV_32F</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32FC1">CV_32FC1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32FC2">CV_32FC2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32FC3">CV_32FC3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32FC4">CV_32FC4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32S">CV_32S</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32SC1">CV_32SC1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32SC2">CV_32SC2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32SC3">CV_32SC3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32SC4">CV_32SC4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_64F">CV_64F</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_64FC1">CV_64FC1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_64FC2">CV_64FC2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_64FC3">CV_64FC3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_64FC4">CV_64FC4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8S">CV_8S</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8SC1">CV_8SC1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8SC2">CV_8SC2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8SC3">CV_8SC3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8SC4">CV_8SC4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8U">CV_8U</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8UC1">CV_8UC1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8UC2">CV_8UC2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8UC3">CV_8UC3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8UC4">CV_8UC4</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CvType--">CvType</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#channels-int-">channels</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16FC-int-">CV_16FC</a></span>(int&nbsp;ch)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16SC-int-">CV_16SC</a></span>(int&nbsp;ch)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_16UC-int-">CV_16UC</a></span>(int&nbsp;ch)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32FC-int-">CV_32FC</a></span>(int&nbsp;ch)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_32SC-int-">CV_32SC</a></span>(int&nbsp;ch)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_64FC-int-">CV_64FC</a></span>(int&nbsp;ch)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8SC-int-">CV_8SC</a></span>(int&nbsp;ch)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#CV_8UC-int-">CV_8UC</a></span>(int&nbsp;ch)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#depth-int-">depth</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#ELEM_SIZE-int-">ELEM_SIZE</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#isInteger-int-">isInteger</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#makeType-int-int-">makeType</a></span>(int&nbsp;depth,
        int&nbsp;channels)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/CvType.html#typeToString-int-">typeToString</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="CV_16F">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16F</h4>
<pre>public static final&nbsp;int CV_16F</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.CvType.CV_16F">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_16FC1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16FC1</h4>
<pre>public static final&nbsp;int CV_16FC1</pre>
</li>
</ul>
<a name="CV_16FC2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16FC2</h4>
<pre>public static final&nbsp;int CV_16FC2</pre>
</li>
</ul>
<a name="CV_16FC3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16FC3</h4>
<pre>public static final&nbsp;int CV_16FC3</pre>
</li>
</ul>
<a name="CV_16FC4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16FC4</h4>
<pre>public static final&nbsp;int CV_16FC4</pre>
</li>
</ul>
<a name="CV_16S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16S</h4>
<pre>public static final&nbsp;int CV_16S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.CvType.CV_16S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_16SC1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16SC1</h4>
<pre>public static final&nbsp;int CV_16SC1</pre>
</li>
</ul>
<a name="CV_16SC2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16SC2</h4>
<pre>public static final&nbsp;int CV_16SC2</pre>
</li>
</ul>
<a name="CV_16SC3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16SC3</h4>
<pre>public static final&nbsp;int CV_16SC3</pre>
</li>
</ul>
<a name="CV_16SC4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16SC4</h4>
<pre>public static final&nbsp;int CV_16SC4</pre>
</li>
</ul>
<a name="CV_16U">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16U</h4>
<pre>public static final&nbsp;int CV_16U</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.CvType.CV_16U">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_16UC1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16UC1</h4>
<pre>public static final&nbsp;int CV_16UC1</pre>
</li>
</ul>
<a name="CV_16UC2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16UC2</h4>
<pre>public static final&nbsp;int CV_16UC2</pre>
</li>
</ul>
<a name="CV_16UC3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16UC3</h4>
<pre>public static final&nbsp;int CV_16UC3</pre>
</li>
</ul>
<a name="CV_16UC4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16UC4</h4>
<pre>public static final&nbsp;int CV_16UC4</pre>
</li>
</ul>
<a name="CV_32F">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32F</h4>
<pre>public static final&nbsp;int CV_32F</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.CvType.CV_32F">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_32FC1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32FC1</h4>
<pre>public static final&nbsp;int CV_32FC1</pre>
</li>
</ul>
<a name="CV_32FC2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32FC2</h4>
<pre>public static final&nbsp;int CV_32FC2</pre>
</li>
</ul>
<a name="CV_32FC3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32FC3</h4>
<pre>public static final&nbsp;int CV_32FC3</pre>
</li>
</ul>
<a name="CV_32FC4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32FC4</h4>
<pre>public static final&nbsp;int CV_32FC4</pre>
</li>
</ul>
<a name="CV_32S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32S</h4>
<pre>public static final&nbsp;int CV_32S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.CvType.CV_32S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_32SC1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32SC1</h4>
<pre>public static final&nbsp;int CV_32SC1</pre>
</li>
</ul>
<a name="CV_32SC2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32SC2</h4>
<pre>public static final&nbsp;int CV_32SC2</pre>
</li>
</ul>
<a name="CV_32SC3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32SC3</h4>
<pre>public static final&nbsp;int CV_32SC3</pre>
</li>
</ul>
<a name="CV_32SC4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32SC4</h4>
<pre>public static final&nbsp;int CV_32SC4</pre>
</li>
</ul>
<a name="CV_64F">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_64F</h4>
<pre>public static final&nbsp;int CV_64F</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.CvType.CV_64F">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_64FC1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_64FC1</h4>
<pre>public static final&nbsp;int CV_64FC1</pre>
</li>
</ul>
<a name="CV_64FC2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_64FC2</h4>
<pre>public static final&nbsp;int CV_64FC2</pre>
</li>
</ul>
<a name="CV_64FC3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_64FC3</h4>
<pre>public static final&nbsp;int CV_64FC3</pre>
</li>
</ul>
<a name="CV_64FC4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_64FC4</h4>
<pre>public static final&nbsp;int CV_64FC4</pre>
</li>
</ul>
<a name="CV_8S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8S</h4>
<pre>public static final&nbsp;int CV_8S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.CvType.CV_8S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_8SC1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8SC1</h4>
<pre>public static final&nbsp;int CV_8SC1</pre>
</li>
</ul>
<a name="CV_8SC2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8SC2</h4>
<pre>public static final&nbsp;int CV_8SC2</pre>
</li>
</ul>
<a name="CV_8SC3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8SC3</h4>
<pre>public static final&nbsp;int CV_8SC3</pre>
</li>
</ul>
<a name="CV_8SC4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8SC4</h4>
<pre>public static final&nbsp;int CV_8SC4</pre>
</li>
</ul>
<a name="CV_8U">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8U</h4>
<pre>public static final&nbsp;int CV_8U</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.CvType.CV_8U">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_8UC1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8UC1</h4>
<pre>public static final&nbsp;int CV_8UC1</pre>
</li>
</ul>
<a name="CV_8UC2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8UC2</h4>
<pre>public static final&nbsp;int CV_8UC2</pre>
</li>
</ul>
<a name="CV_8UC3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8UC3</h4>
<pre>public static final&nbsp;int CV_8UC3</pre>
</li>
</ul>
<a name="CV_8UC4">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CV_8UC4</h4>
<pre>public static final&nbsp;int CV_8UC4</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CvType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CvType</h4>
<pre>public&nbsp;CvType()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="channels-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>channels</h4>
<pre>public static final&nbsp;int&nbsp;channels(int&nbsp;type)</pre>
</li>
</ul>
<a name="CV_16FC-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16FC</h4>
<pre>public static final&nbsp;int&nbsp;CV_16FC(int&nbsp;ch)</pre>
</li>
</ul>
<a name="CV_16SC-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16SC</h4>
<pre>public static final&nbsp;int&nbsp;CV_16SC(int&nbsp;ch)</pre>
</li>
</ul>
<a name="CV_16UC-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_16UC</h4>
<pre>public static final&nbsp;int&nbsp;CV_16UC(int&nbsp;ch)</pre>
</li>
</ul>
<a name="CV_32FC-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32FC</h4>
<pre>public static final&nbsp;int&nbsp;CV_32FC(int&nbsp;ch)</pre>
</li>
</ul>
<a name="CV_32SC-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_32SC</h4>
<pre>public static final&nbsp;int&nbsp;CV_32SC(int&nbsp;ch)</pre>
</li>
</ul>
<a name="CV_64FC-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_64FC</h4>
<pre>public static final&nbsp;int&nbsp;CV_64FC(int&nbsp;ch)</pre>
</li>
</ul>
<a name="CV_8SC-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8SC</h4>
<pre>public static final&nbsp;int&nbsp;CV_8SC(int&nbsp;ch)</pre>
</li>
</ul>
<a name="CV_8UC-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_8UC</h4>
<pre>public static final&nbsp;int&nbsp;CV_8UC(int&nbsp;ch)</pre>
</li>
</ul>
<a name="depth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>depth</h4>
<pre>public static final&nbsp;int&nbsp;depth(int&nbsp;type)</pre>
</li>
</ul>
<a name="ELEM_SIZE-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ELEM_SIZE</h4>
<pre>public static final&nbsp;int&nbsp;ELEM_SIZE(int&nbsp;type)</pre>
</li>
</ul>
<a name="isInteger-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInteger</h4>
<pre>public static final&nbsp;boolean&nbsp;isInteger(int&nbsp;type)</pre>
</li>
</ul>
<a name="makeType-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makeType</h4>
<pre>public static final&nbsp;int&nbsp;makeType(int&nbsp;depth,
                                 int&nbsp;channels)</pre>
</li>
</ul>
<a name="typeToString-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>typeToString</h4>
<pre>public static final&nbsp;java.lang.String&nbsp;typeToString(int&nbsp;type)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/CvException.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/CvType.html" target="_top">Frames</a></li>
<li><a href="CvType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:57 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>KeyPoint (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="KeyPoint (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/KeyPoint.html" target="_top">Frames</a></li>
<li><a href="KeyPoint.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.core</div>
<h2 title="Class KeyPoint" class="title">Class KeyPoint</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.core.KeyPoint</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">KeyPoint</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#angle">angle</a></span></code>
<div class="block">Computed orientation of the keypoint (-1 if not applicable).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#class_id">class_id</a></span></code>
<div class="block">Object ID, that can be used to cluster keypoints by an object they
 belong to.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#octave">octave</a></span></code>
<div class="block">Octave (pyramid layer), from which the keypoint has been extracted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#pt">pt</a></span></code>
<div class="block">Coordinates of the keypoint.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#response">response</a></span></code>
<div class="block">The response, by which the strongest keypoints have been selected.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#size">size</a></span></code>
<div class="block">Diameter of the useful keypoint adjacent area.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#KeyPoint--">KeyPoint</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#KeyPoint-float-float-float-">KeyPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        float&nbsp;_size)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#KeyPoint-float-float-float-float-">KeyPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        float&nbsp;_size,
        float&nbsp;_angle)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#KeyPoint-float-float-float-float-float-">KeyPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        float&nbsp;_size,
        float&nbsp;_angle,
        float&nbsp;_response)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#KeyPoint-float-float-float-float-float-int-">KeyPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        float&nbsp;_size,
        float&nbsp;_angle,
        float&nbsp;_response,
        int&nbsp;_octave)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#KeyPoint-float-float-float-float-float-int-int-">KeyPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        float&nbsp;_size,
        float&nbsp;_angle,
        float&nbsp;_response,
        int&nbsp;_octave,
        int&nbsp;_class_id)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/KeyPoint.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="angle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>angle</h4>
<pre>public&nbsp;float angle</pre>
<div class="block">Computed orientation of the keypoint (-1 if not applicable).</div>
</li>
</ul>
<a name="class_id">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>class_id</h4>
<pre>public&nbsp;int class_id</pre>
<div class="block">Object ID, that can be used to cluster keypoints by an object they
 belong to.</div>
</li>
</ul>
<a name="octave">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>octave</h4>
<pre>public&nbsp;int octave</pre>
<div class="block">Octave (pyramid layer), from which the keypoint has been extracted.</div>
</li>
</ul>
<a name="pt">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pt</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a> pt</pre>
<div class="block">Coordinates of the keypoint.</div>
</li>
</ul>
<a name="response">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>response</h4>
<pre>public&nbsp;float response</pre>
<div class="block">The response, by which the strongest keypoints have been selected. Can
 be used for further sorting or subsampling.</div>
</li>
</ul>
<a name="size">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>size</h4>
<pre>public&nbsp;float size</pre>
<div class="block">Diameter of the useful keypoint adjacent area.</div>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="KeyPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KeyPoint</h4>
<pre>public&nbsp;KeyPoint()</pre>
</li>
</ul>
<a name="KeyPoint-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KeyPoint</h4>
<pre>public&nbsp;KeyPoint(float&nbsp;x,
                float&nbsp;y,
                float&nbsp;_size)</pre>
</li>
</ul>
<a name="KeyPoint-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KeyPoint</h4>
<pre>public&nbsp;KeyPoint(float&nbsp;x,
                float&nbsp;y,
                float&nbsp;_size,
                float&nbsp;_angle)</pre>
</li>
</ul>
<a name="KeyPoint-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KeyPoint</h4>
<pre>public&nbsp;KeyPoint(float&nbsp;x,
                float&nbsp;y,
                float&nbsp;_size,
                float&nbsp;_angle,
                float&nbsp;_response)</pre>
</li>
</ul>
<a name="KeyPoint-float-float-float-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KeyPoint</h4>
<pre>public&nbsp;KeyPoint(float&nbsp;x,
                float&nbsp;y,
                float&nbsp;_size,
                float&nbsp;_angle,
                float&nbsp;_response,
                int&nbsp;_octave)</pre>
</li>
</ul>
<a name="KeyPoint-float-float-float-float-float-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>KeyPoint</h4>
<pre>public&nbsp;KeyPoint(float&nbsp;x,
                float&nbsp;y,
                float&nbsp;_size,
                float&nbsp;_angle,
                float&nbsp;_response,
                int&nbsp;_octave,
                int&nbsp;_class_id)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/KeyPoint.html" target="_top">Frames</a></li>
<li><a href="KeyPoint.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

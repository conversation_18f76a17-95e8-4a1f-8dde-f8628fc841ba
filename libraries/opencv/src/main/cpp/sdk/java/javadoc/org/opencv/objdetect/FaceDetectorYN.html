<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:55 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>FaceDetectorYN (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FaceDetectorYN (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/CascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/FaceRecognizerSF.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/FaceDetectorYN.html" target="_top">Frames</a></li>
<li><a href="FaceDetectorYN.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class FaceDetectorYN" class="title">Class FaceDetectorYN</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.FaceDetectorYN</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">FaceDetectorYN</span>
extends java.lang.Object</pre>
<div class="block">DNN-based face detector

 model download link: https://github.com/opencv/opencv_zoo/tree/master/models/face_detection_yunet</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#create-java.lang.String-java.lang.String-org.opencv.core.Size-">create</a></span>(java.lang.String&nbsp;model,
      java.lang.String&nbsp;config,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</code>
<div class="block">Creates an instance of this class with given parameters</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#create-java.lang.String-java.lang.String-org.opencv.core.Size-float-">create</a></span>(java.lang.String&nbsp;model,
      java.lang.String&nbsp;config,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
      float&nbsp;score_threshold)</code>
<div class="block">Creates an instance of this class with given parameters</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#create-java.lang.String-java.lang.String-org.opencv.core.Size-float-float-">create</a></span>(java.lang.String&nbsp;model,
      java.lang.String&nbsp;config,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
      float&nbsp;score_threshold,
      float&nbsp;nms_threshold)</code>
<div class="block">Creates an instance of this class with given parameters</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#create-java.lang.String-java.lang.String-org.opencv.core.Size-float-float-int-">create</a></span>(java.lang.String&nbsp;model,
      java.lang.String&nbsp;config,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
      float&nbsp;score_threshold,
      float&nbsp;nms_threshold,
      int&nbsp;top_k)</code>
<div class="block">Creates an instance of this class with given parameters</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#create-java.lang.String-java.lang.String-org.opencv.core.Size-float-float-int-int-">create</a></span>(java.lang.String&nbsp;model,
      java.lang.String&nbsp;config,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
      float&nbsp;score_threshold,
      float&nbsp;nms_threshold,
      int&nbsp;top_k,
      int&nbsp;backend_id)</code>
<div class="block">Creates an instance of this class with given parameters</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#create-java.lang.String-java.lang.String-org.opencv.core.Size-float-float-int-int-int-">create</a></span>(java.lang.String&nbsp;model,
      java.lang.String&nbsp;config,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
      float&nbsp;score_threshold,
      float&nbsp;nms_threshold,
      int&nbsp;top_k,
      int&nbsp;backend_id,
      int&nbsp;target_id)</code>
<div class="block">Creates an instance of this class with given parameters</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#detect-org.opencv.core.Mat-org.opencv.core.Mat-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;faces)</code>
<div class="block">A simple interface to detect face from given image</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#getInputSize--">getInputSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#getNMSThreshold--">getNMSThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#getScoreThreshold--">getScoreThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#getTopK--">getTopK</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#setInputSize-org.opencv.core.Size-">setInputSize</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</code>
<div class="block">Set the size for the network input, which overwrites the input size of creating model.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#setNMSThreshold-float-">setNMSThreshold</a></span>(float&nbsp;nms_threshold)</code>
<div class="block">Set the Non-maximum-suppression threshold to suppress bounding boxes that have IoU greater than the given value</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#setScoreThreshold-float-">setScoreThreshold</a></span>(float&nbsp;score_threshold)</code>
<div class="block">Set the score threshold to filter out bounding boxes of score less than the given value</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html#setTopK-int-">setTopK</a></span>(int&nbsp;top_k)</code>
<div class="block">Set the number of bounding boxes preserved before NMS</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create-java.lang.String-java.lang.String-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a>&nbsp;create(java.lang.String&nbsp;model,
                                    java.lang.String&nbsp;config,
                                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</pre>
<div class="block">Creates an instance of this class with given parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.String-org.opencv.core.Size-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a>&nbsp;create(java.lang.String&nbsp;model,
                                    java.lang.String&nbsp;config,
                                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
                                    float&nbsp;score_threshold)</pre>
<div class="block">Creates an instance of this class with given parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.String-org.opencv.core.Size-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a>&nbsp;create(java.lang.String&nbsp;model,
                                    java.lang.String&nbsp;config,
                                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
                                    float&nbsp;score_threshold,
                                    float&nbsp;nms_threshold)</pre>
<div class="block">Creates an instance of this class with given parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.String-org.opencv.core.Size-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a>&nbsp;create(java.lang.String&nbsp;model,
                                    java.lang.String&nbsp;config,
                                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
                                    float&nbsp;score_threshold,
                                    float&nbsp;nms_threshold,
                                    int&nbsp;top_k)</pre>
<div class="block">Creates an instance of this class with given parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.String-org.opencv.core.Size-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a>&nbsp;create(java.lang.String&nbsp;model,
                                    java.lang.String&nbsp;config,
                                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
                                    float&nbsp;score_threshold,
                                    float&nbsp;nms_threshold,
                                    int&nbsp;top_k,
                                    int&nbsp;backend_id)</pre>
<div class="block">Creates an instance of this class with given parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dd><code>backend_id</code> - the id of backend</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.String-org.opencv.core.Size-float-float-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a>&nbsp;create(java.lang.String&nbsp;model,
                                    java.lang.String&nbsp;config,
                                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
                                    float&nbsp;score_threshold,
                                    float&nbsp;nms_threshold,
                                    int&nbsp;top_k,
                                    int&nbsp;backend_id,
                                    int&nbsp;target_id)</pre>
<div class="block">Creates an instance of this class with given parameters</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dd><code>backend_id</code> - the id of backend</dd>
<dd><code>target_id</code> - the id of target device</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;int&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;faces)</pre>
<div class="block">A simple interface to detect face from given image</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - an image to detect</dd>
<dd><code>faces</code> - detection results stored in a cv::Mat</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getInputSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInputSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;getInputSize()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="getNMSThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNMSThreshold</h4>
<pre>public&nbsp;float&nbsp;getNMSThreshold()</pre>
</li>
</ul>
<a name="getScoreThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScoreThreshold</h4>
<pre>public&nbsp;float&nbsp;getScoreThreshold()</pre>
</li>
</ul>
<a name="getTopK--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTopK</h4>
<pre>public&nbsp;int&nbsp;getTopK()</pre>
</li>
</ul>
<a name="setInputSize-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputSize</h4>
<pre>public&nbsp;void&nbsp;setInputSize(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</pre>
<div class="block">Set the size for the network input, which overwrites the input size of creating model. Call this method when the size of input image does not match the input size when creating model</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>input_size</code> - the size of the input image</dd>
</dl>
</li>
</ul>
<a name="setNMSThreshold-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNMSThreshold</h4>
<pre>public&nbsp;void&nbsp;setNMSThreshold(float&nbsp;nms_threshold)</pre>
<div class="block">Set the Non-maximum-suppression threshold to suppress bounding boxes that have IoU greater than the given value</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nms_threshold</code> - threshold for NMS operation</dd>
</dl>
</li>
</ul>
<a name="setScoreThreshold-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScoreThreshold</h4>
<pre>public&nbsp;void&nbsp;setScoreThreshold(float&nbsp;score_threshold)</pre>
<div class="block">Set the score threshold to filter out bounding boxes of score less than the given value</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>score_threshold</code> - threshold for filtering out bounding boxes</dd>
</dl>
</li>
</ul>
<a name="setTopK-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTopK</h4>
<pre>public&nbsp;void&nbsp;setTopK(int&nbsp;top_k)</pre>
<div class="block">Set the number of bounding boxes preserved before NMS</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>top_k</code> - the number of bounding boxes to preserve from top rank based on score</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/CascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/FaceRecognizerSF.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/FaceDetectorYN.html" target="_top">Frames</a></li>
<li><a href="FaceDetectorYN.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

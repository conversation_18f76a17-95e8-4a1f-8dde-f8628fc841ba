<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:55 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>QRCodeEncoder (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="QRCodeEncoder (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/QRCodeDetector.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/QRCodeEncoder_Params.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/QRCodeEncoder.html" target="_top">Frames</a></li>
<li><a href="QRCodeEncoder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class QRCodeEncoder" class="title">Class QRCodeEncoder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.QRCodeEncoder</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">QRCodeEncoder</span>
extends java.lang.Object</pre>
<div class="block">Groups the object candidate rectangles.
     rectList  Input/output vector of rectangles. Output vector includes retained and grouped rectangles. (The Python list is not modified in place.)
     weights Input/output vector of weights of rectangles. Output vector includes weights of retained and grouped rectangles. (The Python list is not modified in place.)
     groupThreshold Minimum possible number of rectangles minus 1. The threshold is used in a group of rectangles to retain it.
     eps Relative difference between sides of the rectangles to merge them into a group.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#CORRECT_LEVEL_H">CORRECT_LEVEL_H</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#CORRECT_LEVEL_L">CORRECT_LEVEL_L</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#CORRECT_LEVEL_M">CORRECT_LEVEL_M</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#CORRECT_LEVEL_Q">CORRECT_LEVEL_Q</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#ECI_UTF8">ECI_UTF8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#MODE_ALPHANUMERIC">MODE_ALPHANUMERIC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#MODE_AUTO">MODE_AUTO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#MODE_BYTE">MODE_BYTE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#MODE_ECI">MODE_ECI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#MODE_KANJI">MODE_KANJI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#MODE_NUMERIC">MODE_NUMERIC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#MODE_STRUCTURED_APPEND">MODE_STRUCTURED_APPEND</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#create--">create</a></span>()</code>
<div class="block">Constructor</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#create-org.opencv.objdetect.QRCodeEncoder_Params-">create</a></span>(<a href="../../../org/opencv/objdetect/QRCodeEncoder_Params.html" title="class in org.opencv.objdetect">QRCodeEncoder_Params</a>&nbsp;parameters)</code>
<div class="block">Constructor</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#encode-java.lang.String-org.opencv.core.Mat-">encode</a></span>(java.lang.String&nbsp;encoded_info,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;qrcode)</code>
<div class="block">Generates QR code from input string.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#encodeStructuredAppend-java.lang.String-java.util.List-">encodeStructuredAppend</a></span>(java.lang.String&nbsp;encoded_info,
                      java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;qrcodes)</code>
<div class="block">Generates QR code from input string in Structured Append mode.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="CORRECT_LEVEL_H">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CORRECT_LEVEL_H</h4>
<pre>public static final&nbsp;int CORRECT_LEVEL_H</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.CORRECT_LEVEL_H">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CORRECT_LEVEL_L">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CORRECT_LEVEL_L</h4>
<pre>public static final&nbsp;int CORRECT_LEVEL_L</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.CORRECT_LEVEL_L">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CORRECT_LEVEL_M">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CORRECT_LEVEL_M</h4>
<pre>public static final&nbsp;int CORRECT_LEVEL_M</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.CORRECT_LEVEL_M">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CORRECT_LEVEL_Q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CORRECT_LEVEL_Q</h4>
<pre>public static final&nbsp;int CORRECT_LEVEL_Q</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.CORRECT_LEVEL_Q">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ECI_UTF8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ECI_UTF8</h4>
<pre>public static final&nbsp;int ECI_UTF8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.ECI_UTF8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_ALPHANUMERIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_ALPHANUMERIC</h4>
<pre>public static final&nbsp;int MODE_ALPHANUMERIC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.MODE_ALPHANUMERIC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_AUTO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_AUTO</h4>
<pre>public static final&nbsp;int MODE_AUTO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.MODE_AUTO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_BYTE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_BYTE</h4>
<pre>public static final&nbsp;int MODE_BYTE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.MODE_BYTE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_ECI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_ECI</h4>
<pre>public static final&nbsp;int MODE_ECI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.MODE_ECI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_KANJI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_KANJI</h4>
<pre>public static final&nbsp;int MODE_KANJI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.MODE_KANJI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_NUMERIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_NUMERIC</h4>
<pre>public static final&nbsp;int MODE_NUMERIC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.MODE_NUMERIC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_STRUCTURED_APPEND">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MODE_STRUCTURED_APPEND</h4>
<pre>public static final&nbsp;int MODE_STRUCTURED_APPEND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.QRCodeEncoder.MODE_STRUCTURED_APPEND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a>&nbsp;create()</pre>
<div class="block">Constructor</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.objdetect.QRCodeEncoder_Params-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a>&nbsp;create(<a href="../../../org/opencv/objdetect/QRCodeEncoder_Params.html" title="class in org.opencv.objdetect">QRCodeEncoder_Params</a>&nbsp;parameters)</pre>
<div class="block">Constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>parameters</code> - QR code encoder parameters QRCodeEncoder::Params</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="encode-java.lang.String-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encode</h4>
<pre>public&nbsp;void&nbsp;encode(java.lang.String&nbsp;encoded_info,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;qrcode)</pre>
<div class="block">Generates QR code from input string.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>encoded_info</code> - Input string to encode.</dd>
<dd><code>qrcode</code> - Generated QR code.</dd>
</dl>
</li>
</ul>
<a name="encodeStructuredAppend-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeStructuredAppend</h4>
<pre>public&nbsp;void&nbsp;encodeStructuredAppend(java.lang.String&nbsp;encoded_info,
                                   java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;qrcodes)</pre>
<div class="block">Generates QR code from input string in Structured Append mode. The encoded message is splitting over a number of QR codes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>encoded_info</code> - Input string to encode.</dd>
<dd><code>qrcodes</code> - Vector of generated QR codes.</dd>
</dl>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/QRCodeDetector.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/QRCodeEncoder_Params.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/QRCodeEncoder.html" target="_top">Frames</a></li>
<li><a href="QRCodeEncoder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

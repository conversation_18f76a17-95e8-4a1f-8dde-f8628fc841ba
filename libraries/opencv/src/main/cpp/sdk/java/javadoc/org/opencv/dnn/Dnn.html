<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Tue Jun 07 08:57:55 UTC 2022 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Dnn (OpenCV 4.6.0 Java documentation)</title>
<meta name="date" content="2022-06-07">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Dnn (OpenCV 4.6.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9,"i44":9,"i45":9,"i46":9,"i47":9,"i48":9,"i49":9,"i50":9,"i51":9,"i52":9,"i53":9,"i54":9,"i55":9,"i56":9,"i57":9,"i58":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/DictValue.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/KeypointsModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/Dnn.html" target="_top">Frames</a></li>
<li><a href="Dnn.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.dnn</div>
<h2 title="Class Dnn" class="title">Class Dnn</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.dnn.Dnn</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Dnn</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_BACKEND_CUDA">DNN_BACKEND_CUDA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_BACKEND_DEFAULT">DNN_BACKEND_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_BACKEND_HALIDE">DNN_BACKEND_HALIDE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_BACKEND_INFERENCE_ENGINE">DNN_BACKEND_INFERENCE_ENGINE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_BACKEND_OPENCV">DNN_BACKEND_OPENCV</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_BACKEND_TIMVX">DNN_BACKEND_TIMVX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_BACKEND_VKCOM">DNN_BACKEND_VKCOM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_BACKEND_WEBNN">DNN_BACKEND_WEBNN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_CPU">DNN_TARGET_CPU</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_CUDA">DNN_TARGET_CUDA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_CUDA_FP16">DNN_TARGET_CUDA_FP16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_FPGA">DNN_TARGET_FPGA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_HDDL">DNN_TARGET_HDDL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_MYRIAD">DNN_TARGET_MYRIAD</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_NPU">DNN_TARGET_NPU</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_OPENCL">DNN_TARGET_OPENCL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_OPENCL_FP16">DNN_TARGET_OPENCL_FP16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#DNN_TARGET_VULKAN">DNN_TARGET_VULKAN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#SoftNMSMethod_SOFTNMS_GAUSSIAN">SoftNMSMethod_SOFTNMS_GAUSSIAN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#SoftNMSMethod_SOFTNMS_LINEAR">SoftNMSMethod_SOFTNMS_LINEAR</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#Dnn--">Dnn</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImage-org.opencv.core.Mat-">blobFromImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code>
<div class="block">Creates 4-dimensional blob from image.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImage-org.opencv.core.Mat-double-">blobFromImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             double&nbsp;scalefactor)</code>
<div class="block">Creates 4-dimensional blob from image.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-">blobFromImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             double&nbsp;scalefactor,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code>
<div class="block">Creates 4-dimensional blob from image.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Scalar-">blobFromImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             double&nbsp;scalefactor,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
             <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code>
<div class="block">Creates 4-dimensional blob from image.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">blobFromImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             double&nbsp;scalefactor,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
             <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
             boolean&nbsp;swapRB)</code>
<div class="block">Creates 4-dimensional blob from image.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">blobFromImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             double&nbsp;scalefactor,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
             <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
             boolean&nbsp;swapRB,
             boolean&nbsp;crop)</code>
<div class="block">Creates 4-dimensional blob from image.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-int-">blobFromImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             double&nbsp;scalefactor,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
             <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
             boolean&nbsp;swapRB,
             boolean&nbsp;crop,
             int&nbsp;ddepth)</code>
<div class="block">Creates 4-dimensional blob from image.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImages-java.util.List-">blobFromImages</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images)</code>
<div class="block">Creates 4-dimensional blob from series of images.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImages-java.util.List-double-">blobFromImages</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
              double&nbsp;scalefactor)</code>
<div class="block">Creates 4-dimensional blob from series of images.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImages-java.util.List-double-org.opencv.core.Size-">blobFromImages</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
              double&nbsp;scalefactor,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code>
<div class="block">Creates 4-dimensional blob from series of images.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImages-java.util.List-double-org.opencv.core.Size-org.opencv.core.Scalar-">blobFromImages</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
              double&nbsp;scalefactor,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code>
<div class="block">Creates 4-dimensional blob from series of images.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImages-java.util.List-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">blobFromImages</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
              double&nbsp;scalefactor,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
              boolean&nbsp;swapRB)</code>
<div class="block">Creates 4-dimensional blob from series of images.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImages-java.util.List-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">blobFromImages</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
              double&nbsp;scalefactor,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
              boolean&nbsp;swapRB,
              boolean&nbsp;crop)</code>
<div class="block">Creates 4-dimensional blob from series of images.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#blobFromImages-java.util.List-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-int-">blobFromImages</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
              double&nbsp;scalefactor,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
              boolean&nbsp;swapRB,
              boolean&nbsp;crop,
              int&nbsp;ddepth)</code>
<div class="block">Creates 4-dimensional blob from series of images.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.Integer&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#getAvailableTargets-int-">getAvailableTargets</a></span>(int&nbsp;be)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#getInferenceEngineCPUType--">getInferenceEngineCPUType</a></span>()</code>
<div class="block">Returns Inference Engine CPU type.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#getInferenceEngineVPUType--">getInferenceEngineVPUType</a></span>()</code>
<div class="block">Returns Inference Engine VPU type.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#imagesFromBlob-org.opencv.core.Mat-java.util.List-">imagesFromBlob</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob_,
              java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images_)</code>
<div class="block">Parse a 4D blob and output the images it contains as 2D arrays through a simpler data structure
 (std::vector&lt;cv::Mat&gt;).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#NMSBoxes-org.opencv.core.MatOfRect2d-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-">NMSBoxes</a></span>(<a href="../../../org/opencv/core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
        float&nbsp;score_threshold,
        float&nbsp;nms_threshold,
        <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</code>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#NMSBoxes-org.opencv.core.MatOfRect2d-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-float-">NMSBoxes</a></span>(<a href="../../../org/opencv/core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
        float&nbsp;score_threshold,
        float&nbsp;nms_threshold,
        <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
        float&nbsp;eta)</code>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#NMSBoxes-org.opencv.core.MatOfRect2d-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-float-int-">NMSBoxes</a></span>(<a href="../../../org/opencv/core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
        float&nbsp;score_threshold,
        float&nbsp;nms_threshold,
        <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
        float&nbsp;eta,
        int&nbsp;top_k)</code>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#NMSBoxesRotated-org.opencv.core.MatOfRotatedRect-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-">NMSBoxesRotated</a></span>(<a href="../../../org/opencv/core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
               <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
               float&nbsp;score_threshold,
               float&nbsp;nms_threshold,
               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#NMSBoxesRotated-org.opencv.core.MatOfRotatedRect-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-float-">NMSBoxesRotated</a></span>(<a href="../../../org/opencv/core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
               <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
               float&nbsp;score_threshold,
               float&nbsp;nms_threshold,
               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
               float&nbsp;eta)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#NMSBoxesRotated-org.opencv.core.MatOfRotatedRect-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-float-int-">NMSBoxesRotated</a></span>(<a href="../../../org/opencv/core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
               <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
               float&nbsp;score_threshold,
               float&nbsp;nms_threshold,
               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
               float&nbsp;eta,
               int&nbsp;top_k)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNet-java.lang.String-">readNet</a></span>(java.lang.String&nbsp;model)</code>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNet-java.lang.String-org.opencv.core.MatOfByte-">readNet</a></span>(java.lang.String&nbsp;framework,
       <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNet-java.lang.String-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">readNet</a></span>(java.lang.String&nbsp;framework,
       <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
       <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</code>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNet-java.lang.String-java.lang.String-">readNet</a></span>(java.lang.String&nbsp;model,
       java.lang.String&nbsp;config)</code>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNet-java.lang.String-java.lang.String-java.lang.String-">readNet</a></span>(java.lang.String&nbsp;model,
       java.lang.String&nbsp;config,
       java.lang.String&nbsp;framework)</code>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromCaffe-org.opencv.core.MatOfByte-">readNetFromCaffe</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferProto)</code>
<div class="block">Reads a network model stored in Caffe model in memory.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromCaffe-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">readNetFromCaffe</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferProto,
                <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code>
<div class="block">Reads a network model stored in Caffe model in memory.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromCaffe-java.lang.String-">readNetFromCaffe</a></span>(java.lang.String&nbsp;prototxt)</code>
<div class="block">Reads a network model stored in &lt;a href="http://caffe.berkeleyvision.org"&gt;Caffe&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromCaffe-java.lang.String-java.lang.String-">readNetFromCaffe</a></span>(java.lang.String&nbsp;prototxt,
                java.lang.String&nbsp;caffeModel)</code>
<div class="block">Reads a network model stored in &lt;a href="http://caffe.berkeleyvision.org"&gt;Caffe&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromDarknet-org.opencv.core.MatOfByte-">readNetFromDarknet</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferCfg)</code>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromDarknet-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">readNetFromDarknet</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferCfg,
                  <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromDarknet-java.lang.String-">readNetFromDarknet</a></span>(java.lang.String&nbsp;cfgFile)</code>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromDarknet-java.lang.String-java.lang.String-">readNetFromDarknet</a></span>(java.lang.String&nbsp;cfgFile,
                  java.lang.String&nbsp;darknetModel)</code>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromModelOptimizer-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">readNetFromModelOptimizer</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModelConfig,
                         <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferWeights)</code>
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromModelOptimizer-java.lang.String-java.lang.String-">readNetFromModelOptimizer</a></span>(java.lang.String&nbsp;xml,
                         java.lang.String&nbsp;bin)</code>
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromONNX-org.opencv.core.MatOfByte-">readNetFromONNX</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buffer)</code>
<div class="block">Reads a network model from &lt;a href="https://onnx.ai/"&gt;ONNX&lt;/a&gt;
 in-memory buffer.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromONNX-java.lang.String-">readNetFromONNX</a></span>(java.lang.String&nbsp;onnxFile)</code>
<div class="block">Reads a network model &lt;a href="https://onnx.ai/"&gt;ONNX&lt;/a&gt;.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromTensorflow-org.opencv.core.MatOfByte-">readNetFromTensorflow</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</code>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromTensorflow-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">readNetFromTensorflow</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
                     <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</code>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromTensorflow-java.lang.String-">readNetFromTensorflow</a></span>(java.lang.String&nbsp;model)</code>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromTensorflow-java.lang.String-java.lang.String-">readNetFromTensorflow</a></span>(java.lang.String&nbsp;model,
                     java.lang.String&nbsp;config)</code>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromTorch-java.lang.String-">readNetFromTorch</a></span>(java.lang.String&nbsp;model)</code>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromTorch-java.lang.String-boolean-">readNetFromTorch</a></span>(java.lang.String&nbsp;model,
                boolean&nbsp;isBinary)</code>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readNetFromTorch-java.lang.String-boolean-boolean-">readNetFromTorch</a></span>(java.lang.String&nbsp;model,
                boolean&nbsp;isBinary,
                boolean&nbsp;evaluate)</code>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readTensorFromONNX-java.lang.String-">readTensorFromONNX</a></span>(java.lang.String&nbsp;path)</code>
<div class="block">Creates blob from .pb file.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readTorchBlob-java.lang.String-">readTorchBlob</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Loads blob which was serialized as torch.Tensor object of Torch7 framework.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#readTorchBlob-java.lang.String-boolean-">readTorchBlob</a></span>(java.lang.String&nbsp;filename,
             boolean&nbsp;isBinary)</code>
<div class="block">Loads blob which was serialized as torch.Tensor object of Torch7 framework.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#releaseHDDLPlugin--">releaseHDDLPlugin</a></span>()</code>
<div class="block">Release a HDDL plugin.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#resetMyriadDevice--">resetMyriadDevice</a></span>()</code>
<div class="block">Release a Myriad device (binded by OpenCV).</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#shrinkCaffeModel-java.lang.String-java.lang.String-">shrinkCaffeModel</a></span>(java.lang.String&nbsp;src,
                java.lang.String&nbsp;dst)</code>
<div class="block">Convert all weights of Caffe network to half precision floating point.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#shrinkCaffeModel-java.lang.String-java.lang.String-java.util.List-">shrinkCaffeModel</a></span>(java.lang.String&nbsp;src,
                java.lang.String&nbsp;dst,
                java.util.List&lt;java.lang.String&gt;&nbsp;layersTypes)</code>
<div class="block">Convert all weights of Caffe network to half precision floating point.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#softNMSBoxes-org.opencv.core.MatOfRect-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-">softNMSBoxes</a></span>(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
            float&nbsp;score_threshold,
            float&nbsp;nms_threshold,
            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</code>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#softNMSBoxes-org.opencv.core.MatOfRect-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-long-">softNMSBoxes</a></span>(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
            float&nbsp;score_threshold,
            float&nbsp;nms_threshold,
            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
            long&nbsp;top_k)</code>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#softNMSBoxes-org.opencv.core.MatOfRect-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-long-float-">softNMSBoxes</a></span>(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
            float&nbsp;score_threshold,
            float&nbsp;nms_threshold,
            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
            long&nbsp;top_k,
            float&nbsp;sigma)</code>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Dnn.html#writeTextGraph-java.lang.String-java.lang.String-">writeTextGraph</a></span>(java.lang.String&nbsp;model,
              java.lang.String&nbsp;output)</code>
<div class="block">Create a text representation for a binary network stored in protocol buffer format.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DNN_BACKEND_CUDA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_BACKEND_CUDA</h4>
<pre>public static final&nbsp;int DNN_BACKEND_CUDA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_CUDA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_BACKEND_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_BACKEND_DEFAULT</h4>
<pre>public static final&nbsp;int DNN_BACKEND_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_DEFAULT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_BACKEND_HALIDE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_BACKEND_HALIDE</h4>
<pre>public static final&nbsp;int DNN_BACKEND_HALIDE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_HALIDE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_BACKEND_INFERENCE_ENGINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_BACKEND_INFERENCE_ENGINE</h4>
<pre>public static final&nbsp;int DNN_BACKEND_INFERENCE_ENGINE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_INFERENCE_ENGINE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_BACKEND_OPENCV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_BACKEND_OPENCV</h4>
<pre>public static final&nbsp;int DNN_BACKEND_OPENCV</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_OPENCV">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_BACKEND_TIMVX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_BACKEND_TIMVX</h4>
<pre>public static final&nbsp;int DNN_BACKEND_TIMVX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_TIMVX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_BACKEND_VKCOM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_BACKEND_VKCOM</h4>
<pre>public static final&nbsp;int DNN_BACKEND_VKCOM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_VKCOM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_BACKEND_WEBNN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_BACKEND_WEBNN</h4>
<pre>public static final&nbsp;int DNN_BACKEND_WEBNN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_BACKEND_WEBNN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_CPU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_CPU</h4>
<pre>public static final&nbsp;int DNN_TARGET_CPU</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_CPU">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_CUDA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_CUDA</h4>
<pre>public static final&nbsp;int DNN_TARGET_CUDA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_CUDA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_CUDA_FP16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_CUDA_FP16</h4>
<pre>public static final&nbsp;int DNN_TARGET_CUDA_FP16</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_CUDA_FP16">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_FPGA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_FPGA</h4>
<pre>public static final&nbsp;int DNN_TARGET_FPGA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_FPGA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_HDDL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_HDDL</h4>
<pre>public static final&nbsp;int DNN_TARGET_HDDL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_HDDL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_MYRIAD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_MYRIAD</h4>
<pre>public static final&nbsp;int DNN_TARGET_MYRIAD</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_MYRIAD">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_NPU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_NPU</h4>
<pre>public static final&nbsp;int DNN_TARGET_NPU</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_NPU">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_OPENCL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_OPENCL</h4>
<pre>public static final&nbsp;int DNN_TARGET_OPENCL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_OPENCL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_OPENCL_FP16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_OPENCL_FP16</h4>
<pre>public static final&nbsp;int DNN_TARGET_OPENCL_FP16</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_OPENCL_FP16">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DNN_TARGET_VULKAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DNN_TARGET_VULKAN</h4>
<pre>public static final&nbsp;int DNN_TARGET_VULKAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.DNN_TARGET_VULKAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SoftNMSMethod_SOFTNMS_GAUSSIAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SoftNMSMethod_SOFTNMS_GAUSSIAN</h4>
<pre>public static final&nbsp;int SoftNMSMethod_SOFTNMS_GAUSSIAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.SoftNMSMethod_SOFTNMS_GAUSSIAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SoftNMSMethod_SOFTNMS_LINEAR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SoftNMSMethod_SOFTNMS_LINEAR</h4>
<pre>public static final&nbsp;int SoftNMSMethod_SOFTNMS_LINEAR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.dnn.Dnn.SoftNMSMethod_SOFTNMS_LINEAR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Dnn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Dnn</h4>
<pre>public&nbsp;Dnn()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="blobFromImage-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImage</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</pre>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImage-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImage</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                double&nbsp;scalefactor)</pre>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>image</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImage</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                double&nbsp;scalefactor,
                                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</pre>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>image</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImage</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                double&nbsp;scalefactor,
                                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</pre>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>image</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImage</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                double&nbsp;scalefactor,
                                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                                boolean&nbsp;swapRB)</pre>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>image</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImage</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                double&nbsp;scalefactor,
                                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                                boolean&nbsp;swapRB,
                                boolean&nbsp;crop)</pre>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>image</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.</dd>
<dd><code>crop</code> - flag which indicates whether image will be cropped after resize or not
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImage-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImage</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                double&nbsp;scalefactor,
                                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                                boolean&nbsp;swapRB,
                                boolean&nbsp;crop,
                                int&nbsp;ddepth)</pre>
<div class="block">Creates 4-dimensional blob from image. Optionally resizes and crops <code>image</code> from center,
 subtract <code>mean</code> values, scales values by <code>scalefactor</code>, swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image (with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>image</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.</dd>
<dd><code>crop</code> - flag which indicates whether image will be cropped after resize or not</dd>
<dd><code>ddepth</code> - Depth of output blob. Choose CV_32F or CV_8U.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImages-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImages</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImages(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images)</pre>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImages-java.util.List-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImages</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImages(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                                 double&nbsp;scalefactor)</pre>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImages-java.util.List-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImages</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImages(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                                 double&nbsp;scalefactor,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</pre>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImages-java.util.List-double-org.opencv.core.Size-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImages</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImages(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                                 double&nbsp;scalefactor,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                 <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</pre>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImages-java.util.List-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImages</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImages(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                                 double&nbsp;scalefactor,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                 <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                                 boolean&nbsp;swapRB)</pre>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImages-java.util.List-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImages</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImages(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                                 double&nbsp;scalefactor,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                 <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                                 boolean&nbsp;swapRB,
                                 boolean&nbsp;crop)</pre>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.</dd>
<dd><code>crop</code> - flag which indicates whether image will be cropped after resize or not
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="blobFromImages-java.util.List-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blobFromImages</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blobFromImages(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                                 double&nbsp;scalefactor,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                 <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                                 boolean&nbsp;swapRB,
                                 boolean&nbsp;crop,
                                 int&nbsp;ddepth)</pre>
<div class="block">Creates 4-dimensional blob from series of images. Optionally resizes and
 crops <code>images</code> from center, subtract <code>mean</code> values, scales values by <code>scalefactor</code>,
 swap Blue and Red channels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>images</code> - input images (all with 1-, 3- or 4-channels).</dd>
<dd><code>size</code> - spatial size for output image</dd>
<dd><code>mean</code> - scalar with mean values which are subtracted from channels. Values are intended
 to be in (mean-R, mean-G, mean-B) order if <code>image</code> has BGR ordering and <code>swapRB</code> is true.</dd>
<dd><code>scalefactor</code> - multiplier for <code>images</code> values.</dd>
<dd><code>swapRB</code> - flag which indicates that swap first and last channels
 in 3-channel image is necessary.</dd>
<dd><code>crop</code> - flag which indicates whether image will be cropped after resize or not</dd>
<dd><code>ddepth</code> - Depth of output blob. Choose CV_32F or CV_8U.
 if <code>crop</code> is true, input image is resized so one side after resize is equal to corresponding
 dimension in <code>size</code> and another one is equal or larger. Then, crop from the center is performed.
 If <code>crop</code> is false, direct resize without cropping and preserving aspect ratio is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>4-dimensional Mat with NCHW dimensions order.</dd>
</dl>
</li>
</ul>
<a name="getAvailableTargets-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvailableTargets</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.Integer&gt;&nbsp;getAvailableTargets(int&nbsp;be)</pre>
</li>
</ul>
<a name="getInferenceEngineCPUType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInferenceEngineCPUType</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getInferenceEngineCPUType()</pre>
<div class="block">Returns Inference Engine CPU type.

 Specify OpenVINO plugin: CPU or ARM.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getInferenceEngineVPUType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInferenceEngineVPUType</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getInferenceEngineVPUType()</pre>
<div class="block">Returns Inference Engine VPU type.

 See values of <code>CV_DNN_INFERENCE_ENGINE_VPU_TYPE_*</code> macros.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imagesFromBlob-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imagesFromBlob</h4>
<pre>public static&nbsp;void&nbsp;imagesFromBlob(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob_,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images_)</pre>
<div class="block">Parse a 4D blob and output the images it contains as 2D arrays through a simpler data structure
 (std::vector&lt;cv::Mat&gt;).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>blob_</code> - 4 dimensional array (images, channels, height, width) in floating point precision (CV_32F) from
 which you would like to extract the images.</dd>
<dd><code>images_</code> - array of 2D Mat containing the images extracted from the blob in floating point precision
 (CV_32F). They are non normalized neither mean added. The number of returned images equals the first dimension
 of the blob (batch size). Every image has a number of channels equals to the second dimension of the blob (depth).</dd>
</dl>
</li>
</ul>
<a name="NMSBoxes-org.opencv.core.MatOfRect2d-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NMSBoxes</h4>
<pre>public static&nbsp;void&nbsp;NMSBoxes(<a href="../../../org/opencv/core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
                            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                            float&nbsp;score_threshold,
                            float&nbsp;nms_threshold,
                            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</pre>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
</dl>
</li>
</ul>
<a name="NMSBoxes-org.opencv.core.MatOfRect2d-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NMSBoxes</h4>
<pre>public static&nbsp;void&nbsp;NMSBoxes(<a href="../../../org/opencv/core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
                            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                            float&nbsp;score_threshold,
                            float&nbsp;nms_threshold,
                            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
                            float&nbsp;eta)</pre>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>eta</code> - a coefficient in adaptive threshold formula: \(nms\_threshold_{i+1}=eta\cdot nms\_threshold_i\).</dd>
</dl>
</li>
</ul>
<a name="NMSBoxes-org.opencv.core.MatOfRect2d-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NMSBoxes</h4>
<pre>public static&nbsp;void&nbsp;NMSBoxes(<a href="../../../org/opencv/core/MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a>&nbsp;bboxes,
                            <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                            float&nbsp;score_threshold,
                            float&nbsp;nms_threshold,
                            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
                            float&nbsp;eta,
                            int&nbsp;top_k)</pre>
<div class="block">Performs non maximum suppression given boxes and corresponding scores.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>eta</code> - a coefficient in adaptive threshold formula: \(nms\_threshold_{i+1}=eta\cdot nms\_threshold_i\).</dd>
<dd><code>top_k</code> - if <code>&amp;gt;0</code>, keep at most <code>top_k</code> picked indices.</dd>
</dl>
</li>
</ul>
<a name="NMSBoxesRotated-org.opencv.core.MatOfRotatedRect-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NMSBoxesRotated</h4>
<pre>public static&nbsp;void&nbsp;NMSBoxesRotated(<a href="../../../org/opencv/core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
                                   <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                                   float&nbsp;score_threshold,
                                   float&nbsp;nms_threshold,
                                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</pre>
</li>
</ul>
<a name="NMSBoxesRotated-org.opencv.core.MatOfRotatedRect-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NMSBoxesRotated</h4>
<pre>public static&nbsp;void&nbsp;NMSBoxesRotated(<a href="../../../org/opencv/core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
                                   <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                                   float&nbsp;score_threshold,
                                   float&nbsp;nms_threshold,
                                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
                                   float&nbsp;eta)</pre>
</li>
</ul>
<a name="NMSBoxesRotated-org.opencv.core.MatOfRotatedRect-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NMSBoxesRotated</h4>
<pre>public static&nbsp;void&nbsp;NMSBoxesRotated(<a href="../../../org/opencv/core/MatOfRotatedRect.html" title="class in org.opencv.core">MatOfRotatedRect</a>&nbsp;bboxes,
                                   <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                                   float&nbsp;score_threshold,
                                   float&nbsp;nms_threshold,
                                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
                                   float&nbsp;eta,
                                   int&nbsp;top_k)</pre>
</li>
</ul>
<a name="readNet-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNet(java.lang.String&nbsp;model)</pre>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights. The following file
 extensions are expected for models from different frameworks:
 * <code>*.caffemodel</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pb</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.t7</code> | <code>*.net</code> (Torch, http://torch.ch/)
 * <code>*.weights</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.bin</code> (DLDT, https://software.intel.com/openvino-toolkit)
 * <code>*.onnx</code> (ONNX, https://onnx.ai/)
 file with the following extensions:
 * <code>*.prototxt</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pbtxt</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.cfg</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.xml</code> (DLDT, https://software.intel.com/openvino-toolkit)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.

 This function automatically detects an origin framework of trained model
 and calls an appropriate function such REF: readNetFromCaffe, REF: readNetFromTensorflow,
 REF: readNetFromTorch or REF: readNetFromDarknet. An order of <code>model</code> and <code>config</code>
 arguments does not matter.</dd>
</dl>
</li>
</ul>
<a name="readNet-java.lang.String-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNet(java.lang.String&nbsp;framework,
                          <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</pre>
<div class="block">Read deep learning network represented in one of the supported formats.
 This is an overloaded member function, provided for convenience.
 It differs from the above function only in what argument(s) it accepts.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>framework</code> - Name of origin framework.</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNet-java.lang.String-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNet(java.lang.String&nbsp;framework,
                          <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
                          <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</pre>
<div class="block">Read deep learning network represented in one of the supported formats.
 This is an overloaded member function, provided for convenience.
 It differs from the above function only in what argument(s) it accepts.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>framework</code> - Name of origin framework.</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dd><code>bufferConfig</code> - A buffer with a content of text file contains network configuration.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNet-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNet(java.lang.String&nbsp;model,
                          java.lang.String&nbsp;config)</pre>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights. The following file
 extensions are expected for models from different frameworks:
 * <code>*.caffemodel</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pb</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.t7</code> | <code>*.net</code> (Torch, http://torch.ch/)
 * <code>*.weights</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.bin</code> (DLDT, https://software.intel.com/openvino-toolkit)
 * <code>*.onnx</code> (ONNX, https://onnx.ai/)</dd>
<dd><code>config</code> - Text file contains network configuration. It could be a
 file with the following extensions:
 * <code>*.prototxt</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pbtxt</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.cfg</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.xml</code> (DLDT, https://software.intel.com/openvino-toolkit)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.

 This function automatically detects an origin framework of trained model
 and calls an appropriate function such REF: readNetFromCaffe, REF: readNetFromTensorflow,
 REF: readNetFromTorch or REF: readNetFromDarknet. An order of <code>model</code> and <code>config</code>
 arguments does not matter.</dd>
</dl>
</li>
</ul>
<a name="readNet-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNet(java.lang.String&nbsp;model,
                          java.lang.String&nbsp;config,
                          java.lang.String&nbsp;framework)</pre>
<div class="block">Read deep learning network represented in one of the supported formats.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights. The following file
 extensions are expected for models from different frameworks:
 * <code>*.caffemodel</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pb</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.t7</code> | <code>*.net</code> (Torch, http://torch.ch/)
 * <code>*.weights</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.bin</code> (DLDT, https://software.intel.com/openvino-toolkit)
 * <code>*.onnx</code> (ONNX, https://onnx.ai/)</dd>
<dd><code>config</code> - Text file contains network configuration. It could be a
 file with the following extensions:
 * <code>*.prototxt</code> (Caffe, http://caffe.berkeleyvision.org/)
 * <code>*.pbtxt</code> (TensorFlow, https://www.tensorflow.org/)
 * <code>*.cfg</code> (Darknet, https://pjreddie.com/darknet/)
 * <code>*.xml</code> (DLDT, https://software.intel.com/openvino-toolkit)</dd>
<dd><code>framework</code> - Explicit framework name tag to determine a format.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.

 This function automatically detects an origin framework of trained model
 and calls an appropriate function such REF: readNetFromCaffe, REF: readNetFromTensorflow,
 REF: readNetFromTorch or REF: readNetFromDarknet. An order of <code>model</code> and <code>config</code>
 arguments does not matter.</dd>
</dl>
</li>
</ul>
<a name="readNetFromCaffe-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromCaffe</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromCaffe(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferProto)</pre>
<div class="block">Reads a network model stored in Caffe model in memory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bufferProto</code> - buffer containing the content of the .prototxt file</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromCaffe-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromCaffe</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromCaffe(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferProto,
                                   <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</pre>
<div class="block">Reads a network model stored in Caffe model in memory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bufferProto</code> - buffer containing the content of the .prototxt file</dd>
<dd><code>bufferModel</code> - buffer containing the content of the .caffemodel file</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromCaffe-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromCaffe</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromCaffe(java.lang.String&nbsp;prototxt)</pre>
<div class="block">Reads a network model stored in &lt;a href="http://caffe.berkeleyvision.org"&gt;Caffe&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prototxt</code> - path to the .prototxt file with text description of the network architecture.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromCaffe-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromCaffe</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromCaffe(java.lang.String&nbsp;prototxt,
                                   java.lang.String&nbsp;caffeModel)</pre>
<div class="block">Reads a network model stored in &lt;a href="http://caffe.berkeleyvision.org"&gt;Caffe&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prototxt</code> - path to the .prototxt file with text description of the network architecture.</dd>
<dd><code>caffeModel</code> - path to the .caffemodel file with learned network.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromDarknet-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromDarknet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromDarknet(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferCfg)</pre>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bufferCfg</code> - A buffer contains a content of .cfg file with text description of the network architecture.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromDarknet-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromDarknet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromDarknet(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferCfg,
                                     <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</pre>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bufferCfg</code> - A buffer contains a content of .cfg file with text description of the network architecture.</dd>
<dd><code>bufferModel</code> - A buffer contains a content of .weights file with learned network.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromDarknet-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromDarknet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromDarknet(java.lang.String&nbsp;cfgFile)</pre>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cfgFile</code> - path to the .cfg file with text description of the network architecture.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Network object that ready to do forward, throw an exception in failure cases.</dd>
</dl>
</li>
</ul>
<a name="readNetFromDarknet-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromDarknet</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromDarknet(java.lang.String&nbsp;cfgFile,
                                     java.lang.String&nbsp;darknetModel)</pre>
<div class="block">Reads a network model stored in &lt;a href="https://pjreddie.com/darknet/"&gt;Darknet&lt;/a&gt; model files.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cfgFile</code> - path to the .cfg file with text description of the network architecture.</dd>
<dd><code>darknetModel</code> - path to the .weights file with learned network.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Network object that ready to do forward, throw an exception in failure cases.</dd>
</dl>
</li>
</ul>
<a name="readNetFromModelOptimizer-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromModelOptimizer</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromModelOptimizer(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModelConfig,
                                            <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferWeights)</pre>
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bufferModelConfig</code> - Buffer contains XML configuration with network's topology.</dd>
<dd><code>bufferWeights</code> - Buffer contains binary data with trained weights.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.
 Networks imported from Intel's Model Optimizer are launched in Intel's Inference Engine
 backend.</dd>
</dl>
</li>
</ul>
<a name="readNetFromModelOptimizer-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromModelOptimizer</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromModelOptimizer(java.lang.String&nbsp;xml,
                                            java.lang.String&nbsp;bin)</pre>
<div class="block">Load a network from Intel's Model Optimizer intermediate representation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>xml</code> - XML configuration file with network's topology.</dd>
<dd><code>bin</code> - Binary file with trained weights.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.
 Networks imported from Intel's Model Optimizer are launched in Intel's Inference Engine
 backend.</dd>
</dl>
</li>
</ul>
<a name="readNetFromONNX-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromONNX</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromONNX(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buffer)</pre>
<div class="block">Reads a network model from &lt;a href="https://onnx.ai/"&gt;ONNX&lt;/a&gt;
 in-memory buffer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - in-memory buffer that stores the ONNX model bytes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Network object that ready to do forward, throw an exception
 in failure cases.</dd>
</dl>
</li>
</ul>
<a name="readNetFromONNX-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromONNX</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromONNX(java.lang.String&nbsp;onnxFile)</pre>
<div class="block">Reads a network model &lt;a href="https://onnx.ai/"&gt;ONNX&lt;/a&gt;.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>onnxFile</code> - path to the .onnx file with text description of the network architecture.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Network object that ready to do forward, throw an exception in failure cases.</dd>
</dl>
</li>
</ul>
<a name="readNetFromTensorflow-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromTensorflow</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromTensorflow(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel)</pre>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bufferModel</code> - buffer containing the content of the pb file</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromTensorflow-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromTensorflow</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromTensorflow(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig)</pre>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bufferModel</code> - buffer containing the content of the pb file</dd>
<dd><code>bufferConfig</code> - buffer containing the content of the pbtxt file</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromTensorflow-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromTensorflow</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromTensorflow(java.lang.String&nbsp;model)</pre>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - path to the .pb file with binary protobuf description of the network architecture
 Resulting Net object is built by text graph using weights from a binary one that
 let us make it more flexible.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromTensorflow-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromTensorflow</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromTensorflow(java.lang.String&nbsp;model,
                                        java.lang.String&nbsp;config)</pre>
<div class="block">Reads a network model stored in &lt;a href="https://www.tensorflow.org/"&gt;TensorFlow&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - path to the .pb file with binary protobuf description of the network architecture</dd>
<dd><code>config</code> - path to the .pbtxt file that contains text graph definition in protobuf format.
 Resulting Net object is built by text graph using weights from a binary one that
 let us make it more flexible.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readNetFromTorch-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromTorch</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromTorch(java.lang.String&nbsp;model)</pre>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - path to the file, dumped from Torch by using torch.save() function.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.

 <b>Note:</b> Ascii mode of Torch serializer is more preferable, because binary mode extensively use <code>long</code> type of C language,
 which has various bit-length on different systems.

 The loading file must contain serialized &lt;a href="https://github.com/torch/nn/blob/master/doc/module.md"&gt;nn.Module&lt;/a&gt; object
 with importing network. Try to eliminate a custom objects from serialazing data to avoid importing errors.

 List of supported layers (i.e. object instances derived from Torch nn.Module class):
 - nn.Sequential
 - nn.Parallel
 - nn.Concat
 - nn.Linear
 - nn.SpatialConvolution
 - nn.SpatialMaxPooling, nn.SpatialAveragePooling
 - nn.ReLU, nn.TanH, nn.Sigmoid
 - nn.Reshape
 - nn.SoftMax, nn.LogSoftMax

 Also some equivalents of these classes from cunn, cudnn, and fbcunn may be successfully imported.</dd>
</dl>
</li>
</ul>
<a name="readNetFromTorch-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromTorch</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromTorch(java.lang.String&nbsp;model,
                                   boolean&nbsp;isBinary)</pre>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - path to the file, dumped from Torch by using torch.save() function.</dd>
<dd><code>isBinary</code> - specifies whether the network was serialized in ascii mode or binary.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.

 <b>Note:</b> Ascii mode of Torch serializer is more preferable, because binary mode extensively use <code>long</code> type of C language,
 which has various bit-length on different systems.

 The loading file must contain serialized &lt;a href="https://github.com/torch/nn/blob/master/doc/module.md"&gt;nn.Module&lt;/a&gt; object
 with importing network. Try to eliminate a custom objects from serialazing data to avoid importing errors.

 List of supported layers (i.e. object instances derived from Torch nn.Module class):
 - nn.Sequential
 - nn.Parallel
 - nn.Concat
 - nn.Linear
 - nn.SpatialConvolution
 - nn.SpatialMaxPooling, nn.SpatialAveragePooling
 - nn.ReLU, nn.TanH, nn.Sigmoid
 - nn.Reshape
 - nn.SoftMax, nn.LogSoftMax

 Also some equivalents of these classes from cunn, cudnn, and fbcunn may be successfully imported.</dd>
</dl>
</li>
</ul>
<a name="readNetFromTorch-java.lang.String-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNetFromTorch</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readNetFromTorch(java.lang.String&nbsp;model,
                                   boolean&nbsp;isBinary,
                                   boolean&nbsp;evaluate)</pre>
<div class="block">Reads a network model stored in &lt;a href="http://torch.ch"&gt;Torch7&lt;/a&gt; framework's format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - path to the file, dumped from Torch by using torch.save() function.</dd>
<dd><code>isBinary</code> - specifies whether the network was serialized in ascii mode or binary.</dd>
<dd><code>evaluate</code> - specifies testing phase of network. If true, it's similar to evaluate() method in Torch.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.

 <b>Note:</b> Ascii mode of Torch serializer is more preferable, because binary mode extensively use <code>long</code> type of C language,
 which has various bit-length on different systems.

 The loading file must contain serialized &lt;a href="https://github.com/torch/nn/blob/master/doc/module.md"&gt;nn.Module&lt;/a&gt; object
 with importing network. Try to eliminate a custom objects from serialazing data to avoid importing errors.

 List of supported layers (i.e. object instances derived from Torch nn.Module class):
 - nn.Sequential
 - nn.Parallel
 - nn.Concat
 - nn.Linear
 - nn.SpatialConvolution
 - nn.SpatialMaxPooling, nn.SpatialAveragePooling
 - nn.ReLU, nn.TanH, nn.Sigmoid
 - nn.Reshape
 - nn.SoftMax, nn.LogSoftMax

 Also some equivalents of these classes from cunn, cudnn, and fbcunn may be successfully imported.</dd>
</dl>
</li>
</ul>
<a name="readTensorFromONNX-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTensorFromONNX</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;readTensorFromONNX(java.lang.String&nbsp;path)</pre>
<div class="block">Creates blob from .pb file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - to the .pb file with input tensor.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Mat.</dd>
</dl>
</li>
</ul>
<a name="readTorchBlob-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTorchBlob</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;readTorchBlob(java.lang.String&nbsp;filename)</pre>
<div class="block">Loads blob which was serialized as torch.Tensor object of Torch7 framework.
 WARNING: This function has the same limitations as readNetFromTorch().</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="readTorchBlob-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTorchBlob</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;readTorchBlob(java.lang.String&nbsp;filename,
                                boolean&nbsp;isBinary)</pre>
<div class="block">Loads blob which was serialized as torch.Tensor object of Torch7 framework.
 WARNING: This function has the same limitations as readNetFromTorch().</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>isBinary</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="releaseHDDLPlugin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>releaseHDDLPlugin</h4>
<pre>public static&nbsp;void&nbsp;releaseHDDLPlugin()</pre>
<div class="block">Release a HDDL plugin.</div>
</li>
</ul>
<a name="resetMyriadDevice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetMyriadDevice</h4>
<pre>public static&nbsp;void&nbsp;resetMyriadDevice()</pre>
<div class="block">Release a Myriad device (binded by OpenCV).

 Single Myriad device cannot be shared across multiple processes which uses
 Inference Engine's Myriad plugin.</div>
</li>
</ul>
<a name="shrinkCaffeModel-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>shrinkCaffeModel</h4>
<pre>public static&nbsp;void&nbsp;shrinkCaffeModel(java.lang.String&nbsp;src,
                                    java.lang.String&nbsp;dst)</pre>
<div class="block">Convert all weights of Caffe network to half precision floating point.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Path to origin model from Caffe framework contains single
 precision floating point weights (usually has <code>.caffemodel</code> extension).</dd>
<dd><code>dst</code> - Path to destination model with updated weights.
 By default, converts only Convolutional and Fully-Connected layers'
 weights.

 <b>Note:</b> Shrinked model has no origin float32 weights so it can't be used
 in origin Caffe framework anymore. However the structure of data
 is taken from NVidia's Caffe fork: https://github.com/NVIDIA/caffe.
 So the resulting model may be used there.</dd>
</dl>
</li>
</ul>
<a name="shrinkCaffeModel-java.lang.String-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>shrinkCaffeModel</h4>
<pre>public static&nbsp;void&nbsp;shrinkCaffeModel(java.lang.String&nbsp;src,
                                    java.lang.String&nbsp;dst,
                                    java.util.List&lt;java.lang.String&gt;&nbsp;layersTypes)</pre>
<div class="block">Convert all weights of Caffe network to half precision floating point.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - Path to origin model from Caffe framework contains single
 precision floating point weights (usually has <code>.caffemodel</code> extension).</dd>
<dd><code>dst</code> - Path to destination model with updated weights.</dd>
<dd><code>layersTypes</code> - Set of layers types which parameters will be converted.
 By default, converts only Convolutional and Fully-Connected layers'
 weights.

 <b>Note:</b> Shrinked model has no origin float32 weights so it can't be used
 in origin Caffe framework anymore. However the structure of data
 is taken from NVidia's Caffe fork: https://github.com/NVIDIA/caffe.
 So the resulting model may be used there.</dd>
</dl>
</li>
</ul>
<a name="softNMSBoxes-org.opencv.core.MatOfRect-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>softNMSBoxes</h4>
<pre>public static&nbsp;void&nbsp;softNMSBoxes(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
                                <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                                <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
                                float&nbsp;score_threshold,
                                float&nbsp;nms_threshold,
                                <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices)</pre>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.
 Reference: https://arxiv.org/abs/1704.04503</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply Soft NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>updated_scores</code> - a set of corresponding updated confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.
 SEE: SoftNMSMethod</dd>
</dl>
</li>
</ul>
<a name="softNMSBoxes-org.opencv.core.MatOfRect-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>softNMSBoxes</h4>
<pre>public static&nbsp;void&nbsp;softNMSBoxes(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
                                <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                                <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
                                float&nbsp;score_threshold,
                                float&nbsp;nms_threshold,
                                <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
                                long&nbsp;top_k)</pre>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.
 Reference: https://arxiv.org/abs/1704.04503</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply Soft NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>updated_scores</code> - a set of corresponding updated confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>top_k</code> - keep at most <code>top_k</code> picked indices.
 SEE: SoftNMSMethod</dd>
</dl>
</li>
</ul>
<a name="softNMSBoxes-org.opencv.core.MatOfRect-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-float-float-org.opencv.core.MatOfInt-long-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>softNMSBoxes</h4>
<pre>public static&nbsp;void&nbsp;softNMSBoxes(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;bboxes,
                                <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scores,
                                <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;updated_scores,
                                float&nbsp;score_threshold,
                                float&nbsp;nms_threshold,
                                <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indices,
                                long&nbsp;top_k,
                                float&nbsp;sigma)</pre>
<div class="block">Performs soft non maximum suppression given boxes and corresponding scores.
 Reference: https://arxiv.org/abs/1704.04503</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bboxes</code> - a set of bounding boxes to apply Soft NMS.</dd>
<dd><code>scores</code> - a set of corresponding confidences.</dd>
<dd><code>updated_scores</code> - a set of corresponding updated confidences.</dd>
<dd><code>score_threshold</code> - a threshold used to filter boxes by score.</dd>
<dd><code>nms_threshold</code> - a threshold used in non maximum suppression.</dd>
<dd><code>indices</code> - the kept indices of bboxes after NMS.</dd>
<dd><code>top_k</code> - keep at most <code>top_k</code> picked indices.</dd>
<dd><code>sigma</code> - parameter of Gaussian weighting.
 SEE: SoftNMSMethod</dd>
</dl>
</li>
</ul>
<a name="writeTextGraph-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeTextGraph</h4>
<pre>public static&nbsp;void&nbsp;writeTextGraph(java.lang.String&nbsp;model,
                                  java.lang.String&nbsp;output)</pre>
<div class="block">Create a text representation for a binary network stored in protocol buffer format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - A path to binary network.</dd>
<dd><code>output</code> - A path to output text file to be created.

 <b>Note:</b> To reduce output file size, trained weights are not included.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/DictValue.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/KeypointsModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/Dnn.html" target="_top">Frames</a></li>
<li><a href="Dnn.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2022-06-07 08:57:54 / OpenCV 4.6.0</small></p>
</body>
</html>

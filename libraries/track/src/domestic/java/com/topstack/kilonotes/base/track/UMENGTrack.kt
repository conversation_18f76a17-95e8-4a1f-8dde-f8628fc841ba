package com.topstack.kilonotes.base.track

import android.content.Context
import com.topstack.kilonotes.umeng.Umeng
import com.topstack.kilonotes.xuanhu.behavior.FunBehaviorReporter
import com.umeng.analytics.MobclickAgent

object UMENGTrack : ITrack {

    override fun preInit(
        context: Context,
        key: String,
        pushKey: String?,
        channel: String,
        userPrivacyAgreed: Boolean,
        inDebugMode: Boolean
    ) {
        Umeng.preInitUmengIfNeed(context, key, channel, inDebugMode)
        if (userPrivacyAgreed) {
            init(context, key, pushKey, channel)
        }
        MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.MANUAL)
    }

    override fun init(context: Context, key: String, pushKey: String?, channel: String) {
        Umeng.initUmengIfNeed(context, key, pushKey, channel)
    }

    override fun onPageStart(pageName: String) {
        MobclickAgent.onPageStart(pageName)
    }

    override fun onPageEnd(pageName: String) {
        MobclickAgent.onPageEnd(pageName)
    }

    override fun onResume(context: Context) {
        MobclickAgent.onResume(context)
    }

    override fun onPause(context: Context) {
        MobclickAgent.onPause(context)
    }

    override fun onEvent(context: Context, eventID: String) {
        MobclickAgent.onEvent(context, eventID)
    }

    override fun onEventWithXuanhuBehavior(context: Context, eventID: String) {
        MobclickAgent.onEvent(context, eventID)
        FunBehaviorReporter.onBehaviorEvent(eventID, emptyMap())
    }

    override fun onEvent(context: Context, eventID: String, label: String) {
        MobclickAgent.onEvent(context, eventID, label)
    }

    override fun onEvent(context: Context, eventID: String, attributes: Map<String, String>) {
        MobclickAgent.onEvent(context, eventID, attributes)
    }

    override fun onEventWithXuanhuBehavior(
        context: Context,
        eventID: String,
        attributes: Map<String, String>
    ) {
        MobclickAgent.onEvent(context, eventID, attributes)
        FunBehaviorReporter.onBehaviorEvent(eventID, attributes)
    }

    override fun onEvent(
        context: Context, eventID: String, attributes: Map<String, String>, value: Int
    ) {
        MobclickAgent.onEventValue(context, eventID, attributes, value)
    }


}
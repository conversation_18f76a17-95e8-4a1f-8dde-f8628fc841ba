package com.topstack.kilonotes.base.track.event

import com.topstack.kilonotes.base.track.TrackFactory
import com.topstack.kilonotes.infra.util.AppUtils

sealed interface IEvent {
    val ID: String

    fun send() {
        val context = AppUtils.appContext
        when (this) {
            is IPageStartEvent -> TrackFactory.track.onPageStart(ID)
            is IPageEndEvent -> TrackFactory.track.onPageEnd(ID)
            is ICountEvent -> TrackFactory.track.onEvent(context, ID)
            is IAttributeEvent -> TrackFactory.track.onEvent(context, ID, attributes)
            is IValueEvent -> TrackFactory.track.onEvent(context, ID, attributes, duration)
        }
    }

    fun sendWithXuanhuBehavior() {
        when (this) {
            is ICountEvent -> TrackFactory.track.onEventWithXuanhuBehavior(AppUtils.appContext, ID)
            is IAttributeEvent -> TrackFactory.track.onEventWithXuanhuBehavior(
                AppUtils.appContext,
                ID,
                attributes
            )

            else -> {}
        }
    }
}

sealed interface IPageEvent : IEvent

interface IPageStartEvent : IPageEvent

interface IPageEndEvent : IPageEvent

interface ICountEvent : IEvent

interface IAttributeEvent : IEvent {
    var attributes: Map<String, String>
}

interface IValueEvent : IEvent {
    var duration: Int
    var attributes: Map<String, String>
}
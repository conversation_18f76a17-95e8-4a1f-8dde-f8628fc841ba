package com.topstack.kilonotes.base.track

import android.content.Context

interface ITrack {

    fun preInit(
        context: Context,
        key: String,
        pushKey: String?,
        channel: String,
        userPrivacyAgreed: Boolean,
        inDebugMode: Boolean,
    ) {
    }

    fun init(context: Context, key: String, pushKey: String?, channel: String) {}

    fun onPageStart(pageName: String) {}

    fun onPageEnd(pageName: String) {}

    fun onResume(context: Context) {}

    fun onPause(context: Context) {}

    fun onEvent(context: Context, eventID: String) {}

    fun onEventWithXuanhuBehavior(context: Context, eventID: String) {}

    fun onEvent(context: Context, eventID: String, label: String) {}

    fun onEvent(context: Context, eventID: String, attributes: Map<String, String>) {}

    fun onEventWithXuanhuBehavior(context: Context, eventID: String, attributes: Map<String, String>) {}

    fun onEvent(context: Context, eventID: String, attributes: Map<String, String>, value: Int) {}
}
package com.topstack.kilonotes.base.track.helper.cloudbackup

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy.Companion.REPLACE
import androidx.room.Query

/**
 *
 */
@Dao
interface CloudBackupReportDao {
    @Insert(onConflict = REPLACE)
    fun insertCloudBackupEventReportTime(vipNoteCountReportTime: CloudBackupEventReportTime)

    @Query("SELECT * FROM cloud_backup_event_report_time WHERE event = :event AND documentId = :documentId")
    fun getCloudBackupEventRecordInfo(
        event: String,
        documentId: String
    ): CloudBackupEventReportTime?

    @Insert(onConflict = REPLACE)
    fun insertCloudBackupNonVipLastReportModifiedTime(
        nonVipLastReportModifiedTime: CloudBackupNonVipLastReportModifiedTime
    )

    @Query("SELECT * FROM cloud_backup_non_vip_last_report_modified_time WHERE documentId = :documentId")
    fun getDocumentLastReportModifiedTime(documentId: String): CloudBackupNonVipLastReportModifiedTime?
}
package com.topstack.kilonotes.base.track.helper.db

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.topstack.kilonotes.base.track.helper.cloudbackup.CloudBackupEventReportTime
import com.topstack.kilonotes.base.track.helper.cloudbackup.CloudBackupNonVipLastReportModifiedTime
import com.topstack.kilonotes.base.track.helper.cloudbackup.CloudBackupReportDao
import com.topstack.kilonotes.infra.util.AppUtils

/**
 * 埋点辅助数据库，如果有的埋点有存储数据的需求，可以存到这个数据库中。
 */
@Database(
    entities = [
        CloudBackupEventReportTime::class,
        CloudBackupNonVipLastReportModifiedTime::class
    ],
    version = 1,
    exportSchema = true
)
abstract class TrackHelperDatabase : RoomDatabase() {

    abstract fun noteCountReportTimeDao(): CloudBackupReportDao

    companion object {
        private const val DATABASE_NAME = "track_helper.db"

        @Volatile
        private var INSTANCE: TrackHelperDatabase? = null

        fun getDatabase(): TrackHelperDatabase {
            if (null == INSTANCE) {
                synchronized(this) {
                    if (null == INSTANCE) {
                        INSTANCE = buildDatabase()
                    }
                }
            }
            return INSTANCE!!
        }

        private fun buildDatabase(): TrackHelperDatabase {
            return Room.databaseBuilder(
                AppUtils.appContext,
                TrackHelperDatabase::class.java,
                DATABASE_NAME
            ).build()
        }
    }
}
package com.topstack.kilonotes.xuanhu

import android.app.Application
import android.content.Context

interface IXuanhuHelper {
    suspend fun init(
        context: Application,
        deviceId: String,
        debug: Boolean,
        versionCode: Int,
        channel: String,
        callback: (Boolean?) -> Unit
    )

    fun sendEvent(eventKey: String)

    fun sendEvent(eventKey: String, eventExtras: Map<String, Any>)

    fun sendRepeatableEvent(eventKey: String, eventExtras: Map<String, Any>? = null) {}

    suspend fun getAllHeaderParams(context: Context, callback: (HashMap<String, String>) -> Unit)

    fun checkHeartbeat() {}
}
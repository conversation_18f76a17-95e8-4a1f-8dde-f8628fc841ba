package com.topstack.kilonotes.xuanhu

import android.app.Application
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import com.`fun`.openid.sdk.FunOpenIDSdk
import com.`fun`.report.sdk.FunReportConfig
import com.`fun`.report.sdk.FunReportSdk
import com.topstack.kilonotes.infra.device.DeviceUtils.getDeviceId
import com.topstack.kilonotes.infra.device.DeviceUtils.requestOaid
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.infra.util.toMd5
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext


object XuanhuHelper : IXuanhuHelper {

    const val TAG = "xuanhuTag"
    const val GET_IS_PAID_USER_DELAY_TIME = 30000L

    override suspend fun init(
        context: Application,
        deviceId: String,
        debug: <PERSON>olean,
        versionCode: Int,
        channel: String,
        callback: (Boolean?) -> Unit
    ) {
        val reportConfig = FunReportConfig.Builder()
            .setToken(deviceId)
            .setLogEnabled(debug)
            .setIbuEnabled(true)
            .setReportUrl("https://qn.top-stack.net")
            .build()
        FunReportSdk.getInstance().init(context, reportConfig)
        LogHelper.d(TAG, "悬壶：初始化 ")

        withContext(Dispatchers.Main) {
            delay(GET_IS_PAID_USER_DELAY_TIME)
            val isPaidUser = FunReportSdk.getInstance().isPaidUser
            callback.invoke(isPaidUser)
            LogHelper.d(TAG, "悬壶：isPaidUser:${isPaidUser}")
        }
    }

    override fun sendEvent(eventKey: String) {
        FunReportSdk.getInstance().onEvent(eventKey)
        LogHelper.d(TAG, "悬壶：key:${eventKey}")
    }

    override fun sendEvent(eventKey: String, eventExtras: Map<String, Any>) {
        FunReportSdk.getInstance().onEvent(eventKey, eventExtras)
        LogHelper.d(TAG, "悬壶：key:${eventKey},value:${eventExtras}")
    }

    override fun sendRepeatableEvent(eventKey: String, eventExtras: Map<String, Any>?) {
        FunReportSdk.getInstance()
            .onPaymentEvent(eventKey, 0.0, FunReportSdk.PaymentCurrency.CNY, eventExtras)
    }


    override suspend fun getAllHeaderParams(
        context: Context,
        callback: (HashMap<String, String>) -> Unit
    ) {
        requestOaid(context) { oaid ->
            val headerMap = hashMapOf<String, String>()
            val channel = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                context.getPackageManager()
                    .getApplicationInfo(
                        context.getPackageName(),
                        PackageManager.ApplicationInfoFlags.of(PackageManager.GET_META_DATA.toLong())
                    ).metaData.getString("app.build.channel")
            } else {
                context.getPackageManager()
                    .getApplicationInfo(
                        context.getPackageName(),
                        PackageManager.GET_META_DATA
                    ).metaData.getString("app.build.channel")
            }
            val anid = FunOpenIDSdk.getAndroidId(context)
            val appvn = AppUtils.packageInfo.versionName
            val sysv = Build.VERSION.SDK_INT.toString()
            val model = Build.MODEL
            val manu = Build.BRAND
            val appv = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                AppUtils.packageInfo.longVersionCode
            } else {
                AppUtils.packageInfo.versionCode
            }

            channel?.let {
                headerMap.put("channel", it)
            }
            headerMap["oaid"] = oaid
            headerMap["immd5"] = getDeviceId().toMd5()
            headerMap["anid"] = anid
            headerMap["appvn"] = appvn
            headerMap["appv"] = appv.toString()
            headerMap["sysv"] = sysv
            headerMap["model"] = model
            headerMap["manu"] = manu
            callback.invoke(headerMap)
        }
    }
}
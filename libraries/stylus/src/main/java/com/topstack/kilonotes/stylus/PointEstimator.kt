package com.topstack.kilonotes.stylus

import android.graphics.PointF
import android.view.MotionEvent
import android.view.View

/**
 *
 */
interface PointEstimator {
    companion object {
        const val TAG = "PointEstimator"
    }

    fun isSupported(): Boolean = false

    fun setEstimationEnabled(enabled: Boolean) {}

    fun onTouchEvent(event: MotionEvent) {}

    fun setRefreshRate(refreshRate: Float) {}

    fun getEstimatedPoints(): List<PointF> = emptyList()

    fun release() {}

    fun onDoodleViewCreate(doodleView: View) {}

    fun onDoodleViewSizeChanged(doodleView: View, w: Int, h: Int, oldw: Int, oldh: Int) {}

    fun onDoodleViewAttachedToWindow(doodleView: View) {}

    fun onDoodleViewDetachedFromWindow(doodleView: View) {}
}
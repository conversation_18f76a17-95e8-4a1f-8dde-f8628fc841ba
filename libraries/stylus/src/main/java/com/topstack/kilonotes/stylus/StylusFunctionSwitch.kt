package com.topstack.kilonotes.stylus

import android.content.Context
import android.view.KeyEvent
import androidx.lifecycle.LifecycleOwner

/**
 *
 */
interface StylusFunctionSwitch {
    fun registerFunctionSwitchCallback(
        context: Context,
        lifecycleOwner: LifecycleOwner,
        onSwitch: (switchType: FunctionSwitchType) -> <PERSON><PERSON><PERSON>,
    )

    fun unregisterFunctionSwitchCallback(
        context: Context,
        lifecycleOwner: LifecycleOwner,
        onSwitchAction: ((switchType: FunctionSwitchType) -> Boolean)? = null,
    )

    fun onKeyEvent(event: KeyEvent?) {}
}
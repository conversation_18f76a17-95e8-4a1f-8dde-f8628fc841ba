package com.topstack.kilonotes.xuanhu.behavior

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.google.gson.Gson
import com.topstack.kilonotes.infra.util.AppUtils

@Database(
    entities = [BehaviorEventEntity::class],
    version = 1,
    exportSchema = true
)
@TypeConverters(FunBehaviorReportDatabase.Converters::class)
abstract class FunBehaviorReportDatabase : RoomDatabase() {
    abstract fun behaviorEventDao(): BehaviorEventDao

    class Converters {
        private val gson = Gson()

        @TypeConverter
        fun fromBehaviorEvent(value: BehaviorEvent?): String? {
            if (value == null) {
                return null
            }
            return gson.toJson(value)
        }

        @TypeConverter
        fun toBehaviorEvent(value: String?): BehaviorEvent? {
            if (value == null) {
                return null
            }
            return try {
                gson.fromJson(value, BehaviorEvent::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    companion object {
        private const val DATABASE_NAME = "xuanhu_behavior_events.db"

        @Volatile
        private var INSTANCE: FunBehaviorReportDatabase? = null

        fun getDatabase(): FunBehaviorReportDatabase {
            if (null == INSTANCE) {
                synchronized(this) {
                    if (null == INSTANCE) {
                        INSTANCE = buildDatabase()
                    }
                }
            }
            return INSTANCE!!
        }

        private fun buildDatabase(): FunBehaviorReportDatabase {
            return Room.databaseBuilder(
                AppUtils.appContext,
                FunBehaviorReportDatabase::class.java,
                DATABASE_NAME
            ).build()
        }
    }
}
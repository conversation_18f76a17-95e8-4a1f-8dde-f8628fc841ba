package com.topstack.kilonotes.xuanhu.behavior

import com.google.gson.Gson
import com.topstack.kilonotes.infra.device.DeviceUtils
import com.topstack.kilonotes.infra.network.BusinessHttpRequest
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.xuanhu.behavior.FunBehaviorReporter.logTag
import com.topstack.kilonotes.xuanhu.encrypt.RequestEncryptHelper
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.UUID
import java.util.concurrent.TimeUnit

/**
 *
 */
class FunBehaviorReportRequester {
    companion object {
        private val needEncrypt = AppUtils.isPlayChannel()

        private val reportDomain: String = if (needEncrypt) {
            "https://crpt.top-stack.net"
        } else {
            "https://qc.top-stack.net/"
        }

        private val httpClient: OkHttpClient by lazy {
            OkHttpClient.Builder()
                .connectTimeout(BusinessHttpRequest.TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(BusinessHttpRequest.TIMEOUT, TimeUnit.SECONDS)
                .build()
        }

        private val gson = Gson()
        private val JSON = "application/json; charset=utf-8".toMediaType()
    }

    /**
     * 需要与服务端通用请求头里的 tk 一致。
     *
     * @see [com.topstack.kilonotes.infra.network.RequestCommonHeaderInterceptor]
     */
    private val token = DeviceUtils.getDeviceId()

    fun reportBehavior(events: List<BehaviorEvent?>): ReportResponse {
        val sessionId = UUID.randomUUID()
        LogHelper.i(
            logTag,
            "reportBehavior(sessionId: $sessionId, encrypt: $needEncrypt): ${events.map { it?.eventId }}"
        )
        LogHelper.d(logTag, "reportBehavior detail (sessionId: $sessionId, token: $token): $events")
        val completeEvents = events.filterNotNull().map {
            CompleteBehaviorEvent(eventId = it.eventId, attributes = it.attributes, token = token)
        }

        val bodyString: String = if (needEncrypt) {
            val jsonBody = gson.toJson(completeEvents)
            RequestEncryptHelper.buildEncryptedRequestBodyString(
                businessType = RequestEncryptHelper.businessTypeXuanHuBehaviorReport,
                originalMethod = "POST",
                originalPath = "/",
                originalParams = "",
                originalBody = jsonBody
            )
        } else {
            gson.toJson(completeEvents)
        }

        val targetUrl = if (needEncrypt) "$reportDomain/behavior" else reportDomain

        val request = Request.Builder()
            .url(targetUrl)
            .post(bodyString.toRequestBody(JSON))
            .build()

        val reportResponse = try {
            httpClient.newCall(request).execute().use { httpResponse ->
                if (!httpResponse.isSuccessful) {
                    return@use ReportResponse(
                        ret = httpResponse.code,
                        code = httpResponse.code,
                        msg = "HTTP error: ${httpResponse.message}"
                    )
                }

                if (needEncrypt) {
                    if (httpResponse.code == 200) {
                        val bodyString = httpResponse.body?.string()
                        if (bodyString.isNullOrEmpty()) {
                            ReportResponse(
                                httpResponse.code,
                                httpResponse.code,
                                httpResponse.message
                            )
                        } else {
                            val decryptedResponse = RequestEncryptHelper.decryptResponse(bodyString)
                            LogHelper.d(logTag, "decryptedResponse: $decryptedResponse")
                            gson.fromJson(decryptedResponse, ReportResponse::class.java)
                        }
                    } else {
                        ReportResponse(
                            httpResponse.code,
                            httpResponse.code,
                            httpResponse.message
                        )
                    }
                } else {
                    val responseBody = httpResponse.body?.string() ?: return@use ReportResponse(
                        ret = -1,
                        code = -1,
                        msg = "Empty response body"
                    )
                    gson.fromJson(responseBody, ReportResponse::class.java)
                }
            }
        } catch (e: Exception) {
            ReportResponse(
                ret = -1,
                code = -1,
                msg = "response failed: ${e.message}"
            )
        }
        LogHelper.i(logTag, "reportBehavior response (sessionId: $sessionId): $reportResponse")

        return reportResponse
    }

    data class ReportResponse(
        val ret: Int,
        val code: Int,
        val msg: String
    ) {
        val isSuccessful: Boolean get() = ret == 200
    }
}
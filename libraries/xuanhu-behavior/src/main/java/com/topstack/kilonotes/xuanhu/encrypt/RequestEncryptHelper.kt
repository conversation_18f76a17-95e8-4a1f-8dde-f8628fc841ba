package com.topstack.kilonotes.xuanhu.encrypt

import android.util.Base64
import androidx.annotation.Keep
import com.google.gson.Gson
import com.topstack.kilonotes.infra.util.LogHelper
import java.security.MessageDigest

/**
 *
 */
object RequestEncryptHelper {
    const val businessTypeXuanHuReport = 3
    const val businessTypeXuanHuBehaviorReport = 5

    private val TAG = "RequestEncryptHelper"

    private val rootDomain = "top-stack.net"
    private val gson by lazy {
        Gson()
    }

    private fun buildSecretKeys(): AesGcmWithIntegrity.SecretKeys {
        val password = Base64.encodeToString(
            MessageDigest.getInstance("SHA1")
                .digest(rootDomain.toByteArray()), Base64.NO_WRAP
        )

        val salt = Base64.encodeToString(
            MessageDigest.getInstance("MD5")
                .digest(rootDomain.toByteArray()), Base64.NO_WRAP
        )

        return AesGcmWithIntegrity.generateKeyFromPassword(password, salt)
    }

    /**
     * 构建加密请求体。
     * @param businessType 业务类型
     * @param originalMethod 原始请求方法， 如"GET"
     * @param originalPath 原始请求路径, 如 "/abcd/dfgh"
     * @param originalHeaders 原始请求头（http规范的请求头没必要放进来）
     * @param originalParams 原始请求url参数字符串，需要对每个参数都进行urlencode
     * @param originalBody 原始请求体
     *
     * @see [参考文档](https://rqqmslsz9y.feishu.cn/docx/OgJJd1HlUouv02xxfCFcUeuRnPF)
     */
    fun buildEncryptedRequestBodyString(
        businessType: Int,
        originalMethod: String,
        originalPath: String,
        originalHeaders: Map<String, String> = emptyMap(),
        originalParams: String = "",
        originalBody: String = "",
    ): String {
        val encryptingRequestBody = EncryptingRequestBody(
            businessType,
            originalMethod,
            originalPath,
            originalHeaders,
            originalParams,
            originalBody
        )

        val encryptingRequestBodyString = gson.toJson(encryptingRequestBody)
        LogHelper.d(TAG, "encryptingRequestBodyString: $encryptingRequestBodyString")

        val secretKeys = buildSecretKeys()
        val encryptedRequestBodyString = AesGcmWithIntegrity.encrypt(
            encryptingRequestBodyString,
            secretKeys
        ).toString()
        LogHelper.d(TAG, "------------------------------------------------------------")
        LogHelper.d(TAG, "encryptedRequestBodyString: $encryptedRequestBodyString")

        return encryptedRequestBodyString
    }

    fun decryptResponse(response: String): String {
        val secretKeys = buildSecretKeys()
        val cipherTextIvMac = AesGcmWithIntegrity.CipherTextIvMac(response)
        val decryptedResponse = AesGcmWithIntegrity.decryptString(cipherTextIvMac, secretKeys)
        LogHelper.d(TAG, "decryptedResponse: $decryptedResponse")

        return decryptedResponse
    }
}

@Keep
data class EncryptingRequestBody(
    val businessType: Int,
    val method: String,
    val path: String,
    val headers: Map<String, String>,
    val params: String,
    val body: String
)

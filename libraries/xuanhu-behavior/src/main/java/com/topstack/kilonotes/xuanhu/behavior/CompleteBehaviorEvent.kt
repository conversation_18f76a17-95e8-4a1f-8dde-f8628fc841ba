package com.topstack.kilonotes.xuanhu.behavior

import android.os.Build
import androidx.annotation.Keep
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.infra.util.AppUtils
import java.util.Locale

/**
 *
 */
@Keep
data class CompleteBehaviorEvent(
    val eventId: String,
    val attributes: Map<String, String>,
    val app: String = AppUtils.appContext.packageName,
    val channel: String = AppUtils.channel,
    val appv: String = "${AppUtils.packageInfo.versionCode}",
    val appvn: String = AppUtils.packageInfo.versionName,
    val net: String = when {
        NetworkUtils.isWifiAvailable() -> "wifi"
        NetworkUtils.isNetworkAvailable() -> "mobile"
        else -> "none"
    },
    val manu: String = Build.MANUFACTURER,
    val brand: String = Build.BRAND,
    val model: String = Build.MODEL,
    val sysv: String = Build.VERSION.RELEASE,
    val h: String = AppUtils.appContext.resources.displayMetrics.heightPixels.toString(),
    val w: String = AppUtils.appContext.resources.displayMetrics.widthPixels.toString(),
    val locale: String = Locale.getDefault().toString(),
    val opcode: String = "",
    val token: String = "",
    val anid: String = "",
    val oaid: String = "",
    val gaid: String = "",
    val idfa: String = "",
    val plat: String = "a"
)

package com.topstack.kilonotes.xuanhu.behavior

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update

@Dao
interface BehaviorEventDao {
    @Insert
    suspend fun insert(event: BehaviorEventEntity)

    @Query("SELECT * FROM behavior_event WHERE retryCount <= :retryLimit AND (retryTime = 0 OR retryTime <= :currentTime) ORDER BY retryTime ASC LIMIT :countLimit")
    suspend fun getPendingEventsWithRetryTimeLimit(retryLimit: Int, countLimit: Int, currentTime: Long): List<BehaviorEventEntity>

    @Query("DELETE FROM behavior_event WHERE retryCount > :retryLimit")
    suspend fun deletePendingEventsExceedRetryLimit(retryLimit: Int)

    @Delete
    suspend fun delete(event: BehaviorEventEntity)

    @Delete
    suspend fun deleteEvents(events: List<BehaviorEventEntity>)

    @Update
    suspend fun update(event: BehaviorEventEntity)
}
package com.topstack.kilonotes.xuanhu.behavior

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.topstack.kilonotes.infra.network.NetworkUtils
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

object FunBehaviorReporter {
    const val logTag = "FunBehaviorReporter"

    private val requester: FunBehaviorReportRequester by lazy {
        FunBehaviorReportRequester()
    }

    private lateinit var database: FunBehaviorReportDatabase
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    private const val DEFAULT_BATCH_SIZE = 3
    private val retryLimit = 5

    private val isInitialized = AtomicBoolean(false)

    fun init(context: Context) {
        if (isInitialized.get()) {
            return
        }
        database = FunBehaviorReportDatabase.getDatabase()
        isInitialized.set(true)

        NetworkUtils.addNetworkChangeCallback(object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                retryFailedEvents()
            }
        })

        ProcessLifecycleOwner.get().lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                retryFailedEvents()
            }
        })
    }

    fun onBehaviorEvent(eventId: String, attributes: Map<String, String>) {
        onBehaviorEvent(BehaviorEvent(eventId, attributes))
    }

    fun onBehaviorEvent(behaviorEvent: BehaviorEvent) {
        if (!isInitialized.get()) {
            LogHelper.w(logTag, "onBehaviorEvent: FunBehaviorReporter is not initialized")
            return
        }
        scope.launch {
            try {
                val response = requester.reportBehavior(listOf(behaviorEvent))
                if (!response.isSuccessful) {
                    saveToDatabase(behaviorEvent)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                saveToDatabase(behaviorEvent)
            }
        }
    }

    private suspend fun saveToDatabase(event: BehaviorEvent) {
        database.behaviorEventDao().insert(BehaviorEventEntity(event = event))
    }


    private var isRetrying = false

    /**
     * 重试发送失败的事件
     * 建议在以下时机调用：
     * 1. APP 启动时
     * 2. 网络状态变化时（从无网络变为有网络）
     *
     * @param batchSize 每次重试的事件数量，默认为 3 条
     */
    private fun retryFailedEvents(batchSize: Int = DEFAULT_BATCH_SIZE) {
        // 删除旧数据库文件
        val oldDbName = "behavior_events.db"
        AppUtils.appContext.getDatabasePath(oldDbName).delete()
        AppUtils.appContext.getDatabasePath("$oldDbName-shm").delete()
        AppUtils.appContext.getDatabasePath("$oldDbName-wal").delete()
        AppUtils.appContext.getDatabasePath("$oldDbName-journal").delete()

        if (!isInitialized.get()) {
            LogHelper.w(logTag, "retryFailedEvents: FunBehaviorReporter is not initialized")
            return
        }
        if (isRetrying) {
            return
        }

        isRetrying = true
        scope.launch {
            try {
                LogHelper.d(logTag, "retryFailedEvents: start")
                var hasMoreEvents = true
                val currentTime = System.currentTimeMillis()
                database.behaviorEventDao().deletePendingEventsExceedRetryLimit(retryLimit)

                // 循环批量重试，直到没有符合条件的事件为止
                while (hasMoreEvents) {
                    // 直接从数据库获取符合条件的事件：重试次数小于等于10且重试时间小于当前时间的事件
                    val originalPendingEvents =
                        database.behaviorEventDao()
                            .getPendingEventsWithRetryTimeLimit(
                                retryLimit = retryLimit,
                                batchSize,
                                currentTime
                            )
                    if (originalPendingEvents.isEmpty()) {
                        hasMoreEvents = false
                        break
                    }

                    val ignoreEventIds = listOf("user_page_content", "user_all_note_size")
                    val deletingEvents = originalPendingEvents.filter { it.event == null || it.event.eventId in ignoreEventIds }
                    database.behaviorEventDao().deleteEvents(deletingEvents)

                    val filteredPendingEvents = originalPendingEvents.filter { it.event != null && it.event.eventId !in ignoreEventIds }
                    if (filteredPendingEvents.isEmpty()) {
                        continue
                    }

                    // 按批次处理事件
                    try {
                        val events = filteredPendingEvents.map { it.event }
                        val response = requester.reportBehavior(events)
                        if (response.isSuccessful) {
                            // 删除已成功上报的事件
                            filteredPendingEvents.forEach { database.behaviorEventDao().delete(it) }
                        } else {
                            // 更新重试次数和重试时间
                            // 设置下次重试时间为当前时间 + 5分钟
                            filteredPendingEvents.forEach {
                                val nextRetryCount = it.retryCount + 1
                                val nextRetryTime = currentTime + 5 * 60 * 1000
                                database.behaviorEventDao().update(
                                    it.copy(retryCount = nextRetryCount, retryTime = nextRetryTime)
                                )
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        // 更新重试次数和重试时间
                        filteredPendingEvents.forEach {
                            val nextRetryCount = it.retryCount + 1
                            val nextRetryTime = currentTime + 5 * 60 * 1000
                            database.behaviorEventDao().update(
                                it.copy(retryCount = nextRetryCount, retryTime = nextRetryTime)
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                isRetrying = false
            }
        }
    }
}
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: stylus_point.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public interface StylusPointListProtoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.StylusPointListProto)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  java.util.List<com.topstack.kilonotes.proto.StylusPointProto> 
      getPointsList();
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  com.topstack.kilonotes.proto.StylusPointProto getPoints(int index);
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  int getPointsCount();
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  java.util.List<? extends com.topstack.kilonotes.proto.StylusPointProtoOrBuilder> 
      getPointsOrBuilderList();
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  com.topstack.kilonotes.proto.StylusPointProtoOrBuilder getPointsOrBuilder(
      int index);
}

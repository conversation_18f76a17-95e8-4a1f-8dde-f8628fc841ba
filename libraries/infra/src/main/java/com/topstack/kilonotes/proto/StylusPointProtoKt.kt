// Generated by the protocol buffer compiler. DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: stylus_point.proto

// Generated files should ignore deprecation warnings
@file:Suppress("DEPRECATION")
package com.topstack.kilonotes.proto;

@kotlin.jvm.JvmName("-initializestylusPointProto")
public inline fun stylusPointProto(block: com.topstack.kilonotes.proto.StylusPointProtoKt.Dsl.() -> kotlin.Unit): com.topstack.kilonotes.proto.StylusPointProto =
  com.topstack.kilonotes.proto.StylusPointProtoKt.Dsl._create(com.topstack.kilonotes.proto.StylusPointProto.newBuilder()).apply { block() }._build()
/**
 * Protobuf type `com.topstack.kilonotes.StylusPointProto`
 */
public object StylusPointProtoKt {
  @kotlin.OptIn(com.google.protobuf.kotlin.OnlyForUseByGeneratedProtoCode::class)
  @com.google.protobuf.kotlin.ProtoDslMarker
  public class Dsl private constructor(
    private val _builder: com.topstack.kilonotes.proto.StylusPointProto.Builder
  ) {
    public companion object {
      @kotlin.jvm.JvmSynthetic
    @kotlin.PublishedApi
      internal fun _create(builder: com.topstack.kilonotes.proto.StylusPointProto.Builder): Dsl = Dsl(builder)
    }

    @kotlin.jvm.JvmSynthetic
  @kotlin.PublishedApi
    internal fun _build(): com.topstack.kilonotes.proto.StylusPointProto = _builder.build()

    /**
     * `float x = 1;`
     */
    public var x: kotlin.Float
      @JvmName("getX")
      get() = _builder.x
      @JvmName("setX")
      set(value) {
        _builder.x = value
      }
    /**
     * `float x = 1;`
     */
    public fun clearX() {
      _builder.clearX()
    }

    /**
     * `float y = 2;`
     */
    public var y: kotlin.Float
      @JvmName("getY")
      get() = _builder.y
      @JvmName("setY")
      set(value) {
        _builder.y = value
      }
    /**
     * `float y = 2;`
     */
    public fun clearY() {
      _builder.clearY()
    }

    /**
     * `float pressure = 3;`
     */
    public var pressure: kotlin.Float
      @JvmName("getPressure")
      get() = _builder.pressure
      @JvmName("setPressure")
      set(value) {
        _builder.pressure = value
      }
    /**
     * `float pressure = 3;`
     */
    public fun clearPressure() {
      _builder.clearPressure()
    }

    /**
     * `bool discontinuity = 4;`
     */
    public var discontinuity: kotlin.Boolean
      @JvmName("getDiscontinuity")
      get() = _builder.discontinuity
      @JvmName("setDiscontinuity")
      set(value) {
        _builder.discontinuity = value
      }
    /**
     * `bool discontinuity = 4;`
     */
    public fun clearDiscontinuity() {
      _builder.clearDiscontinuity()
    }

    /**
     * `int64 event_time = 5;`
     */
    public var eventTime: kotlin.Long
      @JvmName("getEventTime")
      get() = _builder.eventTime
      @JvmName("setEventTime")
      set(value) {
        _builder.eventTime = value
      }
    /**
     * `int64 event_time = 5;`
     */
    public fun clearEventTime() {
      _builder.clearEventTime()
    }
  }
}
@kotlin.jvm.JvmSynthetic
public inline fun com.topstack.kilonotes.proto.StylusPointProto.copy(block: `com.topstack.kilonotes.proto`.StylusPointProtoKt.Dsl.() -> kotlin.Unit): com.topstack.kilonotes.proto.StylusPointProto =
  `com.topstack.kilonotes.proto`.StylusPointProtoKt.Dsl._create(this.toBuilder()).apply { block() }._build()


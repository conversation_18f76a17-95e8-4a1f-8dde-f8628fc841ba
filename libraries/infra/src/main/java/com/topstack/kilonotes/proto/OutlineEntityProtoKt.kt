// Generated by the protocol buffer compiler. DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: outline.proto

// Generated files should ignore deprecation warnings
@file:Suppress("DEPRECATION")
package com.topstack.kilonotes.proto;

@kotlin.jvm.JvmName("-initializeoutlineEntityProto")
public inline fun outlineEntityProto(block: com.topstack.kilonotes.proto.OutlineEntityProtoKt.Dsl.() -> kotlin.Unit): com.topstack.kilonotes.proto.OutlineEntityProto =
  com.topstack.kilonotes.proto.OutlineEntityProtoKt.Dsl._create(com.topstack.kilonotes.proto.OutlineEntityProto.newBuilder()).apply { block() }._build()
/**
 * ```
 * 对应 OutlineEntity 类
 * ```
 *
 * Protobuf type `com.topstack.kilonotes.OutlineEntityProto`
 */
public object OutlineEntityProtoKt {
  @kotlin.OptIn(com.google.protobuf.kotlin.OnlyForUseByGeneratedProtoCode::class)
  @com.google.protobuf.kotlin.ProtoDslMarker
  public class Dsl private constructor(
    private val _builder: com.topstack.kilonotes.proto.OutlineEntityProto.Builder
  ) {
    public companion object {
      @kotlin.jvm.JvmSynthetic
    @kotlin.PublishedApi
      internal fun _create(builder: com.topstack.kilonotes.proto.OutlineEntityProto.Builder): Dsl = Dsl(builder)
    }

    @kotlin.jvm.JvmSynthetic
  @kotlin.PublishedApi
    internal fun _build(): com.topstack.kilonotes.proto.OutlineEntityProto = _builder.build()

    /**
     * `string title = 1;`
     */
    public var title: kotlin.String
      @JvmName("getTitle")
      get() = _builder.title
      @JvmName("setTitle")
      set(value) {
        _builder.title = value
      }
    /**
     * `string title = 1;`
     */
    public fun clearTitle() {
      _builder.clearTitle()
    }

    /**
     * `.com.topstack.kilonotes.TitleLevelProto title_level = 2;`
     */
    public var titleLevel: com.topstack.kilonotes.proto.TitleLevelProto
      @JvmName("getTitleLevel")
      get() = _builder.titleLevel
      @JvmName("setTitleLevel")
      set(value) {
        _builder.titleLevel = value
      }
    public var titleLevelValue: kotlin.Int
      @JvmName("getTitleLevelValue")
      get() = _builder.titleLevelValue
      @JvmName("setTitleLevelValue")
      set(value) {
        _builder.titleLevelValue = value
      }
    /**
     * `.com.topstack.kilonotes.TitleLevelProto title_level = 2;`
     */
    public fun clearTitleLevel() {
      _builder.clearTitleLevel()
    }

    /**
     * ```
     * UUID 以字符串形式存储
     * ```
     *
     * `string linked_page_uuid = 3;`
     */
    public var linkedPageUuid: kotlin.String
      @JvmName("getLinkedPageUuid")
      get() = _builder.linkedPageUuid
      @JvmName("setLinkedPageUuid")
      set(value) {
        _builder.linkedPageUuid = value
      }
    /**
     * ```
     * UUID 以字符串形式存储
     * ```
     *
     * `string linked_page_uuid = 3;`
     */
    public fun clearLinkedPageUuid() {
      _builder.clearLinkedPageUuid()
    }

    /**
     * `int64 create_time = 4;`
     */
    public var createTime: kotlin.Long
      @JvmName("getCreateTime")
      get() = _builder.createTime
      @JvmName("setCreateTime")
      set(value) {
        _builder.createTime = value
      }
    /**
     * `int64 create_time = 4;`
     */
    public fun clearCreateTime() {
      _builder.clearCreateTime()
    }
  }
}
@kotlin.jvm.JvmSynthetic
public inline fun com.topstack.kilonotes.proto.OutlineEntityProto.copy(block: `com.topstack.kilonotes.proto`.OutlineEntityProtoKt.Dsl.() -> kotlin.Unit): com.topstack.kilonotes.proto.OutlineEntityProto =
  `com.topstack.kilonotes.proto`.OutlineEntityProtoKt.Dsl._create(this.toBuilder()).apply { block() }._build()


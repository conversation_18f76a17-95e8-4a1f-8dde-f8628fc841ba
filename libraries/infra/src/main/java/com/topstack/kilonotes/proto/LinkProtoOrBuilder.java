// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: link.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public interface LinkProtoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.LinkProto)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * PDF的页码
   * </pre>
   *
   * <code>int32 target_index = 1;</code>
   * @return The targetIndex.
   */
  int getTargetIndex();

  /**
   * <pre>
   * PDF中的区域
   * </pre>
   *
   * <code>.com.topstack.kilonotes.RectFProto pdf_bounds = 2;</code>
   * @return Whether the pdfBounds field is set.
   */
  boolean hasPdfBounds();
  /**
   * <pre>
   * PDF中的区域
   * </pre>
   *
   * <code>.com.topstack.kilonotes.RectFProto pdf_bounds = 2;</code>
   * @return The pdfBounds.
   */
  com.topstack.kilonotes.proto.RectFProto getPdfBounds();
  /**
   * <pre>
   * PDF中的区域
   * </pre>
   *
   * <code>.com.topstack.kilonotes.RectFProto pdf_bounds = 2;</code>
   */
  com.topstack.kilonotes.proto.RectFProtoOrBuilder getPdfBoundsOrBuilder();
}

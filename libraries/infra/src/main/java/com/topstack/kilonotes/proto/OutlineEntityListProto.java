// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: outline.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

/**
 * <pre>
 * 大纲实体列表
 * </pre>
 *
 * Protobuf type {@code com.topstack.kilonotes.OutlineEntityListProto}
 */
public final class OutlineEntityListProto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:com.topstack.kilonotes.OutlineEntityListProto)
    OutlineEntityListProtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 3,
      /* suffix= */ "",
      OutlineEntityListProto.class.getName());
  }
  // Use OutlineEntityListProto.newBuilder() to construct.
  private OutlineEntityListProto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private OutlineEntityListProto() {
    outlines_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityListProto_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityListProto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.topstack.kilonotes.proto.OutlineEntityListProto.class, com.topstack.kilonotes.proto.OutlineEntityListProto.Builder.class);
  }

  public static final int OUTLINES_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.topstack.kilonotes.proto.OutlineEntityProto> outlines_;
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.topstack.kilonotes.proto.OutlineEntityProto> getOutlinesList() {
    return outlines_;
  }
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder> 
      getOutlinesOrBuilderList() {
    return outlines_;
  }
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  @java.lang.Override
  public int getOutlinesCount() {
    return outlines_.size();
  }
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  @java.lang.Override
  public com.topstack.kilonotes.proto.OutlineEntityProto getOutlines(int index) {
    return outlines_.get(index);
  }
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  @java.lang.Override
  public com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder getOutlinesOrBuilder(
      int index) {
    return outlines_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < outlines_.size(); i++) {
      output.writeMessage(1, outlines_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < outlines_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, outlines_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.topstack.kilonotes.proto.OutlineEntityListProto)) {
      return super.equals(obj);
    }
    com.topstack.kilonotes.proto.OutlineEntityListProto other = (com.topstack.kilonotes.proto.OutlineEntityListProto) obj;

    if (!getOutlinesList()
        .equals(other.getOutlinesList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getOutlinesCount() > 0) {
      hash = (37 * hash) + OUTLINES_FIELD_NUMBER;
      hash = (53 * hash) + getOutlinesList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityListProto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.topstack.kilonotes.proto.OutlineEntityListProto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 大纲实体列表
   * </pre>
   *
   * Protobuf type {@code com.topstack.kilonotes.OutlineEntityListProto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.topstack.kilonotes.OutlineEntityListProto)
      com.topstack.kilonotes.proto.OutlineEntityListProtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityListProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityListProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.topstack.kilonotes.proto.OutlineEntityListProto.class, com.topstack.kilonotes.proto.OutlineEntityListProto.Builder.class);
    }

    // Construct using com.topstack.kilonotes.proto.OutlineEntityListProto.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (outlinesBuilder_ == null) {
        outlines_ = java.util.Collections.emptyList();
      } else {
        outlines_ = null;
        outlinesBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityListProto_descriptor;
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.OutlineEntityListProto getDefaultInstanceForType() {
      return com.topstack.kilonotes.proto.OutlineEntityListProto.getDefaultInstance();
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.OutlineEntityListProto build() {
      com.topstack.kilonotes.proto.OutlineEntityListProto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.OutlineEntityListProto buildPartial() {
      com.topstack.kilonotes.proto.OutlineEntityListProto result = new com.topstack.kilonotes.proto.OutlineEntityListProto(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.topstack.kilonotes.proto.OutlineEntityListProto result) {
      if (outlinesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          outlines_ = java.util.Collections.unmodifiableList(outlines_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.outlines_ = outlines_;
      } else {
        result.outlines_ = outlinesBuilder_.build();
      }
    }

    private void buildPartial0(com.topstack.kilonotes.proto.OutlineEntityListProto result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.topstack.kilonotes.proto.OutlineEntityListProto) {
        return mergeFrom((com.topstack.kilonotes.proto.OutlineEntityListProto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.topstack.kilonotes.proto.OutlineEntityListProto other) {
      if (other == com.topstack.kilonotes.proto.OutlineEntityListProto.getDefaultInstance()) return this;
      if (outlinesBuilder_ == null) {
        if (!other.outlines_.isEmpty()) {
          if (outlines_.isEmpty()) {
            outlines_ = other.outlines_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOutlinesIsMutable();
            outlines_.addAll(other.outlines_);
          }
          onChanged();
        }
      } else {
        if (!other.outlines_.isEmpty()) {
          if (outlinesBuilder_.isEmpty()) {
            outlinesBuilder_.dispose();
            outlinesBuilder_ = null;
            outlines_ = other.outlines_;
            bitField0_ = (bitField0_ & ~0x00000001);
            outlinesBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getOutlinesFieldBuilder() : null;
          } else {
            outlinesBuilder_.addAllMessages(other.outlines_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.topstack.kilonotes.proto.OutlineEntityProto m =
                  input.readMessage(
                      com.topstack.kilonotes.proto.OutlineEntityProto.parser(),
                      extensionRegistry);
              if (outlinesBuilder_ == null) {
                ensureOutlinesIsMutable();
                outlines_.add(m);
              } else {
                outlinesBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.topstack.kilonotes.proto.OutlineEntityProto> outlines_ =
      java.util.Collections.emptyList();
    private void ensureOutlinesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        outlines_ = new java.util.ArrayList<com.topstack.kilonotes.proto.OutlineEntityProto>(outlines_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        com.topstack.kilonotes.proto.OutlineEntityProto, com.topstack.kilonotes.proto.OutlineEntityProto.Builder, com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder> outlinesBuilder_;

    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public java.util.List<com.topstack.kilonotes.proto.OutlineEntityProto> getOutlinesList() {
      if (outlinesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(outlines_);
      } else {
        return outlinesBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public int getOutlinesCount() {
      if (outlinesBuilder_ == null) {
        return outlines_.size();
      } else {
        return outlinesBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public com.topstack.kilonotes.proto.OutlineEntityProto getOutlines(int index) {
      if (outlinesBuilder_ == null) {
        return outlines_.get(index);
      } else {
        return outlinesBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder setOutlines(
        int index, com.topstack.kilonotes.proto.OutlineEntityProto value) {
      if (outlinesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOutlinesIsMutable();
        outlines_.set(index, value);
        onChanged();
      } else {
        outlinesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder setOutlines(
        int index, com.topstack.kilonotes.proto.OutlineEntityProto.Builder builderForValue) {
      if (outlinesBuilder_ == null) {
        ensureOutlinesIsMutable();
        outlines_.set(index, builderForValue.build());
        onChanged();
      } else {
        outlinesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder addOutlines(com.topstack.kilonotes.proto.OutlineEntityProto value) {
      if (outlinesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOutlinesIsMutable();
        outlines_.add(value);
        onChanged();
      } else {
        outlinesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder addOutlines(
        int index, com.topstack.kilonotes.proto.OutlineEntityProto value) {
      if (outlinesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOutlinesIsMutable();
        outlines_.add(index, value);
        onChanged();
      } else {
        outlinesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder addOutlines(
        com.topstack.kilonotes.proto.OutlineEntityProto.Builder builderForValue) {
      if (outlinesBuilder_ == null) {
        ensureOutlinesIsMutable();
        outlines_.add(builderForValue.build());
        onChanged();
      } else {
        outlinesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder addOutlines(
        int index, com.topstack.kilonotes.proto.OutlineEntityProto.Builder builderForValue) {
      if (outlinesBuilder_ == null) {
        ensureOutlinesIsMutable();
        outlines_.add(index, builderForValue.build());
        onChanged();
      } else {
        outlinesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder addAllOutlines(
        java.lang.Iterable<? extends com.topstack.kilonotes.proto.OutlineEntityProto> values) {
      if (outlinesBuilder_ == null) {
        ensureOutlinesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, outlines_);
        onChanged();
      } else {
        outlinesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder clearOutlines() {
      if (outlinesBuilder_ == null) {
        outlines_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        outlinesBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public Builder removeOutlines(int index) {
      if (outlinesBuilder_ == null) {
        ensureOutlinesIsMutable();
        outlines_.remove(index);
        onChanged();
      } else {
        outlinesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public com.topstack.kilonotes.proto.OutlineEntityProto.Builder getOutlinesBuilder(
        int index) {
      return getOutlinesFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder getOutlinesOrBuilder(
        int index) {
      if (outlinesBuilder_ == null) {
        return outlines_.get(index);  } else {
        return outlinesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public java.util.List<? extends com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder> 
         getOutlinesOrBuilderList() {
      if (outlinesBuilder_ != null) {
        return outlinesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(outlines_);
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public com.topstack.kilonotes.proto.OutlineEntityProto.Builder addOutlinesBuilder() {
      return getOutlinesFieldBuilder().addBuilder(
          com.topstack.kilonotes.proto.OutlineEntityProto.getDefaultInstance());
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public com.topstack.kilonotes.proto.OutlineEntityProto.Builder addOutlinesBuilder(
        int index) {
      return getOutlinesFieldBuilder().addBuilder(
          index, com.topstack.kilonotes.proto.OutlineEntityProto.getDefaultInstance());
    }
    /**
     * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
     */
    public java.util.List<com.topstack.kilonotes.proto.OutlineEntityProto.Builder> 
         getOutlinesBuilderList() {
      return getOutlinesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        com.topstack.kilonotes.proto.OutlineEntityProto, com.topstack.kilonotes.proto.OutlineEntityProto.Builder, com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder> 
        getOutlinesFieldBuilder() {
      if (outlinesBuilder_ == null) {
        outlinesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            com.topstack.kilonotes.proto.OutlineEntityProto, com.topstack.kilonotes.proto.OutlineEntityProto.Builder, com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder>(
                outlines_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        outlines_ = null;
      }
      return outlinesBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:com.topstack.kilonotes.OutlineEntityListProto)
  }

  // @@protoc_insertion_point(class_scope:com.topstack.kilonotes.OutlineEntityListProto)
  private static final com.topstack.kilonotes.proto.OutlineEntityListProto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.topstack.kilonotes.proto.OutlineEntityListProto();
  }

  public static com.topstack.kilonotes.proto.OutlineEntityListProto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<OutlineEntityListProto>
      PARSER = new com.google.protobuf.AbstractParser<OutlineEntityListProto>() {
    @java.lang.Override
    public OutlineEntityListProto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<OutlineEntityListProto> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<OutlineEntityListProto> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.topstack.kilonotes.proto.OutlineEntityListProto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}


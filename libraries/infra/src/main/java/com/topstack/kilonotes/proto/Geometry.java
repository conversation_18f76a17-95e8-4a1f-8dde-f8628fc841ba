// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: geometry.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public final class Geometry {
  private Geometry() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 3,
      /* suffix= */ "",
      Geometry.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PointFOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.proto.PointF)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>float x = 1;</code>
     * @return The x.
     */
    float getX();

    /**
     * <code>float y = 2;</code>
     * @return The y.
     */
    float getY();
  }
  /**
   * <pre>
   * 基本点坐标
   * </pre>
   *
   * Protobuf type {@code com.topstack.kilonotes.proto.PointF}
   */
  public static final class PointF extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.topstack.kilonotes.proto.PointF)
      PointFOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 3,
        /* suffix= */ "",
        PointF.class.getName());
    }
    // Use PointF.newBuilder() to construct.
    private PointF(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private PointF() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return Geometry.internal_static_com_topstack_kilonotes_proto_PointF_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return Geometry.internal_static_com_topstack_kilonotes_proto_PointF_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PointF.class, Builder.class);
    }

    public static final int X_FIELD_NUMBER = 1;
    private float x_ = 0F;
    /**
     * <code>float x = 1;</code>
     * @return The x.
     */
    @Override
    public float getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 2;
    private float y_ = 0F;
    /**
     * <code>float y = 2;</code>
     * @return The y.
     */
    @Override
    public float getY() {
      return y_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (Float.floatToRawIntBits(x_) != 0) {
        output.writeFloat(1, x_);
      }
      if (Float.floatToRawIntBits(y_) != 0) {
        output.writeFloat(2, y_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (Float.floatToRawIntBits(x_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(1, x_);
      }
      if (Float.floatToRawIntBits(y_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(2, y_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PointF)) {
        return super.equals(obj);
      }
      PointF other = (PointF) obj;

      if (Float.floatToIntBits(getX())
          != Float.floatToIntBits(
              other.getX())) return false;
      if (Float.floatToIntBits(getY())
          != Float.floatToIntBits(
              other.getY())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + X_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getX());
      hash = (37 * hash) + Y_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getY());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PointF parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PointF parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PointF parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PointF parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PointF parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PointF parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PointF parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static PointF parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static PointF parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static PointF parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PointF parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static PointF parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PointF prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 基本点坐标
     * </pre>
     *
     * Protobuf type {@code com.topstack.kilonotes.proto.PointF}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.topstack.kilonotes.proto.PointF)
        PointFOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return Geometry.internal_static_com_topstack_kilonotes_proto_PointF_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return Geometry.internal_static_com_topstack_kilonotes_proto_PointF_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PointF.class, Builder.class);
      }

      // Construct using com.topstack.kilonotes.proto.Geometry.PointF.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        x_ = 0F;
        y_ = 0F;
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return Geometry.internal_static_com_topstack_kilonotes_proto_PointF_descriptor;
      }

      @Override
      public PointF getDefaultInstanceForType() {
        return PointF.getDefaultInstance();
      }

      @Override
      public PointF build() {
        PointF result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public PointF buildPartial() {
        PointF result = new PointF(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(PointF result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.x_ = x_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.y_ = y_;
        }
      }

      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PointF) {
          return mergeFrom((PointF)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PointF other) {
        if (other == PointF.getDefaultInstance()) return this;
        if (other.getX() != 0F) {
          setX(other.getX());
        }
        if (other.getY() != 0F) {
          setY(other.getY());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 13: {
                x_ = input.readFloat();
                bitField0_ |= 0x00000001;
                break;
              } // case 13
              case 21: {
                y_ = input.readFloat();
                bitField0_ |= 0x00000002;
                break;
              } // case 21
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private float x_ ;
      /**
       * <code>float x = 1;</code>
       * @return The x.
       */
      @Override
      public float getX() {
        return x_;
      }
      /**
       * <code>float x = 1;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(float value) {

        x_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>float x = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        bitField0_ = (bitField0_ & ~0x00000001);
        x_ = 0F;
        onChanged();
        return this;
      }

      private float y_ ;
      /**
       * <code>float y = 2;</code>
       * @return The y.
       */
      @Override
      public float getY() {
        return y_;
      }
      /**
       * <code>float y = 2;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(float value) {

        y_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>float y = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        bitField0_ = (bitField0_ & ~0x00000002);
        y_ = 0F;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:com.topstack.kilonotes.proto.PointF)
    }

    // @@protoc_insertion_point(class_scope:com.topstack.kilonotes.proto.PointF)
    private static final PointF DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PointF();
    }

    public static PointF getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PointF>
        PARSER = new com.google.protobuf.AbstractParser<PointF>() {
      @Override
      public PointF parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<PointF> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<PointF> getParserForType() {
      return PARSER;
    }

    @Override
    public PointF getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RectFOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.proto.RectF)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>float left = 1;</code>
     * @return The left.
     */
    float getLeft();

    /**
     * <code>float top = 2;</code>
     * @return The top.
     */
    float getTop();

    /**
     * <code>float right = 3;</code>
     * @return The right.
     */
    float getRight();

    /**
     * <code>float bottom = 4;</code>
     * @return The bottom.
     */
    float getBottom();
  }
  /**
   * <pre>
   * 矩形区域
   * </pre>
   *
   * Protobuf type {@code com.topstack.kilonotes.proto.RectF}
   */
  public static final class RectF extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.topstack.kilonotes.proto.RectF)
      RectFOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 3,
        /* suffix= */ "",
        RectF.class.getName());
    }
    // Use RectF.newBuilder() to construct.
    private RectF(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RectF() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return Geometry.internal_static_com_topstack_kilonotes_proto_RectF_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return Geometry.internal_static_com_topstack_kilonotes_proto_RectF_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RectF.class, Builder.class);
    }

    public static final int LEFT_FIELD_NUMBER = 1;
    private float left_ = 0F;
    /**
     * <code>float left = 1;</code>
     * @return The left.
     */
    @Override
    public float getLeft() {
      return left_;
    }

    public static final int TOP_FIELD_NUMBER = 2;
    private float top_ = 0F;
    /**
     * <code>float top = 2;</code>
     * @return The top.
     */
    @Override
    public float getTop() {
      return top_;
    }

    public static final int RIGHT_FIELD_NUMBER = 3;
    private float right_ = 0F;
    /**
     * <code>float right = 3;</code>
     * @return The right.
     */
    @Override
    public float getRight() {
      return right_;
    }

    public static final int BOTTOM_FIELD_NUMBER = 4;
    private float bottom_ = 0F;
    /**
     * <code>float bottom = 4;</code>
     * @return The bottom.
     */
    @Override
    public float getBottom() {
      return bottom_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (Float.floatToRawIntBits(left_) != 0) {
        output.writeFloat(1, left_);
      }
      if (Float.floatToRawIntBits(top_) != 0) {
        output.writeFloat(2, top_);
      }
      if (Float.floatToRawIntBits(right_) != 0) {
        output.writeFloat(3, right_);
      }
      if (Float.floatToRawIntBits(bottom_) != 0) {
        output.writeFloat(4, bottom_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (Float.floatToRawIntBits(left_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(1, left_);
      }
      if (Float.floatToRawIntBits(top_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(2, top_);
      }
      if (Float.floatToRawIntBits(right_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(3, right_);
      }
      if (Float.floatToRawIntBits(bottom_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(4, bottom_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof RectF)) {
        return super.equals(obj);
      }
      RectF other = (RectF) obj;

      if (Float.floatToIntBits(getLeft())
          != Float.floatToIntBits(
              other.getLeft())) return false;
      if (Float.floatToIntBits(getTop())
          != Float.floatToIntBits(
              other.getTop())) return false;
      if (Float.floatToIntBits(getRight())
          != Float.floatToIntBits(
              other.getRight())) return false;
      if (Float.floatToIntBits(getBottom())
          != Float.floatToIntBits(
              other.getBottom())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LEFT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getLeft());
      hash = (37 * hash) + TOP_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getTop());
      hash = (37 * hash) + RIGHT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getRight());
      hash = (37 * hash) + BOTTOM_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getBottom());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static RectF parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RectF parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RectF parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RectF parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RectF parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RectF parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RectF parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static RectF parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static RectF parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static RectF parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static RectF parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static RectF parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(RectF prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 矩形区域
     * </pre>
     *
     * Protobuf type {@code com.topstack.kilonotes.proto.RectF}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.topstack.kilonotes.proto.RectF)
        RectFOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return Geometry.internal_static_com_topstack_kilonotes_proto_RectF_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return Geometry.internal_static_com_topstack_kilonotes_proto_RectF_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RectF.class, Builder.class);
      }

      // Construct using com.topstack.kilonotes.proto.Geometry.RectF.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        left_ = 0F;
        top_ = 0F;
        right_ = 0F;
        bottom_ = 0F;
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return Geometry.internal_static_com_topstack_kilonotes_proto_RectF_descriptor;
      }

      @Override
      public RectF getDefaultInstanceForType() {
        return RectF.getDefaultInstance();
      }

      @Override
      public RectF build() {
        RectF result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public RectF buildPartial() {
        RectF result = new RectF(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(RectF result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.left_ = left_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.top_ = top_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.right_ = right_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.bottom_ = bottom_;
        }
      }

      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RectF) {
          return mergeFrom((RectF)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RectF other) {
        if (other == RectF.getDefaultInstance()) return this;
        if (other.getLeft() != 0F) {
          setLeft(other.getLeft());
        }
        if (other.getTop() != 0F) {
          setTop(other.getTop());
        }
        if (other.getRight() != 0F) {
          setRight(other.getRight());
        }
        if (other.getBottom() != 0F) {
          setBottom(other.getBottom());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 13: {
                left_ = input.readFloat();
                bitField0_ |= 0x00000001;
                break;
              } // case 13
              case 21: {
                top_ = input.readFloat();
                bitField0_ |= 0x00000002;
                break;
              } // case 21
              case 29: {
                right_ = input.readFloat();
                bitField0_ |= 0x00000004;
                break;
              } // case 29
              case 37: {
                bottom_ = input.readFloat();
                bitField0_ |= 0x00000008;
                break;
              } // case 37
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private float left_ ;
      /**
       * <code>float left = 1;</code>
       * @return The left.
       */
      @Override
      public float getLeft() {
        return left_;
      }
      /**
       * <code>float left = 1;</code>
       * @param value The left to set.
       * @return This builder for chaining.
       */
      public Builder setLeft(float value) {

        left_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>float left = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLeft() {
        bitField0_ = (bitField0_ & ~0x00000001);
        left_ = 0F;
        onChanged();
        return this;
      }

      private float top_ ;
      /**
       * <code>float top = 2;</code>
       * @return The top.
       */
      @Override
      public float getTop() {
        return top_;
      }
      /**
       * <code>float top = 2;</code>
       * @param value The top to set.
       * @return This builder for chaining.
       */
      public Builder setTop(float value) {

        top_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>float top = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTop() {
        bitField0_ = (bitField0_ & ~0x00000002);
        top_ = 0F;
        onChanged();
        return this;
      }

      private float right_ ;
      /**
       * <code>float right = 3;</code>
       * @return The right.
       */
      @Override
      public float getRight() {
        return right_;
      }
      /**
       * <code>float right = 3;</code>
       * @param value The right to set.
       * @return This builder for chaining.
       */
      public Builder setRight(float value) {

        right_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>float right = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRight() {
        bitField0_ = (bitField0_ & ~0x00000004);
        right_ = 0F;
        onChanged();
        return this;
      }

      private float bottom_ ;
      /**
       * <code>float bottom = 4;</code>
       * @return The bottom.
       */
      @Override
      public float getBottom() {
        return bottom_;
      }
      /**
       * <code>float bottom = 4;</code>
       * @param value The bottom to set.
       * @return This builder for chaining.
       */
      public Builder setBottom(float value) {

        bottom_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>float bottom = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBottom() {
        bitField0_ = (bitField0_ & ~0x00000008);
        bottom_ = 0F;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:com.topstack.kilonotes.proto.RectF)
    }

    // @@protoc_insertion_point(class_scope:com.topstack.kilonotes.proto.RectF)
    private static final RectF DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new RectF();
    }

    public static RectF getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RectF>
        PARSER = new com.google.protobuf.AbstractParser<RectF>() {
      @Override
      public RectF parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RectF> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<RectF> getParserForType() {
      return PARSER;
    }

    @Override
    public RectF getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_topstack_kilonotes_proto_PointF_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_topstack_kilonotes_proto_PointF_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_topstack_kilonotes_proto_RectF_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_topstack_kilonotes_proto_RectF_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\016geometry.proto\022\034com.topstack.kilonotes" +
      ".proto\"\036\n\006PointF\022\t\n\001x\030\001 \001(\002\022\t\n\001y\030\002 \001(\002\"A" +
      "\n\005RectF\022\014\n\004left\030\001 \001(\002\022\013\n\003top\030\002 \001(\002\022\r\n\005ri" +
      "ght\030\003 \001(\002\022\016\n\006bottom\030\004 \001(\002b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_topstack_kilonotes_proto_PointF_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_topstack_kilonotes_proto_PointF_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_topstack_kilonotes_proto_PointF_descriptor,
        new String[] { "X", "Y", });
    internal_static_com_topstack_kilonotes_proto_RectF_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_topstack_kilonotes_proto_RectF_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_topstack_kilonotes_proto_RectF_descriptor,
        new String[] { "Left", "Top", "Right", "Bottom", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

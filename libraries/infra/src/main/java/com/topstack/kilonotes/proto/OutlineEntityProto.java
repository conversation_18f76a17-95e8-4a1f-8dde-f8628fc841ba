// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: outline.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

/**
 * <pre>
 * 对应 OutlineEntity 类
 * </pre>
 *
 * Protobuf type {@code com.topstack.kilonotes.OutlineEntityProto}
 */
public final class OutlineEntityProto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:com.topstack.kilonotes.OutlineEntityProto)
    OutlineEntityProtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 3,
      /* suffix= */ "",
      OutlineEntityProto.class.getName());
  }
  // Use OutlineEntityProto.newBuilder() to construct.
  private OutlineEntityProto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private OutlineEntityProto() {
    title_ = "";
    titleLevel_ = 0;
    linkedPageUuid_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityProto_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityProto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.topstack.kilonotes.proto.OutlineEntityProto.class, com.topstack.kilonotes.proto.OutlineEntityProto.Builder.class);
  }

  public static final int TITLE_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object title_ = "";
  /**
   * <code>string title = 1;</code>
   * @return The title.
   */
  @java.lang.Override
  public java.lang.String getTitle() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      title_ = s;
      return s;
    }
  }
  /**
   * <code>string title = 1;</code>
   * @return The bytes for title.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTitleBytes() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      title_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TITLE_LEVEL_FIELD_NUMBER = 2;
  private int titleLevel_ = 0;
  /**
   * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
   * @return The enum numeric value on the wire for titleLevel.
   */
  @java.lang.Override public int getTitleLevelValue() {
    return titleLevel_;
  }
  /**
   * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
   * @return The titleLevel.
   */
  @java.lang.Override public com.topstack.kilonotes.proto.TitleLevelProto getTitleLevel() {
    com.topstack.kilonotes.proto.TitleLevelProto result = com.topstack.kilonotes.proto.TitleLevelProto.forNumber(titleLevel_);
    return result == null ? com.topstack.kilonotes.proto.TitleLevelProto.UNRECOGNIZED : result;
  }

  public static final int LINKED_PAGE_UUID_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object linkedPageUuid_ = "";
  /**
   * <pre>
   * UUID 以字符串形式存储
   * </pre>
   *
   * <code>string linked_page_uuid = 3;</code>
   * @return The linkedPageUuid.
   */
  @java.lang.Override
  public java.lang.String getLinkedPageUuid() {
    java.lang.Object ref = linkedPageUuid_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      linkedPageUuid_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * UUID 以字符串形式存储
   * </pre>
   *
   * <code>string linked_page_uuid = 3;</code>
   * @return The bytes for linkedPageUuid.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLinkedPageUuidBytes() {
    java.lang.Object ref = linkedPageUuid_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      linkedPageUuid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CREATE_TIME_FIELD_NUMBER = 4;
  private long createTime_ = 0L;
  /**
   * <code>int64 create_time = 4;</code>
   * @return The createTime.
   */
  @java.lang.Override
  public long getCreateTime() {
    return createTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, title_);
    }
    if (titleLevel_ != com.topstack.kilonotes.proto.TitleLevelProto.ZERO.getNumber()) {
      output.writeEnum(2, titleLevel_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(linkedPageUuid_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, linkedPageUuid_);
    }
    if (createTime_ != 0L) {
      output.writeInt64(4, createTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, title_);
    }
    if (titleLevel_ != com.topstack.kilonotes.proto.TitleLevelProto.ZERO.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, titleLevel_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(linkedPageUuid_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, linkedPageUuid_);
    }
    if (createTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, createTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.topstack.kilonotes.proto.OutlineEntityProto)) {
      return super.equals(obj);
    }
    com.topstack.kilonotes.proto.OutlineEntityProto other = (com.topstack.kilonotes.proto.OutlineEntityProto) obj;

    if (!getTitle()
        .equals(other.getTitle())) return false;
    if (titleLevel_ != other.titleLevel_) return false;
    if (!getLinkedPageUuid()
        .equals(other.getLinkedPageUuid())) return false;
    if (getCreateTime()
        != other.getCreateTime()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TITLE_FIELD_NUMBER;
    hash = (53 * hash) + getTitle().hashCode();
    hash = (37 * hash) + TITLE_LEVEL_FIELD_NUMBER;
    hash = (53 * hash) + titleLevel_;
    hash = (37 * hash) + LINKED_PAGE_UUID_FIELD_NUMBER;
    hash = (53 * hash) + getLinkedPageUuid().hashCode();
    hash = (37 * hash) + CREATE_TIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCreateTime());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.topstack.kilonotes.proto.OutlineEntityProto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.topstack.kilonotes.proto.OutlineEntityProto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.topstack.kilonotes.proto.OutlineEntityProto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.topstack.kilonotes.proto.OutlineEntityProto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 对应 OutlineEntity 类
   * </pre>
   *
   * Protobuf type {@code com.topstack.kilonotes.OutlineEntityProto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.topstack.kilonotes.OutlineEntityProto)
      com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.topstack.kilonotes.proto.OutlineEntityProto.class, com.topstack.kilonotes.proto.OutlineEntityProto.Builder.class);
    }

    // Construct using com.topstack.kilonotes.proto.OutlineEntityProto.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      title_ = "";
      titleLevel_ = 0;
      linkedPageUuid_ = "";
      createTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.topstack.kilonotes.proto.Outline.internal_static_com_topstack_kilonotes_OutlineEntityProto_descriptor;
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.OutlineEntityProto getDefaultInstanceForType() {
      return com.topstack.kilonotes.proto.OutlineEntityProto.getDefaultInstance();
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.OutlineEntityProto build() {
      com.topstack.kilonotes.proto.OutlineEntityProto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.OutlineEntityProto buildPartial() {
      com.topstack.kilonotes.proto.OutlineEntityProto result = new com.topstack.kilonotes.proto.OutlineEntityProto(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.topstack.kilonotes.proto.OutlineEntityProto result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.title_ = title_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.titleLevel_ = titleLevel_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.linkedPageUuid_ = linkedPageUuid_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.createTime_ = createTime_;
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.topstack.kilonotes.proto.OutlineEntityProto) {
        return mergeFrom((com.topstack.kilonotes.proto.OutlineEntityProto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.topstack.kilonotes.proto.OutlineEntityProto other) {
      if (other == com.topstack.kilonotes.proto.OutlineEntityProto.getDefaultInstance()) return this;
      if (!other.getTitle().isEmpty()) {
        title_ = other.title_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.titleLevel_ != 0) {
        setTitleLevelValue(other.getTitleLevelValue());
      }
      if (!other.getLinkedPageUuid().isEmpty()) {
        linkedPageUuid_ = other.linkedPageUuid_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.getCreateTime() != 0L) {
        setCreateTime(other.getCreateTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              title_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              titleLevel_ = input.readEnum();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              linkedPageUuid_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              createTime_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object title_ = "";
    /**
     * <code>string title = 1;</code>
     * @return The title.
     */
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string title = 1;</code>
     * @return The bytes for title.
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string title = 1;</code>
     * @param value The title to set.
     * @return This builder for chaining.
     */
    public Builder setTitle(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      title_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string title = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitle() {
      title_ = getDefaultInstance().getTitle();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string title = 1;</code>
     * @param value The bytes for title to set.
     * @return This builder for chaining.
     */
    public Builder setTitleBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      title_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int titleLevel_ = 0;
    /**
     * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
     * @return The enum numeric value on the wire for titleLevel.
     */
    @java.lang.Override public int getTitleLevelValue() {
      return titleLevel_;
    }
    /**
     * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
     * @param value The enum numeric value on the wire for titleLevel to set.
     * @return This builder for chaining.
     */
    public Builder setTitleLevelValue(int value) {
      titleLevel_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
     * @return The titleLevel.
     */
    @java.lang.Override
    public com.topstack.kilonotes.proto.TitleLevelProto getTitleLevel() {
      com.topstack.kilonotes.proto.TitleLevelProto result = com.topstack.kilonotes.proto.TitleLevelProto.forNumber(titleLevel_);
      return result == null ? com.topstack.kilonotes.proto.TitleLevelProto.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
     * @param value The titleLevel to set.
     * @return This builder for chaining.
     */
    public Builder setTitleLevel(com.topstack.kilonotes.proto.TitleLevelProto value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000002;
      titleLevel_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitleLevel() {
      bitField0_ = (bitField0_ & ~0x00000002);
      titleLevel_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object linkedPageUuid_ = "";
    /**
     * <pre>
     * UUID 以字符串形式存储
     * </pre>
     *
     * <code>string linked_page_uuid = 3;</code>
     * @return The linkedPageUuid.
     */
    public java.lang.String getLinkedPageUuid() {
      java.lang.Object ref = linkedPageUuid_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        linkedPageUuid_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * UUID 以字符串形式存储
     * </pre>
     *
     * <code>string linked_page_uuid = 3;</code>
     * @return The bytes for linkedPageUuid.
     */
    public com.google.protobuf.ByteString
        getLinkedPageUuidBytes() {
      java.lang.Object ref = linkedPageUuid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        linkedPageUuid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * UUID 以字符串形式存储
     * </pre>
     *
     * <code>string linked_page_uuid = 3;</code>
     * @param value The linkedPageUuid to set.
     * @return This builder for chaining.
     */
    public Builder setLinkedPageUuid(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      linkedPageUuid_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * UUID 以字符串形式存储
     * </pre>
     *
     * <code>string linked_page_uuid = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearLinkedPageUuid() {
      linkedPageUuid_ = getDefaultInstance().getLinkedPageUuid();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * UUID 以字符串形式存储
     * </pre>
     *
     * <code>string linked_page_uuid = 3;</code>
     * @param value The bytes for linkedPageUuid to set.
     * @return This builder for chaining.
     */
    public Builder setLinkedPageUuidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      linkedPageUuid_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private long createTime_ ;
    /**
     * <code>int64 create_time = 4;</code>
     * @return The createTime.
     */
    @java.lang.Override
    public long getCreateTime() {
      return createTime_;
    }
    /**
     * <code>int64 create_time = 4;</code>
     * @param value The createTime to set.
     * @return This builder for chaining.
     */
    public Builder setCreateTime(long value) {

      createTime_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>int64 create_time = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCreateTime() {
      bitField0_ = (bitField0_ & ~0x00000008);
      createTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:com.topstack.kilonotes.OutlineEntityProto)
  }

  // @@protoc_insertion_point(class_scope:com.topstack.kilonotes.OutlineEntityProto)
  private static final com.topstack.kilonotes.proto.OutlineEntityProto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.topstack.kilonotes.proto.OutlineEntityProto();
  }

  public static com.topstack.kilonotes.proto.OutlineEntityProto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<OutlineEntityProto>
      PARSER = new com.google.protobuf.AbstractParser<OutlineEntityProto>() {
    @java.lang.Override
    public OutlineEntityProto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<OutlineEntityProto> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<OutlineEntityProto> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.topstack.kilonotes.proto.OutlineEntityProto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}


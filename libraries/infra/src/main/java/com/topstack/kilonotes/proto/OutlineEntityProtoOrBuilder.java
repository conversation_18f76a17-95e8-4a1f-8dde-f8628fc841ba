// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: outline.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public interface OutlineEntityProtoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.OutlineEntityProto)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string title = 1;</code>
   * @return The title.
   */
  java.lang.String getTitle();
  /**
   * <code>string title = 1;</code>
   * @return The bytes for title.
   */
  com.google.protobuf.ByteString
      getTitleBytes();

  /**
   * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
   * @return The enum numeric value on the wire for titleLevel.
   */
  int getTitleLevelValue();
  /**
   * <code>.com.topstack.kilonotes.TitleLevelProto title_level = 2;</code>
   * @return The titleLevel.
   */
  com.topstack.kilonotes.proto.TitleLevelProto getTitleLevel();

  /**
   * <pre>
   * UUID 以字符串形式存储
   * </pre>
   *
   * <code>string linked_page_uuid = 3;</code>
   * @return The linkedPageUuid.
   */
  java.lang.String getLinkedPageUuid();
  /**
   * <pre>
   * UUID 以字符串形式存储
   * </pre>
   *
   * <code>string linked_page_uuid = 3;</code>
   * @return The bytes for linkedPageUuid.
   */
  com.google.protobuf.ByteString
      getLinkedPageUuidBytes();

  /**
   * <code>int64 create_time = 4;</code>
   * @return The createTime.
   */
  long getCreateTime();
}

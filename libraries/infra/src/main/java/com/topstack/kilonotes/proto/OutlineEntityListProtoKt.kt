// Generated by the protocol buffer compiler. DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: outline.proto

// Generated files should ignore deprecation warnings
@file:Suppress("DEPRECATION")
package com.topstack.kilonotes.proto;

@kotlin.jvm.JvmName("-initializeoutlineEntityListProto")
public inline fun outlineEntityListProto(block: com.topstack.kilonotes.proto.OutlineEntityListProtoKt.Dsl.() -> kotlin.Unit): com.topstack.kilonotes.proto.OutlineEntityListProto =
  com.topstack.kilonotes.proto.OutlineEntityListProtoKt.Dsl._create(com.topstack.kilonotes.proto.OutlineEntityListProto.newBuilder()).apply { block() }._build()
/**
 * ```
 * 大纲实体列表
 * ```
 *
 * Protobuf type `com.topstack.kilonotes.OutlineEntityListProto`
 */
public object OutlineEntityListProtoKt {
  @kotlin.OptIn(com.google.protobuf.kotlin.OnlyForUseByGeneratedProtoCode::class)
  @com.google.protobuf.kotlin.ProtoDslMarker
  public class Dsl private constructor(
    private val _builder: com.topstack.kilonotes.proto.OutlineEntityListProto.Builder
  ) {
    public companion object {
      @kotlin.jvm.JvmSynthetic
    @kotlin.PublishedApi
      internal fun _create(builder: com.topstack.kilonotes.proto.OutlineEntityListProto.Builder): Dsl = Dsl(builder)
    }

    @kotlin.jvm.JvmSynthetic
  @kotlin.PublishedApi
    internal fun _build(): com.topstack.kilonotes.proto.OutlineEntityListProto = _builder.build()

    /**
     * An uninstantiable, behaviorless type to represent the field in
     * generics.
     */
    @kotlin.OptIn(com.google.protobuf.kotlin.OnlyForUseByGeneratedProtoCode::class)
    public class OutlinesProxy private constructor() : com.google.protobuf.kotlin.DslProxy()
    /**
     * `repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;`
     */
     public val outlines: com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.OutlineEntityProto, OutlinesProxy>
      @kotlin.jvm.JvmSynthetic
      get() = com.google.protobuf.kotlin.DslList(
        _builder.outlinesList
      )
    /**
     * `repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;`
     * @param value The outlines to add.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("addOutlines")
    public fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.OutlineEntityProto, OutlinesProxy>.add(value: com.topstack.kilonotes.proto.OutlineEntityProto) {
      _builder.addOutlines(value)
    }
    /**
     * `repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;`
     * @param value The outlines to add.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("plusAssignOutlines")
    @Suppress("NOTHING_TO_INLINE")
    public inline operator fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.OutlineEntityProto, OutlinesProxy>.plusAssign(value: com.topstack.kilonotes.proto.OutlineEntityProto) {
      add(value)
    }
    /**
     * `repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;`
     * @param values The outlines to add.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("addAllOutlines")
    public fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.OutlineEntityProto, OutlinesProxy>.addAll(values: kotlin.collections.Iterable<com.topstack.kilonotes.proto.OutlineEntityProto>) {
      _builder.addAllOutlines(values)
    }
    /**
     * `repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;`
     * @param values The outlines to add.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("plusAssignAllOutlines")
    @Suppress("NOTHING_TO_INLINE")
    public inline operator fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.OutlineEntityProto, OutlinesProxy>.plusAssign(values: kotlin.collections.Iterable<com.topstack.kilonotes.proto.OutlineEntityProto>) {
      addAll(values)
    }
    /**
     * `repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;`
     * @param index The index to set the value at.
     * @param value The outlines to set.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("setOutlines")
    public operator fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.OutlineEntityProto, OutlinesProxy>.set(index: kotlin.Int, value: com.topstack.kilonotes.proto.OutlineEntityProto) {
      _builder.setOutlines(index, value)
    }
    /**
     * `repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;`
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("clearOutlines")
    public fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.OutlineEntityProto, OutlinesProxy>.clear() {
      _builder.clearOutlines()
    }

  }
}
@kotlin.jvm.JvmSynthetic
public inline fun com.topstack.kilonotes.proto.OutlineEntityListProto.copy(block: `com.topstack.kilonotes.proto`.OutlineEntityListProtoKt.Dsl.() -> kotlin.Unit): com.topstack.kilonotes.proto.OutlineEntityListProto =
  `com.topstack.kilonotes.proto`.OutlineEntityListProtoKt.Dsl._create(this.toBuilder()).apply { block() }._build()


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: outline.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public interface OutlineEntityListProtoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.OutlineEntityListProto)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  java.util.List<com.topstack.kilonotes.proto.OutlineEntityProto> 
      getOutlinesList();
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  com.topstack.kilonotes.proto.OutlineEntityProto getOutlines(int index);
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  int getOutlinesCount();
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  java.util.List<? extends com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder> 
      getOutlinesOrBuilderList();
  /**
   * <code>repeated .com.topstack.kilonotes.OutlineEntityProto outlines = 1;</code>
   */
  com.topstack.kilonotes.proto.OutlineEntityProtoOrBuilder getOutlinesOrBuilder(
      int index);
}

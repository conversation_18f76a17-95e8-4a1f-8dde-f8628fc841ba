// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: stylus_point.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public interface StylusPointProtoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.StylusPointProto)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>float x = 1;</code>
   * @return The x.
   */
  float getX();

  /**
   * <code>float y = 2;</code>
   * @return The y.
   */
  float getY();

  /**
   * <code>float pressure = 3;</code>
   * @return The pressure.
   */
  float getPressure();

  /**
   * <code>bool discontinuity = 4;</code>
   * @return The discontinuity.
   */
  boolean getDiscontinuity();

  /**
   * <code>int64 event_time = 5;</code>
   * @return The eventTime.
   */
  long getEventTime();
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: link.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public interface LinkListProtoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.LinkListProto)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .com.topstack.kilonotes.LinkProto links = 1;</code>
   */
  java.util.List<com.topstack.kilonotes.proto.LinkProto> 
      getLinksList();
  /**
   * <code>repeated .com.topstack.kilonotes.LinkProto links = 1;</code>
   */
  com.topstack.kilonotes.proto.LinkProto getLinks(int index);
  /**
   * <code>repeated .com.topstack.kilonotes.LinkProto links = 1;</code>
   */
  int getLinksCount();
  /**
   * <code>repeated .com.topstack.kilonotes.LinkProto links = 1;</code>
   */
  java.util.List<? extends com.topstack.kilonotes.proto.LinkProtoOrBuilder> 
      getLinksOrBuilderList();
  /**
   * <code>repeated .com.topstack.kilonotes.LinkProto links = 1;</code>
   */
  com.topstack.kilonotes.proto.LinkProtoOrBuilder getLinksOrBuilder(
      int index);
}

// Generated by the protocol buffer compiler. DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: stylus_point.proto

// Generated files should ignore deprecation warnings
@file:Suppress("DEPRECATION")
package com.topstack.kilonotes.proto;

@kotlin.jvm.JvmName("-initializestylusPointListProto")
public inline fun stylusPointListProto(block: com.topstack.kilonotes.proto.StylusPointListProtoKt.Dsl.() -> kotlin.Unit): com.topstack.kilonotes.proto.StylusPointListProto =
  com.topstack.kilonotes.proto.StylusPointListProtoKt.Dsl._create(com.topstack.kilonotes.proto.StylusPointListProto.newBuilder()).apply { block() }._build()
/**
 * Protobuf type `com.topstack.kilonotes.StylusPointListProto`
 */
public object StylusPointListProtoKt {
  @kotlin.OptIn(com.google.protobuf.kotlin.OnlyForUseByGeneratedProtoCode::class)
  @com.google.protobuf.kotlin.ProtoDslMarker
  public class Dsl private constructor(
    private val _builder: com.topstack.kilonotes.proto.StylusPointListProto.Builder
  ) {
    public companion object {
      @kotlin.jvm.JvmSynthetic
    @kotlin.PublishedApi
      internal fun _create(builder: com.topstack.kilonotes.proto.StylusPointListProto.Builder): Dsl = Dsl(builder)
    }

    @kotlin.jvm.JvmSynthetic
  @kotlin.PublishedApi
    internal fun _build(): com.topstack.kilonotes.proto.StylusPointListProto = _builder.build()

    /**
     * An uninstantiable, behaviorless type to represent the field in
     * generics.
     */
    @kotlin.OptIn(com.google.protobuf.kotlin.OnlyForUseByGeneratedProtoCode::class)
    public class PointsProxy private constructor() : com.google.protobuf.kotlin.DslProxy()
    /**
     * `repeated .com.topstack.kilonotes.StylusPointProto points = 1;`
     */
     public val points: com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.StylusPointProto, PointsProxy>
      @kotlin.jvm.JvmSynthetic
      get() = com.google.protobuf.kotlin.DslList(
        _builder.pointsList
      )
    /**
     * `repeated .com.topstack.kilonotes.StylusPointProto points = 1;`
     * @param value The points to add.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("addPoints")
    public fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.StylusPointProto, PointsProxy>.add(value: com.topstack.kilonotes.proto.StylusPointProto) {
      _builder.addPoints(value)
    }
    /**
     * `repeated .com.topstack.kilonotes.StylusPointProto points = 1;`
     * @param value The points to add.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("plusAssignPoints")
    @Suppress("NOTHING_TO_INLINE")
    public inline operator fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.StylusPointProto, PointsProxy>.plusAssign(value: com.topstack.kilonotes.proto.StylusPointProto) {
      add(value)
    }
    /**
     * `repeated .com.topstack.kilonotes.StylusPointProto points = 1;`
     * @param values The points to add.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("addAllPoints")
    public fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.StylusPointProto, PointsProxy>.addAll(values: kotlin.collections.Iterable<com.topstack.kilonotes.proto.StylusPointProto>) {
      _builder.addAllPoints(values)
    }
    /**
     * `repeated .com.topstack.kilonotes.StylusPointProto points = 1;`
     * @param values The points to add.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("plusAssignAllPoints")
    @Suppress("NOTHING_TO_INLINE")
    public inline operator fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.StylusPointProto, PointsProxy>.plusAssign(values: kotlin.collections.Iterable<com.topstack.kilonotes.proto.StylusPointProto>) {
      addAll(values)
    }
    /**
     * `repeated .com.topstack.kilonotes.StylusPointProto points = 1;`
     * @param index The index to set the value at.
     * @param value The points to set.
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("setPoints")
    public operator fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.StylusPointProto, PointsProxy>.set(index: kotlin.Int, value: com.topstack.kilonotes.proto.StylusPointProto) {
      _builder.setPoints(index, value)
    }
    /**
     * `repeated .com.topstack.kilonotes.StylusPointProto points = 1;`
     */
    @kotlin.jvm.JvmSynthetic
    @kotlin.jvm.JvmName("clearPoints")
    public fun com.google.protobuf.kotlin.DslList<com.topstack.kilonotes.proto.StylusPointProto, PointsProxy>.clear() {
      _builder.clearPoints()
    }

  }
}
@kotlin.jvm.JvmSynthetic
public inline fun com.topstack.kilonotes.proto.StylusPointListProto.copy(block: `com.topstack.kilonotes.proto`.StylusPointListProtoKt.Dsl.() -> kotlin.Unit): com.topstack.kilonotes.proto.StylusPointListProto =
  `com.topstack.kilonotes.proto`.StylusPointListProtoKt.Dsl._create(this.toBuilder()).apply { block() }._build()


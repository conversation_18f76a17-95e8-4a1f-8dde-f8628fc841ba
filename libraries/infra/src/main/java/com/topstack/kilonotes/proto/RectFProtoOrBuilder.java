// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: link.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public interface RectFProtoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.RectFProto)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>float left = 1;</code>
   * @return The left.
   */
  float getLeft();

  /**
   * <code>float top = 2;</code>
   * @return The top.
   */
  float getTop();

  /**
   * <code>float right = 3;</code>
   * @return The right.
   */
  float getRight();

  /**
   * <code>float bottom = 4;</code>
   * @return The bottom.
   */
  float getBottom();
}

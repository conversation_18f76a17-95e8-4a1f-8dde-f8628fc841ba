// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: stylus_point.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

/**
 * Protobuf type {@code com.topstack.kilonotes.StylusPointListProto}
 */
public final class StylusPointListProto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:com.topstack.kilonotes.StylusPointListProto)
    StylusPointListProtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 3,
      /* suffix= */ "",
      StylusPointListProto.class.getName());
  }
  // Use StylusPointListProto.newBuilder() to construct.
  private StylusPointListProto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private StylusPointListProto() {
    points_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.topstack.kilonotes.proto.StylusPoint.internal_static_com_topstack_kilonotes_StylusPointListProto_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.topstack.kilonotes.proto.StylusPoint.internal_static_com_topstack_kilonotes_StylusPointListProto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.topstack.kilonotes.proto.StylusPointListProto.class, com.topstack.kilonotes.proto.StylusPointListProto.Builder.class);
  }

  public static final int POINTS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<com.topstack.kilonotes.proto.StylusPointProto> points_;
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  @java.lang.Override
  public java.util.List<com.topstack.kilonotes.proto.StylusPointProto> getPointsList() {
    return points_;
  }
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.topstack.kilonotes.proto.StylusPointProtoOrBuilder> 
      getPointsOrBuilderList() {
    return points_;
  }
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  @java.lang.Override
  public int getPointsCount() {
    return points_.size();
  }
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  @java.lang.Override
  public com.topstack.kilonotes.proto.StylusPointProto getPoints(int index) {
    return points_.get(index);
  }
  /**
   * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
   */
  @java.lang.Override
  public com.topstack.kilonotes.proto.StylusPointProtoOrBuilder getPointsOrBuilder(
      int index) {
    return points_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < points_.size(); i++) {
      output.writeMessage(1, points_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < points_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, points_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.topstack.kilonotes.proto.StylusPointListProto)) {
      return super.equals(obj);
    }
    com.topstack.kilonotes.proto.StylusPointListProto other = (com.topstack.kilonotes.proto.StylusPointListProto) obj;

    if (!getPointsList()
        .equals(other.getPointsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getPointsCount() > 0) {
      hash = (37 * hash) + POINTS_FIELD_NUMBER;
      hash = (53 * hash) + getPointsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.topstack.kilonotes.proto.StylusPointListProto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.topstack.kilonotes.proto.StylusPointListProto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.topstack.kilonotes.proto.StylusPointListProto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.topstack.kilonotes.proto.StylusPointListProto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.topstack.kilonotes.StylusPointListProto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.topstack.kilonotes.StylusPointListProto)
      com.topstack.kilonotes.proto.StylusPointListProtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.topstack.kilonotes.proto.StylusPoint.internal_static_com_topstack_kilonotes_StylusPointListProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.topstack.kilonotes.proto.StylusPoint.internal_static_com_topstack_kilonotes_StylusPointListProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.topstack.kilonotes.proto.StylusPointListProto.class, com.topstack.kilonotes.proto.StylusPointListProto.Builder.class);
    }

    // Construct using com.topstack.kilonotes.proto.StylusPointListProto.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (pointsBuilder_ == null) {
        points_ = java.util.Collections.emptyList();
      } else {
        points_ = null;
        pointsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.topstack.kilonotes.proto.StylusPoint.internal_static_com_topstack_kilonotes_StylusPointListProto_descriptor;
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.StylusPointListProto getDefaultInstanceForType() {
      return com.topstack.kilonotes.proto.StylusPointListProto.getDefaultInstance();
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.StylusPointListProto build() {
      com.topstack.kilonotes.proto.StylusPointListProto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.topstack.kilonotes.proto.StylusPointListProto buildPartial() {
      com.topstack.kilonotes.proto.StylusPointListProto result = new com.topstack.kilonotes.proto.StylusPointListProto(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.topstack.kilonotes.proto.StylusPointListProto result) {
      if (pointsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          points_ = java.util.Collections.unmodifiableList(points_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.points_ = points_;
      } else {
        result.points_ = pointsBuilder_.build();
      }
    }

    private void buildPartial0(com.topstack.kilonotes.proto.StylusPointListProto result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.topstack.kilonotes.proto.StylusPointListProto) {
        return mergeFrom((com.topstack.kilonotes.proto.StylusPointListProto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.topstack.kilonotes.proto.StylusPointListProto other) {
      if (other == com.topstack.kilonotes.proto.StylusPointListProto.getDefaultInstance()) return this;
      if (pointsBuilder_ == null) {
        if (!other.points_.isEmpty()) {
          if (points_.isEmpty()) {
            points_ = other.points_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePointsIsMutable();
            points_.addAll(other.points_);
          }
          onChanged();
        }
      } else {
        if (!other.points_.isEmpty()) {
          if (pointsBuilder_.isEmpty()) {
            pointsBuilder_.dispose();
            pointsBuilder_ = null;
            points_ = other.points_;
            bitField0_ = (bitField0_ & ~0x00000001);
            pointsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getPointsFieldBuilder() : null;
          } else {
            pointsBuilder_.addAllMessages(other.points_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.topstack.kilonotes.proto.StylusPointProto m =
                  input.readMessage(
                      com.topstack.kilonotes.proto.StylusPointProto.parser(),
                      extensionRegistry);
              if (pointsBuilder_ == null) {
                ensurePointsIsMutable();
                points_.add(m);
              } else {
                pointsBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<com.topstack.kilonotes.proto.StylusPointProto> points_ =
      java.util.Collections.emptyList();
    private void ensurePointsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        points_ = new java.util.ArrayList<com.topstack.kilonotes.proto.StylusPointProto>(points_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        com.topstack.kilonotes.proto.StylusPointProto, com.topstack.kilonotes.proto.StylusPointProto.Builder, com.topstack.kilonotes.proto.StylusPointProtoOrBuilder> pointsBuilder_;

    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public java.util.List<com.topstack.kilonotes.proto.StylusPointProto> getPointsList() {
      if (pointsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(points_);
      } else {
        return pointsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public int getPointsCount() {
      if (pointsBuilder_ == null) {
        return points_.size();
      } else {
        return pointsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public com.topstack.kilonotes.proto.StylusPointProto getPoints(int index) {
      if (pointsBuilder_ == null) {
        return points_.get(index);
      } else {
        return pointsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder setPoints(
        int index, com.topstack.kilonotes.proto.StylusPointProto value) {
      if (pointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePointsIsMutable();
        points_.set(index, value);
        onChanged();
      } else {
        pointsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder setPoints(
        int index, com.topstack.kilonotes.proto.StylusPointProto.Builder builderForValue) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        points_.set(index, builderForValue.build());
        onChanged();
      } else {
        pointsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder addPoints(com.topstack.kilonotes.proto.StylusPointProto value) {
      if (pointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePointsIsMutable();
        points_.add(value);
        onChanged();
      } else {
        pointsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder addPoints(
        int index, com.topstack.kilonotes.proto.StylusPointProto value) {
      if (pointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePointsIsMutable();
        points_.add(index, value);
        onChanged();
      } else {
        pointsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder addPoints(
        com.topstack.kilonotes.proto.StylusPointProto.Builder builderForValue) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        points_.add(builderForValue.build());
        onChanged();
      } else {
        pointsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder addPoints(
        int index, com.topstack.kilonotes.proto.StylusPointProto.Builder builderForValue) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        points_.add(index, builderForValue.build());
        onChanged();
      } else {
        pointsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder addAllPoints(
        java.lang.Iterable<? extends com.topstack.kilonotes.proto.StylusPointProto> values) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, points_);
        onChanged();
      } else {
        pointsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder clearPoints() {
      if (pointsBuilder_ == null) {
        points_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        pointsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public Builder removePoints(int index) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        points_.remove(index);
        onChanged();
      } else {
        pointsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public com.topstack.kilonotes.proto.StylusPointProto.Builder getPointsBuilder(
        int index) {
      return getPointsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public com.topstack.kilonotes.proto.StylusPointProtoOrBuilder getPointsOrBuilder(
        int index) {
      if (pointsBuilder_ == null) {
        return points_.get(index);  } else {
        return pointsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public java.util.List<? extends com.topstack.kilonotes.proto.StylusPointProtoOrBuilder> 
         getPointsOrBuilderList() {
      if (pointsBuilder_ != null) {
        return pointsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(points_);
      }
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public com.topstack.kilonotes.proto.StylusPointProto.Builder addPointsBuilder() {
      return getPointsFieldBuilder().addBuilder(
          com.topstack.kilonotes.proto.StylusPointProto.getDefaultInstance());
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public com.topstack.kilonotes.proto.StylusPointProto.Builder addPointsBuilder(
        int index) {
      return getPointsFieldBuilder().addBuilder(
          index, com.topstack.kilonotes.proto.StylusPointProto.getDefaultInstance());
    }
    /**
     * <code>repeated .com.topstack.kilonotes.StylusPointProto points = 1;</code>
     */
    public java.util.List<com.topstack.kilonotes.proto.StylusPointProto.Builder> 
         getPointsBuilderList() {
      return getPointsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        com.topstack.kilonotes.proto.StylusPointProto, com.topstack.kilonotes.proto.StylusPointProto.Builder, com.topstack.kilonotes.proto.StylusPointProtoOrBuilder> 
        getPointsFieldBuilder() {
      if (pointsBuilder_ == null) {
        pointsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            com.topstack.kilonotes.proto.StylusPointProto, com.topstack.kilonotes.proto.StylusPointProto.Builder, com.topstack.kilonotes.proto.StylusPointProtoOrBuilder>(
                points_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        points_ = null;
      }
      return pointsBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:com.topstack.kilonotes.StylusPointListProto)
  }

  // @@protoc_insertion_point(class_scope:com.topstack.kilonotes.StylusPointListProto)
  private static final com.topstack.kilonotes.proto.StylusPointListProto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.topstack.kilonotes.proto.StylusPointListProto();
  }

  public static com.topstack.kilonotes.proto.StylusPointListProto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<StylusPointListProto>
      PARSER = new com.google.protobuf.AbstractParser<StylusPointListProto>() {
    @java.lang.Override
    public StylusPointListProto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<StylusPointListProto> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<StylusPointListProto> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.topstack.kilonotes.proto.StylusPointListProto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}


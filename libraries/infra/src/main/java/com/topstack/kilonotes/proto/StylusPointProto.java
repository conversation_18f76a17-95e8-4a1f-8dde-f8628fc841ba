// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: stylus_point.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

/**
 * Protobuf type {@code com.topstack.kilonotes.StylusPointProto}
 */
public final class StylusPointProto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:com.topstack.kilonotes.StylusPointProto)
    StylusPointProtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 3,
      /* suffix= */ "",
      StylusPointProto.class.getName());
  }
  // Use StylusPointProto.newBuilder() to construct.
  private StylusPointProto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private StylusPointProto() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return StylusPoint.internal_static_com_topstack_kilonotes_StylusPointProto_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return StylusPoint.internal_static_com_topstack_kilonotes_StylusPointProto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            StylusPointProto.class, Builder.class);
  }

  public static final int X_FIELD_NUMBER = 1;
  private float x_ = 0F;
  /**
   * <code>float x = 1;</code>
   * @return The x.
   */
  @Override
  public float getX() {
    return x_;
  }

  public static final int Y_FIELD_NUMBER = 2;
  private float y_ = 0F;
  /**
   * <code>float y = 2;</code>
   * @return The y.
   */
  @Override
  public float getY() {
    return y_;
  }

  public static final int PRESSURE_FIELD_NUMBER = 3;
  private float pressure_ = 0F;
  /**
   * <code>float pressure = 3;</code>
   * @return The pressure.
   */
  @Override
  public float getPressure() {
    return pressure_;
  }

  public static final int DISCONTINUITY_FIELD_NUMBER = 4;
  private boolean discontinuity_ = false;
  /**
   * <code>bool discontinuity = 4;</code>
   * @return The discontinuity.
   */
  @Override
  public boolean getDiscontinuity() {
    return discontinuity_;
  }

  public static final int EVENT_TIME_FIELD_NUMBER = 5;
  private long eventTime_ = 0L;
  /**
   * <code>int64 event_time = 5;</code>
   * @return The eventTime.
   */
  @Override
  public long getEventTime() {
    return eventTime_;
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (Float.floatToRawIntBits(x_) != 0) {
      output.writeFloat(1, x_);
    }
    if (Float.floatToRawIntBits(y_) != 0) {
      output.writeFloat(2, y_);
    }
    if (Float.floatToRawIntBits(pressure_) != 0) {
      output.writeFloat(3, pressure_);
    }
    if (discontinuity_ != false) {
      output.writeBool(4, discontinuity_);
    }
    if (eventTime_ != 0L) {
      output.writeInt64(5, eventTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (Float.floatToRawIntBits(x_) != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(1, x_);
    }
    if (Float.floatToRawIntBits(y_) != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(2, y_);
    }
    if (Float.floatToRawIntBits(pressure_) != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(3, pressure_);
    }
    if (discontinuity_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, discontinuity_);
    }
    if (eventTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, eventTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof StylusPointProto)) {
      return super.equals(obj);
    }
    StylusPointProto other = (StylusPointProto) obj;

    if (Float.floatToIntBits(getX())
        != Float.floatToIntBits(
            other.getX())) return false;
    if (Float.floatToIntBits(getY())
        != Float.floatToIntBits(
            other.getY())) return false;
    if (Float.floatToIntBits(getPressure())
        != Float.floatToIntBits(
            other.getPressure())) return false;
    if (getDiscontinuity()
        != other.getDiscontinuity()) return false;
    if (getEventTime()
        != other.getEventTime()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + X_FIELD_NUMBER;
    hash = (53 * hash) + Float.floatToIntBits(
        getX());
    hash = (37 * hash) + Y_FIELD_NUMBER;
    hash = (53 * hash) + Float.floatToIntBits(
        getY());
    hash = (37 * hash) + PRESSURE_FIELD_NUMBER;
    hash = (53 * hash) + Float.floatToIntBits(
        getPressure());
    hash = (37 * hash) + DISCONTINUITY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getDiscontinuity());
    hash = (37 * hash) + EVENT_TIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getEventTime());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static StylusPointProto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static StylusPointProto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static StylusPointProto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static StylusPointProto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static StylusPointProto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static StylusPointProto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static StylusPointProto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static StylusPointProto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static StylusPointProto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static StylusPointProto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static StylusPointProto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static StylusPointProto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(StylusPointProto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.topstack.kilonotes.StylusPointProto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.topstack.kilonotes.StylusPointProto)
      StylusPointProtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return StylusPoint.internal_static_com_topstack_kilonotes_StylusPointProto_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return StylusPoint.internal_static_com_topstack_kilonotes_StylusPointProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              StylusPointProto.class, Builder.class);
    }

    // Construct using com.topstack.kilonotes.proto.StylusPointProto.newBuilder()
    private Builder() {

    }

    private Builder(
        BuilderParent parent) {
      super(parent);

    }
    @Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      x_ = 0F;
      y_ = 0F;
      pressure_ = 0F;
      discontinuity_ = false;
      eventTime_ = 0L;
      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return StylusPoint.internal_static_com_topstack_kilonotes_StylusPointProto_descriptor;
    }

    @Override
    public StylusPointProto getDefaultInstanceForType() {
      return StylusPointProto.getDefaultInstance();
    }

    @Override
    public StylusPointProto build() {
      StylusPointProto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public StylusPointProto buildPartial() {
      StylusPointProto result = new StylusPointProto(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(StylusPointProto result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.x_ = x_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.y_ = y_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.pressure_ = pressure_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.discontinuity_ = discontinuity_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.eventTime_ = eventTime_;
      }
    }

    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof StylusPointProto) {
        return mergeFrom((StylusPointProto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(StylusPointProto other) {
      if (other == StylusPointProto.getDefaultInstance()) return this;
      if (other.getX() != 0F) {
        setX(other.getX());
      }
      if (other.getY() != 0F) {
        setY(other.getY());
      }
      if (other.getPressure() != 0F) {
        setPressure(other.getPressure());
      }
      if (other.getDiscontinuity() != false) {
        setDiscontinuity(other.getDiscontinuity());
      }
      if (other.getEventTime() != 0L) {
        setEventTime(other.getEventTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 13: {
              x_ = input.readFloat();
              bitField0_ |= 0x00000001;
              break;
            } // case 13
            case 21: {
              y_ = input.readFloat();
              bitField0_ |= 0x00000002;
              break;
            } // case 21
            case 29: {
              pressure_ = input.readFloat();
              bitField0_ |= 0x00000004;
              break;
            } // case 29
            case 32: {
              discontinuity_ = input.readBool();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              eventTime_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private float x_ ;
    /**
     * <code>float x = 1;</code>
     * @return The x.
     */
    @Override
    public float getX() {
      return x_;
    }
    /**
     * <code>float x = 1;</code>
     * @param value The x to set.
     * @return This builder for chaining.
     */
    public Builder setX(float value) {

      x_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>float x = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearX() {
      bitField0_ = (bitField0_ & ~0x00000001);
      x_ = 0F;
      onChanged();
      return this;
    }

    private float y_ ;
    /**
     * <code>float y = 2;</code>
     * @return The y.
     */
    @Override
    public float getY() {
      return y_;
    }
    /**
     * <code>float y = 2;</code>
     * @param value The y to set.
     * @return This builder for chaining.
     */
    public Builder setY(float value) {

      y_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>float y = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearY() {
      bitField0_ = (bitField0_ & ~0x00000002);
      y_ = 0F;
      onChanged();
      return this;
    }

    private float pressure_ ;
    /**
     * <code>float pressure = 3;</code>
     * @return The pressure.
     */
    @Override
    public float getPressure() {
      return pressure_;
    }
    /**
     * <code>float pressure = 3;</code>
     * @param value The pressure to set.
     * @return This builder for chaining.
     */
    public Builder setPressure(float value) {

      pressure_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>float pressure = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearPressure() {
      bitField0_ = (bitField0_ & ~0x00000004);
      pressure_ = 0F;
      onChanged();
      return this;
    }

    private boolean discontinuity_ ;
    /**
     * <code>bool discontinuity = 4;</code>
     * @return The discontinuity.
     */
    @Override
    public boolean getDiscontinuity() {
      return discontinuity_;
    }
    /**
     * <code>bool discontinuity = 4;</code>
     * @param value The discontinuity to set.
     * @return This builder for chaining.
     */
    public Builder setDiscontinuity(boolean value) {

      discontinuity_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>bool discontinuity = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscontinuity() {
      bitField0_ = (bitField0_ & ~0x00000008);
      discontinuity_ = false;
      onChanged();
      return this;
    }

    private long eventTime_ ;
    /**
     * <code>int64 event_time = 5;</code>
     * @return The eventTime.
     */
    @Override
    public long getEventTime() {
      return eventTime_;
    }
    /**
     * <code>int64 event_time = 5;</code>
     * @param value The eventTime to set.
     * @return This builder for chaining.
     */
    public Builder setEventTime(long value) {

      eventTime_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>int64 event_time = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearEventTime() {
      bitField0_ = (bitField0_ & ~0x00000010);
      eventTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:com.topstack.kilonotes.StylusPointProto)
  }

  // @@protoc_insertion_point(class_scope:com.topstack.kilonotes.StylusPointProto)
  private static final StylusPointProto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new StylusPointProto();
  }

  public static StylusPointProto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<StylusPointProto>
      PARSER = new com.google.protobuf.AbstractParser<StylusPointProto>() {
    @Override
    public StylusPointProto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<StylusPointProto> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<StylusPointProto> getParserForType() {
    return PARSER;
  }

  @Override
  public StylusPointProto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: uuid.proto
// Protobuf Java Version: 4.29.3

package com.topstack.kilonotes.proto;

public interface UUIDListProtoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.topstack.kilonotes.UUIDListProto)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated string uuids = 1;</code>
   * @return A list containing the uuids.
   */
  java.util.List<java.lang.String>
      getUuidsList();
  /**
   * <code>repeated string uuids = 1;</code>
   * @return The count of uuids.
   */
  int getUuidsCount();
  /**
   * <code>repeated string uuids = 1;</code>
   * @param index The index of the element to return.
   * @return The uuids at the given index.
   */
  java.lang.String getUuids(int index);
  /**
   * <code>repeated string uuids = 1;</code>
   * @param index The index of the value to return.
   * @return The bytes of the uuids at the given index.
   */
  com.google.protobuf.ByteString
      getUuidsBytes(int index);
}

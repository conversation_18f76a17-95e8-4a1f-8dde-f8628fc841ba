package com.topstack.kilonotes.pdf

import java.lang.UnsupportedOperationException

class PdfText(
    private val document: PdfDocument,
    private val fontSize: Float
) : PdfPageObject() {

    override fun create(): PdfText {
        throw UnsupportedOperationException("Please use another create function!!")
    }

    /**
     * 使用字体创建
     */
    fun create(font: PdfFont): PdfText {
        if (document.exists() && font.exists()) {
            pageObjectInstance = PdfiumCore.nativeCreateTextObject(
                document.getNativeDocument()!!,
                font.getNativeFont()!!,
                fontSize
            )
        }
        return this
    }

    /**
     * 使用PDF默认支持的字体创建
     * The 14 standard PDF fonts are Courier (Regular, Oblique, Bold, Bold Oblique),
     * Helvetica (Regular, Oblique, Bold, Bold Oblique),
     * Times (Roman, Italic, Bold, Bold Italic),
     * Symbol, and ITC Zapf Dingbats.
     */
    fun create(fontName: String): PdfText {
        if (document.exists()) {
            pageObjectInstance =
                PdfiumCore.nativeNewTextObject(document.getNativeDocument()!!, fontName, fontSize)
        }
        return this
    }

    fun setText(content: String) {
        if (exists()) {
            PdfiumCore.nativeSetText(pageObjectInstance, content)
        }
    }

    fun setTextRenderMode(mode: TextRenderMode) {
        if (exists()) {
            PdfiumCore.nativeSetTextRenderMode(pageObjectInstance, mode)
        }
    }


}
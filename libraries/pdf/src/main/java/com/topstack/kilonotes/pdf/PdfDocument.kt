package com.topstack.kilonotes.pdf

import android.graphics.Bitmap
import java.io.File

class PdfDocument : AutoCloseable {
    private val TAG = "PdfDocument"
    private var nativeDocumentInstance: Long = 0
    private var documentPath: String = ""

    @Throws(PdfError::class)
    constructor(path: String, password: String? = null) {
        val file = File(path)
        documentPath = path
        if (file.exists()) {
            nativeDocumentInstance = PdfiumCore.nativeLoadDocument(path, password)
            if (exists()) {
                return
            }
            val lastErrorCode = PdfiumCore.nativeGetLastError()
            throw PdfError(PdfErrorCode.getByValue(lastErrorCode))
        }
    }

    constructor() {
        createNewDocument()
    }

    fun getNativeDocument(): Long? {
        return if (!exists()) {
            null
        } else {
            nativeDocumentInstance
        }
    }

    fun createNewDocument(): PdfDocument {
        nativeDocumentInstance = PdfiumCore.nativeCreateNewDocument()
        if (!exists()) {
            throw RuntimeException("Native Create Document Fail!")
        }
        return this
    }

    fun exists(): Boolean {
        return nativeDocumentInstance != 0L
    }

    fun createNewPage(index: Int, width: Double, height: Double): PdfPage {
        if (!exists()) {
            throw RuntimeException("Native Create Document Fail!")
        }
        val page = PdfiumCore.nativeNewPage(nativeDocumentInstance, index, width, height)
        return PdfPage(page)
    }

    fun loadPage(index: Int): PdfPage? {
        if (exists()) {
            val page = PdfiumCore.nativeLoadPage(nativeDocumentInstance, index)
            return PdfPage(page)
        }
        return null
    }

    fun deletePage(pageIndex: Int) {
        if (exists()) {
            PdfiumCore.nativeDeletePage(nativeDocumentInstance, pageIndex)
        }
    }

    fun copyTargetDocumentPages(
        srcDoc: PdfDocument,
        pagesIndex: List<Int>,
        insertIndex: Int
    ): Boolean {
        if (!exists() || !srcDoc.exists()) {
            return false
        }
        //拷贝需要的页面
        return PdfiumCore.nativeImportPagesByIndex(
            nativeDocumentInstance,
            srcDoc.getNativeDocument()!!,
            pagesIndex.toIntArray(),
            pagesIndex.size.toLong(),
            insertIndex
        )
    }

    fun save(path: String) {
        if (exists()) {
            PdfiumCore.nativeSaveAsCopy(nativeDocumentInstance, path, 0)
        }
    }

    @JvmOverloads
    fun renderPageBitmap(
        targetBitmap: Bitmap,
        pageIndex: Int,
        startX: Int,
        startY: Int,
        sizeX: Int,
        sizeY: Int,
        pdfRotate: PdfRotate = PdfRotate.NORMAL
    ) {
        val page = loadPage(pageIndex) ?: return
        page.use {
            it.renderBitmap(targetBitmap, startX, startY, sizeX, sizeY, pdfRotate)
        }
    }

    companion object {
        fun isPassWordProtected(path: String): Boolean {
            return try {
                PdfDocument(path, null).close()
                false
            } catch (e: PdfError) {
                e.errorCode == PdfErrorCode.PASSWORD
            }
        }
    }


    override fun close() {
        if (exists()) {
            PdfiumCore.nativeCloseDocument(nativeDocumentInstance)
        }
        nativeDocumentInstance = 0
    }
}
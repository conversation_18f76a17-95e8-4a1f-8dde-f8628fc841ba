package com.topstack.kilonotes.pdf

class PdfFont(private val document: PdfDocument) : AutoCloseable {
    private var fontInstance = 0L

    fun create(fontPath: String, fontType: FontType, isCidFont: Boolean):PdfFont {
        if (document.exists()) {
            fontInstance = PdfiumCore.nativeLoadFont(
                document.getNativeDocument()!!,
                fontPath,
                fontType,
                isCidFont
            )
        }
        return this
    }

    fun getNativeFont(): Long? {
        return if (fontInstance == 0L) {
            null
        } else {
            fontInstance
        }
    }

    fun exists(): Boolean {
        return fontInstance != 0L
    }

    override fun close() {
        if (exists()) {
            PdfiumCore.nativeCloseFont(fontInstance)
        }
    }
}
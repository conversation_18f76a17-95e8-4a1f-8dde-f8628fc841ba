package com.topstack.kilonotes.pdf

import android.graphics.Bitmap

class PdfPage(
    private val nativePageInstance: Long
) : AutoCloseable {
    val width: Float by lazy {
        PdfiumCore.nativeGetPageWidth(nativePageInstance)
    }

    val height: Float by lazy {
        PdfiumCore.nativeGetPageHeight(nativePageInstance)
    }

    fun getNativePage(): Long {
        return nativePageInstance
    }

    fun insertPageObject(pageObject: PdfPageObject) {
        pageObject.getNativePageObject()?.let {
            PdfiumCore.nativeInsertObject(nativePageInstance, it)
        }
    }

    fun removePageObject(pageObject: PdfPageObject) {
        pageObject.getNativePageObject()?.let {
            PdfiumCore.nativeRemoveObject(nativePageInstance, it)
        }
    }

    fun generatePageContent() {
        PdfiumCore.nativeGeneratePageContent(nativePageInstance)
    }

    fun renderBitmap(
        targetBitmap: Bitmap,
        startX: Int,
        startY: Int,
        sizeX: Int,
        sizeY: Int,
        pdfRotate: PdfRotate
    ) {
        PdfiumCore.nativeRenderPageBitmap(
            nativePageInstance,
            targetBitmap,
            startX,
            startY,
            sizeX,
            sizeY,
            pdfRotate
        )
    }

    override fun close() {
        PdfiumCore.nativeClosePage(nativePageInstance)
    }
}
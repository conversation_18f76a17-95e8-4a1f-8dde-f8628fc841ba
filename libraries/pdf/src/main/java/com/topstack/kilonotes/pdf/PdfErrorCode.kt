package com.topstack.kilonotes.pdf

enum class PdfErrorCode(val code: Int) {
    FILE(0), FORMAT(1), PAGE(2), PASSWORD(3), SECURITY(4), SUCCESS(5), UNKNOWN(6), XFALAYOUT(7), XFALOAD(
        8
    );

    companion object {
        fun getByValue(code: Int): PdfErrorCode {
            for (errorCode in values()) {
                if (errorCode.code == code) {
                    return errorCode
                }
            }
            return UNKNOWN
        }
    }

}
package com.topstack.kilonotes.pdf

import android.graphics.Color
import android.graphics.Matrix
import android.graphics.RectF

abstract class PdfPageObject : AutoCloseable {
    protected var pageObjectInstance: Long = 0

    abstract fun create(): PdfPageObject

    fun exists(): Bo<PERSON>an {
        return pageObjectInstance != 0L
    }

    fun getNativePageObject(): Long? {
        return if (!exists()) {
            null
        } else {
            pageObjectInstance
        }
    }

    open fun setBlendMode(blendMode: BlendMode) {
        if (!exists()) return
        PdfiumCore.nativeSetBlendMode(pageObjectInstance, blendMode)
    }

    open fun setStrokeColor(color: Int, alpha: Int): Boolean {
        if (!exists()) return false
        val red = Color.red(color)
        val green = Color.green(color)
        val blue = Color.blue(color)
        return PdfiumCore.nativeSetStrokeColor(pageObjectInstance, red, green, blue, alpha)
    }

    open fun setStorkWidth(width: Float): <PERSON>olean {
        if (!exists()) return false
        return PdfiumCore.nativeSetStorkWidth(pageObjectInstance, width)
    }

    open fun setFillColor(color: Int, alpha: Int): Boolean {
        if (!exists()) return false
        val red = Color.red(color)
        val green = Color.green(color)
        val blue = Color.blue(color)
        return PdfiumCore.nativeSetFillColor(pageObjectInstance, red, green, blue, alpha)
    }

    open fun setMatrix(matrix: Matrix): Boolean {
        if (!exists()) return false
        return PdfiumCore.nativeSetMatrix(pageObjectInstance, matrix)
    }

    open fun setMatrix(matrix: PdfMatrix): Boolean {
        if (!exists()) return false
        return PdfiumCore.nativeSetPdfMatrix(pageObjectInstance, matrix)
    }

    fun getBound(): RectF? {
        if (!exists()) return null
        return PdfiumCore.nativeGetPageObjectBound(pageObjectInstance)
    }

    override fun close() {
        if (exists()) {
            PdfiumCore.nativeDestroyPageObj(pageObjectInstance)
        }
    }
}
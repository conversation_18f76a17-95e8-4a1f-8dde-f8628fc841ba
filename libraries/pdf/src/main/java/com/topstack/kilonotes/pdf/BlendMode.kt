package com.topstack.kilonotes.pdf

enum class BlendMode(val mode: String) {
    // Blend mode can be one of following: Color, ColorBurn, ColorDodge, Darken,
    // Difference, Exclusion, HardLight, Hue, Lighten, Luminosity, Multiply, Normal,
    // Overlay, Saturation, Screen, SoftLight
    COLOR("Color"),
    COLOR_BURN("ColorBurn"),
    COLOR_DODGE("ColorDodge"),
    DARKEN("Darken"),
    DIFFERENCE("Difference"),
    EXCLUSION("Exclusion"),
    HARD_LIGHT("HardLight"),
    HUE("Hue"),
    <PERSON><PERSON>HTEN("Lighten"),
    LUMINOSITY("Luminosity"),
    MULTIPLY("Multiply"),
    NORMAL("Normal"),
    OVERLAY("Overlay"),
    SATURATION("Saturation"),
    SCREEN("Screen"),
    SOFT_LIGHT("SoftLight")
}
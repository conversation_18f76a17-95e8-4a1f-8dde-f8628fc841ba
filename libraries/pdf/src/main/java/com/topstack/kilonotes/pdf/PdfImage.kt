package com.topstack.kilonotes.pdf

import android.graphics.Bitmap

class PdfImage(private val document: PdfDocument) : PdfPageObject() {
    override fun create(): PdfImage {
        if (document.exists()) {
            pageObjectInstance = PdfiumCore.nativeNewImageObject(document.getNativeDocument()!!)
        }
        return this
    }

    fun loadJEPG(page: PdfPage, imagePath: String) {
        if (exists()) {
            PdfiumCore.nativeLoadJEPJFileInline(
                page.getNativePage(),
                0,
                pageObjectInstance,
                imagePath
            )
        }
    }

    fun loadBitmap(page: PdfPage, bitmap: Bitmap) {
        if (exists()) {
            PdfiumCore.nativeSetBitmap2ImageObject(
                page.getNativePage(),
                0,
                pageObjectInstance,
                bitmap
            )
        }
    }

}
package com.topstack.kilonotes.pdf

import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.RectF
import java.util.concurrent.atomic.AtomicInteger

class PdfiumCore {

    companion object {
        init {
            System.loadLibrary("pdfium")
            System.loadLibrary("pdf")
        }

        private val referenceCount: AtomicInteger = AtomicInteger(0)

        @Synchronized
        @Throws(IllegalStateException::class)
        fun autoInitLibrary() {
            val count = referenceCount.get()
            when {
                count < 0 -> {
                    throw IllegalStateException("autoInitLibrary referenceCount < 0")
                }
                count == 0 -> {
                    initLibrary()
                    referenceCount.getAndIncrement()
                }
                else -> {
                    referenceCount.getAndIncrement()
                    //do nothing
                }
            }
        }

        @Synchronized
        @Throws(IllegalStateException::class)
        fun autoDestroyLibrary() {
            val count = referenceCount.get()
            when {
                count <= 0 -> {
                    throw IllegalStateException("autoInitLibrary referenceCount < 0")
                }
                count == 1 -> {
                    destroyLibrary()
                    referenceCount.getAndDecrement()
                }
                else -> {
                    referenceCount.getAndDecrement()
                    //do nothing
                }
            }
        }

        external fun initLibrary()
        external fun destroyLibrary()

        external fun nativeLoadDocument(path: String, password: String?): Long
        external fun nativeCloseDocument(documentPoint: Long)

        //        external fun nativeLoadPage()
//        external fun nativeClosePage()
        external fun nativeGetLastError(): Int

        //*******edit start***********
        external fun nativeCreateNewDocument(): Long
        external fun nativeNewPage(
            documentPoint: Long,
            index: Int,
            width: Double,
            height: Double
        ): Long

        external fun nativeLoadPage(documentPoint: Long, index: Int): Long

        external fun nativeClosePage(pagePoint: Long)

        external fun nativeDeletePage(documentPoint: Long, index: Int)

        external fun nativeGetPageWidth(pagePoint: Long): Float

        external fun nativeGetPageHeight(pagePoint: Long): Float

        //        external fun nativeInsertObject()
//        external fun nativeDeleteObject()
        external fun nativeSaveAsCopy(documentPoint: Long, path: String, flag: Long): Boolean

        //        external fun nativeSaveWithVersion()
        external fun nativeGeneratePageContent(pagePoint: Long): Boolean


        external fun nativeCreatePathObject(initX: Float, initY: Float): Long
        external fun nativePathLineTo(pathPoint: Long, pointX: Float, pointY: Float): Boolean

        // x1     - the horizontal position of the first Bezier control point.
        // y1     - the vertical position of the first Bezier control point.
        // x2     - the horizontal position of the second Bezier control point.
        // y2     - the vertical position of the second Bezier control point.
        // x3     - the horizontal position of the ending point of the Bezier curve.
        // y3     - the vertical position of the ending point of the Bezier curve.
        external fun nativePathBezierTo(
            pathPoint: Long,
            pointX1: Float,
            pointY1: Float,
            pointX2: Float,
            pointY2: Float,
            pointX3: Float,
            pointY3: Float
        ): Boolean

        external fun nativePathClose(pathPoint: Long): Boolean
        external fun nativePathMoveTo(pathPoint: Long, pointX: Float, pointY: Float): Boolean
        external fun nativeSetPathDrawMode(
            pathPoint: Long,
            fillMode: FillMode,
            isStroke: Boolean
        ): Boolean


        external fun nativeNewTextObject(
            documentPoint: Long,
            font: String,
            fontSize: Float
        ): Long

        external fun nativeCreateTextObject(
            documentPoint: Long,
            fontPoint: Long,
            fontSize: Float
        ): Long

        external fun nativeSetText(textPoint: Long, content: String): Boolean
        external fun nativeSetTextRenderMode(
            textPoint: Long,
            textRenderMode: TextRenderMode
        ): Boolean

        external fun nativeAddMark(pageObjectPoint: Long, tag: String): Long
        external fun nativeInsertObject(pagePoint: Long, pageObjectPoint: Long)
        external fun nativeRemoveObject(pagePoint: Long, pageObjectPoint: Long): Boolean

        // Blend mode can be one of following: Color, ColorBurn, ColorDodge, Darken,
        // Difference, Exclusion, HardLight, Hue, Lighten, Luminosity, Multiply, Normal,
        // Overlay, Saturation, Screen, SoftLight
        external fun nativeSetBlendMode(pageObjectPoint: Long, blendMode: BlendMode)
        external fun nativeSetStrokeColor(
            pageObjectPoint: Long,
            r: Int,
            g: Int,
            b: Int,
            a: Int
        ): Boolean

        external fun nativeSetStorkWidth(pageObjectPoint: Long, width: Float): Boolean
        external fun nativeSetFillColor(
            pageObjectPoint: Long,
            r: Int,
            g: Int,
            b: Int,
            a: Int
        ): Boolean

        external fun nativeSetMatrix(pageObjectPoint: Long, matrix: Matrix): Boolean

        external fun nativeSetPdfMatrix(pageObjectPoint: Long, matrix: PdfMatrix): Boolean

        external fun nativeDestroyPageObj(pageObjectPoint: Long)
        //*******edit end***********

        external fun nativeCopyPdfPreferences(
            destDocPoint: Long,
            srcDocPoint: Long
        ): Boolean

        external fun nativeImportPagesByIndex(
            destDocPoint: Long,
            srcDocPoint: Long,
            pageIndices: IntArray,
            length: Long,
            insertIndex: Int
        ): Boolean

        external fun nativeNewImageObject(documentPoint: Long): Long

        //This function loads the JPEG image inline, so the image
        // content is copied to the file. This allows |file_access| and its associated
        // data to be deleted after this function returns.
        external fun nativeLoadJEPJFileInline(
            pagePoint: Long,
            count: Int,
            imageObjectPoint: Long,
            imagePath: String
        ): Boolean

        external fun nativeSetBitmap2ImageObject(
            pagePoint: Long,
            count: Int,
            imageObjectPoint: Long,
            bitmap: Bitmap
        )

        external fun nativeCreateAnnot(pagePoint: Long, type: AnnotationSubType): Long
        external fun nativeCloseAnnot(annotPoint: Long)

        //只有元素在注解的区域内才会显示
        external fun nativeSetAnnotRect(
            annotPoint: Long,
            left: Float,
            top: Float,
            right: Float,
            bottom: Float
        ): Boolean

        external fun nativeAnnotAppendObject(annotPoint: Long, pageObjectPoint: Long): Boolean

        external fun nativeSetAnnotColor(
            annotPoint: Long,
            type: AnnotationColorType,
            r: Int,
            g: Int,
            b: Int,
            a: Int
        ): Boolean

        external fun nativeGetPageObjectBound(pageObjectPoint: Long): RectF

        external fun nativeAppendAnnotAttachmentRect(
            annotPoint: Long,
            x1: Float,
            y1: Float,
            x2: Float,
            y2: Float,
            x3: Float,
            y3: Float,
            x4: Float,
            y4: Float
        ): Boolean

        external fun nativeUpdateObject(annotPoint: Long, pageObjectPoint: Long): Boolean

        external fun nativeSetAnnotBorder(
            annotPoint: Long,
            horizontalRadius: Float,
            verticalRadius: Float,
            borderWidth: Float
        ): Boolean

        external fun nativeLoadFont(
            documentPoint: Long,
            fontPath: String,
            fontType: FontType,
            isCidFont: Boolean
        ): Long

        external fun nativeCloseFont(fontPoint: Long)

        external fun nativeSetLineJoin(pageObjectPoint: Long, lineJoinType: LineJoinType): Boolean

        external fun nativeSetLineCap(pageObjectPoint: Long, lineCapType: LineCapType): Boolean

        external fun nativeRenderPageBitmap(
            pagePoint: Long,
            targetBitmap: Bitmap,
            startX: Int,
            startY: Int,
            sizeX: Int,
            sizeY: Int,
            pdfRotate: PdfRotate
        )

        external fun nativeRenderPageBitmapWithTransform(
            pagePoint: Long,
            targetBitmap: Bitmap,
            scaleX: Float,
            scaleY: Float,
            translateX: Float,
            translateY: Float
        )
    }
}
package com.topstack.kilonotes.pdf

class PdfPath(
    private val initX: Float,
    private val initY: Float
) : PdfPageObject() {

    override fun create(): PdfPath {
        pageObjectInstance = PdfiumCore.nativeCreatePathObject(initX, initY)
        return this
    }

    fun lineTo(x: Float, y: Float) {
        if (exists()) {
            PdfiumCore.nativePathLineTo(pageObjectInstance, x, y)
        }
    }

    fun quadTo(x1: Float, y1: Float, x2: Float, y2: Float) {
        if (exists()) {
            cubicTo(x1, y1, x1, y1, x2, y2)
        }
    }

    fun cubicTo(x1: Float, y1: Float, x2: Float, y2: Float, x3: Float, y3: Float) {
        if (exists()) {
            PdfiumCore.nativePathBezierTo(pageObjectInstance, x1, y1, x2, y2, x3, y3)
        }
    }

    fun moveTo(x: Float, y: Float) {
        if (exists()) {
            PdfiumCore.nativePathMoveTo(pageObjectInstance, x, y)
        }
    }

    fun setPathDrawMode(fillMode: FillMode) {
        if (exists()) {
            PdfiumCore.nativeSetPathDrawMode(pageObjectInstance, fillMode, true)
        }
    }

    fun closePath() {
        if (exists()) {
            PdfiumCore.nativePathClose(pageObjectInstance)
        }
    }

    fun setLineCap(lineCapType: LineCapType) {
        if (exists()) {
            PdfiumCore.nativeSetLineCap(pageObjectInstance, lineCapType)
        }
    }

    fun setLineJoin(lineJoinType: LineJoinType) {
        if (exists()) {
            PdfiumCore.nativeSetLineJoin(pageObjectInstance, lineJoinType)
        }
    }
}
package com.topstack.pdfrun.doc

import android.util.Size
import com.topstack.pdfrun.AutoCloseablePdfContextOwner
import com.topstack.pdfrun.PdfContext
import com.topstack.pdfrun.util.lockAndRun
import java.io.File
import java.lang.ref.WeakReference
import java.util.concurrent.locks.ReentrantLock

/**
 *
 */
class PdfDocument private constructor(
    override var pdfContext: PdfContext?,
    private val nativePtr: Long,
) : AutoCloseablePdfContextOwner() {

    companion object {
        private val loadLock = ReentrantLock()
        private val pageLock = ReentrantLock()
        private val documentCache = hashMapOf<String, WeakReference<PdfDocument>>()

        fun loadFrom(
            context: PdfContext,
            filePath: String,
            password: String? = null,
        ): PdfDocument? = context.runOrThrow { pdfRunner ->
            val pdfFile = File(filePath)
            if (!pdfFile.exists()) {
                return@runOrThrow null
            }

            val cacheKey = "${filePath}_${pdfFile.lastModified()}"
            documentCache[cacheKey]?.get()?.takeIf { !it.closed }?.let { cachedDocument ->
                cachedDocument.increaseRefCount()
                return@runOrThrow cachedDocument
            }

            return@runOrThrow lockAndRun(loadLock) {
                documentCache[cacheKey]?.get()?.takeIf { !it.closed }?.let { cachedDocument ->
                    cachedDocument.increaseRefCount()
                    return@lockAndRun cachedDocument
                }

                val ptr = pdfRunner.nativeLoadDocument(filePath, password)
                return@lockAndRun if (ptr == 0L) {
                    null
                } else {
                    PdfDocument(context, ptr).also {
                        documentCache[cacheKey] = WeakReference(it)
                    }
                }
            }
        }
    }

    override fun onClose(pdfContext: PdfContext) {
        pdfContext.runOrThrow { pdfRunner ->
            pdfRunner.nativeCloseDocument(nativePtr)
            pageCache.values.forEach { pdfPage ->
                pdfPage.close()
            }
        }
    }

    fun getPageCount(): Int = pdfContext?.runOrThrow { pdfRunner ->
        pdfRunner.nativeGetPageCount(nativePtr)
    } ?: 0

    /**
     * 获取指定页面大小（磅），不需要加载该页面。
     */
    fun getPageSize(pageIndex: Int): Size? {
        return if (pageIndex >= getPageCount()) {
            null
        } else {
            pdfContext?.runOrThrow { pdfRunner ->
                val size = pdfRunner.nativeGetPageSizePointByIndex(nativePtr, pageIndex)
                if (size.width == 0 && size.height == 0) null else size
            }
        }
    }

    private val pageCache = hashMapOf<Int, PdfPage>()

    fun openPage(index: Int): PdfPage? =
        pdfContext?.runOrThrow { pdfRunner ->
            pageCache[index]?.let { cachedPage ->
                if (!cachedPage.closed) {
                    cachedPage.increaseRefCount()
                    return@runOrThrow cachedPage
                }
            }

            if (index in 0 until getPageCount()) {
                return@runOrThrow lockAndRun(pageLock) {
                    pageCache[index]?.let { cachedPage ->
                        if (!cachedPage.closed) {
                            cachedPage.increaseRefCount()
                            return@lockAndRun cachedPage
                        }
                    }

                    val pagePtr = pdfRunner.nativeLoadPage(nativePtr, index)
                    if (pagePtr == 0L) {
                        return@lockAndRun null
                    } else {
                        val page = PdfPage(pdfContext, pagePtr)
                        page.width = pdfRunner.nativeGetPageWidthPoint(pagePtr)
                        page.height = pdfRunner.nativeGetPageHeightPoint(pagePtr)
                        pageCache[index] = page

                        return@lockAndRun page
                    }
                }
            } else {
                null
            }
        }
}
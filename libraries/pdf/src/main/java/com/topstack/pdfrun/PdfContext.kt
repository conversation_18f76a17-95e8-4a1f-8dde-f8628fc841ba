package com.topstack.pdfrun

/**
 *
 */
class PdfContext internal constructor(private val scope: PdfScope) {
    private var pdfRunner: ConcurrentPdfRunner? = ConcurrentPdfRunner().apply {
        nativeInitLibrary()
    }

    private var isDestroyed = false
    private var isDestroying = false

    internal fun destroy() {
        if (!isDestroyed && !isDestroying) {
            isDestroying = true

            closeableElements.forEach { element ->
                element.close()
            }
            closeableElements.clear()

            isDestroyed = true
            pdfRunner?.nativeDestroyLibrary()
            pdfRunner = null
            isDestroying = false
        }
    }

    internal fun <T> runOrThrow(block: (pdfRunner: ConcurrentPdfRunner) -> T): T {
        val runner = pdfRunner
        if (isDestroyed || runner == null) {
            throw IllegalStateException("Cannot run in a destroyed context.")
        }

        return block.invoke(runner)
    }

    private val closeableElements = mutableListOf<AutoCloseable>()

    fun addCloseableElement(element: AutoCloseable) {
        if (!isDestroyed && !isDestroying) {
            closeableElements.add(element)
        }
    }

    fun removeCloseableElement(element: AutoCloseable) {
        if (!isDestroyed && !isDestroying) {
            closeableElements.remove(element)
        }
    }
}
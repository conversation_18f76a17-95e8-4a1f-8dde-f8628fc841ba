package com.topstack.pdfrun

import android.graphics.Rect
import android.util.Size
import com.topstack.pdfrun.util.lockAndRun
import java.util.concurrent.locks.ReentrantLock

/**
 *
 */
internal class ConcurrentPdfRunner {
    private val pdfRunner = PdfRunner()

    private val nativeLock = ReentrantLock()

    fun nativeInitLibrary() = pdfRunner.nativeInitLibrary()

    fun nativeDestroyLibrary() = pdfRunner.nativeDestroyLibrary()

    fun nativeLoadDocument(path: String, password: String?): Long = lockAndRun(nativeLock) {
        pdfRunner.nativeLoadDocument(path, password)
    }

    fun nativeCloseDocument(documentPtr: Long) = lockAndRun(nativeLock) {
        pdfRunner.nativeCloseDocument(documentPtr)
    }

    fun nativeGetPageCount(documentPtr: Long): Int = lockAndRun(nativeLock) {
        pdfRunner.nativeGetPageCount(documentPtr)
    }

    fun nativeLoadPage(documentPtr: Long, index: Int): Long = lockAndRun(nativeLock) {
        pdfRunner.nativeLoadPage(documentPtr, index)
    }

    fun nativeGetPageWidthPoint(pagePtr: Long): Int = lockAndRun(nativeLock) {
        pdfRunner.nativeGetPageWidthPoint(pagePtr)
    }

    fun nativeGetPageHeightPoint(pagePtr: Long): Int = lockAndRun(nativeLock) {
        pdfRunner.nativeGetPageHeightPoint(pagePtr)
    }

    fun nativeGetPageSizePointByIndex(documentPtr: Long, pageIndex: Int): Size =
        lockAndRun(nativeLock) {
            pdfRunner.nativeGetPageSizePointByIndex(documentPtr, pageIndex)
        }

    fun nativeClosePage(pagePtr: Long) = lockAndRun(nativeLock) {
        pdfRunner.nativeClosePage(pagePtr)
    }

    fun nativeGetPageTextHandle(pagePtr: Long): Long = lockAndRun(nativeLock) {
        pdfRunner.nativeGetPageTextHandle(pagePtr)
    }

    fun nativeClosePageTextHandle(textHandlePtr: Long) = lockAndRun(nativeLock) {
        pdfRunner.nativeClosePageTextHandle(textHandlePtr)
    }

    fun nativeGetTextCount(textHandlePtr: Long): Int = lockAndRun(nativeLock) {
        pdfRunner.nativeGetTextCount(textHandlePtr)
    }

    fun nativeGetPageText(textHandlePtr: Long, startIndex: Int, count: Int): String =
        lockAndRun(nativeLock) {
            pdfRunner.nativeGetPageText(textHandlePtr, startIndex, count)
        }

    fun nativeCountRects(textHandlePtr: Long, startIndex: Int, count: Int): Int =
        lockAndRun(nativeLock) {
            pdfRunner.nativeCountRects(textHandlePtr, startIndex, count)
        }

    fun nativeGetRect(pagePtr: Long, textHandlePtr: Long, rectIndex: Int): Rect =
        lockAndRun(nativeLock) {
            pdfRunner.nativeGetRect(pagePtr, textHandlePtr, rectIndex)
        }

    fun nativeGetCharRect(pagePtr: Long, textHandlePtr: Long, charIndex: Int): Box =
        lockAndRun(nativeLock) {
            pdfRunner.nativeGetCharRect(pagePtr, textHandlePtr, charIndex)
        }

    fun nativeFindStart(
        textHandlePtr: Long,
        keyword: String,
        matchCase: Boolean,
        matchWord: Boolean,
        consecutive: Boolean
    ): Long = lockAndRun(nativeLock) {
        pdfRunner.nativeFindStart(textHandlePtr, keyword, matchCase, matchWord, consecutive)
    }

    fun nativeFindNext(searchHandlePtr: Long): Boolean = lockAndRun(nativeLock) {
        pdfRunner.nativeFindNext(searchHandlePtr)
    }

    fun nativeFindPrevious(searchHandlePtr: Long): Boolean = lockAndRun(nativeLock) {
        pdfRunner.nativeFindPrevious(searchHandlePtr)
    }

    fun nativeGetSearchResultIndex(searchHandlePtr: Long): Int = lockAndRun(nativeLock) {
        pdfRunner.nativeGetSearchResultIndex(searchHandlePtr)
    }

    fun nativeGetSearchMatchedLength(searchHandlePtr: Long): Int = lockAndRun(nativeLock) {
        pdfRunner.nativeGetSearchMatchedLength(searchHandlePtr)
    }

    fun nativeCloseSearchHandle(searchHandlePtr: Long) = lockAndRun(nativeLock) {
        pdfRunner.nativeCloseSearchHandle(searchHandlePtr)
    }
}
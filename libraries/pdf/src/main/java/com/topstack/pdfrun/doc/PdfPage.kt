package com.topstack.pdfrun.doc

import com.topstack.pdfrun.AutoCloseablePdfContextOwner
import com.topstack.pdfrun.Box
import com.topstack.pdfrun.ConcurrentPdfRunner
import com.topstack.pdfrun.PdfContext
import com.topstack.pdfrun.doc.PdfPage.TextFinder.Builder

/**
 *
 */
class PdfPage internal constructor(
    override var pdfContext: PdfContext?,
    private val nativePtr: Long,
) : AutoCloseablePdfContextOwner() {

    /**
     * 页面宽度（磅）。
     */
    var width: Int = 0
        internal set

    /**
     * 页面高度（磅）。
     */
    var height: Int = 0
        internal set

    override fun onClose(pdfContext: PdfContext) {
        pdfContext.runOrThrow { pdfRunner ->
            pdfRunner.nativeClosePage(nativePtr)

            textInfoHandleLoaded = false
            val textHandle = textInfoHandle
            textInfoHandle = null
            textHandle?.close()
        }
    }

    private var textInfoHandleLoaded = false

    private var textInfoHandle: TextInfoHandle? = null

    fun getTextCount(): Int {
        var result = 0
        loadTextInfoHandle()
        pdfContext?.let { context ->
            textInfoHandle?.let { textHandle ->
                context.runOrThrow { pdfRunner ->
                    result = pdfRunner.nativeGetTextCount(textHandle.nativePtr)
                }
            }
        }
        return result
    }

    fun getText(): String {
        var result = ""
        loadTextInfoHandle()
        pdfContext?.let { context ->
            textInfoHandle?.let { textHandle ->
                context.runOrThrow { pdfRunner ->
                    val textCount = pdfRunner.nativeGetTextCount(textHandle.nativePtr)
                    if (textCount > 0) {
                        result = pdfRunner.nativeGetPageText(textHandle.nativePtr, 0, textCount)
                    }
                }
            }
        }
        return result
    }

    /**
     * 获取一个搜索器构建器，用来构建搜索器 [TextFinder]。
     *
     * 搜索器用完后需要调用 [TextFinder.close] 关闭。
     */
    fun obtainTextFinderBuilder(): Builder = Builder(this)

    fun findAll(
        keyword: String,
        matchCase: Boolean = false,
        matchWord: Boolean = false,
        ignoreLinebreak: Boolean = false,
        consecutive: Boolean = false,
    ): List<TextFinder.FindResult> {
        val results = mutableListOf<TextFinder.FindResult>()
        val finder = obtainTextFinderBuilder()
            .setKeyword(keyword)
            .setMatchCase(matchCase)
            .setMatchWord(matchWord)
            .setConsecutive(consecutive)
            .setIgnoreLineBreak(ignoreLinebreak)
            .build()

        var result = finder.findNext()
        while (result != null) {
            results.add(result)
            result = finder.findNext()
        }

        finder.close()

        val sortedResults = results.sortedWith { result1, result2 ->
            val firstBoxInResult1 = result1.boxes.firstOrNull()
            val firstBoxInResult2 = result2.boxes.firstOrNull()

            if (firstBoxInResult1 == null) {
                return@sortedWith -1
            }

            if (firstBoxInResult2 == null) {
                return@sortedWith 1
            }

            return@sortedWith if (firstBoxInResult1.top == firstBoxInResult2.top) {
                firstBoxInResult1.left - firstBoxInResult2.left
            } else {
                firstBoxInResult1.top - firstBoxInResult2.top
            }
        }
        return sortedResults
    }

    private fun loadTextInfoHandle() {
        if (!textInfoHandleLoaded || textInfoHandle == null || textInfoHandle?.closed == true) {
            pdfContext?.let { context ->
                textInfoHandleLoaded = true
                context.runOrThrow { pdfRunner ->
                    val textPagePtr = pdfRunner.nativeGetPageTextHandle(nativePtr)
                    if (textPagePtr != 0L) {
                        textInfoHandle = TextInfoHandle(pdfContext, textPagePtr)
                    }
                }
            }
        }
    }

    internal class TextInfoHandle constructor(
        override var pdfContext: PdfContext?,
        val nativePtr: Long,
    ) : AutoCloseablePdfContextOwner() {
        override fun onClose(pdfContext: PdfContext) {
            pdfContext.runOrThrow { pdfRunner ->
                pdfRunner.nativeClosePageTextHandle(nativePtr)
            }
        }
    }

    internal class SearchHandle constructor(
        override var pdfContext: PdfContext?,
        val nativePtr: Long,
    ) : AutoCloseablePdfContextOwner() {
        override fun onClose(pdfContext: PdfContext) {
            pdfContext.runOrThrow { pdfRunner ->
                pdfRunner.nativeCloseSearchHandle(nativePtr)
            }
        }
    }

    class TextFinder private constructor(
        private val keyword: String,
        private val matchCase: Boolean,
        private val matchWord: Boolean,
        private val consecutive: Boolean,
        private val ignoreLinebreak: Boolean,
        private val pagePtr: Long,
        private val textHandle: TextInfoHandle?,
        private val searchHandle: SearchHandle?
    ) {

        fun findNext(): FindResult? = find { pdfRunner, searchHandlePtr ->
            pdfRunner.nativeFindNext(searchHandlePtr)
        }

        fun findPrevious(): FindResult? = find { pdfRunner, searchHandlePtr ->
            pdfRunner.nativeFindPrevious(searchHandlePtr)
        }

        private fun find(nativeFind: (pdfRunner: ConcurrentPdfRunner, searchHandlePtr: Long) -> Boolean): FindResult? {
            val textHandle = textHandle ?: return null
            val handle = searchHandle ?: return null

            var result: FindResult? = null
            handle.pdfContext?.runOrThrow { pdfRunner ->
                val found = nativeFind(pdfRunner, handle.nativePtr)
                if (found) {
                    val startIndex = pdfRunner.nativeGetSearchResultIndex(handle.nativePtr)
                    val length = pdfRunner.nativeGetSearchMatchedLength(handle.nativePtr)

                    val boxes = mutableListOf<Box>()
                    var matchedText = ""
                    if (startIndex >= 0 && length > 0) {
                        matchedText =
                            pdfRunner.nativeGetPageText(textHandle.nativePtr, startIndex, length)

                        val rectCount =
                            pdfRunner.nativeCountRects(textHandle.nativePtr, startIndex, length)
                        if (rectCount > 0) {
                            for (i in 0 until rectCount) {
                                val rect = pdfRunner.nativeGetRect(pagePtr, textHandle.nativePtr, i)
                                boxes.add(Box(rect.left, rect.top, rect.right, rect.bottom, 0F))
                            }
                        }

                        // 另一种获取文本区域方式
//                        for (index in startIndex until (startIndex + length)) {
//                            val box =
//                                pdfRunner.nativeGetCharRect(pagePtr, textHandle.nativePtr, index)
//                            boxes.add(box)
//                        }
                    }

                    val sortedBoxes = boxes.sortedWith { box1, box2 ->
                        if (box1.top == box2.top) {
                            box1.left - box2.left
                        } else {
                            box1.top - box2.top
                        }
                    }

                    val compareText = if (ignoreLinebreak) {
                        matchedText.replace("\n", "").replace("\r", "")
                    } else {
                        matchedText
                    }
                    result = if (compareText.equals(keyword, ignoreCase = !matchCase)) {
                        FindResult(matchedText, startIndex, length, sortedBoxes)
                    } else {
                        find(nativeFind)
                    }
                }
            }

            return result
        }

        fun close() {
            searchHandle?.close()
        }

        data class FindResult(
            val matchedText: String,
            val start: Int,
            val matchLength: Int,
            val boxes: List<Box>
        )

        class Builder internal constructor(private val pdfPage: PdfPage) {
            private var _keyword: String = ""
            private var _matchCase: Boolean = false
            private var _matchWord: Boolean = false
            private var _consecutive: Boolean = false
            private var _ignoreLinebreak: Boolean = false

            fun setKeyword(keyword: String): Builder {
                _keyword = keyword
                return this
            }

            fun setMatchCase(matchCase: Boolean): Builder {
                _matchCase = matchCase
                return this
            }

            fun setMatchWord(matchWord: Boolean): Builder {
                _matchWord = matchWord
                return this
            }

            fun setConsecutive(consecutive: Boolean): Builder {
                _consecutive = consecutive
                return this
            }

            fun setIgnoreLineBreak(ignoreLinebreak: Boolean): Builder {
                _ignoreLinebreak = ignoreLinebreak
                return this
            }

            fun build(): TextFinder {
                pdfPage.loadTextInfoHandle()
                val textHandle = pdfPage.textInfoHandle
                val searchHandle: SearchHandle? = if (_keyword.isEmpty() || textHandle == null) {
                    null
                } else {
                    pdfPage.pdfContext?.let { context ->
                        context.runOrThrow { pdfRunner ->
                            val searchHandlePtr = pdfRunner.nativeFindStart(
                                textHandle.nativePtr,
                                _keyword,
                                _matchCase,
                                _matchWord,
                                _consecutive
                            )
                            if (searchHandlePtr != 0L) {
                                SearchHandle(context, searchHandlePtr)
                            } else {
                                null
                            }
                        }
                    }
                }
                return TextFinder(
                    keyword = _keyword,
                    matchCase = _matchCase,
                    matchWord = _matchWord,
                    consecutive = _consecutive,
                    ignoreLinebreak = _ignoreLinebreak,
                    pagePtr = pdfPage.nativePtr,
                    textHandle = textHandle,
                    searchHandle = searchHandle,
                )
            }
        }
    }
}
package com.topstack.pdfrun

import java.util.UUID

/**
 *
 */
class PdfScope {
    val id: UUID = UUID.randomUUID()

    private val context = PdfContext(this)

    var isCanceled: Boolean = false
        private set

    fun cancel() {
        if (!isCanceled) {
            isCanceled = true
            context.destroy()
        }
    }

    fun run(block: (context: PdfContext) -> Unit) {
        block.invoke(context)
    }

    protected fun finalize() {
        cancel()
    }
}
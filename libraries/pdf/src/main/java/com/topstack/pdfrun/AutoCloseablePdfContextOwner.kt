package com.topstack.pdfrun

import androidx.annotation.CallSuper
import com.topstack.pdfrun.util.lockAndRun
import java.util.concurrent.locks.ReentrantLock

/**
 *
 */
abstract class AutoCloseablePdfContextOwner : AutoCloseable, PdfContextOwner {
    private val refLock = ReentrantLock()
    private val closeLock = ReentrantLock()

    @Volatile
    private var referenceCount: Int = 0

    internal fun increaseRefCount() {
        refLock.lock()
        try {
            if (referenceCount < 0) {
                referenceCount = 0
            }
            referenceCount++
        } finally {
            refLock.unlock()
        }
    }

    private fun decreaseRefCount() {
        refLock.lock()
        try {
            referenceCount--
        } finally {
            refLock.unlock()
        }
    }

    init {
        increaseRefCount()
    }

    override var pdfContext: PdfContext? = null
        set(value) {
            field?.removeCloseableElement(this)
            field = value
            field?.addCloseableElement(this)
        }

    @Volatile
    var closed: Boolean = false
        get() {
            return lockAndRun(closeLock) {
                return@lockAndRun field
            }
        }
        private set

    @CallSuper
    override fun close() {
        if (!closed) {
            decreaseRefCount()
            if (referenceCount <= 0) {
                lockAndRun(closeLock) {
                    closed = true
                }
                referenceCount = 0
                pdfContext?.let {
                    onClose(it)
                }
                pdfContext = null
            }
        }
    }

    abstract fun onClose(pdfContext: PdfContext)

    protected fun finalize() {
        close()
    }
}
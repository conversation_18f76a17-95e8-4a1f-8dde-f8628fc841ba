package com.topstack.pdfrun

import android.graphics.Rect
import android.util.Size

/**
 *
 */
internal class PdfRunner {
    companion object {
        init {
            System.loadLibrary("pdfium")
            System.loadLibrary("pdf-run")
        }
    }

    external fun nativeInitLibrary()

    external fun nativeDestroyLibrary()

    external fun nativeLoadDocument(path: String, password: String?): Long

    external fun nativeCloseDocument(documentPtr: Long)

    external fun nativeGetPageCount(documentPtr: Long): Int

    external fun nativeLoadPage(documentPtr: Long, index: Int): Long

    external fun nativeGetPageWidthPoint(pagePtr: Long): Int

    external fun nativeGetPageHeightPoint(pagePtr: Long): Int

    external fun nativeGetPageSizePointByIndex(documentPtr: Long, pageIndex: Int): Size

    external fun nativeClosePage(pagePtr: Long)

    external fun nativeGetPageTextHandle(pagePtr: Long): Long

    external fun nativeClosePageTextHandle(textHandlePtr: Long)

    external fun nativeGetTextCount(textHandlePtr: Long): Int

    external fun nativeGetPageText(textHandlePtr: Long, startIndex: Int, count: Int): String

    external fun nativeCountRects(textHandlePtr: Long, startIndex: Int, count: Int): Int

    external fun nativeGetRect(pagePtr: Long, textHandlePtr: Long, rectIndex: Int): Rect

    external fun nativeGetCharRect(pagePtr: Long, textHandlePtr: Long, charIndex: Int): Box

    external fun nativeFindStart(
        textHandlePtr: Long,
        keyword: String,
        matchCase: Boolean,
        matchWord: Boolean,
        consecutive: Boolean
    ): Long

    external fun nativeFindNext(searchHandlePtr: Long): Boolean

    external fun nativeFindPrevious(searchHandlePtr: Long): Boolean

    external fun nativeGetSearchResultIndex(searchHandlePtr: Long): Int

    external fun nativeGetSearchMatchedLength(searchHandlePtr: Long): Int

    external fun nativeCloseSearchHandle(searchHandlePtr: Long)
}
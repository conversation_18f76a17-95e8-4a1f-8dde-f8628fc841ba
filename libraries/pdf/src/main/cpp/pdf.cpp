#include <jni.h>
#include <string>
#include "fstream"
#include "pdfium/include/fpdfview.h"
#include "pdfium/include/fpdf_edit.h"
#include "pdfium/include/fpdf_save.h"
#include "pdfium/include/fpdf_ppo.h"
#include "pdfium/include/fpdf_annot.h"
#include <android/bitmap.h>
#include <array>

size_t GetFileSize(const std::string &file_name) {
    std::ifstream in(file_name.c_str());
    in.seekg(0, std::ios::end);
    size_t size = in.tellg();
    in.close();
    return size; //单位是：byte
}

// 获取转换为UTF-16 LE编码的字符串
std::u16string utf8_to_utf16le(const std::string &u8str, bool addbom, bool *ok) {
    std::u16string u16str;
    u16str.reserve(u8str.size());
    if (addbom) {
        u16str.push_back(0xFEFF);    //bom (字节表示为 FF FE)
    }
    std::string::size_type len = u8str.length();

    const unsigned char *p = (unsigned char *) (u8str.data());
    // 判断是否具有BOM(判断长度小于3字节的情况)
    if (len > 3 && p[0] == 0xEF && p[1] == 0xBB && p[2] == 0xBF) {
        p += 3;
        len -= 3;
    }

    bool is_ok = true;
    // 开始转换
    for (std::string::size_type i = 0; i < len; ++i) {
        uint32_t ch = p[i];    // 取出UTF8序列首字节
        if ((ch & 0x80) == 0) {
            // 最高位为0，只有1字节表示UNICODE代码点
            u16str.push_back((char16_t) ch);
            continue;
        }
        switch (ch & 0xF0) {
            case 0xF0: // 4 字节字符, 0x10000 到 0x10FFFF
            {
                uint32_t c2 = p[++i];
                uint32_t c3 = p[++i];
                uint32_t c4 = p[++i];
                // 计算UNICODE代码点值(第一个字节取低3bit，其余取6bit)
                uint32_t codePoint =
                        ((ch & 0x07U) << 18) | ((c2 & 0x3FU) << 12) | ((c3 & 0x3FU) << 6) |
                        (c4 & 0x3FU);
                if (codePoint >= 0x10000) {
                    // 在UTF-16中 U+10000 到 U+10FFFF 用两个16bit单元表示, 代理项对.
                    // 1、将代码点减去0x10000(得到长度为20bit的值)
                    // 2、high 代理项 是将那20bit中的高10bit加上0xD800(110110 00 00000000)
                    // 3、low  代理项 是将那20bit中的低10bit加上0xDC00(110111 00 00000000)
                    codePoint -= 0x10000;
                    u16str.push_back((char16_t) ((codePoint >> 10) | 0xD800U));
                    u16str.push_back((char16_t) ((codePoint & 0x03FFU) | 0xDC00U));
                } else {
                    // 在UTF-16中 U+0000 到 U+D7FF 以及 U+E000 到 U+FFFF 与Unicode代码点值相同.
                    // U+D800 到 U+DFFF 是无效字符, 为了简单起见，这里假设它不存在(如果有则不编码)
                    u16str.push_back((char16_t) codePoint);
                }
            }
                break;
            case 0xE0: // 3 字节字符, 0x800 到 0xFFFF
            {
                uint32_t c2 = p[++i];
                uint32_t c3 = p[++i];
                // 计算UNICODE代码点值(第一个字节取低4bit，其余取6bit)
                uint32_t codePoint = ((ch & 0x0FU) << 12) | ((c2 & 0x3FU) << 6) | (c3 & 0x3FU);
                u16str.push_back((char16_t) codePoint);
            }
                break;
            case 0xD0: // 2 字节字符, 0x80 到 0x7FF
            case 0xC0: {
                uint32_t c2 = p[++i];
                // 计算UNICODE代码点值(第一个字节取低5bit，其余取6bit)
                uint32_t codePoint = ((ch & 0x1FU) << 12) | ((c2 & 0x3FU) << 6);
                u16str.push_back((char16_t) codePoint);
            }
                break;
            default:    // 单字节部分(前面已经处理，所以不应该进来)
                is_ok = false;
                break;
        }
    }
    if (ok != NULL) { *ok = is_ok; }

    return u16str;
}

extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeLoadDocument(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jstring path,
                                                                             jstring password) {
    const char *docPath = (*env).GetStringUTFChars(path, JNI_FALSE);
    const char *docPassword;
    if (password == nullptr) {
        docPassword = nullptr;
    } else {
        docPassword = (*env).GetStringUTFChars(password, JNI_FALSE);
    }
    FPDF_DOCUMENT fpdfDocument = FPDF_LoadDocument(docPath, docPassword);
    if (fpdfDocument == nullptr) {
        return 0;
    }
    return reinterpret_cast<jlong>(*&fpdfDocument);
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeGetLastError(JNIEnv *env,
                                                                             jobject thiz) {
    return FPDF_GetLastError();
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeCloseDocument(JNIEnv *env,
                                                                              jobject thiz,
                                                                              jlong document_point) {
    auto doc = reinterpret_cast<FPDF_DOCUMENT>(document_point);
    FPDF_CloseDocument(doc);
}

extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeCreateNewDocument(JNIEnv *env,
                                                                                  jobject thiz) {
    FPDF_DOCUMENT fpdfDocument = FPDF_CreateNewDocument();
    if (fpdfDocument == nullptr) {
        return 0;
    } else {
        return reinterpret_cast<jlong>(*&fpdfDocument);
    }
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeNewPage(JNIEnv *env, jobject thiz,
                                                                        jlong document_point,
                                                                        jint index,
                                                                        jdouble width,
                                                                        jdouble height) {
    FPDF_PAGE fpdfPage = FPDFPage_New(reinterpret_cast<FPDF_DOCUMENT>(document_point),
                                      static_cast<int>(index),
                                      static_cast<double>(width),
                                      static_cast<double>(height));
    return reinterpret_cast<jlong>(*&fpdfPage);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeDeletePage(JNIEnv *env,
                                                                           jobject thiz,
                                                                           jlong document_point,
                                                                           jint index) {
    FPDFPage_Delete(reinterpret_cast<FPDF_DOCUMENT>(document_point), static_cast<int>(index));
}

std::ofstream saveFilestream_;

int SaveFileWriter(FPDF_FILEWRITE *pThis,
                   const void *pData,
                   unsigned long size) {
    if (saveFilestream_.is_open())
        saveFilestream_.write(static_cast<const char *>(pData), size);

    return size;
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSaveAsCopy(JNIEnv *env,
                                                                           jobject thiz,
                                                                           jlong document_point,
                                                                           jstring path,
                                                                           jlong flag) {
    saveFilestream_.open(env->GetStringUTFChars(path, JNI_FALSE), std::ios_base::binary);
    FPDF_FILEWRITE filewrite = FPDF_FILEWRITE();
    filewrite.version = 1;
    filewrite.WriteBlock = SaveFileWriter;
    auto flags = static_cast<unsigned long>(flag);
    FPDF_BOOL result = FPDF_SaveAsCopy(reinterpret_cast<FPDF_DOCUMENT>(document_point), &filewrite,
                                       flags);
    saveFilestream_.close();
    return result;

}


extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_initLibrary(JNIEnv *env, jobject thiz) {
    FPDF_LIBRARY_CONFIG config;
    config.version = 2;
    config.m_pUserFontPaths = nullptr;
    config.m_pIsolate = nullptr;
    config.m_v8EmbedderSlot = 0;
    FPDF_InitLibraryWithConfig(&config);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_destroyLibrary(JNIEnv *env,
                                                                         jobject thiz) {
    FPDF_DestroyLibrary();
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeGeneratePageContent(JNIEnv *env,
                                                                                    jobject thiz,
                                                                                    jlong page_point) {
    FPDF_BOOL result = FPDFPage_GenerateContent(reinterpret_cast<FPDF_PAGE>(page_point));
    return result;
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeCreatePathObject(JNIEnv *env,
                                                                                 jobject thiz,
                                                                                 jfloat init_x,
                                                                                 jfloat init_y) {
    auto x = static_cast<float>(init_x);
    auto y = static_cast<float>(init_y);
    FPDF_PAGEOBJECT pageObject = FPDFPageObj_CreateNewPath(x, y);
    return reinterpret_cast<jlong>(*&pageObject);
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativePathLineTo(JNIEnv *env,
                                                                           jobject thiz,
                                                                           jlong path_point,
                                                                           jfloat point_x,
                                                                           jfloat point_y) {
    auto path = reinterpret_cast<FPDF_PAGEOBJECT>(path_point);
    auto x = static_cast<float>(point_x);
    auto y = static_cast<float >(point_y);
    auto result = FPDFPath_LineTo(path, x, y);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativePathBezierTo(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong path_point,
                                                                             jfloat point_x1,
                                                                             jfloat point_y1,
                                                                             jfloat point_x2,
                                                                             jfloat point_y2,
                                                                             jfloat point_x3,
                                                                             jfloat point_y3) {
    auto path = reinterpret_cast<FPDF_PAGEOBJECT>(path_point);
    auto x1 = static_cast<float>(point_x1);
    auto x2 = static_cast<float>(point_x2);
    auto x3 = static_cast<float>(point_x3);
    auto y1 = static_cast<float>(point_y1);
    auto y2 = static_cast<float>(point_y2);
    auto y3 = static_cast<float>(point_y3);
    auto result = FPDFPath_BezierTo(path, x1, y1, x2, y2, x3, y3);
    return result;
}


extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativePathClose(JNIEnv *env, jobject thiz,
                                                                          jlong path_point) {
    auto path = reinterpret_cast<FPDF_PAGEOBJECT>(path_point);
    auto result = FPDFPath_Close(path);
    return result;
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeAddMark(JNIEnv *env, jobject thiz,
                                                                        jlong page_object_point,
                                                                        jstring tag) {
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    auto objTag = env->GetStringUTFChars(tag, JNI_FALSE);
    auto markObject = FPDFPageObj_AddMark(pageObject, objTag);
    return reinterpret_cast<jlong>(*&markObject);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetBlendMode(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong page_object_point,
                                                                             jobject blend_mode) {
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    jclass clsBlendMode = (*env).GetObjectClass(blend_mode);
    jmethodID getModeId = (*env).GetMethodID(clsBlendMode, "getMode", "()Ljava/lang/String;");
    auto modeValue = (jstring) (*env).CallObjectMethod(blend_mode, getModeId);
    auto mode = env->GetStringUTFChars(modeValue, JNI_FALSE);
    FPDFPageObj_SetBlendMode(pageObject, mode);
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetStrokeColor(JNIEnv *env,
                                                                               jobject thiz,
                                                                               jlong page_object_point,
                                                                               jint r, jint g,
                                                                               jint b, jint a) {
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    auto result = FPDFPageObj_SetStrokeColor(pageObject, static_cast<unsigned int>(r),
                                             static_cast<unsigned int>(g),
                                             static_cast<unsigned int>(b),
                                             static_cast<unsigned int>(a));
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetStorkWidth(JNIEnv *env,
                                                                              jobject thiz,
                                                                              jlong page_object_point,
                                                                              jfloat width) {
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    auto result = FPDFPageObj_SetStrokeWidth(pageObject, static_cast<float>(width));
    return result;
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeInsertObject(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong page_point,
                                                                             jlong page_object_point) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    FPDFPage_InsertObject(page, pageObject);
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetPathDrawMode(JNIEnv *env,
                                                                                jobject thiz,
                                                                                jlong path_point,
                                                                                jobject fill_mode,
                                                                                jboolean is_stroke) {
    auto pathObject = reinterpret_cast<FPDF_PAGEOBJECT>(path_point);
    jclass clsFillMode = (*env).GetObjectClass(fill_mode);
    jmethodID ordinalId = (*env).GetMethodID(clsFillMode, "ordinal", "()I");
    jint modeValue = (*env).CallIntMethod(fill_mode, ordinalId);
    auto result = FPDFPath_SetDrawMode(pathObject, modeValue, is_stroke);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetFillColor(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong page_object_point,
                                                                             jint r, jint g, jint b,
                                                                             jint a) {
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    auto result = FPDFPageObj_SetFillColor(pageObject, static_cast<unsigned int>(r),
                                           static_cast<unsigned int>(g),
                                           static_cast<unsigned int>(b),
                                           static_cast<unsigned int>(a));
    return result;
}


extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativePathMoveTo(JNIEnv *env,
                                                                           jobject thiz,
                                                                           jlong path_point,
                                                                           jfloat point_x,
                                                                           jfloat point_y) {
    auto path = reinterpret_cast<FPDF_PAGEOBJECT>(path_point);
    auto result = FPDFPath_MoveTo(path, static_cast<float>(point_x), static_cast<float>(point_y));
    return result;
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeNewTextObject(JNIEnv *env,
                                                                              jobject thiz,
                                                                              jlong document_point,
                                                                              jstring font,
                                                                              jfloat font_size) {
    auto document = reinterpret_cast<FPDF_DOCUMENT>(document_point);
    auto fpdfFont = env->GetStringUTFChars(font, JNI_FALSE);
    auto fontSize = static_cast<float>(font_size);
    FPDF_PAGEOBJECT pageObject = FPDFPageObj_NewTextObj(document, fpdfFont,
                                                        fontSize);
    return reinterpret_cast<jlong>(*&pageObject);
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetTextRenderMode(JNIEnv *env,
                                                                                  jobject thiz,
                                                                                  jlong text_point,
                                                                                  jobject text_render_mode) {
    auto text = reinterpret_cast<FPDF_PAGEOBJECT>(text_point);
    jclass clsTextRenderMode = (*env).GetObjectClass(text_render_mode);
    jmethodID getModeId = (*env).GetMethodID(clsTextRenderMode, "getMode", "()I");
    auto modeValue = (*env).CallIntMethod(text_render_mode, getModeId);
    auto result = FPDFTextObj_SetTextRenderMode(text, (FPDF_TEXT_RENDERMODE) modeValue);
    return result;
}

FPDF_WIDESTRING charStringToFPDF_WIDESTRING2(std::wstring &wstr) {
    size_t length = sizeof(uint16_t) * (wstr.length() + 1);
    auto *result(static_cast<unsigned short *>(malloc(length)));
    auto *result_span = reinterpret_cast<uint8_t *>(result);
    size_t i = 0;
    for (wchar_t w: wstr) {
        result_span[i++] = w & 0xff;
        result_span[i++] = (w >> 8) & 0xff;
    }
    result_span[i++] = 0;
    result_span[i] = 0;

    return result;
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetText(JNIEnv *env, jobject thiz,
                                                                        jlong text_point,
                                                                        jstring content) {
    auto text = reinterpret_cast<FPDF_PAGEOBJECT>(text_point);
    auto utf8 = env->GetStringUTFChars(content, JNI_FALSE);
    auto coverResult = false;
    auto utf16Content = utf8_to_utf16le(utf8, false, &coverResult);
    auto str = (unsigned short *) utf16Content.c_str();
    auto result = FPDFText_SetText(text, str);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetMatrix(JNIEnv *env, jobject thiz,
                                                                          jlong page_object_point,
                                                                          jobject matrix) {
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    auto values = env->NewFloatArray(9);
    jclass clsMatrix = (*env).GetObjectClass(matrix);
    auto getValuesId = env->GetMethodID(clsMatrix, "getValues", "([F)V");
    env->CallVoidMethodA(matrix, getValuesId, reinterpret_cast<const jvalue *>(&values));
    auto array = env->GetFloatArrayElements(values, JNI_FALSE);
    FS_MATRIX fsMatrix{array[0], array[1], array[3], array[4], array[2], array[5]};
    auto result = FPDFPageObj_SetMatrix(pageObject, &fsMatrix);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetPdfMatrix(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong page_object_point,
                                                                             jobject matrix) {
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    jclass clsMatrix = (*env).GetObjectClass(matrix);
    auto aId = env->GetFieldID(clsMatrix, "a", "F");
    auto bId = env->GetFieldID(clsMatrix, "b", "F");
    auto cId = env->GetFieldID(clsMatrix, "c", "F");
    auto dId = env->GetFieldID(clsMatrix, "d", "F");
    auto eId = env->GetFieldID(clsMatrix, "e", "F");
    auto fId = env->GetFieldID(clsMatrix, "f", "F");
    auto a = env->GetFloatField(matrix, aId);
    auto b = env->GetFloatField(matrix, bId);
    auto c = env->GetFloatField(matrix, cId);
    auto d = env->GetFloatField(matrix, dId);
    auto e = env->GetFloatField(matrix, eId);
    auto f = env->GetFloatField(matrix, fId);
    FS_MATRIX fsMatrix{a, b, c, d, e, f};
    auto result = FPDFPageObj_SetMatrix(pageObject, &fsMatrix);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeImportPagesByIndex(JNIEnv *env,
                                                                                   jobject thiz,
                                                                                   jlong dest_doc_point,
                                                                                   jlong src_doc_point,
                                                                                   jintArray page_indices,
                                                                                   jlong length,
                                                                                   jint insert_index) {
    auto destDoc = reinterpret_cast<FPDF_DOCUMENT>(dest_doc_point);
    auto srcDoc = reinterpret_cast<FPDF_DOCUMENT>(src_doc_point);
    auto pageIndices = env->GetIntArrayElements(page_indices, JNI_FALSE);
    auto arrayLength = static_cast<long>(length);
    auto insertIndex = static_cast<int>(insert_index);
    auto result = FPDF_ImportPagesByIndex(destDoc, srcDoc, pageIndices, arrayLength, insertIndex);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeCopyPdfPreferences(JNIEnv *env,
                                                                                   jobject thiz,
                                                                                   jlong dest_doc_point,
                                                                                   jlong src_doc_point) {
    auto destDoc = reinterpret_cast<FPDF_DOCUMENT>(dest_doc_point);
    auto srcDoc = reinterpret_cast<FPDF_DOCUMENT>(src_doc_point);
    auto result = FPDF_CopyViewerPreferences(destDoc, srcDoc);
    return result;
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeNewImageObject(JNIEnv *env,
                                                                               jobject thiz,
                                                                               jlong document_point) {
    auto document = reinterpret_cast<FPDF_DOCUMENT>(document_point);
    auto imageObject = FPDFPageObj_NewImageObj(document);
    return reinterpret_cast<jlong>(*&imageObject);
}

std::ifstream readFilestream_;

int loadJEPGGetBlock(void *param,
                     unsigned long pos,
                     unsigned char *pBuf,
                     unsigned long size) {
    readFilestream_.seekg(pos);
    readFilestream_.read(reinterpret_cast<char *>(pBuf), size);
    return size;
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeLoadJEPJFileInline(JNIEnv *env,
                                                                                   jobject thiz,
                                                                                   jlong page_point,
                                                                                   jint count,
                                                                                   jlong image_object_point,
                                                                                   jstring image_path) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    auto pageCount = static_cast<int>(count);
    auto imageObject = reinterpret_cast<FPDF_PAGEOBJECT>(image_object_point);
    auto path = env->GetStringUTFChars(image_path, JNI_FALSE);
    auto fileSize = GetFileSize(path);
    readFilestream_.open(path, std::ios_base::in);
    FPDF_FILEACCESS fileaccess = {};
    fileaccess.m_FileLen = fileSize;
    fileaccess.m_GetBlock = loadJEPGGetBlock;
    fileaccess.m_Param = nullptr;
    auto result = FPDFImageObj_LoadJpegFileInline(&page, pageCount, imageObject, &fileaccess);
    readFilestream_.close();
    return result;
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeCreateAnnot(JNIEnv *env,
                                                                            jobject thiz,
                                                                            jlong page_point,
                                                                            jobject type) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    jclass clsAnnotationSubType = (*env).GetObjectClass(type);
    jmethodID ordinalId = (*env).GetMethodID(clsAnnotationSubType, "ordinal", "()I");
    jint subType = (*env).CallIntMethod(type, ordinalId);
    auto annotation = FPDFPage_CreateAnnot(page, subType);
    return reinterpret_cast<jlong>(*&annotation);
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetAnnotRect(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong annot_point,
                                                                             jfloat left,
                                                                             jfloat top,
                                                                             jfloat right,
                                                                             jfloat bottom) {
    auto annot = reinterpret_cast<FPDF_ANNOTATION>(annot_point);
    FS_RECTF rectf = {static_cast<float>(left),
                      static_cast<float>(top),
                      static_cast<float>(right),
                      static_cast<float>(bottom)};
    auto result = FPDFAnnot_SetRect(annot, &rectf);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeAnnotAppendObject(JNIEnv *env,
                                                                                  jobject thiz,
                                                                                  jlong annot_point,
                                                                                  jlong page_object_point) {
    auto annot = reinterpret_cast<FPDF_ANNOTATION>(annot_point);
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    auto result = FPDFAnnot_AppendObject(annot, pageObject);
    return result;
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeCloseAnnot(JNIEnv *env,
                                                                           jobject thiz,
                                                                           jlong annot_point) {
    auto annot = reinterpret_cast<FPDF_ANNOTATION>(annot_point);
    FPDFPage_CloseAnnot(annot);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeClosePage(JNIEnv *env, jobject thiz,
                                                                          jlong page_point) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    FPDF_ClosePage(page);
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetAnnotColor(JNIEnv *env,
                                                                              jobject thiz,
                                                                              jlong annot_point,
                                                                              jobject type,
                                                                              jint r, jint g,
                                                                              jint b, jint a) {
    auto annot = reinterpret_cast<FPDF_ANNOTATION>(annot_point);
    jclass clsAnnotationColorType = (*env).GetObjectClass(type);
    jmethodID ordinalId = (*env).GetMethodID(clsAnnotationColorType, "ordinal", "()I");
    jint colorType = (*env).CallIntMethod(type, ordinalId);
    FPDFANNOT_COLORTYPE fpdfannotColortype;
    if (colorType == 0)
        fpdfannotColortype = FPDFANNOT_COLORTYPE_Color;
    else
        fpdfannotColortype = FPDFANNOT_COLORTYPE_InteriorColor;
    auto result = FPDFAnnot_SetColor(annot,
                                     fpdfannotColortype,
                                     static_cast<unsigned int>(r),
                                     static_cast<unsigned int>(g),
                                     static_cast<unsigned int>(b),
                                     static_cast<unsigned int>(a));
    return result;
}


extern "C"
JNIEXPORT jobject JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeGetPageObjectBound(JNIEnv *env,
                                                                                   jobject thiz,
                                                                                   jlong page_object_point) {
    auto pageObject = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    float left = 0;
    float bottom = 0;
    float right = 0;
    float top = 0;
    FPDFPageObj_GetBounds(pageObject, &left, &bottom, &right, &top);
    auto clsRectF = (*env).FindClass("android/graphics/RectF");
    auto rectFConstructId = (*env).GetMethodID(clsRectF, "<init>", "(FFFF)V");
    auto rectf = (*env).NewObject(clsRectF, rectFConstructId, left, top, right, bottom);
    return rectf;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeAppendAnnotAttachmentRect(
        JNIEnv *env,
        jobject thiz,
        jlong annot_point,
        jfloat x1,
        jfloat y1,
        jfloat x2,
        jfloat y2,
        jfloat x3,
        jfloat y3,
        jfloat x4,
        jfloat y4) {
    auto annot = reinterpret_cast<FPDF_ANNOTATION>(annot_point);
    FS_QUADPOINTSF quadpoints;
    quadpoints.x1 = static_cast<float>(x1);
    quadpoints.y1 = static_cast<float>(y1);
    quadpoints.x2 = static_cast<float>(x2);
    quadpoints.y2 = static_cast<float>(y2);
    quadpoints.x3 = static_cast<float>(x3);
    quadpoints.y3 = static_cast<float>(y3);
    quadpoints.x4 = static_cast<float>(x4);
    quadpoints.y4 = static_cast<float>(y4);
    auto result = FPDFAnnot_AppendAttachmentPoints(annot, &quadpoints);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeUpdateObject(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong annot_point,
                                                                             jlong page_object_point) {
    auto annot = reinterpret_cast<FPDF_ANNOTATION>(annot_point);
    auto page = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    auto result = FPDFAnnot_UpdateObject(annot, page);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetAnnotBorder(JNIEnv *env,
                                                                               jobject thiz,
                                                                               jlong annot_point,
                                                                               jfloat horizontal_radius,
                                                                               jfloat vertical_radius,
                                                                               jfloat border_width) {
    auto annot = reinterpret_cast<FPDF_ANNOTATION>(annot_point);
    auto result = FPDFAnnot_SetBorder(annot, horizontal_radius, vertical_radius, border_width);
    return result;
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeLoadFont(JNIEnv *env, jobject thiz,
                                                                         jlong document_point,
                                                                         jstring font_path,
                                                                         jobject font_type,
                                                                         jboolean is_cid_font) {
    auto document = reinterpret_cast<FPDF_DOCUMENT>(document_point);
    jclass clsFontType = (*env).GetObjectClass(font_type);
    jmethodID getTypeId = (*env).GetMethodID(clsFontType, "getType", "()I");
    auto typeValue = (*env).CallIntMethod(font_type, getTypeId);
    auto path = env->GetStringUTFChars(font_path, JNI_FALSE);
    std::ifstream readStream;
    readStream.open(path, std::ios_base::in | std::ios::binary);
    //移动到尾部获取文件长度
    readStream.seekg(0, std::ios::end);
    size_t size = readStream.tellg();
    auto data = static_cast<char *>(malloc(size));
    //移动到开头，读取文件
    readStream.seekg(0, std::ios::beg);
    readStream.read(data, size);
    auto font = FPDFText_LoadFont(document,
                                  reinterpret_cast<const uint8_t *>(data),
                                  size,
                                  typeValue,
                                  is_cid_font);
    return reinterpret_cast<jlong>(*&font);
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeCreateTextObject(JNIEnv *env,
                                                                                 jobject thiz,
                                                                                 jlong document_point,
                                                                                 jlong font_point,
                                                                                 jfloat font_size) {
    auto document = reinterpret_cast<FPDF_DOCUMENT>(document_point);
    auto font = reinterpret_cast<FPDF_FONT>(font_point);
    auto size = static_cast<float>(font_size);
    auto object = FPDFPageObj_CreateTextObj(document, font, size);
    return reinterpret_cast<jlong>(*&object);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeCloseFont(JNIEnv *env, jobject thiz,
                                                                          jlong font_point) {
    auto font = reinterpret_cast<FPDF_FONT>(font_point);
    FPDFFont_Close(font);
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeLoadPage(JNIEnv *env, jobject thiz,
                                                                         jlong document_point,
                                                                         jint index) {
    auto document = reinterpret_cast<FPDF_DOCUMENT>(document_point);
    auto page = FPDF_LoadPage(document, static_cast<int>(index));
    return reinterpret_cast<jlong>(*&page);
}

#define RGB565_R(p) ((((p) & 0xF800) >> 11) << 3)
#define RGB565_G(p) ((((p) & 0x7E0 ) >> 5)  << 2)
#define RGB565_B(p) ( ((p) & 0x1F  )        << 3)
#define MAKE_RGB565(r, g, b) ((((r) >> 3) << 11) | (((g) >> 2) << 5) | ((b) >> 3))

#define RGBA_A(p) (((p) & 0xFF000000) >> 24)
#define RGBA_R(p) (((p) & 0x00FF0000) >> 16)
#define RGBA_G(p) (((p) & 0x0000FF00) >>  8)
#define RGBA_B(p)  ((p) & 0x000000FF)
#define MAKE_RGBA(r, g, b, a) (((a) << 24) | ((r) << 16) | ((g) << 8) | (b))
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetBitmap2ImageObject(JNIEnv *env,
                                                                                      jobject thiz,
                                                                                      jlong page_point,
                                                                                      jint count,
                                                                                      jlong image_object_point,
                                                                                      jobject bitmap) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    auto pageCount = static_cast<int>(count);
    auto imageObject = reinterpret_cast<FPDF_PAGEOBJECT>(image_object_point);
    AndroidBitmapInfo info;
    int result = AndroidBitmap_getInfo(env, bitmap, &info);
    if (result < 0) {
        return;
    }
    auto androidFormat = info.format;
    if (androidFormat != ANDROID_BITMAP_FORMAT_RGBA_8888 &&
        androidFormat != ANDROID_BITMAP_FORMAT_RGB_565) {
        return;
    }
    int format;
    void *addr;
    AndroidBitmap_lockPixels(env, bitmap, &addr);
    if (androidFormat == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        format = FPDFBitmap_BGRA;
    } else {
        format = FPDFBitmap_BGR;
    }
    //将RGBA -> BGRA

    for (int y = 0; y < info.height; ++y) {
        for (int x = 0; x < info.width; ++x) {
            int a = 0, r = 0, g = 0, b = 0;
            void *pixel = NULL;
            if (info.format == ANDROID_BITMAP_FORMAT_RGB_565) {
                pixel = ((uint16_t *) addr) + y * info.width + x;
                uint16_t v = *(uint16_t *) pixel;
                r = RGB565_R(v);
                g = RGB565_G(v);
                b = RGB565_B(v);
            } else {
                pixel = ((uint32_t *) addr) + y * info.width + x;
                uint32_t v = *(uint32_t *) pixel;
                a = RGBA_A(v);
                r = RGBA_R(v);
                g = RGBA_G(v);
                b = RGBA_B(v);
            }

            // Write the pixel back
            if (info.format == ANDROID_BITMAP_FORMAT_RGB_565) {
                *((uint16_t *) pixel) = MAKE_RGB565(b, g, r);
            } else {// RGBA
                *((uint32_t *) pixel) = MAKE_RGBA(b, g, r, a);
            }
        }
    }
    AndroidBitmap_unlockPixels(env, bitmap);
    auto pdfBitmap = FPDFBitmap_CreateEx(info.width, info.height, format, addr, info.stride);
    FPDFImageObj_SetBitmap(&page, pageCount, imageObject, pdfBitmap);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeDestroyPageObj(JNIEnv *env,
                                                                               jobject thiz,
                                                                               jlong page_object_point) {
    auto pageObj = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    FPDFPageObj_Destroy(pageObj);
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeRemoveObject(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong page_point,
                                                                             jlong page_object_point) {
    auto pageObj = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    return FPDFPage_RemoveObject(page, pageObj);
}
extern "C"
JNIEXPORT jfloat JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeGetPageWidth(JNIEnv *env,
                                                                             jobject thiz,
                                                                             jlong page_point) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    auto width = FPDF_GetPageWidthF(page);
    return static_cast<jfloat>(width);
}
extern "C"
JNIEXPORT jfloat JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeGetPageHeight(JNIEnv *env,
                                                                              jobject thiz,
                                                                              jlong page_point) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    auto height = FPDF_GetPageHeightF(page);
    return static_cast<jfloat>(height);
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetLineJoin(JNIEnv *env,
                                                                            jobject thiz,
                                                                            jlong page_object_point,
                                                                            jobject line_join_type) {
    auto pageObj = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    jclass clsLineJoinType = (*env).GetObjectClass(line_join_type);
    jmethodID ordinalId = (*env).GetMethodID(clsLineJoinType, "ordinal", "()I");
    jint lineJoinType = (*env).CallIntMethod(line_join_type, ordinalId);
    auto result = FPDFPageObj_SetLineJoin(pageObj, lineJoinType);
    return result;
}
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeSetLineCap(JNIEnv *env,
                                                                           jobject thiz,
                                                                           jlong page_object_point,
                                                                           jobject line_cap_type) {
    auto pageObj = reinterpret_cast<FPDF_PAGEOBJECT>(page_object_point);
    jclass clsLineCapType = (*env).GetObjectClass(line_cap_type);
    jmethodID ordinalId = (*env).GetMethodID(clsLineCapType, "ordinal", "()I");
    jint lineCapType = (*env).CallIntMethod(line_cap_type, ordinalId);
    auto result = FPDFPageObj_SetLineCap(pageObj, lineCapType);
    return result;
}
struct rgb {
    uint8_t red;
    uint8_t green;
    uint8_t blue;
};

uint16_t rgb_to_565(unsigned char R8, unsigned char G8, unsigned char B8) {
    unsigned char R5 = (R8 * 249 + 1014) >> 11;
    unsigned char G6 = (G8 * 253 + 505) >> 10;
    unsigned char B5 = (B8 * 249 + 1014) >> 11;
    return (R5 << 11) | (G6 << 5) | (B5);
}

void rgbBitmapTo565(void *source, int sourceStride, void *dest, AndroidBitmapInfo *info) {
    rgb *srcLine;
    uint16_t *dstLine;
    int y, x;
    for (y = 0; y < info->height; y++) {
        srcLine = (rgb *) source;
        dstLine = (uint16_t *) dest;
        for (x = 0; x < info->width; x++) {
            rgb *r = &srcLine[x];
            dstLine[x] = rgb_to_565(r->red, r->green, r->blue);
        }
        source = (char *) source + sourceStride;
        dest = (char *) dest + info->stride;
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeRenderPageBitmap(JNIEnv *env,
                                                                                 jobject thiz,
                                                                                 jlong page_point,
                                                                                 jobject target_bitmap,
                                                                                 jint start_x,
                                                                                 jint start_y,
                                                                                 jint size_x,
                                                                                 jint size_y,
                                                                                 jobject pdf_rotate) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    if (page == nullptr || target_bitmap == nullptr) {
        return;
    }
    AndroidBitmapInfo info;
    int ret = AndroidBitmap_getInfo(env, target_bitmap, &info);
    if (ret < 0) {
        return;
    }
    auto format = info.format;
    if (format != ANDROID_BITMAP_FORMAT_RGBA_8888 && format != ANDROID_BITMAP_FORMAT_RGB_565) {
        return;
    }
    auto canvasHorSize = info.width;
    auto canvasVerSize = info.height;
    void *addr;
    AndroidBitmap_lockPixels(env, target_bitmap, &addr);

    void *temp;
    int pdf_bitmap_format;
    //bitmap每行的字节数
    int source_stride;
    if (format == ANDROID_BITMAP_FORMAT_RGB_565) {
        temp = malloc(canvasVerSize * canvasHorSize * sizeof(rgb));
        source_stride = canvasHorSize * sizeof(rgb);
        pdf_bitmap_format = FPDFBitmap_BGR;
    } else {
        temp = addr;
        source_stride = info.stride;
        pdf_bitmap_format = FPDFBitmap_BGRA;
    }

    auto pdfBitmap = FPDFBitmap_CreateEx(canvasHorSize, canvasVerSize, pdf_bitmap_format, temp,
                                         source_stride);

    int baseHorSize = (canvasHorSize < size_x) ? canvasHorSize : (int) size_x;
    int baseVerSize = (canvasVerSize < size_y) ? canvasVerSize : (int) size_y;
    int baseX = (start_x < 0) ? 0 : (int) start_x;
    int baseY = (start_y < 0) ? 0 : (int) start_y;
    int flags = FPDF_REVERSE_BYTE_ORDER | FPDF_ANNOT;

    if (format == ANDROID_BITMAP_FORMAT_RGB_565) {
        FPDFBitmap_FillRect(pdfBitmap, baseX, baseY, baseHorSize, baseVerSize, 0xFFFFFF);
    }

    jclass clsLineCapType = (*env).GetObjectClass(pdf_rotate);
    jmethodID ordinalId = (*env).GetMethodID(clsLineCapType, "ordinal", "()I");
    jint rotate = (*env).CallIntMethod(pdf_rotate, ordinalId);

    FPDF_RenderPageBitmap(pdfBitmap, page, start_x, start_y, size_x, size_y, rotate, flags);

    if (format == ANDROID_BITMAP_FORMAT_RGB_565) {
        rgbBitmapTo565(temp, source_stride, addr, &info);
        free(temp);
    }
    AndroidBitmap_unlockPixels(env, target_bitmap);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_topstack_kilonotes_pdf_PdfiumCore_00024Companion_nativeRenderPageBitmapWithTransform(
        JNIEnv *env, jobject thiz, jlong page_point, jobject target_bitmap, jfloat scale_x,
        jfloat scale_y, jfloat translate_x, jfloat translate_y) {
    auto page = reinterpret_cast<FPDF_PAGE>(page_point);
    if (page == nullptr || target_bitmap == nullptr) {
        return;
    }
    AndroidBitmapInfo info;
    int ret = AndroidBitmap_getInfo(env, target_bitmap, &info);
    if (ret < 0) {
        return;
    }
    auto format = info.format;
    if (format != ANDROID_BITMAP_FORMAT_RGBA_8888 && format != ANDROID_BITMAP_FORMAT_RGB_565) {
        return;
    }
    auto canvasHorSize = info.width;
    auto canvasVerSize = info.height;
    void *addr;
    AndroidBitmap_lockPixels(env, target_bitmap, &addr);

    void *temp;
    int pdf_bitmap_format;
    //bitmap每行的字节数
    int source_stride;
    if (format == ANDROID_BITMAP_FORMAT_RGB_565) {
        temp = malloc(canvasVerSize * canvasHorSize * sizeof(rgb));
        source_stride = canvasHorSize * sizeof(rgb);
        pdf_bitmap_format = FPDFBitmap_BGR;
    } else {
        temp = addr;
        source_stride = info.stride;
        pdf_bitmap_format = FPDFBitmap_BGRA;
    }

    auto pdfBitmap = FPDFBitmap_CreateEx(canvasHorSize, canvasVerSize, pdf_bitmap_format, temp,
                                         source_stride);
    int flags = FPDF_REVERSE_BYTE_ORDER | FPDF_ANNOT;

    FS_MATRIX matrix = {
            scale_x,
            0,
            0,
            scale_y,
            translate_x,
            translate_y
    };
    FS_RECTF clipping = {
            0, 0, static_cast<float>(canvasHorSize), static_cast<float>(canvasVerSize)
    };

    FPDF_RenderPageBitmapWithMatrix(pdfBitmap, page, &matrix, &clipping, flags);

    if (format == ANDROID_BITMAP_FORMAT_RGB_565) {
        rgbBitmapTo565(temp, source_stride, addr, &info);
        free(temp);
    }
    AndroidBitmap_unlockPixels(env, target_bitmap);
}
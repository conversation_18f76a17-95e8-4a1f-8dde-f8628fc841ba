package com.topstack.kilonotes.sns.wechat

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.diffdev.DiffDevOAuthFactory
import com.tencent.mm.opensdk.diffdev.OAuthErrCode
import com.tencent.mm.opensdk.diffdev.OAuthListener
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.modelpay.PayResp
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.topstack.kilonotes.NativeWxPayResult
import com.topstack.kilonotes.OrderType
import com.topstack.kilonotes.account.UserRequests
import com.topstack.kilonotes.account.WechatLogin
import com.topstack.kilonotes.infra.foundation.thread.ThreadUtils
import com.topstack.kilonotes.infra.gson.commonGsonClient
import com.topstack.kilonotes.infra.util.AppUtils
import com.topstack.kilonotes.infra.util.LogHelper
import com.topstack.kilonotes.infra.util.toSha1
import com.topstack.kilonotes.pay.ORIGINAL_CODE_UNDEFINED
import com.topstack.kilonotes.pay.PayItem
import com.topstack.kilonotes.pay.PayResultCode
import com.topstack.kilonotes.pay.wechat.QueryNativeOrderResponse
import com.topstack.kilonotes.pay.wechat.WxPayRequests
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.LinkedList
import java.util.TreeMap
import java.util.UUID


/**
 *
 */
object WXHelper {
    const val logTag = "PayHelper"
    const val AUTH_RESULT_SUCCESS = 0
    const val AUTH_RESULT_USER_CANCEL = 1
    const val AUTH_RESULT_ERROR_UNKNOWN = -1
    const val AUTH_RESULT_ERROR_SERVER_FAILED = -2
    const val AUTH_RESULT_ERROR_FAILED_TO_GOT_QRCODE = -3
    const val AUTH_RESULT_ERROR_USER_DENIED = -4

    const val NATIVE_ORDER_SUCCESS = "SUCCESS"

    private val appId = "wxd22ca58cde1d6209"

    internal val wxApi: IWXAPI by lazy {
        WXAPIFactory.createWXAPI(AppUtils.appContext, appId)
    }

    fun hasWxInstalled(): Boolean = wxApi.isWXAppInstalled

    private var wxRegistered = false
    private var refreshBroadcastReceiverRegistered = false

    fun registerToWx(): Boolean {
        if (wxRegistered) {
            return true
        }

        val success = wxApi.registerApp(appId)

        if (success) {
            wxRegistered = true
        } else {
            if (!refreshBroadcastReceiverRegistered) {
                refreshBroadcastReceiverRegistered = true
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    AppUtils.appContext.registerReceiver(
                        object : BroadcastReceiver() {
                            override fun onReceive(context: Context, intent: Intent?) {
                                wxRegistered = wxApi.registerApp(appId)
                            }
                        },
                        IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP),
                        Context.RECEIVER_NOT_EXPORTED
                    )
                } else {
                    AppUtils.appContext.registerReceiver(object : BroadcastReceiver() {
                        override fun onReceive(context: Context, intent: Intent?) {
                            wxRegistered = wxApi.registerApp(appId)
                        }
                    }, IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP))
                }
            }
        }
        return success
    }

    private val appAuthCallbacks =
        hashMapOf<String, (success: Boolean, resultCode: Int, originalCode: Int) -> Unit>()

    val authResult: LinkedList<Boolean> = LinkedList<Boolean>()

    /**
     * 拉起微信APP授权登录
     */
    fun authByWxApp(callback: (success: Boolean, resultCode: Int, originalCode: Int) -> Unit) {
        val loginReq = SendAuth.Req()
        val session = UUID.randomUUID().toString()
        appAuthCallbacks[session] = callback
        loginReq.transaction = session
        loginReq.scope = "snsapi_userinfo"
        loginReq.state = session

        wxApi.sendReq(loginReq)
    }

    /**
     * 停止微信二维码登录授权
     */
    fun stopWxQrCodeAuth() {
        val authClient = DiffDevOAuthFactory.getDiffDevOAuth()
        authClient.removeAllListeners()
        authClient.stopAuth()
        authClient.detach()
    }

    /**
     * 微信二维码登录
     */
    suspend fun authByWxQrcode(listener: QrcodeAuthListener) = withContext(Dispatchers.IO) {
        val scope = "snsapi_userinfo"
        val noncestr = "${System.currentTimeMillis()}"
        val timeStamp = "${System.currentTimeMillis()}"
        val params = TreeMap<String, String>().apply {
            put("appid", appId)
            put("noncestr", noncestr)
            put("timestamp", timeStamp)
        }

        UserRequests.requestWxSdkTicket { success, sdkTicket ->
            if (success) {
                params["sdk_ticket"] = sdkTicket
                val signature = generateWxAuthSignature(params)

                val authClient = DiffDevOAuthFactory.getDiffDevOAuth()
                authClient.removeAllListeners()
                authClient.stopAuth()
                authClient.detach()
                authClient.auth(
                    appId, scope, noncestr, timeStamp, signature,
                    object : OAuthListener {
                        override fun onAuthGotQrcode(qrcodeImgPath: String?, imgBuf: ByteArray?) {
                            if (imgBuf == null || imgBuf.isEmpty()) {
                                ThreadUtils.postMainThread {
                                    listener.onAuthResult(
                                        false,
                                        AUTH_RESULT_ERROR_FAILED_TO_GOT_QRCODE,
                                        AUTH_RESULT_ERROR_FAILED_TO_GOT_QRCODE
                                    )
                                }
                            } else {
                                ThreadUtils.postMainThread {
                                    listener.onGotQrcode(imgBuf)
                                }
                            }
                        }

                        override fun onQrcodeScanned() {
                            ThreadUtils.postMainThread {
                                listener.onQrcodeScanned()
                            }
                        }

                        override fun onAuthFinish(errCode: OAuthErrCode, authCode: String?) {
                            LogHelper.d(
                                "WXHelper authByWxQrcode",
                                "errCode:${errCode.code},authCode:$authCode"
                            )
                            when (errCode) {
                                OAuthErrCode.WechatAuth_Err_OK -> {
                                    if (authCode == null) {
                                        ThreadUtils.postMainThread {
                                            listener.onAuthResult(
                                                false,
                                                AUTH_RESULT_ERROR_UNKNOWN,
                                                errCode.code
                                            )
                                        }
                                    } else {
                                        GlobalScope.launch {
                                            val loginSuccess =
                                                UserRequests.requestLogin(WechatLogin(authCode))
                                            withContext(Dispatchers.Main) {
                                                if (loginSuccess) {
                                                    listener.onAuthResult(
                                                        true,
                                                        AUTH_RESULT_SUCCESS,
                                                        errCode.code
                                                    )
                                                } else {
                                                    listener.onAuthResult(
                                                        false,
                                                        AUTH_RESULT_ERROR_SERVER_FAILED,
                                                        errCode.code
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }

                                OAuthErrCode.WechatAuth_Err_Timeout -> {
                                    ThreadUtils.postMainThread {
                                        listener.onQrcodeTimeout()
                                    }
                                }

                                OAuthErrCode.WechatAuth_Err_Cancel -> {
                                    ThreadUtils.postMainThread {
                                        listener.onAuthResult(
                                            false,
                                            AUTH_RESULT_USER_CANCEL,
                                            errCode.code
                                        )
                                    }
                                }

                                else -> {
                                    ThreadUtils.postMainThread {
                                        listener.onAuthResult(
                                            false,
                                            AUTH_RESULT_ERROR_UNKNOWN,
                                            errCode.code
                                        )
                                    }
                                }
                            }
                        }
                    })
            }
        }
    }

    private fun generateWxAuthSignature(normalParams: TreeMap<String, String>): String {
        val joinedParams = buildString {
            for ((key, value) in normalParams) {
                append("$key=$value&")
            }
            if (isNotEmpty()) {
                setLength(length - 1)
            }
        }
        return joinedParams.toSha1()
    }

    private val wxPayCallbacks =
        hashMapOf<String, (success: Boolean, resultCode: PayResultCode, originalCode: String, reportOrderRelatedIdSet: Set<String>) -> Unit>()
    private val prepayIdToOutTradeNoMap = hashMapOf<String, String>()

    fun payByWx(
        lifecycleOwner: LifecycleOwner,
        itemId: String,
        orderType: OrderType,
        orderCallback: (orderId: String?, reportOrderRelatedId: String?) -> Unit,
        callback: (success: Boolean, resultCode: PayResultCode, originalCode: String, reportOrderRelatedIdSet: Set<String>) -> Unit,
        headerMap: HashMap<String, String>? = null,
    ) {
        lifecycleOwner.lifecycleScope.launch {
            // todo: move order business to pay order library
            val unifiedOrder =
                WxPayRequests.requestUnifiedOrder(
                    itemId,
                    WxPayRequests.TRADE_TYPE_APP,
                    orderType,
                    headerMap
                )
            if (unifiedOrder == null) {
                LogHelper.w(logTag, "failed to get wx unified order")
                withContext(Dispatchers.Main) {
                    callback(
                        false,
                        PayResultCode.PAY_RESULT_UNKNOWN_ERROR,
                        ORIGINAL_CODE_UNDEFINED,
                        emptySet()
                    )
                    orderCallback(null, null)
                }
            } else {
                wxPayCallbacks[unifiedOrder.prepayId] = callback
                prepayIdToOutTradeNoMap[unifiedOrder.prepayId] = unifiedOrder.tradeId
                val payReq = PayReq().apply {
                    appId = unifiedOrder.appId
                    partnerId = unifiedOrder.partnerId
                    prepayId = unifiedOrder.prepayId
                    packageValue = unifiedOrder.pkg
                    nonceStr = unifiedOrder.noncestr
                    timeStamp = unifiedOrder.timestamp
                    sign = unifiedOrder.sign
                }
                withContext(Dispatchers.Main) {
                    orderCallback(unifiedOrder.tradeId, unifiedOrder.tradeId)
                }

                wxApi.sendReq(payReq)
            }
        }
    }

    fun onWxResponse(resp: BaseResp) {
        when (resp.type) {
            ConstantsAPI.COMMAND_SENDAUTH -> {
                if (resp is SendAuth.Resp) {
                    val session = resp.transaction
                    val authCallback = appAuthCallbacks[session]
                    if (authCallback != null) {
                        when (val errCode = resp.errCode) {
                            BaseResp.ErrCode.ERR_OK -> {
                                GlobalScope.launch {
                                    val loginSuccess =
                                        UserRequests.requestLogin(WechatLogin(resp.code))
                                    withContext(Dispatchers.Main) {
                                        if (loginSuccess) {
                                            authResult.push(true)
                                            authCallback(
                                                true,
                                                AUTH_RESULT_SUCCESS,
                                                errCode
                                            )
                                        } else {
                                            authResult.push(false)
                                            authCallback(
                                                false,
                                                AUTH_RESULT_ERROR_SERVER_FAILED,
                                                errCode
                                            )
                                        }
                                    }
                                }
                            }

                            BaseResp.ErrCode.ERR_USER_CANCEL -> {
                                ThreadUtils.postMainThread {
                                    authResult.push(false)
                                    authCallback(
                                        false,
                                        AUTH_RESULT_USER_CANCEL,
                                        errCode
                                    )
                                }
                            }

                            BaseResp.ErrCode.ERR_AUTH_DENIED -> {
                                ThreadUtils.postMainThread {
                                    authResult.push(false)
                                    authCallback(
                                        false,
                                        AUTH_RESULT_ERROR_USER_DENIED,
                                        errCode
                                    )
                                }
                            }

                            else -> {
                                ThreadUtils.postMainThread {
                                    authResult.push(false)
                                    authCallback(false, AUTH_RESULT_ERROR_UNKNOWN, errCode)
                                }
                            }
                        }
                    }
                }
            }

            ConstantsAPI.COMMAND_PAY_BY_WX -> {
                if (resp is PayResp) {
                    val payCallback = wxPayCallbacks[resp.prepayId]
                    if (payCallback != null) {
                        ThreadUtils.postMainThread {
                            when (resp.errCode) {
                                BaseResp.ErrCode.ERR_OK -> {
                                    payCallback(
                                        true,
                                        PayResultCode.PAY_RESULT_SUCCESS,
                                        resp.errCode.toString(),
                                        setOf(prepayIdToOutTradeNoMap[resp.prepayId].orEmpty())
                                    )
                                }

                                BaseResp.ErrCode.ERR_USER_CANCEL -> {
                                    payCallback(
                                        false,
                                        PayResultCode.PAY_RESULT_USER_CANCEL,
                                        resp.errCode.toString(),
                                        emptySet()
                                    )
                                }

                                else -> {
                                    payCallback(
                                        false,
                                        PayResultCode.PAY_RESULT_UNKNOWN_ERROR,
                                        resp.errCode.toString(),
                                        emptySet()
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    fun nativePay(
        lifecycleOwner: LifecycleOwner,
        payItem: PayItem,
        orderType: OrderType,
        headerMap: HashMap<String, String>? = null,
        callback: (bean: NativeWxPayResult?) -> Unit,
    ) {
        lifecycleOwner.lifecycleScope.launch {
            val order =
                WxPayRequests.requestUnifiedOrder(
                    if (OrderType.isVipOrderType(orderType)) "${payItem.id}" else payItem.productId,
                    WxPayRequests.TRADE_TYPE_NATIVE,
                    orderType,
                    headerMap
                )
            if (order != null) {
                withContext(Dispatchers.Main) {
                    callback(
                        NativeWxPayResult(
                            order.timestamp.toLong(),
                            order.timeExpire.toLong(),
                            order.codeUrl,
                            order.tradeId
                        )
                    )
                }
            } else {
                withContext(Dispatchers.Main) {
                    callback(null)
                }
            }
        }
    }

    fun queryNativeOrder(
        lifecycleOwner: LifecycleOwner,
        orderId: String,
        callback: (bean: QueryNativeOrderResponse?) -> Unit,
    ) {
        lifecycleOwner.lifecycleScope.launch {
            LogHelper.d("qrcode query", "orderId = $orderId")
            val order = WxPayRequests.queryOrder(orderId)
            LogHelper.d("qrcode query", commonGsonClient.toJson(order))
            withContext(Dispatchers.Main) {
                callback(order)
            }
        }
    }

    interface QrcodeAuthListener {
        fun onGotQrcode(imageBuffer: ByteArray)
        fun onQrcodeScanned()
        fun onQrcodeTimeout()
        fun onAuthResult(success: Boolean, resultCode: Int, originalCode: Int)
    }
}
package com.topstack.kilonotes.pay.wechat

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

/**
 *
 */
@Keep
class WxUnifiedOrder {
    @SerializedName("appid")
    val appId: String = ""

    val noncestr: String = ""

    @SerializedName("package")
    val pkg: String = ""

    @SerializedName("partnerid")
    val partnerId: String = ""

    @SerializedName("prepayid")
    val prepayId: String = ""

    val sign: String = ""

    val timestamp: String = ""

    val codeUrl: String = ""

    val timeExpire: String = ""

    @SerializedName("outTradeNo")
    val tradeId: String = ""
}
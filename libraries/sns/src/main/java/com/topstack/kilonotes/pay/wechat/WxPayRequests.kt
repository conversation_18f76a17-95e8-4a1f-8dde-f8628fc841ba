package com.topstack.kilonotes.pay.wechat

import com.topstack.kilonotes.OrderType
import com.topstack.kilonotes.account.UserManager
import com.topstack.kilonotes.infra.config.baseServerUrl
import com.topstack.kilonotes.infra.network.BusinessHttpRequest
import com.topstack.kilonotes.infra.network.HttpResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 *
 */
object WxPayRequests {
    const val TRADE_TYPE_APP = "APP"
    const val TRADE_TYPE_NATIVE = "NATIVE"

    private const val PATH_GET_UNIFIED_ORDER = "/pay/wechat/unified_order"

    private const val PATH_QUERY_ORDER = "/pay/wechat/query_order"

    suspend fun requestUnifiedOrder(
        itemId: String,
        tradeType: String,
        orderType: OrderType,
        headerMap: HashMap<String, String>? = null
    ): WxUnifiedOrder? {
        val loggedInUser = UserManager.loggedInUser
        if (loggedInUser == null || itemId.isBlank()) {
            return null
        }

        return withContext(Dispatchers.IO) {
            val result = BusinessHttpRequest().requestHttpPostSingleData(
                WxUnifiedOrder::class.java,
                baseServerUrl,
                PATH_GET_UNIFIED_ORDER,
                query = hashMapOf<String, String>().apply {
                    put("itemId", itemId)
                    put("openId", loggedInUser.openId)
                    put("tradeType", tradeType)
                    put("orderType", orderType.value.toString())
                },
                headerMap = headerMap
            )

            when (result) {
                is HttpResult.Success -> {
                    return@withContext result.data
                }

                is HttpResult.Error -> {
                    return@withContext null
                }
            }
        }
    }

    suspend fun queryOrder(orderId: String): QueryNativeOrderResponse? {
        val loggedInUser = UserManager.loggedInUser
        if (loggedInUser == null || orderId.isBlank() || loggedInUser.openId.isBlank()) {
            return null
        }
        return withContext(Dispatchers.IO) {
            val result = BusinessHttpRequest().requestHttpGetSingleData(
                QueryNativeOrderResponse::class.java,
                baseServerUrl,
                PATH_QUERY_ORDER,
                query = hashMapOf<String, String>().apply {
                    put("outTradeNo", orderId)
                    put("openId", loggedInUser.openId)
                }
            )
            when (result) {
                is HttpResult.Success -> {
                    return@withContext result.data
                }

                is HttpResult.Error -> {
                    return@withContext null
                }
            }
        }
    }
}

